import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { numberManagementMockData } from '../lib/mockData';

// Create an axios instance for API calls
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Flag to determine if we should use mock data
const USE_MOCK_DATA = process.env.NODE_ENV === 'development';

// Types for eSIM management
export interface AvailableEsim {
  id: string;
  planName: string;
  data: string;
  validity: string;
  country: string;
  cost: number;
}

export interface OwnedEsim {
  id: string;
  planName: string;
  status: string;
  activationDate: string;
  expiryDate: string;
  dataRemaining?: string;
  country: string;
}

export interface EsimSearchParams {
  country?: string;
  region?: string;
}

/**
 * Hook for managing eSIMs
 */
export function useEsimManagement() {
  const queryClient = useQueryClient();

  // Fetch owned eSIMs
  const ownedEsimsQuery = useQuery({
    queryKey: ['esims', 'owned'],
    queryFn: async () => {
      if (USE_MOCK_DATA) {
        // Return mock data
        return numberManagementMockData.ownedEsims;
      }
      const { data } = await api.get<OwnedEsim[]>('/esims/owned');
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Search for available eSIMs
  const searchAvailableEsims = (searchParams: EsimSearchParams) => {
    return useQuery({
      queryKey: ['esims', 'available', searchParams],
      queryFn: async () => {
        if (USE_MOCK_DATA) {
          // Return mock data with a slight delay to simulate API call
          await new Promise(resolve => setTimeout(resolve, 500));
          return numberManagementMockData.availableEsims;
        }
        const { data } = await api.get<AvailableEsim[]>('/esims/available', {
          params: searchParams,
        });
        return data;
      },
      enabled: false, // Don't run automatically, only when triggered
      staleTime: 60 * 1000, // 1 minute
    });
  };

  // Purchase an eSIM
  const purchaseEsimMutation = useMutation({
    mutationFn: async (planId: string) => {
      if (USE_MOCK_DATA) {
        // Simulate API call with a delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { data: { success: true } };
      }
      return api.post('/esims/purchase', { planId });
    },
    onSuccess: () => {
      // Invalidate owned eSIMs query to refresh the list
      queryClient.invalidateQueries({ queryKey: ['esims', 'owned'] });
      // Invalidate user balance
      queryClient.invalidateQueries({ queryKey: ['user', 'balance'] });
    },
  });

  // Get eSIM activation details
  const getEsimActivationDetails = (esimId: string) => {
    return useQuery({
      queryKey: ['esims', 'activation', esimId],
      queryFn: async () => {
        if (USE_MOCK_DATA) {
          // Simulate API call with a delay
          await new Promise(resolve => setTimeout(resolve, 700));
          return {
            qrCode: 'https://example.com/qrcode.png',
            activationCode: 'ESIM-1234-5678-9012',
            instructions: 'Scan this QR code with your device or enter the activation code manually.'
          };
        }
        const { data } = await api.get(`/esims/${esimId}/activation`);
        return data;
      },
      enabled: !!esimId, // Only run if esimId is provided
    });
  };

  // Get eSIM usage data
  const getEsimUsageData = (esimId: string) => {
    return useQuery({
      queryKey: ['esims', 'usage', esimId],
      queryFn: async () => {
        if (USE_MOCK_DATA) {
          // Simulate API call with a delay
          await new Promise(resolve => setTimeout(resolve, 600));
          return {
            dataUsed: '1.8 GB',
            dataRemaining: '3.2 GB',
            dataTotal: '5 GB',
            usagePercentage: 36,
            expiryDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 25).toISOString(), // 25 days from now
            usageHistory: [
              { date: '2023-06-10', usage: '0.2 GB' },
              { date: '2023-06-11', usage: '0.5 GB' },
              { date: '2023-06-12', usage: '0.3 GB' },
              { date: '2023-06-13', usage: '0.4 GB' },
              { date: '2023-06-14', usage: '0.4 GB' },
            ]
          };
        }
        const { data } = await api.get(`/esims/${esimId}/usage`);
        return data;
      },
      enabled: !!esimId, // Only run if esimId is provided
    });
  };

  return {
    // Queries
    ownedEsims: {
      data: ownedEsimsQuery.data || [],
      isLoading: ownedEsimsQuery.isLoading,
      error: ownedEsimsQuery.error,
      refetch: ownedEsimsQuery.refetch,
    },
    searchAvailableEsims,
    getEsimActivationDetails,
    getEsimUsageData,

    // Mutations
    purchaseEsim: purchaseEsimMutation.mutate,
    isPurchasing: purchaseEsimMutation.isPending,
    purchaseError: purchaseEsimMutation.error,
  };
}
