'use client';

import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { PlusIcon, PencilIcon, TrashIcon, CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from '../shared/LoadingSpinner';
import ErrorMessage from '../shared/ErrorMessage';

export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  template: string;
  category: string;
  version: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function PromptTemplateEditor() {
  // State
  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    description: '',
    template: '',
    category: 'general',
    isActive: true,
  });
  const [editTemplate, setEditTemplate] = useState<Partial<PromptTemplate>>({});
  
  // Get the query client
  const queryClient = useQueryClient();

  // Fetch prompt templates
  const {
    data: templates,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['ai', 'prompt-templates'],
    queryFn: async () => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock data
        return [
          {
            id: '1',
            name: 'Call Summary',
            description: 'Template for summarizing phone calls',
            template: 'You are a helpful assistant summarizing a customer service call. Please provide a concise summary of the following conversation: {{transcription}}',
            category: 'summarization',
            version: '1.0',
            isActive: true,
            createdAt: '2023-06-01T10:30:00Z',
            updatedAt: '2023-06-01T10:30:00Z',
          },
          {
            id: '2',
            name: 'SMS Response',
            description: 'Template for responding to SMS messages',
            template: 'You are a helpful SMS assistant. Keep responses concise and helpful (under 160 characters when possible). Respond to: {{message}}',
            category: 'messaging',
            version: '1.2',
            isActive: true,
            createdAt: '2023-06-05T14:45:00Z',
            updatedAt: '2023-06-10T09:15:00Z',
          },
          {
            id: '3',
            name: 'Voice Call Greeting',
            description: 'Template for initial voice call greeting',
            template: 'You are a voice assistant making a phone call. Introduce yourself briefly and state the purpose of the call: {{purpose}}',
            category: 'voice',
            version: '1.0',
            isActive: false,
            createdAt: '2023-06-10T09:15:00Z',
            updatedAt: '2023-06-10T09:15:00Z',
          },
        ] as PromptTemplate[];
      }
      
      // In production, fetch from API
      const { data } = await axios.get<PromptTemplate[]>('/api/ai/prompt-templates');
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Add template mutation
  const addTemplateMutation = useMutation({
    mutationFn: async (template: Omit<PromptTemplate, 'id' | 'version' | 'createdAt' | 'updatedAt'>) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Return mock success
        return { success: true, id: `${Date.now()}` };
      }
      
      // In production, call API
      const { data } = await axios.post('/api/ai/prompt-templates', template);
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['ai', 'prompt-templates'] });
      
      // Reset form
      setNewTemplate({
        name: '',
        description: '',
        template: '',
        category: 'general',
        isActive: true,
      });
      setIsAdding(false);
      
      // Show success toast
      console.log('Template added successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to add template');
    },
  });

  // Update template mutation
  const updateTemplateMutation = useMutation({
    mutationFn: async (template: Partial<PromptTemplate> & { id: string }) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.put(`/api/ai/prompt-templates/${template.id}`, template);
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['ai', 'prompt-templates'] });
      
      // Reset editing state
      setEditingId(null);
      setEditTemplate({});
      
      // Show success toast
      console.log('Template updated successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to update template');
    },
  });

  // Delete template mutation
  const deleteTemplateMutation = useMutation({
    mutationFn: async (id: string) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 600));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.delete(`/api/ai/prompt-templates/${id}`);
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['ai', 'prompt-templates'] });
      
      // Show success toast
      console.log('Template deleted successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to delete template');
    },
  });

  // Toggle template active status mutation
  const toggleTemplateMutation = useMutation({
    mutationFn: async ({ id, isActive }: { id: string; isActive: boolean }) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.patch(`/api/ai/prompt-templates/${id}`, { isActive });
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['ai', 'prompt-templates'] });
      
      // Show success toast
      console.log('Template status updated successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to update template status');
    },
  });

  // Handle form submission for adding a new template
  const handleAddSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    addTemplateMutation.mutate(newTemplate);
  };

  // Handle form submission for editing a template
  const handleEditSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingId && editTemplate) {
      updateTemplateMutation.mutate({ ...editTemplate, id: editingId });
    }
  };

  // Start editing a template
  const startEditing = (template: PromptTemplate) => {
    setEditingId(template.id);
    setEditTemplate({
      name: template.name,
      description: template.description,
      template: template.template,
      category: template.category,
      isActive: template.isActive,
    });
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingId(null);
    setEditTemplate({});
  };

  // Handle delete template
  const handleDelete = (id: string) => {
    if (window.confirm('Are you sure you want to delete this template?')) {
      deleteTemplateMutation.mutate(id);
    }
  };

  // Handle toggle template active status
  const handleToggleActive = (id: string, isActive: boolean) => {
    toggleTemplateMutation.mutate({ id, isActive });
  };

  // Get category label
  const getCategoryLabel = (category: string) => {
    switch (category) {
      case 'summarization':
        return 'Summarization';
      case 'messaging':
        return 'Messaging';
      case 'voice':
        return 'Voice';
      case 'general':
        return 'General';
      default:
        return category.charAt(0).toUpperCase() + category.slice(1);
    }
  };

  // Get category badge color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'summarization':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'messaging':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'voice':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
      case 'general':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700/50 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700/50 dark:text-gray-300';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-1">
            Prompt Templates
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Create and manage templates for AI interactions
          </p>
        </div>
        <button
          onClick={() => setIsAdding(true)}
          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          disabled={isAdding}
        >
          <PlusIcon className="h-4 w-4 mr-1" />
          Add Template
        </button>
      </div>

      {/* Add new template form */}
      {isAdding && (
        <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 p-4 mb-6">
          <h3 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            Add New Template
          </h3>
          <form onSubmit={handleAddSubmit} className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Name
              </label>
              <input
                type="text"
                id="name"
                value={newTemplate.name}
                onChange={(e) => setNewTemplate({ ...newTemplate, name: e.target.value })}
                className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                required
              />
            </div>
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Description
              </label>
              <input
                type="text"
                id="description"
                value={newTemplate.description}
                onChange={(e) => setNewTemplate({ ...newTemplate, description: e.target.value })}
                className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                required
              />
            </div>
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Category
              </label>
              <select
                id="category"
                value={newTemplate.category}
                onChange={(e) => setNewTemplate({ ...newTemplate, category: e.target.value })}
                className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="general">General</option>
                <option value="summarization">Summarization</option>
                <option value="messaging">Messaging</option>
                <option value="voice">Voice</option>
              </select>
            </div>
            <div>
              <label htmlFor="template" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Template
              </label>
              <textarea
                id="template"
                rows={6}
                value={newTemplate.template}
                onChange={(e) => setNewTemplate({ ...newTemplate, template: e.target.value })}
                className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="Enter your prompt template. Use {{variable}} for placeholders."
                required
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Use double curly braces for variables, e.g., {{'{{'}}transcription{{'}}'}}
              </p>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                checked={newTemplate.isActive}
                onChange={(e) => setNewTemplate({ ...newTemplate, isActive: e.target.checked })}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
              />
              <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Active
              </label>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setIsAdding(false)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                disabled={addTemplateMutation.isPending}
              >
                {addTemplateMutation.isPending ? (
                  <>
                    <LoadingSpinner size="sm" className="mr-2" />
                    Saving...
                  </>
                ) : (
                  'Save Template'
                )}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Templates list */}
      {isLoading ? (
        <div className="flex justify-center py-8">
          <LoadingSpinner size="lg" />
        </div>
      ) : error ? (
        <ErrorMessage message="Failed to load prompt templates. Please try again later." />
      ) : templates && templates.length > 0 ? (
        <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {templates.map((template) => (
              <li key={template.id} className="p-4">
                {editingId === template.id ? (
                  // Edit mode
                  <form onSubmit={handleEditSubmit} className="space-y-4">
                    <div>
                      <label htmlFor={`edit-name-${template.id}`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Name
                      </label>
                      <input
                        type="text"
                        id={`edit-name-${template.id}`}
                        value={editTemplate.name || ''}
                        onChange={(e) => setEditTemplate({ ...editTemplate, name: e.target.value })}
                        className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor={`edit-description-${template.id}`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Description
                      </label>
                      <input
                        type="text"
                        id={`edit-description-${template.id}`}
                        value={editTemplate.description || ''}
                        onChange={(e) => setEditTemplate({ ...editTemplate, description: e.target.value })}
                        className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor={`edit-category-${template.id}`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Category
                      </label>
                      <select
                        id={`edit-category-${template.id}`}
                        value={editTemplate.category || ''}
                        onChange={(e) => setEditTemplate({ ...editTemplate, category: e.target.value })}
                        className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      >
                        <option value="general">General</option>
                        <option value="summarization">Summarization</option>
                        <option value="messaging">Messaging</option>
                        <option value="voice">Voice</option>
                      </select>
                    </div>
                    <div>
                      <label htmlFor={`edit-template-${template.id}`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Template
                      </label>
                      <textarea
                        id={`edit-template-${template.id}`}
                        rows={6}
                        value={editTemplate.template || ''}
                        onChange={(e) => setEditTemplate({ ...editTemplate, template: e.target.value })}
                        className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        placeholder="Enter your prompt template. Use {{variable}} for placeholders."
                        required
                      />
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Use double curly braces for variables, e.g., {{'{{'}}transcription{{'}}'}}
                      </p>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id={`edit-isActive-${template.id}`}
                        checked={editTemplate.isActive}
                        onChange={(e) => setEditTemplate({ ...editTemplate, isActive: e.target.checked })}
                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                      />
                      <label htmlFor={`edit-isActive-${template.id}`} className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                        Active
                      </label>
                    </div>
                    <div className="flex justify-end space-x-3">
                      <button
                        type="button"
                        onClick={cancelEditing}
                        className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        disabled={updateTemplateMutation.isPending}
                      >
                        {updateTemplateMutation.isPending ? (
                          <>
                            <LoadingSpinner size="sm" className="mr-2" />
                            Saving...
                          </>
                        ) : (
                          'Save Changes'
                        )}
                      </button>
                    </div>
                  </form>
                ) : (
                  // View mode
                  <div>
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="text-md font-medium text-gray-900 dark:text-white">
                          {template.name}
                          {!template.isActive && (
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                              Inactive
                            </span>
                          )}
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          {template.description}
                        </p>
                        <div className="mt-2 flex items-center">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(template.category)}`}>
                            {getCategoryLabel(template.category)}
                          </span>
                          <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                            Version: {template.version}
                          </span>
                          <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                            Updated: {new Date(template.updatedAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleToggleActive(template.id, !template.isActive)}
                          className={`p-1 rounded-full ${
                            template.isActive
                              ? 'text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300'
                              : 'text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-400'
                          }`}
                          title={template.isActive ? 'Deactivate' : 'Activate'}
                        >
                          <CheckIcon className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => startEditing(template)}
                          className="p-1 rounded-full text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                          title="Edit"
                        >
                          <PencilIcon className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleDelete(template.id)}
                          className="p-1 rounded-full text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                          title="Delete"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                    <div className="mt-3">
                      <div className="bg-gray-50 dark:bg-gray-900/50 rounded-md p-3 font-mono text-sm text-gray-800 dark:text-gray-300 whitespace-pre-wrap">
                        {template.template}
                      </div>
                    </div>
                  </div>
                )}
              </li>
            ))}
          </ul>
        </div>
      ) : (
        <div className="text-center p-8 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No templates found</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Get started by creating a new prompt template.
          </p>
          <div className="mt-6">
            <button
              onClick={() => setIsAdding(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              Add Template
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
