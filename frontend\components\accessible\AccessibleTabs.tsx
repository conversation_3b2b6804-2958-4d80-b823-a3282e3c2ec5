import React, { useState, useRef, useEffect } from 'react';
import { twMerge } from 'tailwind-merge';

export interface TabItem {
  /**
   * The ID of the tab
   */
  id: string;
  
  /**
   * The label of the tab
   */
  label: string;
  
  /**
   * The content of the tab
   */
  content: React.ReactNode;
  
  /**
   * Whether the tab is disabled
   */
  disabled?: boolean;
}

export interface AccessibleTabsProps {
  /**
   * The tabs to display
   */
  tabs: TabItem[];
  
  /**
   * The ID of the initially selected tab
   */
  defaultTabId?: string;
  
  /**
   * The ID of the selected tab (controlled)
   */
  selectedTabId?: string;
  
  /**
   * Function to call when a tab is selected
   */
  onChange?: (tabId: string) => void;
  
  /**
   * The orientation of the tabs
   */
  orientation?: 'horizontal' | 'vertical';
  
  /**
   * Additional CSS classes to apply to the tabs container
   */
  className?: string;
  
  /**
   * Additional CSS classes to apply to the tab list
   */
  tabListClassName?: string;
  
  /**
   * Additional CSS classes to apply to the tab panels
   */
  tabPanelClassName?: string;
}

/**
 * An accessible tabs component with proper ARIA attributes and keyboard support.
 */
export const AccessibleTabs: React.FC<AccessibleTabsProps> = ({
  tabs,
  defaultTabId,
  selectedTabId: controlledTabId,
  onChange,
  orientation = 'horizontal',
  className,
  tabListClassName,
  tabPanelClassName,
}) => {
  // Determine if the component is controlled or uncontrolled
  const isControlled = controlledTabId !== undefined;
  
  // State for the selected tab ID
  const [selectedTabId, setSelectedTabId] = useState<string>(
    isControlled ? controlledTabId : (defaultTabId || tabs[0]?.id || '')
  );
  
  // Update internal state when controlled value changes
  useEffect(() => {
    if (isControlled && controlledTabId !== selectedTabId) {
      setSelectedTabId(controlledTabId);
    }
  }, [isControlled, controlledTabId, selectedTabId]);
  
  // Refs for the tab buttons
  const tabRefs = useRef<(HTMLButtonElement | null)[]>([]);
  
  // Handle tab selection
  const handleTabSelect = (tabId: string) => {
    if (!isControlled) {
      setSelectedTabId(tabId);
    }
    
    if (onChange) {
      onChange(tabId);
    }
  };
  
  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>, index: number) => {
    const tabCount = tabs.length;
    let nextIndex: number | null = null;
    
    switch (event.key) {
      case 'ArrowRight':
      case 'ArrowDown':
        // Move to the next tab
        nextIndex = (index + 1) % tabCount;
        break;
      case 'ArrowLeft':
      case 'ArrowUp':
        // Move to the previous tab
        nextIndex = (index - 1 + tabCount) % tabCount;
        break;
      case 'Home':
        // Move to the first tab
        nextIndex = 0;
        break;
      case 'End':
        // Move to the last tab
        nextIndex = tabCount - 1;
        break;
      default:
        return;
    }
    
    // Skip disabled tabs
    while (tabs[nextIndex]?.disabled && nextIndex !== index) {
      if (event.key === 'ArrowRight' || event.key === 'ArrowDown') {
        nextIndex = (nextIndex + 1) % tabCount;
      } else if (event.key === 'ArrowLeft' || event.key === 'ArrowUp') {
        nextIndex = (nextIndex - 1 + tabCount) % tabCount;
      }
    }
    
    // Focus and select the tab
    if (nextIndex !== null && nextIndex !== index && !tabs[nextIndex]?.disabled) {
      event.preventDefault();
      tabRefs.current[nextIndex]?.focus();
      handleTabSelect(tabs[nextIndex].id);
    }
  };
  
  // Generate a unique ID for the tabs
  const tabsId = useRef(`tabs-${Math.random().toString(36).substr(2, 9)}`);
  
  return (
    <div className={twMerge('w-full', className)}>
      {/* Tab list */}
      <div
        role="tablist"
        aria-orientation={orientation}
        className={twMerge(
          'flex',
          orientation === 'vertical' ? 'flex-col' : 'flex-row',
          'border-b border-gray-200',
          tabListClassName
        )}
      >
        {tabs.map((tab, index) => (
          <button
            key={tab.id}
            id={`${tabsId.current}-tab-${tab.id}`}
            ref={(el) => (tabRefs.current[index] = el)}
            role="tab"
            aria-selected={selectedTabId === tab.id}
            aria-controls={`${tabsId.current}-panel-${tab.id}`}
            tabIndex={selectedTabId === tab.id ? 0 : -1}
            onClick={() => !tab.disabled && handleTabSelect(tab.id)}
            onKeyDown={(e) => handleKeyDown(e, index)}
            disabled={tab.disabled}
            className={twMerge(
              'px-4 py-2 font-medium text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2',
              selectedTabId === tab.id
                ? 'border-b-2 border-purple-500 text-purple-600'
                : 'text-gray-500 hover:text-gray-700',
              tab.disabled && 'opacity-50 cursor-not-allowed'
            )}
          >
            {tab.label}
          </button>
        ))}
      </div>
      
      {/* Tab panels */}
      {tabs.map((tab) => (
        <div
          key={tab.id}
          id={`${tabsId.current}-panel-${tab.id}`}
          role="tabpanel"
          aria-labelledby={`${tabsId.current}-tab-${tab.id}`}
          hidden={selectedTabId !== tab.id}
          tabIndex={0}
          className={twMerge('p-4 focus:outline-none', tabPanelClassName)}
        >
          {tab.content}
        </div>
      ))}
    </div>
  );
};

export default AccessibleTabs;
