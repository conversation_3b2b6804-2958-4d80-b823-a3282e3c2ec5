const prisma = require('../lib/prisma');
const { asyncHandler } = require('../utils/errorHandler');
const logger = require('../utils/logger');
const config = require('../config');

/**
 * @desc    Search available eSIM plans
 * @route   GET /api/esims/available
 * @access  Private
 */
const getAvailableEsims = asyncHandler(async (req, res) => {
    try {
        const { region, dataAmount, duration, price } = req.query;
        
        // Build filter criteria
        const filter = {};
        
        if (region) {
            filter.region = { 
                contains: region, 
                mode: 'insensitive' 
            };
        }
        
        if (dataAmount) {
            // Convert to number and filter for plans with at least this much data
            const minData = parseInt(dataAmount);
            if (!isNaN(minData)) {
                filter.dataAmountMB = { gte: minData };
            }
        }
        
        if (duration) {
            // Convert to number and filter for plans with at least this duration
            const minDuration = parseInt(duration);
            if (!isNaN(minDuration)) {
                filter.durationDays = { gte: minDuration };
            }
        }
        
        if (price) {
            // Convert to number and filter for plans with at most this price
            const maxPrice = parseInt(price);
            if (!isNaN(maxPrice)) {
                filter.priceCreditAmount = { lte: maxPrice };
            }
        }
        
        // Only show active plans
        filter.isActive = true;
        
        // Query available eSIM plans
        const esimPlans = await prisma.esimPlan.findMany({
            where: filter,
            orderBy: [
                { region: 'asc' },
                { dataAmountMB: 'asc' }
            ],
            select: {
                id: true,
                name: true,
                description: true,
                provider: true,
                region: true,
                countries: true,
                dataAmountMB: true,
                durationDays: true,
                priceCreditAmount: true,
                features: true,
                isActive: true
            }
        });
        
        res.status(200).json({
            success: true,
            count: esimPlans.length,
            data: esimPlans
        });
    } catch (error) {
        logger.error('Error fetching available eSIMs:', error);
        throw error;
    }
});

/**
 * @desc    Purchase an eSIM plan
 * @route   POST /api/esims/purchase
 * @access  Private
 */
const purchaseEsim = asyncHandler(async (req, res) => {
    try {
        const { planId } = req.body;
        const userId = req.user.id;
        
        if (!planId) {
            return res.status(400).json({
                success: false,
                message: 'Plan ID is required'
            });
        }
        
        // Start a transaction since we need to perform multiple operations
        const result = await prisma.$transaction(async (prisma) => {
            // Get the eSIM plan
            const plan = await prisma.esimPlan.findUnique({
                where: { id: planId }
            });
            
            if (!plan) {
                throw new Error('eSIM plan not found');
            }
            
            if (!plan.isActive) {
                throw new Error('This eSIM plan is no longer available');
            }
            
            // Check if user has enough credits
            const user = await prisma.user.findUnique({
                where: { id: userId },
                select: { credits: true }
            });
            
            if (!user) {
                throw new Error('User not found');
            }
            
            if (user.credits < plan.priceCreditAmount) {
                throw new Error('Insufficient credits');
            }
            
            // Create the purchase record
            const purchase = await prisma.esimPurchase.create({
                data: {
                    userId,
                    planId,
                    purchaseDate: new Date(),
                    status: 'processing',
                    pricePaid: plan.priceCreditAmount,
                    metadata: {
                        planName: plan.name,
                        region: plan.region,
                        dataAmountMB: plan.dataAmountMB,
                        durationDays: plan.durationDays
                    }
                }
            });
            
            // Deduct credits from user
            await prisma.user.update({
                where: { id: userId },
                data: {
                    credits: { decrement: plan.priceCreditAmount }
                }
            });
            
            // Record the credit transaction
            await prisma.creditTransaction.create({
                data: {
                    userId,
                    amount: -plan.priceCreditAmount,
                    type: 'debit',
                    description: `eSIM purchase: ${plan.name}`,
                    relatedEntityType: 'esimPurchase',
                    relatedEntityId: purchase.id,
                    createdAt: new Date(),
                    status: 'completed'
                }
            });
            
            return { purchase, plan };
        });
        
        // After the transaction, initiate the actual eSIM provisioning
        // This would typically be done through an external API call to the eSIM provider
        // Here we'll simulate it with a delayed update
        setTimeout(async () => {
            try {
                // Simulate external API call to eSIM provider
                const esimDetails = await simulateEsimProvisioning(result.purchase.id, result.plan);
                
                // Update the purchase with the eSIM details
                await prisma.esimPurchase.update({
                    where: { id: result.purchase.id },
                    data: {
                        status: 'active',
                        activationDate: new Date(),
                        expiryDate: new Date(Date.now() + result.plan.durationDays * 24 * 60 * 60 * 1000),
                        esimIdentifier: esimDetails.esimId,
                        activationCode: esimDetails.activationCode,
                        qrCodeUrl: esimDetails.qrCodeUrl,
                        metadata: {
                            ...result.purchase.metadata,
                            provisionedAt: new Date().toISOString(),
                            provider: result.plan.provider
                        }
                    }
                });
                
                // Create a notification for the user
                await prisma.notification.create({
                    data: {
                        userId,
                        type: 'esimActivation',
                        title: 'eSIM Activated',
                        message: `Your eSIM for ${result.plan.region} has been activated and is ready to use.`,
                        read: false,
                        dismissed: false,
                        link: `/esims/${result.purchase.id}/activation`,
                        createdAt: new Date()
                    }
                });
                
                logger.info(`eSIM provisioned successfully for purchase ${result.purchase.id}`);
            } catch (error) {
                logger.error(`Error provisioning eSIM for purchase ${result.purchase.id}:`, error);
                
                // Update the purchase status to failed
                await prisma.esimPurchase.update({
                    where: { id: result.purchase.id },
                    data: {
                        status: 'failed',
                        metadata: {
                            ...result.purchase.metadata,
                            failureReason: error.message,
                            failedAt: new Date().toISOString()
                        }
                    }
                });
                
                // Create a notification for the user
                await prisma.notification.create({
                    data: {
                        userId,
                        type: 'esimActivationFailed',
                        title: 'eSIM Activation Failed',
                        message: `There was an issue activating your eSIM for ${result.plan.region}. Please contact support.`,
                        read: false,
                        dismissed: false,
                        link: `/support`,
                        createdAt: new Date()
                    }
                });
            }
        }, 5000); // Simulate a 5 second delay for the provisioning process
        
        res.status(200).json({
            success: true,
            message: 'eSIM purchase initiated successfully',
            data: {
                purchaseId: result.purchase.id,
                status: 'processing',
                message: 'Your eSIM is being provisioned. You will be notified once it is ready.'
            }
        });
    } catch (error) {
        logger.error('Error purchasing eSIM:', error);
        
        // Provide a user-friendly error message
        let message = 'An error occurred while purchasing the eSIM';
        
        if (error.message === 'eSIM plan not found') {
            return res.status(404).json({
                success: false,
                message: 'The requested eSIM plan was not found'
            });
        }
        
        if (error.message === 'This eSIM plan is no longer available') {
            return res.status(400).json({
                success: false,
                message: 'This eSIM plan is no longer available'
            });
        }
        
        if (error.message === 'Insufficient credits') {
            return res.status(400).json({
                success: false,
                message: 'You do not have enough credits to purchase this eSIM plan'
            });
        }
        
        throw error;
    }
});

/**
 * @desc    List owned eSIMs
 * @route   GET /api/esims/owned
 * @access  Private
 */
const getOwnedEsims = asyncHandler(async (req, res) => {
    try {
        const userId = req.user.id;
        const { status } = req.query;
        
        // Build filter
        const filter = { userId };
        
        if (status) {
            filter.status = status;
        }
        
        // Query owned eSIMs
        const esimPurchases = await prisma.esimPurchase.findMany({
            where: filter,
            orderBy: { purchaseDate: 'desc' },
            include: {
                plan: {
                    select: {
                        name: true,
                        region: true,
                        dataAmountMB: true,
                        durationDays: true,
                        provider: true
                    }
                }
            }
        });
        
        res.status(200).json({
            success: true,
            count: esimPurchases.length,
            data: esimPurchases
        });
    } catch (error) {
        logger.error('Error fetching owned eSIMs:', error);
        throw error;
    }
});

/**
 * @desc    Get eSIM activation details
 * @route   GET /api/esims/:esimPurchaseId/activation
 * @access  Private
 */
const getEsimActivationDetails = asyncHandler(async (req, res) => {
    try {
        const { esimPurchaseId } = req.params;
        const userId = req.user.id;
        
        // Get eSIM purchase
        const esimPurchase = await prisma.esimPurchase.findUnique({
            where: { id: esimPurchaseId },
            include: {
                plan: {
                    select: {
                        name: true,
                        region: true,
                        countries: true,
                        dataAmountMB: true,
                        durationDays: true,
                        provider: true,
                        features: true
                    }
                }
            }
        });
        
        if (!esimPurchase) {
            return res.status(404).json({
                success: false,
                message: 'eSIM purchase not found'
            });
        }
        
        // Check if the purchase belongs to the user
        if (esimPurchase.userId !== userId) {
            return res.status(403).json({
                success: false,
                message: 'You do not have permission to access this eSIM'
            });
        }
        
        // Prepare the response data
        let response = {
            id: esimPurchase.id,
            purchaseDate: esimPurchase.purchaseDate,
            status: esimPurchase.status,
            planName: esimPurchase.plan.name,
            region: esimPurchase.plan.region,
            countries: esimPurchase.plan.countries,
            dataAmountMB: esimPurchase.plan.dataAmountMB,
            durationDays: esimPurchase.plan.durationDays,
            provider: esimPurchase.plan.provider,
            features: esimPurchase.plan.features
        };
        
        // Only include activation details if the eSIM is active
        if (esimPurchase.status === 'active') {
            response = {
                ...response,
                activationDate: esimPurchase.activationDate,
                expiryDate: esimPurchase.expiryDate,
                esimIdentifier: esimPurchase.esimIdentifier,
                activationCode: esimPurchase.activationCode,
                qrCodeUrl: esimPurchase.qrCodeUrl,
                activationInstructions: getActivationInstructions(esimPurchase.plan.provider)
            };
        } else if (esimPurchase.status === 'processing') {
            response = {
                ...response,
                estimatedActivationTime: new Date(esimPurchase.purchaseDate.getTime() + 5 * 60 * 1000) // Estimate 5 minutes from purchase
            };
        } else if (esimPurchase.status === 'failed') {
            response = {
                ...response,
                failureReason: esimPurchase.metadata.failureReason,
                failedAt: esimPurchase.metadata.failedAt,
                supportContactInfo: config.support.contactInfo
            };
        }
        
        res.status(200).json({
            success: true,
            data: response
        });
    } catch (error) {
        logger.error('Error fetching eSIM activation details:', error);
        throw error;
    }
});

/**
 * @desc    Get eSIM usage data
 * @route   GET /api/esims/:esimInstanceId/usage
 * @access  Private
 */
const getEsimUsage = asyncHandler(async (req, res) => {
    try {
        const { esimInstanceId } = req.params;
        const userId = req.user.id;
        
        // Get eSIM purchase
        const esimPurchase = await prisma.esimPurchase.findUnique({
            where: { id: esimInstanceId },
            include: {
                plan: {
                    select: {
                        dataAmountMB: true,
                        durationDays: true
                    }
                }
            }
        });
        
        if (!esimPurchase) {
            return res.status(404).json({
                success: false,
                message: 'eSIM not found'
            });
        }
        
        // Check if the purchase belongs to the user
        if (esimPurchase.userId !== userId) {
            return res.status(403).json({
                success: false,
                message: 'You do not have permission to access this eSIM'
            });
        }
        
        // Get usage data if the eSIM is active
        if (esimPurchase.status !== 'active') {
            return res.status(400).json({
                success: false,
                message: 'Usage data is only available for active eSIMs'
            });
        }
        
        // This would typically involve making an API call to the eSIM provider
        // to get the actual usage data. For now, we'll simulate it.
        const usageData = await simulateEsimUsageData(esimPurchase);
        
        res.status(200).json({
            success: true,
            data: usageData
        });
    } catch (error) {
        logger.error('Error fetching eSIM usage data:', error);
        throw error;
    }
});

/**
 * Simulate eSIM provisioning with a provider
 * In a real implementation, this would make API calls to the eSIM provider
 * @param {string} purchaseId - The purchase ID
 * @param {object} plan - The eSIM plan details
 * @returns {Promise<object>} - The simulated eSIM details
 */
const simulateEsimProvisioning = async (purchaseId, plan) => {
    // Simulate an API call to the eSIM provider
    return {
        esimId: `ESIM-${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
        activationCode: `${Math.random().toString(36).substr(2, 4)}-${Math.random().toString(36).substr(2, 4)}-${Math.random().toString(36).substr(2, 4)}-${Math.random().toString(36).substr(2, 4)}`.toUpperCase(),
        qrCodeUrl: `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=ESIM:${purchaseId}:${Date.now()}`,
        provider: plan.provider,
        region: plan.region,
        dataAmountMB: plan.dataAmountMB,
        durationDays: plan.durationDays
    };
};

/**
 * Simulate eSIM usage data
 * In a real implementation, this would make API calls to the eSIM provider
 * @param {object} esimPurchase - The eSIM purchase details
 * @returns {Promise<object>} - The simulated usage data
 */
const simulateEsimUsageData = async (esimPurchase) => {
    // Calculate days remaining
    const now = new Date();
    const expiryDate = esimPurchase.expiryDate;
    const totalDays = esimPurchase.plan.durationDays;
    
    // Calculate elapsed days (clamp to avoid negative values)
    const elapsedDays = Math.min(
        Math.max(0, Math.floor((now - esimPurchase.activationDate) / (1000 * 60 * 60 * 24))), 
        totalDays
    );
    
    // Calculate days remaining (clamp to avoid negative values)
    const daysRemaining = Math.max(0, Math.floor((expiryDate - now) / (1000 * 60 * 60 * 24)));
    
    // Simulate data usage based on elapsed time (more data used as time passes)
    // Randomize a bit to make it look realistic
    const totalDataMB = esimPurchase.plan.dataAmountMB;
    const usageRatio = elapsedDays / totalDays;
    const baseUsage = totalDataMB * usageRatio;
    const randomFactor = (Math.random() - 0.5) * 0.2; // +/- 10% randomness
    const dataUsedMB = Math.min(totalDataMB, Math.max(0, baseUsage * (1 + randomFactor)));
    const dataRemainingMB = Math.max(0, totalDataMB - dataUsedMB);
    
    return {
        totalDataMB,
        dataUsedMB: Math.round(dataUsedMB * 100) / 100, // Round to 2 decimal places
        dataRemainingMB: Math.round(dataRemainingMB * 100) / 100,
        daysRemaining,
        expiryDate,
        lastUpdated: new Date()
    };
};

/**
 * Get activation instructions based on provider
 * @param {string} provider - The eSIM provider name
 * @returns {string} - Activation instructions
 */
const getActivationInstructions = (provider) => {
    // Provide generic instructions for now
    return `
1. Ensure you have a stable internet connection (Wi-Fi recommended).
2. Go to your phone's Settings > Cellular/Mobile Data > Add Cellular/Mobile Plan.
3. Scan the QR code provided in your activation details.
4. Follow the on-screen prompts to complete the setup.
5. If QR code scanning is not available, you may need to enter the activation code manually.
6. For specific instructions for your device, please consult your device manufacturer's documentation or the ${provider} support website.
    `;
};

module.exports = {
    getAvailableEsims,
    purchaseEsim,
    getOwnedEsims,
    getEsimActivationDetails,
    getEsimUsage
};
