// File: get-current-user-id.js
// This script can be pasted into the browser console to get the current user ID

(function() {
  // Try to get the supabase session from localStorage
  const supabaseKey = Object.keys(localStorage).find(key => 
    key.startsWith('sb-') && key.endsWith('-auth-token')
  );
  
  if (!supabaseKey) {
    console.error('No Supabase session found in localStorage');
    return null;
  }
  
  try {
    const sessionStr = localStorage.getItem(supabaseKey);
    const session = JSON.parse(sessionStr);
    
    if (session && session.user && session.user.id) {
      console.log('Current user ID:', session.user.id);
      console.log('Email:', session.user.email);
      return session.user.id;
    } else {
      console.error('Session found but no user ID');
      return null;
    }
  } catch (error) {
    console.error('Error parsing session:', error);
    return null;
  }
})();
