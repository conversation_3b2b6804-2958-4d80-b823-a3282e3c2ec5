"use client";

import React, { useState, useEffect } from 'react';
import { 
  PhoneIcon, 
  ClockIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  ArrowPathIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  ChevronDownIcon,
  EllipsisVerticalIcon,
  ChatBubbleLeftRightIcon,
  ArrowTopRightOnSquareIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';

export function CallList() {
  const [calls, setCalls] = useState([]);
  const [filteredCalls, setFilteredCalls] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date');
  const [sortOrder, setSortOrder] = useState('desc');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCall, setSelectedCall] = useState(null);

  // Mock data loading
  useEffect(() => {
    const mockCalls = [
      {
        id: 'call-001',
        caller: 'John Smith',
        phone: '+****************',
        date: new Date(2023, 3, 15, 14, 30),
        status: 'resolved',
        duration: '2m 15s',
        tags: ['billing', 'urgent'],
        aiResponse: 'I\'ve scheduled a callback from our billing specialist tomorrow at 2pm. Would you prefer a different time?',
        customerResponse: 'No, 2pm works for me. Thanks!',
        outcome: 'Scheduled callback',
        notes: 'Customer had questions about recent bill increase',
        priority: 'medium'
      },
      {
        id: 'call-002',
        caller: 'Sarah Johnson',
        phone: '+****************',
        date: new Date(2023, 3, 15, 9, 45),
        status: 'pending',
        duration: '1m 32s',
        tags: ['technical', 'followup'],
        aiResponse: 'I understand you\'re having issues with your internet connection. I can help troubleshoot this. Have you tried restarting your router?',
        customerResponse: 'Yes, I\'ve tried that already but it\'s still not working',
        outcome: 'In progress',
        notes: 'Escalated to tier 2 technical support',
        priority: 'high'
      },
      {
        id: 'call-003',
        caller: 'Michael Brown',
        phone: '+****************',
        date: new Date(2023, 3, 14, 16, 20),
        status: 'resolved',
        duration: '3m 45s',
        tags: ['account', 'general'],
        aiResponse: 'I can help you update your address on file. Could you confirm your new address?',
        customerResponse: '123 New Street, Apt 4B, New York, NY 10001',
        outcome: 'Information updated',
        notes: 'Customer moved to new address, all details updated in system',
        priority: 'low'
      },
      {
        id: 'call-004',
        caller: 'Emily Wilson',
        phone: '+****************',
        date: new Date(2023, 3, 14, 10, 15),
        status: 'missed',
        duration: '0m 0s',
        tags: ['sales', 'new'],
        aiResponse: 'I notice you called about our premium subscription. I\'d be happy to provide more details or answer any questions you have.',
        customerResponse: '',
        outcome: 'No response',
        notes: 'Attempted follow-up via SMS, no response yet',
        priority: 'medium'
      },
      {
        id: 'call-005',
        caller: 'David Martinez',
        phone: '+****************',
        date: new Date(2023, 3, 13, 13, 50),
        status: 'resolved',
        duration: '4m 20s',
        tags: ['product', 'feedback'],
        aiResponse: 'Thank you for your feedback on our new feature. I\'ve documented your suggestions and will pass them to our product team.',
        customerResponse: 'Great, thanks for listening!',
        outcome: 'Feedback recorded',
        notes: 'Customer had valuable insights for product improvement',
        priority: 'medium'
      },
      {
        id: 'call-006',
        caller: 'Jennifer Garcia',
        phone: '+****************',
        date: new Date(2023, 3, 13, 9, 10),
        status: 'pending',
        duration: '1m 55s',
        tags: ['billing', 'urgent'],
        aiResponse: 'I understand your concern about the additional charge. Let me look into this for you right away.',
        customerResponse: 'Please do, I think there\'s been a mistake',
        outcome: 'Under investigation',
        notes: 'Billing department reviewing transaction history',
        priority: 'high'
      }
    ];
    
    setTimeout(() => {
      setCalls(mockCalls);
      setFilteredCalls(mockCalls);
      setIsLoading(false);
    }, 1000);
  }, []);

  // Filter and sort calls
  useEffect(() => {
    let result = [...calls];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(call => 
        call.caller.toLowerCase().includes(query) || 
        call.phone.includes(query) ||
        call.notes.toLowerCase().includes(query) ||
        call.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    // Apply status filter
    if (statusFilter !== 'all') {
      result = result.filter(call => call.status === statusFilter);
    }
    
    // Apply sort
    result.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'date':
          comparison = a.date - b.date;
          break;
        case 'name':
          comparison = a.caller.localeCompare(b.caller);
          break;
        case 'priority':
          const priorityOrder = { high: 0, medium: 1, low: 2 };
          comparison = priorityOrder[a.priority] - priorityOrder[b.priority];
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        default:
          comparison = a.date - b.date;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });
    
    setFilteredCalls(result);
  }, [calls, searchQuery, statusFilter, sortBy, sortOrder]);

  // Get status badge style
  const getStatusBadge = (status) => {
    switch (status) {
      case 'resolved':
        return 'bg-green-900/30 text-green-400 border-green-500/30';
      case 'pending':
        return 'bg-yellow-900/30 text-yellow-400 border-yellow-500/30';
      case 'missed':
        return 'bg-red-900/30 text-red-400 border-red-500/30';
      default:
        return 'bg-gray-900/30 text-gray-400 border-gray-500/30';
    }
  };

  // Get priority indicator style
  const getPriorityIndicator = (priority) => {
    switch (priority) {
      case 'high':
        return 'bg-red-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Format date
  const formatDate = (date) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date >= today) {
      return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (date >= yesterday) {
      return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' }) + 
             ` at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    }
  };

  // Toggle call details
  const toggleCallDetails = (call) => {
    if (selectedCall && selectedCall.id === call.id) {
      setSelectedCall(null);
    } else {
      setSelectedCall(call);
    }
  };

  return (
    <div className="h-full flex flex-col overflow-hidden bg-gray-900">
      <div className="p-5 sm:p-6 border-b border-gray-800">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h3 className="text-xl font-bold text-white flex items-center">
            <PhoneIcon className="h-5 w-5 mr-2 text-indigo-400" />
            Call Management
          </h3>
          
          {/* Search and filters */}
          <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
            <div className="relative flex-grow">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
              <input
                type="text"
                placeholder="Search calls..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-900 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              />
            </div>
            <button 
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-3 py-2 bg-gray-800 hover:bg-gray-700 transition-colors rounded-lg border border-gray-700"
            >
              <AdjustmentsHorizontalIcon className="h-5 w-5 text-indigo-400" />
              <span>Filters</span>
              <ChevronDownIcon className={`h-4 w-4 text-gray-400 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
            </button>
          </div>
        </div>
        
        {/* Filter panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-900/60 rounded-lg border border-gray-800 grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Status</label>
              <select 
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="all">All Statuses</option>
                <option value="resolved">Resolved</option>
                <option value="pending">Pending</option>
                <option value="missed">Missed</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Sort By</label>
              <select 
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="date">Date & Time</option>
                <option value="name">Caller Name</option>
                <option value="priority">Priority</option>
                <option value="status">Status</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">Order</label>
              <select 
                value={sortOrder}
                onChange={(e) => setSortOrder(e.target.value)}
                className="w-full p-2 bg-gray-800 border border-gray-700 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="desc">Newest First</option>
                <option value="asc">Oldest First</option>
              </select>
            </div>
          </div>
        )}
      </div>
      
      {/* Call list */}
      <div className="flex-grow overflow-auto">
        {isLoading ? (
          <div className="flex justify-center items-center h-full">
            <ArrowPathIcon className="h-8 w-8 text-indigo-500 animate-spin" />
            <span className="ml-3 text-lg text-gray-300">Loading calls...</span>
          </div>
        ) : filteredCalls.length === 0 ? (
          <div className="flex flex-col justify-center items-center h-full text-center p-6">
            <XCircleIcon className="h-12 w-12 text-gray-500 mb-3" />
            <h3 className="text-lg font-medium text-gray-300 mb-1">No calls found</h3>
            <p className="text-gray-500">Try adjusting your filters or search query</p>
          </div>
        ) : (
          <ul className="divide-y divide-gray-800">
            {filteredCalls.map(call => (
              <li 
                key={call.id} 
                className={`
                  hover:bg-gray-800/50 transition-colors
                  ${selectedCall && selectedCall.id === call.id ? 'bg-gray-800/70' : ''}
                `}
              >
                <div className="p-4 cursor-pointer" onClick={() => toggleCallDetails(call)}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <div className={`mt-1 h-3 w-3 rounded-full flex-shrink-0 ${getPriorityIndicator(call.priority)}`}></div>
                      <div>
                        <h4 className="font-medium text-white">
                          <Link href={`/crm/customer/${call.customerId}`} className="hover:text-indigo-400 transition-colors">
                            {call.caller} <ArrowTopRightOnSquareIcon className="h-3 w-3 inline ml-1" />
                          </Link>
                        </h4>
                        <p className="text-gray-400 text-sm">{call.phone}</p>
                        <div className="flex items-center text-gray-500 text-xs mt-1">
                          <ClockIcon className="h-3.5 w-3.5 mr-1" />
                          <span>{formatDate(call.date)}</span>
                          {call.duration !== '0m 0s' && (
                            <>
                              <span className="mx-1">•</span>
                              <span>{call.duration}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className={`text-xs px-2.5 py-1 rounded-full border ${getStatusBadge(call.status)}`}>
                        {call.status.charAt(0).toUpperCase() + call.status.slice(1)}
                      </div>
                      <button className="text-gray-400 hover:text-white transition-colors">
                        <EllipsisVerticalIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                  
                  {/* Tags */}
                  {call.tags.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-1.5">
                      {call.tags.map((tag, index) => (
                        <span 
                          key={index} 
                          className="text-xs px-2 py-0.5 bg-gray-700/50 text-gray-300 rounded-full"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
                
                {/* Call details panel */}
                {selectedCall && selectedCall.id === call.id && (
                  <div className="px-4 pb-4 pt-1 bg-gray-800/40 border-t border-gray-700/50">
                    <div className="text-sm text-gray-300 mb-2 space-y-3">
                      {/* AI Response */}
                      <div className="bg-indigo-900/30 p-3 rounded-lg border border-indigo-700/30">
                        <div className="flex items-center mb-1.5">
                          <div className="w-6 h-6 bg-indigo-600/50 rounded-full mr-2 flex items-center justify-center">
                            <span className="text-xs font-bold">AI</span>
                          </div>
                          <span className="text-xs font-medium text-indigo-300">AI Response</span>
                        </div>
                        <p className="text-gray-200 ml-8">{call.aiResponse}</p>
                      </div>
                      
                      {/* Customer Response (if any) */}
                      {call.customerResponse && (
                        <div className="bg-gray-800/70 p-3 rounded-lg border border-gray-700/30">
                          <div className="flex items-center mb-1.5">
                            <div className="w-6 h-6 bg-gray-700/70 rounded-full mr-2 flex items-center justify-center">
                              <span className="text-xs font-bold">C</span>
                            </div>
                            <span className="text-xs font-medium text-gray-400">Customer Response</span>
                          </div>
                          <p className="text-gray-300 ml-8">{call.customerResponse}</p>
                        </div>
                      )}
                      
                      {/* Outcome */}
                      <div className="bg-gray-900/50 p-3 rounded-lg border border-gray-700/30">
                        <div className="flex items-start">
                          <div className="flex-shrink-0">
                            <span className="text-xs font-medium text-gray-400 mr-2">Outcome:</span>
                          </div>
                          <div>
                            <p className="text-gray-300">{call.outcome}</p>
                            <p className="text-gray-400 text-xs mt-1.5">{call.notes}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Action buttons */}
                    <div className="flex justify-end space-x-2 pt-2">
                      <button className="bg-gray-800 hover:bg-gray-700 text-white px-3 py-1.5 rounded-lg text-sm flex items-center">
                        <PencilIcon className="h-4 w-4 mr-1" />
                        Edit
                      </button>
                      <Link 
                        href={`/crm/customer/${call.customerId}`} 
                        className="bg-indigo-600/70 hover:bg-indigo-600 text-white px-3 py-1.5 rounded-lg text-sm flex items-center"
                      >
                        <ArrowTopRightOnSquareIcon className="h-4 w-4 mr-1" />
                        View Customer
                      </Link>
                    </div>
                  </div>
                )}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
} 