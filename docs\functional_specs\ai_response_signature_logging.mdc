---
description: 
globs: 
alwaysApply: false
---
---
description: Defines the standard format and process for logging AI model interactions and responses.
---
# AI Response Signature Logging (`ai_response_signature_logging.mdc`)

## 1. Purpose and Scope

**Purpose:** To establish a standardized method for logging key details of interactions with AI models within the CallSaver platform. This ensures traceability, aids debugging, supports performance monitoring, enables auditing, and provides data for potential model fine-tuning or analysis.

**Scope:**
- Definition of an "AI Response Signature".
- Logging format (structured JSON).
- Target logging destination (e.g., dedicated database table `AiInteractionLog`, structured log stream).
- Data points to capture for various AI interaction types (e.g., call summarization, transcription, intent recognition, agent assistance).
- Data retention policies.
- PII masking and privacy considerations.
- Integration points within the AI Integration Layer.

## 2. AI Response Signature Definition

An AI Response Signature is a structured record capturing the essential metadata and data points of a single significant AI model invocation and its result.

**Core Fields (Common to most interactions):**
- `interactionId`: Unique identifier for this specific AI interaction log entry (UUID).
- `correlationId`: Identifier linking this interaction to a broader request or workflow (e.g., call SID, message ID, user session ID).
- `timestamp`: ISO 8601 timestamp of when the interaction occurred (UTC).
- `userId`: Identifier of the user associated with the request (if applicable).
- `organizationId`: Identifier of the organization associated with the request (if applicable).
- `featureArea`: The application feature triggering the AI (e.g., `CallSummarization`, `Transcription`, `SentimentAnalysis`, `EsimRecommendation`, `AutomationRuleExecution`).
- `aiModelUsed`: Identifier of the specific AI model and version used (e.g., `openai/gpt-4-turbo-2024-04-09`, `anthropic/claude-3-opus-20240229`, `google/gemini-1.5-pro-latest`).
- `promptHash`: A hash (e.g., SHA-256) of the core prompt sent to the model (to detect prompt changes without logging full potentially large/sensitive prompts). Consider logging full prompts only in specific debug modes or environments.
- `inputDataSignature`: A representation or hash of key input data characteristics (e.g., audio duration for transcription, text length for summarization). Avoid logging raw sensitive input data directly here.
- `responseSignature`: A representation or hash of key output characteristics (e.g., summary length, transcription word count, detected intents, confidence score).
- `latencyMs`: Duration of the AI model call in milliseconds.
- `costUnits`: Estimated or actual cost units consumed (if applicable, based on provider pricing).
- `statusCode`: Status of the AI interaction (e.g., `SUCCESS`, `ERROR_PROVIDER`, `ERROR_TIMEOUT`, `ERROR_FILTERED`, `ERROR_INTERNAL`).
- `errorCode`: Specific error code if `statusCode` indicates an error.
- `userFeedbackReference`: Link or identifier to any associated user feedback mechanism (e.g., thumbs up/down interaction ID).

**Optional/Context-Specific Fields:**
- `confidenceScore`: Model's confidence score for the primary output (if provided).
- `detectedIntents`: List of intents recognized (for NLU tasks).
- `extractedEntities`: Key entities extracted.
- `sentimentScore`: Calculated sentiment score.
- `languageCode`: Detected language of input/output.
- `contentFilterResult`: Result from content safety filters (e.g., `PASSED`, `FLAGGED_HATE`, `FLAGGED_PII`).

## 3. Logging Mechanism

- **Format:** Structured JSON logs.
- **Destination:**
    - **Primary:** Dedicated database table (e.g., `AiInteractionLog` in Prisma schema) for querying and analysis.
    - **Secondary:** Forwarded to a centralized logging system (e.g., Datadog, ELK stack) for real-time monitoring and alerting.
- **Implementation:** Logging should occur within the AI Integration Layer or specific AI service wrappers immediately after an interaction completes or fails.

## 4. Data Retention and Privacy

- **Retention Policy:** Define retention periods based on business needs and compliance requirements (e.g., 90 days for detailed logs, longer for aggregated metrics). This should be configurable.
- **PII Masking:**
    - **Prompts/Inputs:** Avoid logging raw prompts/inputs containing potential PII in the standard signature log. Use hashes or signatures. Full prompts might be logged conditionally in secure, access-controlled environments for debugging, with stricter retention.
    - **Outputs:** Analyze outputs for potential PII and apply masking techniques before logging the `responseSignature` if necessary.
    - Adhere to GDPR/CCPA requirements regarding data minimization and user data rights. Refer to `gdpr_ccpa_compliance_tracking.mdc` (to be created).

## 5. Use Cases

- **Debugging:** Trace specific AI failures or unexpected outputs.
- **Auditing:** Provide an audit trail of AI decisions or information generation.
- **Performance Monitoring:** Track latency, error rates, and costs per model/feature.
- **Abuse Detection:** Identify patterns of malicious use or prompt injection attempts.
- **Fine-tuning Data:** Select high-quality interactions (e.g., those with positive user feedback) as candidates for model fine-tuning datasets (requires careful handling of privacy).
- **A/B Testing:** Compare performance metrics between different models or prompts.

## 6. Related Documents

- `docs/functional_specs/ai_integration_layer.mdc`
- `docs/functional_specs/ai_execution_log.mdc`
- `docs/functional_specs/task_queue.mdc`
- `docs/functional_specs/gdpr_ccpa_compliance_tracking.mdc` (to be created)
- `back/backend/prisma/schema.prisma` (requires `AiInteractionLog` model)
