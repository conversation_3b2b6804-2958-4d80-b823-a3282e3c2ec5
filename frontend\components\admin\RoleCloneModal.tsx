'use client';

import { useState, Fragment, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Role, useRoleManagement } from '../../hooks/useRoleManagement';
import { Spinner } from '../ui/Spinner';
import toast from 'react-hot-toast';

interface RoleCloneModalProps {
  role: Role | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function RoleCloneModal({
  role,
  isOpen,
  onClose,
}: RoleCloneModalProps) {
  // Form state
  const [newName, setNewName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get role management functions
  const { cloneRole } = useRoleManagement();

  // Reset form when modal opens/closes or role changes
  useEffect(() => {
    if (isOpen && role) {
      setNewName(`${role.name} (Copy)`);
      setError(null);
    }
  }, [isOpen, role]);

  // Validate form
  const validateForm = (): boolean => {
    if (!newName.trim()) {
      setError('Role name is required');
      return false;
    }
    
    if (newName.trim().length < 2) {
      setError('Role name must be at least 2 characters');
      return false;
    }
    
    if (newName.trim().length > 50) {
      setError('Role name must be less than 50 characters');
      return false;
    }
    
    return true;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!role || !validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await cloneRole(role.id, newName.trim());
      onClose();
      toast.success(`Role "${role.name}" cloned successfully as "${newName.trim()}"`);
    } catch (err: any) {
      setError(err.message || 'Failed to clone role');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!role) return null;

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={onClose}
                    disabled={isSubmitting}
                  >
                    <span className="sr-only">Close</span>
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-semibold leading-6 text-gray-900 dark:text-white"
                    >
                      Clone Role: {role.name}
                    </Dialog.Title>
                    <div className="mt-4">
                      <form onSubmit={handleSubmit} className="space-y-4">
                        {/* New Role Name */}
                        <div>
                          <label
                            htmlFor="newName"
                            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                          >
                            New Role Name <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            id="newName"
                            name="newName"
                            value={newName}
                            onChange={(e) => setNewName(e.target.value)}
                            className={`mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm ${
                              error ? 'border-red-500' : ''
                            }`}
                            placeholder="e.g., Support Agent (Copy)"
                            disabled={isSubmitting}
                          />
                          {error && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-500">
                              {error}
                            </p>
                          )}
                        </div>

                        <div className="mt-2">
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            This will create a new role with the same permissions and parent role as "{role.name}".
                          </p>
                        </div>

                        {/* Form Actions */}
                        <div className="mt-5 sm:mt-6 sm:flex sm:flex-row-reverse">
                          <button
                            type="submit"
                            className="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 sm:ml-3 sm:w-auto"
                            disabled={isSubmitting}
                          >
                            {isSubmitting ? (
                              <Spinner size="sm" className="mr-2" />
                            ) : null}
                            Clone Role
                          </button>
                          <button
                            type="button"
                            className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 sm:mt-0 sm:w-auto"
                            onClick={onClose}
                            disabled={isSubmitting}
                          >
                            Cancel
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
