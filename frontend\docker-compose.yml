version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: deps # Use the deps stage for development
    command: npm run dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - DATABASE_URL=********************************************/callsaver?schema=public
      - MONGODB_URI=mongodb://mongo:27017/callsaver
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=development_secret_key
    depends_on:
      - postgres
      - mongo

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=callsaver
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  mongo:
    image: mongo:6-jammy
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

volumes:
  postgres_data:
  mongo_data: 