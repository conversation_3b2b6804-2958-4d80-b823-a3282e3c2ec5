/**
 * E2E Test for Call and SMS Handling Journey
 * 
 * This test covers the process of viewing and managing call logs and SMS messages,
 * ensuring users can track communication through the dashboard.
 */

describe('Call and SMS Handling Journey', () => {
  beforeEach(() => {
    // Log in before each test
    cy.login();
    
    // Mock API responses
    cy.intercept('GET', '/api/calls*', {
      statusCode: 200,
      body: {
        calls: [
          {
            id: 'call1',
            callSid: 'CA123456789',
            from: '+15551112222',
            to: '+15551234567',
            direction: 'inbound',
            status: 'completed',
            duration: 125,
            recordingUrl: 'https://example.com/recording.mp3',
            transcription: 'Hello, I would like to inquire about your services.',
            sentiment: 'positive',
            createdAt: new Date().toISOString(),
            aiHandled: true
          },
          {
            id: 'call2',
            callSid: 'CA987654321',
            from: '+15551234567',
            to: '+15553334444',
            direction: 'outbound',
            status: 'completed',
            duration: 95,
            recordingUrl: 'https://example.com/recording2.mp3',
            transcription: 'Thank you for your inquiry. We offer a variety of services.',
            sentiment: 'neutral',
            createdAt: new Date(Date.now() - 86400000).toISOString(),
            aiHandled: false
          }
        ],
        total: 2,
        page: 1,
        totalPages: 1
      }
    }).as('getCalls');
    
    cy.intercept('GET', '/api/calls/call1', {
      statusCode: 200,
      body: {
        id: 'call1',
        callSid: 'CA123456789',
        from: '+15551112222',
        to: '+15551234567',
        direction: 'inbound',
        status: 'completed',
        duration: 125,
        recordingUrl: 'https://example.com/recording.mp3',
        transcription: 'Hello, I would like to inquire about your services.',
        aiResponse: 'Thank you for your inquiry. Our services include...',
        sentiment: 'positive',
        createdAt: new Date().toISOString(),
        aiHandled: true,
        forwardedTo: null,
        notes: '',
        tags: []
      }
    }).as('getCallDetails');
    
    cy.intercept('GET', '/api/messages*', {
      statusCode: 200,
      body: {
        messages: [
          {
            id: 'msg1',
            messageSid: 'SM123456789',
            from: '+15551112222',
            to: '+15551234567',
            direction: 'inbound',
            status: 'received',
            body: 'Hello, do you have appointments available tomorrow?',
            sentiment: 'neutral',
            createdAt: new Date().toISOString(),
            aiHandled: true
          },
          {
            id: 'msg2',
            messageSid: 'SM987654321',
            from: '+15551234567',
            to: '+15551112222',
            direction: 'outbound',
            status: 'delivered',
            body: 'Yes, we have several slots available tomorrow. Would 10am or 2pm work for you?',
            sentiment: 'positive',
            createdAt: new Date(Date.now() - 3600000).toISOString(),
            aiHandled: true
          }
        ],
        total: 2,
        page: 1,
        totalPages: 1
      }
    }).as('getMessages');
    
    cy.intercept('GET', '/api/conversations*', {
      statusCode: 200,
      body: {
        conversations: [
          {
            id: 'conv1',
            phoneNumber: '+15551112222',
            lastMessage: 'Hello, do you have appointments available tomorrow?',
            unreadCount: 1,
            lastUpdated: new Date().toISOString()
          }
        ]
      }
    }).as('getConversations');
    
    cy.intercept('GET', '/api/conversations/conv1/messages', {
      statusCode: 200,
      body: {
        messages: [
          {
            id: 'msg1',
            conversationId: 'conv1',
            body: 'Hello, do you have appointments available tomorrow?',
            direction: 'inbound',
            createdAt: new Date(Date.now() - 7200000).toISOString()
          },
          {
            id: 'msg2',
            conversationId: 'conv1',
            body: 'Yes, we have several slots available tomorrow. Would 10am or 2pm work for you?',
            direction: 'outbound',
            createdAt: new Date(Date.now() - 3600000).toISOString()
          }
        ]
      }
    }).as('getConversationMessages');
    
    cy.intercept('POST', '/api/messages/send', {
      statusCode: 200,
      body: {
        success: true,
        message: {
          id: 'new-msg',
          messageSid: 'SM111222333',
          from: '+15551234567',
          to: '+15551112222',
          direction: 'outbound',
          status: 'queued',
          body: 'We look forward to seeing you!',
          createdAt: new Date().toISOString()
        }
      }
    }).as('sendMessage');
    
    cy.intercept('PATCH', '/api/calls/call1', {
      statusCode: 200,
      body: {
        success: true
      }
    }).as('updateCall');
  });
  
  it('should display call logs', () => {
    // Navigate to call logs
    cy.visit('/dashboard/calls');
    
    // Wait for data to load
    cy.wait('@getCalls');
    
    // Verify page content
    cy.get('h1').should('contain', 'Call Logs');
    
    // Verify call list
    cy.get('[data-testid="call-list"]').should('be.visible');
    cy.get('[data-testid="call-item"]').should('have.length', 2);
    
    // Verify first call details
    cy.get('[data-testid="call-item"]').first().within(() => {
      cy.get('[data-testid="call-direction"]').should('contain', 'Inbound');
      cy.get('[data-testid="call-phone"]').should('contain', '+15551112222');
      cy.get('[data-testid="call-duration"]').should('contain', '2m 5s');
      cy.get('[data-testid="call-ai-handled"]').should('contain', 'AI Handled');
    });
  });
  
  it('should display call details', () => {
    // Navigate to call details
    cy.visit('/dashboard/calls/call1');
    
    // Wait for data to load
    cy.wait('@getCallDetails');
    
    // Verify page content
    cy.get('h1').should('contain', 'Call Details');
    
    // Verify call information
    cy.get('[data-testid="call-from"]').should('contain', '+15551112222');
    cy.get('[data-testid="call-to"]').should('contain', '+15551234567');
    cy.get('[data-testid="call-duration"]').should('contain', '2m 5s');
    cy.get('[data-testid="call-status"]').should('contain', 'Completed');
    
    // Verify transcription
    cy.get('[data-testid="transcription"]').should('contain', 'Hello, I would like to inquire about your services');
    
    // Verify AI response
    cy.get('[data-testid="ai-response"]').should('contain', 'Thank you for your inquiry');
    
    // Verify audio player
    cy.get('[data-testid="audio-player"]').should('be.visible');
  });
  
  it('should allow adding notes to a call', () => {
    // Navigate to call details
    cy.visit('/dashboard/calls/call1');
    
    // Wait for data to load
    cy.wait('@getCallDetails');
    
    // Add a note
    cy.get('[data-testid="notes-input"]').type('Customer was asking about pricing plans.');
    cy.get('[data-testid="save-notes"]').click();
    
    // Wait for update request
    cy.wait('@updateCall');
    
    // Verify success message
    cy.get('[data-testid="success-message"]').should('be.visible');
  });
  
  it('should display SMS conversations', () => {
    // Navigate to messages
    cy.visit('/dashboard/messages');
    
    // Wait for data to load
    cy.wait('@getConversations');
    
    // Verify page content
    cy.get('h1').should('contain', 'Messages');
    
    // Verify conversation list
    cy.get('[data-testid="conversation-list"]').should('be.visible');
    cy.get('[data-testid="conversation-item"]').should('have.length', 1);
    
    // Verify conversation preview
    cy.get('[data-testid="conversation-item"]').first().within(() => {
      cy.get('[data-testid="contact-number"]').should('contain', '+15551112222');
      cy.get('[data-testid="last-message"]').should('contain', 'Hello, do you have appointments');
      cy.get('[data-testid="unread-badge"]').should('contain', '1');
    });
  });
  
  it('should display conversation messages and allow sending replies', () => {
    // Navigate to conversation
    cy.visit('/dashboard/messages/conv1');
    
    // Wait for data to load
    cy.wait('@getConversationMessages');
    
    // Verify page content
    cy.get('h1').should('contain', 'Conversation');
    cy.get('[data-testid="contact-number"]').should('contain', '+15551112222');
    
    // Verify message list
    cy.get('[data-testid="message-list"]').should('be.visible');
    cy.get('[data-testid="message-item"]').should('have.length', 2);
    
    // Verify inbound message
    cy.get('[data-testid="message-item"]').first().within(() => {
      cy.get('[data-testid="message-content"]').should('contain', 'Hello, do you have appointments');
      cy.get('[data-testid="message-direction"]').should('contain', 'inbound');
    });
    
    // Verify outbound message
    cy.get('[data-testid="message-item"]').eq(1).within(() => {
      cy.get('[data-testid="message-content"]').should('contain', 'Yes, we have several slots');
      cy.get('[data-testid="message-direction"]').should('contain', 'outbound');
    });
    
    // Send a reply
    cy.get('[data-testid="message-input"]').type('We look forward to seeing you!');
    cy.get('[data-testid="send-button"]').click();
    
    // Wait for send request
    cy.wait('@sendMessage');
    
    // Verify new message appears
    cy.get('[data-testid="message-item"]').should('have.length', 3);
    cy.get('[data-testid="message-item"]').last().should('contain', 'We look forward to seeing you!');
  });
  
  it('should allow filtering call logs', () => {
    // Navigate to call logs
    cy.visit('/dashboard/calls');
    
    // Wait for data to load
    cy.wait('@getCalls');
    
    // Open filters
    cy.get('[data-testid="filter-button"]').click();
    
    // Set direction filter
    cy.get('[data-testid="direction-filter"]').select('inbound');
    
    // Apply filters
    cy.get('[data-testid="apply-filters"]').click();
    
    // Wait for filtered data
    cy.wait('@getCalls');
    
    // Verify only inbound calls are shown
    cy.get('[data-testid="call-direction"]').each($el => {
      cy.wrap($el).should('contain', 'Inbound');
    });
  });
});
