"use client";

import Link from 'next/link';
import Image from 'next/image';
import { useState, useEffect, useRef } from 'react';
import { useLanguage } from '../i18n/LanguageContext';
import getSupabaseClient from '../utils/supabaseClient';
import { useRouter } from 'next/navigation';

export default function Navbar() {
  const [scrollY, setScrollY] = useState(0);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { isRTL } = useLanguage();
  const router = useRouter();
  const supabaseClientRef = useRef(null);

  // Update scroll position for additional effects
  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Check authentication status
  useEffect(() => {
    const checkAuth = async () => {
      try {
        setIsLoading(true);
        // Try to get the Supabase client
        try {
          supabaseClientRef.current = getSupabaseClient();
        } catch (error) {
          console.error('Error getting Supabase client:', error);
          supabaseClientRef.current = null;
        }

        // Check if we have a valid client
        if (supabaseClientRef.current && supabaseClientRef.current.auth) {
          const { data } = await supabaseClientRef.current.auth.getSession();
          setIsAuthenticated(!!data?.session);
        } else {
          console.error('Supabase client or auth not available');
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();

    // Set up auth state listener with error handling
    let subscription = null;

    try {
      // Only set up listener if we have a valid client
      if (supabaseClientRef.current && supabaseClientRef.current.auth) {
        const authListener = supabaseClientRef.current.auth.onAuthStateChange(
          (event, session) => {
            setIsAuthenticated(!!session);
          }
        );

        if (authListener && authListener.data) {
          subscription = authListener.data.subscription;
        }
      }
    } catch (error) {
      console.error('Error setting up auth listener:', error);
    }

    return () => {
      if (subscription && typeof subscription.unsubscribe === 'function') {
        subscription.unsubscribe();
      }
    };
  }, []);

  // Determine if navbar should be more visible based on scroll
  const isScrolled = scrollY > 50;

  // Improved scroll function with better targeting and error handling
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      // Calculate header height to adjust scroll position
      const navHeight = 80; // Approximate height of fixed navbar
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - navHeight;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    } else {
      console.warn(`Section with ID "${sectionId}" not found`);
    }
    // Close the mobile menu after clicking a link
    setIsMenuOpen(false);
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      // Try to get the Supabase client if we don't have it yet
      if (!supabaseClientRef.current) {
        try {
          supabaseClientRef.current = getSupabaseClient();
        } catch (error) {
          console.error('Error getting Supabase client for sign out:', error);
        }
      }

      // Check if we have a valid client
      if (supabaseClientRef.current && supabaseClientRef.current.auth) {
        await supabaseClientRef.current.auth.signOut();
      }

      // Clear any stored demo user
      if (typeof window !== 'undefined') {
        localStorage.removeItem('callsaver_demo_user');
      }
      router.push('/signin');
    } catch (error) {
      console.error('Error signing out:', error);
      // Still try to redirect even if there's an error
      router.push('/signin');
    }
  };

  return (
    <>
      <nav
        className={`w-auto max-w-[95%] sm:max-w-[90%] md:max-w-4xl mx-auto flex items-center justify-between py-2 px-2 sm:px-3 md:px-4 fixed top-2 sm:top-4 left-0 right-0 z-50 rounded-full ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}
        style={{
          backgroundColor: isScrolled ? 'rgba(13, 13, 23, 0.65)' : 'rgba(13, 13, 23, 0.65)',
          backdropFilter: 'blur(10px)',
          boxShadow: isScrolled
            ? '0 10px 25px rgba(0, 0, 0, 0.15), 0 0 30px rgba(139, 92, 246, 0.15)'
            : '0 8px 20px rgba(0, 0, 0, 0.1), 0 0 20px rgba(139, 92, 246, 0.1)',
          border: '1px solid rgba(255, 255, 255, 0.08)',
        }}
      >
        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
          <Link href="/" className={`flex items-center ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
            <div className={`relative w-7 h-7 md:w-9 md:h-9 ${isRTL ? 'ml-1 md:ml-2' : 'mr-1 md:mr-2'} nav-logo`}>
              <div className="absolute w-7 h-7 md:w-8 md:h-8 bg-purple-600 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 md:h-5 md:w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
            </div>
            <span className="text-base md:text-lg font-bold text-white">CallSaver</span>
          </Link>
        </div>

        {/* Desktop Navigation - Updated with blueprint items */}
        <div className={`hidden md:flex items-center justify-center mx-auto ${isRTL ? 'flex-row-reverse space-x-reverse' : 'flex-row'} space-x-8`}>
          <div>
            <Link
              href="/"
              className="text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900"
              aria-label="Home"
            >
              Home
            </Link>
          </div>
          <div>
            <button
              onClick={() => scrollToSection('features')}
              className="text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900"
              aria-label="Go to Features section"
            >
              Features
            </button>
          </div>
          <div>
            <button
              onClick={() => scrollToSection('pricing')}
              className="text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900"
              aria-label="Go to Pricing section"
            >
              Pricing
            </button>
          </div>
          <div>
            <Link
              href="/support"
              className="text-white text-sm hover:text-purple-300 transition-colors nav-link focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900"
              aria-label="Go to Support page"
            >
              Support
            </Link>
          </div>
        </div>

        <div className={`flex items-center ml-auto ${isRTL ? 'flex-row-reverse space-x-reverse' : 'flex-row'} space-x-2 md:space-x-3`}>
          <div className="block">
            {!isLoading && (
              isAuthenticated ? (
                <div className="flex items-center">
                  <Link href="/dashboard" className="mr-2 sm:mr-3 text-white text-xs md:text-sm font-medium hover:text-purple-200 transition-colors">
                    Dashboard
                  </Link>
                  <button
                    onClick={handleSignOut}
                    className="bg-purple-600 hover:bg-purple-700 text-white text-xs md:text-sm px-3 sm:px-4 md:px-5 py-1.5 sm:py-2 rounded-full font-medium transition-colors shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40"
                  >
                    Sign Out
                  </button>
                </div>
              ) : (
                <>
                  <Link href="/signin" className="mr-2 sm:mr-3 text-white text-xs md:text-sm font-medium hover:text-purple-200 transition-colors">
                    Sign In
                  </Link>
                  <Link href="/signup" className="bg-purple-600 hover:bg-purple-700 text-white text-xs md:text-sm px-3 sm:px-4 md:px-5 py-1.5 sm:py-2 rounded-full font-medium transition-colors shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40">
                    Get Started
                  </Link>
                </>
              )
            )}
          </div>

          {/* Mobile Hamburger Menu Button */}
          <button
            className="md:hidden flex-shrink-0 ml-1 md:ml-3 text-white p-1.5 focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-1 focus:ring-offset-gray-900 rounded-md"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 md:h-6 md:w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              {isMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>
      </nav>

      {/* Mobile Menu - Modified for blueprint items */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-40 md:hidden" onClick={() => setIsMenuOpen(false)}>
          <div className="absolute inset-0 bg-black opacity-50" onClick={() => setIsMenuOpen(false)}></div>
          <div className="absolute top-16 sm:top-20 right-2 sm:right-4 w-[calc(100%-1rem)] max-w-xs p-4 bg-gray-900/95 backdrop-blur-lg border border-purple-500/20 rounded-xl shadow-2xl z-50 flex flex-col space-y-2" onClick={(e) => e.stopPropagation()}>
            <Link
              href="/"
              className="text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center"
              onClick={() => setIsMenuOpen(false)}
              aria-label="Go to Home page"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
              </svg>
              Home
            </Link>
            <button
              onClick={() => scrollToSection('features')}
              className="text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center"
              aria-label="Go to Features section"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
              Features
            </button>
            <button
              onClick={() => scrollToSection('pricing')}
              className="text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center"
              aria-label="Go to Pricing section"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
              Pricing
            </button>
            <Link
              href="/support"
              className="text-white text-left py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center"
              onClick={() => setIsMenuOpen(false)}
              aria-label="Go to Support page"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
              Support
            </Link>
            <hr className="border-gray-700" />
            {!isLoading && (
              isAuthenticated ? (
                <>
                  <Link
                    href="/dashboard"
                    className="text-white py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center"
                    onClick={() => setIsMenuOpen(false)}
                    aria-label="Go to Dashboard"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                    </svg>
                    Dashboard
                  </Link>
                  <button
                    onClick={() => {
                      setIsMenuOpen(false);
                      handleSignOut();
                    }}
                    className="text-white py-2 px-4 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors w-full flex items-center justify-center"
                    aria-label="Sign Out"
                  >
                    Sign Out
                  </button>
                </>
              ) : (
                <>
                  <Link
                    href="/signin"
                    className="text-white py-2 px-4 hover:bg-purple-600/20 rounded-lg transition-colors w-full flex items-center"
                    onClick={() => setIsMenuOpen(false)}
                    aria-label="Go to Sign In page"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2 text-purple-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                    Sign In
                  </Link>
                  <Link
                    href="/signup"
                    className="text-white py-2 px-4 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors w-full flex items-center justify-center"
                    onClick={() => setIsMenuOpen(false)}
                    aria-label="Get Started"
                  >
                    Get Started
                  </Link>
                </>
              )
            )}
          </div>
        </div>
      )}
    </>
  );
}