'use client';

import React, { useState } from 'react';
import { useAuthStore } from '../../stores/authStore';
import PermissionGate from '../PermissionGate';
import { Spinner } from '../ui/Spinner';
import { useRoleManagement } from '../../hooks/useRoleManagement';
import PermissionGroupModal from './PermissionGroupModal';

const PermissionGroupManagement: React.FC = () => {
  const { hasPermission } = useAuthStore();
  const [selectedGroupId, setSelectedGroupId] = useState<string | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Use the role management hook
  const { permissions } = useRoleManagement();

  // Get the selected group
  const selectedGroup = selectedGroupId
    ? permissions.data?.groups.find(group => group.id === selectedGroupId) || null
    : null;

  // Handle group selection
  const handleGroupSelect = (groupId: string) => {
    setSelectedGroupId(groupId);
  };

  if (permissions.isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <PermissionGate permission="permissions:read:any">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold mb-6">Permission Group Management</h2>

        {/* Permission Group Modal */}
        <PermissionGroupModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          group={null}
          allPermissions={permissions.data?.ungrouped || []}
        />

        {/* Edit Permission Group Modal */}
        <PermissionGroupModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          group={selectedGroup}
          allPermissions={permissions.data?.ungrouped || []}
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Group List */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Permission Groups</h3>
              <PermissionGate permission="permissions:create:any">
                <button
                  type="button"
                  className="px-3 py-1.5 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600"
                  onClick={() => setIsCreateModalOpen(true)}
                >
                  New Group
                </button>
              </PermissionGate>
            </div>
            <div className="space-y-2">
              {permissions.data?.groups.map((group) => (
                <div
                  key={group.id}
                  className={`p-3 rounded-md cursor-pointer transition-colors ${
                    selectedGroupId === group.id
                      ? 'bg-blue-100 border-l-4 border-blue-500'
                      : 'bg-white hover:bg-gray-100'
                  }`}
                  onClick={() => handleGroupSelect(group.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="font-medium">{group.name}</div>
                    <div className="text-xs text-gray-500">
                      {group.permissions.length} permissions
                    </div>
                  </div>
                  {group.description && (
                    <div className="text-sm text-gray-500 mt-1">{group.description}</div>
                  )}
                </div>
              ))}

              {permissions.data?.groups.length === 0 && (
                <div className="text-center py-4 text-gray-500">
                  No permission groups found
                </div>
              )}
            </div>
          </div>

          {/* Group Details */}
          <div className="md:col-span-2">
            {selectedGroup ? (
              <div className="bg-white p-6 rounded-lg border">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-xl font-semibold">{selectedGroup.name}</h3>
                  <div className="space-x-2">
                    <PermissionGate permission="permissions:update:any">
                      <button
                        type="button"
                        className="px-3 py-1.5 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600"
                        onClick={() => setIsEditModalOpen(true)}
                      >
                        Edit Group
                      </button>
                    </PermissionGate>
                  </div>
                </div>

                {selectedGroup.description && (
                  <p className="text-gray-500 mb-4">{selectedGroup.description}</p>
                )}

                <div className="mt-4">
                  <h4 className="font-medium mb-2">Permissions in this Group</h4>
                  {selectedGroup.permissions.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {selectedGroup.permissions.map((permission) => (
                        <div
                          key={permission.id}
                          className="p-2 border rounded-md bg-gray-50"
                        >
                          <div className="font-medium">
                            {permission.displayName || permission.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {permission.resource}:{permission.action}
                            {permission.scope !== 'any' ? `:${permission.scope}` : ''}
                          </div>
                          {permission.description && (
                            <div className="text-sm text-gray-500 mt-1">
                              {permission.description}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      No permissions in this group
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex justify-center items-center h-64 bg-gray-50 rounded-lg">
                <p className="text-gray-500">Select a permission group to view details</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </PermissionGate>
  );
};

export default PermissionGroupManagement;
