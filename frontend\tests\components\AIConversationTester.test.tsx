import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AIConversationTester from '../../components/automation/AIConversationTester';
import axios from 'axios';

// Mock dependencies
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock audio playback
window.HTMLMediaElement.prototype.play = jest.fn();
window.HTMLMediaElement.prototype.pause = jest.fn();

// Mock SpeechRecognition API
const mockSpeechRecognition = {
  start: jest.fn(),
  stop: jest.fn(),
  onresult: jest.fn(),
  onerror: jest.fn(),
  onend: jest.fn(),
  continuous: false,
  interimResults: false,
  lang: '',
};

Object.defineProperty(global.window, 'SpeechRecognition', {
  value: jest.fn().mockImplementation(() => mockSpeechRecognition),
  writable: true
});

describe('AIConversationTester', () => {
  let queryClient: QueryClient;
  
  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock successful API response
    mockedAxios.post.mockResolvedValue({
      data: {
        response: "This is a test AI response",
        audioUrl: "https://example.com/test-audio.mp3"
      }
    });
  });
  
  const renderComponent = (props: { numberId: string, mode: 'call' | 'sms' }) => {
    return render(
      <QueryClientProvider client={queryClient}>
        <AIConversationTester {...props} />
      </QueryClientProvider>
    );
  };
  
  test('should render initial state correctly for call mode', () => {
    renderComponent({ numberId: '123', mode: 'call' });
    
    expect(screen.getByText('Call AI Assistant Tester')).toBeInTheDocument();
    expect(screen.getByText('Test your AI\'s responses before deploying')).toBeInTheDocument();
    expect(screen.getByText('Use real AI (costs credits)')).toBeInTheDocument();
    expect(screen.getByText('Voice input')).toBeInTheDocument();
    expect(screen.getByText('Voice output')).toBeInTheDocument();
  });
  
  test('should render initial state correctly for SMS mode', () => {
    renderComponent({ numberId: '123', mode: 'sms' });
    
    expect(screen.getByText('SMS AI Assistant Tester')).toBeInTheDocument();
    expect(screen.getByText('Test your AI\'s responses before deploying')).toBeInTheDocument();
    expect(screen.getByText('Use real AI (costs credits)')).toBeInTheDocument();
    expect(screen.queryByText('Voice input')).not.toBeInTheDocument();
    expect(screen.queryByText('Voice output')).not.toBeInTheDocument();
  });
  
  test('should send message and display AI response', async () => {
    renderComponent({ numberId: '123', mode: 'sms' });
    
    // Type a message
    const inputField = screen.getByPlaceholderText('Type your message...');
    fireEvent.change(inputField, { target: { value: 'Hello AI' } });
    
    // Send the message
    const sendButton = screen.getByTitle('Send message');
    fireEvent.click(sendButton);
    
    // Verify that user message is displayed
    expect(screen.getByText('Hello AI')).toBeInTheDocument();
    expect(screen.getByText('You')).toBeInTheDocument();
    
    // Wait for AI response to be displayed
    await waitFor(() => {
      expect(screen.getByText('This is a test AI response')).toBeInTheDocument();
      expect(screen.getByText('AI Assistant')).toBeInTheDocument();
    });
    
    // Verify API was called with correct parameters
    expect(mockedAxios.post).toHaveBeenCalledWith('/api/automation/sms/test-response', {
      numberId: '123',
      message: 'Hello AI',
      useRealAI: true,
      generateAudio: false
    });
  });
  
  test('should toggle configuration options', () => {
    renderComponent({ numberId: '123', mode: 'call' });
    
    // Get checkboxes
    const useRealAICheckbox = screen.getByLabelText('Use real AI (costs credits)');
    const simulateLatencyCheckbox = screen.getByLabelText('Simulate latency');
    const voiceInputCheckbox = screen.getByLabelText('Voice input');
    const voiceOutputCheckbox = screen.getByLabelText('Voice output');
    
    // Toggle checkboxes
    fireEvent.click(useRealAICheckbox);
    fireEvent.click(simulateLatencyCheckbox);
    fireEvent.click(voiceInputCheckbox);
    fireEvent.click(voiceOutputCheckbox);
    
    // Verify checkboxes toggled correctly
    expect(useRealAICheckbox).not.toBeChecked();
    expect(simulateLatencyCheckbox).not.toBeChecked();
    expect(voiceInputCheckbox).not.toBeChecked();
    expect(voiceOutputCheckbox).not.toBeChecked();
  });
  
  test('should clear conversation when clicking the clear button', async () => {
    renderComponent({ numberId: '123', mode: 'sms' });
    
    // Send a message to populate conversation
    const inputField = screen.getByPlaceholderText('Type your message...');
    fireEvent.change(inputField, { target: { value: 'Hello AI' } });
    
    const sendButton = screen.getByTitle('Send message');
    fireEvent.click(sendButton);
    
    // Wait for AI response
    await waitFor(() => {
      expect(screen.getByText('This is a test AI response')).toBeInTheDocument();
    });
    
    // Click clear button
    const clearButton = screen.getByTitle('Clear conversation');
    fireEvent.click(clearButton);
    
    // Verify conversation was cleared
    expect(screen.queryByText('Hello AI')).not.toBeInTheDocument();
    expect(screen.queryByText('This is a test AI response')).not.toBeInTheDocument();
    
    // Verify that we're back to initial empty state
    expect(screen.getByText('Test how your AI assistant will respond to SMS messages.')).toBeInTheDocument();
    expect(screen.getByText('Start a conversation to see your AI in action.')).toBeInTheDocument();
  });
  
  test('should handle API errors gracefully', async () => {
    // Override the axios mock to simulate an error
    mockedAxios.post.mockRejectedValueOnce(new Error('API Error'));
    
    renderComponent({ numberId: '123', mode: 'sms' });
    
    // Send a message
    const inputField = screen.getByPlaceholderText('Type your message...');
    fireEvent.change(inputField, { target: { value: 'Hello AI' } });
    
    const sendButton = screen.getByTitle('Send message');
    fireEvent.click(sendButton);
    
    // Verify error message is displayed
    await waitFor(() => {
      expect(screen.getByText("I'm sorry, but I encountered an error while processing your request. Please try again.")).toBeInTheDocument();
    });
  });
});
