'use client';

import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { OwnedNumber } from '../../hooks/useNumberManagement';

interface ReleaseConfirmationDialogProps {
  numberData: OwnedNumber | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isReleasing: boolean;
}

export default function ReleaseConfirmationDialog({ 
  numberData, 
  isOpen, 
  onClose, 
  onConfirm, 
  isReleasing 
}: ReleaseConfirmationDialogProps) {
  // Format phone number for display
  const formatPhoneNumber = (phone: string | undefined) => {
    if (!phone) return '';
    
    // This is a simple formatter, you might want to use a library like libphonenumber-js
    // for more sophisticated formatting based on country codes
    if (phone.startsWith('+1')) {
      // US format: +1 (XXX) XXX-XXXX
      const cleaned = phone.replace(/\D/g, '').substring(1); // Remove non-digits and the +1
      if (cleaned.length === 10) {
        return `+1 (${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6, 10)}`;
      }
    }
    return phone; // Return as-is if not US or not 10 digits
  };

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 dark:bg-red-900 sm:mx-0 sm:h-10 sm:w-10">
                    <ExclamationTriangleIcon className="h-6 w-6 text-red-600 dark:text-red-400" aria-hidden="true" />
                  </div>
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                    <Dialog.Title as="h3" className="text-lg font-semibold leading-6 text-gray-900 dark:text-white">
                      Release Phone Number
                    </Dialog.Title>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Are you sure you want to release this phone number? This action cannot be undone and you may lose this number permanently.
                      </p>
                    </div>
                    
                    {numberData && (
                      <div className="mt-4 bg-gray-50 dark:bg-gray-750 p-4 rounded-md">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {numberData.friendlyName || 'Unnamed Number'}
                        </p>
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          {formatPhoneNumber(numberData.phoneNumber)}
                        </p>
                      </div>
                    )}
                    
                    <div className="mt-4">
                      <p className="text-sm font-medium text-red-600 dark:text-red-400">
                        Warning:
                      </p>
                      <ul className="mt-2 text-sm text-gray-500 dark:text-gray-400 list-disc pl-5 space-y-1">
                        <li>All automations and configurations for this number will be deleted.</li>
                        <li>Any services or applications using this number will stop working.</li>
                        <li>This number may be assigned to someone else after release.</li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={onConfirm}
                    disabled={isReleasing}
                  >
                    {isReleasing ? 'Releasing...' : 'Release Number'}
                  </button>
                  <button
                    type="button"
                    className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-650 sm:mt-0 sm:w-auto"
                    onClick={onClose}
                  >
                    Cancel
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
