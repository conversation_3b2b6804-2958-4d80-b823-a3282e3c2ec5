/**
 * QR Code Generator Component
 * 
 * This component displays a QR code for eSIM activation and provides
 * instructions for installation on various devices.
 */

import React, { useState } from 'react';
import Image from 'next/image';
import { Card, <PERSON>ton, Spinner, Alert, Modal, Tabs, TabPanel } from '../../components/ui';

export function QRCodeGenerator({ profile, onGenerateQR, onClose }) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const handleGenerateQR = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Call the provided handler function
      await onGenerateQR();
    } catch (err) {
      setError(err.message || 'Failed to generate QR code');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <Modal title="eSIM Activation QR Code" onClose={onClose} size="lg">
      <div className="p-4">
        {error && (
          <Alert type="error" className="mb-4">
            {error}
          </Alert>
        )}
        
        {!profile.qrCode ? (
          <div className="text-center p-8">
            {loading ? (
              <div className="my-8">
                <Spinner size="lg" />
                <p className="mt-4 text-gray-600">Generating QR code...</p>
              </div>
            ) : (
              <>
                <p className="mb-4 text-gray-600">
                  Generate a QR code to activate this eSIM profile on your device.
                </p>
                <Button onClick={handleGenerateQR}>Generate QR Code</Button>
              </>
            )}
          </div>
        ) : (
          <div>
            <div className="flex flex-col items-center mb-6">
              <div className="relative w-64 h-64 mb-4 border rounded-lg overflow-hidden">
                {/* Display QR code - if it's a data URL, use Image component, otherwise use img */}
                {profile.qrCode.qrCodeData.startsWith('data:') ? (
                  <Image
                    src={profile.qrCode.qrCodeData}
                    alt="eSIM Activation QR Code"
                    layout="fill"
                    objectFit="contain"
                  />
                ) : (
                  <div className="p-4 flex items-center justify-center h-full">
                    <p className="text-center text-sm break-all bg-gray-100 p-3 rounded">
                      {profile.qrCode.activationCode || profile.qrCode.qrCodeData}
                    </p>
                  </div>
                )}
              </div>
              
              <div className="text-center">
                <p className="font-medium mb-1">Scan this QR code to activate your eSIM</p>
                <p className="text-sm text-gray-600">
                  QR code expires {profile.qrCode.expiresAt ? new Date(profile.qrCode.expiresAt).toLocaleString() : 'soon'}
                </p>
              </div>
            </div>
            
            <Tabs>
              <TabPanel title="iPhone" iconName="apple">
                <ol className="list-decimal pl-5 space-y-2">
                  <li>Open <strong>Settings</strong> on your iPhone</li>
                  <li>Tap <strong>Mobile Data</strong></li>
                  <li>Tap <strong>Add eSIM</strong></li>
                  <li>Select <strong>Use QR Code</strong></li>
                  <li>Scan the QR code shown above</li>
                  <li>Follow the on-screen instructions to complete activation</li>
                </ol>
              </TabPanel>
              
              <TabPanel title="Android" iconName="android">
                <ol className="list-decimal pl-5 space-y-2">
                  <li>Open <strong>Settings</strong> on your Android device</li>
                  <li>Tap <strong>Network & internet</strong></li>
                  <li>Tap <strong>Mobile network</strong> or <strong>SIMs</strong></li>
                  <li>Tap <strong>+ Add mobile plan</strong> or <strong>+ Add SIM</strong></li>
                  <li>Select <strong>Scan carrier QR code</strong></li>
                  <li>Scan the QR code shown above</li>
                  <li>Follow the on-screen instructions to complete activation</li>
                </ol>
                <p className="text-sm text-gray-600 mt-3">
                  Note: Steps may vary depending on your device manufacturer and Android version.
                </p>
              </TabPanel>
              
              <TabPanel title="Manual" iconName="document-text">
                <p className="mb-3">If scanning the QR code doesn't work, you can manually enter the activation code:</p>
                <div className="bg-gray-100 p-3 rounded-md mb-4 break-all font-mono text-sm">
                  {profile.qrCode.activationCode || 'Activation code not available'}
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-medium">On iPhone:</h4>
                  <ol className="list-decimal pl-5 space-y-1">
                    <li>Go to <strong>Settings {'->'} Mobile Data {'->'} Add eSIM</strong></li>
                    <li>Select <strong>Enter Details Manually</strong></li>
                    <li>Enter the activation code exactly as shown above</li>
                  </ol>
                  
                  <h4 className="font-medium mt-4">On Android:</h4>
                  <ol className="list-decimal pl-5 space-y-1">
                    <li>Go to <strong>Settings {'->'} Network & internet {'->'} Mobile network</strong></li>
                    <li>Tap <strong>+ Add mobile plan</strong> or <strong>+ Add SIM</strong></li>
                    <li>Select <strong>Enter code instead</strong> or similar option</li>
                    <li>Enter the activation code exactly as shown above</li>
                  </ol>
                </div>
              </TabPanel>
            </Tabs>
            
            <div className="mt-6 pt-4 border-t">
              <div className="flex justify-between">
                <Button variant="outline" onClick={handleGenerateQR}>
                  Refresh QR Code
                </Button>
                <Button onClick={onClose}>Close</Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}
