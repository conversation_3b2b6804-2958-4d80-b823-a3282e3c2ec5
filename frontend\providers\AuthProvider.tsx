'use client';

import { ReactNode, useEffect } from 'react';
import { useAuthStore } from '../stores/authStore';
import { useRouter, usePathname } from 'next/navigation';

interface AuthProviderProps {
  children: ReactNode;
}

// List of paths that don't require authentication
const PUBLIC_PATHS = ['/login', '/register', '/reset-password', '/'];

export function AuthProvider({ children }: AuthProviderProps) {
  const { isAuthenticated, isLoading, checkAuth } = useAuthStore();
  const router = useRouter();
  const pathname = usePathname() || '/';

  // Check authentication status on initial load
  useEffect(() => {
    const verifySession = async () => {
      await checkAuth();
    };

    verifySession();
  }, [checkAuth]);

  // Handle routing based on auth status
  useEffect(() => {
    // Skip during loading
    if (isLoading) return;

    const isPublicPath = PUBLIC_PATHS.some(path => 
      pathname === path || pathname.startsWith(`${path}/`)
    );

    // If user is not authenticated and trying to access a protected route
    if (!isAuthenticated && !isPublicPath) {
      // Redirect to login page with a return URL
      router.push(`/login?returnUrl=${encodeURIComponent(pathname)}`);
    }
    
    // If user is authenticated and trying to access a login/register page
    if (isAuthenticated && (pathname === '/login' || pathname === '/register')) {
      // Redirect to dashboard
      router.push('/dashboard');
    }
  }, [isAuthenticated, isLoading, pathname, router]);

  // Show nothing during the initial auth check to prevent flashing content
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return <>{children}</>;
}
