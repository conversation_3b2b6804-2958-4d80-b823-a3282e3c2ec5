'use client';

import { PhoneIcon } from '@heroicons/react/24/outline';
import AvailableNumberItem from './AvailableNumberItem';
import EmptyState from '../shared/EmptyState';
import ErrorMessage from '../shared/ErrorMessage';
import { AvailableNumber } from '../../hooks/useNumberManagement';

interface AvailableNumbersListProps {
  numbers: AvailableNumber[];
  isLoading: boolean;
  error: Error | null;
  onPurchaseClick: (number: AvailableNumber) => void;
}

export default function AvailableNumbersList({
  numbers,
  isLoading,
  error,
  onPurchaseClick
}: AvailableNumbersListProps) {
  // Handle error state
  if (error) {
    return (
      <ErrorMessage
        title="Could not load available numbers"
        message="We couldn't load available numbers. Please try again with different search criteria."
        error={error}
      />
    );
  }

  // Handle empty state
  if (!isLoading && numbers.length === 0) {
    return (
      <EmptyState
        title="No numbers found"
        message="We couldn't find any numbers matching your search criteria. Try adjusting your search parameters."
        icon={PhoneIcon}
      />
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
      {numbers.map((number) => (
        <AvailableNumberItem
          key={number.id}
          numberData={number}
          onPurchaseClick={() => onPurchaseClick(number)}
        />
      ))}
    </div>
  );
}
