---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: true
---
---

## prompt_to_mdc_router.mdc

### Section: Backend API Tasks

**Step 1: API Gateway Init**  
Required:  
- docs/functional_specs/api_gateway_routes.mdc  
- docs/functional_specs/cursor_project_rules.mdc  
- docs/functional_specs/env_configuration_rules.mdc  

**Step 2: API Key Management & Dashboard**  
Required:  
- docs/functional_specs/api_gateway_routes.mdc  
- docs/functional_specs/user_roles_and_permissions.mdc  
- docs/functional_specs/credit_and_billing_logic.mdc  
- docs/functional_specs/notifications_and_alerts_document.mdc  

**Step 3: User Authentication & Authorization**
Required:
- docs/functional_specs/user_roles_and_permissions.mdc
- docs/functional_specs/api_gateway_routes.mdc
- back/backend/prisma/schema.prisma

**Step 4: Stripe Webhook Integration**
Required:
- docs/functional_specs/credit_and_billing_logic.mdc
- docs/functional_specs/api_gateway_routes.mdc
- back/backend/controllers/webhooks/stripeWebhookController.js
- back/backend/middleware/stripeWebhookAuthMiddleware.js

**Step 5: Twilio Webhook Integration (Voice, SMS, Status)**
Required:
- docs/functional_specs/api_gateway_routes.mdc
- back/backend/controllers/webhooks/voiceWebhookController.js
- back/backend/controllers/webhooks/smsWebhookController.js
- back/backend/controllers/webhooks/callStatusWebhookController.js
- back/backend/controllers/webhooks/gatherWebhookController.js
- back/backend/controllers/webhooks/transcriptionWebhookController.js

**Step 6: Database Schema Modifications**
Required:
- back/backend/prisma/schema.prisma
- docs/functional_specs/database_schema_design.mdc (if exists, otherwise infer from prisma)

**Step 7: Caching Strategy Implementation**
Required:
- back/backend/lib/cacheService.js
- docs/functional_specs/caching_strategy.mdc (if exists)

**Step 8: AI Integration Layer**
Required:
- docs/functional_specs/ai_integration_layer.mdc
- docs/functional_specs/ai_execution_log.mdc
- docs/functional_specs/task_queue.mdc
- docs/functional_specs/ai_response_signature_logging.mdc
- docs/functional_specs/ai_task_processing_and_escalation.mdc
- docs/ai/ai_prompt_training_strategy.mdc # <-- Added

---

### Section: Reliability & Scalability

**Service Failover and Redundancy**
Required:
- docs/architecture/service_failover_and_redundancy.mdc
- docs/functional_specs/api_gateway_routes.mdc # Identifies service dependencies
- docs/functional_specs/ai_integration_layer.mdc # AI provider specifics
- docs/functional_specs/esim_transition_plan.mdc # eSIM specifics
- docs/functional_specs/task_queue.mdc # Queue resilience

**Multi-Tenant Data Isolation**
Required:
- docs/architecture/multi_tenant_data_isolation.mdc
- back/backend/prisma/schema.prisma
- Relevant backend middleware/service logic applying filters

**Task Queue Architecture**
Required:
- docs/architecture/task_queue_architecture.mdc
- docs/functional_specs/ai_task_processing_and_escalation.mdc # Consumer details
- docs/platform_security/webhook_reliability_and_idempotency.mdc # Consumer details
- back/backend/lib/taskQueue/config.js # Queue configuration
- back/backend/lib/taskQueue/queueService.js # Queue service implementation

**Redis Infrastructure**
Required:
- docs/architecture/redis_implementation_guide.mdc # Redis implementation guide
- docs/architecture/redis_production_configuration.mdc # Redis production configuration
- docs/architecture/redis_cluster_implementation.mdc # Redis cluster implementation
- docs/dev_guides/redis_setup_guide.md # Redis setup guide for developers
- back/backend/lib/redis.js # Centralized Redis client
- back/backend/lib/circuitBreaker.js # Circuit breaker pattern
- back/backend/lib/monitoring/redisHealthCheck.js # Redis health monitoring
- back/backend/scripts/setup-redis-dev.js # Redis development setup script
- back/backend/test-redis-connection.js # Redis connection test script

---

### Section: Integrations

**Calendar Synchronization**
Required:
- docs/integrations/calendar_sync_rules.mdc
- docs/functional_specs/appointment_scheduler_document.mdc
- Relevant backend service/controller for integration logic

**Voice Transcription**
Required:
- docs/implementation_plans/voice_transcription_implementation.mdc
- Relevant backend service/controller for transcription logic

---

### Section: Analytics & Reporting

**Advanced Analytics Aggregation Strategy**
Required:
- docs/architecture/advanced_analytics_aggregation_strategy.mdc
- docs/functional_specs/analytics_section_document.mdc # Defines basic metrics
- back/backend/prisma/schema.prisma # Source data schema
- docs/compliance/gdpr_ccpa_compliance_tracking.mdc # Privacy considerations

---

### Section: Frontend UI Tasks

**Dashboard UI**  
Required:  
- docs/implementation_plans/dashboard_ui_implementation.mdc  
- docs/functional_specs/component_state_mapping.mdc  
- docs/implementation_plans/global_state_and_api_integration.mdc  
- front/mainpage/hooks/useDashboard.ts
- front/mainpage/app/(dashboard)/page.tsx (or relevant dashboard page component)

**User Settings & Profile UI**
Required:
- docs/implementation_plans/user_settings_profile_ui_implementation.mdc
- Relevant frontend components for user settings/profile

**Number Management UI**  
Required:  
- docs/implementation_plans/number_management_ui_implementation.mdc  
- docs/functional_specs/api_gateway_routes.mdc  
- docs/functional_specs/component_state_mapping.mdc  
- front/mainpage/hooks/useNumbers.ts
- front/mainpage/app/(dashboard)/numbers/page.tsx (or relevant numbers page component)

**Automation UI**
Required:
- docs/implementation_plans/automation_ui_implementation.mdc
- docs/functional_specs/component_state_mapping.mdc
- docs/implementation_plans/global_state_and_api_integration.mdc
- front/mainpage/hooks/useAutomation.ts
- front/mainpage/app/(dashboard)/automation/page.tsx (or relevant automation page component)

**Global State Management**
Required:
- docs/implementation_plans/global_state_and_api_integration.mdc
- front/mainpage/stores/authStore.ts
- front/mainpage/stores/uiStore.ts
- front/mainpage/providers/AppProviders.tsx

**API Client Integration**
Required:
- front/mainpage/lib/apiClient.ts
- docs/functional_specs/api_gateway_routes.mdc

**Authentication Flow (Frontend)**
Required:
- front/mainpage/stores/authStore.ts
- front/mainpage/providers/AuthProvider.tsx
- front/mainpage/app/login/page.tsx (or relevant login/auth components)

---

### Section: Platform UX

**Offline Mode Behavior**
Required:
- docs/platform_ux/offline_mode_behavior.mdc
- docs/functional_specs/component_state_mapping.mdc
- front/mainpage/stores/ (relevant stores, e.g., numberStore, automationStore)
- front/mainpage/lib/apiClient.ts # For detecting network status

**Frontend Performance Budget**
Required:
- docs/platform_ux/performance_budget.mdc
- docs/dev_guides/testing_strategy.mdc # Performance testing section
- Frontend monitoring tools/configs

**Mobile Responsive Design**
Required:
- docs/implementation_plans/mobile_responsive_design_implementation.mdc
- Relevant frontend layout and style components

---

### Section: Cross-Cutting Concerns

**Environment Configuration**
Required:
- docs/functional_specs/env_configuration_rules.mdc
- .env.example (or relevant env files)

**Feature Flag Strategy**
Required:
  - docs/functional_specs/feature_flag_strategy.mdc
  - docs/functional_specs/deployment_strategy.mdc (if exists)

**Testing (Backend)**
Required:
- back/backend/tests/ (relevant test files)
- back/backend/jest.config.js

**Testing (Frontend)**
Required:
- front/mainpage/tests/ (relevant test files)
- front/mainpage/jest.config.js (or relevant test config)

**Deployment & CI/CD**
Required:
- docs/functional_specs/deployment_strategy.mdc (if exists)
- Relevant CI/CD configuration files (e.g., GitHub Actions workflows)

---

### Section: Developer Tooling & Guides

**Internal CLI Tooling Strategy**
Required:
- docs/dev_guides/internal_cli_tooling_strategy.mdc
- docs/functional_specs/user_roles_and_permissions.mdc # For access control
- docs/functional_specs/env_configuration_rules.mdc # For environment targeting

**Developer SDK Guidelines**
Required:
- docs/dev_guides/developer_sdk_guidelines.mdc
- docs/functional_specs/api_gateway_routes.mdc # Core API definition
- docs/functional_specs/session_management_strategy.mdc # Auth handling
- docs/functional_specs/user_roles_and_permissions.mdc # API key scope

**Overall Testing Strategy**
Required:
- docs/dev_guides/testing_strategy.mdc
- Relevant test configuration files (jest.config.js, etc.)
- CI/CD pipeline configuration

---

### Section: Security & Compliance

**Automated Abuse Prevention**
Required:
  - docs/functional_specs/automated_abuse_prevention.mdc
  - docs/functional_specs/api_gateway_routes.mdc
  - docs/functional_specs/credit_and_billing_logic.mdc
  - docs/functional_specs/ai_response_signature_logging.mdc
  - docs/functional_specs/cursor_project_rules.mdc

**GDPR/CCPA Compliance & DSR Handling**
Required:
  - docs/compliance/gdpr_ccpa_compliance_tracking.mdc
  - docs/functional_specs/user_roles_and_permissions.mdc # For account settings access
  - back/backend/prisma/schema.prisma # For data mapping reference
  - docs/functional_specs/cursor_project_rules.mdc # Data Protection rules

**Session Management & Token Refresh**
Required:
- docs/functional_specs/session_management_strategy.mdc
- docs/functional_specs/api_gateway_routes.mdc
- front/mainpage/stores/authStore.ts
- back/backend/middleware/authMiddleware.js (or equivalent)

**Webhook Security & Reliability**
Required:
- docs/platform_security/webhook_reliability_and_idempotency.mdc
- docs/functional_specs/task_queue.mdc
- back/backend/middleware/webhookSecurity.js # Webhook security middleware
- back/backend/lib/serviceFailover/healthChecks.js # Service health checks
- Relevant webhook controller implementations

---

Rules:
1. Only load the listed `.mdc` files per task category/step.
2. Use this file as a primary filter to prevent unnecessary context loading and token waste.
3. Any AI assistant (e.g., Claude, Gemini) must consult this document before loading `.mdc` or source code files for a given task. If a file is not listed for the task, it should not be loaded unless explicitly requested or deemed absolutely essential after careful consideration.
4. Paths are relative to the project root (`c:/Users/<USER>/Documents/callsaver.app`).
5. This document should be updated as the project structure evolves.
