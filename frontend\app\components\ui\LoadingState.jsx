"use client";

import React from 'react';

export default function LoadingState({ size = 'medium', fullPage = false, text = 'Loading...' }) {
  const sizeClasses = {
    small: 'h-4 w-4 border-2',
    medium: 'h-8 w-8 border-2',
    large: 'h-12 w-12 border-b-2 border-t-2'
  };
  
  const spinner = (
    <div className={`animate-spin rounded-full ${sizeClasses[size]} border-purple-500`}></div>
  );
  
  if (fullPage) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-950">
        <div className="flex flex-col items-center">
          {spinner}
          <p className="mt-4 text-lg text-gray-400">{text}</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex items-center justify-center py-8">
      <div className="flex flex-col items-center">
        {spinner}
        {text && <p className="mt-2 text-sm text-gray-400">{text}</p>}
      </div>
    </div>
  );
} 