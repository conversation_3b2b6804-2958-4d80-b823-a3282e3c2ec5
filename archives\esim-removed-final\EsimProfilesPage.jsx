/**
 * eSIM Profiles Page Component
 * 
 * This component provides a user interface for managing eSIM profiles,
 * including viewing, creating, and managing data packages.
 */

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useToast } from '../../hooks/useToast';
import { useAuth } from '../../hooks/useAuth';
import { Card, Badge, Button, Spinner, Alert } from '../../components/ui';
import { formatBytes, formatDate } from '../../utils/formatting';
import { QRCodeGenerator } from './QRCodeGenerator';
import { PackageSelector } from './PackageSelector';
import { DataUsageChart } from './DataUsageChart';

const STATUS_COLORS = {
  provisioned: 'blue',
  activated: 'green',
  suspended: 'amber',
  deactivated: 'gray'
};

export default function EsimProfilesPage() {
  const [profiles, setProfiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [detailView, setDetailView] = useState(null); // 'qr', 'usage', 'packages', null
  
  const router = useRouter();
  const toast = useToast();
  const { user, apiClient } = useAuth();
  
  // Fetch profiles on component mount
  useEffect(() => {
    fetchProfiles();
  }, []);
  
  // Fetch all profiles for the current user
  const fetchProfiles = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClient.get('/api/esim/profiles');
      
      if (response.data.success) {
        setProfiles(response.data.data);
      } else {
        setError(response.data.message || 'Failed to load eSIM profiles');
      }
    } catch (err) {
      setError(err.message || 'An error occurred while loading profiles');
      toast.error('Failed to load eSIM profiles');
    } finally {
      setLoading(false);
    }
  };
  
  // Create a new eSIM profile
  const createProfile = async (profileData) => {
    try {
      setLoading(true);
      
      const response = await apiClient.post('/api/esim/profiles', profileData);
      
      if (response.data.success) {
        toast.success('eSIM profile created successfully');
        fetchProfiles();
        setShowCreateModal(false);
      } else {
        toast.error(response.data.message || 'Failed to create profile');
      }
    } catch (err) {
      toast.error(err.message || 'An error occurred while creating profile');
    } finally {
      setLoading(false);
    }
  };
  
  // Deactivate an eSIM profile
  const deactivateProfile = async (profileId) => {
    if (!confirm('Are you sure you want to deactivate this eSIM profile? This action cannot be undone.')) {
      return;
    }
    
    try {
      setLoading(true);
      
      const response = await apiClient.delete(`/api/esim/profiles/${profileId}`);
      
      if (response.data.success) {
        toast.success('eSIM profile deactivated successfully');
        fetchProfiles();
      } else {
        toast.error(response.data.message || 'Failed to deactivate profile');
      }
    } catch (err) {
      toast.error(err.message || 'An error occurred while deactivating profile');
    } finally {
      setLoading(false);
    }
  };
  
  // Generate QR code for profile activation
  const generateQR = async (profileId) => {
    try {
      setLoading(true);
      
      const response = await apiClient.get(`/api/esim/profiles/${profileId}/qr`);
      
      if (response.data.success) {
        setSelectedProfile({
          ...selectedProfile,
          qrCode: response.data.data
        });
        setDetailView('qr');
      } else {
        toast.error(response.data.message || 'Failed to generate QR code');
      }
    } catch (err) {
      toast.error(err.message || 'An error occurred while generating QR code');
    } finally {
      setLoading(false);
    }
  };
  
  // Purchase a data package for a profile
  const purchasePackage = async (profileId, packageId) => {
    try {
      setLoading(true);
      
      const response = await apiClient.post(`/api/esim/profiles/${profileId}/packages`, {
        packageId
      });
      
      if (response.data.success) {
        toast.success('Data package purchased successfully');
        fetchProfiles();
        setDetailView(null);
      } else {
        toast.error(response.data.message || 'Failed to purchase package');
      }
    } catch (err) {
      toast.error(err.message || 'An error occurred while purchasing package');
    } finally {
      setLoading(false);
    }
  };
  
  // View profile details
  const viewProfile = (profile, view = null) => {
    setSelectedProfile(profile);
    setDetailView(view);
  };
  
  // Render the profile list
  const renderProfileList = () => {
    if (loading && !profiles.length) {
      return <Spinner size="lg" className="mx-auto my-8" />;
    }
    
    if (error) {
      return (
        <Alert type="error" className="my-4">
          {error}
        </Alert>
      );
    }
    
    if (!profiles.length) {
      return (
        <div className="text-center my-8 p-6 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-medium mb-2">No eSIM Profiles Found</h3>
          <p className="text-gray-600 mb-4">You haven't created any eSIM profiles yet.</p>
          <Button onClick={() => setShowCreateModal(true)}>Create eSIM Profile</Button>
        </div>
      );
    }
    
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {profiles.map(profile => (
          <Card key={profile.id} className="p-4">
            <div className="flex justify-between items-start mb-2">
              <div>
                <h3 className="text-lg font-medium">{profile.assignedNumber || 'eSIM Profile'}</h3>
                <p className="text-sm text-gray-600">{profile.packageName || 'No package'}</p>
              </div>
              <Badge color={STATUS_COLORS[profile.status] || 'gray'}>
                {profile.status}
              </Badge>
            </div>
            
            <div className="my-3">
              <div className="text-sm grid grid-cols-2 gap-x-2 gap-y-1">
                <span className="text-gray-500">Data:</span>
                <span>{profile.dataLimit === -1 
                  ? 'Unlimited' 
                  : `${formatBytes(profile.dataUsed)} / ${formatBytes(profile.dataLimit * 1024 * 1024)}`}
                </span>
                
                <span className="text-gray-500">Activated:</span>
                <span>{profile.activatedAt ? formatDate(profile.activatedAt) : 'Not activated'}</span>
                
                <span className="text-gray-500">Expires:</span>
                <span>{profile.expiresAt ? formatDate(profile.expiresAt) : 'N/A'}</span>
                
                <span className="text-gray-500">Provider:</span>
                <span>{profile.provider}</span>
              </div>
            </div>
            
            <div className="flex flex-wrap gap-2 mt-4">
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => viewProfile(profile, 'qr')}
              >
                Activation QR
              </Button>
              
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => viewProfile(profile, 'usage')}
              >
                Data Usage
              </Button>
              
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => viewProfile(profile, 'packages')}
              >
                Buy Data
              </Button>
              
              <Button 
                size="sm" 
                variant="danger" 
                onClick={() => deactivateProfile(profile.id)}
              >
                Deactivate
              </Button>
            </div>
          </Card>
        ))}
      </div>
    );
  };
  
  // Render a profile detail view
  const renderDetailView = () => {
    if (!selectedProfile) return null;
    
    switch (detailView) {
      case 'qr':
        return <QRCodeGenerator 
          profile={selectedProfile} 
          onGenerateQR={() => generateQR(selectedProfile.id)} 
          onClose={() => setDetailView(null)} 
        />;
        
      case 'usage':
        return <DataUsageChart 
          profile={selectedProfile} 
          onClose={() => setDetailView(null)} 
        />;
        
      case 'packages':
        return <PackageSelector 
          profile={selectedProfile} 
          onPurchase={(packageId) => purchasePackage(selectedProfile.id, packageId)} 
          onClose={() => setDetailView(null)} 
        />;
        
      default:
        return null;
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">eSIM Profiles</h1>
        <Button onClick={() => setShowCreateModal(true)}>Create New Profile</Button>
      </div>
      
      {renderProfileList()}
      
      {/* Detail View Modal */}
      {detailView && renderDetailView()}
      
      {/* Create Profile Modal */}
      {showCreateModal && (
        <PackageSelector 
          onPurchase={(packageId) => createProfile({ packageId })} 
          onClose={() => setShowCreateModal(false)}
          isNewProfile={true} 
        />
      )}
    </div>
  );
}
