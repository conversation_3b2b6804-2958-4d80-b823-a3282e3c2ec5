"use client";

import { useState } from 'react';
import { 
  ArrowPathIcon, 
  CheckCircleIcon,
  XCircleIcon,
  PhoneArrowUpRightIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

export default function ForwardingSettings() {
  const [isEnabled, setIsEnabled] = useState(false);
  const [forwardTo, setForwardTo] = useState('');
  const [ringTime, setRingTime] = useState(15);
  const [voicemailEnabled, setVoicemailEnabled] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState(null);

  // Format phone number input as user types
  const formatPhoneNumber = (input) => {
    // Remove all non-digit characters
    const cleaned = input.replace(/\D/g, '');
    
    // Format based on length
    if (cleaned.length <= 3) {
      return cleaned;
    } else if (cleaned.length <= 6) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
    } else {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
    }
  };

  // Handle phone number input change
  const handlePhoneChange = (e) => {
    const formattedNumber = formatPhoneNumber(e.target.value);
    setForwardTo(formattedNumber);
  };

  const handleSave = async () => {
    // Validate phone number
    const cleanedNumber = forwardTo.replace(/\D/g, '');
    if (isEnabled && cleanedNumber.length < 10) {
      setSaveError('Please enter a valid phone number');
      return;
    }

    setIsSaving(true);
    setSaveSuccess(false);
    setSaveError(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate successful save
      setSaveSuccess(true);
      
      // Reset success message after 3 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      setSaveError('Failed to save forwarding settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-white flex items-center">
          <PhoneArrowUpRightIcon className="h-5 w-5 mr-2 text-purple-400" />
          Call Forwarding
        </h3>
        <div className="flex items-center">
          <span className="text-sm text-gray-400 mr-2">Forwarding</span>
          <button 
            onClick={() => setIsEnabled(!isEnabled)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 ${
              isEnabled ? 'bg-purple-600' : 'bg-gray-700'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                isEnabled ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>

      {saveSuccess && (
        <div className="mb-4 p-3 bg-green-500/20 border border-green-500/30 rounded-lg">
          <p className="text-green-300 text-sm flex items-center">
            <CheckCircleIcon className="h-4 w-4 mr-2" />
            Forwarding settings saved successfully
          </p>
        </div>
      )}

      {saveError && (
        <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
          <p className="text-red-300 text-sm flex items-center">
            <XCircleIcon className="h-4 w-4 mr-2" />
            {saveError}
          </p>
        </div>
      )}

      <div className="space-y-4">
        <div>
          <label htmlFor="forwardTo" className="block text-sm font-medium text-gray-400 mb-1">
            Forward Calls To
          </label>
          <input
            type="tel"
            id="forwardTo"
            value={forwardTo}
            onChange={handlePhoneChange}
            placeholder="Enter phone number"
            className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
            disabled={!isEnabled}
          />
          <p className="text-xs text-gray-500 mt-1">
            All calls will be forwarded to this number
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">
            Ring Time (seconds)
          </label>
          <div className="flex items-center">
            <input
              type="range"
              min="5"
              max="60"
              step="5"
              value={ringTime}
              onChange={(e) => setRingTime(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              disabled={!isEnabled}
            />
            <span className="ml-2 text-white font-medium w-10 text-center">
              {ringTime}s
            </span>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            How long to ring before going to voicemail
          </p>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-400">
              Enable Voicemail
            </label>
            <p className="text-xs text-gray-500 mt-1">
              Record voicemail if call is not answered
            </p>
          </div>
          <button 
            onClick={() => setVoicemailEnabled(!voicemailEnabled)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 ${
              voicemailEnabled ? 'bg-purple-600' : 'bg-gray-700'
            }`}
            disabled={!isEnabled}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                voicemailEnabled ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        <div className="p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
          <div className="flex items-start">
            <InformationCircleIcon className="h-5 w-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-blue-300">
              Call forwarding allows you to redirect incoming calls to another phone number. When enabled, all calls to your CallSaver number will be forwarded to the specified number.
            </p>
          </div>
        </div>

        <button
          onClick={handleSave}
          disabled={isSaving}
          className={`w-full py-2 ${
            isSaving 
              ? 'bg-purple-600/50 cursor-wait' 
              : 'bg-purple-600 hover:bg-purple-700 cursor-pointer'
          } text-white font-medium rounded-lg transition-colors flex items-center justify-center`}
        >
          {isSaving ? (
            <>
              <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Saving...
            </>
          ) : (
            'Save Settings'
          )}
        </button>
      </div>
    </div>
  );
}
