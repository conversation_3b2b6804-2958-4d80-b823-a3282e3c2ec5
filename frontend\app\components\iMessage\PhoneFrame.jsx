"use client";

import { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import MessageBubble from './MessageBubble';

export default function PhoneFrame({ data, showMissedCall = true, showHeader = true }) {
  // Add safety check for missing data
  if (!data || !data.messages) {
    data = {
      messages: [],
      missedCallAlert: null,
      context: "No data available",
      actionButtons: []
    };
  }

  // Use state for phone numbers to prevent hydration mismatch
  const [phoneNumber, setPhoneNumber] = useState("(*************");
  
  // Use static time to match website and prevent hydration issues
  const [currentTime, setCurrentTime] = useState("3:16 PM");
  const messagesContainerRef = useRef(null);
  
  // Generate random numbers only on client-side after mounting to prevent hydration mismatch
  useEffect(() => {
    // Generate random US phone number for the header only if needed
    if (phoneNumber === "(*************") {
      const randomPhoneNumber = `(${Math.floor(Math.random() * 800) + 200})-${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`;
      setPhoneNumber(randomPhoneNumber);
    }
  }, [phoneNumber]);
  
  // Extract business name from context or missedCallAlert
  const getBusinessName = () => {
    if (data.missedCallAlert?.type?.includes('Restaurant')) {
      return 'Italian Restaurant';
    } else if (data.title === 'Technical Support') {
      return 'FastNet Support';
    } else if (data.title === 'Auto Service') {
      return 'Premier Auto Service';
    } else if (data.title === 'Real Estate') {
      return 'Skyline Properties';
    } else if (data.title === 'Photography Session') {
      return 'Emma Photography';
    } else {
      return 'CallSaver Service'; // Default to generic name
    }
  };
  
  const businessName = getBusinessName();
  
  // Filter out any greeting messages from the original data to avoid duplication
  const [visibleMessages, setVisibleMessages] = useState([]);
  
  // Process messages to avoid duplicate greetings
  useEffect(() => {
    if (data.messages && Array.isArray(data.messages)) {
      // Check if there's already a greeting message
      const hasGreeting = data.messages.some((msg, index) => 
        msg.sender === 'ai' && 
        index === 0 && 
        (msg.content.includes('noticed you just called') || 
         msg.content.includes('Hi!') || 
         msg.content.includes('Hello') || 
         msg.content.includes('Welcome'))
      );
      
      // If there's already a greeting, use all messages as-is
      // Otherwise, skip the first AI message as we'll add our own greeting
      const messagesToShow = hasGreeting 
        ? data.messages 
        : data.messages.slice(data.messages[0]?.sender === 'ai' ? 1 : 0);
        
      setVisibleMessages(messagesToShow);
    } else {
      setVisibleMessages([]);
    }
  }, [data]);
  
  // Get a smart greeting message based on the business type
  const getSmartGreeting = () => {
    // Base greeting with business name
    let greeting = `Hi! We noticed you just called ${businessName}. Sorry we missed your call!`;
    
    // Add business-specific context
    if (data.title === 'Technical Support') {
      greeting += " Our automated assistant can help troubleshoot common internet connection issues or schedule a technician visit if needed.";
    } else if (data.title === 'Restaurant Booking') {
      greeting += " We'd be happy to help you make a reservation or answer any questions about our menu and specials.";
    } else if (data.title === 'Auto Service') {
      greeting += " Our service team can help diagnose issues, schedule maintenance, or provide quotes based on your vehicle's needs.";
    } else if (data.title === 'Real Estate') {
      greeting += " I can help you find the perfect property match, provide more details on specific listings, or schedule viewings at your convenience.";
    } else if (data.title === 'Photography Session') {
      greeting += " Emma is now busy taking magic moments for her customers. How can I assist you? Would you like to book a shooting or do you have any further questions?";
    } else {
      greeting += " How can I assist you today?";
    }
    
    return greeting;
  };
  
  // Generate relevant options based on the business type
  const getOptions = () => {
    if (data.title === 'Technical Support') {
      return [
        "Schedule a technician visit",
        "Troubleshoot my connection",
        "Check my account status",
        "Speak with a representative"
      ];
    } else if (data.title === 'Restaurant Booking') {
      return [
        "Make a dinner reservation",
        "View today's specials",
        "Check wait times",
        "Get location & hours"
      ];
    } else if (data.title === 'Auto Service') {
      return [
        "Schedule maintenance",
        "Diagnose a problem",
        "Get service quotes",
        "Find nearest location"
      ];
    } else if (data.title === 'Real Estate') {
      return [
        "Find properties for sale",
        "Schedule a viewing",
        "Discuss selling my home",
        "Learn about financing options"
      ];
    } else if (data.title === 'Photography Session') {
      return [
        "Book a photography session",
        "View photography packages",
        "Check photographer availability",
        "Ask about photoshoot locations"
      ];
    } else {
      return [
        "Schedule an appointment",
        "Get more information",
        "Speak to a representative",
        "View services and pricing"
      ];
    }
  };
  
  const smartGreeting = getSmartGreeting();
  const options = getOptions();
  
  // If showHeader is false, we only render the messages content
  if (!showHeader) {
    return (
      <div className="px-3 py-3 h-full overflow-y-auto bg-[#f8f8f8] pb-4 max-h-[481px]">
        {/* Date bubble */}
        <div className="text-center my-2 mb-4">
          <span className="text-[11px] bg-[#f1f1f1] text-gray-500 rounded-full px-3 py-0.5 inline-block font-medium border border-gray-200 shadow-sm">Today</span>
        </div>
        
        {/* Missed call alert (if enabled) */}
        {showMissedCall && (
          <div className="bg-[#f9f9f9] px-3 py-2.5 rounded-lg mb-4 shadow-sm mx-auto border border-gray-200">
            <div className="flex items-start">
              <div className="mr-2 bg-red-100 rounded-full p-1.5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-red-600">Missed Call</p>
                <p className="text-xs text-gray-500">(************* - 1 minute ago</p>
              </div>
            </div>
          </div>
        )}
        
        {/* Context/Title - Always show this */}
        <div className="text-center mb-4">
          <p className="text-xs text-green-600 font-semibold bg-green-50 py-1 px-3 rounded-full inline-block border border-green-100">
            SMS Auto-Reply Activated
          </p>
          <p className="text-xs text-green-600 mt-1.5">
            Callsaver Eco-System Triggered through Call-Forwarding
          </p>
        </div>
        
        {/* Conditionally show smart greeting message only if no greeting exists in messages */}
        {!data.messages.some(msg => 
            msg.sender === 'ai' && 
            (msg.content.includes('noticed you just called') || 
             msg.content.includes('Hi!') || 
             msg.content.includes('Hello') || 
             msg.content.includes('Welcome'))
        ) && (
          <div className="flex mb-4 justify-start">
            <div className="max-w-[85%] relative rounded-[16px] rounded-bl-[4px] px-4 py-2.5 bg-[#f1f1f1] text-black border border-gray-300 shadow-sm">
              <div className="relative z-10">
                <p className="text-sm">{smartGreeting}</p>
                <p className="mt-2 text-sm">Please reply with a number to choose:</p>
                <ol className="list-decimal pl-5 mt-1 space-y-0.5 text-sm">
                  {options.map((option, index) => (
                    <li key={index}>{option}</li>
                  ))}
                </ol>
              </div>
              <div className="text-right text-[10px] text-gray-500 mt-1 mr-0.5">
                Delivered
              </div>
            </div>
          </div>
        )}
        
        {/* Chat messages */}
        {visibleMessages.map((message, index) => (
          <MessageBubble
            key={index}
            message={message}
            isLast={index === visibleMessages.length - 1}
          />
        ))}
      </div>
    );
  }
  
  // Otherwise render the full phone frame
  return (
    <div className="relative mx-auto my-8">
      {/* Phone frame */}
      <div className="relative w-[320px] h-[650px] bg-black rounded-[55px] p-3 shadow-xl mx-auto
                     border-[14px] border-black overflow-hidden">
        {/* Notch */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[130px] h-[30px] bg-black rounded-b-2xl z-30"></div>
        
        {/* Screen */}
        <div className="w-full h-full bg-white rounded-[40px] overflow-hidden relative">
          {/* Status bar */}
          <div className="h-7 w-full bg-white flex justify-between items-center px-6 text-xs text-black font-medium z-20 relative">
            <div>{currentTime}</div>
            <div className="flex items-center space-x-1.5">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
              </svg>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.242 0 1 1 0 01-1.415-1.415 5 5 0 017.072 0 1 1 0 01-1.415 1.415zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
              <div>100%</div>
            </div>
          </div>
          
          {/* Message header */}
          <div className="h-14 bg-white border-b border-gray-200 flex justify-between items-center px-4 z-10">
            <div className="flex items-center">
              <div className="text-gray-500 mr-1.5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M15 19l-7-7 7-7" />
                </svg>
              </div>
              <div>
                <div className="font-semibold text-base">{businessName}</div>
                <div className="text-xs text-green-600">{phoneNumber} • SMS</div>
              </div>
            </div>
            <div className="flex space-x-5">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            </div>
          </div>
          
          {/* Messages container */}
          <div 
            ref={messagesContainerRef}
            className="px-3 py-3 h-[481px] overflow-y-auto bg-[#f8f8f8] relative scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent"
          >
            {/* Date bubble */}
            <div className="text-center my-2 mb-4">
              <span className="text-[11px] bg-[#f1f1f1] text-gray-500 rounded-full px-3 py-0.5 inline-block font-medium border border-gray-200 shadow-sm">Today</span>
            </div>
            
            {/* Missed call alert (if enabled) */}
            {showMissedCall && (
              <div className="bg-[#f9f9f9] px-3 py-2.5 rounded-lg mb-4 shadow-sm mx-auto border border-gray-200">
                <div className="flex items-start">
                  <div className="mr-2 bg-red-100 rounded-full p-1.5">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-red-600">Missed Call</p>
                    <p className="text-xs text-gray-500">(************* - 1 minute ago</p>
                  </div>
                </div>
              </div>
            )}
            
            {/* Context/Title - Always show this */}
            <div className="text-center mb-4">
              <p className="text-xs text-green-600 font-semibold bg-green-50 py-1 px-3 rounded-full inline-block border border-green-100">
                SMS Auto-Reply Activated
              </p>
              <p className="text-xs text-green-600 mt-1.5">
                Callsaver Eco-System Triggered through Call-Forwarding
              </p>
            </div>
            
            {/* Conditionally show smart greeting message only if no greeting exists in messages */}
            {!data.messages.some(msg => 
                msg.sender === 'ai' && 
                (msg.content.includes('noticed you just called') || 
                 msg.content.includes('Hi!') || 
                 msg.content.includes('Hello') || 
                 msg.content.includes('Welcome'))
            ) && (
              <div className="flex mb-4 justify-start">
                <div className="max-w-[85%] relative rounded-[16px] rounded-bl-[4px] px-4 py-2.5 bg-[#f1f1f1] text-black border border-gray-300 shadow-sm">
                  <div className="relative z-10">
                    <p className="text-sm">{smartGreeting}</p>
                    <p className="mt-2 text-sm">Please reply with a number to choose:</p>
                    <ol className="list-decimal pl-5 mt-1 space-y-0.5 text-sm">
                      {options.map((option, index) => (
                        <li key={index}>{option}</li>
                      ))}
                    </ol>
                  </div>
                  <div className="text-right text-[10px] text-gray-500 mt-1 mr-0.5">
                    Delivered
                  </div>
                </div>
              </div>
            )}
            
            {/* Chat messages */}
            {visibleMessages.map((message, index) => (
              <MessageBubble
                key={index}
                message={message}
                isLast={index === visibleMessages.length - 1}
              />
            ))}
          </div>
          
          {/* Message input */}
          <div className="h-14 bg-white border-t border-gray-200 flex items-center px-2 absolute bottom-0 w-full z-20">
            <div className="flex-1 bg-[#f1f1f1] rounded-full border border-gray-300 flex items-center px-2 mx-1 h-9">
              <span className="text-gray-400 text-sm px-2 truncate">Text Message - SMS</span>
              <div className="flex-grow"></div>
            </div>
            <div className="w-10 h-10 bg-gray-100 flex items-center justify-center rounded-full ml-1">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="w-10 h-10 bg-green-500 flex items-center justify-center rounded-full ml-1">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
              </svg>
            </div>
          </div>
        </div>
      </div>
      
      {/* Action buttons */}
      {data.actionButtons && data.actionButtons.length > 0 && (
        <div className="mt-6 flex justify-center gap-4 flex-wrap">
          {data.actionButtons.map((button, index) => (
            <motion.button
              key={index}
              className={`flex items-center px-5 py-2.5 rounded-full text-sm font-medium transition-all ${
                button.primary 
                  ? 'bg-green-500 text-white shadow-md hover:bg-green-600' 
                  : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
              }`}
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
            >
              {button.icon && (
                <span className="mr-2">
                  {button.icon}
                </span>
              )}
              {button.label}
            </motion.button>
          ))}
        </div>
      )}
    </div>
  );
} 