import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { api } from '../lib/apiClient';

// Define the User type
interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

// Define the permissions type
type Permissions = string[];

// Define the AuthState interface
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  permissions: Permissions;
  isLoading: boolean;

  // Actions
  login: (userData: User) => void;
  logout: () => void;
  setLoading: (status: boolean) => void;
  setUser: (userData: User | null) => void;
  setPermissions: (permissions: Permissions) => void;
  checkAuth: () => Promise<boolean>;
  fetchPermissions: () => Promise<Permissions>;
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
}

// Create the auth store
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      user: null,
      permissions: [],
      isLoading: true,

      login: (userData: User) => {
        set({ isAuthenticated: true, user: userData, isLoading: false });
        // Fetch permissions after login
        get().fetchPermissions();
      },

      logout: async () => {
        try {
          // Call the logout API endpoint
          await api.post('/auth/logout');
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          // Always clear local state, even if API call fails
          set({ isAuthenticated: false, user: null, permissions: [], isLoading: false });
        }
      },

      setLoading: (status: boolean) => {
        set({ isLoading: status });
      },

      setUser: (userData: User | null) => {
        set({ user: userData });
      },

      setPermissions: (permissions: Permissions) => {
        set({ permissions });
      },

      checkAuth: async () => {
        set({ isLoading: true });
        try {
          const user = await api.get<User>('/users/me');
          set({ isAuthenticated: true, user, isLoading: false });

          // Fetch permissions after successful authentication
          await get().fetchPermissions();

          return true;
        } catch (error) {
          // If unauthorized or any other error, ensure logged out state
          set({ isAuthenticated: false, user: null, permissions: [], isLoading: false });
          return false;
        }
      },

      fetchPermissions: async () => {
        try {
          const response = await api.get<{ success: boolean; data: { permissions: Permissions } }>('/users/permissions');
          const permissions = response.data.permissions;
          set({ permissions });
          return permissions;
        } catch (error) {
          console.error('Error fetching permissions:', error);
          return [];
        }
      },

      hasPermission: (permission: string) => {
        const { permissions } = get();

        // Super admin has all permissions
        if (permissions.includes('**')) return true;

        // Parse the permission string
        const [resource, action, scope = 'any'] = permission.split(':');

        // Check for exact permission match
        if (permissions.includes(permission)) return true;

        // Check for wildcard permissions
        if (permissions.includes(`${resource}:*`)) return true;
        if (permissions.includes(`${resource}:*:${scope}`)) return true;
        if (permissions.includes(`*:${action}`)) return true;
        if (permissions.includes(`*:${action}:${scope}`)) return true;
        if (permissions.includes(`${resource}:${action}:*`)) return true;
        if (permissions.includes(`${resource}:${action}`)) return true;

        return false;
      },

      hasAnyPermission: (permissionsToCheck: string[]) => {
        return permissionsToCheck.some(permission => get().hasPermission(permission));
      },

      hasAllPermissions: (permissionsToCheck: string[]) => {
        return permissionsToCheck.every(permission => get().hasPermission(permission));
      },
    }),
    {
      name: 'callsaver-auth', // Name for the persisted state in storage
      storage: createJSONStorage(() => sessionStorage), // Use sessionStorage instead of localStorage for security
      // Only persist authentication status and user data, not loading state
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        user: state.user,
        permissions: state.permissions
      }),
    }
  )
);

// Export a selector to prevent unnecessary re-renders
export const useUser = () => useAuthStore((state) => state.user);
export const useIsAuthenticated = () => useAuthStore((state) => state.isAuthenticated);
export const useAuthLoading = () => useAuthStore((state) => state.isLoading);
