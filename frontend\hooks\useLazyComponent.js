'use client';

import { useState, useEffect } from 'react';

/**
 * Custom hook for lazy loading components
 * 
 * This hook allows you to lazy load components based on various conditions:
 * - When the component is in or near the viewport (using IntersectionObserver)
 * - After a specified delay
 * - When a specific condition is met
 * - On demand (manually triggered)
 * 
 * @param {Function} importFunc - Dynamic import function that returns the component
 * @param {Object} options - Configuration options
 * @param {boolean} options.viewport - Whether to load when in viewport (default: false)
 * @param {number} options.delay - Delay in ms before loading (default: 0)
 * @param {boolean} options.condition - Condition that must be true to load (default: true)
 * @param {string} options.rootMargin - Root margin for IntersectionObserver (default: '200px')
 * @param {number} options.threshold - Threshold for IntersectionObserver (default: 0.1)
 * @returns {Object} - { Component, load, loading, error }
 */
const useLazyComponent = (importFunc, options = {}) => {
  const {
    viewport = false,
    delay = 0,
    condition = true,
    rootMargin = '200px',
    threshold = 0.1,
  } = options;

  const [Component, setComponent] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [ref, setRef] = useState(null);

  // Function to load the component
  const load = async () => {
    if (Component || loading) return;

    setLoading(true);
    try {
      const module = await importFunc();
      // Handle both default and named exports
      const LoadedComponent = module.default || module;
      setComponent(() => LoadedComponent);
      setLoading(false);
    } catch (err) {
      console.error('Error loading component:', err);
      setError(err);
      setLoading(false);
    }
  };

  // Load based on delay
  useEffect(() => {
    if (!condition || Component || !delay) return;

    const timer = setTimeout(() => {
      load();
    }, delay);

    return () => clearTimeout(timer);
  }, [condition, delay, Component]);

  // Load based on viewport visibility
  useEffect(() => {
    if (!viewport || !ref || Component) return;
    if (!condition) return;
    if (typeof window === 'undefined' || !window.IntersectionObserver) {
      // Fallback for environments without IntersectionObserver
      load();
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          load();
          observer.disconnect();
        }
      },
      { rootMargin, threshold }
    );

    observer.observe(ref);

    return () => {
      observer.disconnect();
    };
  }, [viewport, ref, Component, condition, rootMargin, threshold]);

  return {
    Component,
    load,
    loading,
    error,
    ref: setRef,
  };
};

export default useLazyComponent;
