"use client";

import { useEffect } from 'react';

/**
 * Component that implements various performance optimizations
 * This includes prefetching, preconnecting, and other performance-related tasks
 */
export default function PerformanceOptimizer() {
  // Define functions inside useEffect to avoid dependency issues
  useEffect(() => {
    /**
     * Prefetch critical resources for common user flows
     */
    const prefetchCriticalResources = () => {
      // Only run on production to avoid unnecessary requests during development
      if (process.env.NODE_ENV !== 'production') return;

      // Helper function to prefetch a URL
      const prefetch = (url) => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = url;
        document.head.appendChild(link);
      };

      // Prefetch critical paths that users are likely to navigate to
      setTimeout(() => {
        prefetch('/dashboard');
        prefetch('/crm');
        // Add more critical paths as needed
      }, 3000); // Delay prefetching to prioritize initial page load
    };

    /**
     * Set up lazy loading for non-critical elements
     */
    const setupLazyLoading = () => {
      if (typeof window === 'undefined' || !window.IntersectionObserver) return;

      // Create an observer for lazy-loaded elements
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (!entry.isIntersecting) return;

          const target = entry.target;

          // Handle different types of elements
          if (target.dataset.lazySrc) {
            // For images
            target.src = target.dataset.lazySrc;
            target.removeAttribute('data-lazy-src');
          } else if (target.dataset.lazyBg) {
            // For background images
            target.style.backgroundImage = `url(${target.dataset.lazyBg})`;
            target.removeAttribute('data-lazy-bg');
          } else if (target.dataset.lazyComponent) {
            // Load components dynamically - the component should handle its own loading
            target.dataset.lazyLoaded = 'true';
            target.removeAttribute('data-lazy-component');
          }

          // Once loaded, no need to observe anymore
          observer.unobserve(target);
        });
      }, {
        rootMargin: '200px', // Start loading when element is 200px from viewport
        threshold: 0.01 // Trigger when at least 1% of the element is visible
      });

      // Observe all elements with lazy loading attributes
      document.querySelectorAll('[data-lazy-src], [data-lazy-bg], [data-lazy-component]').forEach(el => {
        observer.observe(el);
      });

      // Clean up observer on unmount
      return () => {
        observer.disconnect();
      };
    };

    /**
     * Optimize performance when page visibility changes
     */
    const setupVisibilityHandling = () => {
      if (typeof document === 'undefined') return;

      // Handler function for visibility changes
      const handleVisibilityChange = () => {
        if (document.hidden) {
          // Page is not visible
          document.body.classList.add('performance-paused');
          // Signal to other components that page is hidden
          window.dispatchEvent(new CustomEvent('callsaver:visibility-hidden'));
        } else {
          // Page is visible again
          document.body.classList.remove('performance-paused');
          // Signal to other components that page is visible
          window.dispatchEvent(new CustomEvent('callsaver:visibility-visible'));
        }
      };

      // Add event listener
      document.addEventListener('visibilitychange', handleVisibilityChange);

      // Clean up event listener on unmount
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    };

    // Execute the optimization functions
    prefetchCriticalResources();
    const lazyLoadingCleanup = setupLazyLoading();
    const visibilityHandlingCleanup = setupVisibilityHandling();

    // Return a cleanup function that calls all the individual cleanup functions
    return () => {
      if (lazyLoadingCleanup) lazyLoadingCleanup();
      if (visibilityHandlingCleanup) visibilityHandlingCleanup();
    };
  }, []);

  // Functions moved inside useEffect to avoid dependency issues

  // This component doesn't render anything visible
  return null;
}