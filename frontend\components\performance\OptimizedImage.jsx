'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';

/**
 * OptimizedImage Component
 * 
 * A wrapper around Next.js Image component with additional optimizations:
 * - Lazy loading with IntersectionObserver
 * - Blur-up loading effect
 * - Responsive sizing
 * - WebP/AVIF format support
 * - Error handling with fallback
 * 
 * @param {Object} props - Component props
 * @param {string} props.src - Image source URL
 * @param {string} props.alt - Image alt text
 * @param {number} props.width - Image width
 * @param {number} props.height - Image height
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.objectFit - Object fit style (cover, contain, etc.)
 * @param {string} props.objectPosition - Object position style
 * @param {boolean} props.priority - Whether to prioritize loading
 * @param {string} props.placeholder - Placeholder type (blur, empty)
 * @param {string} props.blurDataURL - Base64 encoded blur image
 * @param {string} props.fallbackSrc - Fallback image source if main image fails to load
 * @returns {React.ReactElement} - The optimized image component
 */
const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  className = '',
  objectFit = 'cover',
  objectPosition = 'center',
  priority = false,
  placeholder = 'empty',
  blurDataURL,
  fallbackSrc,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(false);
  const [inView, setInView] = useState(false);
  const [ref, setRef] = useState(null);

  // Generate a blur data URL if not provided
  const generatedBlurDataURL = !blurDataURL && placeholder === 'blur'
    ? 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImcxIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj48c3RvcCBzdG9wLWNvbG9yPSIjZjNmNGY2IiBvZmZzZXQ9IjAlIi8+PHN0b3Agc3RvcC1jb2xvcj0iI2UxZTRlOCIgb2Zmc2V0PSIxMDAlIi8+PC9saW5lYXJHcmFkaWVudD48L2RlZnM+PHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiIGZpbGw9InVybCgjZzEpIi8+PC9zdmc+'
    : blurDataURL;

  // Set up intersection observer to detect when image is in viewport
  useEffect(() => {
    if (!ref || priority) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '200px',
        threshold: 0.01,
      }
    );
    
    observer.observe(ref);
    
    return () => {
      observer.disconnect();
    };
  }, [ref, priority]);

  // Handle image load event
  const handleLoad = () => {
    setIsLoaded(true);
  };

  // Handle image error event
  const handleError = () => {
    setError(true);
  };

  // Use fallback image if main image fails to load
  const imageSrc = error && fallbackSrc ? fallbackSrc : src;

  // Determine if we should render the image
  const shouldRender = priority || inView;

  return (
    <div
      ref={setRef}
      className={`relative overflow-hidden ${className}`}
      style={{ width: width || '100%', height: height || 'auto' }}
    >
      {shouldRender ? (
        <Image
          src={imageSrc}
          alt={alt}
          width={width}
          height={height}
          className={`transition-opacity duration-500 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
          style={{
            objectFit,
            objectPosition,
          }}
          onLoad={handleLoad}
          onError={handleError}
          priority={priority}
          placeholder={placeholder}
          blurDataURL={generatedBlurDataURL}
          {...props}
        />
      ) : (
        <div
          className="w-full h-full bg-gray-200 animate-pulse"
          style={{ aspectRatio: width && height ? width / height : undefined }}
        />
      )}
    </div>
  );
};

export default OptimizedImage;
