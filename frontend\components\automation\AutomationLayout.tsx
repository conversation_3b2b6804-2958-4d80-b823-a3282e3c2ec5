'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import NumberList from './NumberList';
import AutomationConfigPanel from './AutomationConfigPanel';
import AutomationSkeleton from './AutomationSkeleton';
import ErrorMessage from '../shared/ErrorMessage';
import EmptyState from '../shared/EmptyState';
import { PhoneIcon } from '@heroicons/react/24/outline';

// Type for phone number data
export interface PhoneNumber {
  id: string;
  phoneNumber: string;
  friendlyName: string;
  automationStatus: 'active' | 'inactive';
}

export default function AutomationLayout() {
  // State for the selected phone number
  const [selectedNumberId, setSelectedNumberId] = useState<string | null>(null);

  // Fetch the list of owned phone numbers
  const {
    data: numbers,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['numbers', 'owned'],
    queryFn: async () => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock data
        return [
          {
            id: '1',
            phoneNumber: '+***********',
            friendlyName: 'Business Line',
            automationStatus: 'active',
          },
          {
            id: '2',
            phoneNumber: '+***********',
            friendlyName: 'Toll-Free Support',
            automationStatus: 'inactive',
          },
          {
            id: '3',
            phoneNumber: '+***********',
            friendlyName: 'Sales Line',
            automationStatus: 'active',
          },
        ] as PhoneNumber[];
      }
      
      // In production, fetch from API
      const { data } = await axios.get<PhoneNumber[]>('/api/numbers');
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Handle number selection
  const handleSelectNumber = (numberId: string) => {
    setSelectedNumberId(numberId);
  };

  // If loading, show skeleton
  if (isLoading) {
    return <AutomationSkeleton />;
  }

  // If error, show error message
  if (error) {
    return (
      <ErrorMessage
        title="Failed to load phone numbers"
        message="We couldn't load your phone numbers. Please try again later."
        actionText="Try Again"
        onAction={() => window.location.reload()}
      />
    );
  }

  // If no numbers, show empty state
  if (!numbers || numbers.length === 0) {
    return (
      <EmptyState
        icon={<PhoneIcon className="h-12 w-12 text-gray-400" />}
        title="No phone numbers found"
        description="You need to purchase a phone number before you can set up automations."
        actionText="Buy a Number"
        actionHref="/numbers"
      />
    );
  }

  return (
    <div className="flex flex-col lg:flex-row h-full w-full gap-6">
      {/* Phone number list */}
      <div className="w-full lg:w-1/4 lg:max-w-xs">
        <NumberList
          numbers={numbers}
          selectedNumberId={selectedNumberId}
          onSelectNumber={handleSelectNumber}
        />
      </div>

      {/* Configuration panel */}
      <div className="flex-1">
        {selectedNumberId ? (
          <AutomationConfigPanel selectedNumberId={selectedNumberId} />
        ) : (
          <div className="flex items-center justify-center h-64 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <p className="text-gray-500 dark:text-gray-400">
              Select a phone number to configure its automation settings.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
