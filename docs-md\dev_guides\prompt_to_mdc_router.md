# Prompt to MDC Task Router

This document maps incoming prompts, keywords, or identified code changes to the relevant task `.mdc` files for tracking and execution. It also defines tags used for categorization and potential auto-assignment.

## Routing Map

This map helps direct development efforts based on the area of focus.

| Keywords / Prompt Focus / Changed Component Area | Target Task File                     | Relevant Tags                                                                 |
| :----------------------------------------------- | :----------------------------------- | :---------------------------------------------------------------------------- |
| `security`, `auth`, `HMAC`, `validation`, `roles`  | `docs/tasks/security_tasks.mdc`      | #security, #authentication, #authorization, #webhook, #validation, #middleware |
| `webhook`, `idempotency`, `retry`, `failure`, `queue` | `docs/tasks/idempotency_tasks.mdc` | #idempotency, #webhook, #reliability, #queue, #failure_recovery, #stripe, #twilio |
| `AI`, `memory`, `Pinecone`, `context`, `fallback` | `docs/tasks/ai_tasks.mdc`            | #ai, #memory, #pinecone, #context_window, #reliability, #refactor             |
| `cleanup`, `legacy`, `refactor`, `structure`     | `docs/tasks/cleanup_tasks.mdc`       | #cleanup, #legacy_code, #refactor, #maintenance, #frontend, #structure        |
| `test`, `coverage`, `jest`, `integration`, `unit` | `docs/tasks/testing_tasks.mdc`       | #testing, #integration_test, #unit_test, #webhook, #billing, #ai, #reliability |
| `performance`, `speed`, `cache`, `database`, `pool` | `docs/tasks/performance_tasks.mdc`   | #performance, #database, #caching, #redis, #prisma, #mongoose, #streaming     |
| `dev workflow`, `CI`, `CD`, `git hooks`, `config` | `docs/tasks/dev_workflow_tasks.mdc`  | #dev_workflow, #ci, #cd, #git, #hooks, #config, #automation, #documentation |
| `scalability`, `HA`, `multi-region`, `compliance` | `docs/tasks/scalability_tasks.mdc`   | #scalability, #high_availability, #compliance, #gdpr, #audit_log, #monitoring |
| `architecture`, `diagram`, `decisions`           | `docs/architecture/*`, `docs/tasks/dev_workflow_tasks.mdc` | #architecture, #documentation, #planning                                      |
| `deployment`, `infra`, `kubernetes`, `docker`    | `docs/infra/*`, `docs/tasks/scalability_tasks.mdc` | #infrastructure, #deployment, #scalability, #planning                         |

## Tag Definitions for Auto-Assignment

These tags can be used in commit messages, PR descriptions, or issue trackers to link work back to specific task categories.

-   **#security:** Related to authentication, authorization, input validation, vulnerability patching.
-   **#webhook:** Tasks involving incoming webhooks (Twilio, Stripe, etc.).
-   **#idempotency:** Ensuring operations can be retried safely.
-   **#failure_recovery:** Handling errors, retries, dead-letter queues.
-   **#ai:** Tasks related to AI models, memory, context, prompts.
-   **#memory:** Specifically about AI context or user session memory.
-   **#pinecone:** Tasks involving the Pinecone vector database.
-   **#cleanup:** Removing dead code, refactoring for clarity, improving structure.
-   **#legacy_code:** Dealing with outdated or `.original` code.
-   **#testing:** Writing or updating unit, integration, or end-to-end tests.
-   **#performance:** Improving speed, resource usage, database queries, caching.
-   **#database:** Changes related to Prisma, Mongoose, SQL, NoSQL schemas or queries.
-   **#caching:** Implementing or modifying caching strategies (Redis, in-memory).
-   **#dev_workflow:** Improving CI/CD, git hooks, local setup, tooling.
-   **#ci, #cd:** Continuous Integration / Continuous Deployment pipeline tasks.
-   **#config:** Managing environment variables or application configuration.
-   **#documentation:** Updating `.mdc` files, READMEs, or architecture diagrams.
-   **#architecture:** High-level design decisions and documentation.
-   **#scalability:** Preparing the system for higher loads, HA, multi-region.
-   **#high_availability:** Ensuring system uptime and redundancy.
-   **#compliance:** Tasks related to GDPR, CCPA, PII handling, audit logs.
-   **#monitoring:** Adding dashboards, alerts, or logging for observability.
-   **#refactor:** Restructuring existing code without changing external behavior.
-   **#frontend:** Changes specific to the Next.js frontend application.
-   **#backend:** Changes specific to the Node.js/Express backend application.
-   **#middleware:** Modifying or adding Express middleware.
-   **#controller:** Changes within API route controllers.
-   **#service:** Changes within business logic service layers.
-   **#queue:** Tasks related to BullMQ or other job queues.
-   **#stripe:** Integration with the Stripe API.
-   **#twilio:** Integration with the Twilio API.
