import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';
import PermissionGate from '../PermissionGate';
import { Spinner } from '../ui/Spinner';
import { useUserManagement, User } from '../../hooks/useUserManagement';
import { useRoleManagement } from '../../hooks/useRoleManagement';
import RoleHierarchyDisplay from './RoleHierarchyDisplay';

// Types are imported from hooks

const UserRoleManagement: React.FC = () => {
  const { hasPermission } = useAuthStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedRoleId, setSelectedRoleId] = useState<string>('');

  // Use the user management hook
  const { usersQuery, updateUserRole, isUpdatingUserRole } = useUserManagement();

  // Use the role management hook
  const { roles } = useRoleManagement();

  // Fetch users with search term
  const { data: users, isLoading: usersLoading } = usersQuery(searchTerm);

  // Set the selected role when a user is selected
  useEffect(() => {
    if (selectedUser) {
      setSelectedRoleId(selectedUser.roleId);
    } else {
      setSelectedRoleId('');
    }
  }, [selectedUser]);

  // Handle user selection
  const handleUserSelect = (user: User) => {
    setSelectedUser(user);
  };

  // Handle role change
  const handleRoleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedRoleId(e.target.value);
  };

  // Handle save
  const handleSave = () => {
    if (selectedUser && selectedRoleId) {
      updateUserRole({
        userId: selectedUser.id,
        roleId: selectedRoleId,
      }, {
        onSuccess: () => {
          setSelectedUser(null);
        }
      });
    }
  };

  // Get role name by ID
  const getRoleName = (roleId: string): string => {
    const role = roles.data?.find(r => r.id === roleId);
    return role ? role.name : 'Unknown';
  };

  if (usersLoading || roles.isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <PermissionGate permission="users:update:any">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold mb-6">User Role Management</h2>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Search users by name or email..."
              className="w-full p-3 pl-10 border border-gray-300 rounded-md"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <svg
              className="absolute left-3 top-3.5 h-5 w-5 text-gray-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                clipRule="evenodd"
              />
            </svg>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* User List */}
          <div className="bg-gray-50 p-4 rounded-lg max-h-[600px] overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">Users</h3>
            <div className="space-y-2">
              {users?.map((user) => (
                <div
                  key={user.id}
                  className={`p-3 rounded-md cursor-pointer transition-colors ${
                    selectedUser?.id === user.id
                      ? 'bg-blue-100 border-l-4 border-blue-500'
                      : 'bg-white hover:bg-gray-100'
                  }`}
                  onClick={() => handleUserSelect(user)}
                >
                  <div className="font-medium">{user.name}</div>
                  <div className="text-sm text-gray-500">{user.email}</div>
                  <div className="text-xs text-gray-400 mt-1">
                    Role: <span className="font-medium">{getRoleName(user.roleId)}</span>
                  </div>
                </div>
              ))}

              {users?.length === 0 && (
                <div className="text-center py-4 text-gray-500">
                  No users found
                </div>
              )}
            </div>
          </div>

          {/* User Details */}
          <div className="md:col-span-2">
            {selectedUser ? (
              <div className="bg-white p-6 rounded-lg border">
                <h3 className="text-xl font-semibold mb-4">{selectedUser.name}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p>{selectedUser.email}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Current Role</p>
                    <p>{getRoleName(selectedUser.roleId)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">User ID</p>
                    <p className="font-mono text-sm">{selectedUser.id}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Created At</p>
                    <p>{new Date(selectedUser.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h4 className="font-medium mb-2">Change Role</h4>
                  <div className="flex items-end gap-4">
                    <div className="flex-grow">
                      <label className="block text-sm text-gray-500 mb-1">
                        Select Role
                      </label>
                      <select
                        id="role-select"
                        name="role"
                        aria-label="Select Role"
                        className="w-full p-2 border border-gray-300 rounded-md"
                        value={selectedRoleId}
                        onChange={handleRoleChange}
                      >
                        <option value="">Select a role</option>
                        {roles.data?.map((role) => (
                          <option key={role.id} value={role.id}>
                            {role.name}{role.description ? ` - ${role.description}` : ''}
                          </option>
                        ))}
                      </select>
                    </div>
                    <button
                      type="button"
                      className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
                      onClick={handleSave}
                      disabled={!selectedRoleId || selectedRoleId === selectedUser.roleId || isUpdatingUserRole}
                    >
                      {isUpdatingUserRole ? (
                        <Spinner size="sm" className="text-white" />
                      ) : (
                        'Update Role'
                      )}
                    </button>
                  </div>
                </div>

                {/* Role Hierarchy and Permissions */}
                <div className="mt-6">
                  <h4 className="font-medium mb-2">Role Details</h4>
                  {selectedRoleId && roles.data && (
                    <RoleHierarchyDisplay
                      role={roles.data.find(r => r.id === selectedRoleId) || null}
                      allRoles={roles.data}
                    />
                  )}
                </div>
              </div>
            ) : (
              <div className="flex justify-center items-center h-64 bg-gray-50 rounded-lg">
                <p className="text-gray-500">Select a user to manage roles</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </PermissionGate>
  );
};

export default UserRoleManagement;
