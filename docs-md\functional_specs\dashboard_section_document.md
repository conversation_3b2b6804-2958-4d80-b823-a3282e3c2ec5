---
description:
globs:
alwaysApply: false
---
# Dashboard Section Functional Document (`dashboard_section_document.mdc`)

## 1. Purpose and Scope

**Purpose:** Provide users with a high-level overview of their CallSaver account activity, key metrics, recent events, and quick access to common actions. This is the primary landing page after login.

**Scope:**
- Display summary statistics (e.g., total calls, messages, active automations).
- Show recent activity feeds (calls, voicemails, messages, alerts).
- Provide quick links to critical sections (e.g., Buy Number, Manage Automations, View Logs).
- Display current credit balance and usage overview.
- Offer quick access to AI assistant interactions or status.

## 2. User Interactions

- **View Metrics:** Users can see key performance indicators at a glance.
- **Review Recent Activity:** Scroll through lists of recent calls, messages, voicemails, and system alerts.
- **Navigate:** Click quick links or sidebar navigation to access other sections.
- **Check Balance:** View current credit balance and potentially a link to top-up.
- **Interact with AI Snippets:** (If applicable) See recent AI summaries or alerts, click to view details.
- **Customize View:** (Future) Allow users to customize the widgets displayed on their dashboard.

## 3. Backend Integrations & Services Used

- **User Service:** Fetch user profile information and preferences.
- **Analytics Service:** Retrieve aggregated metrics and recent activity data.
- **Billing Service:** Get current credit balance.
- **Notification Service:** Fetch recent system alerts or notifications.
- **AI Service:** (Optional) Fetch AI-generated summaries or insights.
- **Database:** Access pre-aggregated data views for performance.

## 4. Necessary API Endpoints

- `GET /api/dashboard/summary`: Fetches key metrics (total calls today/week/month, messages, active automations, credit balance).
- `GET /api/dashboard/recent-activity?type=all|calls|messages|voicemails|alerts&limit=10`: Fetches a list of recent events.
- `GET /api/dashboard/ai-insights?limit=3`: (Optional) Fetches recent AI-generated summaries or important alerts.
- `GET /api/users/me/balance`: Fetches the current credit balance (potentially part of the summary endpoint).

## 5. Expected Frontend Component Structure

```
/components
  /dashboard
    DashboardLayout.tsx         # Main layout container for the dashboard
    MetricCard.tsx              # Reusable card for displaying a single metric
    RecentActivityFeed.tsx      # Component to display lists of recent events
      ActivityItem.tsx          # Individual item in the feed (call, message, etc.)
    QuickLinks.tsx              # Section for quick navigation links
    CreditBalanceWidget.tsx     # Displays current credits
    AISummaryWidget.tsx         # (Optional) Displays AI insights
    DashboardSkeleton.tsx       # Loading state placeholder
```

## 6. Data Displayed

- Total calls (configurable time range: today, week, month).
- Total messages (configurable time range).
- Number of active automations/AI assistants.
- Current credit balance.
- List of recent calls (caller ID, time, duration, link to log).
- List of recent messages (sender/recipient, time, snippet, link to log).
- List of recent voicemails (caller ID, time, duration, link to log).
- List of recent system alerts (type, message, time, link if applicable).
- (Optional) AI-generated summaries or critical alerts.

## 7. State and UI Behavior

- **Loading State:** Display skeletons or loading indicators while fetching data.
- **Empty State:** Show informative messages if there's no data (e.g., "No calls yet today").
- **Data Refresh:** Implement automatic refresh (e.g., every 60 seconds) or a manual refresh button.
- **Responsiveness:** Ensure layout adapts to different screen sizes.
- **Interactivity:** Metrics might be clickable to navigate to the relevant detailed section (e.g., clicking "Total Calls" goes to Call Logs).

## 8. AI Integration

- Display AI-generated summaries of recent activity (e.g., "You had 5 missed calls today, 2 with high urgency sentiment").
- Show alerts generated by the AI assistant (e.g., "AI detected potential spam call from +1234567890").
- Quick link to interact with the primary AI assistant.

## 9. Error Handling Rules

- **API Errors:** Display a generic error message (e.g., "Failed to load dashboard data") with a retry option. Use toast notifications for non-critical fetch errors.
- **Specific Widget Errors:** If one widget fails to load (e.g., recent activity), display an error within that widget's area without breaking the entire dashboard.
- **Authentication Errors:** Redirect to login if the user session is invalid.

## 10. Logging and Usage Tracking Expectations

- **Log:**
    - Successful dashboard data loads.
    - API errors encountered during data fetching (include error details).
    - User navigation clicks from dashboard widgets/links.
- **Track:**
    - Dashboard view events (page loads).
    - Time spent on the dashboard page.
    - Clicks on specific widgets or quick links (understand user focus).
    - Use of the refresh mechanism.
