'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { Switch } from '@headlessui/react';

interface AutomationToggleProps {
  numberId: string;
  initialStatus: boolean;
}

export default function AutomationToggle({
  numberId,
  initialStatus,
}: AutomationToggleProps) {
  // State for the toggle
  const [enabled, setEnabled] = useState(initialStatus);
  
  // Get the query client
  const queryClient = useQueryClient();

  // Mutation for toggling automation
  const toggleMutation = useMutation({
    mutationFn: async (newStatus: boolean) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.post(`/api/numbers/${numberId}/automations/toggle`, {
        enabled: newStatus,
      });
      return data;
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['numbers', 'owned'] });
      queryClient.invalidateQueries({ queryKey: ['automations', numberId] });
    },
    onError: () => {
      // Revert the toggle on error
      setEnabled(!enabled);
      
      // Show error toast (you can use a toast library like react-hot-toast)
      console.error('Failed to toggle automation status');
    },
  });

  // Handle toggle change
  const handleToggle = () => {
    const newStatus = !enabled;
    setEnabled(newStatus); // Optimistically update UI
    toggleMutation.mutate(newStatus);
  };

  return (
    <div className="flex items-center">
      <span className="mr-3 text-sm font-medium text-gray-500 dark:text-gray-400">
        {enabled ? 'Active' : 'Inactive'}
      </span>
      <Switch
        checked={enabled}
        onChange={handleToggle}
        disabled={toggleMutation.isPending}
        className={`${
          enabled ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
        } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${
          toggleMutation.isPending ? 'opacity-50 cursor-not-allowed' : ''
        }`}
      >
        <span
          className={`${
            enabled ? 'translate-x-6' : 'translate-x-1'
          } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
        />
      </Switch>
    </div>
  );
}
