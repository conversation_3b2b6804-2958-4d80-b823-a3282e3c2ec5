---
description:
globs:
alwaysApply: false
---
# Global State and API Integration Plan (`global_state_and_api_integration.mdc`)

## 1. Overview

This document outlines the global frontend architecture for state management, API communication, and real-time updates within the CallSaver Next.js application. It serves as the foundation upon which feature-specific implementations (Dashboard, Automation, Number Management, etc.) are built, ensuring consistency and maintainability. It primarily implements the strategy defined in `component_state_mapping.mdc`.

## 2. Core Libraries

- **Server State:** TanStack Query (React Query) v5 (`@tanstack/react-query`, `@tanstack/react-query-devtools`)
- **Global Client State:** Zustand (`zustand`)
- **API Client:** Axios (`axios`) or native `fetch` with wrappers. (Decision: Use Axios for interceptors and ease of use).
- **UI Framework:** Next.js 14+ (App Router)
- **Styling:** TailwindCSS
- **Notifications:** `react-hot-toast` or similar.

## 3. Global Client State (Zustand)

Define Zustand stores for state that needs to be accessed or modified across different, potentially unrelated parts of the application and isn't directly tied to server data. Stores will reside in `front/mainpage/stores/`.

**Example Stores:**

- **`useAuthStore`:**
    - State: `isAuthenticated: boolean`, `user: { id: string, name: string, email: string, role: string } | null`, `isLoading: boolean`
    - Actions: `login(userData)`, `logout()`, `setLoading(status)`, `setUser(userData)`
    - Purpose: Manage user authentication status and basic profile info needed globally (e.g., in header, sidebar). Initial state potentially hydrated from server-side session check.

- **`useUIStore`:**
    - State: `isSidebarCollapsed: boolean`, `isNotificationsPanelOpen: boolean`, `unreadNotificationsCount: number`
    - Actions: `toggleSidebar()`, `toggleNotificationsPanel()`, `setUnreadNotificationsCount(count)`, `incrementUnreadNotificationsCount()`
    - Purpose: Manage global UI states like navigation visibility and notification indicators. Persist `isSidebarCollapsed` to `localStorage`.

**Implementation:**
- Create individual store files (e.g., `stores/authStore.ts`, `stores/uiStore.ts`).
- Use selectors (`useAuthStore(state => state.user)`) in components to prevent unnecessary re-renders.
- Consider Zustand middleware for devtools (`zustand/middleware/devtools`) and persistence (`zustand/middleware/persist`).

## 4. Server State (React Query)

React Query will manage all interactions with the backend API for fetching, caching, and mutating server data.

**Configuration (`front/mainpage/providers/ReactQueryProvider.tsx`):**
- **`QueryClientProvider`:** Wrap the main application layout (`app/layout.tsx`) with the provider.
- **`QueryClient` Instance:** Create a single `QueryClient` instance.
    - **Default Options:** Configure global defaults:
        ```javascript
        const queryClient = new QueryClient({
          defaultOptions: {
            queries: {
              staleTime: 5 * 60 * 1000, // 5 minutes
              refetchOnWindowFocus: true, // Consider tuning based on data volatility
              retry: (failureCount, error) => {
                // Don't retry on 4xx errors (auth, bad request)
                if (error?.response?.status >= 400 && error?.response?.status < 500) {
                  return false;
                }
                return failureCount < 3; // Retry 3 times on other errors
              },
            },
            mutations: {
              onError: (error, variables, context) => {
                // Global mutation error logging/handling (e.g., generic toast)
                console.error('Mutation failed:', error);
                // Consider showing a generic error toast here
              },
            },
          },
        });
        ```
- **DevTools:** Include `<ReactQueryDevtools initialIsOpen={false} />` within the provider for development builds.

**Usage:**
- Feature modules will define their own query keys and use `useQuery`, `useInfiniteQuery`, `useMutation` hooks as detailed in their respective implementation plans (`dashboard_ui_implementation.mdc`, etc.).
- Query keys should follow a consistent pattern (e.g., `['entity', 'detail/list', { params }]`).

## 5. API Client (Axios)

Configure a global Axios instance for making API requests. This allows for centralized configuration of base URL, headers, and interceptors. (`front/mainpage/lib/apiClient.ts`)

```typescript
import axios from 'axios';

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api', // Get base URL from env
  withCredentials: true, // Crucial for sending HttpOnly cookies
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
});

// Request Interceptor (Optional - e.g., for adding specific headers if needed)
apiClient.interceptors.request.use(
  (config) => {
    // Modify config before request is sent (e.g., add dynamic headers)
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response Interceptor (Crucial for global error handling)
apiClient.interceptors.response.use(
  (response) => {
    // Any status code within 2xx range cause this function to trigger
    return response;
  },
  (error) => {
    // Any status codes outside 2xx range cause this function to trigger
    const { response } = error;

    if (response) {
      // Handle specific status codes globally
      if (response.status === 401) {
        // Unauthorized: User session expired or invalid
        console.error('API Error 401: Unauthorized');
        // Option 1: Trigger logout action in auth store
        // useAuthStore.getState().logout(); // Be careful with direct store access outside components
        // Option 2: Redirect to login page (might need access to router)
        // window.location.href = '/login?sessionExpired=true'; // Simple redirect
      } else if (response.status === 403) {
        // Forbidden: User does not have permission
        console.error('API Error 403: Forbidden');
        // Show a generic "Permission Denied" message/toast
      } else if (response.status >= 500) {
        // Server Error
        console.error(`API Error ${response.status}: Server Error`, response.data);
        // Show a generic "Server Error" message/toast
      }
    } else if (error.request) {
      // Request was made but no response received (network error)
      console.error('API Error: No response received', error.request);
      // Show a generic "Network Error" message/toast
    } else {
      // Something happened in setting up the request
      console.error('API Error: Request setup error', error.message);
    }

    // IMPORTANT: Return the rejected promise so React Query/component can handle specific errors too
    return Promise.reject(error);
  }
);

export default apiClient;
```

**Integration:**
- React Query's `queryFn` and `mutationFn` will use this `apiClient` instance.
- Example `queryFn`: `queryFn: async () => { const { data } = await apiClient.get('/dashboard/summary'); return data; }`

## 6. WebSocket Integration

Manage WebSocket connections for real-time updates (e.g., notifications, status changes).

**Approach:**
- Establish a single WebSocket connection globally, likely within a main layout component or a dedicated provider (`front/mainpage/providers/WebSocketProvider.tsx`).
- Use a library like `socket.io-client` or native WebSockets.
- **Connection Management:** Handle connection opening, closing, reconnection logic. Only connect if the user is authenticated.
- **Event Handling:**
    - Listen for specific events from the server (e.g., `new_notification`, `balance_updated`, `call_status_changed`).
    - On receiving an event:
        - **Update Global State:** Directly update Zustand stores for simple state changes (e.g., `useUIStore.getState().incrementUnreadNotificationsCount()`).
        - **Invalidate Server Cache:** Use `queryClient.invalidateQueries()` to trigger React Query refetches for relevant server data (e.g., `queryClient.invalidateQueries(['notifications'])` on `new_notification`). This is the preferred method for keeping server state consistent.
        - **Show Notifications:** Use `react-hot-toast` to display real-time notifications to the user if appropriate.

## 7. Global Error Handling

Combine React Query's error handling with Axios interceptors and potentially React Error Boundaries.

- **API Errors (4xx, 5xx, Network):** Handled primarily by the Axios response interceptor for global actions (logging, redirects for 401, generic toasts for 5xx/network). The rejected promise allows React Query/components to handle specific error states (`isError`, `error`) for displaying contextual messages in the UI.
- **Rendering Errors:** Use Next.js built-in error handling or implement custom React Error Boundaries (`error.tsx` files in App Router) to catch rendering errors within specific parts of the application and display fallback UI.
- **Mutation Errors:** `useMutation`'s `onError` callback can handle specific mutation failures (e.g., showing detailed form errors). Global `onError` in `QueryClient` defaults provides a fallback.
- **Toast Notifications:** Use `react-hot-toast` for non-blocking error feedback (e.g., background refresh failures, generic server errors).

## 8. Authentication Flow

- **Login:**
    - User submits credentials via a login form.
    - Call backend login endpoint (`POST /api/auth/login`).
    - On success: Backend sets HttpOnly session cookie. API response returns user data.
    - Frontend: Update `useAuthStore` with user data and `isAuthenticated: true`. Redirect to the dashboard.
- **Logout:**
    - User clicks logout button.
    - Call backend logout endpoint (`POST /api/auth/logout`). Backend clears session cookie.
    - Frontend: Update `useAuthStore` (clear user, `isAuthenticated: false`). Redirect to login page. Clear React Query cache (`queryClient.clear()`).
- **Session Check:**
    - On initial app load (potentially in `app/layout.tsx` or a root client component), make a request to a `/api/users/me` endpoint.
    - If successful (returns user data), hydrate `useAuthStore`.
    - If fails (401), ensure `useAuthStore` reflects logged-out state.
    - This ensures the frontend auth state is synchronized with the backend session on page loads/refreshes. Axios interceptor handles subsequent 401s.

## 9. Implementation Notes

- Ensure environment variables (`NEXT_PUBLIC_API_BASE_URL`, `NEXT_PUBLIC_WS_URL`) are configured correctly.
- Follow guidelines from `frontend_guidelines_document.mdc` and `cursor_project_rules.mdc`.
- Place providers (`ReactQueryProvider`, `AuthProvider` wrapping Zustand, `WebSocketProvider`) correctly in the component tree, likely in `app/layout.tsx`.
- Implement robust logging for state changes, API calls, WebSocket events, and errors.
