import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../lib/apiClient';

// Types for Number data
export interface PhoneNumber {
  id: string;
  number: string;
  countryCode: string;
  provider: string;
  capabilities: {
    voice: boolean;
    sms: boolean;
    mms: boolean;
  };
  status: 'active' | 'inactive' | 'porting';
  assignedAt: string;
  lastUsedAt?: string;
}

export interface NumberSearchParams {
  country?: string;
  areaCode?: string;
  pattern?: string;
  capabilities?: ('voice' | 'sms' | 'mms')[];
}

export interface PurchaseNumberParams {
  numberId: string;
  friendlyName?: string;
}

/**
 * Hook for managing phone numbers
 */
export function useNumbers() {
  const queryClient = useQueryClient();
  
  // Fetch owned numbers
  const ownedNumbersQuery = useQuery({
    queryKey: ['numbers', 'owned'],
    queryFn: () => api.get<PhoneNumber[]>('/numbers/owned'),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // Search available numbers
  const searchNumbers = (params: NumberSearchParams) => {
    return useQuery({
      queryKey: ['numbers', 'available', params],
      queryFn: () => api.get<PhoneNumber[]>('/numbers/available', params),
      enabled: !!(params.country || params.areaCode || params.pattern), // Only run if there are search params
      staleTime: 2 * 60 * 1000, // 2 minutes
    });
  };
  
  // Get number details
  const getNumberDetails = (numberId: string) => {
    return useQuery({
      queryKey: ['numbers', 'details', numberId],
      queryFn: () => api.get<PhoneNumber & { 
        usage: { calls: number, messages: number },
        configuration: any 
      }>(`/numbers/${numberId}`),
      enabled: !!numberId,
    });
  };
  
  // Purchase a new number
  const purchaseNumberMutation = useMutation({
    mutationFn: (params: PurchaseNumberParams) => {
      return api.post<{ success: boolean, number: PhoneNumber }>('/numbers/purchase', params);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['numbers', 'owned'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'summary'] }); // Update dashboard too
    },
  });
  
  // Release a number
  const releaseNumberMutation = useMutation({
    mutationFn: (numberId: string) => {
      return api.delete<{ success: boolean }>(`/numbers/${numberId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['numbers', 'owned'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'summary'] });
    },
  });
  
  // Update number configuration
  const updateNumberConfigMutation = useMutation({
    mutationFn: ({ numberId, config }: { numberId: string, config: any }) => {
      return api.put<{ success: boolean }>(`/numbers/${numberId}/configuration`, config);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['numbers', 'details', variables.numberId] });
    },
  });
  
  return {
    // Queries
    ownedNumbers: {
      data: ownedNumbersQuery.data,
      isLoading: ownedNumbersQuery.isLoading,
      error: ownedNumbersQuery.error,
    },
    searchNumbers,
    getNumberDetails,
    
    // Mutations
    purchaseNumber: purchaseNumberMutation.mutate,
    isPurchasing: purchaseNumberMutation.isPending,
    purchaseError: purchaseNumberMutation.error,
    
    releaseNumber: releaseNumberMutation.mutate,
    isReleasing: releaseNumberMutation.isPending,
    releaseError: releaseNumberMutation.error,
    
    updateNumberConfig: updateNumberConfigMutation.mutate,
    isUpdatingConfig: updateNumberConfigMutation.isPending,
    updateConfigError: updateNumberConfigMutation.error,
    
    // Refresh
    refreshOwnedNumbers: () => queryClient.invalidateQueries({ queryKey: ['numbers', 'owned'] }),
  };
}
