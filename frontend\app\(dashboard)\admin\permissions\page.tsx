'use client';

import React, { useState } from 'react';
import { useAuthStore } from '../../../../stores/authStore';
import PermissionGate from '../../../../components/PermissionGate';
import RoleManagement from '../../../../components/admin/RoleManagement';
import UserRoleManagement from '../../../../components/admin/UserRoleManagement';
import AdminPageLayout from '../../../../components/layouts/AdminPageLayout';

const PermissionsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'roles' | 'users'>('roles');
  const { hasPermission } = useAuthStore();

  // Check if user has admin permissions
  const hasAdminAccess = hasPermission('roles:read:any') || hasPermission('users:update:any');

  if (!hasAdminAccess) {
    return (
      <div className="flex flex-col items-center justify-center h-[70vh]">
        <div className="text-red-500 text-6xl mb-4">🔒</div>
        <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
        <p className="text-gray-600 mb-6">
          You do not have permission to access this page.
        </p>
      </div>
    );
  }

  return (
    <AdminPageLayout
      title="Permissions Management"
      description="Manage roles and user permissions"
    >
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'roles'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('roles')}
            >
              Role Management
            </button>
            <button
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'users'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('users')}
            >
              User Role Assignment
            </button>
          </nav>
        </div>
      </div>

      {activeTab === 'roles' && (
        <PermissionGate permission="roles:read:any">
          <RoleManagement />
        </PermissionGate>
      )}

      {activeTab === 'users' && (
        <PermissionGate permission="users:update:any">
          <UserRoleManagement />
        </PermissionGate>
      )}
    </AdminPageLayout>
  );
};

export default PermissionsPage;
