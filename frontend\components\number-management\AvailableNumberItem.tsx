'use client';

import { PhoneIcon, ShoppingCartIcon } from '@heroicons/react/24/outline';
import { AvailableNumber } from '../../hooks/useNumberManagement';

interface AvailableNumberItemProps {
  numberData: AvailableNumber;
  onPurchaseClick: () => void;
}

export default function AvailableNumberItem({
  numberData,
  onPurchaseClick
}: AvailableNumberItemProps) {
  const {
    id,
    phoneNumber,
    location,
    capabilities,
    type,
    cost
  } = numberData;

  // Format phone number for display
  const formatPhoneNumber = (phone: string) => {
    // This is a simple formatter, you might want to use a library like libphonenumber-js
    // for more sophisticated formatting based on country codes
    if (phone.startsWith('+1')) {
      // US format: +1 (XXX) XXX-XXXX
      const cleaned = phone.replace(/\D/g, '').substring(1); // Remove non-digits and the +1
      if (cleaned.length === 10) {
        return `+1 (${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6, 10)}`;
      }
    }
    return phone; // Return as-is if not US or not 10 digits
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
      {/* Header */}
      <div className="p-3 sm:p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <div className="p-1.5 sm:p-2 bg-blue-100 dark:bg-blue-900 rounded-full mr-2 sm:mr-3">
            <PhoneIcon className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="text-base sm:text-lg font-medium text-gray-900 dark:text-white">
              {formatPhoneNumber(phoneNumber)}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {location}
            </p>
          </div>
        </div>
      </div>

      {/* Body */}
      <div className="p-3 sm:p-4">
        <div className="grid grid-cols-2 gap-2 sm:gap-4 mb-3 sm:mb-4">
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Type</p>
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Setup Fee</p>
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              ${cost.setup.toFixed(2)}
            </p>
          </div>
        </div>

        <div className="mb-3 sm:mb-4">
          <p className="text-xs text-gray-500 dark:text-gray-400">Monthly Cost</p>
          <p className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
            ${cost.monthly.toFixed(2)}/month
          </p>
        </div>

        {/* Capabilities */}
        <div className="mb-3 sm:mb-4">
          <p className="text-xs text-gray-500 dark:text-gray-400 mb-1.5 sm:mb-2">Capabilities</p>
          <div className="flex flex-wrap gap-2">
            {capabilities.map((capability, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300"
              >
                {capability}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* Footer with purchase button */}
      <div className="p-3 sm:p-4 bg-gray-50 dark:bg-gray-750 border-t border-gray-200 dark:border-gray-700">
        <button
          type="button"
          onClick={onPurchaseClick}
          className="w-full inline-flex justify-center items-center px-3 sm:px-4 py-1.5 sm:py-2 border border-transparent rounded-md shadow-sm text-xs sm:text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <ShoppingCartIcon className="h-4 w-4 mr-2" />
          Purchase Number
        </button>
      </div>
    </div>
  );
}
