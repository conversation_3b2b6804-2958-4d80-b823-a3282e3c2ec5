import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export async function GET(request) {
  try {
    // Initialize Supabase client
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          get: (name) => cookieStore.get(name)?.value,
          set: (name, value, options) => {
            cookieStore.set({ name, value, ...options });
          },
          remove: (name, options) => {
            cookieStore.set({ name, value: '', ...options });
          },
        },
      }
    );

    // Get current session
    const { data: { session } } = await supabase.auth.getSession();
    
    // If no session, user is not authenticated
    if (!session) {
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 401 });
    }

    // Backend API URL
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3007';
    
    // Get the access token from the session
    const accessToken = session.access_token;
    
    // Fetch phone numbers from the backend
    const response = await fetch(`${apiUrl}/api/numbers`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      }
    });

    if (!response.ok) {
      // If response is not ok, throw an error with the response status
      const errorResponse = await response.json();
      return NextResponse.json({ 
        success: false, 
        message: errorResponse.message || 'Failed to fetch phone numbers' 
      }, { status: response.status });
    }

    // Parse response data
    const data = await response.json();
    
    // Return the phone numbers to the client
    return NextResponse.json({
      success: true,
      numbers: data.numbers || []
    });
  } catch (error) {
    console.error('Error fetching phone numbers:', error);
    
    // Return a generic error message to the client
    return NextResponse.json({ 
      success: false, 
      message: 'Failed to fetch phone numbers' 
    }, { status: 500 });
  }
} 