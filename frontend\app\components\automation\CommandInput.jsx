"use client";

import { useState, useRef, useEffect } from 'react';
import { <PERSON>Send, FiPhone, FiMessageSquare, FiInfo } from 'react-icons/fi';

const COMMAND_PATTERNS = [
  {
    id: 'call',
    icon: <FiPhone className="text-green-500" />,
    pattern: '/call +[number] Say: [message]',
    description: 'Make an outbound call with spoken message',
    example: '/call +491723773552 Say: Hi there, this is CallSaver!'
  },
  {
    id: 'sms',
    icon: <FiMessageSquare className="text-blue-500" />,
    pattern: '/sms +[number] [message]',
    description: 'Send an SMS message',
    example: '/sms +491723773552 Your appointment is confirmed for tomorrow.'
  },
  {
    id: 'help',
    icon: <FiInfo className="text-purple-500" />,
    pattern: '/help',
    description: 'Show available commands',
    example: '/help'
  }
];

export default function CommandInput({ onSendCommand, isProcessing }) {
  const [input, setInput] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [activeCommand, setActiveCommand] = useState(null);
  const inputRef = useRef(null);

  // Detect command type as user types
  useEffect(() => {
    const commandType = input.trim().startsWith('/call') 
      ? 'call' 
      : input.trim().startsWith('/sms')
        ? 'sms'
        : input.trim().startsWith('/help')
          ? 'help'
          : null;
    
    setActiveCommand(commandType);
    
    // Show suggestions when user starts typing a command
    if (input.startsWith('/') && input.length < 10) {
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  }, [input]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!input.trim() || isProcessing) return;
    
    onSendCommand(input);
    setInput('');
  };

  const handleKeyDown = (e) => {
    // Submit on Enter, unless Shift is pressed
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
    
    // Tab completion for command syntax
    if (e.key === 'Tab' && activeCommand) {
      e.preventDefault();
      const command = COMMAND_PATTERNS.find(c => c.id === activeCommand);
      if (command && command.id === 'call' && !input.includes('Say:')) {
        setInput(input + ' Say: ');
      }
    }
  };

  // Apply syntax highlighting to input
  const getHighlightedInput = () => {
    if (!activeCommand) return input;
    
    // For call command
    if (activeCommand === 'call') {
      // Highlight number and message parts
      const parts = input.match(/^(\/call\s+)(\+\d+)(\s+Say:\s+)(.*)$/);
      if (parts) {
        return (
          <>
            <span className="text-purple-500 font-semibold">{parts[1]}</span>
            <span className="text-green-500 font-semibold">{parts[2]}</span>
            <span className="text-purple-500 font-semibold">{parts[3]}</span>
            <span className="text-blue-500">{parts[4]}</span>
          </>
        );
      }
    }
    
    // For SMS command
    if (activeCommand === 'sms') {
      const parts = input.match(/^(\/sms\s+)(\+\d+)(\s+)(.*)$/);
      if (parts) {
        return (
          <>
            <span className="text-purple-500 font-semibold">{parts[1]}</span>
            <span className="text-green-500 font-semibold">{parts[2]}</span>
            <span>{parts[3]}</span>
            <span className="text-blue-500">{parts[4]}</span>
          </>
        );
      }
    }
    
    // For help or partial commands
    return <span className="text-purple-500 font-semibold">{input}</span>;
  };

  return (
    <div className="relative w-full">
      {/* Command suggestions dropdown */}
      {showSuggestions && (
        <div className="absolute bottom-full mb-2 w-full bg-gray-800 border border-gray-700 rounded-md shadow-lg p-2 z-10">
          <h4 className="text-xs text-gray-400 mb-2 px-2">Available commands:</h4>
          {COMMAND_PATTERNS.map(command => (
            <div 
              key={command.id}
              className={`flex items-center p-2 hover:bg-gray-700 rounded cursor-pointer ${activeCommand === command.id ? 'bg-gray-700' : ''}`}
              onClick={() => {
                setInput(command.id === 'help' ? '/help' : `/${command.id} `);
                inputRef.current.focus();
              }}
            >
              <div className="mr-2">{command.icon}</div>
              <div>
                <div className="text-sm font-medium text-white">{command.pattern}</div>
                <div className="text-xs text-gray-400">{command.description}</div>
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* Command input with visual display */}
      <form onSubmit={handleSubmit} className="flex items-center w-full">
        <div className="relative flex-1 bg-gray-800 rounded-l-md border-r-0 border border-gray-700">
          {/* Visual input with highlighting */}
          <div className="absolute inset-0 pointer-events-none p-3 overflow-hidden flex items-center">
            {getHighlightedInput()}
          </div>
          
          {/* Actual input field (invisible but functional) */}
          <input
            ref={inputRef}
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type a command or message (/call, /sms, /help)..."
            className="w-full bg-transparent text-transparent caret-white p-3 rounded-l-md focus:outline-none focus:ring-1 focus:ring-blue-500"
            disabled={isProcessing}
          />
          
          {/* Command icon indicator */}
          {activeCommand && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {COMMAND_PATTERNS.find(c => c.id === activeCommand)?.icon}
            </div>
          )}
        </div>
        
        <button
          type="submit"
          disabled={!input.trim() || isProcessing}
          className={`p-3 rounded-r-md ${
            isProcessing 
              ? 'bg-gray-700 text-gray-400' 
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          <FiSend className={isProcessing ? 'animate-pulse' : ''} />
        </button>
      </form>
      
      {/* Helper text */}
      <div className="text-xs text-gray-500 mt-1 ml-1">
        Press Tab for syntax completion, Enter to send
      </div>
    </div>
  );
} 