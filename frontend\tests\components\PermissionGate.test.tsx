import React from 'react';
import { render, screen } from '@testing-library/react';
import PermissionGate from '../../components/PermissionGate';
import { useAuthStore } from '../../stores/authStore';

// Mock dependencies
jest.mock('../../stores/authStore', () => ({
  useAuthStore: jest.fn()
}));

describe('PermissionGate', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock the auth store
    useAuthStore.mockImplementation(() => ({
      hasPermission: jest.fn().mockImplementation((permission) => {
        return permission === 'users:read:any' || permission === 'phoneNumbers:read:self';
      }),
      hasAnyPermission: jest.fn().mockImplementation((permissions) => {
        return permissions.includes('users:read:any') || permissions.includes('phoneNumbers:read:self');
      }),
      hasAllPermissions: jest.fn().mockImplementation((permissions) => {
        return permissions.every(p => p === 'users:read:any' || p === 'phoneNumbers:read:self');
      })
    }));
  });
  
  test('should render children if user has the permission', () => {
    render(
      <PermissionGate permission="users:read:any">
        <div data-testid="protected-content">Protected Content</div>
      </PermissionGate>
    );
    
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });
  
  test('should not render children if user does not have the permission', () => {
    render(
      <PermissionGate permission="users:delete:any">
        <div data-testid="protected-content">Protected Content</div>
      </PermissionGate>
    );
    
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
  });
  
  test('should render fallback if user does not have the permission', () => {
    render(
      <PermissionGate 
        permission="users:delete:any"
        fallback={<div data-testid="fallback-content">Access Denied</div>}
      >
        <div data-testid="protected-content">Protected Content</div>
      </PermissionGate>
    );
    
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
    expect(screen.getByText('Access Denied')).toBeInTheDocument();
  });
  
  test('should render children if user has any of the permissions', () => {
    render(
      <PermissionGate anyPermission={['users:read:any', 'users:delete:any']}>
        <div data-testid="protected-content">Protected Content</div>
      </PermissionGate>
    );
    
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
  });
  
  test('should not render children if user has none of the permissions', () => {
    render(
      <PermissionGate anyPermission={['users:delete:any', 'users:create:any']}>
        <div data-testid="protected-content">Protected Content</div>
      </PermissionGate>
    );
    
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
  });
  
  test('should render children if user has all of the permissions', () => {
    render(
      <PermissionGate allPermissions={['users:read:any', 'phoneNumbers:read:self']}>
        <div data-testid="protected-content">Protected Content</div>
      </PermissionGate>
    );
    
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
  });
  
  test('should not render children if user is missing any of the permissions', () => {
    render(
      <PermissionGate allPermissions={['users:read:any', 'users:delete:any']}>
        <div data-testid="protected-content">Protected Content</div>
      </PermissionGate>
    );
    
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
  });
  
  test('should render children if no permissions are specified', () => {
    render(
      <PermissionGate>
        <div data-testid="protected-content">Protected Content</div>
      </PermissionGate>
    );
    
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
  });
});
