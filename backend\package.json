{"name": "callsaver-backend-v2", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo \"No build step required\" && exit 0", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint .", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@prisma/client": "^6.5.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "twilio": "^5.5.1"}, "devDependencies": {"eslint": "^8.56.0", "nodemon": "^3.0.3", "prisma": "^6.5.0"}}