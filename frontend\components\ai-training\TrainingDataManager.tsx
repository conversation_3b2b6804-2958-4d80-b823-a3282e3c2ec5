'use client';

import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import FileUpload from './FileUpload';
import DataInputForm from './DataInputForm';
import TrainingDataTable from './TrainingDataTable';
import LoadingSpinner from '../shared/LoadingSpinner';
import ErrorMessage from '../shared/ErrorMessage';

export interface TrainingData {
  id: string;
  type: 'file' | 'text';
  preview: string;
  createdAt: string;
  name?: string;
  size?: number;
}

export default function TrainingDataManager() {
  const [activeView, setActiveView] = useState<'list' | 'upload' | 'input'>('list');
  const queryClient = useQueryClient();

  // Fetch training data
  const {
    data: trainingData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['ai', 'training-data'],
    queryFn: async () => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock data
        return [
          {
            id: '1',
            type: 'file',
            preview: 'FAQ document with 25 common customer questions and answers',
            createdAt: '2023-06-01T10:30:00Z',
            name: 'company_faq.pdf',
            size: 256000,
          },
          {
            id: '2',
            type: 'text',
            preview: 'When customers ask about pricing, explain that we offer three tiers: Basic ($9.99/mo), Pro ($19.99/mo), and Enterprise (custom pricing).',
            createdAt: '2023-06-05T14:45:00Z',
          },
          {
            id: '3',
            type: 'file',
            preview: 'Product catalog with detailed descriptions and specifications',
            createdAt: '2023-06-10T09:15:00Z',
            name: 'product_catalog.csv',
            size: 512000,
          },
        ] as TrainingData[];
      }
      
      // In production, fetch from API
      const { data } = await axios.get<TrainingData[]>('/api/ai/training-data');
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Upload training data mutation
  const uploadMutation = useMutation({
    mutationFn: async (data: { type: 'file' | 'text'; content: string | FormData }) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data: response } = await axios.post(
        '/api/ai/training-data',
        data.type === 'file' ? data.content : { type: data.type, content: data.content },
        data.type === 'file' ? { headers: { 'Content-Type': 'multipart/form-data' } } : {}
      );
      return response;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['ai', 'training-data'] });
      
      // Reset view to list
      setActiveView('list');
      
      // Show success toast (you can use a toast library like react-hot-toast)
      console.log('Training data uploaded successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to upload training data');
    },
  });

  // Delete training data mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.delete(`/api/ai/training-data/${id}`);
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['ai', 'training-data'] });
      
      // Show success toast
      console.log('Training data deleted successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to delete training data');
    },
  });

  // Handle file upload
  const handleFileUpload = (formData: FormData) => {
    uploadMutation.mutate({ type: 'file', content: formData });
  };

  // Handle text input
  const handleTextSubmit = (text: string) => {
    uploadMutation.mutate({ type: 'text', content: text });
  };

  // Handle delete
  const handleDelete = (id: string) => {
    deleteMutation.mutate(id);
  };

  // If loading, show loading spinner
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" color="blue" />
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <ErrorMessage
        title="Failed to load training data"
        message="We couldn't load your training data. Please try again later."
        error={error instanceof Error ? error : new Error('Unknown error')}
        onRetry={() => queryClient.invalidateQueries({ queryKey: ['ai', 'training-data'] })}
      />
    );
  }

  return (
    <div className="space-y-6">
      {activeView === 'list' && (
        <>
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Training Data
            </h2>
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={() => setActiveView('upload')}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
              >
                Upload File
              </button>
              <button
                type="button"
                onClick={() => setActiveView('input')}
                className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
              >
                Add Text
              </button>
            </div>
          </div>

          <TrainingDataTable
            data={trainingData || []}
            onDelete={handleDelete}
            isDeleting={deleteMutation.isPending}
          />
        </>
      )}

      {activeView === 'upload' && (
        <>
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Upload Training Data
            </h2>
            <button
              type="button"
              onClick={() => setActiveView('list')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
            >
              Back to List
            </button>
          </div>

          <FileUpload
            onUploadSuccess={handleFileUpload}
            isUploading={uploadMutation.isPending}
          />
        </>
      )}

      {activeView === 'input' && (
        <>
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">
              Add Training Text
            </h2>
            <button
              type="button"
              onClick={() => setActiveView('list')}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
            >
              Back to List
            </button>
          </div>

          <DataInputForm
            onSubmit={handleTextSubmit}
            isSubmitting={uploadMutation.isPending}
          />
        </>
      )}
    </div>
  );
}
