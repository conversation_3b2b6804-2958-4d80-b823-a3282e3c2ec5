---
description: 
globs: 
alwaysApply: false
---
# Appointment Scheduler Functional Document (`appointment_scheduler_document.mdc`)

## 1. Purpose and Scope

**Purpose:** Enable the AI assistant (and potentially users directly) to schedule appointments, primarily phone calls, based on predefined availability rules and potentially integrating with external calendars.

**Scope:**
- Allow users to define their general availability (days of the week, hours).
- Allow users to block specific time slots.
- Integrate with the AI assistant to handle scheduling requests received via calls or SMS (e.g., "Can I book a demo for next Tuesday?").
- (Future) Integrate with external calendars (Google Calendar, Outlook Calendar) to check for conflicts and sync appointments.
- Provide a calendar view within the dashboard to see scheduled appointments.
- Allow manual creation, editing, and deletion of appointments.
- Trigger reminders for upcoming appointments via the Notification Service.

## 2. User Interactions

- **Set Availability:** Use a UI (e.g., weekly grid with time slots) to define recurring available times and block out specific dates/times.
- **View Calendar:** See a calendar (day, week, month view) displaying scheduled appointments.
- **View Appointment Details:** Click on an appointment in the calendar to see details (attendee info, time, duration, notes, associated call log if applicable).
- **Manual Scheduling:** Create new appointments via a form, specifying attendee, date/time, duration, and notes.
- **Edit/Cancel Appointment:** Modify details or cancel existing appointments.
- **AI Interaction (Indirect):** Users configure the AI in the Automation section to enable the "schedule appointment" command. Callers/texters interact with the AI to request appointments.

## 3. Backend Integrations & Services Used

- **Appointment Scheduling Service:** Manages availability rules, stores appointment data, handles conflict checking, and potentially interfaces with external calendar APIs.
- **AI Service:**
    - Understands natural language requests for scheduling.
    - Queries the Appointment Scheduling Service for available slots.
    - Interacts with the user/caller to confirm details.
    - Instructs the Appointment Scheduling Service to create the appointment.
- **Database:** Stores user availability rules, appointment details (attendee, time, status, notes, associated AI interaction ID).
- **Notification Service:** Sends reminders for upcoming appointments and potentially confirmations upon booking.
- **User Service:** Associates appointments and availability with the correct user.
- **External Calendar APIs (Future):** (Google Calendar API, Microsoft Graph API) for syncing availability and appointments.

## 4. Necessary API Endpoints

- `GET /api/availability`: Fetches the user's current availability rules and blocked times.
- `PUT /api/availability`: Updates the user's availability rules.
- `POST /api/availability/block-time`: Adds a specific blocked time slot.
- `DELETE /api/availability/block-time/{blockId}`: Removes a blocked time slot.
- `GET /api/appointments?startDate=<date>&endDate=<date>`: Fetches appointments within a given date range for calendar display.
- `GET /api/appointments/{appointmentId}`: Fetches details of a specific appointment.
- `POST /api/appointments`: Creates a new appointment (used by manual scheduling and potentially by AI service after confirmation).
- `PUT /api/appointments/{appointmentId}`: Updates an existing appointment.
- `DELETE /api/appointments/{appointmentId}`: Cancels/deletes an appointment.
- **Internal Endpoints:**
    - `GET /internal/availability/check?startTime=<datetime>&endTime=<datetime>`: Checks if a specific time slot is free (used by AI Service).
    - `POST /internal/appointments/create-from-ai`: Endpoint specifically for the AI service to create an appointment after successful negotiation.

## 5. Expected Frontend Component Structure

```
/components
  /scheduler
    SchedulerLayout.tsx           # Main layout, potentially part of Settings or dedicated section
    AvailabilityEditor.tsx        # UI for setting recurring availability and blocking times
      WeeklyAvailabilityGrid.tsx  # Grid for setting hours per day
      BlockedTimeList.tsx         # List of specific blocked slots
    AppointmentCalendar.tsx       # Displays the calendar view (using a library like FullCalendar)
    AppointmentDetailsModal.tsx   # Modal to show/edit appointment details
    CreateAppointmentForm.tsx     # Form for manually creating appointments
    SchedulerSkeleton.tsx         # Loading state placeholder
```

## 6. Data Displayed

- **Availability Editor:** Weekly grid showing selectable time slots, list of specific dates/times blocked.
- **Calendar View:** Events representing scheduled appointments, potentially color-coded by status or type.
- **Appointment Details:** Attendee Name/Number, Date & Time, Duration, Notes, Status (Confirmed, Canceled), Link to AI interaction log (if scheduled by AI).

## 7. State and UI Behavior

- **Loading States:** Show skeletons while loading availability or appointments.
- **Calendar Interaction:** Allow switching between day/week/month views. Clicking dates or times might initiate manual scheduling. Clicking events opens the details modal.
- **Availability Editing:** Provide intuitive controls for selecting time ranges. Save changes with feedback.
- **Form Validation:** Validate appointment details (e.g., ensure end time is after start time, attendee info present).
- **Conflict Handling:** Prevent manual scheduling over existing appointments or blocked times (provide clear feedback). The backend must enforce this for AI scheduling as well.
- **Real-time Updates (Optional):** Calendar could potentially update via WebSockets if appointments are created/modified by the AI or another user session.

## 8. AI Integration

- **Core Functionality:** The AI Assistant uses this service to fulfill scheduling requests.
    - **NLU:** Understand requests like "Book a 30-minute call for next Tuesday morning".
    - **Availability Check:** Query the Appointment Scheduling Service for open slots matching the request.
    - **Disambiguation/Negotiation:** If multiple slots are available, offer choices. If the requested time is booked, suggest alternatives.
    - **Confirmation:** Confirm the chosen time with the caller/texter.
    - **Booking:** Call the internal API endpoint to create the appointment record in the database.
    - **Context Linking:** Store a reference to the AI interaction (call/message SID) with the appointment record.
- **Configuration:** Users enable the "appointment scheduling" capability for their AI assistant in the Automation section.

## 9. Error Handling Rules

- **Availability Conflicts:** Prevent booking appointments during unavailable times or over existing appointments. The AI should inform the caller if their requested time is unavailable and suggest alternatives. Manual scheduling UI should prevent selection or show an error.
- **Invalid Date/Time:** Handle invalid inputs in manual scheduling forms.
- **API Errors:** Display errors if fetching availability/appointments fails, or if creating/updating/deleting fails.
- **External Calendar Sync Errors (Future):** Log errors during sync attempts. Potentially notify the user if sync fails repeatedly. Provide options to re-authenticate or disable sync.
- **AI Scheduling Failures:** If the AI fails to book after confirming with the user (e.g., due to a race condition where the slot was just taken), it should apologize and potentially retry or offer alternatives. Log these failures.

## 10. Logging and Usage Tracking Expectations

- **Log:**
    - Availability rule changes (user, timestamp).
    - Appointment creation, updates, deletions (who performed the action - user or AI, appointment details).
    - AI scheduling attempts (requested time, suggested slots, final booked time, success/failure).
    - Errors during scheduling (conflicts, API errors, AI failures).
    - Reminder notifications sent.
    - (Future) External calendar sync events (start, success, failure, errors).
- **Track:**
    - Views of the scheduler/calendar section.
    - Frequency of availability updates.
    - Number of appointments scheduled (manual vs. AI).
    - AI scheduling success rate.
    - Most common days/times for appointments.
    - Cancellation rates.
    - Usage of external calendar integration features.
