# Redis Production Configuration Guide

This document outlines the recommended configuration for Redis in production environments for the CallSaver application, with a focus on persistence, security, high availability, and performance.

## Overview

In production, Redis requires careful configuration to ensure:
1. Data persistence to prevent data loss
2. Proper security measures to protect sensitive data
3. High availability to minimize downtime
4. Performance optimization to handle production loads

## Persistence Configuration

Redis offers two persistence mechanisms: RDB (Redis Database) snapshots and AOF (Append-Only File) logs. For CallSaver, we recommend using both for maximum data safety.

### Recommended Persistence Configuration

```
# RDB Persistence - Point-in-time snapshots
save 900 1      # Save if at least 1 key changed in 15 minutes
save 300 10     # Save if at least 10 keys changed in 5 minutes
save 60 10000   # Save if at least 10,000 keys changed in 1 minute

# RDB filename and location
dbfilename dump.rdb
dir /var/lib/redis/data

# RDB compression and checksums
rdbcompression yes
rdbchecksum yes

# AOF Persistence - Append-only file log
appendonly yes
appendfilename "appendonly.aof"

# AOF fsync policy (choose one):
# - always: Sync after every write (safest, slowest)
# - everysec: Sync once per second (good compromise)
# - no: Let OS decide when to sync (fastest, least safe)
appendfsync everysec

# AOF rewrite settings
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# When both AOF and RDB are enabled, Redis uses AOF for recovery
```

### Persistence Directory Setup

The persistence directory should be:
1. On a separate disk/volume from the OS
2. Backed up regularly
3. Monitored for disk space

```bash
# Create Redis data directory
sudo mkdir -p /var/lib/redis/data
sudo chown redis:redis /var/lib/redis/data
sudo chmod 770 /var/lib/redis/data
```

### Backup Strategy

1. **Automated RDB Backups**:
   ```bash
   # Example backup script (to be run via cron)
   #!/bin/bash
   TIMESTAMP=$(date +%Y%m%d%H%M%S)
   BACKUP_DIR="/backup/redis"
   mkdir -p $BACKUP_DIR
   cp /var/lib/redis/data/dump.rdb $BACKUP_DIR/dump-$TIMESTAMP.rdb
   # Keep only the last 7 days of backups
   find $BACKUP_DIR -name "dump-*.rdb" -mtime +7 -delete
   ```

2. **Remote Backup Storage**:
   ```bash
   # Example script to copy backups to remote storage (e.g., S3)
   aws s3 sync /backup/redis s3://callsaver-backups/redis/
   ```

## Security Configuration

Redis security is critical in production environments, especially when storing session data, rate limiting information, and task queue jobs.

### Network Security

```
# Bind to specific interfaces (never expose to public internet)
bind 127.0.0.1 ********  # Localhost and private network interface

# Disable RESP2 unauthed clients
protected-mode yes

# Disable potentially dangerous commands
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG ""
rename-command SHUTDOWN ""
```

### Authentication

```
# Set a strong password (min 30 chars, mixed case, numbers, special chars)
requirepass "YourVeryStrongPasswordHere1234!@#$%^&*()"

# If using Redis ACLs (Redis 6+)
user callsaver on >YourVeryStrongPasswordHere1234!@#$%^&*() ~* +@all -@dangerous
```

### TLS Configuration (Redis 6+)

```
# TLS configuration
tls-port 6380
tls-cert-file /path/to/redis.crt
tls-key-file /path/to/redis.key
tls-ca-cert-file /path/to/ca.crt
tls-auth-clients yes
tls-replication yes
tls-cluster yes
```

## High Availability Configuration

For production, we recommend using Redis Sentinel for automatic failover.

### Redis Sentinel Setup

1. **Main Redis Configuration**:
   ```
   # In addition to persistence and security settings
   replica-serve-stale-data yes
   replica-read-only yes
   repl-diskless-sync yes
   repl-diskless-sync-delay 5
   repl-timeout 60
   ```

2. **Sentinel Configuration** (sentinel.conf):
   ```
   port 26379
   sentinel monitor callsaver-master ******** 6379 2
   sentinel down-after-milliseconds callsaver-master 5000
   sentinel failover-timeout callsaver-master 60000
   sentinel auth-pass callsaver-master YourVeryStrongPasswordHere1234!@#$%^&*()
   ```

3. **Minimum Setup**:
   - 1 Redis master
   - 2 Redis replicas
   - 3 Sentinel instances (on separate hosts)

### Client Configuration for Sentinel

Update the Redis client configuration in the application:

```javascript
// In back/backend/config/index.js
module.exports = {
  // ... other config
  redis: {
    // For production with Sentinel
    production: {
      sentinels: [
        { host: '********', port: 26379 },
        { host: '********', port: 26379 },
        { host: '********', port: 26379 }
      ],
      name: 'callsaver-master', // Sentinel master name
      password: process.env.REDIS_PASSWORD,
      db: 0,
      prefix: process.env.REDIS_PREFIX || 'callsaver:prod:'
    }
  }
};
```

## Performance Optimization

### Memory Management

```
# Maximum memory Redis can use
maxmemory 4gb  # Adjust based on available RAM

# Eviction policy when memory limit is reached
# Options:
# - noeviction: Return errors when memory limit is reached
# - allkeys-lru: Evict least recently used keys
# - volatile-lru: Evict least recently used keys with expiration
# - allkeys-random: Evict random keys
# - volatile-random: Evict random keys with expiration
# - volatile-ttl: Evict keys with shortest TTL
maxmemory-policy volatile-lru

# LRU and minimal TTL algorithm precision
maxmemory-samples 5
```

### Connection Settings

```
# Maximum number of clients
maxclients 10000

# TCP keepalive
tcp-keepalive 300

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
```

### Latency Monitoring

```
# Latency monitoring
latency-monitor-threshold 100  # milliseconds
```

## Monitoring and Alerting

### Key Metrics to Monitor

1. **Memory Usage**:
   - `used_memory_rss`
   - `used_memory_peak`
   - Alert when approaching maxmemory (e.g., 80%)

2. **Performance**:
   - `instantaneous_ops_per_sec`
   - `rejected_connections`
   - Latency metrics

3. **Persistence**:
   - Last RDB save time
   - AOF rewrite in progress
   - Disk usage for Redis data directory

4. **Replication** (if using Sentinel):
   - Replica lag
   - Replication status

### Prometheus Metrics

We recommend using the Redis Prometheus exporter for monitoring:

```bash
# Install Redis Prometheus exporter
docker run -d --name redis-exporter \
  -p 9121:9121 \
  -e REDIS_ADDR=redis://********:6379 \
  -e REDIS_PASSWORD=YourVeryStrongPasswordHere1234!@#$%^&*() \
  oliver006/redis_exporter
```

### Grafana Dashboard

Create a Grafana dashboard with the following panels:
1. Memory usage
2. Operations per second
3. Connected clients
4. Hit/miss ratio for cache operations
5. Command execution time
6. Network traffic
7. Persistence status

## Deployment Considerations

### Containerized Deployment

For containerized environments (Docker/Kubernetes):

```yaml
# Example Docker Compose configuration
version: '3'
services:
  redis:
    image: redis:7
    command: redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf
      - redis-data:/data
    ports:
      - "6379:6379"
    restart: always
    deploy:
      resources:
        limits:
          memory: 5G
        reservations:
          memory: 4G

volumes:
  redis-data:
```

### Kubernetes StatefulSet

For Kubernetes deployments, use a StatefulSet with persistent volumes:

```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis
spec:
  serviceName: "redis"
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7
        command:
          - redis-server
          - "/etc/redis/redis.conf"
        ports:
        - containerPort: 6379
          name: redis
        volumeMounts:
        - name: redis-config
          mountPath: /etc/redis
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-config
        configMap:
          name: redis-config
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 10Gi
```

## Implementation Plan

### Phase 1: Basic Production Setup
1. Configure Redis with persistence (RDB + AOF)
2. Implement security measures (authentication, protected mode)
3. Set up automated backups
4. Configure memory limits and eviction policy

### Phase 2: High Availability
1. Set up Redis replicas
2. Configure Redis Sentinel
3. Update application to use Sentinel-aware Redis client
4. Test failover scenarios

### Phase 3: Monitoring and Optimization
1. Set up Redis Prometheus exporter
2. Create Grafana dashboards
3. Configure alerts for critical metrics
4. Fine-tune Redis configuration based on actual usage patterns

## Conclusion

This production configuration ensures that Redis is properly configured for the CallSaver application's needs in terms of persistence, security, high availability, and performance. The configuration should be adjusted based on specific infrastructure requirements and load patterns observed in production.

## References

- [Redis Persistence Documentation](https://redis.io/topics/persistence)
- [Redis Security Documentation](https://redis.io/topics/security)
- [Redis Sentinel Documentation](https://redis.io/topics/sentinel)
- [Redis Administration](https://redis.io/topics/admin)
