"use client";

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useAuthStore } from '../stores/authStore';
import { useUIStore } from '../stores/uiStore';
import { queryClient } from './QueryProvider';

// Define the WebSocket context type
interface WebSocketContextType {
  isConnected: boolean;
  lastPong: Date | null;
  send: (data: any) => void;
}

// Create the context with default values
const WebSocketContext = createContext<WebSocketContextType>({
  isConnected: false,
  lastPong: null,
  send: () => {}, // Empty function as placeholder
});

interface WebSocketProviderProps {
  children: React.ReactNode;
}

/**
 * WebSocket Provider
 * Manages WebSocket connection and message handling
 */
export function WebSocketProvider({ children }: WebSocketProviderProps) {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [lastPong, setLastPong] = useState<Date | null>(null);
  const [reconnectAttempt, setReconnectAttempt] = useState(0);
  
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);
  const user = useAuthStore(state => state.user);
  const incrementUnreadNotifications = useUIStore(state => state.incrementUnreadNotificationsCount);
  
  // Connection and reconnection logic
  useEffect(() => {
    // Only connect if user is authenticated
    if (!isAuthenticated || !user) {
      if (socket) {
        socket.close();
        setSocket(null);
        setIsConnected(false);
      }
      return;
    }
    
    // WebSocket server URL from environment
    const wsUrl = process.env.NEXT_PUBLIC_WS_URL || `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`;
    
    // Initialize WebSocket
    const ws = new WebSocket(`${wsUrl}?userId=${user.id}`);
    
    // Set up WebSocket event handlers
    ws.onopen = () => {
      console.log('WebSocket connected');
      setIsConnected(true);
      setReconnectAttempt(0); // Reset reconnect attempts
      
      // Send an initial authentication message
      ws.send(JSON.stringify({
        type: 'auth',
        userId: user.id,
      }));
    };
    
    ws.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason);
      setIsConnected(false);
      
      // Implement reconnection logic with exponential backoff
      if (isAuthenticated) {
        const backoffTime = Math.min(1000 * Math.pow(2, reconnectAttempt), 30000); // Max 30 seconds
        console.log(`Reconnecting in ${backoffTime}ms (attempt ${reconnectAttempt + 1})`);
        
        setTimeout(() => {
          setReconnectAttempt(prev => prev + 1);
        }, backoffTime);
      }
    };
    
    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
    
    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        handleMessage(message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };
    
    // Store the WebSocket instance
    setSocket(ws);
    
    // Clean up on unmount
    return () => {
      ws.close();
    };
  }, [isAuthenticated, user, reconnectAttempt]);
  
  // Handle different types of WebSocket messages
  const handleMessage = (message: any) => {
    console.log('WebSocket message received:', message);
    
    // Handle different message types
    switch (message.type) {
      case 'pong':
        setLastPong(new Date());
        break;
      
      case 'notification':
        // When a new notification arrives
        incrementUnreadNotifications();
        // Invalidate notifications query to refetch
        queryClient.invalidateQueries({ queryKey: ['notifications'] });
        break;
      
      case 'call_status_updated':
        // Invalidate call logs to refresh the list
        queryClient.invalidateQueries({ queryKey: ['call-logs'] });
        break;
      
      case 'credit_updated':
        // Update credit balance in UI
        queryClient.invalidateQueries({ queryKey: ['credits'] });
        break;
      
      case 'new_number_assigned':
        // Refresh phone numbers list
        queryClient.invalidateQueries({ queryKey: ['numbers'] });
        break;
      
      default:
        console.log('Unhandled WebSocket message type:', message.type);
    }
  };
  
  // Function to send a message through the WebSocket
  const send = (data: any) => {
    if (socket && isConnected) {
      socket.send(JSON.stringify(data));
    } else {
      console.warn('Cannot send message: WebSocket is not connected');
    }
  };
  
  // Keep WebSocket alive with ping/pong (if server supports it)
  useEffect(() => {
    if (!isConnected) return;
    
    const pingInterval = setInterval(() => {
      if (socket && socket.readyState === WebSocket.OPEN) {
        send({ type: 'ping' });
      }
    }, 30000); // Send ping every 30 seconds
    
    return () => {
      clearInterval(pingInterval);
    };
  }, [socket, isConnected]);
  
  // Context value
  const value = {
    isConnected,
    lastPong,
    send,
  };
  
  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
}

// Custom hook to use the WebSocket context
export const useWebSocket = () => useContext(WebSocketContext);
