---
description: 
globs: 
alwaysApply: false
---
# Notifications and Alerts Functional Document (`notifications_and_alerts_document.mdc`)

## 1. Purpose and Scope

**Purpose:** Establish a centralized system for generating, delivering, and managing user-facing notifications and alerts within the CallSaver platform. Ensure users are informed about important events, status changes, and required actions across various communication channels.

**Scope:**
- Define various notification types (e.g., New Voicemail, Missed Call, Low Credits, AI Alert, Appointment Reminder, System Announcement, Security Alert, eSIM Expiry Warning).
- Support multiple delivery channels:
    - In-App Notification Center/Feed.
    - Email Notifications.
    - (Future) SMS Notifications.
    - (Future) Push Notifications (for potential mobile app).
- Allow users to configure their notification preferences (handled in Settings, but referenced here).
- Provide an interface (e.g., a dropdown panel or dedicated page) to view recent notifications.
- Manage notification states (read/unread).
- Enable dismissal of notifications.
- Integrate with various backend services that trigger notifications.

## 2. User Interactions

- **View Notifications:** Access a notification center (e.g., bell icon in header) to see a list of recent notifications.
- **Mark as Read:** Notifications marked as read automatically upon opening the center, or individually by clicking.
- **Dismiss Notification:** Option to remove a notification from the visible list (individually or "Dismiss All").
- **Navigate from Notification:** Clicking a notification navigates the user to the relevant context (e.g., clicking a "New Voicemail" notification goes to that specific call log entry).
- **Manage Preferences:** Link from the notification center to the Notification Preferences section in Settings.

## 3. Backend Integrations & Services Used

- **Notification Service:** Central service responsible for:
    - Receiving notification requests from other services.
    - Checking user preferences for the specific notification type and channel.
    - Formatting notification content.
    - Dispatching notifications via appropriate channels (Email, WebSockets for In-App, SMS/Push providers).
    - Storing notification history for the in-app center.
    - Managing read/unread status.
- **Triggering Services:**
    - **Call Logging Service:** Triggers notifications for missed calls, new voicemails.
    - **AI Service:** Triggers alerts for high sentiment calls, detected spam, command failures, custom AI alerts.
    - **Billing Service:** Triggers low credit warnings, payment failures, subscription updates.
    - **Number Management/eSIM Service:** Triggers alerts for number/eSIM expiry, activation success/failure.
    - **Appointment Scheduler Service:** Triggers appointment reminders.
    - **User Service/Auth:** Triggers security alerts (e.g., new login detected).
    - **Admin/System:** Can trigger system-wide announcements.
- **Database:** Stores notification history, user preferences (or references User Service), templates.
- **Email Service (e.g., SendGrid, Mailgun):** Sends email notifications.
- **WebSocket Service:** Pushes real-time notifications to the frontend for the in-app center.
- **SMS/Push Notification Providers (Future):** (e.g., Twilio Notify, Firebase Cloud Messaging).

## 4. Necessary API Endpoints

- `GET /api/notifications?page=1&limit=15&status=unread|all`: Fetches notifications for the in-app center.
- `POST /api/notifications/mark-read`: Marks specific notifications or all notifications as read.
- `POST /api/notifications/dismiss`: Marks specific notifications as dismissed (removes from view, distinct from read).
- `GET /api/notifications/unread-count`: Fetches the count of unread notifications (for badge display).
- **Internal Endpoints (Service-to-Service):**
    - `POST /internal/notifications/send`: Generic endpoint for other services to trigger a notification (payload includes user ID, type, data, urgency).

## 5. Expected Frontend Component Structure

```
/components
  /notifications
    NotificationCenterTrigger.tsx # Bell icon in header, displays unread count badge
    NotificationCenterPanel.tsx   # Dropdown/Panel displaying the list of notifications
      NotificationList.tsx        # Renders the list
        NotificationItem.tsx      # Represents a single notification entry
      NotificationActions.tsx     # Buttons for "Mark All Read", "Dismiss All", "Settings"
    NotificationSkeleton.tsx    # Loading state placeholder
```

## 6. Data Displayed

- **Notification Item:** Icon representing type, Notification message (concise), Timestamp (e.g., "5m ago", "yesterday"), Read/Unread indicator, Link to relevant context, Dismiss button (optional).
- **Unread Count Badge:** Number displayed on the notification trigger icon.

## 7. State and UI Behavior

- **Real-time Updates:** Unread count badge and notification list update in real-time via WebSockets when new notifications arrive.
- **Unread State:** Unread notifications are visually distinct (e.g., different background color, bold text).
- **Marking Read:** Opening the panel might automatically mark all visible notifications as read, or require explicit action. Update API and UI state accordingly.
- **Dismissal:** Dismissing removes the notification from the list view (API call to update status).
- **Empty State:** Display a message like "No new notifications" when the list is empty.
- **Loading State:** Show skeleton loaders when opening the panel for the first time or fetching older notifications.
- **Navigation:** Clicking an item navigates the user, potentially closing the panel.

## 8. AI Integration

- **AI-Triggered Alerts:** The AI Service is a primary source of intelligent alerts:
    - High/Negative Sentiment Detected in call/message.
    - Potential Spam Call Identified.
    - AI Command Execution Failed (with reason).
    - Custom alerts defined in Automation rules (e.g., "Alert me if a call mentions 'urgent order'").
    - Predictive Alerts (Future): e.g., "Based on usage, your credits may run out in 3 days." or "Your eSIM data is projected to be depleted before expiry."
- **Notification Content:** AI could potentially summarize information for the notification message itself (e.g., "AI summarized a voicemail from +123... regarding 'Project X'").

## 9. Error Handling Rules

- **Fetching Errors:** If notifications fail to load for the in-app center, show an error message within the panel.
- **Marking Read/Dismiss Errors:** Provide subtle feedback (e.g., toast notification) if marking read or dismissing fails on the backend. The UI might revert the state optimistically.
- **Delivery Failures (Backend):** The Notification Service should log failures to deliver via specific channels (e.g., email bounce, push token invalid). Implement retry logic where appropriate.
- **WebSocket Disconnection:** Handle WebSocket disconnections gracefully; attempt reconnection. The unread count might become stale until reconnected.

## 10. Logging and Usage Tracking Expectations

- **Log:**
    - Every notification generation request received by the Notification Service (source service, type, user).
    - Every notification dispatch attempt (channel, success/failure, reason for failure).
    - User marking notifications as read/dismissed.
    - Errors during notification fetching or state updates.
- **Track:**
    - Opening rate of the notification center.
    - Click-through rate on notifications (which types are most engaging).
    - Usage of "Mark All Read" / "Dismiss All".
    - User navigation to notification preferences.
    - Volume of notifications generated by type and source service.
    - Delivery success/failure rates per channel.
