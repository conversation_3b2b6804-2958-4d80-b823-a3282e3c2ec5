'use client';

import { useState } from 'react';
import { useScheduledAutomation, AutomationTemplate } from '../../../hooks/useScheduledAutomation';
import LoadingSpinner from '../../shared/LoadingSpinner';
import ErrorMessage from '../../shared/ErrorMessage';
import { MagnifyingGlassIcon, DocumentTextIcon } from '@heroicons/react/24/outline';

interface TemplateSelectorProps {
  templateType: 'call' | 'sms';
  selectedTemplateId: string;
  onChange: (templateId: string) => void;
}

export default function TemplateSelector({
  templateType,
  selectedTemplateId,
  onChange,
}: TemplateSelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  
  const { getTemplates } = useScheduledAutomation();
  const { data: templates, isLoading, error } = getTemplates(templateType);
  
  // Filter templates based on search term
  const filteredTemplates = templates?.filter(template => 
    template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.preview.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Select Template</h2>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
          Choose a template for your {templateType === 'call' ? 'call script' : 'SMS message'}.
        </p>
      </div>

      {/* Search input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          placeholder={`Search ${templateType === 'call' ? 'call' : 'SMS'} templates...`}
        />
      </div>

      {/* Templates list */}
      <div className="mt-4">
        {isLoading ? (
          <div className="flex justify-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        ) : error ? (
          <ErrorMessage message="Failed to load templates. Please try again later." />
        ) : filteredTemplates && filteredTemplates.length > 0 ? (
          <div className="grid grid-cols-1 gap-4">
            {filteredTemplates.map((template) => (
              <div
                key={template.id}
                onClick={() => onChange(template.id)}
                className={`p-4 border rounded-lg cursor-pointer ${
                  selectedTemplateId === template.id
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <div className="flex items-start">
                  <div className={`p-2 rounded-full ${
                    selectedTemplateId === template.id
                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
                  }`}>
                    <DocumentTextIcon className="h-5 w-5" />
                  </div>
                  <div className="ml-3">
                    <h3 className={`font-medium ${
                      selectedTemplateId === template.id
                        ? 'text-blue-700 dark:text-blue-400'
                        : 'text-gray-900 dark:text-white'
                    }`}>
                      {template.name}
                    </h3>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      {template.preview}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center p-8 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
            <DocumentTextIcon className="h-12 w-12 mx-auto text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No templates found</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {searchTerm
                ? `No ${templateType} templates match your search.`
                : `No ${templateType} templates available. Create a template first.`}
            </p>
            <div className="mt-6">
              <a
                href="/dashboard/templates"
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Create Template
              </a>
            </div>
          </div>
        )}
      </div>

      {/* Template management link */}
      <div className="mt-4 text-center">
        <a
          href="/dashboard/templates"
          className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
        >
          Manage Templates
        </a>
      </div>
    </div>
  );
}
