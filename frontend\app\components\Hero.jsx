"use client";

import Link from 'next/link';
import FallingIcons from './FallingIcons';
import { motion } from 'framer-motion';
import { useLanguage } from '../i18n/LanguageContext';
import { useEffect, useState, useRef } from 'react';
import CTAButton from './CTAButton';

export default function Hero() {
  const { t, isRTL } = useLanguage();
  const particlesRef = useRef(null);
  const [particles, setParticles] = useState([]);
  
  // Generate random floating particles
  useEffect(() => {
    const generateParticles = () => {
      const newParticles = [];
      // Use window to ensure this only runs on client
      if (typeof window !== 'undefined') {
        const count = Math.min(10, window.innerWidth / 120);
        
        for (let i = 0; i < count; i++) {
          newParticles.push({
            id: i,
            size: Math.random() * 4 + 2,
            x: Math.random() * 100,
            y: Math.random() * 100,
            opacity: Math.random() * 0.2 + 0.05,
            duration: Math.random() * 20 + 10,
            delay: Math.random() * 5,
            x1: (Math.random() - 0.5) * 80,
            y1: (Math.random() - 0.5) * 80,
            x2: (Math.random() - 0.5) * 80,
            y2: (Math.random() - 0.5) * 80,
            x3: (Math.random() - 0.5) * 80,
            y3: (Math.random() - 0.5) * 80,
            color: Math.random() > 0.5 ? 'purple' : (Math.random() > 0.5 ? 'pink' : 'blue')
          });
        }
      }
      
      setParticles(newParticles);
    };
    
    generateParticles();
    
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', generateParticles);
      
      return () => {
        window.removeEventListener('resize', generateParticles);
      };
    }
  }, []);
  
  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };
  
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const navHeight = 80;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - navHeight;
      
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className={`min-h-screen flex flex-col relative overflow-hidden ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Falling icons background */}
      <FallingIcons />
      
      <motion.div 
        className="flex-1 flex flex-col items-center justify-center px-4 md:px-8 text-center py-16 md:py-20 relative z-10"
        initial="hidden"
        animate="visible"
        variants={staggerContainer}
      >
        <motion.h1 
          className="heading-xl neon-text-outline high-contrast-text max-w-6xl mx-auto"
          variants={fadeInUp}
        >
          <span className="bg-clip-text bg-gradient-to-r from-purple-400 via-purple-200 to-white text-transparent inline-block">
            {t('hero.title.line1')}
          </span>
          <br />
          <span className="bg-clip-text bg-gradient-to-r from-white via-purple-200 to-purple-400 text-transparent inline-block">
            {t('hero.title.line2')}
          </span>
        </motion.h1>
        
        <motion.p 
          className="text-lg md:text-xl subheading-text mb-10 max-w-3xl mx-auto leading-relaxed"
          variants={fadeInUp}
        >
          {t('hero.subtitle')}
        </motion.p>
        
        <motion.div
          className="flex flex-col sm:flex-row justify-center gap-4 sm:gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <button 
            onClick={() => scrollToSection('pricing')}
            className="inline-flex items-center justify-center px-8 py-3 text-base font-medium rounded-full text-white bg-purple-600 hover:bg-purple-700 transition duration-300 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 shadow-lg hover:shadow-xl"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
            </svg>
            {t('hero.buttons.trial')}
          </button>
        </motion.div>
        
        <motion.div 
          className={`flex flex-col md:flex-row items-center justify-center gap-4 md:gap-6 mt-4`}
          variants={fadeInUp}
        >
          <motion.div 
            className="bg-gray-800/60 backdrop-blur-sm px-5 py-2 rounded-full flex items-center ai-badge neon-border"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <span className="text-purple-300 mr-2">{t('hero.footer.poweredBy')}</span>
            <motion.span 
              className="font-bold bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400 text-transparent neon-text-outline"
              animate={{ 
                backgroundPosition: ["0% center", "100% center", "0% center"]
              }}
              transition={{ 
                duration: 3,
                repeat: Infinity,
                repeatType: "reverse"
              }}
            >
              AI
            </motion.span>
          </motion.div>
          
          <motion.div 
            className="flex items-center"
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <div className="flex -space-x-1 mr-3 dots-container">
              <motion.div 
                className="w-3 h-3 rounded-full dot-pink dot neon-icon-pulse"
                whileHover={{ scale: 1.5 }}
                animate={{
                  boxShadow: ["0 0 0px rgba(236, 72, 153, 0)", "0 0 8px rgba(236, 72, 153, 0.5)", "0 0 0px rgba(236, 72, 153, 0)"]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <motion.div 
                className="w-3 h-3 rounded-full dot-purple dot neon-icon-pulse"
                whileHover={{ scale: 1.5 }}
                animate={{
                  boxShadow: ["0 0 0px rgba(139, 92, 246, 0)", "0 0 8px rgba(139, 92, 246, 0.5)", "0 0 0px rgba(139, 92, 246, 0)"]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.3
                }}
              />
              <motion.div 
                className="w-3 h-3 rounded-full dot-blue dot neon-icon-pulse"
                whileHover={{ scale: 1.5 }}
                animate={{
                  boxShadow: ["0 0 0px rgba(96, 165, 250, 0)", "0 0 8px rgba(96, 165, 250, 0.5)", "0 0 0px rgba(96, 165, 250, 0)"]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.6
                }}
              />
            </div>
            <span className="high-contrast-text">{t('hero.footer.businesses')}</span>
          </motion.div>
        </motion.div>
      </motion.div>
    </div>
  );
} 