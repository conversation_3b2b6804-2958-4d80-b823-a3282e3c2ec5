'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs';

export default function DeveloperDashboard() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [apiUsage, setApiUsage] = useState({
    totalRequests: 0,
    successRate: 0,
    avgResponseTime: 0,
    requestsByEndpoint: []
  });

  useEffect(() => {
    const fetchApiUsage = async () => {
      try {
        setIsLoading(true);
        // In a real implementation, this would be an API call to get API usage stats
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        setApiUsage({
          totalRequests: 12345,
          successRate: 99.7,
          avgResponseTime: 120, // ms
          requestsByEndpoint: [
            { endpoint: '/api/calls', count: 4567, avgResponseTime: 115 },
            { endpoint: '/api/messages', count: 3456, avgResponseTime: 95 },
            { endpoint: '/api/numbers', count: 2345, avgResponseTime: 105 },
            { endpoint: '/api/users', count: 1234, avgResponseTime: 130 },
            { endpoint: '/api/voicemails', count: 743, avgResponseTime: 150 }
          ]
        });
      } catch (err) {
        console.error('Error fetching API usage:', err);
        setError('Failed to load developer dashboard data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchApiUsage();
  }, []);

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold text-white mb-6">Developer Dashboard</h1>
      
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
          <p className="text-red-200">{error}</p>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white">Total API Requests</CardTitle>
            <CardDescription className="text-gray-400">Last 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-white">{isLoading ? '...' : apiUsage.totalRequests.toLocaleString()}</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white">Success Rate</CardTitle>
            <CardDescription className="text-gray-400">Last 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-white">{isLoading ? '...' : `${apiUsage.successRate}%`}</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white">Avg Response Time</CardTitle>
            <CardDescription className="text-gray-400">Last 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-white">{isLoading ? '...' : `${apiUsage.avgResponseTime} ms`}</p>
          </CardContent>
        </Card>
      </div>
      
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="bg-gray-800/70 border border-purple-500/20 mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
          <TabsTrigger value="documentation">Documentation</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">API Usage Overview</h2>
          <p className="text-gray-300 mb-6">Monitor your API usage and performance metrics.</p>
          
          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead className="bg-gray-700/50 text-gray-300">
                <tr>
                  <th className="px-4 py-2 rounded-tl-lg">Endpoint</th>
                  <th className="px-4 py-2">Requests</th>
                  <th className="px-4 py-2 rounded-tr-lg">Avg Response Time</th>
                </tr>
              </thead>
              <tbody className="text-gray-300">
                {isLoading ? (
                  <tr>
                    <td colSpan="3" className="px-4 py-2 text-center">Loading API usage data...</td>
                  </tr>
                ) : (
                  apiUsage.requestsByEndpoint.map((endpoint, index) => (
                    <tr key={index} className={index % 2 === 0 ? 'bg-gray-700/30' : 'bg-gray-700/10'}>
                      <td className="px-4 py-2 font-mono text-sm">{endpoint.endpoint}</td>
                      <td className="px-4 py-2">{endpoint.count.toLocaleString()}</td>
                      <td className="px-4 py-2">{endpoint.avgResponseTime} ms</td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          
          <div className="mt-8">
            <h3 className="text-lg font-semibold text-white mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <a href="/dashboard/developer/api-keys" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
                Manage API Keys
              </a>
              <a href="/dashboard/developer/webhooks" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
                Configure Webhooks
              </a>
              <a href="/docs/api" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
                View API Documentation
              </a>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="api-keys" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">API Keys</h2>
          <p className="text-gray-300 mb-4">For detailed API key management, please visit the <a href="/dashboard/developer/api-keys" className="text-purple-400 hover:text-purple-300">API Keys</a> page.</p>
        </TabsContent>
        
        <TabsContent value="webhooks" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">Webhooks</h2>
          <p className="text-gray-300">Configure webhooks to receive real-time notifications for events in your account.</p>
        </TabsContent>
        
        <TabsContent value="documentation" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">API Documentation</h2>
          <p className="text-gray-300">Access comprehensive documentation for the CallSaver API.</p>
          
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">REST API Reference</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">Complete reference for all REST API endpoints, parameters, and responses.</p>
                <a href="/docs/api/rest" className="text-purple-400 hover:text-purple-300">View Documentation →</a>
              </CardContent>
            </Card>
            
            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">SDK Documentation</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300 mb-4">Guides and reference for our client SDKs in various programming languages.</p>
                <a href="/docs/api/sdk" className="text-purple-400 hover:text-purple-300">View Documentation →</a>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
