'use client';

import React from 'react';
import Link from 'next/link';
import { usePermissions } from '../../hooks/usePermissions';
import PermissionTooltip from './PermissionTooltip';

interface PermissionAwareLinkProps {
  href: string;
  permission?: string;
  anyPermission?: string[];
  allPermissions?: string[];
  resource?: string;
  action?: string;
  scope?: string;
  children: React.ReactNode;
  className?: string;
  activeClassName?: string;
  isActive?: boolean;
  onClick?: () => void;
  showTooltipWhenNoAccess?: boolean;
  tooltipMessage?: string;
  tooltipPosition?: 'top' | 'bottom' | 'left' | 'right';
  renderDisabledWhenNoAccess?: boolean;
  disabledClassName?: string;
  [key: string]: any; // For any other props that might be passed
}

/**
 * A link component that is only rendered if the user has the required permissions
 */
const PermissionAwareLink: React.FC<PermissionAwareLinkProps> = ({
  href,
  permission,
  anyPermission,
  allPermissions,
  resource,
  action = 'read',
  scope = 'any',
  children,
  className = '',
  activeClassName = '',
  isActive = false,
  onClick,
  showTooltipWhenNoAccess = false,
  tooltipMessage = 'You do not have permission to access this page',
  tooltipPosition = 'top',
  renderDisabledWhenNoAccess = false,
  disabledClassName = 'opacity-50 pointer-events-none cursor-not-allowed',
  ...rest
}) => {
  const {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccess
  } = usePermissions();

  // Check if the user has the required permissions
  let hasAccess = false;

  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (anyPermission) {
    hasAccess = hasAnyPermission(anyPermission);
  } else if (allPermissions) {
    hasAccess = hasAllPermissions(allPermissions);
  } else if (resource) {
    hasAccess = canAccess(resource, action, scope);
  } else {
    // If no permissions are specified, allow access
    hasAccess = true;
  }

  // If the user doesn't have access and we're not rendering a disabled link, don't render anything
  if (!hasAccess && !renderDisabledWhenNoAccess && !showTooltipWhenNoAccess) {
    return null;
  }

  // Combine classes
  const combinedClassName = `${className} ${isActive ? activeClassName : ''}`.trim();

  // If the user doesn't have access but we want to render a disabled link
  if (!hasAccess && renderDisabledWhenNoAccess) {
    const disabledElement = (
      <span className={`${combinedClassName} ${disabledClassName}`} title={tooltipMessage} {...rest}>
        {children}
      </span>
    );

    // If we also want to show a tooltip, wrap the disabled element
    if (showTooltipWhenNoAccess) {
      return (
        <PermissionTooltip message={tooltipMessage} position={tooltipPosition}>
          {disabledElement}
        </PermissionTooltip>
      );
    }

    return disabledElement;
  }

  // If the user has access or we just want to show a tooltip without disabling
  const linkElement = (
    <Link href={href} className={combinedClassName} onClick={onClick} {...rest}>
      {children}
    </Link>
  );

  // If we want to show a tooltip when the user doesn't have access
  if (!hasAccess && showTooltipWhenNoAccess) {
    return (
      <PermissionTooltip message={tooltipMessage} position={tooltipPosition}>
        {linkElement}
      </PermissionTooltip>
    );
  }

  // Otherwise just render the link
  return linkElement;
};

export default PermissionAwareLink;
