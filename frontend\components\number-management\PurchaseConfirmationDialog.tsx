'use client';

import { Fragment } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { ExclamationTriangleIcon, ShoppingCartIcon } from '@heroicons/react/24/outline';
import { AvailableNumber } from '../../hooks/useNumberManagement';

interface PurchaseConfirmationDialogProps {
  numberData: AvailableNumber | null;
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isPurchasing: boolean;
}

export default function PurchaseConfirmationDialog({ 
  numberData, 
  isOpen, 
  onClose, 
  onConfirm, 
  isPurchasing 
}: PurchaseConfirmationDialogProps) {
  // Format phone number for display
  const formatPhoneNumber = (phone: string | undefined) => {
    if (!phone) return '';
    
    // This is a simple formatter, you might want to use a library like libphonenumber-js
    // for more sophisticated formatting based on country codes
    if (phone.startsWith('+1')) {
      // US format: +1 (XXX) XXX-XXXX
      const cleaned = phone.replace(/\D/g, '').substring(1); // Remove non-digits and the +1
      if (cleaned.length === 10) {
        return `+1 (${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6, 10)}`;
      }
    }
    return phone; // Return as-is if not US or not 10 digits
  };

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900 sm:mx-0 sm:h-10 sm:w-10">
                    <ShoppingCartIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" aria-hidden="true" />
                  </div>
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                    <Dialog.Title as="h3" className="text-lg font-semibold leading-6 text-gray-900 dark:text-white">
                      Purchase Phone Number
                    </Dialog.Title>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Are you sure you want to purchase this phone number? This action will charge your account.
                      </p>
                    </div>
                    
                    {numberData && (
                      <div className="mt-4 bg-gray-50 dark:bg-gray-750 p-4 rounded-md">
                        <div className="mb-2">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {formatPhoneNumber(numberData.phoneNumber)}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {numberData.location}
                          </p>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">Setup Fee</p>
                            <p className="font-medium text-gray-900 dark:text-white">
                              ${numberData.cost.setup.toFixed(2)}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">Monthly Cost</p>
                            <p className="font-medium text-gray-900 dark:text-white">
                              ${numberData.cost.monthly.toFixed(2)}/month
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 sm:ml-3 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
                    onClick={onConfirm}
                    disabled={isPurchasing}
                  >
                    {isPurchasing ? 'Processing...' : 'Confirm Purchase'}
                  </button>
                  <button
                    type="button"
                    className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-650 sm:mt-0 sm:w-auto"
                    onClick={onClose}
                  >
                    Cancel
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
