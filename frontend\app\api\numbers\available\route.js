import { NextResponse } from 'next/server';
// Temporarily comment out auth imports to simplify debugging
// import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
// import { cookies } from 'next/headers';

// Mock data for available phone numbers
const mockPhoneNumbers = [
  {
    sid: "PN1234567890ABCDEF",
    phoneNumber: "+14155552671",
    friendlyName: "(*************",
    isoCountry: "US",
    locality: "San Francisco",
    region: "CA",
    capabilities: {
      voice: true,
      sms: true,
      fax: false
    },
    numberType: "local"
  },
  {
    sid: "PN2345678901BCDEFG",
    phoneNumber: "+14155552672",
    friendlyName: "(*************",
    isoCountry: "US",
    locality: "San Francisco",
    region: "CA",
    capabilities: {
      voice: true,
      sms: true,
      fax: false
    },
    numberType: "local"
  },
  {
    sid: "PN3456789012CDEFGH",
    phoneNumber: "+14155552673",
    friendlyName: "(*************",
    isoCountry: "US",
    locality: "San Francisco",
    region: "CA",
    capabilities: {
      voice: true,
      sms: false,
      fax: false
    },
    numberType: "local"
  },
  {
    sid: "PN4567890123DEFGHI",
    phoneNumber: "+14155552674",
    friendlyName: "(*************",
    isoCountry: "US",
    locality: "San Francisco",
    region: "CA",
    capabilities: {
      voice: false,
      sms: true,
      fax: false
    },
    numberType: "local"
  },
  {
    sid: "PN5678901234EFGHIJ",
    phoneNumber: "+14155552675",
    friendlyName: "(*************",
    isoCountry: "US",
    locality: "San Francisco",
    region: "CA",
    capabilities: {
      voice: true,
      sms: true,
      fax: true
    },
    numberType: "local"
  },
  // German phone numbers
  {
    sid: "PN6789012345FGHIJK",
    phoneNumber: "+4930555267",
    friendlyName: "+49 30 555 267",
    isoCountry: "DE",
    locality: "Berlin",
    region: "Berlin",
    capabilities: {
      voice: true,
      sms: true,
      fax: false
    },
    numberType: "local"
  },
  {
    sid: "PN7890123456GHIJKL",
    phoneNumber: "+4930555268",
    friendlyName: "+49 30 555 268",
    isoCountry: "DE",
    locality: "Berlin",
    region: "Berlin",
    capabilities: {
      voice: true,
      sms: true,
      fax: false
    },
    numberType: "local"
  },
  // Canadian phone numbers
  {
    sid: "PN8901234567HIJKLM",
    phoneNumber: "+16135552671",
    friendlyName: "(*************",
    isoCountry: "CA",
    locality: "Ottawa",
    region: "ON",
    capabilities: {
      voice: true,
      sms: true,
      fax: false
    },
    numberType: "local"
  },
  {
    sid: "PN9012345678IJKLMN",
    phoneNumber: "+16135552672",
    friendlyName: "(*************",
    isoCountry: "CA",
    locality: "Ottawa",
    region: "ON",
    capabilities: {
      voice: true,
      sms: true,
      fax: false
    },
    numberType: "local"
  }
];

export async function GET(request) {
  console.log('Numbers API called:', request.url);
  
  try {
    // Get search parameters from the request URL
    const { searchParams } = new URL(request.url);
    const countryCode = searchParams.get('countryCode') || 'US';
    const areaCode = searchParams.get('areaCode');
    const voiceEnabled = searchParams.get('voiceEnabled') === 'true';
    const smsEnabled = searchParams.get('smsEnabled') === 'true';
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    
    console.log('Numbers API params:', { 
      countryCode, areaCode, voiceEnabled, smsEnabled, limit 
    });
    
    // Simulate filtering based on search parameters
    let filteredNumbers = [...mockPhoneNumbers];
    
    // Filter by country code if provided
    if (countryCode) {
      filteredNumbers = filteredNumbers.filter(num => num.isoCountry === countryCode);
      
      // If no numbers match the country code, return all numbers with that country code
      if (filteredNumbers.length === 0) {
        filteredNumbers = mockPhoneNumbers.filter(num => num.isoCountry === countryCode);
      }
    }
    
    // If still no numbers after country filter, use default US numbers
    if (filteredNumbers.length === 0) {
      filteredNumbers = mockPhoneNumbers.filter(num => num.isoCountry === 'US');
    }
    
    // Filter by capabilities if enabled, but ensure we don't filter all numbers out
    let capabilityFiltered = filteredNumbers;
    
    if (voiceEnabled) {
      capabilityFiltered = filteredNumbers.filter(num => num.capabilities.voice);
    }
    
    if (smsEnabled && capabilityFiltered.length > 0) {
      capabilityFiltered = capabilityFiltered.filter(num => num.capabilities.sms);
    }
    
    // Only use capability filtering if it doesn't filter all numbers out
    if (capabilityFiltered.length > 0) {
      filteredNumbers = capabilityFiltered;
    }
    
    // Apply limit
    filteredNumbers = filteredNumbers.slice(0, limit);
    
    console.log(`Returning ${filteredNumbers.length} phone numbers`);
    
    // Add a short delay to simulate API latency
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Return the filtered numbers with a consistent response format
    return NextResponse.json({
      success: true,
      data: {
        numbers: filteredNumbers
      }
    });
    
  } catch (error) {
    console.error('Error in numbers API:', error);
    return NextResponse.json({
      success: false, 
      message: error.message || 'API error', 
      data: {
        numbers: []
      }
    }, { status: 500 });
  }
} 