'use client';

import React, { ReactNode, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuthStore } from '../../stores/authStore';
import { isAdmin } from '../../app/utils/roleUtils';

interface AdminPageLayoutProps {
  children: ReactNode;
  title: string;
  description?: string;
}

export default function AdminPageLayout({ children, title, description }: AdminPageLayoutProps) {
  const router = useRouter();
  const user = useAuthStore(state => state.user);

  // Redirect non-admin users
  useEffect(() => {
    if (user && !isAdmin(user)) {
      router.push('/dashboard');
    }
  }, [user, router]);

  // Admin navigation items
  const adminNavItems = [
    { name: 'Admin Dashboard', path: '/dashboard/admin' },
    { name: 'User Management', path: '/dashboard/admin/users' },
    { name: 'Security Settings', path: '/dashboard/admin/security' },
    { name: 'Tenant Management', path: '/dashboard/admin/tenants' },
    { name: 'Billing Management', path: '/dashboard/admin/billing' },
    { name: 'System Logs', path: '/dashboard/admin/logs' },
    { name: 'API Management', path: '/dashboard/admin/api' },
    { name: 'Analytics', path: '/dashboard/admin/analytics' },
    { name: 'System', path: '/dashboard/admin/system' },
  ];

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Admin Header */}
      <div className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
        <h1 className="text-2xl font-bold text-white">{title}</h1>
        {description && <p className="text-gray-300 mt-2">{description}</p>}
      </div>

      {/* Admin Navigation */}
      <div className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-4 mb-6">
        <nav className="flex flex-wrap gap-2">
          {adminNavItems.map((item) => (
            <Link
              key={item.path}
              href={item.path}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                typeof window !== 'undefined' && window.location.pathname === item.path
                  ? 'bg-purple-600 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              }`}
            >
              {item.name}
            </Link>
          ))}
        </nav>
      </div>

      {/* Main Content */}
      <div>{children}</div>
    </div>
  );
}
