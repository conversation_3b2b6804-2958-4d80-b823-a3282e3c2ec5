'use client';

import { Integration } from './IntegrationsLayout';
import { CheckCircleIcon, XCircleIcon, ClockIcon } from '@heroicons/react/24/solid';

interface IntegrationCardProps {
  integration: Integration;
  isSelected: boolean;
  onSelect: () => void;
}

export default function IntegrationCard({
  integration,
  isSelected,
  onSelect,
}: IntegrationCardProps) {
  // Status indicator
  const StatusIndicator = () => {
    switch (integration.status) {
      case 'connected':
        return (
          <div className="flex items-center text-green-600 dark:text-green-400">
            <CheckCircleIcon className="h-4 w-4 mr-1" />
            <span className="text-xs">Connected</span>
          </div>
        );
      case 'disconnected':
        return (
          <div className="flex items-center text-gray-500 dark:text-gray-400">
            <XCircleIcon className="h-4 w-4 mr-1" />
            <span className="text-xs">Disconnected</span>
          </div>
        );
      case 'pending':
        return (
          <div className="flex items-center text-yellow-600 dark:text-yellow-400">
            <ClockIcon className="h-4 w-4 mr-1" />
            <span className="text-xs">Pending</span>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div
      className={`relative bg-white dark:bg-gray-800 rounded-lg border ${
        isSelected
          ? 'border-blue-500 dark:border-blue-400 ring-2 ring-blue-500/50 dark:ring-blue-400/50'
          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
      } shadow-sm p-4 cursor-pointer transition-all duration-200`}
      onClick={onSelect}
    >
      <div className="flex items-start">
        <div className="flex-shrink-0 text-gray-400 dark:text-gray-500">
          {integration.icon}
        </div>
        <div className="ml-4 flex-1">
          <div className="flex justify-between">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">
              {integration.name}
            </h3>
            <StatusIndicator />
          </div>
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {integration.description}
          </p>
          {integration.status === 'connected' && integration.lastSynced && (
            <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
              Last synced: {new Date(integration.lastSynced).toLocaleDateString()}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
