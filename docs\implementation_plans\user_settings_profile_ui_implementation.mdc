---
description: 
globs: 
alwaysApply: false
---
# User Settings and Profile UI Implementation Plan

## 1. Overview

This document outlines the frontend implementation plan for the CallSaver User Settings and Profile section. It details the components, data fetching, state management, user interactions, and error handling based on `settings_section_document.mdc` and `component_state_mapping.mdc`. This section allows users to manage their personal profile information, account security settings, notification preferences, API access keys, and view their subscription/billing details.

## 2. Component Hierarchy

The User Settings and Profile UI will be built using React components within the Next.js app router structure (e.g., `app/settings/page.tsx`). Components will reside in `front/mainpage/components/settings/`.

```mermaid
graph TD
    A[SettingsPage] --> B(SettingsLayout)
    B --> C(SettingsTabs)
    B --> D(SettingsContent)
    D --> E{Active Tab}
    E -->|Profile| F(ProfileSettingsPanel)
    E -->|Security| G(SecuritySettingsPanel)
    E -->|Notifications| H(NotificationPreferencesPanel)
    E -->|API Keys| I(ApiKeyManagementPanel)
    E -->|Billing| J(BillingSubscriptionPanel)
    E -->|Organization| K(OrganizationSettingsPanel)
    
    F --> F1(ProfileForm)
    F --> F2(AvatarUploader)
    
    G --> G1(PasswordChangeForm)
    G --> G2(MfaSetupForm)
    G --> G3(SessionsManager)
    
    H --> H1(NotificationChannelsForm)
    H --> H2(NotificationTypesForm)
    
    I --> I1(ApiKeysList)
    I --> I2(GenerateApiKeyForm)
    I --> I3(RevokeApiKeyDialog)
    
    J --> J1(SubscriptionInfoPanel)
    J --> J2(BillingHistoryTable)
    J --> J3(PaymentMethodsManager)
    
    K --> K1(OrganizationProfileForm)
    K --> K2(TeamMembersManager)
    K --> K3(BillingContactsForm)
```

## 3. Component Details

### SettingsPage (`app/settings/page.tsx`)
- Main page component that renders the settings layout
- Manages the active tab state
- Handles URL query parameters for direct tab access

### SettingsLayout (`components/settings/SettingsLayout.tsx`)
- Container component for the settings UI
- Renders the tabs and active content panel
- Props: `children`, `activeTab`, `onTabChange`

### SettingsTabs (`components/settings/SettingsTabs.tsx`)
- Renders the navigation tabs for different settings sections
- Highlights the active tab
- Props: `activeTab`, `onTabChange`, `tabs`

### ProfileSettingsPanel (`components/settings/ProfileSettingsPanel.tsx`)
- Container for profile settings components
- Manages form state and submission
- Handles API interactions for profile updates
- Props: `userData`, `isLoading`, `onUpdate`

### ProfileForm (`components/settings/ProfileForm.tsx`)
- Form for editing user profile information (name, email, phone, etc.)
- Validates input fields
- Props: `userData`, `isLoading`, `onSubmit`, `errors`

### AvatarUploader (`components/settings/AvatarUploader.tsx`)
- Component for uploading and cropping profile pictures
- Handles file selection, preview, and upload
- Props: `currentAvatarUrl`, `onUpload`, `isUploading`

### SecuritySettingsPanel (`components/settings/SecuritySettingsPanel.tsx`)
- Container for security settings components
- Manages state for password changes and MFA setup
- Props: `userData`, `isLoading`

### PasswordChangeForm (`components/settings/PasswordChangeForm.tsx`)
- Form for changing user password
- Validates password requirements
- Props: `onSubmit`, `isLoading`, `errors`

### MfaSetupForm (`components/settings/MfaSetupForm.tsx`)
- Interface for enabling/disabling MFA
- Guides through MFA setup process (QR code, verification)
- Props: `isMfaEnabled`, `onEnable`, `onDisable`, `isLoading`

### SessionsManager (`components/settings/SessionsManager.tsx`)
- Lists active sessions with device/location info
- Allows terminating individual sessions
- Props: `sessions`, `onTerminate`, `isLoading`

### NotificationPreferencesPanel (`components/settings/NotificationPreferencesPanel.tsx`)
- Container for notification settings components
- Manages notification preferences state
- Props: `preferences`, `isLoading`, `onUpdate`

### NotificationChannelsForm (`components/settings/NotificationChannelsForm.tsx`)
- Form for configuring notification channels (email, SMS, in-app)
- Props: `channels`, `onUpdate`, `isLoading`

### NotificationTypesForm (`components/settings/NotificationTypesForm.tsx`)
- Form for selecting which events trigger notifications
- Props: `notificationTypes`, `onUpdate`, `isLoading`

### ApiKeyManagementPanel (`components/settings/ApiKeyManagementPanel.tsx`)
- Container for API key management components
- Manages API key state and operations
- Props: `apiKeys`, `isLoading`, `onGenerate`, `onRevoke`

### ApiKeysList (`components/settings/ApiKeysList.tsx`)
- Table/list of existing API keys with metadata
- Shows partially masked keys
- Props: `apiKeys`, `onRevoke`, `isLoading`

### GenerateApiKeyForm (`components/settings/GenerateApiKeyForm.tsx`)
- Form for generating new API keys with labels and permissions
- Props: `onGenerate`, `isGenerating`, `errors`

### RevokeApiKeyDialog (`components/settings/RevokeApiKeyDialog.tsx`)
- Confirmation dialog for revoking API keys
- Props: `apiKey`, `isOpen`, `onClose`, `onConfirm`, `isRevoking`

### BillingSubscriptionPanel (`components/settings/BillingSubscriptionPanel.tsx`)
- Container for billing and subscription components
- Fetches and displays billing information
- Props: `subscriptionData`, `billingHistory`, `isLoading`

### SubscriptionInfoPanel (`components/settings/SubscriptionInfoPanel.tsx`)
- Displays current subscription plan and status
- Shows credit balance and usage
- Props: `subscription`, `credits`, `isLoading`

### BillingHistoryTable (`components/settings/BillingHistoryTable.tsx`)
- Table of past invoices and payments
- Allows downloading invoices
- Props: `billingHistory`, `isLoading`

### PaymentMethodsManager (`components/settings/PaymentMethodsManager.tsx`)
- Lists saved payment methods
- Interface for adding/removing payment methods
- Props: `paymentMethods`, `isLoading`, `onAdd`, `onRemove`

### OrganizationSettingsPanel (`components/settings/OrganizationSettingsPanel.tsx`)
- Container for organization settings (admin only)
- Manages organization profile and team members
- Props: `organizationData`, `isLoading`, `onUpdate`

## 4. Data Fetching and State Management

### React Query Hooks

Create custom hooks in `front/mainpage/hooks/useSettings.ts`:

```typescript
// Profile data
export const useProfile = () => {
  return useQuery({
    queryKey: ['profile'],
    queryFn: async () => {
      const response = await axios.get('/api/user/profile');
      return response.data;
    }
  });
};

// Update profile
export const useUpdateProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (profileData) => {
      const response = await axios.put('/api/user/profile', profileData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    }
  });
};

// Security settings
export const useSecurity = () => {
  return useQuery({
    queryKey: ['security'],
    queryFn: async () => {
      const response = await axios.get('/api/user/security');
      return response.data;
    }
  });
};

// Active sessions
export const useSessions = () => {
  return useQuery({
    queryKey: ['sessions'],
    queryFn: async () => {
      const response = await axios.get('/api/user/sessions');
      return response.data;
    }
  });
};

// Notification preferences
export const useNotificationPreferences = () => {
  return useQuery({
    queryKey: ['notifications'],
    queryFn: async () => {
      const response = await axios.get('/api/user/notifications/preferences');
      return response.data;
    }
  });
};

// API keys
export const useApiKeys = () => {
  return useQuery({
    queryKey: ['apiKeys'],
    queryFn: async () => {
      const response = await axios.get('/api/user/api-keys');
      return response.data;
    }
  });
};

// Billing and subscription
export const useSubscription = () => {
  return useQuery({
    queryKey: ['subscription'],
    queryFn: async () => {
      const response = await axios.get('/api/user/subscription');
      return response.data;
    }
  });
};

// Billing history
export const useBillingHistory = () => {
  return useQuery({
    queryKey: ['billingHistory'],
    queryFn: async () => {
      const response = await axios.get('/api/user/billing/history');
      return response.data;
    }
  });
};

// Organization settings (admin only)
export const useOrganization = () => {
  return useQuery({
    queryKey: ['organization'],
    queryFn: async () => {
      const response = await axios.get('/api/organization');
      return response.data;
    },
    enabled: false // Only fetch when user is confirmed as admin
  });
};
```

### Form State Management

- Use React Hook Form for complex forms
- Implement client-side validation before submission
- Show appropriate loading and error states
- Use optimistic updates where appropriate

## 5. API Endpoints

The UI will interact with the following API endpoints:

### Profile Management
- `GET /api/user/profile` - Get user profile data
- `PUT /api/user/profile` - Update user profile
- `POST /api/user/profile/avatar` - Upload profile picture

### Security Settings
- `GET /api/user/security` - Get security settings (MFA status, etc.)
- `PUT /api/user/security/password` - Change password
- `POST /api/user/security/mfa/enable` - Enable MFA
- `POST /api/user/security/mfa/disable` - Disable MFA
- `GET /api/user/sessions` - List active sessions
- `DELETE /api/user/sessions/{sessionId}` - Terminate a session

### Notification Preferences
- `GET /api/user/notifications/preferences` - Get notification preferences
- `PUT /api/user/notifications/preferences` - Update notification preferences

### API Keys
- `GET /api/user/api-keys` - List API keys
- `POST /api/user/api-keys` - Generate new API key
- `DELETE /api/user/api-keys/{keyId}` - Revoke API key

### Billing and Subscription
- `GET /api/user/subscription` - Get subscription details
- `GET /api/user/billing/history` - Get billing history
- `GET /api/user/billing/payment-methods` - Get payment methods
- `POST /api/user/billing/payment-methods` - Add payment method
- `DELETE /api/user/billing/payment-methods/{methodId}` - Remove payment method

### Organization Settings (Admin Only)
- `GET /api/organization` - Get organization details
- `PUT /api/organization` - Update organization details
- `GET /api/organization/members` - List team members
- `POST /api/organization/members` - Invite team member
- `PUT /api/organization/members/{memberId}` - Update team member role
- `DELETE /api/organization/members/{memberId}` - Remove team member

## 6. User Interactions

### Profile Management
- **Viewing Profile**: User navigates to Settings > Profile to view current profile information.
- **Editing Profile**: User modifies fields in the profile form and clicks "Save Changes".
- **Uploading Avatar**: User clicks on avatar or upload button, selects an image file, optionally crops it, and confirms upload.

### Security Settings
- **Changing Password**: User enters current password, new password, and confirmation, then clicks "Change Password".
- **Enabling MFA**: User clicks "Enable MFA", scans QR code with authenticator app, enters verification code, and confirms.
- **Disabling MFA**: User clicks "Disable MFA", confirms with password or MFA code.
- **Managing Sessions**: User views active sessions list, clicks "Terminate" on sessions to end them.

### Notification Preferences
- **Configuring Channels**: User toggles email, SMS, and in-app notification options.
- **Setting Event Types**: User selects which events should trigger notifications (calls, voicemails, low credits, etc.).

### API Key Management
- **Viewing Keys**: User sees a list of existing API keys with creation date, last used, and partial key.
- **Generating Key**: User clicks "Generate New Key", enters a label, selects permissions, and confirms.
- **Revoking Key**: User clicks "Revoke" on a key, confirms in dialog.

### Billing and Subscription
- **Viewing Subscription**: User sees current plan, status, renewal date, and credit balance.
- **Viewing Billing History**: User browses past invoices and payments, can download PDF invoices.
- **Managing Payment Methods**: User can view, add, and remove payment methods.
- **Upgrading Plan**: User clicks "Upgrade Plan" to be directed to the subscription selection page.

### Organization Settings (Admin Only)
- **Editing Organization Profile**: Admin modifies organization name, address, and other details.
- **Managing Team Members**: Admin views, invites, updates roles, and removes team members.
- **Setting Billing Contacts**: Admin designates which team members receive billing notifications.

## 7. Error Handling and Edge Cases

### Form Validation
- Implement client-side validation for all forms
- Show inline error messages for invalid fields
- Prevent submission of invalid forms

### API Error Handling
- Display appropriate error messages for API failures
- Implement retry logic for transient errors
- Provide clear guidance on how to resolve errors

### Edge Cases
- **Session Expiry**: Handle session expiration during settings changes
- **Concurrent Edits**: Handle conflicts if settings are changed from multiple sessions
- **MFA Setup Failure**: Provide recovery options if MFA setup fails
- **API Key Security**: Ensure API keys are only shown once at generation time
- **Permission Changes**: Handle cases where admin privileges are revoked during settings access

## 8. Implementation Notes

- Follow TailwindCSS guidelines from `frontend_guidelines_document.mdc`
- Adhere to naming and architecture rules from `cursor_project_rules.mdc`
- Use shared components (`Button`, `Modal`, `TextInput`, etc.) for consistency
- Implement accessibility standards (ARIA attributes, keyboard navigation)
- Add detailed logging for settings changes and errors
- Consider using a form management library (`react-hook-form`) for complex forms
- Implement proper loading, error, and empty states for all components
- Use optimistic updates where appropriate to improve perceived performance
- Add confirmation dialogs for destructive actions (revoking API keys, removing payment methods)

## 9. Testing Considerations

- Unit tests for form validation logic
- Integration tests for form submission flows
- Mock API responses for testing error states
- Accessibility testing for all forms and interactive elements
- Cross-browser testing for avatar upload functionality
- Mobile responsiveness testing for all settings panels

## Link to prompt_to_mdc_router.mdc
* **Primary Purpose**: Defines the implementation plan for the User Settings and Profile user interface.
* **Frontend Components**: `SettingsLayout`, `ProfileSettingsPanel`, `SecuritySettingsPanel`, `NotificationPreferencesPanel`, `ApiKeyManagementPanel`, `BillingSubscriptionPanel`, `OrganizationSettingsPanel`, and their sub-components.
* **API Endpoints**: Various endpoints under `/api/user/` for profile, security, notifications, API keys, and billing management.
* **Dependencies**: Relies on functional specifications defined in `settings_section_document.mdc`. Assumes backend API endpoints are implemented.
