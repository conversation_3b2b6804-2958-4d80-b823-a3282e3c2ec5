# Testing eSIM Integration with Mock Provider

While waiting for Airalo API credentials, you can fully test the eSIM integration using the mock provider. This guide provides step-by-step instructions for setting up and testing the eSIM functionality.

## Step 1: Configure Environment for Mock Provider

Create a `.env` file in the project root (or update your existing one) with the following settings:

```
# Enable eSIM functionality
ESIM_ENABLED=true

# Use mock provider for testing
TELEPHONY_DEFAULT_PROVIDER=mock

# Mock provider doesn't need credentials
```

## Step 2: Start the Development Server

1. Install dependencies if you haven't already:
   ```bash
   npm install
   ```

2. Start the backend server:
   ```bash
   cd back/backend
   npm run dev
   ```

3. In a separate terminal, start the frontend server:
   ```bash
   cd front/mainpage
   npm run dev
   ```

## Step 3: Testing Backend Services

You can test the backend services using the included test script:

```bash
node back/backend/test-airalo-connection.js
```

This script will connect to the mock provider and show sample data. Since we're using the mock provider, you should see sample package information without any authentication errors.

## Step 4: Testing the eSIM UI

1. Navigate to the eSIM management page in your browser:
   ```
   http://localhost:3000/dashboard/esim
   ```

2. Test the following functionality:
   - Creating a new eSIM profile (should show mock packages)
   - Viewing activation QR code (mock QR code will be displayed)
   - Checking data usage (mock data will be shown)
   - Purchasing additional data packages

## Step 5: Testing Error Handling

The mock provider occasionally generates random errors to help test error handling. Test how your application handles:

1. API errors during profile creation
2. Network timeouts (simulated with delays)
3. Error messages in the UI

## Step 6: Customizing Mock Data

You can customize the mock provider by modifying `back/backend/esim/providers/mockProvider.js`:

- Adjust the `generateMockPackages()` function to add/modify available packages
- Change the error rate in `simulateRandomErrors(errorRate)` (default is 5%)
- Modify the simulation delays in `simulateDelay(min, max)`

## Step 7: Testing the Telephony Abstraction Layer

The telephony service abstracts away provider-specific details. Test this by:

1. Creating calls through the telephony service
2. Verifying provider selection works correctly
3. Testing fallback to Twilio if enabled

## Step 8: Preparing for Real API Integration

While testing, prepare for the real API integration by:

1. Documenting any UI/UX issues discovered during testing
2. Creating test cases for the real API integration
3. Preparing a migration plan from mock to real provider

## Testing Specific Scenarios

### Test Scenario 1: Full eSIM Lifecycle

1. Create a new eSIM profile with a Global Traveler 1GB package
2. Generate and view the QR code
3. Check the activation status (will auto-activate after ~1 minute)
4. Monitor data usage (will show simulated usage over time)
5. Purchase an additional data package
6. Verify that the expiry date is extended

### Test Scenario 2: Error Handling

1. Create multiple profiles quickly to trigger random errors
2. Observe how the UI handles errors
3. Verify that error messages are user-friendly

### Test Scenario 3: Data Usage Visualization

1. Create a profile and wait for it to activate
2. View the data usage chart
3. Refresh several times to generate usage history points
4. Verify that the chart updates correctly

## Monitoring and Debugging

Use the browser console and server logs for debugging:

1. Browser console will show API requests and responses
2. Backend logs will show mock provider operations
3. Look for any unexpected behavior or error patterns

Once the Airalo API credentials are available, you'll only need to update your environment variables to switch from the mock provider to the real one, without any code changes required.
