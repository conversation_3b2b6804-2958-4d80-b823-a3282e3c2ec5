'use client';

import { Switch } from '@headlessui/react';
import { CreateAutomationParams } from '../../../hooks/useScheduledAutomation';
import { 
  PhoneIcon, 
  ChatBubbleLeftRightIcon, 
  CalendarIcon, 
  ClockIcon,
  AdjustmentsHorizontalIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';

interface ActionPreviewProps {
  automationConfig: CreateAutomationParams;
  isEnabled: boolean;
  onToggleEnabled: (isEnabled: boolean) => void;
}

export default function ActionPreview({
  automationConfig,
  isEnabled,
  onToggleEnabled,
}: ActionPreviewProps) {
  // Format schedule for display
  const formatSchedule = () => {
    if (automationConfig.schedule.type === 'once' && automationConfig.schedule.dateTime) {
      try {
        const date = new Date(automationConfig.schedule.dateTime);
        return `Once on ${format(date, 'MMMM d, yyyy')} at ${format(date, 'h:mm a')}`;
      } catch (error) {
        return 'Invalid date';
      }
    } else if (automationConfig.schedule.type === 'recurring' && automationConfig.schedule.cronExpression) {
      // This is a simplified implementation
      // In a real app, you would use a library to parse and describe the cron expression
      return `Recurring: ${automationConfig.schedule.cronExpression}`;
    }
    
    return 'No schedule set';
  };
  
  // Format conditions for display
  const formatConditions = () => {
    if (automationConfig.conditions.length === 0) {
      return 'No conditions (runs for all contacts)';
    }
    
    return automationConfig.conditions.map(condition => {
      const fieldName = condition.field.split('.').pop() || condition.field;
      const operator = condition.operator.replace('_', ' ');
      return `${fieldName} ${operator} ${condition.value}`;
    }).join(', ');
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Review Automation</h2>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
          Review your automation settings before saving. You can enable or disable the automation using the toggle below.
        </p>
      </div>

      {/* Summary card */}
      <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {automationConfig.name}
            </h3>
            <div className={`px-3 py-1 rounded-full text-sm font-medium ${
              automationConfig.type === 'call'
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            }`}>
              {automationConfig.type === 'call' ? 'Call' : 'SMS'}
            </div>
          </div>
          {automationConfig.description && (
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {automationConfig.description}
            </p>
          )}
        </div>
        
        {/* Details */}
        <div className="px-6 py-4 space-y-4">
          {/* Schedule */}
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <CalendarIcon className="h-5 w-5 text-gray-400" />
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">Schedule</h4>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {formatSchedule()}
              </p>
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Timezone: {automationConfig.schedule.timezone}
              </p>
            </div>
          </div>
          
          {/* Conditions */}
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <AdjustmentsHorizontalIcon className="h-5 w-5 text-gray-400" />
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">Conditions</h4>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {formatConditions()}
              </p>
            </div>
          </div>
          
          {/* Template */}
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <DocumentTextIcon className="h-5 w-5 text-gray-400" />
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">Template</h4>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {automationConfig.templateId ? `Template ID: ${automationConfig.templateId}` : 'No template selected'}
              </p>
            </div>
          </div>
        </div>
        
        {/* Footer with enable/disable toggle */}
        <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Switch
                checked={isEnabled}
                onChange={onToggleEnabled}
                className={`${
                  isEnabled ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'
                } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
              >
                <span
                  className={`${
                    isEnabled ? 'translate-x-6' : 'translate-x-1'
                  } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
                />
              </Switch>
              <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                {isEnabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            
            <div className="flex items-center text-sm">
              {isEnabled ? (
                <div className="flex items-center text-green-600 dark:text-green-400">
                  <CheckCircleIcon className="h-5 w-5 mr-1" />
                  <span>Will run as scheduled</span>
                </div>
              ) : (
                <div className="flex items-center text-gray-500 dark:text-gray-400">
                  <XCircleIcon className="h-5 w-5 mr-1" />
                  <span>Will not run until enabled</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Final notes */}
      <div className="mt-6 text-sm text-gray-500 dark:text-gray-400">
        <p>
          This automation will {isEnabled ? '' : 'not '}run according to the schedule you've set.
          You can always edit or {isEnabled ? 'disable' : 'enable'} it later from the automations list.
        </p>
      </div>
    </div>
  );
}
