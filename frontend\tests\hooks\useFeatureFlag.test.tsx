import { renderHook } from '@testing-library/react-hooks';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useFeatureFlag, useIsFeatureEnabled, useIsResourceFeatureEnabled } from '../../hooks/useFeatureFlag';
import { usePermissions } from '../../hooks/usePermissions';

// Mock dependencies
jest.mock('../../hooks/usePermissions', () => ({
  usePermissions: jest.fn()
}));

describe('useFeatureFlag', () => {
  const queryClient = new QueryClient();
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  beforeEach(() => {
    jest.clearAllMocks();
    queryClient.clear();
    
    // Mock the permissions hook
    (usePermissions as jest.Mock).mockImplementation(() => ({
      hasPermission: jest.fn().mockImplementation((permission) => {
        return permission === 'feature:use:any' || permission === 'admin:access:any';
      }),
      hasAnyPermission: jest.fn().mockImplementation((permissions) => {
        return permissions.includes('feature:use:any') || permissions.includes('admin:access:any');
      }),
      hasAllPermissions: jest.fn().mockImplementation((permissions) => {
        return permissions.every(p => p === 'feature:use:any' || p === 'admin:access:any');
      }),
      canAccess: jest.fn().mockImplementation((resource, action, scope) => {
        return resource === 'feature' && action === 'use';
      }),
      isLoading: false,
      error: null
    }));
  });

  test('should return true when user has the required permission', () => {
    const { result } = renderHook(() => useFeatureFlag({
      name: 'test-feature',
      permission: 'feature:use:any'
    }), { wrapper });
    
    expect(result.current).toBe(true);
  });

  test('should return false when user does not have the required permission', () => {
    const { result } = renderHook(() => useFeatureFlag({
      name: 'test-feature',
      permission: 'feature:use:other'
    }), { wrapper });
    
    expect(result.current).toBe(false);
  });

  test('should return true when user has any of the required permissions', () => {
    const { result } = renderHook(() => useFeatureFlag({
      name: 'test-feature',
      anyPermission: ['feature:use:any', 'feature:use:other']
    }), { wrapper });
    
    expect(result.current).toBe(true);
  });

  test('should return false when user has none of the required permissions', () => {
    const { result } = renderHook(() => useFeatureFlag({
      name: 'test-feature',
      anyPermission: ['feature:use:other', 'feature:admin:any']
    }), { wrapper });
    
    expect(result.current).toBe(false);
  });

  test('should return true when user has all of the required permissions', () => {
    const { result } = renderHook(() => useFeatureFlag({
      name: 'test-feature',
      allPermissions: ['feature:use:any', 'admin:access:any']
    }), { wrapper });
    
    expect(result.current).toBe(true);
  });

  test('should return false when user does not have all of the required permissions', () => {
    const { result } = renderHook(() => useFeatureFlag({
      name: 'test-feature',
      allPermissions: ['feature:use:any', 'feature:admin:any']
    }), { wrapper });
    
    expect(result.current).toBe(false);
  });

  test('should return true when user has access to the resource', () => {
    const { result } = renderHook(() => useFeatureFlag({
      name: 'test-feature',
      resource: 'feature',
      action: 'use',
      scope: 'any'
    }), { wrapper });
    
    expect(result.current).toBe(true);
  });

  test('should return false when user does not have access to the resource', () => {
    const { result } = renderHook(() => useFeatureFlag({
      name: 'test-feature',
      resource: 'other-feature',
      action: 'use',
      scope: 'any'
    }), { wrapper });
    
    expect(result.current).toBe(false);
  });

  test('should return the default value when no permission is specified', () => {
    const { result } = renderHook(() => useFeatureFlag({
      name: 'test-feature',
      defaultEnabled: true
    }), { wrapper });
    
    expect(result.current).toBe(true);
  });

  test('should return false when permissions are loading', () => {
    (usePermissions as jest.Mock).mockImplementation(() => ({
      hasPermission: jest.fn(),
      hasAnyPermission: jest.fn(),
      hasAllPermissions: jest.fn(),
      canAccess: jest.fn(),
      isLoading: true,
      error: null
    }));

    const { result } = renderHook(() => useFeatureFlag({
      name: 'test-feature',
      permission: 'feature:use:any'
    }), { wrapper });
    
    expect(result.current).toBe(false);
  });

  test('should return the specified default value when permissions are loading', () => {
    (usePermissions as jest.Mock).mockImplementation(() => ({
      hasPermission: jest.fn(),
      hasAnyPermission: jest.fn(),
      hasAllPermissions: jest.fn(),
      canAccess: jest.fn(),
      isLoading: true,
      error: null
    }));

    const { result } = renderHook(() => useFeatureFlag({
      name: 'test-feature',
      permission: 'feature:use:any',
      defaultEnabled: true
    }), { wrapper });
    
    expect(result.current).toBe(true);
  });
});

describe('useIsFeatureEnabled', () => {
  const queryClient = new QueryClient();
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  beforeEach(() => {
    jest.clearAllMocks();
    queryClient.clear();
    
    // Mock the permissions hook
    (usePermissions as jest.Mock).mockImplementation(() => ({
      hasPermission: jest.fn().mockImplementation((permission) => {
        return permission === 'feature:use:any';
      }),
      isLoading: false,
      error: null
    }));
  });

  test('should return true when user has the required permission', () => {
    const { result } = renderHook(() => useIsFeatureEnabled('test-feature', 'feature:use:any'), { wrapper });
    
    expect(result.current).toBe(true);
  });

  test('should return false when user does not have the required permission', () => {
    const { result } = renderHook(() => useIsFeatureEnabled('test-feature', 'feature:use:other'), { wrapper });
    
    expect(result.current).toBe(false);
  });
});

describe('useIsResourceFeatureEnabled', () => {
  const queryClient = new QueryClient();
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );

  beforeEach(() => {
    jest.clearAllMocks();
    queryClient.clear();
    
    // Mock the permissions hook
    (usePermissions as jest.Mock).mockImplementation(() => ({
      hasPermission: jest.fn(),
      canAccess: jest.fn().mockImplementation((resource, action, scope) => {
        return resource === 'feature' && action === 'use';
      }),
      isLoading: false,
      error: null
    }));
  });

  test('should return true when user has access to the resource', () => {
    const { result } = renderHook(() => useIsResourceFeatureEnabled('test-feature', 'feature', 'use', 'any'), { wrapper });
    
    expect(result.current).toBe(true);
  });

  test('should return false when user does not have access to the resource', () => {
    const { result } = renderHook(() => useIsResourceFeatureEnabled('test-feature', 'other-feature', 'use', 'any'), { wrapper });
    
    expect(result.current).toBe(false);
  });
});
