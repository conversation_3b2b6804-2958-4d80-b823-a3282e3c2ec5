import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    // Parse request body
    const formData = await request.formData();
    
    // Extract SMS status details from Twilio webhook
    const messageSid = formData.get('MessageSid');
    const messageStatus = formData.get('MessageStatus');
    
    console.log(`SMS status update for ${messageSid}: ${messageStatus}`);
    
    // In production, this would update the message record in the database
    // const updatedMessage = await prisma.message.update({
    //   where: { messageSid },
    //   data: {
    //     status: messageStatus.toLowerCase()
    //   }
    // });
    
    // For demo purposes, we'll just return a success response
    return NextResponse.json({
      success: true,
      message: `SMS status updated to ${messageStatus}`
    });
  } catch (error) {
    console.error('Error processing SMS status callback:', error);
    return NextResponse.json(
      { success: false, message: `Failed to process SMS status: ${error.message}` },
      { status: 500 }
    );
  }
} 