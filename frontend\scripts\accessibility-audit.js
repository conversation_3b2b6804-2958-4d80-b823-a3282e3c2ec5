/**
 * Accessibility Audit Script
 * 
 * This script uses axe-core to audit accessibility issues in the frontend components.
 * It generates a report of accessibility issues that need to be addressed.
 */
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

console.log('Starting accessibility audit...');

// Create directory for reports if it doesn't exist
const reportsDir = path.join(__dirname, '..', 'accessibility-reports');
if (!fs.existsSync(reportsDir)) {
  fs.mkdirSync(reportsDir);
}

// Run axe-core audit using Puppeteer
const runAudit = async () => {
  try {
    console.log('Installing required packages...');
    exec('npm install --save-dev axe-core puppeteer', (error, stdout, stderr) => {
      if (error) {
        console.error(`Error installing packages: ${error.message}`);
        return;
      }
      
      console.log('Packages installed successfully.');
      console.log('Running accessibility audit...');
      
      // Create the audit script
      const auditScriptPath = path.join(__dirname, 'run-axe.js');
      const auditScript = `
const puppeteer = require('puppeteer');
const { AxePuppeteer } = require('@axe-core/puppeteer');
const fs = require('fs');
const path = require('path');

(async () => {
  // URLs to test - add your application routes here
  const urls = [
    'http://localhost:3000',
    'http://localhost:3000/dashboard',
    'http://localhost:3000/dashboard/numbers',
    'http://localhost:3000/dashboard/automation'
  ];
  
  const browser = await puppeteer.launch();
  
  for (const url of urls) {
    try {
      console.log(\`Testing \${url}...\`);
      const page = await browser.newPage();
      await page.goto(url, { waitUntil: 'networkidle2' });
      
      // Run axe
      const results = await new AxePuppeteer(page).analyze();
      
      // Generate report filename
      const urlPath = new URL(url).pathname.replace(/\\//g, '-') || '-home';
      const reportPath = path.join(__dirname, '..', 'accessibility-reports', \`axe-report\${urlPath}.json\`);
      
      // Save report
      fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
      console.log(\`Report saved to \${reportPath}\`);
      
      await page.close();
    } catch (error) {
      console.error(\`Error testing \${url}: \${error.message}\`);
    }
  }
  
  await browser.close();
  
  // Generate summary report
  const reports = fs.readdirSync(path.join(__dirname, '..', 'accessibility-reports'))
    .filter(file => file.startsWith('axe-report') && file.endsWith('.json'))
    .map(file => {
      const filePath = path.join(__dirname, '..', 'accessibility-reports', file);
      const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      return {
        url: content.url,
        violations: content.violations.length,
        passes: content.passes.length,
        incomplete: content.incomplete.length,
        inapplicable: content.inapplicable.length
      };
    });
  
  const summaryPath = path.join(__dirname, '..', 'accessibility-reports', 'summary.json');
  fs.writeFileSync(summaryPath, JSON.stringify(reports, null, 2));
  console.log(\`Summary report saved to \${summaryPath}\`);
  
  // Generate HTML report
  const htmlReportPath = path.join(__dirname, '..', 'accessibility-reports', 'report.html');
  const htmlContent = \`
  <!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accessibility Audit Report</title>
    <style>
      body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
      h1 { color: #333; }
      table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
      th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
      th { background-color: #f2f2f2; }
      tr:nth-child(even) { background-color: #f9f9f9; }
      .summary { margin-bottom: 30px; }
      .violations { color: #d32f2f; }
      .passes { color: #388e3c; }
    </style>
  </head>
  <body>
    <h1>Accessibility Audit Report</h1>
    <div class="summary">
      <h2>Summary</h2>
      <table>
        <tr>
          <th>URL</th>
          <th>Violations</th>
          <th>Passes</th>
          <th>Incomplete</th>
          <th>Inapplicable</th>
        </tr>
        \${reports.map(report => \`
        <tr>
          <td>\${report.url}</td>
          <td class="violations">\${report.violations}</td>
          <td class="passes">\${report.passes}</td>
          <td>\${report.incomplete}</td>
          <td>\${report.inapplicable}</td>
        </tr>
        \`).join('')}
      </table>
    </div>
    
    <h2>Detailed Reports</h2>
    <p>Detailed JSON reports are available in the accessibility-reports directory.</p>
  </body>
  </html>
  \`;
  
  fs.writeFileSync(htmlReportPath, htmlContent);
  console.log(\`HTML report saved to \${htmlReportPath}\`);
})();
      `;
      
      fs.writeFileSync(auditScriptPath, auditScript);
      
      // Run the audit script
      exec(`node ${auditScriptPath}`, (error, stdout, stderr) => {
        if (error) {
          console.error(`Error running audit: ${error.message}`);
          return;
        }
        
        console.log(stdout);
        console.log('Accessibility audit completed successfully.');
      });
    });
  } catch (error) {
    console.error(`Error: ${error.message}`);
  }
};

runAudit();
