"use client";

import { createContext, useContext, useState, useEffect } from 'react';
import englishTranslations from './locales/en.json';
import germanTranslations from './locales/de.json';
import arabicTranslations from './locales/ar.json';

// Language configurations
const LANGUAGES = {
  en: {
    code: 'en',
    name: 'English',
    dir: 'ltr',
    translations: englishTranslations,
    flag: '🇬🇧'
  },
  de: {
    code: 'de',
    name: '<PERSON><PERSON><PERSON>',
    dir: 'ltr',
    translations: germanTranslations,
    flag: '🇩🇪'
  },
  ar: {
    code: 'ar',
    name: 'العربية',
    dir: 'rtl',
    translations: arabicTranslations,
    flag: '🇦🇪'
  }
};

const LanguageContext = createContext(null);

export function LanguageProvider({ children }) {
  const [language, setLanguage] = useState(LANGUAGES.en);

  // Detect user's language
  useEffect(() => {
    // Function to detect language from navigator or localStorage
    const detectLanguage = () => {
      // Check if there's a stored preference
      const storedLang = localStorage.getItem('preferred-language');
      if (storedLang && LANGUAGES[storedLang]) {
        return LANGUAGES[storedLang];
      }

      // Detect browser language
      const browserLang = navigator.language.split('-')[0].toLowerCase();
      if (LANGUAGES[browserLang]) {
        return LANGUAGES[browserLang];
      }

      // Default to English
      return LANGUAGES.en;
    };

    // Set the detected language
    setLanguage(detectLanguage());

    // Update document direction for RTL support
    document.documentElement.dir = detectLanguage().dir;
    if (detectLanguage().dir === 'rtl') {
      document.documentElement.classList.add('rtl');
    } else {
      document.documentElement.classList.remove('rtl');
    }
  }, []);

  // Update document direction when language changes
  useEffect(() => {
    document.documentElement.dir = language.dir;
    
    if (language.dir === 'rtl') {
      document.documentElement.classList.add('rtl');
    } else {
      document.documentElement.classList.remove('rtl');
    }
    
    // Store the preference
    localStorage.setItem('preferred-language', language.code);
  }, [language]);

  // Function to change language
  const changeLanguage = (langCode) => {
    if (LANGUAGES[langCode]) {
      setLanguage(LANGUAGES[langCode]);
    }
  };

  // Helper function to get a translation by key path
  const t = (keyPath) => {
    const keys = keyPath.split('.');
    let value = language.translations;
    
    for (const key of keys) {
      if (value && value[key]) {
        value = value[key];
      } else {
        return keyPath; // Fallback to key if translation not found
      }
    }
    
    return value;
  };

  return (
    <LanguageContext.Provider value={{ 
      language, 
      changeLanguage, 
      t, 
      languages: LANGUAGES,
      isRTL: language.dir === 'rtl'
    }}>
      {children}
    </LanguageContext.Provider>
  );
}

// Custom hook to use the language context
export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
} 