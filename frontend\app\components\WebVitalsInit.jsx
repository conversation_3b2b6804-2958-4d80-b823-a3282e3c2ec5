'use client';

import { useEffect } from 'react';
import { onCLS, onFID, onLCP, onTTFB, onINP } from 'web-vitals';

/**
 * Client component to initialize Web Vitals monitoring
 */
export default function WebVitalsInit() {
  useEffect(() => {
    // Function to log web vitals metrics
    const logWebVitals = (metric) => {
      // Log to console in development
      if (process.env.NODE_ENV !== 'production') {
        console.log(`Web Vitals: ${metric.name} = ${metric.value}`);
      }

      // Send to analytics in production
      if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'web_vitals', {
          event_category: 'Web Vitals',
          event_label: metric.name,
          value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
          non_interaction: true,
        });
      }
    };

    // Initialize all web vitals metrics
    onCLS(logWebVitals);  // Cumulative Layout Shift
    onFID(logWebVitals);  // First Input Delay
    onLCP(logWebVitals);  // Largest Contentful Paint
    onTTFB(logWebVitals); // Time to First Byte
    onINP(logWebVitals);  // Interaction to Next Paint
  }, []);

  // This component doesn't render anything
  return null;
}
