'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuthStore } from '../../../stores/authStore';
import { isAdmin } from '../../../app/utils/roleUtils';

export default function AdminDashboard() {
  const router = useRouter();
  const user = useAuthStore(state => state.user);
  const userIsAdmin = isAdmin(user);
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    totalPhoneNumbers: 0,
    totalCalls: 0,
    totalMessages: 0,
    totalRevenue: 0
  });

  useEffect(() => {
    // Redirect non-admin users
    if (user && !userIsAdmin) {
      console.log('Non-admin user detected, redirecting to dashboard');
      router.push('/dashboard');
      return;
    }

    console.log('Admin dashboard loaded');
    console.log('User:', user);
    console.log('Is admin:', userIsAdmin);

    const fetchAdminStats = async () => {
      try {
        // In a real implementation, this would be an API call to get admin stats
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 500));

        // Mock data
        setStats({
          totalUsers: 1250,
          activeUsers: 876,
          totalPhoneNumbers: 1893,
          totalCalls: 45678,
          totalMessages: 123456,
          totalRevenue: 98765
        });
      } catch (err) {
        console.error('Error fetching admin stats:', err);
      } finally {
        setIsLoading(false);
      }
    };

    if (user && userIsAdmin) {
      fetchAdminStats();
    }
  }, [user, userIsAdmin, router]);

  if (!user) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Loading user data...</h1>
      </div>
    );
  }

  if (!userIsAdmin) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="mb-4">You do not have permission to access the admin dashboard.</p>
        <Link href="/dashboard" className="px-4 py-2 bg-blue-600 text-white rounded-lg">
          Back to Dashboard
        </Link>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Admin Dashboard</h1>
        <p className="text-gray-600 dark:text-gray-300">Welcome to the admin dashboard. You have access to all administrative features.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 p-6 rounded-lg">
          <h3 className="text-xl font-bold text-white mb-1">Total Users</h3>
          <p className="text-gray-400 text-sm mb-3">All registered users</p>
          <p className="text-3xl font-bold text-white">{isLoading ? '...' : stats.totalUsers}</p>
        </div>

        <div className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 p-6 rounded-lg">
          <h3 className="text-xl font-bold text-white mb-1">Active Users</h3>
          <p className="text-gray-400 text-sm mb-3">Users active in last 30 days</p>
          <p className="text-3xl font-bold text-white">{isLoading ? '...' : stats.activeUsers}</p>
        </div>

        <div className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 p-6 rounded-lg">
          <h3 className="text-xl font-bold text-white mb-1">Total Revenue</h3>
          <p className="text-gray-400 text-sm mb-3">Lifetime revenue</p>
          <p className="text-3xl font-bold text-white">${isLoading ? '...' : stats.totalRevenue.toLocaleString()}</p>
        </div>
      </div>

      <div className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-bold text-white mb-4">System Overview</h2>
        <p className="text-gray-300 mb-6">Welcome to the admin dashboard. From here you can manage all aspects of the CallSaver platform.</p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-gray-700/50 border border-gray-600/50 p-4 rounded-lg">
            <h3 className="text-lg font-bold text-white mb-2">Total Phone Numbers</h3>
            <p className="text-2xl font-bold text-white">{isLoading ? '...' : stats.totalPhoneNumbers}</p>
          </div>

          <div className="bg-gray-700/50 border border-gray-600/50 p-4 rounded-lg">
            <h3 className="text-lg font-bold text-white mb-2">Total Calls</h3>
            <p className="text-2xl font-bold text-white">{isLoading ? '...' : stats.totalCalls.toLocaleString()}</p>
          </div>

          <div className="bg-gray-700/50 border border-gray-600/50 p-4 rounded-lg">
            <h3 className="text-lg font-bold text-white mb-2">Total Messages</h3>
            <p className="text-2xl font-bold text-white">{isLoading ? '...' : stats.totalMessages.toLocaleString()}</p>
          </div>
        </div>

        <div className="mt-8">
          <h3 className="text-lg font-semibold text-white mb-4">Admin Sections</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link href="/dashboard/admin/users" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
              User Management
            </Link>
            <Link href="/dashboard/admin/security" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
              Security Settings
            </Link>
            <Link href="/dashboard/admin/tenants" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
              Tenant Management
            </Link>
            <Link href="/dashboard/admin/billing" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
              Billing & Subscriptions
            </Link>
            <Link href="/dashboard/admin/analytics" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
              Advanced Analytics
            </Link>
            <Link href="/dashboard/admin/system" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
              System Configuration
            </Link>
            <Link href="/dashboard/admin/logs" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
              System Logs
            </Link>
            <Link href="/dashboard/admin/api" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
              API Management
            </Link>
            <Link href="/dashboard/admin/task-queue" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
              Task Queue Monitor
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
