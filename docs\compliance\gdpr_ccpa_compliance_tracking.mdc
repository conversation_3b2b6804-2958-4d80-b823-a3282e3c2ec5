---
description: Defines the strategy for GDPR/CCPA compliance, data mapping, and handling Data Subject Requests (DSRs).
---
# GDPR/CCPA Compliance and DSR Handling Strategy (`gdpr_ccpa_compliance_tracking.mdc`)

## 1. Purpose and Scope

**Purpose:** To outline the technical and organizational measures implemented by CallSaver to comply with the General Data Protection Regulation (GDPR), the California Consumer Privacy Act (CCPA), and similar data privacy regulations. This includes data mapping, consent management, security measures, and procedures for handling Data Subject Requests (DSRs).

**Scope:**
- Data mapping and inventory of Personal Data (PD) / Personal Information (PI).
- Legal bases for processing PD/PI.
- Consent management mechanisms.
- Implementation of data subject rights (access, rectification, erasure, portability, objection).
- Data Processing Agreements (DPAs) with third-party sub-processors.
- Data security measures relevant to PD/PI protection.
- Data breach notification procedures.
- Roles and responsibilities (including Data Protection Officer, if applicable).
- Training and awareness.

## 2. Data Mapping and Inventory

- **Process:** Maintain an internal data map identifying all types of PD/PI collected and processed by the CallSaver platform.
- **Data Categories (Examples):**
    - **Account Data:** Name, email, password hash, company name, address, phone number.
    - **Billing Data:** Payment method details (via Stripe, not stored directly), billing history, subscription details, credit balance.
    - **Usage Data:** Call logs (caller/callee numbers, duration, timestamp, recordings, transcriptions, summaries), SMS logs (sender/recipient numbers, content, timestamp), number configuration, automation rules, AI interaction logs.
    - **Technical Data:** IP addresses, browser user agents, cookies, device identifiers, API keys.
    - **Support Data:** Support ticket content, chat logs.
- **Mapping Details:** For each category, document:
    - Source of data.
    - Purpose of processing.
    - Legal basis for processing (see Section 3).
    - Storage location(s) (database tables, log files, cache, third-party services).
    - Third-party sub-processors involved.
    - Retention period.
    - Security measures applied.
- **Reference:** `back/backend/prisma/schema.prisma` is a key input for database mapping.

## 3. Legal Bases for Processing

- Identify and document the legal basis under GDPR/CCPA for each processing activity involving PD/PI. Common bases include:
    - **Contractual Necessity:** Processing necessary to provide the CallSaver service to the user/organization.
    - **Consent:** For optional processing activities (e.g., marketing emails, non-essential cookies).
    - **Legal Obligation:** Processing required by law (e.g., financial record keeping).
    - **Legitimate Interests:** Processing necessary for CallSaver's legitimate interests (e.g., security monitoring, abuse prevention, analytics for service improvement), provided these interests are not overridden by the data subject's rights. Requires balancing test documentation.

## 4. Consent Management

- **Mechanism:** Implement clear and granular consent mechanisms where consent is the legal basis.
    - Use explicit opt-in checkboxes (unticked by default).
    - Provide clear information about what is being consented to.
    - Allow users to easily withdraw consent at any time (e.g., via account settings).
- **Scope:** Marketing communications, non-essential cookies/tracking, optional data sharing.
- **Record Keeping:** Maintain records of user consent (what, when, how).

## 5. Data Subject Rights (DSR) Handling

- **Intake:** Provide clear mechanisms for users (or authorized agents under CCPA) to submit DSRs (e.g., dedicated <NAME_EMAIL>, form within account settings).
- **Verification:** Implement procedures to verify the identity of the requester before processing a DSR.
- **Procedures:**
    - **Right of Access:** Provide the data subject with a copy of their PD/PI undergoing processing, along with information about the processing (purposes, categories, recipients, retention, rights, etc.). Requires querying mapped data sources.
    - **Right to Rectification:** Allow data subjects to correct inaccurate PD/PI (e.g., via account profile settings, support requests for other data).
    - **Right to Erasure ('Right to be Forgotten'):** Delete the data subject's PD/PI upon valid request, subject to legal obligations or legitimate interests allowing retention (e.g., retaining billing records for legal compliance). Requires deleting data across all mapped sources, including backups according to retention policies. Consider anonymization/pseudonymization as an alternative if full deletion is problematic but permissible.
    - **Right to Data Portability:** Provide the data subject's PD/PI (where processed based on consent or contract) in a structured, commonly used, machine-readable format (e.g., JSON, CSV) upon request.
    - **Right to Object/Restrict Processing:** Allow data subjects to object to processing based on legitimate interests or for direct marketing. Implement mechanisms to restrict processing where applicable.
- **Automation:** Develop internal tools (e.g., CLI commands ref `internal_cli_tooling_strategy.mdc`) to assist in efficiently locating, exporting, and deleting user data across systems to fulfill DSRs within regulatory timeframes (e.g., 30 days for GDPR, 45 days for CCPA).
- **Tracking:** Maintain an internal log of received DSRs, verification steps, actions taken, and response dates.

## 6. Data Processing Agreements (DPAs)

- Maintain signed DPAs with all third-party sub-processors that handle PD/PI on behalf of CallSaver (e.g., Twilio, Stripe, AI providers, hosting provider, email service provider).
- Ensure DPAs meet GDPR/CCPA requirements.

## 7. Data Security

- Implement appropriate technical and organizational security measures to protect PD/PI against unauthorized access, loss, or destruction. Refer to `SECURITY_AUDIT.md` (if exists) and specific security policies. Key measures include:
    - Encryption (at rest and in transit).
    - Access controls (role-based, least privilege).
    - Multi-tenant data isolation (ref `multi_tenant_data_isolation.mdc`).
    - Regular security testing and vulnerability management.
    - Secure software development practices.

## 8. Data Breach Notification

- Establish internal procedures for detecting, investigating, and reporting data breaches involving PD/PI to relevant supervisory authorities and affected data subjects within the timeframes required by GDPR/CCPA.

## 9. Roles and Responsibilities

- Designate individuals or teams responsible for overseeing data privacy compliance, managing the data map, handling DSRs, and managing DPAs. Consider appointing a Data Protection Officer (DPO) if required by GDPR.

## 10. Training

- Provide regular data privacy and security training to relevant employees.

## 11. Related Documents

- `docs/architecture/multi_tenant_data_isolation.mdc`
- `back/backend/prisma/schema.prisma`
- `docs/functional_specs/user_roles_and_permissions.mdc`
- `docs/functional_specs/session_management_strategy.mdc`
- `docs/dev_guides/internal_cli_tooling_strategy.mdc` (for DSR tooling)
- `docs/security_audit.md` (if exists)
- Privacy Policy (External Document)
- Terms of Service (External Document)
