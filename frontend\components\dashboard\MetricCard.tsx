'use client';

import Link from 'next/link';
import { ComponentType } from 'react';

interface MetricCardProps {
  title: string;
  value: number;
  icon: ComponentType<{ className?: string }>;
  isLoading?: boolean;
  link?: string;
}

export default function MetricCard({ title, value, icon: Icon, isLoading = false, link }: MetricCardProps) {
  const formattedValue = new Intl.NumberFormat().format(value);

  const cardContent = (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sm:p-6 h-full transition-all duration-200 hover:shadow-lg">
      <div className="flex items-start justify-between">
        <div>
          <p className="text-xs sm:text-sm font-medium text-gray-500 dark:text-gray-400">{title}</p>
          {isLoading ? (
            <div className="h-6 sm:h-8 w-20 sm:w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mt-1"></div>
          ) : (
            <p className="mt-1 text-xl sm:text-2xl font-semibold text-gray-900 dark:text-white">{formattedValue}</p>
          )}
        </div>
        <div className="p-1.5 sm:p-2 bg-indigo-100 dark:bg-indigo-900 rounded-lg">
          <Icon className="h-5 w-5 sm:h-6 sm:w-6 text-indigo-600 dark:text-indigo-400" />
        </div>
      </div>
    </div>
  );

  if (link) {
    return (
      <Link href={link} className="block h-full">
        {cardContent}
      </Link>
    );
  }

  return cardContent;
}
