---
description: Defines the rules, standards, and conventions for creating and managing .mdc files within the CallSaver project.
---
# Project `.mdc` File Rules and Conventions (`cursor_project_rules.mdc`)

## 1. Purpose

This document defines the standards that all `.mdc` (Markdown Context) files within the CallSaver project must adhere to. Consistent adherence ensures clarity, maintainability, and effective use by both humans and AI assistants (like <PERSON><PERSON>, the God of `.mdc` Files).

## 2. File Naming and Location

- **Naming Convention:** Use lowercase `snake_case` for filenames (e.g., `api_gateway_routes.mdc`, `multi_tenant_data_isolation.mdc`).
- **Extension:** Always use the `.mdc` extension.
- **Location:** Place `.mdc` files in logical subdirectories within the `/docs` folder based on their domain:
    - `/docs/functional_specs/`: Core feature specifications.
    - `/docs/architecture/`: High-level system design and architectural patterns.
    - `/docs/dev_guides/`: Guidelines for developers (testing, SDKs, CLI).
    - `/docs/platform_ux/`: User experience related strategies (performance, offline).
    - `/docs/platform_security/`: Security-specific strategies.
    - `/docs/compliance/`: Compliance-related documentation (GDPR, etc.).
    - `/docs/integrations/`: Specifications for third-party integrations.
    - `/docs/ai/`: AI-specific strategies (prompts, etc.).
    - `/docs/implementation_plans/`: Step-by-step plans for implementing specific features (can reference functional specs).
- **Router:** The central command map is located at `.cursor/prompt_to_mdc_router.mdc`.

## 3. File Structure and Formatting

- **Format:** Standard Markdown syntax.
- **Front Matter:** Every `.mdc` file SHOULD start with YAML front matter defining at least a `description`. Other metadata (like `globs` or `alwaysApply` used by specific tooling) can be included if necessary.
  ```yaml
  ---
  description: A brief description of the file's purpose.
  ---
  ```
- **Title:** The first H1 heading (`#`) SHOULD match the filename (excluding extension) and include the filename in parentheses for clarity (e.g., `# API Gateway Routes Functional Document (\`api_gateway_routes.mdc\`)`).
- **Sections:** Use Markdown headings (`##`, `###`, etc.) to structure the document logically.
- **Lists:** Use standard Markdown bulleted (`-`, `*`) or numbered lists for clarity.
- **Code Blocks:** Use fenced code blocks with language identifiers for code examples (e.g., ```json`, ```javascript`, ```prisma`).
- **Emphasis:** Use bold (`**text**`) for emphasis on key terms or requirements (e.g., MUST, SHOULD). Use italics (`*text*`) sparingly.
- **Links:** Use relative links to reference other `.mdc` files or source code files within the project where appropriate.

## 4. Content Guidelines

- **Clarity and Conciseness:** Write clearly and avoid jargon where possible. Be specific about requirements.
- **Scope Definition:** Each `.mdc` file should clearly define its purpose and scope in an introductory section.
- **Completeness:** Aim to cover the necessary details for the specified scope. Reference other `.mdc` files for related information rather than duplicating content extensively.
- **Actionability:** Specifications should be actionable, providing enough detail for implementation or understanding.
- **Consistency:** Maintain consistent terminology across different `.mdc` files.
- **Related Documents:** Include a "Related Documents" section at the end listing other relevant `.mdc` or source files.

## 5. `.mdc` File Management

- **Router Integration:** Every significant `.mdc` specification file MUST be referenced in the `.cursor/prompt_to_mdc_router.mdc` under the appropriate section. This allows AI assistants to load relevant context efficiently.
- **Version Control:** All `.mdc` files are part of the Git repository and subject to standard version control practices (commits, pull requests, reviews).
- **Review:** Changes to significant `.mdc` files (especially architectural or core functional specs) should be reviewed like code changes.
- **Maintenance:** Keep `.mdc` files up-to-date as the platform evolves. Outdated documentation can be misleading. Deprecate or remove `.mdc` files for removed features.

## 6. AI Assistant Interaction (`Cline`)

- Cline (the God of `.mdc` Files) MUST adhere to all rules defined in this document.
- Cline MUST consult `.cursor/prompt_to_mdc_router.mdc` before loading context for a task.
- Cline's output MUST primarily be in the form of new or updated `.mdc` files, following these standards.
- Cline MUST update the router when creating new specification files.
