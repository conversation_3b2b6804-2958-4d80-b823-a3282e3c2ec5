'use client';

import React, { useState } from 'react';
import AdminPageLayout from '../../../../../components/admin/AdminPageLayout';
import PermissionDebugger from '../../../../../components/admin/PermissionDebugger';
import { useQuery } from '@tanstack/react-query';
import { api } from '../../../../../lib/apiClient';

export default function PermissionDebuggerPage() {
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch users for the dropdown
  const { data: users, isLoading } = useQuery({
    queryKey: ['users-for-permission-debug'],
    queryFn: async () => {
      const response = await api.get<{ id: string; email: string; name: string }[]>('/api/admin/users');
      return response;
    },
  });

  // Filter users based on search term
  const filteredUsers = React.useMemo(() => {
    if (!users) return [];
    if (!searchTerm) return users;

    return users.filter(
      (user) =>
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [users, searchTerm]);

  return (
    <AdminPageLayout
      title="Permission Debugger"
      description="Debug and inspect permissions for users in the system"
    >
      <div className="space-y-6">
        {/* User selection */}
        <div className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Select User</h2>
          
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <label htmlFor="user-search" className="block text-sm font-medium text-gray-300 mb-2">
                Search Users
              </label>
              <input
                id="user-search"
                type="text"
                placeholder="Search by name or email..."
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div className="flex-1">
              <label htmlFor="user-select" className="block text-sm font-medium text-gray-300 mb-2">
                Select User
              </label>
              <select
                id="user-select"
                className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
                value={selectedUserId || ''}
                onChange={(e) => setSelectedUserId(e.target.value || null)}
                disabled={isLoading}
              >
                <option value="">Current User (You)</option>
                {filteredUsers.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.name} ({user.email})
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="mt-4 flex justify-end">
            <button
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md transition-colors"
              onClick={() => setSelectedUserId(null)}
            >
              Reset to Current User
            </button>
          </div>
        </div>

        {/* Permission debugger */}
        <PermissionDebugger 
          userId={selectedUserId || undefined} 
          showAllPermissions={true}
        />
        
        {/* Usage instructions */}
        <div className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">How to Use</h2>
          <div className="text-gray-300 space-y-2">
            <p>
              The Permission Debugger allows you to inspect and debug permissions for any user in the system.
              You can see which permissions are granted, which roles grant those permissions, and detailed
              information about each permission.
            </p>
            <p>
              Use the search and filter options to find specific permissions. Click on a permission to view
              its details, including which roles grant it and how to use it in your code.
            </p>
            <p>
              This tool is especially useful for:
            </p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Debugging permission issues</li>
              <li>Understanding permission inheritance</li>
              <li>Verifying role configurations</li>
              <li>Finding the correct permission strings to use in your code</li>
            </ul>
          </div>
        </div>
      </div>
    </AdminPageLayout>
  );
}
