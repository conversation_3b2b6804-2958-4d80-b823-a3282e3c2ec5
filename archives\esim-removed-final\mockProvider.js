/**
 * Mock eSIM Provider
 * 
 * This module provides a mock implementation of an eSIM provider for development
 * and testing purposes. It simulates the behavior of a real provider without
 * making actual API calls.
 */

const logger = require('../../utils/logger');
const crypto = require('crypto');

/**
 * Create a mock provider instance
 * 
 * @param {Object} options - Provider configuration options
 * @returns {Object} Provider instance
 */
function createMockProvider(options = {}) {
  // In-memory storage for mock data
  const storage = {
    profiles: new Map(),
    packages: generateMockPackages(),
    usage: new Map()
  };
  
  logger.info('Creating mock eSIM provider instance');
  
  /**
   * Generate a unique ID
   * 
   * @returns {string} Unique ID
   */
  function generateId() {
    return crypto.randomUUID();
  }
  
  /**
   * Generate a random ICCID (Integrated Circuit Card Identifier)
   * 
   * @returns {string} Random ICCID
   */
  function generateIccid() {
    // ICCID format: 89 (telecom) + 01 (country) + issuer + account + check digit
    const prefix = '8901';
    const issuer = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const account = Math.floor(Math.random() * **********).toString().padStart(9, '0');
    return `${prefix}${issuer}${account}`;
  }
  
  /**
   * Generate mock packages
   * 
   * @returns {Array} Array of mock packages
   */
  function generateMockPackages() {
    return [
      {
        id: 'pkg-global-1gb',
        name: 'Global Traveler 1GB',
        description: 'Global data coverage for travelers',
        dataAmount: 1024, // in MB
        validity: 30, // in days
        price: 19.99,
        currency: 'USD',
        regions: ['global']
      },
      {
        id: 'pkg-global-3gb',
        name: 'Global Traveler 3GB',
        description: 'Global data coverage for travelers',
        dataAmount: 3072, // in MB
        validity: 30, // in days
        price: 39.99,
        currency: 'USD',
        regions: ['global']
      },
      {
        id: 'pkg-global-5gb',
        name: 'Global Traveler 5GB',
        description: 'Global data coverage for travelers',
        dataAmount: 5120, // in MB
        validity: 30, // in days
        price: 59.99,
        currency: 'USD',
        regions: ['global']
      },
      {
        id: 'pkg-europe-1gb',
        name: 'Europe Explorer 1GB',
        description: 'Data coverage across Europe',
        dataAmount: 1024, // in MB
        validity: 30, // in days
        price: 14.99,
        currency: 'USD',
        regions: ['europe']
      },
      {
        id: 'pkg-europe-3gb',
        name: 'Europe Explorer 3GB',
        description: 'Data coverage across Europe',
        dataAmount: 3072, // in MB
        validity: 30, // in days
        price: 29.99,
        currency: 'USD',
        regions: ['europe']
      },
      {
        id: 'pkg-asia-1gb',
        name: 'Asia Traveler 1GB',
        description: 'Data coverage across Asia',
        dataAmount: 1024, // in MB
        validity: 30, // in days
        price: 16.99,
        currency: 'USD',
        regions: ['asia']
      },
      {
        id: 'pkg-unlimited',
        name: 'Global Unlimited',
        description: 'Unlimited data for global travelers',
        dataAmount: -1, // unlimited
        validity: 30, // in days
        price: 99.99,
        currency: 'USD',
        regions: ['global']
      }
    ];
  }
  
  /**
   * Simulate network delay
   * 
   * @param {number} min - Minimum delay in ms
   * @param {number} max - Maximum delay in ms
   * @returns {Promise} Promise that resolves after the delay
   */
  function simulateDelay(min = 200, max = 1000) {
    const delay = Math.floor(Math.random() * (max - min + 1)) + min;
    return new Promise(resolve => setTimeout(resolve, delay));
  }
  
  /**
   * Simulate occasional random errors
   * 
   * @param {number} errorRate - Error rate (0-1)
   * @throws {Error} Random error
   */
  function simulateRandomErrors(errorRate = 0.05) {
    if (Math.random() < errorRate) {
      const errors = [
        { status: 400, message: 'Invalid request parameters', code: 'INVALID_PARAMS' },
        { status: 401, message: 'Authentication failed', code: 'AUTH_FAILED' },
        { status: 403, message: 'Permission denied', code: 'PERMISSION_DENIED' },
        { status: 404, message: 'Resource not found', code: 'NOT_FOUND' },
        { status: 500, message: 'Internal server error', code: 'SERVER_ERROR' },
        { status: 503, message: 'Service unavailable', code: 'SERVICE_UNAVAILABLE' }
      ];
      
      const error = errors[Math.floor(Math.random() * errors.length)];
      const mockError = new Error(error.message);
      mockError.apiError = error;
      throw mockError;
    }
  }
  
  /**
   * Provision a new eSIM profile
   * 
   * @param {Object} options - Provisioning options
   * @returns {Promise<Object>} Provisioned profile
   */
  async function provisionProfile(options = {}) {
    logger.debug('Provisioning mock eSIM profile', { options });
    
    await simulateDelay();
    simulateRandomErrors();
    
    // Find package
    const packageId = options.packageId || 'pkg-global-1gb';
    const packageDetails = storage.packages.find(pkg => pkg.id === packageId) || storage.packages[0];
    
    // Create profile
    const profileId = generateId();
    const iccid = generateIccid();
    const now = new Date();
    const expiresAt = new Date(now.getTime() + (packageDetails.validity * 24 * 60 * 60 * 1000));
    
    const profile = {
      id: profileId,
      iccid: iccid,
      status: 'provisioned',
      activationCode: `LPA:1$${crypto.randomBytes(20).toString('hex')}`,
      activated: null,
      expires: expiresAt.toISOString(),
      packageDetails: packageDetails,
      dataUsed: 0,
      assignedNumber: `+${Math.floor(**********0 + Math.random() * 90000000000)}`,
      metadata: options.metadata || {},
      createdAt: now.toISOString()
    };
    
    // Store in memory
    storage.profiles.set(profileId, profile);
    storage.usage.set(profileId, {
      usageHistory: [],
      lastUpdated: now.toISOString()
    });
    
    logger.info('Successfully provisioned mock eSIM', { profileId });
    
    return { ...profile };
  }
  
  /**
   * Deactivate an eSIM profile
   * 
   * @param {string} profileId - Profile ID
   * @param {Object} options - Deactivation options
   * @returns {Promise<boolean>} Success indicator
   */
  async function deactivateProfile(profileId, options = {}) {
    logger.debug('Deactivating mock eSIM profile', { profileId });
    
    await simulateDelay();
    simulateRandomErrors();
    
    // Check if profile exists
    if (!storage.profiles.has(profileId)) {
      const error = new Error(`Profile not found: ${profileId}`);
      error.apiError = {
        status: 404,
        message: `Profile not found: ${profileId}`,
        code: 'NOT_FOUND'
      };
      throw error;
    }
    
    // Update profile status
    const profile = storage.profiles.get(profileId);
    profile.status = 'deactivated';
    storage.profiles.set(profileId, profile);
    
    logger.info('Successfully deactivated mock eSIM', { profileId });
    
    return true;
  }
  
  /**
   * Get available eSIM packages
   * 
   * @param {Object} criteria - Search criteria
   * @param {Object} options - Search options
   * @returns {Promise<Array>} Available packages
   */
  async function getAvailablePackages(criteria = {}, options = {}) {
    logger.debug('Getting available mock eSIM packages', { criteria });
    
    await simulateDelay();
    simulateRandomErrors();
    
    // Filter packages based on criteria
    let packages = [...storage.packages];
    
    if (criteria.region && criteria.region !== 'global') {
      packages = packages.filter(pkg => 
        pkg.regions.includes(criteria.region) || pkg.regions.includes('global')
      );
    }
    
    if (criteria.minData && !isNaN(criteria.minData)) {
      const minData = Number(criteria.minData);
      packages = packages.filter(pkg => 
        pkg.dataAmount === -1 || pkg.dataAmount >= minData
      );
    }
    
    if (criteria.maxPrice && !isNaN(criteria.maxPrice)) {
      const maxPrice = Number(criteria.maxPrice);
      packages = packages.filter(pkg => pkg.price <= maxPrice);
    }
    
    return packages;
  }
  
  /**
   * Get profile details
   * 
   * @param {string} profileId - Profile ID
   * @param {Object} options - Retrieval options
   * @returns {Promise<Object>} Profile details
   */
  async function getProfileDetails(profileId, options = {}) {
    logger.debug('Getting mock eSIM profile details', { profileId });
    
    await simulateDelay();
    simulateRandomErrors();
    
    // Check if profile exists
    if (!storage.profiles.has(profileId)) {
      const error = new Error(`Profile not found: ${profileId}`);
      error.apiError = {
        status: 404,
        message: `Profile not found: ${profileId}`,
        code: 'NOT_FOUND'
      };
      throw error;
    }
    
    // Get profile
    const profile = storage.profiles.get(profileId);
    
    // Simulate data usage increase over time if the profile is active
    if (profile.status === 'activated') {
      const now = new Date();
      const activatedDate = new Date(profile.activated);
      const daysSinceActivation = Math.max(1, Math.floor((now - activatedDate) / (1000 * 60 * 60 * 24)));
      
      // If the package has a data limit
      if (profile.packageDetails.dataAmount !== -1) {
        // Calculate usage based on time since activation - on average using half the data over the validity period
        const targetUsage = (profile.packageDetails.dataAmount / profile.packageDetails.validity) * daysSinceActivation;
        
        // Add some randomness (±20%)
        const randomFactor = 0.8 + (Math.random() * 0.4);
        profile.dataUsed = Math.min(profile.packageDetails.dataAmount, targetUsage * randomFactor);
      } else {
        // For unlimited packages, just simulate some usage
        profile.dataUsed = daysSinceActivation * 100; // 100MB per day
      }
      
      // Update the profile in storage
      storage.profiles.set(profileId, profile);
    }
    
    return { ...profile };
  }
  
  /**
   * Generate activation QR code
   * 
   * @param {string} profileId - Profile ID
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} QR code data
   */
  async function generateActivationQR(profileId, options = {}) {
    logger.debug('Generating activation QR for mock eSIM', { profileId });
    
    await simulateDelay();
    simulateRandomErrors();
    
    // Check if profile exists
    if (!storage.profiles.has(profileId)) {
      const error = new Error(`Profile not found: ${profileId}`);
      error.apiError = {
        status: 404,
        message: `Profile not found: ${profileId}`,
        code: 'NOT_FOUND'
      };
      throw error;
    }
    
    // Get profile
    const profile = storage.profiles.get(profileId);
    
    // Mock QR code data (base64 encoded)
    // In a real implementation, this would be an actual QR code image
    const qrCodeData = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAIAAADTED8xAAADMElEQVR4nOzVwQnAIBQFQYXff81RUHsTZAZs+Ly9DfDWeQHECQDiBABxAoA4AUCcACBOABAnAIgTAMQJAOIEAHECgDgBQJwAIE4AECcAiBMAxAkA4gQAcQKAOAFAnAAgTgAQJwCIEwDECQDiBABxAoA4AUCcACBOABAnAIgTAMQJAOIEAHECgDgBQJwAIE4AECcAiBMAxAkA4gQAcQKAOAFAnAAgTgAQJwCIEwDECQDiBABxAoA4AUCcACBOABAnAIgTAMQJAOIEAHECgDgBQJwAIE4AECcAiBMAxAkA4gQAcQKAOAFAnAAgTgAQJwCIEwDECQDiBABxAoA4AUCcACBOABAnAIgTAMQJAOIEAHECgDgBQJwAIE4AECcAiBMAxAkA4gQAcQKAOAFAnAAgTgAQJwCIEwDECQDiBABxAoA4AUCcACBOABAnAIgTAMQJAOIEAHECgDgBQJwAIE4AECcAiBMAxAkA4gQAcQKAOAFAnAAgTgAQJwCIEwDECQDiBABxAoA4AUCcACBOABAnAIgTAMQJAOIEAHECgDgBQJwAIE4AECcAiBMAxAkA4gQAcQKAOAFAnAAgTgAQJwCIEwDECQDiBABxAoA4AUCcACBOABAnAIgTAMQJAOIEAHECgDgBQJwAIE4AECcAiBMAxAkA4gQAcQKAOAFAnAAgTgAQJwCIEwDECQDiBABxAoA4AUCcACBOABAnAIgTAMQJAOIEAHECgDgBQJwAIE4AECcAiBMAxAkA4gQAcQKAOAFAnAAgTgAQJwCIEwDECQDiBABxAoA4AUCcACBOABAnAIgTAMQJAOIEAHECgDgBQJwAIE4AECcAiBMAxAkA4gQAcQKAOAFAnAAgTgAQJwCIEwDECQDiBABxAoA4AUCcACBOABAnAIgTAMQJAOIEAHECgDgBQJwAIE4AECcAiBMAxAkA4gQAcQKAOAFAnAAgTgAQJwCIEwDECQDiBABxAoA4AUCcACBOABAnAIgTAMQJAOIEAHECgDgBQJwAIE4AECcAiBMAxAkA4gQAcQKAOAFAnAAgTgAQJwCIEwDECQDiBAAHsncDjLEForUAAAAASUVORK5CYII=`;
    
    return {
      qrCodeData,
      activationCode: profile.activationCode,
      format: 'png',
      expiresAt: profile.expires
    };
  }
  
  /**
   * Check activation status
   * 
   * @param {string} profileId - Profile ID
   * @param {Object} options - Check options
   * @returns {Promise<Object>} Activation status
   */
  async function checkActivationStatus(profileId, options = {}) {
    logger.debug('Checking activation status for mock eSIM', { profileId });
    
    await simulateDelay();
    simulateRandomErrors();
    
    // Check if profile exists and get details
    const profile = await getProfileDetails(profileId);
    
    // If the profile status is 'provisioned' and it was created more than 1 minute ago,
    // randomly activate it to simulate user activation
    if (profile.status === 'provisioned' && !profile.activated) {
      const createdAt = new Date(profile.createdAt);
      const now = new Date();
      const minutesSinceCreation = (now - createdAt) / (1000 * 60);
      
      // 50% chance of activation if created more than 1 minute ago
      if (minutesSinceCreation > 1 && Math.random() > 0.5) {
        profile.status = 'activated';
        profile.activated = now.toISOString();
        storage.profiles.set(profileId, profile);
      }
    }
    
    return {
      profileId,
      status: profile.status,
      activatedAt: profile.activated,
      isActive: profile.status === 'activated'
    };
  }
  
  /**
   * Get data usage
   * 
   * @param {string} profileId - Profile ID
   * @param {Object} options - Usage options
   * @returns {Promise<Object>} Data usage
   */
  async function getDataUsage(profileId, options = {}) {
    logger.debug('Getting data usage for mock eSIM', { profileId });
    
    await simulateDelay();
    simulateRandomErrors();
    
    // Get profile details (which updates the usage data)
    const profile = await getProfileDetails(profileId);
    const usage = storage.usage.get(profileId) || { usageHistory: [], lastUpdated: new Date().toISOString() };
    
    // Update usage history
    const now = new Date();
    usage.usageHistory.push({
      timestamp: now.toISOString(),
      dataUsed: profile.dataUsed
    });
    usage.lastUpdated = now.toISOString();
    
    // Keep only the last 30 data points
    if (usage.usageHistory.length > 30) {
      usage.usageHistory = usage.usageHistory.slice(-30);
    }
    
    // Store updated usage
    storage.usage.set(profileId, usage);
    
    return {
      profileId,
      packageId: profile.packageDetails.id,
      dataTotal: profile.packageDetails.dataAmount,
      dataUsed: profile.dataUsed,
      dataRemaining: profile.packageDetails.dataAmount === -1 ? -1 : profile.packageDetails.dataAmount - profile.dataUsed,
      isUnlimited: profile.packageDetails.dataAmount === -1,
      lastUpdated: usage.lastUpdated,
      usageHistory: usage.usageHistory
    };
  }
  
  /**
   * Purchase a data package
   * 
   * @param {string} profileId - Profile ID
   * @param {Object} packageDetails - Package to purchase
   * @param {Object} options - Purchase options
   * @returns {Promise<Object>} Purchase confirmation
   */
  async function purchaseDataPackage(profileId, packageDetails, options = {}) {
    logger.debug('Purchasing data package for mock eSIM', { 
      profileId, 
      packageId: packageDetails.packageId 
    });
    
    await simulateDelay();
    simulateRandomErrors();
    
    // Check if profile exists
    if (!storage.profiles.has(profileId)) {
      const error = new Error(`Profile not found: ${profileId}`);
      error.apiError = {
        status: 404,
        message: `Profile not found: ${profileId}`,
        code: 'NOT_FOUND'
      };
      throw error;
    }
    
    // Check if package exists
    const packageId = packageDetails.packageId;
    const packageInfo = storage.packages.find(pkg => pkg.id === packageId);
    
    if (!packageInfo) {
      const error = new Error(`Package not found: ${packageId}`);
      error.apiError = {
        status: 404,
        message: `Package not found: ${packageId}`,
        code: 'NOT_FOUND'
      };
      throw error;
    }
    
    // Get profile
    const profile = storage.profiles.get(profileId);
    
    // Update profile with new package
    profile.packageDetails = packageInfo;
    
    // If it's a top-up, adjust expiry date
    const now = new Date();
    const expiresAt = new Date(now.getTime() + (packageInfo.validity * 24 * 60 * 60 * 1000));
    profile.expires = expiresAt.toISOString();
    
    // Save updated profile
    storage.profiles.set(profileId, profile);
    
    // Generate confirmation
    const purchaseId = generateId();
    
    return {
      profileId,
      packageId: packageId,
      purchaseId,
      transactionDate: now.toISOString(),
      price: packageInfo.price,
      currency: packageInfo.currency,
      status: 'completed'
    };
  }
  
  // Return provider interface
  return {
    provisionProfile,
    deactivateProfile,
    getAvailablePackages,
    getProfileDetails,
    generateActivationQR,
    checkActivationStatus,
    getDataUsage,
    purchaseDataPackage
  };
}

module.exports = createMockProvider;
