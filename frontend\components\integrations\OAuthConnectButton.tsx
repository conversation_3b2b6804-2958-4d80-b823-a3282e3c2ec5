'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Integration, useIntegrations } from '../../hooks/useIntegrations';
import LoadingSpinner from '../shared/LoadingSpinner';

interface OAuthConnectButtonProps {
  integration: Integration;
  onConnected: () => void;
}

export default function OAuthConnectButton({
  integration,
  onConnected,
}: OAuthConnectButtonProps) {
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [isProcessingCallback, setIsProcessingCallback] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { connectIntegration } = useIntegrations();

  // Check if this is a callback from OAuth provider
  useEffect(() => {
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const error = searchParams.get('error');
    const integrationId = searchParams.get('integration_id');
    
    // If we have a code and state, and the integration ID matches, process the callback
    if (code && state && integrationId === integration.id && !error) {
      setIsProcessingCallback(true);
      
      // Call the API to exchange the code for tokens
      connectIntegration(
        {
          integrationId: integration.id,
          settings: {
            code,
            state,
          },
        },
        {
          onSuccess: () => {
            // Clear the URL parameters
            const url = new URL(window.location.href);
            url.search = '';
            window.history.replaceState({}, '', url.toString());
            
            setIsProcessingCallback(false);
            onConnected();
          },
          onError: () => {
            setIsProcessingCallback(false);
          },
        }
      );
    }
  }, [searchParams, integration.id, connectIntegration, onConnected]);

  // Handle connect button click
  const handleConnect = () => {
    if (!integration.authUrl) {
      console.error('No auth URL provided for OAuth integration');
      return;
    }
    
    setIsRedirecting(true);
    
    // Add the current URL as the redirect_uri if not already in the authUrl
    let url = integration.authUrl;
    if (!url.includes('redirect_uri=')) {
      const redirectUri = encodeURIComponent(`${window.location.origin}/dashboard/integrations`);
      url += (url.includes('?') ? '&' : '?') + `redirect_uri=${redirectUri}`;
    }
    
    // Add the integration ID to the state parameter if not already in the authUrl
    if (!url.includes('integration_id=')) {
      url += (url.includes('?') ? '&' : '?') + `integration_id=${integration.id}`;
    }
    
    // Redirect to the OAuth provider
    window.location.href = url;
  };

  if (isProcessingCallback) {
    return (
      <div className="flex flex-col items-center justify-center py-4">
        <LoadingSpinner />
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          Processing authentication...
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center">
      <p className="mb-4 text-sm text-gray-500 dark:text-gray-400">
        Connect your {integration.name} account to enable integration.
      </p>
      <button
        type="button"
        className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        onClick={handleConnect}
        disabled={isRedirecting || !integration.authUrl}
      >
        {isRedirecting ? (
          <>
            <LoadingSpinner size="sm" />
            <span className="ml-2">Redirecting...</span>
          </>
        ) : (
          `Connect to ${integration.name}`
        )}
      </button>
    </div>
  );
}
