"use client";

import { useState, useEffect, useRef } from 'react';
import { FiClock, FiPlus, FiChevronDown, FiChevronUp, FiMessageSquare, FiArchive, FiLoader } from 'react-icons/fi';
import CommandInput from './CommandInput';
import CallStatusDisplay from './CallStatusDisplay';
import { ToastContainer } from '../ui/Toast';
import { parseCommand } from '../../utils/commandParser';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { atomDark } from 'react-syntax-highlighter/dist/cjs/styles/prism';

export default function AutomationChat({ phoneNumber }) {
  const [messages, setMessages] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentCall, setCurrentCall] = useState(null);
  const [chatSessions, setChatSessions] = useState([]);
  const [currentChatId, setCurrentChatId] = useState('new');
  const [showChatHistory, setShowChatHistory] = useState(false);
  const [apiKeyMissing, setApiKeyMissing] = useState(false);
  
  const chatContainerRef = useRef(null);
  
  // Scroll to bottom when messages change
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);
  
  // Initialize chat sessions
  useEffect(() => {
    // For demo purposes, create some sample chat sessions
    // In a real app, this would come from an API
    setChatSessions([
      { id: '1', title: 'Setup Call Forwarding', timestamp: new Date(Date.now() - 86400000).toISOString() },
      { id: '2', title: 'SMS Auto-Reply Config', timestamp: new Date(Date.now() - 172800000).toISOString() }
    ]);
  }, []);
  
  // Handle command submission
  const handleSendCommand = async (command) => {
    // If this is a new chat, create a session for it
    if (currentChatId === 'new' && chatSessions.length < 10) {
      const newChatId = Date.now().toString();
      const newChat = {
        id: newChatId,
        title: `Chat ${chatSessions.length + 1}`,
        timestamp: new Date().toISOString()
      };
      
      setChatSessions(prev => [newChat, ...prev]);
      setCurrentChatId(newChatId);
    }
    
    // Add user message to chat
    const userMessage = {
      id: Date.now(),
      sender: 'user',
      content: command,
      timestamp: new Date().toISOString()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setIsProcessing(true);
    
    try {
      // Parse the command to check if it's a slash command
      const parsedCommand = parseCommand(command);
      
      if (parsedCommand) {
        // Handle slash commands
        if (parsedCommand.type === 'help') {
          // Display help information
          addAssistantMessage(`
**Available Commands:**

- **/call +[number] Say: [message]** - Make an outbound call and speak the message
- **/sms +[number] [message]** - Send an SMS message
- **/help** - Show this help message

You can also ask me any questions about CallSaver or general topics!
          `);
          return;
        }
        
        if (parsedCommand.type === 'call') {
          // Process call command
          addAssistantMessage(`Initiating call from ${phoneNumber} to ${parsedCommand.number}...`);
          
          // Call the API to initiate the call
          const response = await fetch('/api/automation/call', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              from: phoneNumber,
              to: parsedCommand.number,
              message: parsedCommand.message
            })
          });
          
          const data = await response.json();
          
          if (!response.ok) {
            throw new Error(data.message || 'Failed to initiate call');
          }
          
          // Update current call status
          const newCall = {
            id: data.callId,
            callSid: data.callSid,
            from: phoneNumber,
            to: parsedCommand.number,
            body: parsedCommand.message,
            status: 'initiated',
            timestamp: new Date().toISOString()
          };
          
          setCurrentCall(newCall);
          
          addAssistantMessage(`Call to ${parsedCommand.number} initiated successfully! Call ID: ${data.callSid}`);
          
          // Subscribe to real-time updates for this call (would use WebSockets in production)
          // For demo, we'll simulate status changes
          simulateCallProgress(newCall);
          
          // Update chat title
          updateChatTitle(`Call to ${parsedCommand.number}`);
          return;
        }
        
        if (parsedCommand.type === 'sms') {
          // Process SMS command
          addAssistantMessage(`Sending SMS from ${phoneNumber} to ${parsedCommand.number}...`);
          
          // Call the API to send the SMS
          const response = await fetch('/api/automation/sms', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              from: phoneNumber,
              to: parsedCommand.number,
              message: parsedCommand.message
            })
          });
          
          const data = await response.json();
          
          if (!response.ok) {
            throw new Error(data.message || 'Failed to send SMS');
          }
          
          addAssistantMessage(`SMS to ${parsedCommand.number} sent successfully! Message ID: ${data.messageSid}`);
          
          // Update chat title
          updateChatTitle(`SMS to ${parsedCommand.number}`);
          return;
        }
      } else {
        // This is not a slash command, so process it with OpenAI
        await processWithOpenAI(command);
      }
    } catch (error) {
      console.error('Command execution failed:', error);
      addAssistantMessage(`Error: ${error.message}`);
      window.toast?.error(`Command failed: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Process message with OpenAI
  const processWithOpenAI = async (userMessage) => {
    try {
      // Prepare the conversation history for the API
      const conversationHistory = messages
        .slice(-10) // Only use the last 10 messages for context
        .map(msg => ({
          sender: msg.sender,
          content: msg.content
        }));

      // Call our OpenAI API endpoint
      const response = await fetch('/api/automation/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messages: [...conversationHistory, { sender: 'user', content: userMessage }]
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to get response from AI');
      }

      // Check if the API detected a command in natural language
      if (data.detectedCommand) {
        // Show the detected command to the user
        addAssistantMessage(`${data.message}\n\n**Detected Command**: \`${data.detectedCommand}\`\n\nWould you like me to execute this command?`);
        
        // Add a clickable button or link to execute the command
        setTimeout(() => {
          const executeCommand = data.detectedCommand;
          handleSendCommand(executeCommand);
        }, 2000); // Wait 2 seconds before auto-executing the command
        
        return;
      }

      // Update chat title if this is a new conversation
      if (messages.length <= 1) {
        // Generate a title from the first user message
        const title = userMessage.length > 25 
          ? `${userMessage.substring(0, 25)}...` 
          : userMessage;
        updateChatTitle(title);
      }

      // Add the AI response to the chat
      addAssistantMessage(data.message);
    } catch (error) {
      console.error('OpenAI API call failed:', error);
      // Don't show API key error since we're using server-side key
      const errorMessage = error.message.includes('API key') 
        ? 'The AI service is temporarily unavailable. Please try again later or contact support.'
        : `Error: ${error.message}`;
      addAssistantMessage(errorMessage);
    }
  };

  // Helper to update chat title
  const updateChatTitle = (title) => {
    if (currentChatId !== 'new') {
      setChatSessions(prev => prev.map(chat => 
        chat.id === currentChatId 
          ? { ...chat, title } 
          : chat
      ));
    }
  };
  
  // Helper to add assistant messages
  const addAssistantMessage = (content) => {
    const assistantMessage = {
      id: Date.now(),
      sender: 'assistant',
      content,
      timestamp: new Date().toISOString()
    };
    
    setMessages(prev => [...prev, assistantMessage]);
  };
  
  // Create a new chat
  const handleNewChat = () => {
    setMessages([]);
    setCurrentChatId('new');
  };
  
  // Select an existing chat
  const handleSelectChat = (chatId) => {
    // In a real app, you would load the chat messages from an API
    // For demo purposes, we'll just show the welcome message
    setMessages([]);
    setCurrentChatId(chatId);
    setShowChatHistory(false);
    
    // Simulate loading messages
    setTimeout(() => {
      addAssistantMessage("I've loaded your previous chat. How can I help you today?");
    }, 500);
  };
  
  // Format timestamp for display
  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };
  
  // Simulate call progress for demo purposes
  const simulateCallProgress = (call) => {
    // Simulate ringing after 1 second
    setTimeout(() => {
      const updatedCall = { ...call, status: 'ringing' };
      setCurrentCall(updatedCall);
    }, 1000);
    
    // Simulate in-progress after 3 seconds
    setTimeout(() => {
      const updatedCall = { ...call, status: 'in-progress' };
      setCurrentCall(updatedCall);
    }, 3000);
    
    // Simulate completed after 8 seconds
    setTimeout(() => {
      const updatedCall = { ...call, status: 'completed', duration: 5 };
      setCurrentCall(updatedCall);
    }, 8000);
  };
  
  return (
    <div className="flex flex-col h-full">
      <ToastContainer />
      
      {/* Chat history dropdown */}
      <div className="relative mb-4">
        <div 
          className="flex items-center justify-between bg-gray-800 p-3 rounded-md cursor-pointer border border-gray-700 hover:bg-gray-750 transition-colors"
          onClick={() => setShowChatHistory(!showChatHistory)}
        >
          <div className="flex items-center">
            <FiMessageSquare className="mr-2 text-blue-400" />
            <span className="font-medium">
              {currentChatId === 'new' 
                ? 'New Chat' 
                : chatSessions.find(c => c.id === currentChatId)?.title || 'Current Chat'}
            </span>
          </div>
          {showChatHistory ? <FiChevronUp /> : <FiChevronDown />}
        </div>
        
        {/* Dropdown content */}
        {showChatHistory && (
          <div className="absolute z-10 w-full mt-1 bg-gray-800 rounded-md border border-gray-700 shadow-lg">
            <div className="p-2">
              <button 
                className="flex items-center w-full py-2 px-3 hover:bg-gray-700 rounded-md transition-colors mb-1"
                onClick={handleNewChat}
              >
                <FiPlus className="mr-2 text-green-400" />
                <span>New Chat</span>
              </button>
              
              <div className="border-t border-gray-700 my-1"></div>
              
              {/* Chat history list */}
              <div className="max-h-60 overflow-y-auto">
                {chatSessions.map(chat => (
                  <button 
                    key={chat.id}
                    className={`flex items-center justify-between w-full py-2 px-3 hover:bg-gray-700 rounded-md transition-colors ${
                      chat.id === currentChatId ? 'bg-gray-700' : ''
                    }`}
                    onClick={() => handleSelectChat(chat.id)}
                  >
                    <div className="flex items-center">
                      <FiArchive className="mr-2 text-gray-400" />
                      <span className="truncate max-w-[200px] text-left">{chat.title}</span>
                    </div>
                    <span className="text-xs text-gray-400">{formatTimestamp(chat.timestamp)}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Main chat section */}
      <div className="flex-1 flex flex-col space-y-4 overflow-hidden">
        {/* Chat container - fixed height with its own scrolling */}
        <div className="flex-1 bg-gray-900/50 rounded-lg border border-gray-700 shadow-lg overflow-hidden">
          <div className="border-b border-gray-700 bg-gray-800 p-3 flex items-center justify-between">
            <h3 className="font-medium flex items-center">
              <FiMessageSquare className="mr-2 text-blue-400" />
              CallSaver AI
            </h3>
            <span className="text-xs text-gray-400">Connected to {phoneNumber}</span>
          </div>
          
          <div 
            ref={chatContainerRef}
            className="h-[400px] overflow-y-auto p-4 space-y-6"
          >
            {/* Welcome message - always show this */}
            <div className="flex items-start">
              <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center mr-3 flex-shrink-0">
                <span className="text-white font-semibold">AI</span>
              </div>
              <div className="bg-gray-800 rounded-lg p-3 max-w-[85%] shadow-md border border-gray-700/50">
                <ReactMarkdown className="prose prose-invert prose-sm max-w-none">
                  {`Welcome to CallSaver AI! I'm powered by GPT-4-turbo and can help with:

- Answering questions about CallSaver features
- Providing telecom and automation advice  
- Processing both slash commands and natural language

You can type:
- \`/call +16205268448 Say: Hello there\` 
- Or simply: \`call +16205268448 and say hello there\`

Try asking me something or use \`/help\` to see all commands!`}
                </ReactMarkdown>
                <div className="mt-2 text-xs text-gray-400 flex items-center">
                  <FiClock className="mr-1" /> just now
                </div>
              </div>
            </div>
            
            {/* Chat messages */}
            {messages.map(message => (
              <div 
                key={message.id} 
                className={`flex items-start ${message.sender === 'user' ? 'justify-end' : ''}`}
              >
                {message.sender === 'assistant' && (
                  <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center mr-3 flex-shrink-0">
                    <span className="text-white font-semibold">AI</span>
                  </div>
                )}
                
                <div 
                  className={`rounded-lg p-3 max-w-[85%] shadow-md ${
                    message.sender === 'user' 
                      ? 'bg-purple-600 text-white border border-purple-500/50' 
                      : message.content.startsWith('Initiating call') || message.content.startsWith('Sending SMS') || message.content.startsWith('Call to') || message.content.startsWith('SMS to')
                        ? 'bg-gray-700 border border-gray-600/50' // Command responses get a different style
                        : 'bg-gray-800 border border-gray-700/50'
                  }`}
                >
                  {/* Use ReactMarkdown for rich text rendering */}
                  {message.sender === 'assistant' ? (
                    <ReactMarkdown
                      className="prose prose-invert prose-sm max-w-none"
                      components={{
                        code({node, inline, className, children, ...props}) {
                          const match = /language-(\w+)/.exec(className || '')
                          return !inline && match ? (
                            <SyntaxHighlighter
                              style={atomDark}
                              language={match[1]}
                              PreTag="div"
                              {...props}
                            >
                              {String(children).replace(/\n$/, '')}
                            </SyntaxHighlighter>
                          ) : (
                            <code className="bg-gray-700 px-1 py-0.5 rounded text-green-400" {...props}>
                              {children}
                            </code>
                          )
                        }
                      }}
                    >
                      {message.content}
                    </ReactMarkdown>
                  ) : (
                    <div>{message.content}</div>
                  )}
                  
                  <div className="mt-2 text-xs text-gray-400 flex items-center">
                    <FiClock className="mr-1" /> just now
                  </div>
                </div>
                
                {message.sender === 'user' && (
                  <div className="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center ml-3 flex-shrink-0">
                    <span className="text-white font-semibold">U</span>
                  </div>
                )}
              </div>
            ))}
            
            {/* Processing indicator */}
            {isProcessing && (
              <div className="flex items-center justify-center py-2">
                <div className="animate-pulse flex items-center">
                  <FiLoader className="animate-spin mr-2 text-blue-400" />
                  <span className="text-sm text-gray-400">Processing...</span>
                </div>
              </div>
            )}
            
            {/* Current call status - only show the most recent active call */}
            {currentCall && currentCall.status !== 'completed' && (
              <div className="my-4">
                <CallStatusDisplay call={currentCall} />
              </div>
            )}
          </div>
        </div>
        
        {/* Command input */}
        <div className="bg-gray-900/50 rounded-lg border border-gray-700 shadow-lg p-3">
          <CommandInput 
            onSendCommand={handleSendCommand}
            isProcessing={isProcessing}
          />
        </div>
      </div>
    </div>
  );
}
