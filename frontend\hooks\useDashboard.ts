import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { useDashboardStore, DashboardSummary, RecentActivity, AIInsight } from '../stores/dashboardStore';
import { dashboardMockData } from '../lib/mockData';

// Create an axios instance for API calls
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Flag to determine if we should use mock data
const USE_MOCK_DATA = process.env.NODE_ENV === 'development';

/**
 * Hook for fetching and managing dashboard data
 */
export function useDashboard() {
  const queryClient = useQueryClient();
  const dashboardStore = useDashboardStore();

  // Fetch dashboard summary
  const dashboardSummaryQuery = useQuery({
    queryKey: ['dashboard', 'summary'],
    queryFn: async () => {
      if (USE_MOCK_DATA) {
        // Return mock data with a slight delay to simulate API call
        await new Promise(resolve => setTimeout(resolve, 500));
        const data = dashboardMockData.summary;
        dashboardStore.setSummary(data as DashboardSummary);
        return data as DashboardSummary;
      }
      const { data } = await api.get<DashboardSummary>('/dashboard/summary');
      dashboardStore.setSummary(data);
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch recent activity
  const recentActivityQuery = useQuery({
    queryKey: ['dashboard', 'recent-activity'],
    queryFn: async () => {
      if (USE_MOCK_DATA) {
        // Return mock data with a slight delay to simulate API call
        await new Promise(resolve => setTimeout(resolve, 600));
        const data = dashboardMockData.recentActivity;
        dashboardStore.setRecentActivity(data as RecentActivity[]);
        return data as RecentActivity[];
      }
      const { data } = await api.get<RecentActivity[]>('/dashboard/recent-activity');
      dashboardStore.setRecentActivity(data);
      return data;
    },
    staleTime: 60 * 1000, // 1 minute
  });

  // Fetch AI insights
  const aiInsightsQuery = useQuery({
    queryKey: ['dashboard', 'ai-insights'],
    queryFn: async () => {
      if (USE_MOCK_DATA) {
        // Return mock data with a slight delay to simulate API call
        await new Promise(resolve => setTimeout(resolve, 700));
        const data = dashboardMockData.aiInsights;
        dashboardStore.setAIInsights(data as AIInsight[]);
        return data as AIInsight[];
      }
      const { data } = await api.get<AIInsight[]>('/dashboard/ai-insights');
      dashboardStore.setAIInsights(data);
      return data;
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
  });

  // Refresh all dashboard data
  const refreshDashboard = async () => {
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'summary'] }),
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'recent-activity'] }),
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'ai-insights'] }),
    ]);
  };

  // Dismiss AI Insight
  const dismissInsightMutation = useMutation({
    mutationFn: async (insightId: string) => {
      if (USE_MOCK_DATA) {
        // Simulate API call with a delay
        await new Promise(resolve => setTimeout(resolve, 500));
        return { data: { success: true } };
      }
      return api.post(`/dashboard/ai-insights/${insightId}/dismiss`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'ai-insights'] });
    },
  });

  // Map the dashboard summary data to match our component expectations
  const mappedSummaryData = dashboardSummaryQuery.data ? {
    totalCallsToday: dashboardSummaryQuery.data.answeredCalls + dashboardSummaryQuery.data.missedCalls || 0,
    totalCallsWeek: dashboardSummaryQuery.data.totalCalls || 0,
    totalMessagesToday: 0, // This will need to be added to the API response
    activeAutomations: 0, // This will need to be added to the API response
    creditBalance: dashboardSummaryQuery.data.creditBalance || 0
  } : null;

  // Map the recent activity data to match our component expectations
  const mappedActivityData = recentActivityQuery.data ? recentActivityQuery.data.map(activity => ({
    id: activity.id,
    type: activity.type === 'credit' ? 'alert' : activity.type, // Map 'credit' to 'alert' for our component
    timestamp: activity.timestamp,
    details: activity.description,
    link: `/activity/${activity.id}`
  })) : [];

  // Map the AI insights data to match our component expectations
  const mappedInsightsData = aiInsightsQuery.data ? aiInsightsQuery.data.map(insight => ({
    id: insight.id,
    timestamp: insight.timestamp,
    summary: insight.description,
    link: insight.relatedNumberId ? `/numbers/${insight.relatedNumberId}` : undefined
  })) : [];

  return {
    // Query results with mapped data
    summary: {
      data: mappedSummaryData,
      isLoading: dashboardSummaryQuery.isLoading,
      error: dashboardSummaryQuery.error,
    },
    recentActivity: {
      data: mappedActivityData,
      isLoading: recentActivityQuery.isLoading,
      error: recentActivityQuery.error,
    },
    aiInsights: {
      data: mappedInsightsData,
      isLoading: aiInsightsQuery.isLoading,
      error: aiInsightsQuery.error,
    },

    // Actions
    refreshDashboard,
    dismissInsight: dismissInsightMutation.mutate,

    // Combined loading state
    isLoading:
      dashboardSummaryQuery.isLoading ||
      recentActivityQuery.isLoading ||
      aiInsightsQuery.isLoading,
  };
}
