'use client';

import { RadioGroup } from '@headlessui/react';
import { CheckCircleIcon } from '@heroicons/react/24/solid';

interface AIPersonalityOption {
  id: string;
  name: string;
  description: string;
}

interface AIPersonalitySelectorProps {
  selectedPersonality: string;
  onChange: (personality: string) => void;
  disabled?: boolean;
}

// Personality options
const personalities: AIPersonalityOption[] = [
  {
    id: 'professional',
    name: 'Professional',
    description: 'Formal, business-like tone. Ideal for corporate environments.',
  },
  {
    id: 'friendly',
    name: 'Friendly',
    description: 'Warm and approachable. Good for customer service.',
  },
  {
    id: 'casual',
    name: 'Casual',
    description: 'Relaxed and conversational. Suitable for informal businesses.',
  },
  {
    id: 'technical',
    name: 'Technical',
    description: 'Precise and detailed. Best for technical support or specialized fields.',
  },
];

export default function AIPersonalitySelector({
  selectedPersonality,
  onChange,
  disabled = false,
}: AIPersonalitySelectorProps) {
  return (
    <RadioGroup value={selectedPersonality} onChange={onChange} disabled={disabled}>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        {personalities.map((personality) => (
          <RadioGroup.Option
            key={personality.id}
            value={personality.id}
            className={({ active, checked }) =>
              `${
                active
                  ? 'ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-800'
                  : ''
              }
              ${
                checked
                  ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                  : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
              }
              ${
                disabled
                  ? 'opacity-50 cursor-not-allowed'
                  : 'cursor-pointer'
              }
                relative rounded-lg border p-4 shadow-sm focus:outline-none`
            }
          >
            {({ checked }) => (
              <>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="text-sm">
                      <RadioGroup.Label
                        as="p"
                        className={`font-medium ${
                          checked
                            ? 'text-blue-900 dark:text-blue-300'
                            : 'text-gray-900 dark:text-white'
                        }`}
                      >
                        {personality.name}
                      </RadioGroup.Label>
                      <RadioGroup.Description
                        as="span"
                        className={`inline ${
                          checked
                            ? 'text-blue-700 dark:text-blue-400'
                            : 'text-gray-500 dark:text-gray-400'
                        }`}
                      >
                        <span>{personality.description}</span>
                      </RadioGroup.Description>
                    </div>
                  </div>
                  {checked && (
                    <div className="shrink-0 text-blue-600 dark:text-blue-400">
                      <CheckCircleIcon className="h-6 w-6" />
                    </div>
                  )}
                </div>
              </>
            )}
          </RadioGroup.Option>
        ))}
      </div>
    </RadioGroup>
  );
}
