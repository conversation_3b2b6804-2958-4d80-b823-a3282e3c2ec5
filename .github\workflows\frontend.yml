name: Frontend Workflow

on:
  workflow_call:
    secrets:
      RAILWAY_FRONTEND_TOKEN:
        required: true

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./frontend
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'
          cache-dependency-path: './frontend/package-lock.json'

      - name: Install dependencies
        run: npm ci

      - name: Install ESLint plugins
        run: node install-eslint-plugins.js

      - name: Run linting
        run: npm run lint

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: lint
    defaults:
      run:
        working-directory: ./frontend
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'
          cache-dependency-path: './frontend/package-lock.json'

      - name: Install dependencies
        run: npm ci

      - name: Generate Prisma Client
        run: npx prisma generate

      - name: Build Next.js app
        run: npm run build

      - name: Cache build output
        uses: actions/cache@v3
        with:
          path: |
            frontend/.next
            frontend/node_modules/.prisma
          key: ${{ runner.os }}-nextjs-${{ github.sha }}

  deploy:
    name: Deploy to Railway
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    defaults:
      run:
        working-directory: ./frontend
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Restore build cache
        uses: actions/cache@v3
        with:
          path: |
            frontend/.next
            frontend/node_modules/.prisma
          key: ${{ runner.os }}-nextjs-${{ github.sha }}

      - name: Install Railway CLI
        run: npm install -g @railway/cli

      - name: Deploy to Railway
        run: railway up
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_FRONTEND_TOKEN }}
