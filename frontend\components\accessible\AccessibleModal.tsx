import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { twMerge } from 'tailwind-merge';
import AccessibleButton from './AccessibleButton';

export interface AccessibleModalProps {
  /**
   * Whether the modal is open
   */
  isOpen: boolean;
  
  /**
   * Function to close the modal
   */
  onClose: () => void;
  
  /**
   * The title of the modal
   */
  title: string;
  
  /**
   * The content of the modal
   */
  children: React.ReactNode;
  
  /**
   * The size of the modal
   */
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  
  /**
   * Additional CSS classes to apply to the modal
   */
  className?: string;
  
  /**
   * Whether to show the close button
   */
  showCloseButton?: boolean;
  
  /**
   * Whether to close the modal when clicking outside
   */
  closeOnClickOutside?: boolean;
  
  /**
   * Whether to close the modal when pressing the Escape key
   */
  closeOnEsc?: boolean;
  
  /**
   * Whether to render the modal in a portal
   */
  usePortal?: boolean;
  
  /**
   * The ID of the element to render the portal in
   */
  portalId?: string;
}

/**
 * An accessible modal component with proper focus management and keyboard support.
 */
export const AccessibleModal: React.FC<AccessibleModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  className,
  showCloseButton = true,
  closeOnClickOutside = true,
  closeOnEsc = true,
  usePortal = true,
  portalId = 'modal-root',
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const previousFocusRef = useRef<HTMLElement | null>(null);
  
  // Size-specific classes
  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    full: 'max-w-full',
  };
  
  // Handle Escape key press
  useEffect(() => {
    if (!isOpen || !closeOnEsc) return;
    
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose, closeOnEsc]);
  
  // Handle focus management
  useEffect(() => {
    if (!isOpen) return;
    
    // Save the currently focused element
    previousFocusRef.current = document.activeElement as HTMLElement;
    
    // Focus the modal
    if (modalRef.current) {
      const focusableElements = modalRef.current.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      if (focusableElements.length > 0) {
        (focusableElements[0] as HTMLElement).focus();
      } else {
        modalRef.current.focus();
      }
    }
    
    // Restore focus when the modal is closed
    return () => {
      if (previousFocusRef.current) {
        previousFocusRef.current.focus();
      }
    };
  }, [isOpen]);
  
  // Handle click outside
  const handleBackdropClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!closeOnClickOutside) return;
    
    if (event.target === event.currentTarget) {
      onClose();
    }
  };
  
  // Create the modal content
  const modalContent = isOpen ? (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      onClick={handleBackdropClick}
      aria-modal="true"
      role="dialog"
      aria-labelledby="modal-title"
    >
      <div
        ref={modalRef}
        className={twMerge(
          'bg-white rounded-lg shadow-xl overflow-hidden w-full m-4',
          sizeClasses[size],
          className
        )}
        tabIndex={-1}
      >
        <div className="flex justify-between items-center p-4 border-b">
          <h2 id="modal-title" className="text-lg font-semibold">
            {title}
          </h2>
          {showCloseButton && (
            <AccessibleButton
              variant="ghost"
              size="sm"
              onClick={onClose}
              aria-label="Close modal"
              className="p-1 rounded-full"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </AccessibleButton>
          )}
        </div>
        <div className="p-4">{children}</div>
      </div>
    </div>
  ) : null;
  
  // Render the modal
  if (!isOpen) {
    return null;
  }
  
  // Use portal if specified
  if (usePortal) {
    // Create portal root if it doesn't exist
    let portalRoot = document.getElementById(portalId);
    if (!portalRoot) {
      portalRoot = document.createElement('div');
      portalRoot.id = portalId;
      document.body.appendChild(portalRoot);
    }
    
    return createPortal(modalContent, portalRoot);
  }
  
  return modalContent;
};

export default AccessibleModal;
