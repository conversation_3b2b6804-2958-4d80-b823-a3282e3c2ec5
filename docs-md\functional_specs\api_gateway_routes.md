---
description:
globs:
alwaysApply: false
---
# API Gateway Routes Functional Document (`api_gateway_routes.mdc`)

## 1. Purpose and Scope

**Purpose:** Define the public-facing REST API routes exposed by the CallSaver backend. This acts as the single entry point for the frontend application and external API consumers (using API keys). It routes requests to the appropriate internal microservices and ensures consistent authentication, authorization, and rate limiting.

**Scope:**
- List all major public API endpoints required by the frontend dashboard sections.
- Specify HTTP methods (GET, POST, PUT, DELETE), URL paths, and expected request/response structures (briefly).
- Define authentication mechanisms (e.g., session cookies for frontend, Bearer tokens for API keys).
- Outline authorization logic (role-based access control enforced at the gateway or downstream service).
- Mention rate limiting strategies.
- Map routes to the responsible backend services described in other `.mdc` files.

## 2. Authentication & Authorization

- **Frontend Authentication:** Use secure HTTP-only cookies containing session tokens, established during login. The gateway validates the session on each request.
- **API Key Authentication:** Accept API keys via the `Authorization: Bearer <API_KEY>` header. The gateway validates the key against stored keys (User Service/DB).
- **Authorization:**
    - The gateway (or downstream service) checks the user's role (obtained from session/API key lookup) against the required permissions for the requested route/action.
    - Return HTTP 401 (Unauthorized) for invalid/missing credentials.
    - Return HTTP 403 (Forbidden) for insufficient permissions.

## 3. Rate Limiting

- Implement rate limiting based on user ID and/or API key.
- Apply different limits for different types of endpoints (e.g., higher limits for reading data, lower limits for expensive operations like number purchasing or AI processing triggers).
- Return HTTP 429 (Too Many Requests) when limits are exceeded.

## 4. API Route Definitions

*This list consolidates endpoints defined across other functional documents.*

**Authentication (`/api/auth`) -> Auth Service**
- `POST /api/auth/login`: User login (email/password). Returns session cookie.
- `POST /api/auth/register`: User registration.
- `POST /api/auth/logout`: Clears session cookie.
- `POST /api/auth/refresh-token`: (If using JWTs) Refresh access token.
- `GET /api/auth/google`: Initiate Google OAuth flow.
- `GET /api/auth/google/callback`: Handle Google OAuth callback.

**User & Organization (`/api/users`, `/api/organization`) -> User Service**
- `GET /api/users/me`: Get current user profile, role, basic subscription status.
- `PUT /api/users/me`: Update user profile.
- `POST /api/users/me/change-password`: Change password.
- `GET /api/users/me/notifications`: Get notification preferences.
- `PUT /api/users/me/notifications`: Update notification preferences.
- `GET /api/organization/users`: List users in the organization (Admin only).
- `POST /api/organization/invitations`: Invite user (Admin only).
- `DELETE /api/organization/invitations/{invitationId}`: Cancel invitation (Admin only).
- `POST /api/organization/invitations/{invitationId}/resend`: Resend invitation (Admin only).
- `PUT /api/organization/users/{userId}/role`: Change user role (Admin only).
- `DELETE /api/organization/users/{userId}`: Remove user (Admin only).
- `GET /api/organization`: Get organization settings (Admin only).
- `PUT /api/organization`: Update organization settings (Admin only).

**API Keys (`/api/api-keys`) -> User Service**
- `GET /api/api-keys`: List user's API keys (masked).
- `POST /api/api-keys`: Generate a new API key.
- `DELETE /api/api-keys/{keyId}`: Revoke an API key.

**Dashboard (`/api/dashboard`) -> Aggregation Layer / Multiple Services**
- `GET /api/dashboard/summary`: Get key metrics overview. (Analytics, Billing Services)
- `GET /api/dashboard/recent-activity`: Get recent calls, messages, etc. (Analytics Service)
- `GET /api/dashboard/ai-insights`: Get AI summaries/alerts. (AI Service)

**Number Management (`/api/numbers`, `/api/esims`) -> Number Provisioning Service**
- `GET /api/numbers/available`: Search available traditional numbers.
- `POST /api/numbers/purchase`: Purchase a traditional number.
- `GET /api/numbers/owned`: List owned traditional numbers.
- `PUT /api/numbers/{phoneNumberId}`: Update traditional number settings.
- `DELETE /api/numbers/{phoneNumberId}`: Release a traditional number.
- `GET /api/esims/available`: Search available eSIM plans.
- `POST /api/esims/purchase`: Purchase an eSIM plan.
- `GET /api/esims/owned`: List owned eSIMs.
- `GET /api/esims/{esimPurchaseId}/activation`: Get eSIM activation details.
- `GET /api/esims/{esimInstanceId}/usage`: Get eSIM usage data.

**Automation (`/api/numbers/{phoneNumberId}/automations`, `/api/numbers/{phoneNumberId}/ai-assistant`) -> Automation Config Service / AI Service**
- `GET /api/numbers/{phoneNumberId}/automations`: Get automation rules for a number.
- `PUT /api/numbers/{phoneNumberId}/automations`: Update automation rules.
- `GET /api/numbers/{phoneNumberId}/ai-assistant`: Get AI assistant config.
- `PUT /api/numbers/{phoneNumberId}/ai-assistant`: Update AI assistant config.
- `POST /api/numbers/{phoneNumberId}/ai-assistant/train`: Submit AI training data.
- `GET /api/numbers/{phoneNumberId}/automation-logs`: Get automation-specific logs.
- `POST /api/numbers/{phoneNumberId}/automations/toggle`: Enable/disable automation.

**Call Logs (`/api/call-logs`) -> Call Logging Service / AI Service**
- `GET /api/call-logs`: List/filter/search call records.
- `GET /api/call-logs/{callSid}`: Get details for a single call.
- `GET /api/call-logs/{callSid}/recording`: Get call recording URL/stream.
- `GET /api/call-logs/{callSid}/transcription`: Get call transcription. (AI Service)
- `GET /api/call-logs/{callSid}/ai-summary`: Get AI summary/sentiment. (AI Service)
- `POST /api/call-logs/{callSid}/notes`: Add/update user notes.
- `POST /api/call-logs/{callSid}/tags`: Add/remove tags.

**Blocklist (`/api/blocklist`) -> User Service / Call Handling Logic**
- `POST /api/blocklist`: Add a number to the blocklist.
- `GET /api/blocklist`: List blocked numbers.
- `DELETE /api/blocklist/{number}`: Remove a number from the blocklist.

**Analytics (`/api/analytics`) -> Analytics Service**
- `GET /api/analytics/overview`: Get summary analytics.
- `GET /api/analytics/calls`: Get call analytics data.
- `GET /api/analytics/messages`: Get message analytics data.
- `GET /api/analytics/voicemails`: Get voicemail analytics data.
- `GET /api/analytics/ai`: Get AI interaction analytics.
- `GET /api/analytics/usage`: Get credit usage analytics.
- `GET /api/analytics/export`: Export analytics data.

**Billing (`/api/billing`) -> Billing Service**
- `GET /api/billing/balance`: Get current credit balance.
- `GET /api/billing/transactions`: Get transaction history.
- `GET /api/billing/subscription`: Get subscription status.
- `GET /api/billing/credit-packs`: Get available credit packs.
- `POST /api/billing/checkout-session`: Create Stripe checkout session.
- `GET /api/billing/portal-url`: Get Stripe customer portal link.

**Notifications (`/api/notifications`) -> Notification Service**
- `GET /api/notifications`: Get notifications for in-app center.
- `POST /api/notifications/mark-read`: Mark notifications as read.
- `POST /api/notifications/dismiss`: Dismiss notifications.
- `GET /api/notifications/unread-count`: Get unread notification count.

**Appointments (`/api/availability`, `/api/appointments`) -> Appointment Scheduling Service**
- `GET /api/availability`: Get availability rules.
- `PUT /api/availability`: Update availability rules.
- `POST /api/availability/block-time`: Block a specific time.
- `DELETE /api/availability/block-time/{blockId}`: Unblock a time.
- `GET /api/appointments`: List appointments in range.
- `GET /api/appointments/{appointmentId}`: Get appointment details.
- `POST /api/appointments`: Create an appointment.
- `PUT /api/appointments/{appointmentId}`: Update an appointment.
- `DELETE /api/appointments/{appointmentId}`: Delete an appointment.

**Help Center (`/api/help`, `/api/support`) -> CMS / Support System Interface**
- `GET /api/help/articles`: Search/list help articles.
- `GET /api/help/articles/{articleSlug}`: Get specific article content.
- `GET /api/help/categories`: List help categories.
- `POST /api/support/tickets`: Submit a support request form.
- `GET /api/support/chat/config`: Get live chat configuration.

**Webhooks (Public Ingress, but not directly called by users)**
- `POST /webhooks/stripe`: Receive Stripe events. -> Billing Service
- `POST /webhooks/twilio/voice`: Receive Twilio call events. -> AI Service / Call Handling
- `POST /webhooks/twilio/message`: Receive Twilio SMS events. -> AI Service / Message Handling
- `POST /webhooks/esim/event`: Receive eSIM provider events. -> Number Provisioning / AI Service

## 5. Logging and Monitoring

- The API Gateway should log every incoming request and its corresponding response (status code, latency).
- Include correlation IDs to trace requests across different services.
- Monitor request volume, error rates (4xx, 5xx), and latency per route.
- Set up alerts for high error rates or latency spikes.

## 6. Versioning Strategy

- Consider API versioning from the start (e.g., `/api/v1/...`).
- Plan how breaking changes will be introduced (new version, deprecation policy).

## 7. Documentation

- Maintain API documentation (e.g., using OpenAPI/Swagger) automatically generated or manually kept in sync with this specification.
- Documentation should clearly define request/response schemas, authentication methods, and required permissions for each endpoint.
