"use client";

import React, { useState } from 'react';
import { LargeDonutChartComponent } from './ChartComponents';

const CallsSavedPanel = ({ timeRange = '7days' }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Time range specific data
  const getCallsData = () => {
    switch(timeRange) {
      case 'today':
        return [
          { name: 'AI Resolved', value: 87, color: '#8b5cf6' },
          { name: 'Agent Assisted', value: 34, color: '#60a5fa' },
          { name: 'Manual Handling', value: 15, color: '#f43f5e' }
        ];
      case '7days':
        return [
          { name: 'AI Resolved', value: 352, color: '#8b5cf6' },
          { name: 'Agent Assisted', value: 148, color: '#60a5fa' },
          { name: 'Manual Handling', value: 86, color: '#f43f5e' }
        ];
      case '30days':
        return [
          { name: 'AI Resolved', value: 1548, color: '#8b5cf6' },
          { name: 'Agent Assisted', value: 623, color: '#60a5fa' },
          { name: 'Manual Handling', value: 289, color: '#f43f5e' }
        ];
      case '90days':
        return [
          { name: 'AI Resolved', value: 4682, color: '#8b5cf6' },
          { name: 'Agent Assisted', value: 1834, color: '#60a5fa' },
          { name: 'Manual Handling', value: 941, color: '#f43f5e' }
        ];
      default:
        return [
          { name: 'AI Resolved', value: 352, color: '#8b5cf6' },
          { name: 'Agent Assisted', value: 148, color: '#60a5fa' },
          { name: 'Manual Handling', value: 86, color: '#f43f5e' }
        ];
    }
  };
  
  // Get time range specific timeline display
  const getTimeRangeDisplay = () => {
    switch(timeRange) {
      case 'today':
        return 'Today';
      case '7days':
        return 'Last 7 days';
      case '30days':
        return 'Last 30 days';
      case '90days':
        return 'Last 90 days';
      default:
        return 'Last 7 days';
    }
  };
  
  // Mock data for calls saved stats based on time range
  const callsData = getCallsData();

  // Calculate total calls and percentage resolved by AI
  const totalCalls = callsData.reduce((sum, item) => sum + item.value, 0);
  const aiResolvedPercentage = ((callsData[0].value / totalCalls) * 100).toFixed(1);
  
  // Calculate the growth rate (mocked)
  const growthRate = timeRange === 'today' ? '+5.2%' : 
                    timeRange === '7days' ? '+8.2%' : 
                    timeRange === '30days' ? '+12.3%' : '+15.7%';

  // Calculate time saved based on average call duration of 4.5 minutes per call
  const avgCallDuration = 4.5; // minutes
  const timeSaved = (callsData[0].value * avgCallDuration / 60).toFixed(1); // in hours
  
  // Detailed metrics for expanded view
  const detailedMetrics = [
    { metric: 'SMS Chats Started', value: '843', change: '+12.8%', positive: true, explanation: 'Number of SMS threads started after a missed call' },
    { metric: 'AI Resolution Rate', value: `${aiResolvedPercentage}%`, change: '+3.2%', positive: true, explanation: 'Percentage of calls fully resolved by AI without human intervention' },
    { metric: 'Time to Resolution', value: '2.8 min', change: '-0.5 min', positive: true, explanation: 'Average time from call start to issue resolution' },
    { metric: 'Cost per Resolution', value: '$0.82', change: '-$0.15', positive: true, explanation: 'Average cost to resolve a customer issue' },
  ];

  return (
    <div className="bg-gray-900 p-6 rounded-xl border border-gray-700 h-full shadow-md shadow-black/20 relative">
      {/* Expand toggle button */}
      <button 
        onClick={() => setIsExpanded(!isExpanded)}
        className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
        aria-label={isExpanded ? "Collapse panel" : "Expand panel"}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          {isExpanded ? (
            <path fillRule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clipRule="evenodd" />
          ) : (
            <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
          )}
        </svg>
      </button>
      
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-white">Calls Saved</h3>
        <div className="flex items-center">
          <div className="text-xs text-gray-300 mr-2">{getTimeRangeDisplay()}</div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="bg-purple-900 rounded-lg p-4 shadow-md shadow-purple-900/30 group hover:bg-purple-800 transition-colors cursor-pointer relative">
          <div className="text-sm text-gray-300 mb-1">Total calls</div>
          <div className="text-2xl font-bold text-white">{totalCalls}</div>
          <div className="text-xs text-gray-400 mt-1 flex items-center">
            <span className="text-green-400">{growthRate}</span>
            <span className="mx-1">from previous period</span>
          </div>
          <div className="absolute top-2 right-2 bg-purple-800 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-200" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
        <div className="bg-green-900 rounded-lg p-4 shadow-md shadow-green-900/30 group hover:bg-green-800 transition-colors cursor-pointer relative">
          <div className="text-sm text-gray-300 mb-1">Time saved</div>
          <div className="text-2xl font-bold text-white">{timeSaved} hours</div>
          <div className="text-xs text-gray-400 mt-1">~{(timeSaved * 60).toFixed(0)} minutes total</div>
          <div className="absolute top-2 right-2 bg-green-800 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-200" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
      </div>
      
      <div className="flex flex-col md:flex-row items-center">
        <div className={`h-48 w-48 ${isExpanded ? 'h-56 w-56' : ''} transition-all duration-300`}>
          <LargeDonutChartComponent 
            data={callsData} 
            innerRadius={isExpanded ? 40 : 30} 
            outerRadius={isExpanded ? 80 : 70}
          />
        </div>
        
        <div className="flex-1 ml-0 md:ml-4 mt-4 md:mt-0 flex flex-col justify-center">
          <div className="mb-6">
            <div className="text-xl font-bold gradient-text flex items-center">
              {aiResolvedPercentage}%
              <span className="ml-2 bg-green-900 text-green-300 text-xs px-2 py-0.5 rounded-full">+3.2%</span>
            </div>
            <div className="text-sm text-gray-300 flex items-center">
              <span>Call automation rate</span>
              <div className="relative group ml-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="absolute -top-2 left-6 w-48 p-2 bg-gray-800 rounded shadow-lg border border-purple-700 text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10">
                  Percentage of total calls handled entirely by AI without human intervention.
                </div>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            {callsData.map((item, index) => (
              <div 
                key={index} 
                className="flex items-center bg-gray-800 p-2 rounded-lg hover:bg-gray-700 transition-colors cursor-pointer relative group"
              >
                <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: item.color }}></div>
                <div className="text-sm text-gray-200">{item.name}</div>
                <div className="ml-auto text-sm font-medium text-white">{item.value}</div>
                <div className="absolute right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <span className="text-xs bg-gray-900 px-1.5 py-0.5 rounded-full">
                    {((item.value / totalCalls) * 100).toFixed(0)}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {isExpanded && (
        <div className="mt-6 pt-4 border-t border-gray-700 animate-fadeIn">
          <h4 className="text-sm font-medium text-white mb-3">Performance Metrics</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {detailedMetrics.map((metric, index) => (
              <div key={index} className="bg-gray-800 p-3 rounded-lg relative group hover:bg-gray-700 transition-colors cursor-pointer">
                <div className="flex justify-between">
                  <div className="text-sm text-gray-300">{metric.metric}</div>
                  <div className={`text-xs px-1.5 py-0.5 rounded-full ${metric.positive ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'}`}>
                    {metric.change}
                  </div>
                </div>
                <div className="text-lg font-medium text-white mt-1">{metric.value}</div>
                <div className="absolute bottom-0 left-0 right-0 bg-gray-900 p-2 rounded-b-lg transform translate-y-full opacity-0 group-hover:opacity-100 pointer-events-none transition-all z-10 text-xs text-gray-300">
                  {metric.explanation}
                </div>
              </div>
            ))}
          </div>
          <div className="mt-4 text-right">
            <button className="text-sm text-purple-400 hover:text-purple-300 transition-colors px-2 py-1 rounded hover:bg-purple-900/30">
              View Detailed Report →
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default CallsSavedPanel; 