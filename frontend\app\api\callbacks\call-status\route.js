import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    // Parse request body
    const formData = await request.formData();
    
    // Extract call status details from Twilio webhook
    const callSid = formData.get('CallSid');
    const callStatus = formData.get('CallStatus');
    const callDuration = formData.get('CallDuration');
    
    console.log(`Call status update for ${callSid}: ${callStatus}, duration: ${callDuration || 'N/A'}`);
    
    // In production, this would update the call record in the database
    // const updatedCall = await prisma.callLog.update({
    //   where: { callSid },
    //   data: {
    //     status: callStatus.toLowerCase(),
    //     duration: callDuration ? parseInt(callDuration) : null
    //   }
    // });
    
    // For demo purposes, we'll just return a success response
    return NextResponse.json({
      success: true,
      message: `Call status updated to ${callStatus}`
    });
  } catch (error) {
    console.error('Error processing call status callback:', error);
    return NextResponse.json(
      { success: false, message: `Failed to process call status: ${error.message}` },
      { status: 500 }
    );
  }
} 