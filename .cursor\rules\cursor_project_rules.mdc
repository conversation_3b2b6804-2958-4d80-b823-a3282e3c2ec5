---
description: 
globs: 
alwaysApply: false
---
---
description: Core project-wide development rules and standards for CallSaver
globs: ["**/*"]
alwaysApply: true
version: 1.0.0
---

# CallSaver Project Rules

## 1. Overview

This document defines the core rules, conventions, and standards that apply across the entire CallSaver project. All team members must adhere to these standards to ensure code quality, maintainability, and consistency across the codebase.

### 1.A Core Principles & Safeguards (Apply Everywhere)

*   **Mission Alignment:** Remember the project helps underserved communities. Prioritize impact, accessibility, and urgency. Measure success by lives improved.
*   **Safety First:** No multitasking during critical operations. Complete verification before action. Preserve functional integrity above all.
*   **No Unauthorized Changes:** Do not modify core architecture (UI, layout, services, APIs, DB schema) without explicit instruction, ADR approval, and thorough review.
*   **Isolation:** Isolate feature work. Changes in one area must have zero unintended impact on unrelated areas. Verify through targeted testing.
*   **Verification:** Verify every change locally *before* pushing or creating a PR. Compare outputs and behavior with stable versions.
*   **Backups:** Ensure automated backups are functioning. Create manual backups before changes with high potential impact (e.g., major data migrations, critical infrastructure changes).
*   **Idempotency:** Ensure critical operations (especially those involving state changes or external API calls) are safely repeatable without unintended side effects.
*   **Read-Only Critical Systems:** Treat critical systems (production DB, configurations) as read-only unless changes are part of an approved, reviewed deployment process.
*   **Review & Approval:** Await manual review and approval (following PR process) before merging *any* changes to `develop` or `main`. Critical changes may require multiple approvals.
*   **MCP Control:** Use managed processes (e.g., CI/CD pipelines, approved MCP tools) for deployments and critical operations. Avoid running potentially disruptive commands (`deploy`, `db migrate production`) directly without safeguards.
*   **Context & Domain Integrity:** Maintain absolute separation between distinct business domains or features (e.g., eSIM vs. Affiliate). Double-check context before committing changes. Implement domain-specific validation where appropriate.

## 1.1 Meta-Rules (Rules for Managing Rules)

*   **Rule Versioning:** Maintain version numbers for all rule documents. Format: `v1.2.3` (Major.Minor.Patch). Include a changelog section for tracking changes.
*   **Rule Conflicts:** When rules conflict, the more specific rule takes precedence. If equal specificity, the newer rule prevails.
*   **Rule Testing:** New rules must undergo validation through test-case scenarios to ensure enforceability.
*   **Rule Measurement:** Each rule should specify how compliance is measured or verified.
*   **Rule Documentation:** Link rules to examples of compliance and non-compliance.
*   **Context Scoping:** Explicitly define the context scope for each rule (frontend-only, backend-only, specific feature, etc.) using the `globs` property in the frontmatter.
*   **Rule Deviations:** Create a log file for approved rule deviations, documenting the reason, approval source, and expiration date.

## 2. Project Organization

### 2.1 Directory Structure

```
callsaver.app/
├── .cursor/               # Cursor AI configuration
├── .cursorrules/          # Legacy rules (maintained for reference)
├── back/                  # Backend codebase
│   └── backend/           # Node.js/Express server
│       ├── config/        # Configuration files
│       ├── controllers/   # Request handlers
│       ├── esim/          # eSIM integration
│       ├── middleware/    # Express middleware
│       ├── models/        # Data models
│       ├── routes/        # API routes
│       ├── services/      # Business logic
│       ├── tests/         # Test suites
│       └── utils/         # Utility functions
├── certificates/          # SSL certificates
├── front/                 # Frontend codebase
│   ├── mainpage/          # Marketing site (Next.js)
│   └── dashboard/         # User dashboard (Next.js)
└── mcp-servers/           # MCP server implementations
```

### 2.2 Naming Conventions

1. **Files & Directories**
   - Use descriptive, purpose-indicating names
   - Follow domain-specific conventions:
     - Backend: camelCase for files (`userService.js`)
     - Frontend: PascalCase for component files (`UserProfile.tsx`)
     - Shared: kebab-case for config files (`eslint-config.js`)
   
2. **Code Elements**
   - Variables/Functions: camelCase (`getUserData`)
   - Classes/Components: PascalCase (`UserProfileComponent`)
   - Constants: UPPER_SNAKE_CASE (`MAX_RETRY_ATTEMPTS`)
   - Private properties: leading underscore (`_privateVar`)
   - TypeScript interfaces: PascalCase with 'I' prefix (`IUserData`)
   - TypeScript types: PascalCase (`UserResponseType`)

## 3. Development Lifecycle

### 3.1 Requirements Phase
*   **Requirement Validation:** All user stories/requirements must pass the "So that" test - explicitly stating the business value. Requirements should be testable and unambiguous.
*   **Tech Debt Tracking:** Maintain a structured tech debt backlog (e.g., in Jira or GitHub Issues) with severity ratings (Critical, High, Medium, Low) and estimated remediation time.
*   **Implementation Prerequisites:** Define clear prerequisites before implementation can begin (e.g., design approval, architecture review, API contract definition).
*   **Feature Flags Planning:** Design feature flag strategy during requirements gathering, not as an afterthought during implementation. Identify features suitable for flagging.
*   **Traceability:** Maintain bidirectional traceability between requirements, design documents, code commits, and test cases.

### 3.2 Implementation Phase
*   **Explicit Context Boundaries:** Define and enforce clear service/feature/domain boundaries. Create unit tests validating these boundaries are not crossed. Avoid unnecessary coupling.
*   **Progressive Enhancement:** Implement core functionality first, then enhance with progressive features. Ensure the core experience is robust.
*   **State Machine Modeling:** Model complex workflows (e.g., user onboarding, eSIM provisioning) as explicit state machines with documented states, transitions, and events. Use diagrams (e.g., Mermaid) where helpful.
*   **Explicit Error States:** Define and document all possible error states for a feature and design clear recovery paths or user feedback mechanisms.
*   **Performance Budgets:** Set explicit performance budgets for new features (e.g., API response time < 250ms P95, component render time < 50ms). Measure against these budgets during development and testing.
*   **Implementation Plan Updates:** After completing each item in the implementation plan, document:
    * Actual completion time vs. estimate
    * Any scope changes encountered
    * Lessons learned or optimizations discovered
    * Update component diagrams if architecture changed significantly

### 3.3 Testing Phase
*   **Test-First Development:** Write failing tests before implementing features where practical.
*   **Test Categories:** Categorize tests as unit, integration, end-to-end, or performance. Ensure appropriate coverage for each category.
*   **Test Data Management:** Maintain separate, version-controlled test data repositories with specific test scenarios. Avoid using production data for testing.
*   **Cross-Browser Testing:** Specify exact browser versions for cross-browser validation based on analytics data.
*   **Negative Testing:** All features must include tests for failure modes, invalid inputs, and edge cases.
*   **Security Testing:** Include security-specific test cases (e.g., input validation, authorization checks) for all new features.
*   **Timing Sensitivity:** Identify and test timing-sensitive components specifically, potentially using mocks or controlled environments.

### 3.4 Deployment Phase
*   **Deployment Windows:** Define specific, pre-approved deployment windows for different environments (e.g., staging anytime, production off-peak hours).
*   **Post-Deployment Verification:** Establish a documented sequence of post-deployment verification steps (smoke tests, health checks, key metric validation).
*   **Rollback Criteria:** Define explicit criteria for when to trigger a rollback (e.g., critical error rate spike, key functionality failure). Have a documented and tested rollback plan.
*   **Feature Flag Activation:** Maintain a separate checklist and approval process for activating feature flags in production.
*   **Canary Deployments:** Implement canary deployments for high-risk changes, gradually increasing traffic exposure.
*   **Traffic Percentage:** Define standard traffic percentage increments for canary releases and A/B tests.

### 3.5 Version Control (Moved from previous section)

1. **Branching Strategy**
   - `main`: Production-ready code. Only merge from `release` or `hotfix` branches.
   - `develop`: Integration branch for features. Represents the next potential release.
   - `feature/*`: New features or enhancements. Branch off `develop`.
   - `bugfix/*`: Non-urgent bug fixes. Branch off `develop`.
   - `release/*`: Release preparation (stabilization, final testing). Branch off `develop`.
   - `hotfix/*`: Urgent production fixes. Branch off `main`.

2. **Commit Standards**
   - Use conventional commits format:
     ```
     <type>(<scope>): <description>

     [optional body]

     [optional footer(s) e.g., Closes #123, BREAKING CHANGE: ...]
     ```
   - Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`, `perf`, `ci`, `build`, `revert`.
   - Keep commits focused and atomic. One logical change per commit.
   - Write clear and concise commit messages. The description should be imperative (e.g., "Add user login endpoint").
   - Reference issue numbers in the footer when applicable.

3. **Pull Request (PR) Process**
   - Create descriptive PR titles summarizing the change.
   - Fill out the PR template completely, linking to relevant issues/requirements.
   - Ensure CI checks (linting, tests, security scans) pass before requesting review.
   - Require at least one reviewer approval (two for critical changes).
   - Address all review comments or provide justification.
   - Rebase `feature` branches onto `develop` before merging to resolve conflicts and maintain a linear history where possible.
   - Use squash merges or rebase merges strategically to keep the `develop` and `main` branch history clean and meaningful.

### 3.6 Environment Management (Moved from previous section)

1. **Environment Variables**
   - Store all configuration, especially secrets, in environment variables.
   - Maintain `.env.example` files in version control with all required variables documented but without actual values.
   - Never commit actual `.env` files or files containing secrets (`.env.local`, `.env.development`, etc.). Use `.gitignore`.
   - Document all environment variables, their purpose, and whether they are required in the relevant README or configuration documentation.
   - Group variables by service/function using prefixes (see `env_configuration_rules.mdc`).

2. **Environment Types**
   - **Development:** Local developer machine. Uses `.env.local` or similar. Connects to local or shared dev services.
   - **Testing:** Automated testing environment (CI/CD). Uses specific test configurations and databases.
   - **Staging:** Pre-production environment mirroring production as closely as possible. Used for final validation and UAT.
   - **Production:** Live environment serving end-users. Highest level of security and monitoring.

## 4. Code Quality Standards

### 4.1 Code Structure & Principles
*   **SOLID Principles:** Adhere to SOLID principles (Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion).
*   **DRY (Don't Repeat Yourself):** Avoid code duplication. Abstract common logic into reusable functions, classes, or components.
*   **KISS (Keep It Simple, Stupid):** Prefer simple, straightforward solutions over complex ones.
*   **YAGNI (You Ain't Gonna Need It):** Avoid implementing functionality that isn't currently required.
*   **Function Size Limit:** Functions should ideally not exceed 30 lines of code (excluding comments and blank lines). Refactor longer functions.
*   **Parameter Limit:** Functions should have 5 or fewer parameters. Use objects for passing complex data.
*   **Nesting Depth:** Limit nesting (conditionals, loops) to a maximum of 3 levels deep. Refactor deeper nesting into separate functions or using techniques like guard clauses.
*   **Cyclomatic Complexity:** Aim for a cyclomatic complexity under 15 for any function. Use tools to measure and refactor complex functions.
*   **Code Ownership:** Assign clear code owners (individuals or teams) to each module/component in a CODEOWNERS file.
*   **Immutability Preference:** Prefer immutable data structures and pure functions where practical, especially in state management and data transformations.
*   **Modularity:** Design components and modules with high cohesion and low coupling.

### 4.2 Error Handling & Logging (Enhanced)
*   **Error Categorization:** Categorize errors by type (e.g., `ValidationError`, `AuthenticationError`, `NotFoundError`, `ExternalServiceError`, `SystemError`). Use custom error classes extending a base `ApplicationError`.
*   **Structured Logging:** Use a structured logging format (JSON) with consistent field names (e.g., `timestamp`, `level`, `service`, `message`, `requestId`, `userId`, `error_code`, `error_details`, `stack_trace` [dev/staging only]). See `env_configuration_rules.mdc` for details.
*   **Error Bubbling:** Define explicit rules for when errors should be handled locally versus bubbled up the call stack. Generally, handle errors where you have enough context to add value or recover, otherwise propagate them.
*   **Retry Policies:** Implement standardized, configurable retry policies (e.g., exponential backoff with jitter) for transient errors when calling external services.
*   **Circuit Breakers:** Implement circuit breakers (e.g., using `opossum`) for critical external dependencies to prevent cascading failures.
*   **Error Correlation:** Include a unique correlation ID (request ID) in all logs related to a single request/transaction for easier tracing across services.
*   **Never Silently Catch:** Avoid empty catch blocks. Always log errors or handle them appropriately.
*   **Contextual Information:** Include relevant context (e.g., user ID, relevant parameters) in error logs to aid debugging. Do NOT log sensitive data directly in messages.

### 4.3 Testing Requirements (Enhanced)
*   **Test-First Development:** Encourage writing failing tests before implementing features, especially for business logic and complex components.
*   **Test Coverage:**
    *   Minimum 85% overall statement coverage.
    *   Critical paths (auth, payment, core features): 95%+ coverage.
    *   Business logic (services): 90%+ coverage.
    *   Document untestable code with clear justification.
*   **Test Types:**
    *   **Unit:** Test individual functions, classes, components in isolation (using mocks/stubs). Focus on logic correctness.
    *   **Integration:** Test interactions between components/modules (e.g., controller-service-repo, component-context). Focus on collaboration.
    *   **End-to-End (E2E):** Test complete user flows through the UI (frontend) or API (backend). Focus on user scenarios. Use tools like Cypress or Playwright.
    *   **Performance:** Test system behavior under load (load testing) and measure response times/resource usage (benchmarking).
    *   **Security:** Include tests specifically targeting potential vulnerabilities (e.g., input validation, authorization bypass attempts).
    *   **Contract Testing:** Consider contract testing (e.g., Pact) for interactions between microservices or frontend/backend APIs.
*   **Test Structure:**
    *   Organize tests mirroring the production code structure (e.g., `src/services/userService.js` -> `tests/unit/services/userService.test.js`).
    *   Use descriptive test names following a pattern like `it('should [do something] when [condition]')`.
    *   Follow the Arrange-Act-Assert (AAA) pattern within tests.
    *   Minimize test interdependence. Each test should ideally run independently.
    *   Use test data factories or fixtures for consistent and reusable test data.

### 4.4 Code Review Criteria (Enhanced)
*   **Functionality:** Does the code correctly implement the requirements and handle specified edge cases?
*   **Maintainability:** Is the code readable, understandable, and consistent with project conventions? Is complexity minimized? Is it appropriately documented?
*   **Performance:** Are there obvious performance bottlenecks? Are database queries efficient? Is caching used appropriately? Does it meet performance budgets?
*   **Security:** Are inputs validated? Are outputs sanitized? Are authentication/authorization checks correctly implemented? Does it adhere to the principle of least privilege? Are secrets handled securely?
*   **Testability & Test Coverage:** Is the code testable? Are there sufficient tests covering logic, edge cases, and failure modes? Do tests pass?
*   **Error Handling:** Are errors handled gracefully? Are appropriate error types used? Is logging sufficient?
*   **Documentation:** Is necessary documentation (JSDoc, READMEs, ADRs) updated or created?
*   **Adherence to Rules:** Does the code comply with all relevant project rules (this document, frontend/backend guidelines, etc.)?

## 5. Security Standards (Enhanced)

### 5.1 Authentication & Authorization
*   **Token Lifecycle:** Define and implement the complete lifecycle for authentication tokens (JWTs, refresh tokens), including issuance, validation, secure storage (HttpOnly cookies preferred), expiration, revocation, and rotation (especially for refresh tokens).
*   **Authorization Matrix:** Maintain an explicit authorization matrix mapping user roles/permissions to specific actions/resources. Implement checks based on this matrix.
*   **Permission Inheritance:** Clearly document any permission inheritance hierarchies.
*   **Least Privilege Enforcement:** Design systems and permissions adhering strictly to the principle of least privilege. Implement automated checks or reviews to verify this.
*   **Session Timeout Policy:** Standardize session inactivity timeout policies based on sensitivity and user role. Implement secure session termination.
*   **Multi-Factor Authentication (MFA):** Define when and how MFA is enforced (e.g., for sensitive operations, admin access). Support multiple MFA methods (TOTP, SMS, hardware keys).
*   **Password Policies:** Enforce strong password complexity requirements, prevent reuse of common or breached passwords, and use secure hashing algorithms (e.g., Argon2, bcrypt) with appropriate salts.
*   **API Key Management:** Implement secure generation, storage, rotation, and revocation mechanisms for API keys used for internal or external integrations.

### 5.2 Data Protection
*   **Data Classification:** Classify all data fields by sensitivity level (e.g., Public, Internal, Confidential, Restricted). Define handling requirements for each level.
*   **Encryption Standards:** Specify encryption algorithms (e.g., AES-256-GCM) and key lengths required for data at rest and in transit, based on data classification. Use HTTPS/TLS 1.3+ for all external communication.
*   **Field-Level Encryption:** Implement field-level encryption in the database for highly sensitive data (e.g., API keys, certain PII) where appropriate. Manage encryption keys securely.
*   **Data Retention:** Define explicit data retention periods by data type, based on business needs and regulatory requirements. Implement automated data purging or anonymization.
*   **Right to be Forgotten:** Implement processes and tools to support the complete, cascading deletion of user data upon request, complying with GDPR/CCPA.
*   **Data Anonymization/Pseudonymization:** Standardize procedures for anonymizing or pseudonymizing data used for analytics, testing, or development environments.
*   **Input Validation & Output Encoding:** Rigorously validate and sanitize all inputs (API requests, user forms) on the backend. Encode all outputs displayed in the UI or returned via APIs to prevent XSS and other injection attacks.

### 5.3 API Security (Enhanced)
*   **API Versioning Policy:** Define an explicit API versioning scheme (e.g., URL path `/api/v1/`) and a clear deprecation policy for older versions.
*   **Rate Limiting Strategy:** Implement robust rate limiting (per user, per IP, per API key) based on usage tiers and endpoint sensitivity. Use algorithms like leaky bucket or token bucket. Return standard `Retry-After` headers (HTTP 429).
*   **Input Validation Patterns:** Standardize input validation patterns using libraries like Zod or JSON Schema. Validate data types, formats, lengths, and ranges. Reject invalid requests early.
*   **CSRF Protection:** Implement CSRF protection (e.g., using double-submit cookies or synchronizer tokens) for all state-changing operations initiated via web browsers.
*   **Security Headers:** Configure and standardize security-related HTTP response headers (e.g., `Content-Security-Policy`, `Strict-Transport-Security`, `X-Content-Type-Options`, `X-Frame-Options`, `Referrer-Policy`).
*   **API Documentation Security:** Separate public vs. private/internal API documentation. Ensure sensitive endpoints or parameters are not exposed in public documentation.
*   **Webhook Security:** Validate webhook signatures using shared secrets. Implement replay attack prevention (e.g., using timestamps and nonces).

### 5.4 Security Testing & Auditing
*   **Regular Scanning:** Perform regular automated vulnerability scanning (SAST, DAST, dependency scanning) within the CI/CD pipeline.
*   **Penetration Testing:** Conduct periodic penetration testing (at least annually or before major releases) by qualified third parties.
*   **Security Code Reviews:** Include specific security checks as part of the standard code review process.
*   **Threat Modeling:** Perform threat modeling exercises for new features or significant architectural changes.
*   **Security Logging & Auditing:** Maintain detailed audit logs for security-relevant events (logins, permission changes, sensitive data access, configuration changes). Monitor logs for suspicious activity.

## 6. Performance Standards (Enhanced)

### 6.1 Frontend Performance
*   **Core Web Vitals:**
    *   Largest Contentful Paint (LCP): < 2.5s
    *   First Input Delay (FID) / Interaction to Next Paint (INP): < 100ms
    *   Cumulative Layout Shift (CLS): < 0.1
*   **Other Key Metrics:**
    *   First Contentful Paint (FCP): < 1.8s
    *   Time to Interactive (TTI): < 5s (on average mobile)
    *   Total Blocking Time (TBT): < 300ms
    *   Lighthouse Performance Score: > 90 (overall)
*   **Resource Optimization:**
    *   Optimize image delivery (modern formats like WebP/AVIF, responsive sizes, lazy loading).
    *   Implement code splitting (route-based and component-based).
    *   Apply resource hints (`preload`, `prefetch`) strategically for critical assets.
    *   Configure proper caching headers (HTTP caching, service workers).
    *   Minimize main thread work and JavaScript execution time.
    *   Analyze and maintain bundle size budgets (e.g., initial JS load < 170KB gzipped). Use tools like `@next/bundle-analyzer`.

### 6.2 Backend Performance
*   **API Response Time Budgets:**
    *   Standard CRUD endpoints: < 200ms at P95
    *   Data-intensive or complex endpoints: < 500ms at P95
    *   Authentication endpoints: < 150ms at P95
    *   Background job initiation: < 50ms
*   **Database Optimization:**
    *   Index commonly queried fields effectively. Use composite indexes where appropriate.
    *   Monitor and optimize slow queries (e.g., using `pg_stat_statements` or database provider tools). Aim for query times < 50ms for common operations.
    *   Implement caching strategies (e.g., Redis) for frequently accessed, relatively static data. Document cache invalidation strategies clearly.
    *   Use database connection pooling effectively. Configure pool size based on expected load and database limits. Monitor pool usage.
    *   Avoid N+1 query problems. Use techniques like eager loading (Prisma `include`) or dataloaders. Implement checks in code reviews or automated tooling.
*   **Resource Management:**
    *   Monitor CPU, memory, and network usage. Set alerts for high utilization.
    *   Optimize resource usage in background jobs.
    *   Detect and fix memory leaks. Run memory leak detection tools periodically or in CI.
    *   Optimize external API call latency (parallel execution where possible, timeouts, efficient data fetching).

## 7. Monitoring & Observability (Enhanced)

### 7.1 Instrumentation & Metrics Collection
*   **Critical Path Monitoring:** Identify and instrument critical user paths (e.g., signup, eSIM activation, call handling) with specific metrics and tracing.
*   **System Metrics:** Collect standard system metrics (CPU, memory, disk I/O, network I/O) for all services and infrastructure components.
*   **Application Metrics:** Collect key application metrics (request latency, throughput (RPS), error rates (per endpoint/status code), saturation (e.g., queue lengths, connection pool usage)). Use standardized metric naming conventions.
*   **Business Metrics:** Define and collect business-relevant custom metrics (e.g., active users, eSIM activations, call duration, AI interaction success rate, revenue).
*   **Distributed Tracing:** Implement distributed tracing across services using standards like OpenTelemetry. Ensure trace context propagation.
*   **User Journey Tracking:** Track completion rates and drop-off points for critical user journeys (e.g., onboarding, configuration).

### 7.2 Logging Standards (Enhanced)
*   **Log Levels:** Use standard log levels consistently:
    *   `ERROR`: Actionable errors requiring immediate attention.
    *   `WARN`: Potential issues or handled exceptions that might indicate future problems.
    *   `INFO`: Significant operational events (e.g., service start/stop, configuration changes, user login).
    *   `DEBUG`: Detailed information useful for debugging specific issues (disabled in production by default).
    *   `TRACE`: Highly detailed diagnostic information (rarely used, disabled in production).
*   **Structured Logging:** Use structured logging (JSON format) exclusively. See `env_configuration_rules.mdc` and Section 4.2 for format details.
*   **Correlation IDs:** Include a unique request/correlation ID in all logs associated with a single transaction or request flow.
*   **Contextual Information:** Log relevant context (user ID, tenant ID, relevant entity IDs) but **never log sensitive data** (passwords, full credit card numbers, API keys) directly. Implement masking for sensitive fields if necessary.
*   **Log Aggregation:** Centralize logs from all services using a log aggregation platform (e.g., DataDog Logs, ELK stack).

### 7.3 Alerting Strategy
*   **Alert Severity Levels:** Define clear severity levels (e.g., P1/Critical, P2/Error, P3/Warning) with associated response expectations.
*   **Alert Routing Rules:** Establish routing rules based on service, alert type, and severity to notify the appropriate on-call personnel or team.
*   **Error Rate Thresholds:** Set dynamic or static alerting thresholds for error rates (overall and per-endpoint).
*   **Performance Degradation Detection:** Implement alerts based on significant deviations from baseline performance (latency, resource usage).
*   **Saturation Alerts:** Alert when system resources (CPU, memory, disk, connection pools, queue lengths) approach critical limits.
*   **Business Metric Alerts:** Alert on significant drops or anomalies in key business metrics.
*   **Alert Grouping:** Implement intelligent alert grouping to reduce noise and alert fatigue.
*   **Escalation Paths:** Define clear escalation paths for unresolved alerts.
*   **False Positive Reduction:** Establish processes for reviewing and tuning alerts to minimize false positives.
*   **Business Impact Correlation:** Correlate technical alerts with potential business impact where possible.
*   **Runbooks:** Link alerts to specific runbooks or troubleshooting guides.

## 8. Tech Debt Management

### 8.1 Identification Process

1. **Tech Debt Sources**
   - Code complexity metrics
   - Test coverage gaps
   - Deprecated dependencies
   - Architecture inconsistencies
   - Performance bottlenecks

2. **Documentation Requirements**
   - Document all known tech debt
   - Assess impact and remediation cost
   - Prioritize based on risk/impact
   - Track in backlog with clear labels

### 8.2 Remediation Policy

1. **Scheduled Remediation**
   - Allocate 20% of development time to tech debt
   - Address critical debt immediately
   - Include debt remediation in feature development
   - Schedule regular debt-focused sprints

2. **Prevention Strategies**
   - Code quality gates in CI/CD
   - Regular dependency updates
   - Architecture decision records
   - Scheduled code audits

## 9. Documentation Standards (Enhanced)

### 9.1 Code & System Documentation
*   **Self-Documenting Code:** Prioritize writing code that is clear and understandable on its own through good naming, structure, and simplicity.
*   **Comment Purpose:** Use comments primarily to explain the "why" behind a piece of code, not the "what" or "how" if it's already clear from the code itself. Document complex algorithms, non-obvious logic, or workarounds.
*   **JSDoc/TSDoc:** Use JSDoc (for JS) or TSDoc (for TS) for documenting all public functions, classes, methods, and complex types, including parameters, return values, and potential exceptions.
*   **README Files:** Maintain informative README files at the root of the project and for each major service or package, explaining its purpose, setup, usage, and key architectural decisions.
*   **API Documentation:** Maintain accurate and up-to-date API documentation using OpenAPI (Swagger) specifications. Generate documentation automatically from code annotations where possible. Include clear request/response examples and authentication requirements.
*   **Architecture Documentation:** Maintain high-level architecture diagrams (e.g., using the C4 model or similar) in `/docs/architecture/`. Document key components, their interactions, and major design decisions (link to relevant ADRs). Keep diagrams reasonably synchronized with the implementation.
*   **State Machine Documentation:** Document complex state machines visually (e.g., using Mermaid state diagrams) and describe states and transitions clearly.
*   **Decision Documentation (ADRs):** Use Architectural Decision Records (ADRs) stored in `/docs/adr/` to document significant architectural choices, their context, and consequences.

### 9.2 User & Operational Documentation
*   **User Journey Documentation:** Document key user journeys and workflows, potentially linking to relevant sections in the `app_flow_document.mdc`.
*   **User Guides & FAQs:** Create and maintain clear, concise user guides for key features and an FAQ section based on common support inquiries. Use screenshots and videos where helpful.
*   **Troubleshooting Guides:** Develop troubleshooting guides for common user issues and operational problems.
*   **Runbooks:** Create operational runbooks for common procedures like deployments, rollbacks, backups, incident response, and scaling operations.
*   **Contextual Help:** Implement contextual help (tooltips, popovers, links to docs) within the application interface where appropriate.
*   **Documentation Versioning:** Version user-facing and operational documentation alongside the software releases they correspond to.
*   **Localized Documentation:** Translate critical user-facing documentation (e.g., setup guides, FAQs) into supported languages.
*   **Documentation Review:** Include documentation updates as part of the code review process for relevant changes. Regularly review documentation for accuracy and clarity.

## 10. API Design Standards

### 10.1 RESTful Principles

1. **Resource Naming**
   - Use plural nouns for resource collections
   - Apply consistent naming patterns
   - Use kebab-case for multi-word resources
   - Include API version in path

2. **HTTP Methods**
   - GET: Retrieve resources
   - POST: Create resources
   - PUT: Update (replace) resources
   - PATCH: Partial updates
   - DELETE: Remove resources

### 10.2 Response Formats

1. **Success Responses**
   ```json
   {
     "success": true,
     "data": { ... },
     "meta": {
       "page": 1,
       "perPage": 25,
       "total": 100
     }
   }
   ```

2. **Error Responses**
   ```json
   {
     "success": false,
     "error": {
       "code": "VALIDATION_ERROR",
       "message": "Invalid data provided",
       "details": [...]
     }
   }
   ```

## 11. Accessibility Requirements (Enhanced)

### 11.1 Compliance Standards
*   **WCAG 2.1 AA Compliance:** All user interfaces must meet Web Content Accessibility Guidelines (WCAG) 2.1 Level AA conformance. Strive for AAA where feasible.
*   **Automated Testing:** Integrate automated accessibility testing tools (e.g., Axe-core) into the CI/CD pipeline and development workflow.
*   **Manual Audits:** Conduct manual accessibility audits (including keyboard-only and screen reader testing) before major releases and periodically thereafter.
*   **Legal Compliance:** Ensure compliance with relevant accessibility legislation (e.g., ADA, Section 508, EN 301 549) based on target markets.

### 11.2 Implementation Guidelines (POUR Principles)

#### Perceivable
*   **Text Alternatives:** Provide appropriate text alternatives (`alt` text) for all non-text content (images, icons). Mark decorative images appropriately (`alt=""`).
*   **Time-Based Media:** Provide captions for pre-recorded audio/video content and transcripts for audio-only content. Consider audio descriptions for video content.
*   **Adaptable Content:** Use semantic HTML to ensure content can be presented in different ways (e.g., simplified layout) without losing information or structure. Ensure proper heading structure (`h1`-`h6`).
*   **Distinguishable Content:**
    *   Ensure sufficient color contrast (4.5:1 for normal text, 3:1 for large text and UI components). Use tools to check contrast.
    *   Do not rely solely on color to convey information. Use text labels, icons, or other visual cues.
    *   Ensure text is resizable up to 200% without loss of content or functionality.
    *   Avoid background audio that interferes with foreground speech.

#### Operable
*   **Keyboard Accessible:** All functionality must be operable through a keyboard interface without requiring specific timings. Ensure logical tab order.
*   **No Keyboard Traps:** Ensure keyboard focus can always be moved away from any component using the keyboard.
*   **Time Adjustable:** Provide users enough time to read and use content. Avoid timed interactions where possible or provide controls to adjust/extend time limits.
*   **Seizures and Physical Reactions:** Do not design content in a way that is known to cause seizures (e.g., avoid flashing content more than three times per second).
*   **Navigable:** Provide ways to help users navigate, find content, and determine where they are (e.g., breadcrumbs, clear page titles, site maps).
*   **Focus Visible:** Ensure keyboard focus indicators are clearly visible and have sufficient contrast.

#### Understandable
*   **Readable:** Make text content readable and understandable. Use clear language and provide definitions for jargon or abbreviations.
*   **Predictable:** Make web pages appear and operate in predictable ways. Ensure consistent navigation and identification of components.
*   **Input Assistance:** Help users avoid and correct mistakes. Provide clear instructions, error identification, and suggestions for correction. Use proper form labels and associate them with inputs.

#### Robust
*   **Compatible:** Maximize compatibility with current and future user agents, including assistive technologies. Use valid HTML and follow web standards. Implement ARIA (Accessible Rich Internet Applications) attributes correctly when enhancing non-native HTML components, but prefer semantic HTML where possible.
*   **Reduced Motion:** Respect the `prefers-reduced-motion` media query to disable or reduce non-essential animations for users who prefer it.

## 12. Internationalization (i18n) & Localization (l10n) (Enhanced)

### 12.1 Language Support & Translation Workflow
*   **Supported Languages:** Define the list of officially supported languages (e.g., English, Arabic, German initially).
*   **Translation Workflow:** Define the complete workflow for managing translations:
    *   **String Extraction:** Automate the extraction of translatable strings from the codebase (e.g., using `i18next-parser` or similar tools).
    *   **Translation Management System:** Use a dedicated platform (e.g., Lokalise, Phrase, Crowdin) to manage translation files, provide context to translators, and track progress.
    *   **Translation Process:** Define the process for professional translation and review.
    *   **Integration:** Automate the synchronization of translation files between the management system and the codebase.
*   **Translation Coverage Metrics:** Track the percentage of translated content for each supported language. Set minimum coverage targets for releases.
*   **No Hardcoded Strings:** Strictly enforce the rule of no hardcoded user-facing strings in the codebase. All text must use translation keys.

### 12.2 Implementation Requirements
*   **Library:** Use `next-intl` for Next.js applications.
*   **Translation Keys:** Use descriptive, hierarchical key names (e.g., `UserProfile.EditButton.label`). Group keys logically (e.g., by feature or page). Provide context comments for translators within the source files or management system.
*   **ICU Message Format:** Use the ICU message format for complex translations involving pluralization, gender, select cases, and embedded formatting.
*   **Component Structure:** Design components to handle varying text lengths gracefully. Avoid fixed-width containers for text where possible.
*   **Lazy Loading:** Load translation files asynchronously, potentially per-route or per-language, to optimize initial load times.
*   **Language Detection:** Implement robust language detection based on browser settings, user preferences, or URL path/subdomain. Provide a clear way for users to switch languages.

### 12.3 Localization Requirements
*   **Date/Time Formatting:** Format dates and times according to the user's locale using libraries like `date-fns` or the browser's `Intl` API. Display timezones clearly where relevant.
*   **Number Formatting:** Format numbers, currencies, and percentages according to locale conventions.
*   **Right-to-Left (RTL) Support:** Ensure layouts automatically adapt for RTL languages (e.g., Arabic, Hebrew). Use CSS logical properties (`margin-inline-start` instead of `margin-left`) and test thoroughly.
*   **Cultural Adaptation:**
    *   Be mindful of color significance, imagery, and symbols that may have different cultural interpretations. Document guidelines.
    *   Adapt address formats, name conventions, and other culturally specific data representations.
    *   Support region-specific payment methods and currencies.
*   **Legal & Compliance:** Ensure privacy policies, terms of service, and other legal documents are appropriately localized and comply with regional regulations.

### 12.4 Testing Requirements
*   **Pseudo-localization:** Use pseudo-localization during development to identify untranslated strings and layout issues caused by text expansion.
*   **Layout Testing:** Verify UI layouts with significantly longer and shorter text strings than the source language.
*   **RTL Testing:** Test all UI components and layouts in an RTL language.
*   **Format Testing:** Verify correct formatting of dates, times, numbers, and currencies in different locales.
*   **Translation Completeness:** Implement checks (manual or automated) to ensure translation coverage meets targets before release.

## 13. Code Review Process

### 13.1 PR Requirements

1. **Before Submission**
   - All tests passing
   - Code formatting applied
   - Self-review completed
   - Documentation updated

2. **Review Process**
   - At least one reviewer required
   - Address all comments or explain why not
   - Use "request changes" for blocking issues
   - Rebase and resolve conflicts before merge

### 13.2 Review Focus Areas

1. **Primary Areas**
   - Correctness: Does it work as intended?
   - Maintainability: Is it easy to understand?
   - Performance: Does it perform efficiently?
   - Security: Does it adhere to security standards?

2. **Secondary Areas**
   - Test coverage: Are all cases covered?
   - Error handling: Are errors handled properly?
   - Documentation: Is it properly documented?
   - Style: Does it follow project conventions?

## 14. Dependencies Management

### 14.1 Dependency Selection

1. **Evaluation Criteria**
   - Active maintenance
   - Community adoption
   - License compatibility
   - Security history
   - Performance impact
   - Bundle size impact

2. **Internal vs. External**
   - Prefer standard library when available
   - Consider maintenance burden for external deps
   - Document why dependency is needed
   - Evaluate build-vs-buy decision

### 14.2 Update Strategy

1. **Regular Updates**
   - Schedule monthly dependency updates
   - Prioritize security patches immediately
   - Test all updates in isolation
   - Keep major version updates separate from features

2. **Dependency Tracking**
   - Use automated dependency scanning
   - Monitor for vulnerabilities
   - Document deprecated dependencies
   - Plan for major version migrations

## 15. Architecture Governance

### 15.1 Architectural Decision Records (ADRs)
*   **ADR Creation:** Document all significant architectural decisions in a standardized ADR format (e.g., Markdown Architectural Decision Records) including context, decision, status, consequences. Store ADRs in `/docs/adr/`.
*   **ADR Review:** All ADRs require review from at least two senior engineers or architects.
*   **ADR Update:** ADRs should be updated, not replaced, with amendment history. Mark superseded decisions clearly.
*   **Reference Architecture:** Maintain a living reference architecture document with visualization (e.g., C4 model diagrams) in `/docs/architecture/`.
*   **Dependency Map:** Create and maintain a service/feature dependency map, potentially using automated tools or diagrams.

## 16. Continuous Improvement

### 16.1 Feedback Loops
*   **User Feedback Collection:** Implement structured user feedback collection mechanisms (surveys, in-app feedback).
*   **Developer Experience Feedback:** Regularly collect feedback on development tooling, processes, and rules.
*   **Post-Incident Reviews:** Conduct blameless post-incident reviews for all significant incidents, documenting root causes and action items.
*   **Metric-Based Retrospectives:** Tie team retrospectives to measurable metrics (e.g., deployment frequency, lead time, error rates).
*   **Knowledge Sharing:** Schedule regular knowledge sharing sessions (e.g., tech talks, brown bags).
*   **Rule Evolution:** Review and update rules quarterly based on learnings, feedback, and project evolution.
*   **Continuous Learning:** Document root causes of issues and prevention steps. No preventable issue should occur twice. Update rules and processes accordingly.

### 16.2 Innovation Process
*   **Experimentation Framework:** Define a structured framework for proposing, running, and evaluating experiments.
*   **Innovation Allocation:** Allocate a defined percentage of development time (e.g., 10-20%) for innovation, learning, and tech debt reduction.
*   **Technology Radar:** Maintain a technology radar to track and evaluate emerging technologies relevant to CallSaver.
*   **Proof of Concept Standards:** Define standards for creating and evaluating proof of concepts (PoCs).
*   **A/B Testing Infrastructure:** Implement robust A/B testing capabilities for user-facing features.
*   **Feature Flag Strategy:** Utilize feature flags for controlled rollouts, experimentation, and gradual releases.

## 17. Version History

| Version | Date | Description |
|---------|------|-------------|
| 1.0.0 | 2025-04-13 | Initial comprehensive project rules document |
| 1.1.0 | 2025-04-14 | Added Meta-Rules, Architecture Governance, Continuous Improvement sections. Integrated enhanced rules from `Enhanced-ProjectRules.md`. |

## 18. Related Documents

- [Frontend Guidelines Document](mdc:.cursor/rules/frontend_guidelines_document.mdc)
- [Backend Structure Document](mdc:.cursor/rules/backend_structure_document.mdc)
- [Tech Stack Document](mdc:.cursor/rules/tech_stack_document.mdc)
- [App Flow Document](mdc:.cursor/rules/app_flow_document.mdc)
- [Implementation Plan](mdc:.cursor/rules/implementation_plan.mdc)
- [Project Requirements Document](mdc:.cursor/rules/project_requirements_document.mdc)
- [Environment Configuration Rules](mdc:.cursor/rules/env_configuration_rules.mdc)
- [AI Behavior Rules](mdc:.cursor/rules/ai_behavior_rules.mdc)
