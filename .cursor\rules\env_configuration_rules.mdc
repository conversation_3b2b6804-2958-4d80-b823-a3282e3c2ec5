---
description: 
globs: 
alwaysApply: false
---
---
description: Standardized environment configuration guidelines for CallSaver
globs: ["**/.env*", "**/*.js", "**/*.ts"]
alwaysApply: true
version: 1.0.0
---

# Environment Configuration Rules

## 1. Overview

This document defines the standardized approach to environment variables and configuration management across the CallSaver platform. Proper environment configuration management is critical for security, deployment flexibility, and operational consistency.

## 2. Environment Variable Structure

### 2.1 Naming Conventions

1. **Format**
   - Use UPPER_SNAKE_CASE for all environment variable names
   - Group related variables with common prefixes
   - Use clear, descriptive names that indicate purpose

2. **Prefixing Strategy**
   - `NODE_` - Node.js runtime configuration
   - `NEXT_` - Next.js specific configuration
   - `DB_` - Database connection parameters
   - `AUTH_` - Authentication service configuration
   - `ESIM_` - eSIM provider integration
   - `TWILIO_` - Twilio service integration
   - `OPENAI_` - AI service integration
   - `PINECONE_` - Vector database configuration
   - `STRIPE_` - Payment processor integration
   - `REDIS_` - Redis/caching configuration
   - `EMAIL_` - Email service configuration
   - `API_` - External API integration
   - `LOG_` - Logging configuration
   - `M<PERSON><PERSON>OR_` - Monitoring configuration

3. **Examples**
   ```
   # Database configuration
   DB_HOST=postgres.railway.app
   DB_PORT=5432
   DB_NAME=callsaver_production
   DB_USER=postgres
   DB_PASSWORD=<secret>
   
   # eSIM provider configuration
   ESIM_API_KEY=<secret>
   ESIM_API_SECRET=<secret>
   ESIM_ENDPOINT=https://api.esim-provider.com/v1
   
   # AI service configuration
   OPENAI_API_KEY=<secret>
   OPENAI_MODEL=gpt-4
   OPENAI_TEMPERATURE=0.7
   ```

### 2.2 Sensitive Variables

1. **Secrets Management**
   - Credentials and secrets must ALWAYS be stored as environment variables
   - NEVER commit secrets to version control
   - Rotate secrets on a regular schedule (90-day maximum)
   - Use different secrets for different environments

2. **Sensitivity Classification**
   - **Critical**: API keys, passwords, tokens (e.g., `DB_PASSWORD`, `STRIPE_SECRET_KEY`)
   - **Sensitive**: Endpoints, configuration that could enable targeted attacks (e.g., `REDIS_URL`)
   - **Public**: Configuration that is safe to expose (e.g., `LOG_LEVEL`, `NODE_ENV`)

## 3. Environment File Management

### 3.1 File Structure

1. **Standard Files**
   - `.env` - Local development environment (never committed)
   - `.env.example` - Template with all required variables (committed, no real values)
   - `.env.test` - Testing environment configuration
   - `.env.production` - Production reference (committed with placeholder values)

2. **File Format**
   ```
   # Section header with description
   # Another descriptive comment
   VARIABLE_NAME=value
   
   # New section with description
   OTHER_VARIABLE=value
   ```

### 3.2 Environment-Specific Configuration

1. **Development Environment**
   - Use local services where possible
   - Include detailed logging
   - Use sandboxed external service accounts

2. **Testing Environment**
   - Use isolated test databases
   - Mock external services where appropriate
   - Include detailed error reporting

3. **Staging Environment**
   - Mirror production configuration
   - Use separate instances of external services
   - Include enhanced logging and monitoring

4. **Production Environment**
   - Optimize for security and performance
   - Restrict debug information
   - Use production service accounts

## 4. Configuration Loading

### 4.1 Backend Implementation

1. **Loading Pattern**
   ```javascript
   // config/index.js
   const dotenv = require('dotenv');
   const path = require('path');
   
   // Load environment-specific variables
   dotenv.config({
     path: process.env.NODE_ENV === 'production'
       ? path.resolve(process.cwd(), '.env.production.local')
       : path.resolve(process.cwd(), '.env.local')
   });
   
   // Validate required variables
   const requiredEnvVars = [
     'DB_HOST',
     'DB_NAME',
     'DB_USER',
     'DB_PASSWORD',
     // Other required variables
   ];
   
   for (const envVar of requiredEnvVars) {
     if (!process.env[envVar]) {
       throw new Error(`Missing required environment variable: ${envVar}`);
     }
   }
   
   // Export validated configuration
   module.exports = {
     database: {
       host: process.env.DB_HOST,
       port: parseInt(process.env.DB_PORT || '5432', 10),
       name: process.env.DB_NAME,
       user: process.env.DB_USER,
       password: process.env.DB_PASSWORD,
     },
     // Other configuration sections
   };
   ```

2. **Type Safety**
   ```typescript
   // config/index.ts
   import dotenv from 'dotenv';
   import path from 'path';
   
   // Environment interface
   interface Env {
     NODE_ENV: 'development' | 'test' | 'staging' | 'production';
     DB_HOST: string;
     DB_PORT: string;
     DB_NAME: string;
     DB_USER: string;
     DB_PASSWORD: string;
     // Other variables with types
   }
   
   // Extend ProcessEnv
   declare global {
     namespace NodeJS {
       interface ProcessEnv extends Env {}
     }
   }
   
   // Load and validate
   dotenv.config();
   
   // Export typed configuration
   export const config = {
     // Configuration with proper typing
   };
   ```

### 4.2 Frontend Implementation

1. **Next.js Environment Variables**
   - Use `NEXT_PUBLIC_` prefix for client-side variables
   - Define variables in `.env.local` for local development
   - Configure deployment platform environment variables
   - Use environment-specific files (`.env.production`, etc.)

2. **Runtime Configuration**
   ```javascript
   // next.config.js
   module.exports = {
     env: {
       // Variables exposed to the client
       NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
       // Other public variables
     },
     // Other Next.js config
   };
   ```

3. **Type Safety**
   ```typescript
   // types/env.d.ts
   declare namespace NodeJS {
     interface ProcessEnv {
       NEXT_PUBLIC_API_URL: string;
       NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: string;
       // Other client-side variables
     }
   }
   ```

## 5. Security Guidelines

### 5.1 Secrets Protection

1. **Never Commit Secrets**
   - Use `.gitignore` to exclude all `.env*` files
   - Use `.env.example` as a template
   - Review code for hardcoded secrets before committing

2. **Secrets Storage**
   - Use environment variables in development
   - Use platform secrets management in production (Railway, Vercel, etc.)
   - Consider using a secrets manager for critical environments

3. **Masking in Logs**
   - Implement sensitive value masking in logs
   - Never log complete secrets or credentials
   - Sanitize error messages that might contain secrets

### 5.2 Access Control

1. **Principle of Least Privilege**
   - Provide minimal access required for each service
   - Use read-only credentials where possible
   - Create service-specific credentials

2. **Secrets Rotation**
   - Implement regular rotation schedule
   - Create rotation procedures
   - Maintain audit trail of rotations

## 6. Environment Variables Checklist

### 6.1 Core Variables

| Category | Variable | Description | Required | Secret |
|----------|----------|-------------|----------|--------|
| Node | NODE_ENV | Environment (development, test, production) | Yes | No |
| Server | PORT | Server port | No | No |
| Server | HOST | Server host | No | No |
| Database | DB_HOST | Database hostname | Yes | No |
| Database | DB_PORT | Database port | Yes | No |
| Database | DB_NAME | Database name | Yes | No |
| Database | DB_USER | Database username | Yes | No |
| Database | DB_PASSWORD | Database password | Yes | Yes |
| Auth | AUTH_JWT_SECRET | JWT signing secret | Yes | Yes |
| Auth | AUTH_JWT_EXPIRES | JWT expiration time | Yes | No |

### 6.2 External Services

| Category | Variable | Description | Required | Secret |
|----------|----------|-------------|----------|--------|
| eSIM | ESIM_API_KEY | eSIM provider API key | Yes | Yes |
| eSIM | ESIM_API_SECRET | eSIM provider API secret | Yes | Yes |
| eSIM | ESIM_API_URL | eSIM provider API URL | Yes | No |
| OpenAI | OPENAI_API_KEY | OpenAI API key | Yes | Yes |
| OpenAI | OPENAI_MODEL | Model to use (e.g., gpt-4) | Yes | No |
| Pinecone | PINECONE_API_KEY | Pinecone API key | Yes | Yes |
| Pinecone | PINECONE_ENVIRONMENT | Pinecone environment | Yes | No |
| Pinecone | PINECONE_INDEX | Pinecone index name | Yes | No |
| Stripe | STRIPE_SECRET_KEY | Stripe secret key | Yes | Yes |
| Stripe | STRIPE_WEBHOOK_SECRET | Stripe webhook secret | Yes | Yes |
| Stripe | NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY | Stripe publishable key | Yes | No |
| Email | EMAIL_API_KEY | Email service API key | Yes | Yes |
| Email | EMAIL_FROM | Default sender email | Yes | No |

### 6.3 Infrastructure

| Category | Variable | Description | Required | Secret |
|----------|----------|-------------|----------|--------|
| Redis | REDIS_URL | Redis connection URL | Yes | Yes |
| Redis | REDIS_PASSWORD | Redis password | No | Yes |
| Logging | LOG_LEVEL | Log verbosity level | No | No |
| Logging | LOG_FORMAT | Log format (json, pretty) | No | No |
| WebRTC | TURN_SERVER_URL | TURN server URL | Yes | No |
| WebRTC | TURN_SERVER_USERNAME | TURN server username | Yes | Yes |
| WebRTC | TURN_SERVER_CREDENTIAL | TURN server credential | Yes | Yes |

## 7. Deployment Considerations

### 7.1 CI/CD Pipeline

1. **Environment Variable Handling**
   - Store secrets in CI/CD platform secret storage
   - Inject variables at build/deployment time
   - Validate required variables before deployment

2. **Environment-Specific Builds**
   - Use different variable sets per environment
   - Implement environment-specific build configurations
   - Validate environment compatibility

### 7.2 Multi-Environment Strategy

1. **Environment Isolation**
   - Maintain separate configurations per environment
   - Prevent cross-environment data access
   - Implement environment-specific feature flags

2. **Promotion Process**
   - Define clear promotion paths between environments
   - Implement configuration validation during promotion
   - Document environment differences

## 8. Version History

| Version | Date | Description |
|---------|------|-------------|
| 1.0.0 | 2025-04-13 | Initial environment configuration rules document |

## 9. Related Documents

- [Backend Structure Document](mdc:.cursor/rules/backend_structure_document.mdc)
- [Tech Stack Document](mdc:.cursor/rules/tech_stack_document.mdc)
- [Project Rules](mdc:.cursor/rules/cursor_project_rules.mdc)
