'use client';

import { IntegrationSettings } from '../../../hooks/useIntegrations';

interface ZapierSettingsProps {
  settings: IntegrationSettings;
  onChange: (settings: IntegrationSettings) => void;
}

export default function ZapierSettings({
  settings,
  onChange,
}: ZapierSettingsProps) {
  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    onChange({
      ...settings,
      [name]: checked,
    });
  };

  return (
    <div className="space-y-4">
      <p className="text-sm text-gray-500 dark:text-gray-400">
        Zapier allows you to connect CallSaver to thousands of other apps. Use the webhook URL provided in the Connect tab to set up your Zaps.
      </p>

      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mt-4">
        Trigger Events
      </h4>
      <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
        Select which events should trigger webhooks to Zapier:
      </p>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="triggerOnCalls"
            name="triggerOnCalls"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.triggerOnCalls !== false} // Default to true
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="triggerOnCalls"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Calls
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Trigger on incoming and outgoing calls.
          </p>
        </div>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="triggerOnVoicemails"
            name="triggerOnVoicemails"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.triggerOnVoicemails !== false} // Default to true
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="triggerOnVoicemails"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Voicemails
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Trigger when a voicemail is received.
          </p>
        </div>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="triggerOnSms"
            name="triggerOnSms"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.triggerOnSms !== false} // Default to true
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="triggerOnSms"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            SMS Messages
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Trigger when an SMS is received.
          </p>
        </div>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="triggerOnAppointments"
            name="triggerOnAppointments"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.triggerOnAppointments || false}
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="triggerOnAppointments"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Appointments
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Trigger when an appointment is scheduled or changed.
          </p>
        </div>
      </div>
    </div>
  );
}
