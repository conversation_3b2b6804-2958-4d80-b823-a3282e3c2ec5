'use client';

import React, { useState } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import PermissionAwareMenu, { MenuItem } from './PermissionAwareMenu';
import { getNavigationItems } from '../../utils/navigationUtils';

interface PermissionAwareSidebarProps {
  className?: string;
  logoUrl?: string;
  logoText?: string;
  onToggleCollapse?: (collapsed: boolean) => void;
  defaultCollapsed?: boolean;
  showCollapseButton?: boolean;
  showMobileToggle?: boolean;
  children?: React.ReactNode;
}

/**
 * A sidebar component that only shows navigation items the user has permission to access
 */
const PermissionAwareSidebar: React.FC<PermissionAwareSidebarProps> = ({
  className = '',
  logoUrl = '/logo.svg',
  logoText = 'CallSaver',
  onToggleCollapse,
  defaultCollapsed = false,
  showCollapseButton = true,
  showMobileToggle = true,
  children,
}) => {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  // Toggle sidebar collapse state
  const toggleCollapse = () => {
    const newState = !isCollapsed;
    setIsCollapsed(newState);
    if (onToggleCollapse) {
      onToggleCollapse(newState);
    }
  };

  // Toggle mobile sidebar
  const toggleMobile = () => {
    setIsMobileOpen(!isMobileOpen);
  };

  // Get navigation items
  const navigationItems = getNavigationItems();

  // Render icon from SVG path
  const renderIcon = (icon: React.ReactNode | string) => {
    if (typeof icon === 'string') {
      return (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={isCollapsed ? 'w-7 h-7' : 'w-5 h-5'}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d={icon as string}
          />
        </svg>
      );
    }
    return icon;
  };

  return (
    <>
      {/* Mobile backdrop */}
      {isMobileOpen && showMobileToggle && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden"
          onClick={toggleMobile}
        ></div>
      )}

      {/* Sidebar */}
      <aside
        className={`
          fixed top-0 left-0 h-full bg-gray-900 border-r border-gray-800 z-30
          transition-all duration-300 ease-in-out
          ${isCollapsed ? 'w-20' : 'w-64'}
          ${isMobileOpen ? 'translate-x-0' : '-translate-x-full'}
          lg:translate-x-0
          ${className}
        `}
      >
        {/* Logo */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-800">
          <Link href="/" className="flex items-center">
            <div className="relative w-10 h-10 mr-2 flex-shrink-0">
              <img src={logoUrl} alt="Logo" className="w-full h-full" />
            </div>
            {!isCollapsed && (
              <span className="text-xl font-semibold text-white">{logoText}</span>
            )}
          </Link>
          {showCollapseButton && (
            <button
              className="text-gray-400 hover:text-white lg:block hidden"
              onClick={toggleCollapse}
              aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isCollapsed ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 5l7 7-7 7M5 5l7 7-7 7"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
                  />
                )}
              </svg>
            </button>
          )}
        </div>

        {/* Navigation */}
        <nav className="mt-4 px-2 overflow-y-auto h-[calc(100vh-12rem)]">
          <PermissionAwareMenu
            items={navigationItems}
            className="space-y-1"
            itemClassName={`
              flex items-center px-3 py-3 rounded-lg transition-colors
              text-gray-400 hover:bg-gray-800 hover:text-white
            `}
            activeItemClassName="bg-purple-600 text-white"
            iconClassName={`${isCollapsed ? 'w-7 h-7' : 'w-5 h-5'} mr-3`}
            childrenClassName="ml-6 mt-1 space-y-1"
            renderIcon={renderIcon}
          />
        </nav>

        {/* Bottom section */}
        <div className="absolute bottom-0 left-0 right-0 p-4">
          {children}
        </div>
      </aside>

      {/* Mobile menu button */}
      {showMobileToggle && (
        <button
          className="fixed top-4 left-4 z-10 lg:hidden text-gray-400 hover:text-white"
          onClick={toggleMobile}
          aria-label="Toggle mobile menu"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 6h16M4 12h16M4 18h16"
            />
          </svg>
        </button>
      )}
    </>
  );
};

export default PermissionAwareSidebar;
