'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../components/ui/card';
import AdminPageLayout from '../../../../components/admin/AdminPageLayout';

export default function UserManagement() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [users, setUsers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedUser, setSelectedUser] = useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Mock user data
  const mockUsers = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Admin', status: 'Active', lastLogin: '2023-05-15T10:30:00Z', createdAt: '2023-01-10T08:15:00Z' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>', role: 'User', status: 'Active', lastLogin: '2023-05-14T14:45:00Z', createdAt: '2023-01-15T09:20:00Z' },
    { id: 3, name: 'Bob Johnson', email: '<EMAIL>', role: 'Developer', status: 'Inactive', lastLogin: '2023-04-30T11:20:00Z', createdAt: '2023-02-05T11:10:00Z' },
    { id: 4, name: 'Alice Williams', email: '<EMAIL>', role: 'User', status: 'Active', lastLogin: '2023-05-12T16:15:00Z', createdAt: '2023-02-10T13:45:00Z' },
    { id: 5, name: 'Charlie Brown', email: '<EMAIL>', role: 'User', status: 'Active', lastLogin: '2023-05-13T09:10:00Z', createdAt: '2023-02-15T10:30:00Z' },
    { id: 6, name: 'Diana Prince', email: '<EMAIL>', role: 'Developer', status: 'Active', lastLogin: '2023-05-11T13:25:00Z', createdAt: '2023-03-01T14:20:00Z' },
    { id: 7, name: 'Ethan Hunt', email: '<EMAIL>', role: 'User', status: 'Suspended', lastLogin: '2023-04-25T15:40:00Z', createdAt: '2023-03-10T09:15:00Z' },
    { id: 8, name: 'Fiona Gallagher', email: '<EMAIL>', role: 'User', status: 'Active', lastLogin: '2023-05-10T10:05:00Z', createdAt: '2023-03-15T11:30:00Z' },
    { id: 9, name: 'George Miller', email: '<EMAIL>', role: 'User', status: 'Active', lastLogin: '2023-05-09T14:30:00Z', createdAt: '2023-04-01T08:45:00Z' },
    { id: 10, name: 'Hannah Baker', email: '<EMAIL>', role: 'Developer', status: 'Active', lastLogin: '2023-05-08T11:15:00Z', createdAt: '2023-04-10T10:20:00Z' },
    { id: 11, name: 'Ian Gallagher', email: '<EMAIL>', role: 'User', status: 'Active', lastLogin: '2023-05-07T16:50:00Z', createdAt: '2023-04-15T13:10:00Z' },
    { id: 12, name: 'Julia Roberts', email: '<EMAIL>', role: 'User', status: 'Inactive', lastLogin: '2023-04-20T09:30:00Z', createdAt: '2023-05-01T09:45:00Z' },
  ];

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setIsLoading(true);
        // In a real implementation, this would be an API call to get users
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Filter users based on search term
        const filteredUsers = mockUsers.filter(user =>
          user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.role.toLowerCase().includes(searchTerm.toLowerCase())
        );

        // Pagination
        const itemsPerPage = 10;
        const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
        setTotalPages(totalPages);

        // Get current page items
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

        setUsers(paginatedUsers);
      } catch (err) {
        console.error('Error fetching users:', err);
        setError('Failed to load users. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUsers();
  }, [searchTerm, currentPage]);

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setIsEditModalOpen(true);
  };

  const handleDeleteUser = (userId) => {
    if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      // In a real implementation, this would be an API call to delete the user
      console.log(`Deleting user with ID: ${userId}`);
      // Then refetch the users
      setUsers(users.filter(user => user.id !== userId));
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };

  return (
    <AdminPageLayout
      title="User Management"
      description="Manage user accounts, roles, and permissions."
    >
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
          <p className="text-red-200">{error}</p>
        </div>
      )}

      <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 mb-6">
        <CardHeader>
          <CardTitle className="text-white">Users</CardTitle>
          <CardDescription className="text-gray-400">Manage user accounts and permissions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between mb-4 gap-4">
            <div className="w-full md:w-1/3">
              <input
                type="text"
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              />
            </div>
            <button
              className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
              onClick={() => {
                setSelectedUser({
                  id: null,
                  name: '',
                  email: '',
                  role: 'User',
                  status: 'Active'
                });
                setIsEditModalOpen(true);
              }}
            >
              Add New User
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead className="bg-gray-700/50 text-gray-300">
                <tr>
                  <th className="px-4 py-2 rounded-tl-lg">Name</th>
                  <th className="px-4 py-2">Email</th>
                  <th className="px-4 py-2">Role</th>
                  <th className="px-4 py-2">Status</th>
                  <th className="px-4 py-2">Last Login</th>
                  <th className="px-4 py-2 rounded-tr-lg">Actions</th>
                </tr>
              </thead>
              <tbody className="text-gray-300">
                {isLoading ? (
                  <tr>
                    <td colSpan="6" className="px-4 py-2 text-center">Loading users...</td>
                  </tr>
                ) : users.length === 0 ? (
                  <tr>
                    <td colSpan="6" className="px-4 py-2 text-center">No users found</td>
                  </tr>
                ) : (
                  users.map((user, index) => (
                    <tr key={user.id} className={index % 2 === 0 ? 'bg-gray-700/30' : 'bg-gray-700/10'}>
                      <td className="px-4 py-2">{user.name}</td>
                      <td className="px-4 py-2">{user.email}</td>
                      <td className="px-4 py-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          user.role === 'Admin' ? 'bg-red-500/20 text-red-300' :
                          user.role === 'Developer' ? 'bg-blue-500/20 text-blue-300' :
                          'bg-green-500/20 text-green-300'
                        }`}>
                          {user.role}
                        </span>
                      </td>
                      <td className="px-4 py-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          user.status === 'Active' ? 'bg-green-500/20 text-green-300' :
                          user.status === 'Inactive' ? 'bg-gray-500/20 text-gray-300' :
                          'bg-red-500/20 text-red-300'
                        }`}>
                          {user.status}
                        </span>
                      </td>
                      <td className="px-4 py-2">{formatDate(user.lastLogin)}</td>
                      <td className="px-4 py-2">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEditUser(user)}
                            className="text-blue-400 hover:text-blue-300"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteUser(user.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-between items-center mt-4">
              <div className="text-gray-400">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="bg-gray-700 text-white px-3 py-1 rounded-lg disabled:opacity-50"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="bg-gray-700 text-white px-3 py-1 rounded-lg disabled:opacity-50"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* User Edit Modal */}
      {isEditModalOpen && selectedUser && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold text-white mb-4">
              {selectedUser.id ? 'Edit User' : 'Add New User'}
            </h2>

            <div className="space-y-4">
              <div>
                <label className="text-gray-300 block mb-1">Name</label>
                <input
                  type="text"
                  value={selectedUser.name}
                  onChange={(e) => setSelectedUser({...selectedUser, name: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                />
              </div>

              <div>
                <label className="text-gray-300 block mb-1">Email</label>
                <input
                  type="email"
                  value={selectedUser.email}
                  onChange={(e) => setSelectedUser({...selectedUser, email: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                />
              </div>

              <div>
                <label className="text-gray-300 block mb-1">Role</label>
                <select
                  value={selectedUser.role}
                  onChange={(e) => setSelectedUser({...selectedUser, role: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                >
                  <option value="User">User</option>
                  <option value="Developer">Developer</option>
                  <option value="Admin">Admin</option>
                </select>
              </div>

              <div>
                <label className="text-gray-300 block mb-1">Status</label>
                <select
                  value={selectedUser.status}
                  onChange={(e) => setSelectedUser({...selectedUser, status: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                >
                  <option value="Active">Active</option>
                  <option value="Inactive">Inactive</option>
                  <option value="Suspended">Suspended</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end mt-6 space-x-2">
              <button
                onClick={() => setIsEditModalOpen(false)}
                className="bg-gray-700 text-white px-4 py-2 rounded-lg"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  // In a real implementation, this would be an API call to save the user
                  console.log('Saving user:', selectedUser);
                  setIsEditModalOpen(false);

                  // Update the user list
                  if (selectedUser.id) {
                    setUsers(users.map(user => user.id === selectedUser.id ? selectedUser : user));
                  } else {
                    // Add new user with a mock ID
                    const newUser = {
                      ...selectedUser,
                      id: Math.max(...users.map(u => u.id)) + 1,
                      lastLogin: new Date().toISOString(),
                      createdAt: new Date().toISOString()
                    };
                    setUsers([...users, newUser]);
                  }
                }}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </AdminPageLayout>
  );
}
