'use client';

import { useState } from 'react';
import { NotificationChannel } from '../../../hooks/useSettings';

interface NotificationChannelsFormProps {
  channels: NotificationChannel[];
  isLoading: boolean;
  onUpdate: (channels: NotificationChannel[]) => void;
}

export default function NotificationChannelsForm({ 
  channels, 
  isLoading, 
  onUpdate 
}: NotificationChannelsFormProps) {
  const [localChannels, setLocalChannels] = useState<NotificationChannel[]>(channels);
  
  // Handle toggle channel
  const handleToggleChannel = (type: 'email' | 'sms' | 'in-app') => {
    const updatedChannels = localChannels.map(channel => 
      channel.type === type 
        ? { ...channel, enabled: !channel.enabled } 
        : channel
    );
    
    setLocalChannels(updatedChannels);
  };
  
  // Handle save changes
  const handleSaveChanges = () => {
    onUpdate(localChannels);
  };
  
  // Check if changes were made
  const hasChanges = JSON.stringify(channels) !== JSON.stringify(localChannels);
  
  return (
    <div>
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
        Choose how you'd like to receive notifications from CallSaver.app.
      </p>
      
      <div className="space-y-4 mb-6">
        {/* Email Notifications */}
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">Email Notifications</h4>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Receive notifications via email
            </p>
          </div>
          <div className="flex items-center">
            <button
              type="button"
              onClick={() => handleToggleChannel('email')}
              disabled={isLoading}
              className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                localChannels.find(c => c.type === 'email')?.enabled 
                  ? 'bg-indigo-600' 
                  : 'bg-gray-200 dark:bg-gray-600'
              }`}
              role="switch"
              aria-checked={localChannels.find(c => c.type === 'email')?.enabled}
            >
              <span 
                className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  localChannels.find(c => c.type === 'email')?.enabled 
                    ? 'translate-x-5' 
                    : 'translate-x-0'
                }`} 
              />
            </button>
          </div>
        </div>
        
        {/* SMS Notifications */}
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">SMS Notifications</h4>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Receive notifications via text message
            </p>
          </div>
          <div className="flex items-center">
            <button
              type="button"
              onClick={() => handleToggleChannel('sms')}
              disabled={isLoading}
              className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                localChannels.find(c => c.type === 'sms')?.enabled 
                  ? 'bg-indigo-600' 
                  : 'bg-gray-200 dark:bg-gray-600'
              }`}
              role="switch"
              aria-checked={localChannels.find(c => c.type === 'sms')?.enabled}
            >
              <span 
                className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  localChannels.find(c => c.type === 'sms')?.enabled 
                    ? 'translate-x-5' 
                    : 'translate-x-0'
                }`} 
              />
            </button>
          </div>
        </div>
        
        {/* In-App Notifications */}
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">In-App Notifications</h4>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Receive notifications within the CallSaver.app interface
            </p>
          </div>
          <div className="flex items-center">
            <button
              type="button"
              onClick={() => handleToggleChannel('in-app')}
              disabled={isLoading}
              className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                localChannels.find(c => c.type === 'in-app')?.enabled 
                  ? 'bg-indigo-600' 
                  : 'bg-gray-200 dark:bg-gray-600'
              }`}
              role="switch"
              aria-checked={localChannels.find(c => c.type === 'in-app')?.enabled}
            >
              <span 
                className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  localChannels.find(c => c.type === 'in-app')?.enabled 
                    ? 'translate-x-5' 
                    : 'translate-x-0'
                }`} 
              />
            </button>
          </div>
        </div>
      </div>
      
      <div className="flex justify-end">
        <button
          type="button"
          onClick={handleSaveChanges}
          disabled={isLoading || !hasChanges}
          className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Saving...' : 'Save Changes'}
        </button>
      </div>
    </div>
  );
}
