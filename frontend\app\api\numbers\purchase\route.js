import { NextResponse } from 'next/server';
// Temporarily comment out auth imports to simplify debugging
// import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
// import { cookies } from 'next/headers';

export async function POST(request) {
  console.log('Purchase API called');
  
  try {
    // Get the request body
    const body = await request.json();
    console.log('Purchase API request body:', body);
    
    // Validate the request
    if (!body.phoneNumber) {
      console.error('Purchase API error: Missing phone number');
      return NextResponse.json(
        { 
          success: false,
          message: 'Missing phone number' 
        }, 
        { status: 400 }
      );
    }
    
    // Simulate database operations and processing time
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Create a unique ID for the purchased number
    const numberId = `pn_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    // Format capabilities
    const capabilities = body.capabilities || {
      voice: true,
      sms: true,
      fax: false
    };
    
    // Create purchase details
    const purchaseDetails = {
      id: numberId,
      number: body.phoneNumber,
      phoneNumber: body.phoneNumber,
      friendlyName: body.phoneNumber.startsWith('+1') ? 
        `(${body.phoneNumber.substring(2, 5)}) ${body.phoneNumber.substring(5, 8)}-${body.phoneNumber.substring(8)}` : 
        body.phoneNumber,
      purchaseDate: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      status: 'active',
      isActive: true,
      isPrimary: false,
      cost: 1, // 1 token
      capabilities: capabilities,
      countryCode: body.countryCode || body.country || 'US',
      region: body.region || 'California',
      locality: body.locality || 'San Francisco'
    };
    
    console.log('Purchase API success:', purchaseDetails);
    
    // In a real implementation, this would store in a database
    // Instead, we'll return the data to be stored client-side in localStorage
    
    return NextResponse.json({
      success: true,
      message: 'Phone number purchased successfully',
      data: purchaseDetails
    });
    
  } catch (error) {
    console.error('Purchase API error:', error);
    return NextResponse.json(
      { 
        success: false,
        message: 'Failed to purchase phone number',
        error: error.message
      }, 
      { status: 500 }
    );
  }
} 