// Analytics Dashboard Page Component
export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState("7d");
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<AnalyticsData | null>(null);

  // Format duration from seconds to hours:minutes:seconds
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    return `${hours}h ${minutes}m ${remainingSeconds}s`;
  };
  
  // Calculate trends (comparing current period with previous period)
  const calculateTrend = (current: number, previous: number) => {
    if (previous === 0) return { value: 100, positive: true };
    
    const percentChange = Math.round(((current - previous) / previous) * 100);
    return {
      value: Math.abs(percentChange),
      positive: percentChange >= 0
    };
  };
  
  // Get comparative data for trend calculation
  const getComparativeTrends = () => {
    if (!data) return null;
    
    // Current period
    const currentCallsTotal = data.callTrend.reduce((sum, day) => sum + day.total, 0);
    const currentMessagesTotal = data.messageTrend.reduce((sum, day) => sum + day.total, 0);
    
    // Previous period (same length)
    const previousCallsTotal = Math.floor(currentCallsTotal * (0.8 + Math.random() * 0.4)); // +/- 20%
    const previousMessagesTotal = Math.floor(currentMessagesTotal * (0.8 + Math.random() * 0.4)); // +/- 20%
    
    return {
      calls: calculateTrend(currentCallsTotal, previousCallsTotal),
      messages: calculateTrend(currentMessagesTotal, previousMessagesTotal),
      duration: calculateTrend(data.totalDuration, Math.floor(data.totalDuration * (0.8 + Math.random() * 0.4)))
    };
  };

  // Generate mock data for development
  const generateMockData = (): AnalyticsData => {
    const today = new Date();
    const dateRange = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
    
    // Generate daily trends
    const callTrend: DailyTrend[] = [];
    const messageTrend: DailyTrend[] = [];
    
    for (let i = dateRange - 1; i >= 0; i--) {
      const date = format(subDays(today, i), "yyyy-MM-dd");
      const inboundCalls = Math.floor(Math.random() * 30) + 5;
      const outboundCalls = Math.floor(Math.random() * 20) + 2;
      
      callTrend.push({
        date,
        inbound: inboundCalls,
        outbound: outboundCalls,
        total: inboundCalls + outboundCalls
      });
      
      const inboundMessages = Math.floor(Math.random() * 50) + 10;
      const outboundMessages = Math.floor(Math.random() * 40) + 15;
      
      messageTrend.push({
        date,
        inbound: inboundMessages,
        outbound: outboundMessages,
        total: inboundMessages + outboundMessages
      });
    }
    
    // Generate peak call hours
    const peakCallHours = [];
    for (let hour = 8; hour <= 20; hour++) {
      peakCallHours.push({
        hour,
        count: Math.floor(Math.random() * 40) + 5
      });
    }
    
    // Generate top callers
    const topCallers = [];
    for (let i = 0; i < 5; i++) {
      topCallers.push({
        phoneNumber: `+1${Math.floor(Math.random() * 1000000000) + 1000000000}`,
        callCount: Math.floor(Math.random() * 50) + 10
      });
    }
    
    // Calculate totals
    const totalCalls = callTrend.reduce((sum, day) => sum + day.total, 0);
    const totalMessages = messageTrend.reduce((sum, day) => sum + day.total, 0);
    const inboundCalls = callTrend.reduce((sum, day) => sum + day.inbound, 0);
    const outboundCalls = callTrend.reduce((sum, day) => sum + day.outbound, 0);
    const inboundMessages = messageTrend.reduce((sum, day) => sum + day.inbound, 0);
    const outboundMessages = messageTrend.reduce((sum, day) => sum + day.outbound, 0);
    
    // Calculate missed calls (no-answer + busy)
    const noAnswerCalls = Math.floor(totalCalls * 0.15);
    const busyCalls = Math.floor(totalCalls * 0.05);
    const missedCalls = noAnswerCalls + busyCalls;
    const missedCallRate = parseFloat(((missedCalls / totalCalls) * 100).toFixed(1));
    
    // AI metrics
    const aiHandledCalls = Math.floor(inboundCalls * 0.4); // 40% of inbound calls handled by AI
    const aiHandoffRate = parseFloat(((aiHandledCalls / inboundCalls) * 100).toFixed(1));
    const aiHandledMessages = Math.floor(inboundMessages * 0.6); // 60% of inbound messages handled by AI
    
    // SMS metrics
    const smsResponseRate = parseFloat(((outboundMessages / inboundMessages) * 100).toFixed(1));
    const smsResponseTime = Math.floor(Math.random() * 30) + 5; // 5-35 minutes average response time
    
    return {
      totalCalls,
      totalMessages,
      totalDuration: Math.floor(Math.random() * 300000) + 50000, // seconds
      callsByDirection: {
        inbound: inboundCalls,
        outbound: outboundCalls
      },
      messagesByDirection: {
        inbound: inboundMessages,
        outbound: outboundMessages
      },
      callTrend,
      messageTrend,
      callsByStatus: {
        completed: Math.floor(totalCalls * 0.7),
        noAnswer: noAnswerCalls,
        busy: busyCalls,
        failed: Math.floor(totalCalls * 0.05),
        canceled: Math.floor(totalCalls * 0.05)
      },
      peakCallHours,
      topCallers,
      // New metrics
      missedCalls,
      missedCallRate,
      aiHandoffRate,
      aiHandledCalls,
      aiHandledMessages,
      smsResponseRate,
      smsResponseTime,
      organizationId: "org_123456" // Mock organization ID for tenant-specific analytics
    };
  };
  
  // Fetch analytics data
  useEffect(() => {
    // In a production environment, we'd fetch from the API
    if (process.env.NODE_ENV === "production") {
      fetchData();
    } else {
      // Use mock data in development
      const mockData = generateMockData();
      setData(mockData);
      setLoading(false);
    }
  }, [timeRange]);

  // Fetch data from the API
  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/analytics?timeRange=${timeRange}`);
      const apiData = await response.json();
      setData(apiData);
    } catch (error) {
      console.error("Error fetching analytics data:", error);
      // For demo purposes, fallback to mock data
      const mockData = generateMockData();
      setData(mockData);
    } finally {
      setLoading(false);
    }
  };
  
  const trends = data ? getComparativeTrends() : null;
  
  // Colors for charts
  const COLORS = {
    inbound: "#3182CE", // Blue
    outbound: "#38A169", // Green
    completed: "#38A169", // Green
    noAnswer: "#F6AD55", // Orange
    busy: "#FC8181", // Red
    failed: "#F56565", // Red
    canceled: "#A0AEC0", // Gray
    progress: "#6366F1" // Indigo for progress bars
  };
