'use client';

import { useState } from 'react';
import { useSubscription, useBillingHistory, usePaymentMethods } from '../../../hooks/useSettings';
import SubscriptionInfoPanel from './SubscriptionInfoPanel';
import BillingHistoryTable from './BillingHistoryTable';
import PaymentMethodsManager from './PaymentMethodsManager';
import LoadingSpinner from '../shared/LoadingSpinner';
import ErrorMessage from '../shared/ErrorMessage';

export default function BillingSubscriptionPanel() {
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  
  // Fetch subscription data
  const { 
    data: subscription, 
    isLoading: isLoadingSubscription, 
    isError: isErrorSubscription, 
    error: subscriptionError 
  } = useSubscription();
  
  // Fetch billing history
  const { 
    data: billingHistory, 
    isLoading: isLoadingHistory, 
    isError: isErrorHistory, 
    error: historyError 
  } = useBillingHistory();
  
  // Fetch payment methods
  const { 
    data: paymentMethods, 
    isLoading: isLoadingPaymentMethods, 
    isError: isErrorPaymentMethods, 
    error: paymentMethodsError 
  } = usePaymentMethods();
  
  // Handle success message
  const handleSuccess = (message: string) => {
    setSuccessMessage(message);
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };
  
  // Check if any data is loading
  const isLoading = isLoadingSubscription || isLoadingHistory || isLoadingPaymentMethods;
  
  // Check if there are any errors
  const isError = isErrorSubscription || isErrorHistory || isErrorPaymentMethods;
  
  // Get the first error
  const error = subscriptionError || historyError || paymentMethodsError;
  
  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }
  
  if (isError) {
    return (
      <ErrorMessage 
        title="Failed to load billing information" 
        message="We couldn't load your billing and subscription information. Please try again later."
        error={error instanceof Error ? error : undefined}
        onRetry={() => window.location.reload()}
      />
    );
  }
  
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Billing & Subscription</h2>
      
      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-800 dark:text-green-400">
          {successMessage}
        </div>
      )}
      
      <div className="space-y-8">
        {/* Subscription Info */}
        <div className="pb-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Current Subscription</h3>
          
          <SubscriptionInfoPanel 
            subscription={subscription} 
          />
        </div>
        
        {/* Payment Methods */}
        <div className="pb-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Payment Methods</h3>
          
          <PaymentMethodsManager 
            paymentMethods={paymentMethods || []} 
            onSuccess={handleSuccess}
          />
        </div>
        
        {/* Billing History */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Billing History</h3>
          
          <BillingHistoryTable 
            billingHistory={billingHistory || []} 
          />
        </div>
      </div>
    </div>
  );
}
