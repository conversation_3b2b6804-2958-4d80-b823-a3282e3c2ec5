  // Render component
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Buy a Phone Number</h2>
        <div className="flex items-center">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
            </svg>
            <span className="font-bold text-yellow-800">{userTokens || 0}</span> tokens
          </span>
        </div>
      </div>
      
      {/* Search Form */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <div>
              <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Country
              </label>
              <select
                id="country"
                name="country"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                value={countryCode}
                onChange={handleCountryChange}
              >
                {COUNTRIES.map((country) => (
                  <option key={country.code} value={country.code}>
                    {country.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Area code input - show only for US and CA */}
            {(countryCode === 'US' || countryCode === 'CA') && (
              <div>
                <label htmlFor="areaCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Area Code (optional)
                </label>
                <input
                  type="text"
                  id="areaCode"
                  name="areaCode"
                  maxLength="3"
                  placeholder="e.g. 415"
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                  value={areaCode}
                  onChange={handleAreaCodeChange}
                />
              </div>
            )}
            
            <div className="sm:col-span-2 lg:col-span-1">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Capabilities
              </label>
              <div className="space-x-4">
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-5 w-5 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500 dark:border-gray-600"
                    checked={capabilities.voice}
                    onChange={() => handleCapabilityChange('voice')}
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Voice</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-5 w-5 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500 dark:border-gray-600"
                    checked={capabilities.sms}
                    onChange={() => handleCapabilityChange('sms')}
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">SMS</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="checkbox"
                    className="form-checkbox h-5 w-5 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500 dark:border-gray-600"
                    checked={capabilities.fax}
                    onChange={() => handleCapabilityChange('fax')}
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Fax</span>
                </label>
              </div>
            </div>
          </div>
          
          <div className="mt-6 flex justify-end">
            <button
              type="submit"
              className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Searching...
                </>
              ) : (
                'Search for Numbers'
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Results Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">Available Phone Numbers</h3>

        {/* Loading indicator */}
        {isLoading && (
          <div className="flex justify-center items-center py-12">
            <svg className="animate-spin h-8 w-8 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="ml-4 text-lg text-gray-700 dark:text-gray-300">Searching for available numbers...</span>
          </div>
        )}

        {/* Error message */}
        {error && !isLoading && (
          <div className="rounded-md bg-red-50 dark:bg-red-900/50 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400 dark:text-red-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Empty state - when search is performed but no results */}
        {!isLoading && !error && phoneNumbers.length === 0 && (
          <div className="text-center py-12">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No phone numbers found</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Try adjusting your search criteria to find available numbers.</p>
          </div>
        )}

        {/* Phone numbers grid */}
        {!isLoading && !error && phoneNumbers.length > 0 && (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {phoneNumbers.map((number) => (
              <div
                key={number.id}
                className={`border rounded-lg p-4 cursor-pointer transition-shadow hover:shadow-md ${selectedNumber?.id === number.id ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20' : 'border-gray-300 dark:border-gray-700'}`}
                onClick={() => handleSelectNumber(number)}
              >
                <div className="flex justify-between items-start mb-2">
                  <div className="text-lg font-medium text-gray-900 dark:text-white">{number.formattedNumber}</div>
                  <div className="text-sm font-semibold bg-yellow-100 text-yellow-800 py-1 px-2 rounded">{number.price}</div>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">{number.location}</div>
                <div className="mt-2 flex space-x-2">
                  {number.capabilities.voice && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Voice
                    </span>
                  )}
                  {number.capabilities.sms && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      SMS
                    </span>
                  )}
                  {number.capabilities.fax && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                      Fax
                    </span>
                  )}
                </div>
                <div className="mt-3 flex justify-end">
                  <button
                    type="button"
                    className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSelectNumber(number);
                    }}
                  >
                    {selectedNumber?.id === number.id ? 'Selected' : 'Select'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Confirmation area */}
        {selectedNumber && !purchaseSuccess && (
          <div className="mt-8 bg-gray-50 dark:bg-gray-700 rounded-lg p-6 border border-gray-300 dark:border-gray-600">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Confirm Purchase</h4>
            <div className="mb-4">
              <p className="text-sm text-gray-700 dark:text-gray-300 mb-1">You've selected:</p>
              <div className="flex justify-between items-center">
                <p className="text-xl font-bold text-gray-900 dark:text-white">{selectedNumber.formattedNumber}</p>
                <div className="text-sm font-semibold bg-yellow-100 text-yellow-800 py-1 px-2 rounded-full flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
                  </svg>
                  <span>1 Token</span>
                </div>
              </div>
            </div>
            <div className="mb-6">
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">Number details:</p>
              <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                <li>Location: {selectedNumber.location}</li>
                <li>Type: {selectedNumber.type}</li>
                <li>
                  Capabilities: {[
                    selectedNumber.capabilities.voice ? 'Voice' : '',
                    selectedNumber.capabilities.sms ? 'SMS' : '',
                    selectedNumber.capabilities.fax ? 'Fax' : ''
                  ].filter(Boolean).join(', ')}
                </li>
              </ul>
            </div>
            <div className="flex justify-end">
              <button
                type="button"
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                onClick={handlePurchaseNumber}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  <>Buy for 1 Token</>
                )}
              </button>
            </div>
          </div>
        )}

        {/* Success message */}
        {purchaseSuccess && purchasedNumberDetails && (
          <div className="mt-8 bg-green-50 dark:bg-green-900/20 rounded-lg p-6 border border-green-200 dark:border-green-800">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0 bg-green-100 dark:bg-green-800 rounded-full p-2">
                <svg className="h-6 w-6 text-green-600 dark:text-green-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h4 className="ml-3 text-lg font-medium text-gray-900 dark:text-white">Phone number purchased successfully!</h4>
            </div>
            
            <div className="mb-6">
              <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">Your new phone number:</p>
              <p className="text-xl font-bold text-gray-900 dark:text-white">{purchasedNumberDetails.friendlyName}</p>
              <div className="mt-4 text-sm text-gray-700 dark:text-gray-300">
                <p className="mb-1">Purchase Date: {new Date(purchasedNumberDetails.purchaseDate).toLocaleString()}</p>
                <p className="mb-1">Location: {purchasedNumberDetails.location}</p>
                <p className="mb-1">
                  Capabilities: {[
                    purchasedNumberDetails.capabilities.voice ? 'Voice' : '',
                    purchasedNumberDetails.capabilities.sms ? 'SMS' : '',
                    purchasedNumberDetails.capabilities.fax ? 'Fax' : ''
                  ].filter(Boolean).join(', ')}
                </p>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row sm:space-x-4 space-y-4 sm:space-y-0">
              <a
                href="/dashboard/settings"
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Configure Settings
              </a>
              <a
                href="/dashboard"
                className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
              >
                Go to Dashboard
              </a>
              <button
                type="button"
                onClick={() => {
                  setPurchaseSuccess(false);
                  setPurchasedNumberDetails(null);
                }}
                className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
              >
                Buy Another Number
              </button>
            </div>
          </div>
        )}

        {/* Debug area - only in development */}
        {process.env.NODE_ENV !== 'production' && debugInfo && (
          <div className="mt-12">
            <div className="border-t border-gray-300 dark:border-gray-700 pt-4">
              <details className="text-xs text-gray-500 dark:text-gray-400">
                <summary className="cursor-pointer font-medium">Debug Information</summary>
                <pre className="mt-2 p-4 bg-gray-100 dark:bg-gray-900 rounded overflow-auto max-h-96">
                  {JSON.stringify(debugInfo, null, 2)}
                </pre>
              </details>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}