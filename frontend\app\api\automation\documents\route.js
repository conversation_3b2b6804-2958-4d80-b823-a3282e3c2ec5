import { createServerClient } from '@supabase/ssr'; // Use ssr client
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export const dynamic = 'force-dynamic'; // Ensure fresh data on each request

export async function GET(request) {
  console.log('API route /api/automation/documents invoked'); 
  const cookieStore = cookies(); // Get cookie store instance
  // Use createServerClient from @supabase/ssr
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        get: (name) => cookieStore.get(name)?.value,
        set: (name, value, options) => {
           console.warn("Attempting to set cookie in Route Handler - might not persist");
           cookieStore.set({ name, value, ...options });
        },
        remove: (name, options) => {
           console.warn("Attempting to remove cookie in Route Handler - might not persist");
           cookieStore.set({ name, value: '', ...options });
        },
      },
    }
  );

  try {
    // Get the current user session
    console.log('Attempting to get session...');
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error('Error getting session:', sessionError);
      // It's possible the cookie parsing error happens here
      if (sessionError.message.includes('Unexpected token')) {
         console.error("Potential cookie parsing error detected.");
         // Consider clearing potentially corrupted cookies or advising user to clear cookies
      }
      return NextResponse.json({ success: false, message: `Session error: ${sessionError.message}` }, { status: 500 });
    }

    if (!session) {
      console.log('No session found, returning unauthorized');
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.id;
    console.log(`Session found, fetching documents for user: ${userId}`);

    // Fetch documents for the current user from the database
    const { data: documents, error: fetchError } = await supabase
      .from('training_documents')
      .select('id, file_name, file_size, mime_type, status, uploaded_at, error_message') // Select relevant columns
      .eq('user_id', userId)
      .order('uploaded_at', { ascending: false }); // Order by newest first

    if (fetchError) {
      console.error('Error fetching training documents:', fetchError);
      throw new Error(`Database error: ${fetchError.message}`);
    }

    console.log(`[API /documents] Fetched ${documents?.length || 0} documents for user ${userId}`);

    // Format the data slightly for consistency (e.g., size in KB/MB) if needed, or do it on frontend
    const formattedDocuments = documents.map(doc => ({
      id: doc.id, // Ensure id is included
      name: doc.file_name,
      status: doc.status,
      added: doc.uploaded_at,
      size: doc.file_size ? `${(doc.file_size / 1024).toFixed(1)} KB` : 'N/A', // Use file_size
      errorMessage: doc.error_message 
    }));


    return NextResponse.json({ success: true, documents: formattedDocuments });

  } catch (error) {
    console.error('[API /documents] GET Error:', error);
    return NextResponse.json({ success: false, message: error.message || 'Failed to fetch documents' }, { status: 500 });
  }
}
