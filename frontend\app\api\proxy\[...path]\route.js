import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

// Proxy route handler to forward requests to the backend with authentication
export async function GET(request, { params }) {
  return handleRequest(request, params);
}

export async function POST(request, { params }) {
  return handleRequest(request, params);
}

// Add other methods (PUT, DELETE, etc.) if needed

async function handleRequest(request, { params }) {
  const cookieStore = cookies();
  const requestPath = params.path.join('/'); // Reconstruct the path
  const searchParams = request.nextUrl.search; // Get original query string
  
  // 1. Create Supabase server client to get session
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        get: (name) => cookieStore.get(name)?.value,
        // No need to set/remove cookies in this read-only context
        set: () => {},
        remove: () => {},
      },
    }
  );

  // 2. Get the current session and access token
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();

  if (sessionError || !session) {
    console.error('Proxy Error: Could not get Supabase session.', sessionError);
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
  }

  const accessToken = session.access_token;

  // 3. Prepare the request to the actual backend
  const backendUrl = `${process.env.APP_BASE_URL}/${requestPath}${searchParams}`;
  console.log(`Proxying ${request.method} request to: ${backendUrl}`);

  const headers = new Headers(request.headers); // Clone original headers
  headers.set('Authorization', `Bearer ${accessToken}`);
  // Remove host header as it will be set by fetch
  headers.delete('host'); 
  // Ensure content-type is forwarded if present (especially for POST/PUT)
  if (request.headers.has('content-type')) {
      headers.set('content-type', request.headers.get('content-type'));
  }
  
  // 4. Forward the request to the backend
  try {
    const backendResponse = await fetch(backendUrl, {
      method: request.method,
      headers: headers,
      // Forward body only if it exists (GET/HEAD don't have body)
      body: request.method !== 'GET' && request.method !== 'HEAD' ? request.body : undefined,
      // Important for streaming responses correctly
      duplex: 'half', 
    });

    console.log(`Backend response status: ${backendResponse.status}`);
    
    // 5. Return the response from the backend to the client
    // Need to create a new NextResponse to stream the body correctly
    return new NextResponse(backendResponse.body, {
        status: backendResponse.status,
        statusText: backendResponse.statusText,
        headers: backendResponse.headers, // Forward backend headers
    });

  } catch (error) {
    console.error(`Proxy Error: Failed to fetch backend URL (${backendUrl}):`, error);
    return NextResponse.json({ error: 'Failed to connect to the backend service' }, { status: 502 }); // Bad Gateway
  }
} 