'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { AutomationConfig } from './AutomationConfigPanel';
import LoadingSpinner from '../shared/LoadingSpinner';

interface VoicemailConfigProps {
  numberId: string;
  initialData: AutomationConfig['voicemailConfig'];
}

export default function VoicemailConfig({
  numberId,
  initialData,
}: VoicemailConfigProps) {
  // State for form data
  const [formData, setFormData] = useState(initialData);
  
  // Get the query client
  const queryClient = useQueryClient();

  // Mutation for updating voicemail config
  const updateMutation = useMutation({
    mutationFn: async (data: AutomationConfig['voicemailConfig']) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data: response } = await axios.put(`/api/numbers/${numberId}/automations`, {
        voicemailConfig: data,
      });
      return response;
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['automations', numberId] });
      
      // Show success toast (you can use a toast library like react-hot-toast)
      console.log('Voicemail configuration updated successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to update voicemail configuration');
    },
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateMutation.mutate(formData);
  };

  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked,
    }));
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Voicemail Settings
        </h3>
        <div className="space-y-4">
          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id="transcriptionEnabled"
                name="transcriptionEnabled"
                type="checkbox"
                checked={formData.transcriptionEnabled}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-700 rounded"
                disabled={updateMutation.isPending}
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="transcriptionEnabled" className="font-medium text-gray-700 dark:text-gray-300">
                Enable Transcription
              </label>
              <p className="text-gray-500 dark:text-gray-400">
                Automatically transcribe voicemail messages to text.
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id="aiSummaryEnabled"
                name="aiSummaryEnabled"
                type="checkbox"
                checked={formData.aiSummaryEnabled}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-700 rounded"
                disabled={updateMutation.isPending || !formData.transcriptionEnabled}
              />
            </div>
            <div className="ml-3 text-sm">
              <label 
                htmlFor="aiSummaryEnabled" 
                className={`font-medium ${
                  formData.transcriptionEnabled 
                    ? 'text-gray-700 dark:text-gray-300' 
                    : 'text-gray-400 dark:text-gray-500'
                }`}
              >
                Enable AI Summary
              </label>
              <p className={`${
                formData.transcriptionEnabled 
                  ? 'text-gray-500 dark:text-gray-400' 
                  : 'text-gray-400 dark:text-gray-500'
              }`}>
                Use AI to generate a summary of the voicemail content.
                {!formData.transcriptionEnabled && ' (Requires transcription to be enabled)'}
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id="notificationEnabled"
                name="notificationEnabled"
                type="checkbox"
                checked={formData.notificationEnabled}
                onChange={handleCheckboxChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-700 rounded"
                disabled={updateMutation.isPending}
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="notificationEnabled" className="font-medium text-gray-700 dark:text-gray-300">
                Enable Email Notifications
              </label>
              <p className="text-gray-500 dark:text-gray-400">
                Receive email notifications when new voicemails are received.
              </p>
            </div>
          </div>

          {formData.notificationEnabled && (
            <div className="pl-7">
              <label htmlFor="notificationEmail" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Notification Email
              </label>
              <input
                type="email"
                name="notificationEmail"
                id="notificationEmail"
                value={formData.notificationEmail || ''}
                onChange={handleInputChange}
                placeholder="<EMAIL>"
                className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                disabled={updateMutation.isPending}
                required={formData.notificationEnabled}
              />
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-end">
        <button
          type="submit"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={updateMutation.isPending}
        >
          {updateMutation.isPending ? (
            <>
              <LoadingSpinner size="small" color="white" />
              <span className="ml-2">Saving...</span>
            </>
          ) : (
            'Save Changes'
          )}
        </button>
      </div>
    </form>
  );
}
