'use client';

import { useState } from 'react';
import { useApiKeys } from '../../../hooks/useSettings';
import Api<PERSON>eysList from './ApiKeysList';
import GenerateApiKeyForm from './GenerateApiKeyForm';
import Revoke<PERSON>piKeyDialog from './RevokeApiKeyDialog';
import LoadingSpinner from '../shared/LoadingSpinner';
import ErrorMessage from '../shared/ErrorMessage';

export default function ApiKeyManagementPanel() {
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showGenerateForm, setShowGenerateForm] = useState(false);
  const [keyToRevoke, setKeyToRevoke] = useState<string | null>(null);
  const [newApiKey, setNewApiKey] = useState<string | null>(null);
  
  // Fetch API keys
  const { 
    data: apiKeys, 
    isLoading, 
    isError, 
    error 
  } = useApiKeys();
  
  // Handle success message
  const handleSuccess = (message: string) => {
    setSuccessMessage(message);
    
    // Clear success message after 5 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 5000);
  };
  
  // Handle generate API key
  const handleGenerateKey = (fullKey: string) => {
    setNewApiKey(fullKey);
    setShowGenerateForm(false);
    handleSuccess('API key generated successfully');
  };
  
  // Handle revoke API key
  const handleRevokeKey = () => {
    setKeyToRevoke(null);
    handleSuccess('API key revoked successfully');
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }
  
  if (isError) {
    return (
      <ErrorMessage 
        title="Failed to load API keys" 
        message="We couldn't load your API keys. Please try again later."
        error={error instanceof Error ? error : undefined}
        onRetry={() => window.location.reload()}
      />
    );
  }
  
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">API Key Management</h2>
      
      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-800 dark:text-green-400">
          {successMessage}
        </div>
      )}
      
      {/* New API Key Alert */}
      {newApiKey && (
        <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-300">
                Important: Save your API key now
              </h3>
              <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-200">
                <p>
                  This is the only time your full API key will be shown. Please copy it and store it securely.
                </p>
                <div className="mt-2">
                  <div className="flex items-center">
                    <input
                      type="text"
                      readOnly
                      value={newApiKey}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white font-mono text-sm"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        navigator.clipboard.writeText(newApiKey);
                        handleSuccess('API key copied to clipboard');
                      }}
                      className="ml-2 px-3 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Copy
                    </button>
                  </div>
                </div>
                <div className="mt-2">
                  <button
                    type="button"
                    onClick={() => setNewApiKey(null)}
                    className="text-sm text-yellow-800 dark:text-yellow-300 underline"
                  >
                    I've saved my API key
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      
      <div className="space-y-8">
        {/* API Keys List */}
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Your API Keys</h3>
            <button
              type="button"
              onClick={() => setShowGenerateForm(true)}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Generate New Key
            </button>
          </div>
          
          <ApiKeysList 
            apiKeys={apiKeys || []} 
            onRevoke={(keyId) => setKeyToRevoke(keyId)}
          />
        </div>
        
        {/* Generate API Key Form */}
        {showGenerateForm && (
          <div className="mt-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Generate New API Key
            </h4>
            
            <GenerateApiKeyForm 
              onSuccess={handleGenerateKey}
              onCancel={() => setShowGenerateForm(false)}
            />
          </div>
        )}
        
        {/* API Key Usage Information */}
        <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">API Documentation</h3>
          
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Use your API key to authenticate requests to the CallSaver.app API. Learn more about how to use the API in our documentation.
          </p>
          
          <a 
            href="/api-docs" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 font-medium"
          >
            View API Documentation →
          </a>
        </div>
      </div>
      
      {/* Revoke API Key Dialog */}
      {keyToRevoke && (
        <RevokeApiKeyDialog 
          apiKeyId={keyToRevoke}
          isOpen={!!keyToRevoke}
          onClose={() => setKeyToRevoke(null)}
          onConfirm={handleRevokeKey}
        />
      )}
    </div>
  );
}
