'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/app/components/DashboardLayout';

export default function TrainAIPage() {
  const router = useRouter();
  
  // State for form inputs and data
  const [query, setQuery] = useState('');
  const [response, setResponse] = useState('');
  const [category, setCategory] = useState('general');
  const [trainingData, setTrainingData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  
  // Fetch existing training data
  const fetchTrainingData = async () => {
    try {
      setIsLoading(true);
      const res = await fetch('/api/twilio/ai-training');
      const data = await res.json();
      
      if (data.success) {
        setTrainingData(data.trainingData || []);
      } else {
        console.error('Failed to fetch training data:', data.error);
      }
    } catch (error) {
      console.error('Error fetching training data:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Load training data on component mount
  useEffect(() => {
    fetchTrainingData();
  }, []);
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!query.trim() || !response.trim()) {
      setMessage('Please fill in both the query and response fields.');
      return;
    }
    
    setIsLoading(true);
    setMessage('');
    
    try {
      const res = await fetch('/api/twilio/ai-training', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          response,
          category,
        }),
      });
      
      const data = await res.json();
      
      if (data.success) {
        setMessage('Training example added successfully!');
        setQuery('');
        setResponse('');
        fetchTrainingData(); // Refresh the training data
      } else {
        setMessage(`Error: ${data.error || 'Failed to add training example'}`);
      }
    } catch (error) {
      console.error('Error adding training example:', error);
      setMessage(`Error: ${error.message || 'Something went wrong'}`);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Calculate statistics
  const categoryStats = trainingData.reduce((acc, example) => {
    acc[example.category] = (acc[example.category] || 0) + 1;
    return acc;
  }, {});
  
  return (
    <DashboardLayout>
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-white mb-2">Train AI Assistant</h1>
          <p className="text-gray-400">
            Teach your AI assistant how to respond to SMS messages by providing examples of questions and ideal responses.
          </p>
        </div>
        
        {/* Training Form */}
        <div className="bg-gray-900/70 backdrop-blur-lg rounded-lg border border-purple-500/20 shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Add Training Example</h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-400 mb-1">
                Category
              </label>
              <select
                id="category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="general">General</option>
                <option value="pricing">Pricing & Plans</option>
                <option value="technical">Technical Support</option>
                <option value="account">Account Management</option>
                <option value="features">Features & Usage</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="query" className="block text-sm font-medium text-gray-400 mb-1">
                User Query (What the user might ask)
              </label>
              <textarea
                id="query"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                rows={3}
                className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="E.g., How do I download my call recordings?"
              />
            </div>
            
            <div>
              <label htmlFor="response" className="block text-sm font-medium text-gray-400 mb-1">
                AI Response (How the assistant should reply)
              </label>
              <textarea
                id="response"
                value={response}
                onChange={(e) => setResponse(e.target.value)}
                rows={5}
                className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="E.g., You can download your call recordings from the dashboard. Go to the Calls section, find the recording you want, and click the download button."
              />
            </div>
            
            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="px-5 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition duration-200 flex items-center"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  'Add Training Example'
                )}
              </button>
            </div>
            
            {message && (
              <div className={`mt-3 p-3 rounded ${message.includes('Error') ? 'bg-red-500/20 text-red-200' : 'bg-green-500/20 text-green-200'}`}>
                {message}
              </div>
            )}
          </form>
        </div>
        
        {/* Training Data Statistics */}
        <div className="bg-gray-900/70 backdrop-blur-lg rounded-lg border border-purple-500/20 shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Training Statistics</h2>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
            <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
              <p className="text-sm text-gray-400">Total Examples</p>
              <p className="text-2xl font-bold text-white">{trainingData.length}</p>
            </div>
            
            {Object.entries(categoryStats).map(([cat, count]) => (
              <div key={cat} className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
                <p className="text-sm text-gray-400">{cat.charAt(0).toUpperCase() + cat.slice(1)}</p>
                <p className="text-2xl font-bold text-white">{count}</p>
              </div>
            ))}
          </div>
        </div>
        
        {/* Training Data List */}
        <div className="bg-gray-900/70 backdrop-blur-lg rounded-lg border border-purple-500/20 shadow-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Training Examples</h2>
          
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin h-8 w-8 border-4 border-purple-500 border-t-transparent rounded-full"></div>
              <p className="ml-3 text-gray-400">Loading training data...</p>
            </div>
          ) : (
            <>
              {trainingData.length === 0 ? (
                <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-8 text-center">
                  <p className="text-gray-400">
                    No training examples yet. Add some examples to teach your AI assistant how to respond to user queries.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {trainingData.map((example, index) => (
                    <div key={index} className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs font-medium px-2 py-1 bg-purple-500/20 text-purple-300 rounded">
                          {example.category.charAt(0).toUpperCase() + example.category.slice(1)}
                        </span>
                        <span className="text-xs text-gray-500">
                          {new Date(example.timestamp).toLocaleString()}
                        </span>
                      </div>
                      
                      <div className="mb-3">
                        <h3 className="text-sm font-medium text-gray-400 mb-1">User Query:</h3>
                        <p className="text-white bg-gray-700/50 rounded-lg p-3">{example.query}</p>
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-gray-400 mb-1">AI Response:</h3>
                        <p className="text-white bg-gray-700/50 rounded-lg p-3">{example.response}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
} 