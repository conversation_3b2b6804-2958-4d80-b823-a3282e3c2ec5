'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../../components/ui/tabs';
import AdminPageLayout from '../../../../components/admin/AdminPageLayout';

export default function BillingManagement() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [billingStats, setBillingStats] = useState({
    totalRevenue: 0,
    monthlyRevenue: 0,
    activeSubscriptions: 0,
    trialAccounts: 0,
    averageRevenue: 0,
    churnRate: 0
  });
  const [subscriptionPlans, setSubscriptionPlans] = useState([]);
  const [transactions, setTransactions] = useState([]);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Mock data
  const mockSubscriptionPlans = [
    { id: 1, name: 'Basic', price: 9.99, features: ['5 phone numbers', '1000 minutes', '1000 SMS'], active: true },
    { id: 2, name: 'Pro', price: 29.99, features: ['15 phone numbers', '5000 minutes', '5000 SMS', 'Advanced analytics'], active: true },
    { id: 3, name: 'Enterprise', price: 99.99, features: ['Unlimited phone numbers', 'Unlimited minutes', 'Unlimited SMS', 'Advanced analytics', 'Priority support', 'Custom integrations'], active: true },
    { id: 4, name: 'Legacy Plan', price: 19.99, features: ['10 phone numbers', '2000 minutes', '2000 SMS'], active: false }
  ];

  const mockTransactions = [
    { id: 1, userId: 101, userName: 'John Doe', plan: 'Pro', amount: 29.99, status: 'Completed', date: '2023-05-01T10:30:00Z' },
    { id: 2, userId: 102, userName: 'Jane Smith', plan: 'Basic', amount: 9.99, status: 'Completed', date: '2023-05-02T14:45:00Z' },
    { id: 3, userId: 103, userName: 'Bob Johnson', plan: 'Enterprise', amount: 99.99, status: 'Completed', date: '2023-05-03T11:20:00Z' },
    { id: 4, userId: 104, userName: 'Alice Williams', plan: 'Pro', amount: 29.99, status: 'Failed', date: '2023-05-04T16:15:00Z' },
    { id: 5, userId: 105, userName: 'Charlie Brown', plan: 'Basic', amount: 9.99, status: 'Completed', date: '2023-05-05T09:10:00Z' },
    { id: 6, userId: 106, userName: 'Diana Prince', plan: 'Enterprise', amount: 99.99, status: 'Pending', date: '2023-05-06T13:30:00Z' },
    { id: 7, userId: 107, userName: 'Edward Norton', plan: 'Pro', amount: 29.99, status: 'Completed', date: '2023-05-07T10:45:00Z' },
    { id: 8, userId: 108, userName: 'Fiona Apple', plan: 'Basic', amount: 9.99, status: 'Completed', date: '2023-05-08T15:20:00Z' }
  ];

  useEffect(() => {
    const fetchBillingData = async () => {
      try {
        setIsLoading(true);
        // In a real implementation, these would be API calls
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        setBillingStats({
          totalRevenue: 125000,
          monthlyRevenue: 15000,
          activeSubscriptions: 876,
          trialAccounts: 234,
          averageRevenue: 28.75,
          churnRate: 3.2
        });
        
        setSubscriptionPlans(mockSubscriptionPlans);
        setTransactions(mockTransactions);
      } catch (err) {
        console.error('Error fetching billing data:', err);
        setError('Failed to load billing data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBillingData();
  }, []);

  const handleEditPlan = (plan) => {
    setSelectedPlan(plan);
    setIsEditModalOpen(true);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };

  return (
    <AdminPageLayout
      title="Billing & Subscriptions"
      description="Manage subscription plans, pricing, and view revenue reports."
    >
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
          <p className="text-red-200">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white">Total Revenue</CardTitle>
            <CardDescription className="text-gray-400">Lifetime revenue</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-white">{isLoading ? '...' : formatCurrency(billingStats.totalRevenue)}</p>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white">Monthly Revenue</CardTitle>
            <CardDescription className="text-gray-400">Current month</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-white">{isLoading ? '...' : formatCurrency(billingStats.monthlyRevenue)}</p>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white">Active Subscriptions</CardTitle>
            <CardDescription className="text-gray-400">Paying customers</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-white">{isLoading ? '...' : billingStats.activeSubscriptions}</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="plans" className="w-full">
        <TabsList className="bg-gray-800/70 border border-purple-500/20 mb-6">
          <TabsTrigger value="plans">Subscription Plans</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="analytics">Revenue Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="plans" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-white">Subscription Plans</h2>
            <button 
              className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
              onClick={() => {
                setSelectedPlan({
                  id: null,
                  name: '',
                  price: 0,
                  features: [],
                  active: true
                });
                setIsEditModalOpen(true);
              }}
            >
              Add New Plan
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {isLoading ? (
              <p className="text-gray-400">Loading plans...</p>
            ) : subscriptionPlans.length === 0 ? (
              <p className="text-gray-400">No subscription plans found</p>
            ) : (
              subscriptionPlans.map(plan => (
                <Card key={plan.id} className={`bg-gray-700/50 border ${plan.active ? 'border-purple-500/30' : 'border-gray-600/30'}`}>
                  <CardHeader>
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-white">{plan.name}</CardTitle>
                      {!plan.active && (
                        <span className="bg-gray-600/50 text-gray-300 text-xs px-2 py-1 rounded-full">
                          Inactive
                        </span>
                      )}
                    </div>
                    <CardDescription className="text-gray-400">
                      {formatCurrency(plan.price)}/month
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="text-gray-300 space-y-2">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center">
                          <span className="mr-2">•</span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <div className="mt-4 flex justify-end">
                      <button 
                        onClick={() => handleEditPlan(plan)}
                        className="text-purple-400 hover:text-purple-300"
                      >
                        Edit Plan
                      </button>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="transactions" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">Recent Transactions</h2>
          
          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead className="bg-gray-700/50 text-gray-300">
                <tr>
                  <th className="px-4 py-2 rounded-tl-lg">ID</th>
                  <th className="px-4 py-2">User</th>
                  <th className="px-4 py-2">Plan</th>
                  <th className="px-4 py-2">Amount</th>
                  <th className="px-4 py-2">Status</th>
                  <th className="px-4 py-2 rounded-tr-lg">Date</th>
                </tr>
              </thead>
              <tbody className="text-gray-300">
                {isLoading ? (
                  <tr>
                    <td colSpan="6" className="px-4 py-2 text-center">Loading transactions...</td>
                  </tr>
                ) : transactions.length === 0 ? (
                  <tr>
                    <td colSpan="6" className="px-4 py-2 text-center">No transactions found</td>
                  </tr>
                ) : (
                  transactions.map((transaction, index) => (
                    <tr key={transaction.id} className={index % 2 === 0 ? 'bg-gray-700/30' : 'bg-gray-700/10'}>
                      <td className="px-4 py-2 font-medium">{transaction.id}</td>
                      <td className="px-4 py-2">{transaction.userName}</td>
                      <td className="px-4 py-2">{transaction.plan}</td>
                      <td className="px-4 py-2">{formatCurrency(transaction.amount)}</td>
                      <td className="px-4 py-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          transaction.status === 'Completed' ? 'bg-green-500/20 text-green-300' :
                          transaction.status === 'Pending' ? 'bg-yellow-500/20 text-yellow-300' :
                          'bg-red-500/20 text-red-300'
                        }`}>
                          {transaction.status}
                        </span>
                      </td>
                      <td className="px-4 py-2">{formatDate(transaction.date)}</td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">Revenue Analytics</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Average Revenue Per User</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : formatCurrency(billingStats.averageRevenue)}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Churn Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : `${billingStats.churnRate}%`}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Trial Accounts</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : billingStats.trialAccounts}</p>
              </CardContent>
            </Card>
          </div>
          
          <div className="bg-gray-700/30 border border-gray-600/30 rounded-lg p-4 text-center">
            <p className="text-gray-400">Revenue chart visualization would be displayed here</p>
            <div className="h-64 flex items-center justify-center">
              <p className="text-gray-500">Chart Placeholder</p>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Plan Edit Modal */}
      {isEditModalOpen && selectedPlan && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold text-white mb-4">
              {selectedPlan.id ? 'Edit Plan' : 'Add New Plan'}
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className="text-gray-300 block mb-1">Plan Name</label>
                <input 
                  type="text" 
                  value={selectedPlan.name} 
                  onChange={(e) => setSelectedPlan({...selectedPlan, name: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                />
              </div>
              
              <div>
                <label className="text-gray-300 block mb-1">Monthly Price ($)</label>
                <input 
                  type="number" 
                  value={selectedPlan.price} 
                  onChange={(e) => setSelectedPlan({...selectedPlan, price: parseFloat(e.target.value)})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                  step="0.01"
                  min="0"
                />
              </div>
              
              <div>
                <label className="text-gray-300 block mb-1">Features (one per line)</label>
                <textarea 
                  value={selectedPlan.features.join('\n')} 
                  onChange={(e) => setSelectedPlan({
                    ...selectedPlan, 
                    features: e.target.value.split('\n').filter(feature => feature.trim() !== '')
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white h-32"
                  placeholder="Enter features, one per line"
                />
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="active" 
                  checked={selectedPlan.active} 
                  onChange={(e) => setSelectedPlan({...selectedPlan, active: e.target.checked})}
                  className="mr-2 h-4 w-4"
                />
                <label htmlFor="active" className="text-gray-300">Active</label>
              </div>
            </div>
            
            <div className="flex justify-end mt-6 space-x-2">
              <button 
                onClick={() => setIsEditModalOpen(false)}
                className="bg-gray-700 text-white px-4 py-2 rounded-lg"
              >
                Cancel
              </button>
              <button 
                onClick={() => {
                  // In a real implementation, this would be an API call to save the plan
                  console.log('Saving plan:', selectedPlan);
                  setIsEditModalOpen(false);
                  
                  // Update the plan list
                  if (selectedPlan.id) {
                    setSubscriptionPlans(subscriptionPlans.map(plan => 
                      plan.id === selectedPlan.id ? selectedPlan : plan
                    ));
                  } else {
                    // Add new plan with a mock ID
                    const newPlan = {
                      ...selectedPlan,
                      id: Math.max(...subscriptionPlans.map(p => p.id), 0) + 1
                    };
                    setSubscriptionPlans([...subscriptionPlans, newPlan]);
                  }
                }}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </AdminPageLayout>
  );
}
