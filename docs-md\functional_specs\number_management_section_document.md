---
description:
globs:
alwaysApply: false
---
# Number Management Section Functional Document (`number_management_section_document.mdc`)

## 1. Purpose and Scope

**Purpose:** Enable users to search for, purchase, manage, and release phone numbers (both traditional/Twilio and potentially eSIM profiles) within the CallSaver platform. This section serves as the central hub for controlling the user's telephony assets.

**Scope:**
- Search for available phone numbers based on criteria (country, area code, capabilities like SMS/Voice/MMS, type like Local/Toll-Free).
- Display search results with number details and pricing.
- Facilitate the purchase process, including payment/credit deduction.
- List all currently owned/active numbers associated with the account.
- Allow users to assign friendly names or labels to their numbers.
- Configure basic number settings (e.g., primary use, link to specific automation profiles).
- Initiate the process of releasing/deleting a number from the account.
- Handle both traditional numbers (e.g., via Twilio) and eSIM provisioning/management (via providers like Airalo).

## 2. User Interactions

- **Search Numbers:** Enter search criteria (country, area code, features) into a form.
- **View Search Results:** Browse a list of available numbers matching the criteria.
- **Select Number for Purchase:** Choose a number from the search results.
- **Confirm Purchase:** Review purchase details (number, cost) and confirm the transaction.
- **View Owned Numbers:** See a list of all numbers currently active on the account.
- **Edit Number Settings:** Click on an owned number to open a modal or panel to edit its friendly name or other basic configurations.
- **Initiate Release:** Select an owned number and click a "Release" or "Delete" button.
- **Confirm Release:** Confirm the action in a confirmation dialog, acknowledging any consequences (loss of number, associated data).
- **Manage eSIMs:** (If applicable) View associated eSIM profiles, potentially trigger installation/activation flows (linking out or using provider APIs).

## 3. Backend Integrations & Services Used

- **Number Provisioning Service:** Interfaces with external providers (Twilio API, Airalo API, other eSIM providers) to search, purchase, configure, and release numbers/eSIMs.
- **Billing Service:** Handles credit deduction for number purchases and recurring costs. Verifies sufficient balance before purchase.
- **User Service:** Associates numbers with the correct user account.
- **Database:** Stores information about owned numbers, their configurations, friendly names, associated provider details, and status.
- **Automation Service:** Links numbers to their respective automation configurations.

## 4. Necessary API Endpoints

- `GET /api/numbers/available?country=<iso>&areaCode=<num>&capabilities=voice|sms|mms&type=local|tollfree`: Searches for available numbers via the provisioning service.
- `POST /api/numbers/purchase`: Initiates the purchase of a specific number (requires number identifier, potentially payment confirmation).
- `GET /api/numbers/owned?page=1&limit=25`: Fetches the list of numbers currently owned by the user.
- `PUT /api/numbers/{phoneNumberId}`: Updates settings for an owned number (e.g., friendly name, basic config).
- `DELETE /api/numbers/{phoneNumberId}`: Initiates the release/deletion of an owned number.
- `GET /api/esims/available?country=<iso>&region=<region>`: (eSIM specific) Searches for available eSIM plans.
- `POST /api/esims/purchase`: (eSIM specific) Initiates the purchase of an eSIM plan.
- `GET /api/esims/owned`: (eSIM specific) Fetches owned eSIM profiles/plans.
- `POST /api/esims/{esimId}/install`: (eSIM specific) Triggers installation flow (might return QR code data or instructions).

## 5. Expected Frontend Component Structure

```
/components
  /number-management
    NumberManagementLayout.tsx    # Main layout for the section
    NumberSearchForm.tsx          # Form for searching available numbers
    AvailableNumbersList.tsx      # Displays search results
      AvailableNumberItem.tsx     # Represents a single available number
    OwnedNumbersList.tsx          # Displays the user's current numbers
      OwnedNumberItem.tsx         # Represents a single owned number
    NumberConfigModal.tsx         # Modal/Panel for editing number settings
    PurchaseConfirmationDialog.tsx # Dialog to confirm number purchase
    ReleaseConfirmationDialog.tsx  # Dialog to confirm number release
    NumberManagementSkeleton.tsx  # Loading state placeholder
    ESimManagementPanel.tsx       # (If applicable) Specific UI for eSIMs
```

## 6. Data Displayed

- **Available Numbers:** Phone number, Location (City/State/Country), Capabilities (Voice, SMS, MMS icons), Type (Local, Toll-Free), Purchase Price / Recurring Cost.
- **Owned Numbers:** Phone Number, Friendly Name (editable), Status (Active, Pending Release), Type, Capabilities, Date Acquired, Link to Automation Config, Release Button.
- **eSIMs:** Plan Name, Data Allowance, Validity, Country/Region, Status (Active, Not Installed), Install/Manage Button.

## 7. State and UI Behavior

- **Loading States:** Show skeletons while searching for numbers or loading the owned list.
- **Search Interaction:** Display results dynamically as search criteria are met. Handle "no results found" state.
- **Purchase Flow:**
    - Disable purchase button if credits are insufficient.
    - Show confirmation dialog with cost.
    - Provide feedback during purchase (loading indicator).
    - Show success message and update owned numbers list, or show detailed error message on failure.
- **Release Flow:**
    - Show confirmation dialog warning about consequences.
    - Provide feedback during release process.
    - Show success message and remove number from owned list, or show error message on failure.
- **Editing:** Allow inline editing or modal/panel for friendly names. Save changes with feedback.
- **Error States:** Clearly display errors from the provider API (e.g., "Number no longer available", "Purchase failed: Payment declined").

## 8. AI Integration

- **Minimal Direct Integration:** This section primarily manages the assets (numbers).
- **Indirect Linking:** Owned numbers should clearly link to their corresponding AI Assistant configuration within the Automation section.
- **Future Potential:** AI could suggest numbers based on user's business description or location provided in settings. AI could analyze usage patterns to recommend adding/removing numbers.

## 9. Error Handling Rules

- **Search Errors:** Display an error if the search API fails (e.g., "Failed to search for numbers").
- **Purchase Errors:** Provide specific feedback:
    - "Insufficient credits."
    - "Payment method failed." (If direct payment involved)
    - "Number is no longer available."
    - "Provider error: [Specific error from Twilio/Airalo if available]."
    - "Failed to purchase number. Please try again later." (Generic)
- **Release Errors:**
    - "Failed to release number. It might be in use by an active process."
    - "Provider error during release: [Specific error]."
    - "Failed to release number. Please contact support." (Generic)
- **Configuration Errors:** Show errors if updating settings fails.
- **Authentication Errors:** Redirect to login if the session is invalid.

## 10. Logging and Usage Tracking Expectations

- **Log:**
    - Number search attempts (include criteria).
    - Number purchase attempts (number, success/failure, error reason if failed).
    - Number release attempts (number, success/failure, error reason if failed).
    - Updates to number configurations (number, changed fields).
    - Errors encountered during any number management operation.
    - eSIM purchase/management actions.
- **Track:**
    - Views of the number management section.
    - Frequency of number searches.
    - Number of successful purchases vs. attempts.
    - Number of successful releases vs. attempts.
    - Use of the configuration editing feature.
    - Interactions with eSIM management features (if applicable).
