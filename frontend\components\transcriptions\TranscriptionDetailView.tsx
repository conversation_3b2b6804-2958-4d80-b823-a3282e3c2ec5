'use client';

import { Fragment, useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { format } from 'date-fns';
import { 
  XMarkIcon, 
  PhoneIcon, 
  ClockIcon, 
  CalendarIcon,
  ShareIcon,
  DocumentArrowDownIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { useTranscriptionDetail, useDeleteTranscription, useShareTranscription } from '../../../hooks/useTranscriptions';
import LoadingSpinner from '../shared/LoadingSpinner';
import ErrorMessage from '../shared/ErrorMessage';
import TranscriptionPlayer from './TranscriptionPlayer';
import SentimentAnalysisVisual from './SentimentAnalysisVisual';
import KeywordHighlighter from './KeywordHighlighter';

interface TranscriptionDetailViewProps {
  transcriptionId: string;
  isOpen: boolean;
  onClose: () => void;
}

export default function TranscriptionDetailView({ 
  transcriptionId, 
  isOpen, 
  onClose 
}: TranscriptionDetailViewProps) {
  const [showShareModal, setShowShareModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [shareUrl, setShareUrl] = useState('');
  const [shareExpiry, setShareExpiry] = useState('');

  // Fetch transcription details
  const { 
    data: transcription, 
    isLoading, 
    isError, 
    error 
  } = useTranscriptionDetail(transcriptionId);

  // Mutations
  const deleteTranscription = useDeleteTranscription();
  const shareTranscription = useShareTranscription();

  // Handlers
  const handleShare = async () => {
    try {
      const result = await shareTranscription.mutateAsync({ 
        id: transcriptionId,
        expiresIn: 24 // 24 hours
      });
      setShareUrl(result.shareUrl);
      setShareExpiry(format(new Date(result.expiresAt), 'MMM d, yyyy h:mm a'));
      setShowShareModal(true);
    } catch (error) {
      console.error('Error sharing transcription:', error);
    }
  };

  const handleCopyShareUrl = () => {
    navigator.clipboard.writeText(shareUrl);
    // Show toast or notification
  };

  const handleDelete = async () => {
    try {
      await deleteTranscription.mutateAsync(transcriptionId);
      setShowDeleteModal(false);
      onClose();
    } catch (error) {
      console.error('Error deleting transcription:', error);
    }
  };

  // Helper function to format duration
  const formatDuration = (seconds?: number) => {
    if (!seconds) return '0:00';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="fixed inset-0 overflow-hidden z-50" onClose={onClose}>
        <div className="absolute inset-0 overflow-hidden">
          <Transition.Child
            as={Fragment}
            enter="ease-in-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in-out duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Dialog.Overlay className="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-y-0 right-0 pl-10 max-w-full flex">
            <Transition.Child
              as={Fragment}
              enter="transform transition ease-in-out duration-300"
              enterFrom="translate-x-full"
              enterTo="translate-x-0"
              leave="transform transition ease-in-out duration-300"
              leaveFrom="translate-x-0"
              leaveTo="translate-x-full"
            >
              <div className="relative w-screen max-w-2xl">
                <div className="h-full flex flex-col bg-white dark:bg-gray-800 shadow-xl overflow-y-auto">
                  <div className="px-4 py-6 sm:px-6">
                    <div className="flex items-start justify-between">
                      <Dialog.Title className="text-lg font-medium text-gray-900 dark:text-white">
                        Call Transcription
                      </Dialog.Title>
                      <div className="ml-3 h-7 flex items-center">
                        <button
                          type="button"
                          className="bg-white dark:bg-gray-800 rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                          onClick={onClose}
                        >
                          <span className="sr-only">Close panel</span>
                          <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                        </button>
                      </div>
                    </div>
                  </div>

                  {isLoading ? (
                    <div className="flex-1 flex items-center justify-center">
                      <LoadingSpinner size="lg" />
                    </div>
                  ) : isError ? (
                    <div className="px-4 sm:px-6">
                      <ErrorMessage 
                        message={`Error loading transcription: ${error instanceof Error ? error.message : 'Unknown error'}`} 
                      />
                    </div>
                  ) : transcription ? (
                    <div className="flex-1 px-4 sm:px-6">
                      {/* Call details */}
                      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="flex items-center">
                            <PhoneIcon className="h-5 w-5 text-gray-400 mr-2" />
                            <div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">From</div>
                              <div className="font-medium text-gray-900 dark:text-white">{transcription.callDetails.from}</div>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <PhoneIcon className="h-5 w-5 text-gray-400 mr-2" />
                            <div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">To</div>
                              <div className="font-medium text-gray-900 dark:text-white">{transcription.callDetails.to}</div>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <CalendarIcon className="h-5 w-5 text-gray-400 mr-2" />
                            <div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">Date</div>
                              <div className="font-medium text-gray-900 dark:text-white">
                                {format(new Date(transcription.callDetails.timestamp), 'MMM d, yyyy h:mm a')}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <ClockIcon className="h-5 w-5 text-gray-400 mr-2" />
                            <div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">Duration</div>
                              <div className="font-medium text-gray-900 dark:text-white">
                                {formatDuration(transcription.callDetails.duration)}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Audio player */}
                      {transcription.recordingUrl && (
                        <div className="mb-6">
                          <TranscriptionPlayer 
                            recordingUrl={transcription.recordingUrl} 
                            transcription={transcription.transcriptionText}
                          />
                        </div>
                      )}

                      {/* Sentiment analysis */}
                      {transcription.sentiment && (
                        <div className="mb-6">
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Sentiment Analysis</h3>
                          <SentimentAnalysisVisual sentimentData={transcription.sentiment} />
                        </div>
                      )}

                      {/* Transcription text with keyword highlighting */}
                      <div className="mb-6">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Transcription</h3>
                        <div className="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                          <KeywordHighlighter 
                            transcriptionText={transcription.transcriptionText} 
                            keywords={transcription.keywords}
                            entities={transcription.entities}
                          />
                        </div>
                      </div>

                      {/* Action buttons */}
                      <div className="flex space-x-4 mb-6">
                        <button
                          onClick={handleShare}
                          className="flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                          <ShareIcon className="h-5 w-5 mr-2 text-gray-400" />
                          Share
                        </button>
                        <button
                          onClick={() => window.open(transcription.recordingUrl || '', '_blank')}
                          disabled={!transcription.recordingUrl}
                          className="flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                        >
                          <DocumentArrowDownIcon className="h-5 w-5 mr-2 text-gray-400" />
                          Download Audio
                        </button>
                        <button
                          onClick={() => setShowDeleteModal(true)}
                          className="flex items-center px-4 py-2 border border-red-300 dark:border-red-700 rounded-md shadow-sm text-sm font-medium text-red-700 dark:text-red-400 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          <TrashIcon className="h-5 w-5 mr-2 text-red-400" />
                          Delete
                        </button>
                      </div>
                    </div>
                  ) : null}
                </div>
              </div>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
