import { createServerClient } from '@supabase/ssr'; // Use ssr client
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export const dynamic = 'force-dynamic'; 

const BACKEND_API_URL = process.env.BACKEND_API_URL || 'http://localhost:3007'; 

// Mock data for purchased phone numbers
const mockPurchasedNumbers = [
  {
    id: "1",
    number: "+14155552671",
    friendlyName: "(*************",
    countryCode: "US",
    region: "California",
    locality: "San Francisco",
    isActive: true,
    isPrimary: true,
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
    capabilities: {
      voice: true,
      sms: true,
      fax: false
    }
  },
  {
    id: "2",
    number: "+14255551234",
    friendlyName: "(*************",
    countryCode: "US",
    region: "Washington",
    locality: "Seattle",
    isActive: true,
    isPrimary: false,
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
    capabilities: {
      voice: true,
      sms: true,
      fax: false
    }
  }
];

export async function GET(request) {
  console.log('API route /api/numbers/mine invoked'); 
  const cookieStore = cookies(); // Get cookie store instance
  
  // Check if this is a direct database query request
  const { searchParams } = new URL(request.url);
  const isDirect = searchParams.get('direct') === 'true';
  
  if (isDirect) {
    console.log('[API /numbers/mine] Direct database access requested');
    try {
      // Import PrismaClient for database access - import inside function to prevent initialization issues
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();
      
      // Use createServerClient from @supabase/ssr
      const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
        {
          cookies: {
            get: (name) => cookieStore.get(name)?.value,
          },
        }
      );
      
      // Get user session for authentication
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError || !session) {
        console.error('[API /numbers/mine] Direct access - no valid session');
        return NextResponse.json({ 
          success: false, 
          message: 'Authentication required',
          numbers: []
        }, { status: 401 });
      }
      
      // Get user from database
      const user = await prisma.user.findUnique({
        where: { email: session.user.email }
      });
      
      if (!user) {
        console.log('[API /numbers/mine] Direct access - user not found in database');
        // Return mock data for development purposes
        return NextResponse.json({
          success: true,
          message: 'User not found in database, returning mock data',
          numbers: mockPurchasedNumbers
        });
      }
      
      // Query the phone numbers from the database
      console.log(`[API /numbers/mine] Querying phone numbers for user ID: ${user.id}`);
      const phoneNumbers = await prisma.phoneNumber.findMany({
        where: { userId: user.id },
        orderBy: { createdAt: 'asc' }
      });
      
      console.log(`[API /numbers/mine] Found ${phoneNumbers.length} phone numbers in database`);
      
      // If no numbers found, return mock data for development
      const numbersToReturn = phoneNumbers.length > 0 ? phoneNumbers : mockPurchasedNumbers;
      
      // Clean up database connection
      await prisma.$disconnect();
      
      // Return the numbers in a standardized format
      return NextResponse.json({
        success: true,
        message: phoneNumbers.length > 0 ? 'Phone numbers retrieved from database directly' : 'No numbers found, returning mock data',
        numbers: numbersToReturn
      });
    } catch (error) {
      console.error('[API /numbers/mine] Error in direct database access:', error);
      // Return mock data on error for development
      return NextResponse.json({ 
        success: true, 
        message: 'Database access error, returning mock data', 
        numbers: mockPurchasedNumbers
      });
    }
  }

  // Continue with normal API flow if not direct access
  // Use createServerClient from @supabase/ssr
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        get: (name) => cookieStore.get(name)?.value,
        // We don't need set/remove in a GET handler usually, but include for completeness if needed elsewhere
        set: (name, value, options) => {
           // Route Handlers might not be able to set cookies directly like middleware
           // This might need adjustment depending on Supabase SSR recommendations for Route Handlers
           console.warn("Attempting to set cookie in Route Handler - might not persist");
           cookieStore.set({ name, value, ...options });
        },
        remove: (name, options) => {
           console.warn("Attempting to remove cookie in Route Handler - might not persist");
           cookieStore.set({ name, value: '', ...options });
        },
      },
    }
  );

  try {
    // 1. Get current session to obtain the access token
    console.log('Attempting to get session...');
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('Error getting session:', sessionError);
      // Return mock data on error for development
      return NextResponse.json({ 
        success: true, 
        message: 'Session error, returning mock data', 
        numbers: mockPurchasedNumbers 
      });
    }
    
    if (!session) {
      console.log('No session found, returning mock data for development');
      return NextResponse.json({ 
        success: true, 
        message: 'No valid session, returning mock data', 
        numbers: mockPurchasedNumbers 
      });
    }
    console.log('Session found for user:', session.user.email);
    const accessToken = session.access_token;

    // 2. Fetch numbers from the backend API, passing the access token
    try {
      // User routes are mounted at /api/users in the backend server setup
      const backendEndpoint = `${BACKEND_API_URL}/api/users/numbers`; 
      console.log(`Fetching numbers from backend: ${backendEndpoint}`);
      const backendResponse = await fetch(backendEndpoint, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`, // Forward the token
          'Content-Type': 'application/json',
        },
        cache: 'no-store', // Ensure fresh data is fetched
      });

      // Check if response is ok before parsing JSON
      if (!backendResponse.ok) {
        let errorBody = 'Unknown backend error';
        try {
          errorBody = await backendResponse.text(); // Try to get error text
          console.error(`Backend API error (${backendResponse.status}):`, errorBody);
        } catch (e) {
           console.error(`Backend API error (${backendResponse.status}), failed to read body.`);
        }
        // Return mock data for development
        return NextResponse.json({ 
          success: true, 
          message: 'Backend API error, returning mock data', 
          numbers: mockPurchasedNumbers 
        });
      }

      const backendData = await backendResponse.json();

      // Backend returns { success: true, count: N, data: [...] }
      if (!backendData.success || !Array.isArray(backendData.data)) {
         console.warn('Backend API call successful but data array missing or invalid in response:', backendData);
         // Return mock data
         return NextResponse.json({ 
           success: true, 
           message: 'Backend returned invalid data, using mock data', 
           numbers: mockPurchasedNumbers 
         });
      }
      
      const phoneNumbers = backendData.data; // Use the 'data' field from backend
      console.log(`Received ${phoneNumbers.length} phone numbers from backend.`);

      // If no numbers found, return mock data for development
      const numbersToReturn = phoneNumbers.length > 0 ? phoneNumbers : mockPurchasedNumbers;

      // Return the phone numbers fetched from the backend, mapping to 'numbers' field for frontend
      return NextResponse.json({
        success: true,
        numbers: numbersToReturn // Keep 'numbers' field name for frontend consistency
      });
    } catch (fetchError) {
      console.error('[API /api/numbers/mine] Error fetching from backend:', fetchError);
      // Return mock data
      return NextResponse.json({ 
        success: true, 
        message: 'Backend fetch error, returning mock data', 
        numbers: mockPurchasedNumbers 
      });
    }
  } catch (error) {
    console.error('[API /api/numbers/mine] Error fetching phone numbers:', error);
    // Return mock data
    return NextResponse.json({ 
      success: true, 
      message: 'Error fetching phone numbers, returning mock data', 
      numbers: mockPurchasedNumbers 
    });
  }
}
