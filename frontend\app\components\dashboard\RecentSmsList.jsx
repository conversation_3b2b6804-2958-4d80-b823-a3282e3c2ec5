"use client";

import { useState } from 'react';
import {
  ChatBubbleLeftEllipsisIcon,
  PaperAirplaneIcon,
  ArrowUturnLeftIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

export default function RecentSmsList({ messages }) {
  const [expandedMessage, setExpandedMessage] = useState(null);
  
  // Ensure messages is an array, handling different response formats
  const messageArray = Array.isArray(messages) 
    ? messages 
    : Array.isArray(messages?.data) 
      ? messages.data 
      : [];

  // Helper function to format phone numbers nicely
  const formatPhoneNumber = (phoneNumber) => {
    if (!phoneNumber) return 'Unknown';
    if (phoneNumber.includes(' ') || phoneNumber.includes('-')) return phoneNumber;
    if (phoneNumber.length === 10) return `(${phoneNumber.substring(0, 3)}) ${phoneNumber.substring(3, 6)}-${phoneNumber.substring(6)}`;
    if (phoneNumber.startsWith('+')) {
      if (phoneNumber.startsWith('+1') && phoneNumber.length === 12) return `+1 (${phoneNumber.substring(2, 5)}) ${phoneNumber.substring(5, 8)}-${phoneNumber.substring(8)}`;
      return phoneNumber;
    }
    return phoneNumber;
  };

  // Format timestamp nicely
  const formatTime = (timestamp) => {
    if (!timestamp) return 'Unknown time';
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffMs = now - date;
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      if (diffDays === 0) return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      if (diffDays === 1) return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      if (diffDays < 7) return `${date.toLocaleDateString([], { weekday: 'long' })} at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      return date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' });
    } catch (e) {
      console.error('Error formatting date:', e);
      return timestamp;
    }
  };

  // Get icon for message direction
  const getMessageIcon = (message) => {
    if (message.direction === 'outbound') {
      return <PaperAirplaneIcon className="h-4 w-4 text-purple-400" />;
    } 
    // Default to inbound
    return <ChatBubbleLeftEllipsisIcon className="h-4 w-4 text-green-400" />;
  };
  
  if (!messageArray || messageArray.length === 0) {
    return (
      <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg h-full">
        <h3 className="text-lg font-medium text-white flex items-center">
          <ChatBubbleLeftEllipsisIcon className="h-5 w-5 mr-2 text-purple-400" />
          Recent Messages
        </h3>
        <div className="mt-6 flex flex-col items-center justify-center text-center h-40">
          <p className="text-gray-400">No message history available yet</p>
          <p className="text-gray-500 text-sm mt-2">Your recent SMS messages will appear here</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
      <h3 className="text-lg font-medium text-white flex items-center">
        <ChatBubbleLeftEllipsisIcon className="h-5 w-5 mr-2 text-purple-400" />
        Recent Messages
      </h3>
      
      <div className="mt-4 divide-y divide-gray-800/60">
        {messageArray.map((message) => (
          <div key={message.id} className="py-3 group">
            <div 
              className="cursor-pointer"
              onClick={() => setExpandedMessage(expandedMessage === message.id ? null : message.id)}
            >
              <div className="flex justify-between items-start">
                <div className="flex items-start">
                  <div className="mr-3 mt-1">
                    {getMessageIcon(message)}
                  </div>
                  <div>
                    <p className="text-white font-medium">
                      {message.direction === 'inbound' ? formatPhoneNumber(message.from) : formatPhoneNumber(message.to)}
                    </p>
                    <p className="text-gray-400 text-xs flex items-center">
                      {message.direction === 'inbound' ? 'Received' : 'Sent'} · {formatTime(message.timestamp)}
                    </p>
                    <p className="text-gray-300 text-sm mt-1 truncate group-hover:whitespace-normal">
                      {message.body}
                    </p>
                  </div>
                </div>
                {/* Optionally add a small indicator/preview here */}
              </div>
            </div>
            
            {expandedMessage === message.id && (
              <div className="mt-3 p-3 bg-gray-800/30 rounded-lg border border-gray-700/50 text-sm">
                <p className="text-gray-400 text-xs mb-1">
                  {message.direction === 'inbound' ? `From: ${formatPhoneNumber(message.from)}` : `To: ${formatPhoneNumber(message.to)}`}
                   · {new Date(message.timestamp).toLocaleString()}
                 </p>
                <p className="text-gray-200 whitespace-pre-wrap mb-3">{message.body}</p>
                
                {/* Reply Section (Placeholder) */}
                <div className="border-t border-gray-700/50 pt-3">
                   <textarea 
                      rows={2}
                      placeholder={`Reply to ${formatPhoneNumber(message.direction === 'inbound' ? message.from : message.to)}...`}
                      className="w-full px-3 py-2 text-sm bg-gray-700/50 border border-gray-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-purple-500 resize-none mb-2"
                    />
                  <button className="flex items-center justify-center py-1 px-3 bg-purple-600/70 hover:bg-purple-600/90 text-white rounded text-xs font-medium transition-colors">
                    <PaperAirplaneIcon className="h-3 w-3 mr-1" />
                    <span>Send Reply</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
      
      {messageArray.length > 5 && (
        <div className="mt-4 text-center">
          <button className="text-sm text-purple-400 hover:text-purple-300 transition-colors">
            View All Messages
          </button>
        </div>
      )}
    </div>
  );
} 