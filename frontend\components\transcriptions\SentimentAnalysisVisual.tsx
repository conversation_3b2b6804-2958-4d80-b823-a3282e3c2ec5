'use client';

import {
  FaceSmileIcon,
  FaceFrownIcon,
  MinusCircleIcon,
  HeartIcon,
  ExclamationTriangleIcon,
  FaceFrownOpenIcon,
  FireIcon
} from '@heroicons/react/24/outline';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  <PERSON>,
  Cell,
  Legend,
  BarChart,
  Bar
} from 'recharts';

interface SentimentSegment {
  text: string;
  sentiment: string;
  score: number;
  emotions?: Record<string, number>;
}

interface SentimentData {
  overall: string;
  overallScore: number;
  segments?: SentimentSegment[];
  confidence?: number;
  emotions?: Record<string, number>;
}

interface SentimentAnalysisVisualProps {
  sentimentData: SentimentData;
}

export default function SentimentAnalysisVisual({ sentimentData }: SentimentAnalysisVisualProps) {
  // Helper function to get sentiment color
  const getSentimentColor = (sentiment: string) => {
    switch (sentiment.toLowerCase()) {
      case 'positive':
        return 'text-green-500';
      case 'negative':
        return 'text-red-500';
      default:
        return 'text-gray-400';
    }
  };

  // Helper function to get sentiment icon
  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment.toLowerCase()) {
      case 'positive':
        return <FaceSmileIcon className={`h-8 w-8 ${getSentimentColor(sentiment)}`} />;
      case 'negative':
        return <FaceFrownIcon className={`h-8 w-8 ${getSentimentColor(sentiment)}`} />;
      default:
        return <MinusCircleIcon className={`h-8 w-8 ${getSentimentColor(sentiment)}`} />;
    }
  };

  // Helper function to get sentiment label
  const getSentimentLabel = (score: number) => {
    if (score >= 0.5) return 'Very Positive';
    if (score >= 0.1) return 'Positive';
    if (score > -0.1) return 'Neutral';
    if (score > -0.5) return 'Negative';
    return 'Very Negative';
  };

  // Prepare chart data if segments exist
  const chartData = sentimentData.segments?.map((segment, index) => ({
    index,
    score: segment.score,
    text: segment.text.substring(0, 30) + (segment.text.length > 30 ? '...' : ''),
    ...segment.emotions
  })) || [];

  // Prepare emotion data for pie chart
  const emotionData = sentimentData.emotions ?
    Object.entries(sentimentData.emotions)
      .map(([name, value]) => ({ name, value: parseFloat(value.toFixed(2)) }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 5) : [];

  // Emotion colors
  const EMOTION_COLORS = {
    joy: '#10B981', // green-500
    anger: '#EF4444', // red-500
    sadness: '#3B82F6', // blue-500
    fear: '#8B5CF6', // purple-500
    surprise: '#F59E0B', // amber-500
    disgust: '#6366F1', // indigo-500
    neutral: '#6B7280', // gray-500
  };

  // Get emotion icon
  const getEmotionIcon = (emotion: string) => {
    switch (emotion.toLowerCase()) {
      case 'joy':
        return <FaceSmileIcon className="h-5 w-5 text-green-500" />;
      case 'anger':
        return <FireIcon className="h-5 w-5 text-red-500" />;
      case 'sadness':
        return <FaceFrownIcon className="h-5 w-5 text-blue-500" />;
      case 'fear':
        return <FaceFrownOpenIcon className="h-5 w-5 text-purple-500" />;
      case 'surprise':
        return <ExclamationTriangleIcon className="h-5 w-5 text-amber-500" />;
      case 'disgust':
        return <FaceFrownOpenIcon className="h-5 w-5 text-indigo-500" />;
      case 'neutral':
        return <MinusCircleIcon className="h-5 w-5 text-gray-500" />;
      default:
        return <HeartIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          {getSentimentIcon(sentimentData.overall)}
          <div className="ml-3">
            <div className="text-lg font-medium text-gray-900 dark:text-white">
              {getSentimentLabel(sentimentData.overallScore)}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Overall sentiment
            </div>
          </div>
        </div>
        <div className="text-2xl font-bold text-gray-900 dark:text-white">
          {sentimentData.overallScore.toFixed(2)}
        </div>
      </div>

      {sentimentData.confidence !== undefined && (
        <div className="mb-4">
          <div className="text-sm text-gray-500 dark:text-gray-400 mb-1">
            Confidence: {Math.round(sentimentData.confidence * 100)}%
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
            <div
              className="bg-blue-600 h-2.5 rounded-full"
              style={{ width: `${sentimentData.confidence * 100}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Emotion Analysis */}
      {emotionData.length > 0 && (
        <div className="mt-4 mb-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Detected Emotions
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Emotion Pie Chart */}
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={emotionData}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={2}
                    dataKey="value"
                  >
                    {emotionData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={EMOTION_COLORS[entry.name as keyof typeof EMOTION_COLORS] || '#6B7280'}
                      />
                    ))}
                  </Pie>
                  <Legend
                    layout="vertical"
                    verticalAlign="middle"
                    align="right"
                    formatter={(value) => value.charAt(0).toUpperCase() + value.slice(1)}
                  />
                  <Tooltip
                    formatter={(value) => [`${value}`, 'Score']}
                    contentStyle={{
                      backgroundColor: '#1F2937',
                      borderColor: '#374151',
                      color: '#F9FAFB'
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>

            {/* Emotion Bar Chart */}
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={emotionData}
                  layout="vertical"
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis
                    type="number"
                    domain={[0, 1]}
                    stroke="#9CA3AF"
                  />
                  <YAxis
                    dataKey="name"
                    type="category"
                    stroke="#9CA3AF"
                    tickFormatter={(value) => value.charAt(0).toUpperCase() + value.slice(1)}
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1F2937',
                      borderColor: '#374151',
                      color: '#F9FAFB'
                    }}
                    formatter={(value) => [`${value}`, 'Score']}
                  />
                  <Bar
                    dataKey="value"
                    barSize={20}
                  >
                    {emotionData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={EMOTION_COLORS[entry.name as keyof typeof EMOTION_COLORS] || '#6B7280'}
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      )

      {chartData.length > 0 && (
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Sentiment Throughout Call
          </h4>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={chartData}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis
                  dataKey="index"
                  stroke="#9CA3AF"
                  tickFormatter={(value) => `${value + 1}`}
                />
                <YAxis
                  domain={[-1, 1]}
                  stroke="#9CA3AF"
                  tickFormatter={(value) => value.toFixed(1)}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: '#1F2937',
                    borderColor: '#374151',
                    color: '#F9FAFB'
                  }}
                  formatter={(value: number) => [value.toFixed(2), 'Sentiment']}
                  labelFormatter={(value) => `Segment ${value + 1}`}
                />
                <Area
                  type="monotone"
                  dataKey="score"
                  stroke="#4F46E5"
                  fill="#4F46E5"
                  fillOpacity={0.3}
                  isAnimationActive={true}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      {sentimentData.segments && sentimentData.segments.length > 0 && (
        <div className="mt-6">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Key Segments
          </h4>
          <ul className="space-y-3">
            {sentimentData.segments.map((segment, index) => (
              <li key={index} className="flex items-start">
                <div className={`mt-1 mr-2 ${getSentimentColor(segment.sentiment)}`}>
                  {segment.sentiment === 'positive' ? (
                    <FaceSmileIcon className="h-5 w-5" />
                  ) : segment.sentiment === 'negative' ? (
                    <FaceFrownIcon className="h-5 w-5" />
                  ) : (
                    <MinusCircleIcon className="h-5 w-5" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="text-sm text-gray-900 dark:text-white">
                    {segment.text}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Score: {segment.score.toFixed(2)}
                  </div>
                  {segment.emotions && Object.entries(segment.emotions).length > 0 && (
                    <div className="mt-1 flex flex-wrap gap-1">
                      {Object.entries(segment.emotions)
                        .sort(([, a], [, b]) => b - a)
                        .slice(0, 3)
                        .map(([emotion, score]) => (
                          <span
                            key={emotion}
                            className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"
                            style={{
                              backgroundColor: `${EMOTION_COLORS[emotion as keyof typeof EMOTION_COLORS] || '#6B7280'}20`,
                              color: EMOTION_COLORS[emotion as keyof typeof EMOTION_COLORS] || '#6B7280'
                            }}
                          >
                            <span className="mr-1">{getEmotionIcon(emotion)}</span>
                            {emotion.charAt(0).toUpperCase() + emotion.slice(1)}: {score.toFixed(2)}
                          </span>
                        ))
                      }
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
