"use client";

import Link from 'next/link';
import { motion } from 'framer-motion';

const CTAButton = ({ 
  href = '/', 
  className = '', 
  size = 'default', 
  variant = 'primary',
  children,
  onClick,
  fullWidth = false,
  icon = null,
  animate = true
}) => {
  
  // Base classes that apply to all buttons
  let baseClasses = "relative rounded-lg font-medium transition-all duration-300 flex items-center justify-center";
  
  // Size variations
  const sizeClasses = {
    small: "text-xs py-2 px-3",
    default: "text-sm py-2.5 px-5",
    large: "text-base py-3 px-6",
  };
  
  // Button variants
  const variantClasses = {
    primary: "bg-gradient-to-r from-purple-600 to-indigo-600 text-white hover:shadow-[0_4px_20px_rgba(139,92,246,0.35)] hover:shadow-glow-purple/20 border-2 border-transparent",
    secondary: "border-2 border-purple-500 text-white hover:bg-purple-500/10 hover:shadow-[0_2px_12px_rgba(139,92,246,0.2)]",
    outline: "border-2 border-gray-700 text-white hover:border-purple-500 hover:bg-purple-500/5 hover:shadow-[0_2px_12px_rgba(139,92,246,0.15)]",
    ghost: "bg-transparent text-white hover:bg-white/5 border-2 border-transparent"
  };
  
  // Full width option
  const widthClass = fullWidth ? "w-full" : "";
  
  // Combine classes
  const buttonClasses = `${baseClasses} ${sizeClasses[size] || sizeClasses.default} ${variantClasses[variant] || variantClasses.primary} ${widthClass} ${className}`;
  
  // Framer Motion variants
  const buttonVariants = {
    initial: { 
      scale: 1 
    },
    hover: { 
      scale: 1.04,
      transition: { 
        type: "spring", 
        stiffness: 400, 
        damping: 10 
      }
    },
    tap: { 
      scale: 0.98,
      transition: { 
        type: "spring", 
        stiffness: 500, 
        damping: 10 
      }
    }
  };
  
  // Pulse animation for the button glow
  const pulseVariants = {
    initial: {
      boxShadow: variant === 'primary' ? "0 0 0 rgba(139, 92, 246, 0)" : "0 0 0 rgba(139, 92, 246, 0)"
    },
    animate: {
      boxShadow: variant === 'primary' 
        ? [
            "0 0 0 0 rgba(139, 92, 246, 0.1)",
            "0 0 0 15px rgba(139, 92, 246, 0)",
          ]
        : [
            "0 0 0 0 rgba(139, 92, 246, 0.05)",
            "0 0 0 10px rgba(139, 92, 246, 0)",
          ],
      transition: {
        duration: 2,
        repeat: Infinity,
        repeatType: "loop"
      }
    }
  };
  
  const isLink = href && !onClick;
  
  // Icon styles
  const iconClass = "mr-2 inline-block";
  
  const ButtonContent = () => (
    <>
      {/* Optional icon */}
      {icon && (
        <span className={iconClass}>
          {icon}
        </span>
      )}
      
      {/* Button text content */}
      {children}
    </>
  );
  
  if (isLink) {
    return (
      <motion.div
        className="relative inline-block"
        variants={animate ? pulseVariants : {}}
        initial="initial"
        animate={animate ? "animate" : "initial"}
      >
        <Link href={href} legacyBehavior>
          <motion.a
            className={buttonClasses}
            variants={animate ? buttonVariants : {}}
            initial="initial"
            whileHover="hover"
            whileTap="tap"
          >
            <ButtonContent />
          </motion.a>
        </Link>
      </motion.div>
    );
  }
  
  // Button with onClick handler
  return (
    <motion.div
      className="relative inline-block"
      variants={animate ? pulseVariants : {}}
      initial="initial"
      animate={animate ? "animate" : "initial"}
    >
      <motion.button
        type="button"
        className={buttonClasses}
        onClick={onClick}
        variants={animate ? buttonVariants : {}}
        initial="initial"
        whileHover="hover"
        whileTap="tap"
      >
        <ButtonContent />
      </motion.button>
    </motion.div>
  );
};

export default CTAButton; 