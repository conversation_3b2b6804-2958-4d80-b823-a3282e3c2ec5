'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { AutomationConfig } from './AutomationConfigPanel';
import LoadingSpinner from '../shared/LoadingSpinner';

interface SmsRulesConfigProps {
  numberId: string;
  initialData: AutomationConfig['smsRules'];
}

export default function SmsRulesConfig({
  numberId,
  initialData,
}: SmsRulesConfigProps) {
  // State for form data
  const [formData, setFormData] = useState(initialData);
  
  // Get the query client
  const queryClient = useQueryClient();

  // Mutation for updating SMS rules
  const updateMutation = useMutation({
    mutationFn: async (data: AutomationConfig['smsRules']) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data: response } = await axios.put(`/api/numbers/${numberId}/automations`, {
        smsRules: data,
      });
      return response;
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['automations', numberId] });
      
      // Show success toast (you can use a toast library like react-hot-toast)
      console.log('SMS rules updated successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to update SMS rules');
    },
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateMutation.mutate(formData);
  };

  // Handle input change
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    
    // Handle action selection
    if (name === 'action') {
      setFormData(prev => ({
        ...prev,
        action: value as 'forward' | 'auto_reply' | 'ai_reply',
      }));
      return;
    }
    
    // Handle other inputs
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          SMS Handling
        </label>
        <select
          name="action"
          value={formData.action}
          onChange={handleChange}
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-700 dark:bg-gray-800 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          disabled={updateMutation.isPending}
        >
          <option value="forward">Forward to another number</option>
          <option value="auto_reply">Send automatic reply</option>
          <option value="ai_reply">Let AI assistant reply</option>
        </select>
      </div>

      {formData.action === 'forward' && (
        <div>
          <label htmlFor="forwardTo" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Forward To
          </label>
          <input
            type="tel"
            name="forwardTo"
            id="forwardTo"
            value={formData.forwardTo || ''}
            onChange={handleChange}
            placeholder="+****************"
            className="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            disabled={updateMutation.isPending}
          />
        </div>
      )}

      {formData.action === 'auto_reply' && (
        <div>
          <label htmlFor="autoReplyText" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Automatic Reply Message
          </label>
          <textarea
            name="autoReplyText"
            id="autoReplyText"
            rows={4}
            value={formData.autoReplyText || ''}
            onChange={handleChange}
            placeholder="Thank you for your message. We will get back to you shortly."
            className="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            disabled={updateMutation.isPending}
          />
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            This message will be sent automatically in response to incoming SMS messages.
          </p>
        </div>
      )}

      {formData.action === 'ai_reply' && (
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-md">
          <h3 className="text-sm font-medium text-blue-800 dark:text-blue-300">
            AI Assistant SMS Handling
          </h3>
          <p className="mt-2 text-sm text-blue-700 dark:text-blue-200">
            Your AI assistant will automatically respond to incoming SMS messages based on its training and knowledge base.
            Configure your AI assistant in the AI Assistant tab.
          </p>
        </div>
      )}

      <div className="flex justify-end">
        <button
          type="submit"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={updateMutation.isPending}
        >
          {updateMutation.isPending ? (
            <>
              <LoadingSpinner size="small" color="white" />
              <span className="ml-2">Saving...</span>
            </>
          ) : (
            'Save Changes'
          )}
        </button>
      </div>
    </form>
  );
}
