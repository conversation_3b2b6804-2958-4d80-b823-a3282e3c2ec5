import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// Define the UI State interface
interface UIState {
  isSidebarCollapsed: boolean;
  isNotificationsPanelOpen: boolean;
  unreadNotificationsCount: number;
  
  // Actions
  toggleSidebar: () => void;
  toggleNotificationsPanel: () => void;
  setUnreadNotificationsCount: (count: number) => void;
  incrementUnreadNotificationsCount: () => void;
  clearUnreadNotificationsCount: () => void;
}

// Create the UI store
export const useUIStore = create<UIState>()(
  persist(
    (set, get) => ({
      isSidebarCollapsed: false,
      isNotificationsPanelOpen: false,
      unreadNotificationsCount: 0,
      
      toggleSidebar: () => {
        set((state) => ({ isSidebarCollapsed: !state.isSidebarCollapsed }));
      },
      
      toggleNotificationsPanel: () => {
        set((state) => ({ 
          isNotificationsPanelOpen: !state.isNotificationsPanelOpen,
          // Auto-clear unread count when opening the panel
          unreadNotificationsCount: state.isNotificationsPanelOpen ? state.unreadNotificationsCount : 0
        }));
      },
      
      setUnreadNotificationsCount: (count: number) => {
        set({ unreadNotificationsCount: count });
      },
      
      incrementUnreadNotificationsCount: () => {
        set((state) => ({ unreadNotificationsCount: state.unreadNotificationsCount + 1 }));
      },
      
      clearUnreadNotificationsCount: () => {
        set({ unreadNotificationsCount: 0 });
      },
    }),
    {
      name: 'callsaver-ui', // Name for the persisted state in storage
      storage: createJSONStorage(() => localStorage), // Use localStorage since this is UI preferences
      // Only persist certain UI state that should be remembered between sessions
      partialize: (state) => ({ 
        isSidebarCollapsed: state.isSidebarCollapsed
        // We don't persist notification panel state or counts
      }),
    }
  )
);

// Export selectors to prevent unnecessary re-renders
export const useSidebarState = () => useUIStore((state) => state.isSidebarCollapsed);
export const useNotificationsPanelState = () => useUIStore((state) => state.isNotificationsPanelOpen);
export const useUnreadNotificationsCount = () => useUIStore((state) => state.unreadNotificationsCount);
