"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { UserCircleIcon, ChevronLeftIcon, ChevronRightIcon, ChatBubbleBottomCenterTextIcon } from '@heroicons/react/24/outline';
import { StarIcon } from '@heroicons/react/24/solid';
import { motion } from "framer-motion";
import { useWindowSize } from "../hooks/useWindowSize";
import Image from "next/image";

// Testimonial data - can be moved to a separate file or fetched from API
const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Small Business Owner",
    avatar: "/avatars/avatar-1.jpg",
    content: "CallSaver's voice AI is like having a full-time receptionist at a fraction of the cost. Our customers are amazed when they realize they're talking to an AI—it sounds completely human and captures every detail perfectly.",
    stars: 5,
    color: "indigo"
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Real Estate Agent",
    avatar: "/avatars/avatar-2.jpg",
    content: "The voice AI handles my after-hours calls flawlessly. I wake up to qualified leads, appointment bookings, and detailed call notes every morning. It's added over $15,000 in monthly commissions from leads I would have missed.",
    stars: 5,
    color: "purple"
  },
  {
    id: 3,
    name: "<PERSON> <PERSON>",
    role: "Healthcare Provider",
    avatar: "/avatars/avatar-3.jpg",
    content: "Our patients love the natural voice conversations with CallSaver. It handles appointment scheduling, insurance questions, and even pre-screening—all while sounding completely natural. We've reduced front desk staff by 60% and improved patient satisfaction.",
    stars: 5,
    color: "pink"
  },
];

// Color mapping for different testimonial cards
const colorMapping = {
  indigo: {
    bg: "from-indigo-900/20 to-indigo-900/10",
    border: "border-indigo-500/20",
    glow: "group-hover:shadow-indigo-500/10",
    text: "text-indigo-400",
    light: "bg-indigo-500/10",
    lightBorder: "border-indigo-500/20"
  },
  purple: {
    bg: "from-purple-900/20 to-purple-900/10",
    border: "border-purple-500/20",
    glow: "group-hover:shadow-purple-500/10",
    text: "text-purple-400",
    light: "bg-purple-500/10",
    lightBorder: "border-purple-500/20"
  },
  pink: {
    bg: "from-pink-900/20 to-pink-900/10",
    border: "border-pink-500/20",
    glow: "group-hover:shadow-pink-500/10",
    text: "text-pink-400", 
    light: "bg-pink-500/10",
    lightBorder: "border-pink-500/20"
  }
};

// Rating stars component - moved outside to be accessible to TestimonialCard
const RatingStars = ({ rating }) => {
  return (
    <div className="flex">
      {[...Array(5)].map((_, i) => (
        <StarIcon 
          key={i}
          className={`h-4 w-4 ${i < rating ? 'text-yellow-400' : 'text-gray-600'}`}
        />
      ))}
    </div>
  );
};

// Avatar component - also moved outside to be accessible to TestimonialCard
const Avatar = ({ testimonial }) => {
  if (testimonial.avatar) {
    return (
      <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-white/10">
        <Image 
          src={testimonial.avatar} 
          alt={`Profile photo of ${testimonial.name}, ${testimonial.role}`} 
          className="w-full h-full object-cover"
          width={48}
          height={48}
        />
      </div>
    );
  }
  
  // Use initials if no image
  const colorClass = colorMapping[testimonial.color] || colorMapping.indigo;
  return (
    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${colorClass.light} border ${colorClass.lightBorder}`}>
      <span className="text-sm font-semibold">{testimonial.name.charAt(0)}</span>
    </div>
  );
};

export default function TestimonialsSection() {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isCarousel, setIsCarousel] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Check screen size to determine if we should use carousel
  useEffect(() => {
    setIsMounted(true);
    const handleResize = () => {
      setIsCarousel(window.innerWidth < 1024);
    };

    // Initial check
    handleResize();
    
    // Add event listener
    window.addEventListener('resize', handleResize);
    
    // Clean up
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Navigate through testimonials
  const goToNext = useCallback(() => {
    setActiveIndex((current) => 
      current === testimonials.length - 1 ? 0 : current + 1
    );
  }, []);

  const goToPrevious = useCallback(() => {
    setActiveIndex((current) => 
      current === 0 ? testimonials.length - 1 : current - 1
    );
  }, []);

  // Auto-rotate carousel
  useEffect(() => {
    if (!isCarousel) return;
    
    const interval = setInterval(goToNext, 6000);
    return () => clearInterval(interval);
  }, [goToNext, isCarousel]);

  // If not mounted yet (for SSR)
  if (!isMounted) {
    return null;
  }

  return (
    <section id="testimonials" className="relative py-8 overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none"></div>
      <div className="absolute -top-40 left-1/3 w-80 h-80 bg-indigo-600 rounded-full filter blur-[100px] opacity-10 animate-pulse-slow"></div>
      <div className="absolute -bottom-40 right-1/4 w-80 h-80 bg-purple-600 rounded-full filter blur-[100px] opacity-10 animate-pulse-slow delay-1000"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Section heading */}
        <div className="text-center mb-12">
          <h2 className="heading-lg laser-gradient-text mb-4" data-text="What Our Customers Say">
            What Our Customers Say
          </h2>
          <p className="subheading-text max-w-3xl mx-auto">
            Join thousands of businesses who've transformed their communication with our human-like AI voice technology
          </p>
        </div>
        
        {/* Testimonial cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 gap-y-8">
          {testimonials.map((testimonial) => (
            <TestimonialCard key={testimonial.id} testimonial={testimonial} />
          ))}
        </div>
      </div>
    </section>
  );
}

// Testimonial Card Component
function TestimonialCard({ testimonial }) {
  const colorClass = colorMapping[testimonial.color] || colorMapping.indigo;

  // Generate stars for rating
  const stars = Array.from({ length: testimonial.stars }, (_, i) => (
    <svg key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
    </svg>
  ));

  return (
    <div className={`rounded-lg border border-glow bg-gradient-to-br ${colorClass.bg} p-6 h-full transition-all duration-300 hover:scale-[1.02] group relative overflow-hidden testimonial-card`}>
      {/* Ambient light effect */}
      <div className={`absolute -right-20 -top-20 w-40 h-40 rounded-full ${colorClass.light} blur-[80px] opacity-30 group-hover:opacity-60 transition-opacity duration-300`}></div>
      
      {/* Star rating */}
      <div className="flex mb-4">{stars}</div>
      
      {/* Testimonial content */}
      <div className="mb-6 relative z-10 testimonial-content">
        <p className="text-gray-300 mb-4">&quot;{testimonial.content}&quot;</p>
      </div>
      
      {/* User info */}
      <div className="flex items-center testimonial-author">
        <div className="mr-4">
          <div className={`w-12 h-12 rounded-full ${colorClass.light || 'bg-indigo-500/10'} border ${colorClass.lightBorder || 'border-indigo-500/20'} flex items-center justify-center overflow-hidden`}>
            {testimonial.color === "indigo" ? (
              // Small Business Owner icon
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            ) : testimonial.color === "purple" ? (
              // Real Estate Agent icon
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
            ) : (
              // Healthcare Provider icon
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-pink-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            )}
          </div>
        </div>
        <div>
          <h4 className="font-medium text-white text-base">{testimonial.name}</h4>
          <p className={`${colorClass.text} text-sm`}>{testimonial.role}</p>
        </div>
      </div>
      
      {/* Hover glow effect */}
      <div className={`absolute inset-0 opacity-0 group-hover:opacity-100 shadow-[inset_0_0_30px_rgba(79,70,229,0.1)] transition-opacity duration-300 pointer-events-none ${colorClass.glow}`}></div>
    </div>
  );
} 