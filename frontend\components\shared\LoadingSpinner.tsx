'use client';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large' | 'lg';
  color?: 'primary' | 'white' | 'gray' | 'blue';
}

export default function LoadingSpinner({ size = 'medium', color = 'primary' }: LoadingSpinnerProps) {
  // Determine size classes
  const sizeClasses = {
    small: 'h-3 w-3 sm:h-4 sm:w-4',
    medium: 'h-6 w-6 sm:h-8 sm:w-8',
    large: 'h-10 w-10 sm:h-12 sm:w-12',
    lg: 'h-10 w-10 sm:h-12 sm:w-12'
  };

  // Determine color classes
  const colorClasses = {
    primary: 'text-indigo-600 dark:text-indigo-400',
    white: 'text-white',
    gray: 'text-gray-500 dark:text-gray-400',
    blue: 'text-blue-600 dark:text-blue-400'
  };

  return (
    <div className="flex justify-center items-center">
      <svg
        className={`animate-spin ${sizeClasses[size]} ${colorClasses[color]}`}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        ></circle>
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
    </div>
  );
}
