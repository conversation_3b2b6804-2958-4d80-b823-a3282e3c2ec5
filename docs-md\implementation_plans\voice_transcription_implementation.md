# Voice Transcription Implementation Plan

## Overview
This document outlines the implementation plan for the Voice Transcription feature within CallSaver.app. This feature enables users to view, search, and analyze transcriptions of their phone calls, with advanced capabilities like sentiment analysis, keyword extraction, and transcript search.

## Component Hierarchy
```mermaid
graph TD
    A[TranscriptionDashboardPage] --> B(TranscriptionList);
    A --> C(TranscriptionDetailView{props: transcription, isOpen, onClose});
    A --> D(TranscriptionSearchBar{props: onSearch});

    B --> B1(TranscriptionListItem{props: transcription, onSelect});

    C --> C1(TranscriptionPlayer{props: recordingUrl, transcription});
    C --> C2(SentimentAnalysisVisual{props: sentimentData});
    C --> C3(KeywordHighlighter{props: transcriptionText, keywords});
    C --> C4(TranscriptionActions{props: transcription, onShare, onDownload, onDelete});
    C --> C5(SpeakerIdentification{props: transcriptionSegments});

    D --> D1(SearchInput{props: query, onChange});
    D --> D2(FilterOptions{props: filters, onChange});
```

## Component Details

* **TranscriptionDashboardPage**: Main container component for the transcription feature.
  * State: `selectedTranscription`, `searchQuery`, `filterOptions`, `isDetailViewOpen`, `isLoading`
  * Responsible for fetching transcription data and managing overall state

* **TranscriptionList**: Displays a list of available transcriptions.
  * Props: `transcriptions`, `onSelect`, `isLoading`
  * Renders a list of `TranscriptionListItem` components
  * Includes pagination and sorting options

* **TranscriptionListItem**: Individual transcription item in the list.
  * Props: `transcription`, `onSelect`
  * Displays basic info: date, caller, duration, sentiment indicator

* **TranscriptionDetailView**: Modal or panel showing detailed transcription view.
  * Props: `transcription`, `isOpen`, `onClose`
  * Contains player, sentiment analysis, keyword highlighting, and actions

* **TranscriptionPlayer**: Audio player with synchronized transcript highlighting.
  * Props: `recordingUrl`, `transcription`
  * Highlights text as audio plays, allows clicking on text to jump to that point

* **SentimentAnalysisVisual**: Visual representation of sentiment analysis.
  * Props: `sentimentData`
  * Shows sentiment trends over time with charts and overall sentiment score

* **KeywordHighlighter**: Highlights important keywords in the transcript.
  * Props: `transcriptionText`, `keywords`
  * Visually emphasizes key terms, topics, and entities

* **TranscriptionActions**: Action buttons for the transcription.
  * Props: `transcription`, `onShare`, `onDownload`, `onDelete`
  * Provides options to share, download, or delete the transcription

* **SpeakerIdentification**: Identifies and labels different speakers.
  * Props: `transcriptionSegments`
  * Visually separates and labels different speakers in the conversation

* **TranscriptionSearchBar**: Search and filter interface.
  * Props: `onSearch`
  * Includes `SearchInput` and `FilterOptions` components

## Data Flow

1. **Load Transcriptions**: `TranscriptionDashboardPage` fetches the list of transcriptions (`/api/transcriptions`) via TanStack Query.
2. **Search/Filter**: User enters search terms or applies filters in `TranscriptionSearchBar`. `onSearch` callback updates the parent state, triggering a new API request with search parameters.
3. **View Details**: User clicks on a `TranscriptionListItem`. `onSelect` callback sets the `selectedTranscription` in the parent and opens the `TranscriptionDetailView`.
4. **Play Recording**: User interacts with `TranscriptionPlayer`. Component manages audio playback and synchronized text highlighting.
5. **Perform Actions**: User clicks action buttons in `TranscriptionActions`. Callbacks trigger API requests for sharing, downloading, or deleting.

## API Integration

* `GET /api/transcriptions`: Fetch list of transcriptions with optional search/filter parameters.
  * Query params: `search`, `dateFrom`, `dateTo`, `sentiment`, `duration`, `page`, `limit`
  * Returns: `{ transcriptions: [...], totalCount, pageCount }`

* `GET /api/transcriptions/{id}`: Fetch detailed transcription data.
  * Returns: `{ id, callSid, recordingUrl, transcriptionText, segments, sentiment, keywords, entities, callDetails }`

* `GET /api/transcriptions/{id}/audio`: Stream or download the audio recording.

* `POST /api/transcriptions/search`: Advanced search within transcriptions.
  * Body: `{ query, filters }`
  * Returns: `{ results: [...], totalCount }`

* `DELETE /api/transcriptions/{id}`: Delete a transcription.
  * Returns: `{ success: true }`

* `POST /api/transcriptions/{id}/share`: Generate a shareable link.
  * Returns: `{ shareUrl, expiresAt }`

## Backend Implementation

### Database Updates
* Ensure the `CallLog` model has all necessary fields for transcription data:
  * `transcriptionText`: Full text of the transcription
  * `transcriptionStatus`: Status of the transcription process
  * `hasTranscription`: Boolean flag for quick filtering
  * `sentimentScore`: Overall sentiment score
  * `sentimentData`: JSON data with detailed sentiment analysis
  * `keywords`: Array or JSON of extracted keywords
  * `entities`: Array or JSON of identified entities
  * `speakerSegments`: JSON data with speaker identification segments

### Services
1. **TranscriptionService**:
   * Enhance existing service to include sentiment analysis
   * Add keyword extraction functionality
   * Implement speaker identification (diarization)
   * Add methods for searching within transcriptions

2. **SentimentAnalysisService**:
   * Create service to analyze sentiment in transcriptions
   * Implement methods for overall sentiment and sentiment over time
   * Provide visualization data for frontend charts

3. **KeywordExtractionService**:
   * Create service to extract important keywords and entities
   * Implement relevance scoring for keywords
   * Group keywords by categories (topics, entities, actions)

### Controllers
1. **TranscriptionController**:
   * Implement endpoints for listing, retrieving, searching, and deleting transcriptions
   * Add endpoints for sharing and downloading
   * Implement filtering and pagination

## Frontend Implementation

### State Management
* Use TanStack Query for data fetching and caching
* Create custom hooks:
  * `useTranscriptions`: For listing and filtering transcriptions
  * `useTranscriptionDetail`: For fetching detailed transcription data
  * `useTranscriptionSearch`: For searching within transcriptions

### UI Components
1. **TranscriptionDashboardPage**:
   * Implement main layout with list and detail views
   * Add search and filter functionality
   * Implement responsive design for mobile and desktop

2. **TranscriptionPlayer**:
   * Create custom audio player with transcript synchronization
   * Implement time-aligned text highlighting
   * Add controls for playback speed, seeking, and volume

3. **SentimentAnalysisVisual**:
   * Create visualizations using Recharts or similar library
   * Implement sentiment timeline chart
   * Add overall sentiment score with visual indicator

4. **KeywordHighlighter**:
   * Implement text highlighting for keywords
   * Create interactive keyword list with filtering
   * Add tooltip explanations for highlighted terms

## User Interactions
* **Viewing Transcriptions**: User navigates to Transcriptions page. List of recent transcriptions loads automatically.
* **Searching**: User enters search terms in search bar. Results update in real-time or on submit.
* **Filtering**: User selects filter options. List updates to show matching transcriptions.
* **Viewing Details**: User clicks on a transcription. Detail view opens showing full transcript, sentiment analysis, and keywords.
* **Playing Audio**: User clicks play button. Audio plays while transcript highlights in sync.
* **Sharing**: User clicks share button. System generates shareable link and provides copy option.
* **Downloading**: User clicks download button. Transcript downloads as text or PDF file.

## Implementation Notes
* **Performance Considerations**:
  * Implement pagination for transcription lists
  * Consider lazy loading for audio files
  * Cache sentiment analysis and keyword extraction results

* **Accessibility**:
  * Ensure audio player has proper controls and keyboard navigation
  * Provide text alternatives for sentiment visualizations
  * Ensure color choices for sentiment and keyword highlighting are accessible

* **Security**:
  * Implement proper access controls for transcription data
  * Ensure shared transcriptions have secure, time-limited links
  * Sanitize search inputs to prevent injection attacks

* **Testing**:
  * Create unit tests for sentiment analysis and keyword extraction
  * Implement integration tests for search functionality
  * Test audio player across different browsers and devices

## Dependencies
* **Frontend**:
  * Recharts (or similar) for data visualization
  * React Audio Player (or custom implementation)
  * TanStack Query for data fetching
  * Tailwind CSS for styling

* **Backend**:
  * OpenAI API for advanced sentiment analysis
  * Natural language processing libraries for keyword extraction
  * Audio processing libraries for speaker identification

## Link to prompt_to_mdc_router.mdc
* **Primary Purpose**: Defines the implementation plan for the Voice Transcription feature.
* **Frontend Components**: `TranscriptionDashboardPage`, `TranscriptionList`, `TranscriptionDetailView`, `TranscriptionPlayer`, `SentimentAnalysisVisual`, `KeywordHighlighter`, and their sub-components.
* **API Endpoints**: `/api/transcriptions`, `/api/transcriptions/{id}`, `/api/transcriptions/search`, `/api/transcriptions/{id}/audio`, `/api/transcriptions/{id}/share`.
* **Dependencies**: Relies on existing `CallLog` model and `transcriptionService.js`. Assumes OpenAI API integration is already implemented.
