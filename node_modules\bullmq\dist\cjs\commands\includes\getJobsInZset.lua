--[[
  We use ZRANGEBYSCORE to make the case where we're deleting a limited number
  of items in a sorted set only run a single iteration. If we simply used
  ZRANGE, we may take a long time traversing through jobs that are within the
  grace period.
]]
local function getJobsInZset(zsetKey, rangeEnd, limit)
  if limit > 0 then
    return rcall("ZRANG<PERSON><PERSON>Y<PERSON>OR<PERSON>", zset<PERSON><PERSON>, 0, rangeEnd, "LIM<PERSON>", 0, limit)
  else
    return rcall("Z<PERSON>NGEBYSCOR<PERSON>", zsetKey, 0, rangeEnd)
  end
end
