# Role-Based Access Control System

## Overview

The CallSaver platform implements a comprehensive role-based access control (RBAC) system that explicitly maps roles to permissions. This system prevents privilege escalation vulnerabilities by ensuring that users can only perform actions that are explicitly allowed for their role.

## Roles

The system defines the following default roles:

1. **Admin** - Full access to all system features and resources
2. **Developer** - Access to developer-specific features and their own resources
3. **Member** - Standard user with access to their own resources
4. **Viewer** - Read-only access to their own resources

## Permission Structure

Permissions follow a structured format:

```
resource:action[:scope]
```

Where:
- **resource**: The type of resource being accessed (e.g., `users`, `phoneNumbers`, `calls`)
- **action**: The operation being performed (e.g., `read`, `create`, `update`, `delete`)
- **scope**: Optional scope of access (e.g., `self`, `org`, `any`)

### Special Permission Wildcards

- `*`: Wildcard for all resources or actions (e.g., `*:read`, `users:*`)
- `**`: Super wildcard grants all permissions (only for admin)

### Permission Scopes

- `self`: Only resources owned by the user
- `org`: Resources within the user's organization
- `any`: Any resource in the system (typically admin only)

## Implementation

The RBAC system is implemented through several components:

1. **Role-Permission Mapping**: Defined in `back/backend/config/rolePermissions.js`
2. **Access Control Service**: Implemented in `back/backend/services/accessControlService.js`
3. **Permission Middleware**: Implemented in `back/backend/middleware/permissionMiddleware.js`

## Usage in Routes

Routes can use the permission middleware to enforce access control:

```javascript
const { requirePermission } = require('../middleware/permissionMiddleware');

// Require a specific permission
router.get('/users/:id', protect, requirePermission('users:read:self'), userController.getUser);

// Require multiple permissions
router.put('/settings', protect, requireAllPermissions(['settings:read', 'settings:update']), settingsController.updateSettings);
```

## Resource Ownership

The system checks resource ownership based on the permission scope:

- `self`: Checks if the resource belongs to the user (userId matches)
- `org`: Checks if the resource belongs to the user's organization
- `any`: Allows access to any resource (typically admin only)

## Security Benefits

This RBAC system provides several security benefits:

1. **Principle of Least Privilege**: Users only have the permissions they need
2. **Defense in Depth**: Multiple layers of permission checks
3. **Explicit Permissions**: No implicit permissions based on role
4. **Granular Control**: Fine-grained control over what actions users can perform
5. **Audit Trail**: All permission checks are logged for security auditing

## Extending the System

The system can be extended in several ways:

1. **Custom Roles**: Define new roles with specific permissions
2. **Dynamic Permissions**: Implement dynamic permission assignment based on user attributes
3. **Permission Groups**: Group related permissions for easier management
4. **Role Hierarchies**: Implement role inheritance for more complex scenarios
