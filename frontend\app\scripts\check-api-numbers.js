// File: check-api-numbers.js
// This script can be used to check what's happening with the numbers API

(function() {
  console.log('Checking phone numbers API...');
  
  // Get the token from localStorage
  const supabaseKey = Object.keys(localStorage).find(key => 
    key.startsWith('sb-') && key.endsWith('-auth-token')
  );
  
  if (!supabaseKey) {
    console.error('No Supabase session found in localStorage');
    return;
  }
  
  try {
    const sessionStr = localStorage.getItem(supabaseKey);
    const session = JSON.parse(sessionStr);
    
    if (!session || !session.access_token) {
      console.error('No access token found in session');
      return;
    }
    
    // Make the API call with the token
    fetch('/api/numbers/mine', {
      headers: {
        'Authorization': `Bearer ${session.access_token}`
      }
    })
    .then(response => {
      console.log('API Response status:', response.status);
      return response.json();
    })
    .then(data => {
      console.log('API Response data:', data);
      if (data.success && data.numbers) {
        console.log(`Found ${data.numbers.length} phone numbers`);
        data.numbers.forEach(num => {
          console.log(`- ${num.number} (${num.countryCode})`);
        });
      } else {
        console.log('No phone numbers found or API error');
      }
    })
    .catch(error => {
      console.error('API call error:', error);
    });
    
  } catch (error) {
    console.error('Error:', error);
  }
})();
