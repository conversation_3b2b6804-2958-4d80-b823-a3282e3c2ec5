import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';

// Types
export interface ProfileData {
  id: string;
  email: string;
  fullName: string;
  phone?: string;
  company?: string;
  jobTitle?: string;
  timezone: string;
  bio?: string;
  avatarUrl?: string;
}

export interface SecurityData {
  isMfaEnabled: boolean;
  passwordLastChanged?: string;
  securityQuestions?: boolean;
}

export interface Session {
  id: string;
  device: string;
  browser: string;
  ip: string;
  location?: string;
  lastActive: string;
  isCurrent: boolean;
}

export interface NotificationChannel {
  type: 'email' | 'sms' | 'in-app';
  enabled: boolean;
}

export interface NotificationType {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  channels: {
    email: boolean;
    sms: boolean;
    inApp: boolean;
  };
}

export interface NotificationPreferences {
  channels: NotificationChannel[];
  types: NotificationType[];
}

export interface ApiKey {
  id: string;
  label: string;
  key: string; // Partially masked
  createdAt: string;
  lastUsed?: string;
  permissions: string[];
}

export interface Subscription {
  id: string;
  plan: string;
  status: 'active' | 'canceled' | 'past_due' | 'trialing';
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  credits: number;
  features: {
    name: string;
    included: boolean;
    limit?: number;
    used?: number;
  }[];
}

export interface BillingHistoryItem {
  id: string;
  date: string;
  description: string;
  amount: number;
  status: 'paid' | 'pending' | 'failed';
  invoiceUrl?: string;
}

export interface PaymentMethod {
  id: string;
  type: 'card' | 'bank_account';
  last4: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  isDefault: boolean;
}

export interface OrganizationData {
  id: string;
  name: string;
  address?: {
    line1: string;
    line2?: string;
    city: string;
    state?: string;
    postalCode: string;
    country: string;
  };
  phone?: string;
  website?: string;
  logoUrl?: string;
  industry?: string;
}

export interface TeamMember {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'member' | 'billing';
  status: 'active' | 'invited' | 'disabled';
  avatarUrl?: string;
  joinedAt?: string;
}

// Profile data
export const useProfile = () => {
  return useQuery({
    queryKey: ['profile'],
    queryFn: async () => {
      const response = await axios.get<ProfileData>('/api/user/profile');
      return response.data;
    }
  });
};

// Update profile
export const useUpdateProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (profileData: Partial<ProfileData>) => {
      const response = await axios.put<ProfileData>('/api/user/profile', profileData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    }
  });
};

// Upload avatar
export const useUploadAvatar = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('avatar', file);
      
      const response = await axios.post<{ avatarUrl: string }>('/api/user/profile/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['profile'] });
    }
  });
};

// Security settings
export const useSecurity = () => {
  return useQuery({
    queryKey: ['security'],
    queryFn: async () => {
      const response = await axios.get<SecurityData>('/api/user/security');
      return response.data;
    }
  });
};

// Change password
export const useChangePassword = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: { currentPassword: string; newPassword: string }) => {
      const response = await axios.put('/api/user/security/password', data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['security'] });
    }
  });
};

// Enable MFA
export const useEnableMfa = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (verificationCode: string) => {
      const response = await axios.post('/api/user/security/mfa/enable', { verificationCode });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['security'] });
    }
  });
};

// Disable MFA
export const useDisableMfa = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (verificationData: { password?: string; verificationCode?: string }) => {
      const response = await axios.post('/api/user/security/mfa/disable', verificationData);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['security'] });
    }
  });
};

// Get MFA setup data
export const useMfaSetup = () => {
  return useQuery({
    queryKey: ['mfa-setup'],
    queryFn: async () => {
      const response = await axios.get<{ qrCodeUrl: string; secret: string }>('/api/user/security/mfa/setup');
      return response.data;
    },
    enabled: false // Only fetch when explicitly requested
  });
};

// Active sessions
export const useSessions = () => {
  return useQuery({
    queryKey: ['sessions'],
    queryFn: async () => {
      const response = await axios.get<Session[]>('/api/user/sessions');
      return response.data;
    }
  });
};

// Terminate session
export const useTerminateSession = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (sessionId: string) => {
      const response = await axios.delete(`/api/user/sessions/${sessionId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
    }
  });
};

// Notification preferences
export const useNotificationPreferences = () => {
  return useQuery({
    queryKey: ['notifications'],
    queryFn: async () => {
      const response = await axios.get<NotificationPreferences>('/api/user/notifications/preferences');
      return response.data;
    }
  });
};

// Update notification preferences
export const useUpdateNotificationPreferences = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (preferences: Partial<NotificationPreferences>) => {
      const response = await axios.put<NotificationPreferences>('/api/user/notifications/preferences', preferences);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    }
  });
};

// API keys
export const useApiKeys = () => {
  return useQuery({
    queryKey: ['apiKeys'],
    queryFn: async () => {
      const response = await axios.get<ApiKey[]>('/api/user/api-keys');
      return response.data;
    }
  });
};

// Generate API key
export const useGenerateApiKey = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: { label: string; permissions: string[] }) => {
      const response = await axios.post<{ apiKey: ApiKey; fullKey: string }>('/api/user/api-keys', data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['apiKeys'] });
    }
  });
};

// Revoke API key
export const useRevokeApiKey = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (keyId: string) => {
      const response = await axios.delete(`/api/user/api-keys/${keyId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['apiKeys'] });
    }
  });
};

// Subscription data
export const useSubscription = () => {
  return useQuery({
    queryKey: ['subscription'],
    queryFn: async () => {
      const response = await axios.get<Subscription>('/api/user/subscription');
      return response.data;
    }
  });
};

// Billing history
export const useBillingHistory = () => {
  return useQuery({
    queryKey: ['billingHistory'],
    queryFn: async () => {
      const response = await axios.get<BillingHistoryItem[]>('/api/user/billing/history');
      return response.data;
    }
  });
};

// Payment methods
export const usePaymentMethods = () => {
  return useQuery({
    queryKey: ['paymentMethods'],
    queryFn: async () => {
      const response = await axios.get<PaymentMethod[]>('/api/user/billing/payment-methods');
      return response.data;
    }
  });
};

// Add payment method
export const useAddPaymentMethod = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (paymentMethodId: string) => {
      const response = await axios.post('/api/user/billing/payment-methods', { paymentMethodId });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentMethods'] });
    }
  });
};

// Remove payment method
export const useRemovePaymentMethod = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (methodId: string) => {
      const response = await axios.delete(`/api/user/billing/payment-methods/${methodId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentMethods'] });
    }
  });
};

// Set default payment method
export const useSetDefaultPaymentMethod = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (methodId: string) => {
      const response = await axios.put(`/api/user/billing/payment-methods/${methodId}/default`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paymentMethods'] });
    }
  });
};

// Organization data (admin only)
export const useOrganization = () => {
  return useQuery({
    queryKey: ['organization'],
    queryFn: async () => {
      const response = await axios.get<OrganizationData>('/api/organization');
      return response.data;
    },
    enabled: false // Only fetch when user is confirmed as admin
  });
};

// Update organization
export const useUpdateOrganization = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: Partial<OrganizationData>) => {
      const response = await axios.put<OrganizationData>('/api/organization', data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organization'] });
    }
  });
};

// Team members
export const useTeamMembers = () => {
  return useQuery({
    queryKey: ['teamMembers'],
    queryFn: async () => {
      const response = await axios.get<TeamMember[]>('/api/organization/members');
      return response.data;
    },
    enabled: false // Only fetch when user is confirmed as admin
  });
};

// Invite team member
export const useInviteTeamMember = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: { email: string; role: string }) => {
      const response = await axios.post('/api/organization/members', data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teamMembers'] });
    }
  });
};

// Update team member role
export const useUpdateTeamMemberRole = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ memberId, role }: { memberId: string; role: string }) => {
      const response = await axios.put(`/api/organization/members/${memberId}`, { role });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teamMembers'] });
    }
  });
};

// Remove team member
export const useRemoveTeamMember = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (memberId: string) => {
      const response = await axios.delete(`/api/organization/members/${memberId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teamMembers'] });
    }
  });
};
