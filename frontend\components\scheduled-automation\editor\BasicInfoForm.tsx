'use client';

import { PhoneIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';

interface BasicInfoFormProps {
  name: string;
  description?: string;
  type: 'call' | 'sms';
  onChange: (field: string, value: any) => void;
}

export default function BasicInfoForm({
  name,
  description,
  type,
  onChange,
}: BasicInfoFormProps) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h2>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
          Start by giving your automation a name and selecting the type of automation you want to create.
        </p>
      </div>

      {/* Name field */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Automation Name <span className="text-red-500">*</span>
        </label>
        <input
          type="text"
          id="name"
          value={name}
          onChange={(e) => onChange('name', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          placeholder="e.g., Appointment Reminder"
          required
        />
      </div>

      {/* Description field */}
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Description (Optional)
        </label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => onChange('description', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          placeholder="Briefly describe the purpose of this automation"
        />
      </div>

      {/* Automation type */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Automation Type <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Call option */}
          <div
            className={`border rounded-lg p-4 cursor-pointer ${
              type === 'call'
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
            }`}
            onClick={() => onChange('type', 'call')}
          >
            <div className="flex items-center">
              <div className={`p-2 rounded-full ${
                type === 'call'
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
              }`}>
                <PhoneIcon className="h-5 w-5" />
              </div>
              <div className="ml-3">
                <h3 className={`font-medium ${
                  type === 'call'
                    ? 'text-blue-700 dark:text-blue-400'
                    : 'text-gray-900 dark:text-white'
                }`}>
                  Automated Call
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Schedule automated voice calls
                </p>
              </div>
            </div>
          </div>

          {/* SMS option */}
          <div
            className={`border rounded-lg p-4 cursor-pointer ${
              type === 'sms'
                ? 'border-green-500 bg-green-50 dark:bg-green-900/30'
                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
            }`}
            onClick={() => onChange('type', 'sms')}
          >
            <div className="flex items-center">
              <div className={`p-2 rounded-full ${
                type === 'sms'
                  ? 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
              }`}>
                <ChatBubbleLeftRightIcon className="h-5 w-5" />
              </div>
              <div className="ml-3">
                <h3 className={`font-medium ${
                  type === 'sms'
                    ? 'text-green-700 dark:text-green-400'
                    : 'text-gray-900 dark:text-white'
                }`}>
                  Automated SMS
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Schedule automated text messages
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
