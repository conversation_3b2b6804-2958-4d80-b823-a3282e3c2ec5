"use client";

import { useEffect, useState, useRef } from "react";
import { motion } from "framer-motion";
import { 
  MessageSquare, 
  Search, 
  Send, 
  RefreshCw, 
  Phone,
  Clock,
  CheckCircle,
  XCircle,
  Filter,
  ChevronDown,
  Plus,
  User
} from "lucide-react";
import { format, formatDistanceToNow, parseISO } from "date-fns";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Ava<PERSON>, AvatarFallback } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { Label } from "@/components/ui/label";

// Type definitions
interface Message {
  id: string;
  messageSid: string;
  from: string;
  to: string;
  body: string;
  direction: "inbound" | "outbound";
  status: string;
  numMedia: number;
  createdAt: string;
  updatedAt: string;
  userId: string;
  organizationId: string;
  phoneNumberId: string;
  errorCode?: number;
  errorMessage?: string;
}

interface PhoneNumber {
  id: string;
  number: string;
  userId: string;
  organizationId: string;
}

interface Contact {
  phoneNumber: string;
  name?: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
}

// Message status badge component
const MessageStatusBadge = ({ status }: { status: string }) => {
  const statusConfig: { [key: string]: { icon: React.ReactNode, color: string } } = {
    "delivered": { icon: <CheckCircle className="h-3 w-3 mr-1" />, color: "bg-green-100 text-green-800" },
    "sent": { icon: <CheckCircle className="h-3 w-3 mr-1" />, color: "bg-blue-100 text-blue-800" },
    "received": { icon: <CheckCircle className="h-3 w-3 mr-1" />, color: "bg-green-100 text-green-800" },
    "failed": { icon: <XCircle className="h-3 w-3 mr-1" />, color: "bg-red-100 text-red-800" },
    "queued": { icon: <Clock className="h-3 w-3 mr-1" />, color: "bg-yellow-100 text-yellow-800" },
  };

  const { icon, color } = statusConfig[status] || 
    { icon: <Clock className="h-3 w-3 mr-1" />, color: "bg-gray-100 text-gray-800" };

  return (
    <Badge variant="outline" className={`flex items-center text-xs ${color}`}>
      {icon} {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>
  );
};

// Format phone number
const formatPhoneNumber = (phoneNumber: string) => {
  if (!phoneNumber) return "";
  
  // Handle E.164 format (e.g., +12125551234)
  if (phoneNumber.startsWith('+')) {
    const countryCode = phoneNumber.slice(1, phoneNumber.length - 10);
    const areaCode = phoneNumber.slice(-10, -7);
    const firstPart = phoneNumber.slice(-7, -4);
    const lastPart = phoneNumber.slice(-4);
    return `+${countryCode} (${areaCode}) ${firstPart}-${lastPart}`;
  }
  
  // Handle 10-digit format
  if (phoneNumber.length === 10) {
    return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
  }
  
  return phoneNumber;
};

// Contact Item component
const ContactItem = ({ 
  contact, 
  selected, 
  onClick 
}: { 
  contact: Contact, 
  selected: boolean, 
  onClick: () => void 
}) => {
  return (
    <div 
      className={`flex items-center p-3 ${selected ? 'bg-primary/10' : 'hover:bg-gray-50'} rounded-md cursor-pointer transition-colors`}
      onClick={onClick}
    >
      <Avatar className="h-10 w-10 mr-3">
        <AvatarFallback className="bg-primary/10 text-primary">
          {contact.name ? contact.name.charAt(0).toUpperCase() : 'U'}
        </AvatarFallback>
      </Avatar>
      <div className="flex-1 min-w-0">
        <div className="flex justify-between items-center">
          <span className="font-medium truncate">
            {contact.name || formatPhoneNumber(contact.phoneNumber)}
          </span>
          {contact.lastMessageTime && (
            <span className="text-xs text-gray-500">
              {formatDistanceToNow(parseISO(contact.lastMessageTime), { addSuffix: true })}
            </span>
          )}
        </div>
        {contact.lastMessage && (
          <p className="text-sm text-gray-500 truncate">{contact.lastMessage}</p>
        )}
      </div>
      {contact.unreadCount && contact.unreadCount > 0 && (
        <Badge className="ml-2">{contact.unreadCount}</Badge>
      )}
    </div>
  );
};

// Message Bubble component
const MessageBubble = ({ message }: { message: Message }) => {
  const isInbound = message.direction === "inbound";
  
  return (
    <div className={`flex ${isInbound ? 'justify-start' : 'justify-end'} mb-4`}>
      <div className={`max-w-[70%] ${isInbound ? 'bg-gray-100' : 'bg-primary text-primary-foreground'} rounded-lg p-3`}>
        <div className="whitespace-pre-wrap break-words">{message.body}</div>
        <div className="flex justify-end items-center mt-1 space-x-1">
          <span className="text-xs opacity-70">
            {format(parseISO(message.createdAt), "h:mm a")}
          </span>
          {!isInbound && <MessageStatusBadge status={message.status} />}
        </div>
      </div>
    </div>
  );
};

// Empty Conversation component
const EmptyConversation = () => (
  <div className="flex flex-col items-center justify-center h-full p-8 text-center text-gray-500">
    <MessageSquare className="h-12 w-12 mb-4 opacity-20" />
    <h3 className="text-lg font-medium mb-2">No conversation selected</h3>
    <p className="max-w-sm">
      Select a conversation from the list or start a new message to begin chatting.
    </p>
  </div>
);

// New Message Dialog component
const NewMessageDialog = ({ 
  open, 
  onOpenChange, 
  phoneNumbers, 
  onSend 
}: { 
  open: boolean, 
  onOpenChange: (open: boolean) => void, 
  phoneNumbers: PhoneNumber[], 
  onSend: (to: string, from: string, message: string) => void 
}) => {
  const [to, setTo] = useState("");
  const [from, setFrom] = useState("");
  const [message, setMessage] = useState("");
  
  useEffect(() => {
    if (phoneNumbers.length > 0 && !from) {
      setFrom(phoneNumbers[0].id);
    }
  }, [phoneNumbers, from]);
  
  const handleSend = () => {
    if (to && from && message) {
      onSend(to, from, message);
      onOpenChange(false);
      setTo("");
      setMessage("");
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>New Message</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="from" className="text-right">
              From
            </Label>
            <Select value={from} onValueChange={setFrom}>
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select a number" />
              </SelectTrigger>
              <SelectContent>
                {phoneNumbers.map((number) => (
                  <SelectItem key={number.id} value={number.id}>
                    {formatPhoneNumber(number.number)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="to" className="text-right">
              To
            </Label>
            <Input
              id="to"
              placeholder="+****************"
              value={to}
              onChange={(e) => setTo(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="message" className="text-right">
              Message
            </Label>
            <Textarea
              id="message"
              placeholder="Type your message..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="submit" onClick={handleSend} disabled={!to || !from || !message}>
            Send Message
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Main Messages Page Component
export default function MessagesPage() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [conversations, setConversations] = useState<{ [key: string]: Message[] }>({});
  const [selectedContact, setSelectedContact] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [newMessage, setNewMessage] = useState("");
  const [phoneNumbers, setPhoneNumbers] = useState<PhoneNumber[]>([]);
  const [selectedPhoneNumber, setSelectedPhoneNumber] = useState<string>("");
  const [newMessageDialogOpen, setNewMessageDialogOpen] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    // Fetch user's phone numbers
    const fetchPhoneNumbers = async () => {
      try {
        const response = await fetch('/api/phone-numbers');
        if (response.ok) {
          const data = await response.json();
          setPhoneNumbers(data.phoneNumbers);
          
          if (data.phoneNumbers.length > 0) {
            setSelectedPhoneNumber(data.phoneNumbers[0].id);
          }
        }
      } catch (error) {
        console.error('Failed to fetch phone numbers:', error);
      }
    };
    
    // Fetch contacts
    const fetchContacts = async () => {
      try {
        setLoading(true);
        // In a real app, this would fetch from the API
        const response = await fetch('/api/messages/contacts');
        
        if (response.ok) {
          const data = await response.json();
          setContacts(data.contacts);
        }
      } catch (error) {
        console.error('Failed to fetch contacts:', error);
        // For demo purposes, creating mock contacts
        const mockContacts: Contact[] = [
          {
            phoneNumber: '+12125551234',
            name: 'John Smith',
            lastMessage: 'Thanks for your call yesterday',
            lastMessageTime: new Date().toISOString(),
            unreadCount: 2
          },
          {
            phoneNumber: '+12125556789',
            lastMessage: 'When will the shipment arrive?',
            lastMessageTime: new Date(Date.now() - 3600000).toISOString(),
          },
          {
            phoneNumber: '+12125550987',
            name: 'Alice Johnson',
            lastMessage: 'The meeting is confirmed for tomorrow',
            lastMessageTime: new Date(Date.now() - 86400000).toISOString(),
          }
        ];
        setContacts(mockContacts);
      } finally {
        setLoading(false);
      }
    };
    
    fetchPhoneNumbers();
    fetchContacts();
  }, []);
  
  useEffect(() => {
    // When a contact is selected, fetch conversation
    if (selectedContact) {
      fetchConversation(selectedContact);
    }
  }, [selectedContact]);
  
  useEffect(() => {
    // Scroll to bottom when new messages arrive
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [conversations, selectedContact]);
  
  const fetchConversation = async (contactPhone: string) => {
    try {
      setLoading(true);
      // In a real app, this would fetch from the API
      const response = await fetch(`/api/messages/conversation?contact=${encodeURIComponent(contactPhone)}`);
      
      if (response.ok) {
        const data = await response.json();
        setConversations(prev => ({
          ...prev,
          [contactPhone]: data.messages
        }));
      }
    } catch (error) {
      console.error('Failed to fetch conversation:', error);
      // For demo purposes, creating mock conversation
      const mockMessages: Message[] = [
        {
          id: '1',
          messageSid: 'SM123',
          from: contactPhone,
          to: '+12125557890',
          body: 'Hello, I need some information about your services.',
          direction: 'inbound',
          status: 'received',
          numMedia: 0,
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          updatedAt: new Date(Date.now() - 86400000).toISOString(),
          userId: 'user1',
          organizationId: 'org1',
          phoneNumberId: 'phone1'
        },
        {
          id: '2',
          messageSid: 'SM124',
          from: '+12125557890',
          to: contactPhone,
          body: 'Thanks for reaching out! How can I assist you today?',
          direction: 'outbound',
          status: 'delivered',
          numMedia: 0,
          createdAt: new Date(Date.now() - 85400000).toISOString(),
          updatedAt: new Date(Date.now() - 85400000).toISOString(),
          userId: 'user1',
          organizationId: 'org1',
          phoneNumberId: 'phone1'
        },
        {
          id: '3',
          messageSid: 'SM125',
          from: contactPhone,
          to: '+12125557890',
          body: "I'm wondering about your pricing plans and if there's a free trial option available.",
          direction: 'inbound',
          status: 'received',
          numMedia: 0,
          createdAt: new Date(Date.now() - 82400000).toISOString(),
          updatedAt: new Date(Date.now() - 82400000).toISOString(),
          userId: 'user1',
          organizationId: 'org1',
          phoneNumberId: 'phone1'
        }
      ];
      
      setConversations(prev => ({
        ...prev,
        [contactPhone]: mockMessages
      }));
    } finally {
      setLoading(false);
    }
  };
  
  const handleSendMessage = async () => {
    if (!selectedContact || !newMessage.trim() || !selectedPhoneNumber) return;
    
    try {
      setSendingMessage(true);
      
      // Find the selected phone number
      const fromNumber = phoneNumbers.find(p => p.id === selectedPhoneNumber)?.number || '';
      
      // Create a new message object
      const message: Message = {
        id: `temp-${Date.now()}`,
        messageSid: '',
        from: fromNumber,
        to: selectedContact,
        body: newMessage.trim(),
        direction: 'outbound',
        status: 'queued',
        numMedia: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: 'current-user',
        organizationId: 'current-org',
        phoneNumberId: selectedPhoneNumber
      };
      
      // Add to local state immediately for UI responsiveness
      setConversations(prev => ({
        ...prev,
        [selectedContact]: [...(prev[selectedContact] || []), message]
      }));
      
      // Clear input
      setNewMessage('');
      
      // In a real app, send to API
      const response = await fetch('/api/messages/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          to: selectedContact,
          from: fromNumber,
          body: message.body,
          phoneNumberId: selectedPhoneNumber
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        // Update with real data from server
        setConversations(prev => {
          const updatedMessages = prev[selectedContact].map(msg => 
            msg.id === message.id ? { ...msg, ...data.message } : msg
          );
          
          return {
            ...prev,
            [selectedContact]: updatedMessages
          };
        });
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      // Update status to failed
      setConversations(prev => {
        const updatedMessages = prev[selectedContact].map(msg => 
          msg.id === `temp-${Date.now()}` ? { ...msg, status: 'failed' } : msg
        );
        
        return {
          ...prev,
          [selectedContact]: updatedMessages
        };
      });
    } finally {
      setSendingMessage(false);
    }
  };
  
  const handleNewMessageSend = async (to: string, fromNumberId: string, message: string) => {
    try {
      // Find the selected phone number
      const fromNumber = phoneNumbers.find(p => p.id === fromNumberId)?.number || '';
      
      // Send the message via API
      const response = await fetch('/api/messages/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          to,
          from: fromNumber,
          body: message,
          phoneNumberId: fromNumberId
        })
      });
      
      if (response.ok) {
        // If successful, select this contact and update UI
        setSelectedContact(to);
        
        // Create a new contact if it doesn't exist
        if (!contacts.some(c => c.phoneNumber === to)) {
          const newContact: Contact = {
            phoneNumber: to,
            lastMessage: message,
            lastMessageTime: new Date().toISOString()
          };
          setContacts(prev => [newContact, ...prev]);
        }
        
        // Fetch conversation or update local state
        fetchConversation(to);
      }
    } catch (error) {
      console.error('Failed to send new message:', error);
    }
  };
  
  const filteredContacts = searchTerm 
    ? contacts.filter(contact => 
        (contact.name?.toLowerCase().includes(searchTerm.toLowerCase()) || 
         contact.phoneNumber.includes(searchTerm)))
    : contacts;
  
  return (
    <motion.div 
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="container max-w-7xl mx-auto py-6 px-4"
    >
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Messages</h1>
          <p className="text-gray-500 mt-1">Send and receive SMS messages</p>
        </div>
        
        <div className="mt-4 md:mt-0 flex space-x-2">
          <Button variant="outline" onClick={() => window.location.reload()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={() => setNewMessageDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Message
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Contact List */}
        <Card className="md:col-span-1">
          <CardHeader className="pb-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input 
                placeholder="Search conversations..." 
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <ScrollArea className="h-[calc(100vh-280px)]">
              {loading && contacts.length === 0 ? (
                <div className="p-3 space-y-4">
                  {Array(5).fill(0).map((_, i) => (
                    <div key={i} className="flex items-center space-x-3">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-full" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : filteredContacts.length === 0 ? (
                <div className="p-8 text-center text-gray-500">
                  <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-20" />
                  <p>No conversations found</p>
                </div>
              ) : (
                <div className="p-1">
                  {filteredContacts.map((contact) => (
                    <ContactItem
                      key={contact.phoneNumber}
                      contact={contact}
                      selected={selectedContact === contact.phoneNumber}
                      onClick={() => setSelectedContact(contact.phoneNumber)}
                    />
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
        
        {/* Conversation */}
        <Card className="md:col-span-2 flex flex-col">
          <CardHeader className="pb-3 flex-shrink-0">
            {selectedContact ? (
              <div className="flex items-center">
                <Avatar className="h-10 w-10 mr-3">
                  <AvatarFallback className="bg-primary/10 text-primary">
                    {contacts.find(c => c.phoneNumber === selectedContact)?.name?.[0]?.toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="text-base">
                    {contacts.find(c => c.phoneNumber === selectedContact)?.name || formatPhoneNumber(selectedContact)}
                  </CardTitle>
                  <CardDescription className="text-xs">
                    {formatPhoneNumber(selectedContact)}
                  </CardDescription>
                </div>
                <div className="ml-auto flex space-x-2">
                  <Button variant="ghost" size="icon">
                    <Phone className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ) : (
              <CardTitle className="text-base">Select a conversation</CardTitle>
            )}
          </CardHeader>
          <CardContent className="flex-grow flex flex-col p-0">
            {selectedContact ? (
              <>
                <ScrollArea className="flex-grow px-4 py-3 h-[calc(100vh-400px)]">
                  {loading && !conversations[selectedContact] ? (
                    <div className="space-y-4">
                      {Array(3).fill(0).map((_, i) => (
                        <div key={i} className={`flex ${i % 2 === 0 ? 'justify-start' : 'justify-end'}`}>
                          <Skeleton className={`h-[80px] w-[200px] ${i % 2 === 0 ? 'mr-auto' : 'ml-auto'} rounded-lg`} />
                        </div>
                      ))}
                    </div>
                  ) : !conversations[selectedContact] || conversations[selectedContact].length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <p>No messages yet. Start the conversation!</p>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {conversations[selectedContact].map((message) => (
                        <MessageBubble key={message.id} message={message} />
                      ))}
                      <div ref={messagesEndRef} />
                    </div>
                  )}
                </ScrollArea>
                
                <div className="p-3 border-t">
                  {phoneNumbers.length > 0 && (
                    <div className="flex items-center mb-3">
                      <Label className="text-xs mr-2">From:</Label>
                      <Select value={selectedPhoneNumber} onValueChange={setSelectedPhoneNumber}>
                        <SelectTrigger className="h-8 text-xs">
                          <SelectValue placeholder="Select a number" />
                        </SelectTrigger>
                        <SelectContent>
                          {phoneNumbers.map((number) => (
                            <SelectItem key={number.id} value={number.id}>
                              {formatPhoneNumber(number.number)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                  
                  <form 
                    className="flex space-x-2"
                    onSubmit={(e) => {
                      e.preventDefault();
                      handleSendMessage();
                    }}
                  >
                    <Input
                      placeholder="Type a message..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      disabled={!selectedPhoneNumber || sendingMessage}
                    />
                    <Button 
                      type="submit" 
                      size="icon" 
                      disabled={!newMessage.trim() || !selectedPhoneNumber || sendingMessage}
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </form>
                </div>
              </>
            ) : (
              <EmptyConversation />
            )}
          </CardContent>
        </Card>
      </div>
      
      {/* New Message Dialog */}
      <NewMessageDialog
        open={newMessageDialogOpen}
        onOpenChange={setNewMessageDialogOpen}
        phoneNumbers={phoneNumbers}
        onSend={handleNewMessageSend}
      />
    </motion.div>
  );
}
