import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions, QueryKey } from '@tanstack/react-query';
import { api } from './apiClient';
import { useAuthStore } from '../stores/authStore';
import { useNumberStore } from '../stores/numberStore';
import { useDashboardStore } from '../stores/dashboardStore';
import toast from 'react-hot-toast';

// Type for API error responses
export interface ApiError {
  message: string;
  code?: string;
  statusCode?: number;
  details?: any;
}

// Define query keys for consistent cache management
export const queryKeys = {
  auth: {
    me: ['auth', 'me'],
    session: ['auth', 'session'],
  },
  numbers: {
    all: ['numbers', 'all'],
    available: ['numbers', 'available'],
    owned: ['numbers', 'owned'],
    details: (id: string) => ['numbers', 'details', id],
  },
  dashboard: {
    summary: ['dashboard', 'summary'],
    recentActivity: ['dashboard', 'recentActivity'],
    aiInsights: ['dashboard', 'aiInsights'],
  },
  callLogs: {
    all: ['call-logs', 'all'],
    details: (id: string) => ['call-logs', 'details', id],
  },
  automation: {
    config: (numberId: string) => ['automation', 'config', numberId],
    aiAssistant: (numberId: string) => ['automation', 'aiAssistant', numberId],
    logs: (numberId: string) => ['automation', 'logs', numberId],
  },
  billing: {
    balance: ['billing', 'balance'],
    transactions: ['billing', 'transactions'],
    subscription: ['billing', 'subscription'],
  },
  notifications: {
    all: ['notifications', 'all'],
    unreadCount: ['notifications', 'unreadCount'],
  },
};

// Error handler for API calls
const handleApiError = (error: any): ApiError => {
  // Extract error from the API response
  const apiError: ApiError = {
    message: 'An unexpected error occurred',
  };

  if (error.response) {
    // The server responded with an error status
    const { status, data } = error.response;
    apiError.statusCode = status;
    
    if (data?.message) {
      apiError.message = data.message;
    } else if (status === 401) {
      apiError.message = 'Authentication required';
      apiError.code = 'UNAUTHORIZED';
    } else if (status === 403) {
      apiError.message = 'You do not have permission to perform this action';
      apiError.code = 'FORBIDDEN';
    } else if (status === 404) {
      apiError.message = 'The requested resource was not found';
      apiError.code = 'NOT_FOUND';
    } else if (status >= 500) {
      apiError.message = 'A server error occurred';
      apiError.code = 'SERVER_ERROR';
    }
    
    if (data?.details) {
      apiError.details = data.details;
    }
  } else if (error.request) {
    // Request was made but no response received (network error)
    apiError.message = 'No response from server. Please check your internet connection';
    apiError.code = 'NETWORK_ERROR';
  } else {
    // Something happened in setting up the request
    apiError.message = error.message || 'An unexpected error occurred';
  }
  
  return apiError;
};

// Generic hook for fetching data with better error handling and store integration
export function useFetchQuery<TData = any>(
  url: string,
  queryKey: QueryKey,
  options?: Omit<UseQueryOptions<TData, ApiError, TData, QueryKey>, 'queryKey' | 'queryFn'>
) {
  return useQuery<TData, ApiError>({
    queryKey,
    queryFn: async () => {
      try {
        return await api.get<TData>(url);
      } catch (error) {
        throw handleApiError(error);
      }
    },
    ...options,
  });
}

// Generic hook for mutations with better error handling
export function useApiMutation<TData = any, TVariables = any>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: Omit<UseMutationOptions<TData, ApiError, TVariables>, 'mutationFn'>
) {
  return useMutation<TData, ApiError, TVariables>({
    mutationFn,
    onError: (error, variables, context) => {
      // Default error handling (show toast)
      toast.error(error.message || 'An error occurred');
      
      // Call custom error handler if provided
      if (options?.onError) {
        options.onError(error, variables, context);
      }
    },
    ...options,
  });
}

// Specialized hooks for common operations

// User profile hook
export function useUserProfile() {
  const { login, setLoading } = useAuthStore();
  
  const result = useFetchQuery<any>(
    '/users/me',
    queryKeys.auth.me,
    {
      staleTime: 30 * 60 * 1000,
      enabled: false,
      gcTime: 60 * 60 * 1000,
      // Using the new React Query v5 structured callbacks
      select: (data) => {
        login(data);
        return data;
      }
    }
  );
  
  // Handle error separately
  if (result.error) {
    setLoading(false);
  }
  
  return result;
}

// Owned numbers hook
export function useOwnedNumbers() {
  const { setCachedNumbers, setLoading, setError } = useNumberStore();
  
  const result = useFetchQuery<any[]>(
    '/numbers/owned',
    queryKeys.numbers.owned,
    {
      staleTime: 5 * 60 * 1000,
      select: (data) => {
        setCachedNumbers(data);
        setLoading(false);
        setError(null);
        return data;
      }
    }
  );
  
  // Handle error separately
  if (result.error) {
    setError(result.error.message);
    setLoading(false);
  }
  
  return result;
}

// Dashboard summary hook
export function useDashboardSummary() {
  const { setSummary, setLoading, setError } = useDashboardStore();
  
  const result = useFetchQuery<any>(
    '/dashboard/summary',
    queryKeys.dashboard.summary,
    {
      staleTime: 60 * 1000,
      select: (data) => {
        setSummary(data);
        setLoading(false);
        return data;
      }
    }
  );
  
  // Handle error separately
  if (result.error) {
    setError(result.error.message);
    setLoading(false);
  }
  
  return result;
}

// Dashboard recent activity hook
export function useRecentActivity() {
  const { setRecentActivity, setError } = useDashboardStore();
  
  const result = useFetchQuery<any[]>(
    '/dashboard/recent-activity',
    queryKeys.dashboard.recentActivity,
    {
      staleTime: 30 * 1000,
      select: (data) => {
        setRecentActivity(data);
        return data;
      }
    }
  );
  
  // Handle error separately
  if (result.error) {
    setError(result.error.message);
  }
  
  return result;
}

// Dashboard AI insights hook
export function useAIInsights() {
  const { setAIInsights, setError } = useDashboardStore();
  
  const result = useFetchQuery<any[]>(
    '/dashboard/ai-insights',
    queryKeys.dashboard.aiInsights,
    {
      staleTime: 5 * 60 * 1000,
      select: (data) => {
        setAIInsights(data);
        return data;
      }
    }
  );
  
  // Handle error separately
  if (result.error) {
    setError(result.error.message);
  }
  
  return result;
}

// Number details hook
export function useNumberDetails(numberId: string | null, options?: { enabled?: boolean }) {
  const { updateNumber, setError } = useNumberStore();
  
  const result = useFetchQuery<any>(
    `/numbers/${numberId}`,
    queryKeys.numbers.details(numberId || 'unknown'),
    {
      staleTime: 5 * 60 * 1000,
      enabled: !!numberId && (options?.enabled !== false),
      select: (data) => {
        updateNumber(data);
        return data;
      }
    }
  );
  
  // Handle error separately
  if (result.error) {
    setError(result.error.message);
  }
  
  return result;
}

// Purchase number hook
export function usePurchaseNumber() {
  const queryClient = useQueryClient();
  const { addNumber } = useNumberStore();
  
  return useApiMutation<any, any>(
    (data) => api.post('/numbers/purchase', data),
    {
      onSuccess: (data) => {
        // Add the new number to the cache
        addNumber(data);
        // Invalidate queries that might be affected
        queryClient.invalidateQueries({ queryKey: queryKeys.numbers.owned });
        queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.summary });
        // Show success message
        toast.success('Phone number purchased successfully');
      },
    }
  );
}

// Update number hook
export function useUpdateNumber() {
  const queryClient = useQueryClient();
  const { updateNumber } = useNumberStore();
  
  return useApiMutation<any, { numberId: string; data: any }>(
    ({ numberId, data }) => api.put(`/numbers/${numberId}`, data),
    {
      onSuccess: (updatedNumber) => {
        // Update the number in the cache
        updateNumber(updatedNumber);
        // Invalidate relevant queries
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.numbers.details(updatedNumber.id) 
        });
        // Show success message
        toast.success('Phone number updated successfully');
      },
    }
  );
}

// Release number hook
export function useReleaseNumber() {
  const queryClient = useQueryClient();
  const { removeNumber } = useNumberStore();
  
  return useApiMutation<any, string>(
    (numberId) => api.delete(`/numbers/${numberId}`),
    {
      onSuccess: (_, numberId) => {
        // Remove the number from the cache
        removeNumber(numberId);
        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: queryKeys.numbers.owned });
        queryClient.invalidateQueries({ queryKey: queryKeys.dashboard.summary });
        // Show success message
        toast.success('Phone number released successfully');
      },
    }
  );
}

// Credit balance hook
export function useCreditBalance() {
  return useFetchQuery<any>(
    '/billing/balance',
    queryKeys.billing.balance,
    {
      // Refresh every minute
      staleTime: 60 * 1000,
    }
  );
}

// Notifications unread count hook
export function useUnreadNotificationsCount() {
  const { setUnreadNotificationsCount } = useUIStore();
  
  return useFetchQuery<{ count: number }>(
    '/notifications/unread-count',
    queryKeys.notifications.unreadCount,
    {
      staleTime: 30 * 1000,
      select: (data) => {
        setUnreadNotificationsCount(data.count);
        return data;
      }
    }
  );
}

// Import UIStore for the notification count
import { useUIStore } from '../stores/uiStore';
// Import AutomationStore
import { useAutomationStore } from '../stores/automationStore';

// ====== Automation Hooks ======

// Fetch automation rules for a phone number
export function useAutomationRules(phoneNumberId: string | null) {
  const { setRules, setLoading, setError } = useAutomationStore();
  
  const result = useFetchQuery<any[]>(
    `/numbers/${phoneNumberId}/automations`,
    queryKeys.automation.config(phoneNumberId || 'unknown'),
    {
      staleTime: 5 * 60 * 1000,
      enabled: !!phoneNumberId,
      select: (data) => {
        setRules(phoneNumberId!, data);
        return data;
      }
    }
  );
  
  // Handle error separately
  if (result.error) {
    setError(result.error.message);
  }
  
  return result;
}

// Fetch AI assistant config for a phone number
export function useAIAssistant(phoneNumberId: string | null) {
  const { setAIAssistant, setError } = useAutomationStore();
  
  const result = useFetchQuery<any>(
    `/numbers/${phoneNumberId}/ai-assistant`,
    queryKeys.automation.aiAssistant(phoneNumberId || 'unknown'),
    {
      staleTime: 5 * 60 * 1000,
      enabled: !!phoneNumberId,
      select: (data) => {
        setAIAssistant(phoneNumberId!, data);
        return data;
      }
    }
  );
  
  // Handle error separately
  if (result.error) {
    setError(result.error.message);
  }
  
  return result;
}

// Fetch automation logs for a phone number
export function useAutomationLogs(phoneNumberId: string | null) {
  const { setLogs, setError } = useAutomationStore();
  
  const result = useFetchQuery<any[]>(
    `/numbers/${phoneNumberId}/automation-logs`,
    queryKeys.automation.logs(phoneNumberId || 'unknown'),
    {
      staleTime: 60 * 1000, // Update frequently
      enabled: !!phoneNumberId,
      select: (data) => {
        setLogs(phoneNumberId!, data);
        return data;
      }
    }
  );
  
  // Handle error separately
  if (result.error) {
    setError(result.error.message);
  }
  
  return result;
}

// Update automation rules
export function useUpdateAutomationRules() {
  const queryClient = useQueryClient();
  const { updateRule } = useAutomationStore();
  
  return useApiMutation<any, { phoneNumberId: string; rules: any[] }>(
    ({ phoneNumberId, rules }) => api.put(`/numbers/${phoneNumberId}/automations`, rules),
    {
      onSuccess: (data, variables) => {
        // Update rules in store
        data.forEach((rule: any) => {
          updateRule(rule);
        });
        
        // Invalidate relevant queries
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.automation.config(variables.phoneNumberId) 
        });
        
        // Show success message
        toast.success('Automation rules updated successfully');
      },
    }
  );
}

// Update AI assistant config
export function useUpdateAIAssistant() {
  const queryClient = useQueryClient();
  const { updateAIAssistant } = useAutomationStore();
  
  return useApiMutation<any, { phoneNumberId: string; config: any }>(
    ({ phoneNumberId, config }) => api.put(`/numbers/${phoneNumberId}/ai-assistant`, config),
    {
      onSuccess: (data, variables) => {
        // Update AI assistant config in store
        updateAIAssistant(data);
        
        // Invalidate relevant queries
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.automation.aiAssistant(variables.phoneNumberId) 
        });
        
        // Show success message
        toast.success('AI assistant configuration updated successfully');
      },
    }
  );
}

// Toggle automation rule active state
export function useToggleAutomation() {
  const queryClient = useQueryClient();
  const { toggleRuleActive } = useAutomationStore();
  
  return useApiMutation<any, { phoneNumberId: string; ruleId: string; isActive: boolean }>(
    ({ phoneNumberId, ruleId, isActive }) => 
      api.post(`/numbers/${phoneNumberId}/automations/toggle`, { ruleId, isActive }),
    {
      onSuccess: (_, variables) => {
        const { phoneNumberId, ruleId, isActive } = variables;
        
        // Update rule in store
        toggleRuleActive(ruleId, phoneNumberId, isActive);
        
        // Invalidate relevant queries
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.automation.config(phoneNumberId) 
        });
        
        // Show success message
        toast.success(`Automation rule ${isActive ? 'enabled' : 'disabled'} successfully`);
      },
    }
  );
}
