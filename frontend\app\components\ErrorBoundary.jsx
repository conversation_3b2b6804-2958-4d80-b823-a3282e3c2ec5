"use client";

import { Component } from 'react';

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // You can log the error to an error reporting service
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      errorInfo: errorInfo
    });
    
    // Log to analytics or monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      // Example: logErrorToService(error, errorInfo);
    }
  }

  render() {
    const { hasError, error } = this.state;
    const { fallback, children, silent = false } = this.props;

    if (hasError) {
      // If a fallback is provided, use it
      if (fallback) {
        return fallback;
      }
      
      // If silent is true, don't show any error UI
      if (silent) {
        return null;
      }

      // Default fallback UI
      return (
        <div className="error-boundary-fallback" style={{ padding: '20px' }}>
          <h2>Something went wrong.</h2>
          <p>{error?.message || 'Unknown error'}</p>
          {process.env.NODE_ENV !== 'production' && (
            <details style={{ whiteSpace: 'pre-wrap' }}>
              <summary>Developer Details</summary>
              <p>{error?.toString()}</p>
              <p>{this.state.errorInfo?.componentStack || 'No component stack available'}</p>
            </details>
          )}
        </div>
      );
    }

    // If there's no error, render children normally
    return children;
  }
}

export default ErrorBoundary; 