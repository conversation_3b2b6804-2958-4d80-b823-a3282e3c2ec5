'use client';

import { useState } from 'react';
import { useScheduledAutomation } from '../../hooks/useScheduledAutomation';
import ExecutionHistoryTable from './analytics/ExecutionHistoryTable';
import PerformanceChart from './analytics/PerformanceChart';
import LoadingSpinner from '../shared/LoadingSpinner';
import ErrorMessage from '../shared/ErrorMessage';

export default function AutomationAnalytics() {
  const [selectedAutomationId, setSelectedAutomationId] = useState<string | undefined>(undefined);
  const [timeRange, setTimeRange] = useState<'day' | 'week' | 'month'>('week');
  
  const { automationsQuery, getAutomationStats } = useScheduledAutomation();
  const { data: automations, isLoading: isLoadingAutomations } = automationsQuery;
  
  const { 
    data: stats, 
    isLoading: isLoadingStats, 
    error: statsError 
  } = getAutomationStats({ 
    automationId: selectedAutomationId, 
    timeRange 
  });

  return (
    <div className="space-y-6">
      {/* Filter controls */}
      <div className="flex flex-col md:flex-row justify-between space-y-4 md:space-y-0">
        {/* Automation selector */}
        <div>
          <label htmlFor="automation-select" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Select Automation
          </label>
          <select
            id="automation-select"
            value={selectedAutomationId || ''}
            onChange={(e) => setSelectedAutomationId(e.target.value || undefined)}
            className="w-full md:w-64 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            disabled={isLoadingAutomations}
          >
            <option value="">All Automations</option>
            {automations?.map((automation) => (
              <option key={automation.id} value={automation.id}>
                {automation.name}
              </option>
            ))}
          </select>
        </div>
        
        {/* Time range selector */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Time Range
          </label>
          <div className="flex space-x-2">
            <button
              onClick={() => setTimeRange('day')}
              className={`px-3 py-1 rounded-md ${
                timeRange === 'day'
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
              }`}
            >
              Day
            </button>
            <button
              onClick={() => setTimeRange('week')}
              className={`px-3 py-1 rounded-md ${
                timeRange === 'week'
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
              }`}
            >
              Week
            </button>
            <button
              onClick={() => setTimeRange('month')}
              className={`px-3 py-1 rounded-md ${
                timeRange === 'month'
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
              }`}
            >
              Month
            </button>
          </div>
        </div>
      </div>

      {/* Stats cards */}
      {isLoadingStats ? (
        <div className="flex justify-center py-8">
          <LoadingSpinner size="lg" />
        </div>
      ) : statsError ? (
        <ErrorMessage message="Failed to load automation statistics. Please try again later." />
      ) : stats ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Success rate card */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Success Rate</h3>
            <div className="mt-2 flex items-baseline">
              <p className="text-3xl font-semibold text-gray-900 dark:text-white">
                {Math.round(stats.successRate * 100)}%
              </p>
            </div>
          </div>
          
          {/* Total executions card */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Executions</h3>
            <div className="mt-2 flex items-baseline">
              <p className="text-3xl font-semibold text-gray-900 dark:text-white">
                {stats.totalExecutions}
              </p>
            </div>
          </div>
          
          {/* Error count card */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Errors</h3>
            <div className="mt-2 flex items-baseline">
              <p className="text-3xl font-semibold text-gray-900 dark:text-white">
                {stats.errorCount}
              </p>
            </div>
          </div>
        </div>
      ) : null}

      {/* Performance chart */}
      {stats && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Performance Over Time</h3>
          <PerformanceChart chartData={stats.chartData} />
        </div>
      )}

      {/* Execution history table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Execution History</h3>
        <ExecutionHistoryTable automationId={selectedAutomationId} />
      </div>
    </div>
  );
}
