"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.saveStacktrace = void 0;
const content = `--[[
  Save stacktrace and failedReason.
  Input:
    KEYS[1] job key
    ARGV[1]  stacktrace
    ARGV[2]  failedReason
  Output:
     0 - OK
    -1 - Missing key
]]
local rcall = redis.call
if rcall("EXISTS", KEYS[1]) == 1 then
  rcall("HMSET", KEYS[1], "stacktrace", ARGV[1], "failedReason", ARGV[2])
  return 0
else
  return -1
end
`;
exports.saveStacktrace = {
    name: 'saveStacktrace',
    content,
    keys: 1,
};
//# sourceMappingURL=saveStacktrace-1.js.map