---
title: Vercel Frontend Deployment Guide
description: Step-by-step instructions for deploying the CallSaver.app frontend to Vercel
date: 2025-04-29
status: Required
priority: High
---

# Vercel Frontend Deployment Guide

This guide provides detailed instructions for deploying the CallSaver.app frontend to Vercel, including environment configuration, build optimization, and deployment verification.

## Prerequisites

- Vercel account with billing set up
- GitHub account connected to Vercel
- Access to the CallSaver.app GitHub repository
- Deployed backend on Railway (see RAILWAY_DEPLOYMENT_GUIDE.md)
- Supabase project correctly configured for authentication

## Deployment Steps

### 1. Initial Project Setup

1. Log in to [Vercel Dashboard](https://vercel.com/dashboard)
2. Click **Add New** > **Project**
3. Import the `callsaver.app` repository from GitHub
4. Configure project settings:
   - **Framework Preset**: Next.js
   - **Root Directory**: `/front/mainpage`
   - **Build Command**: `npm run build`
   - **Output Directory**: `.next`
5. Click **Deploy**

### 2. Environment Variables Setup

Add the following environment variables to your Vercel project:

#### Core Configuration
```
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://callsaver.app  # Update with your actual domain
NEXT_PUBLIC_API_URL=https://api.callsaver.app  # Update with your Railway backend URL
```

#### Supabase Configuration
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

#### Feature Flags
```
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
```

### 3. Build Optimization

To optimize the build and improve performance:

1. Navigate to the **Settings** tab of your Vercel project
2. Click on **General**
3. Under **Build & Development Settings**:
   - Enable **Automatically expose System Environment Variables**
   - Set **Node.js Version** to `18.x` (or current LTS)
4. Under **Optimizations**:
   - Enable **Compression**
   - Select **Brotli** for **Image Optimization**

### 4. Domain Setup

1. Navigate to the **Domains** tab
2. Click **Add** to add your custom domain
3. Enter your domain (e.g., `callsaver.app`)
4. Configure DNS settings as instructed by Vercel
5. Wait for SSL certificate provisioning
6. Enable **Redirect www to non-www** (or vice versa based on preference)

### 5. Continuous Deployment

Vercel automatically sets up continuous deployment. When new code is pushed to the main branch, it will be automatically deployed.

To customize this behavior:

1. Navigate to the **Git** tab
2. Under **Production Branch**, ensure it's set to `main`
3. Configure **Preview Deployment** settings based on your workflow preferences

### 6. Preview Deployments

Vercel creates preview deployments for every pull request. To customize this:

1. Navigate to the **Git** tab
2. Under **Preview Deployment**: 
   - Configure scope to determine which branches generate previews
   - Set preview comments to be added to pull requests

### 7. Analytics Setup

1. Navigate to the **Analytics** tab
2. Click **Enable Analytics**
3. Configure Web Vitals monitoring
4. Set up custom events tracking if needed

### 8. Verification

After deployment is complete, verify the following:

1. Visit your production domain (e.g., `https://callsaver.app`)
2. Test user authentication flow
3. Ensure API calls to the backend are working
4. Check for any console errors
5. Verify all critical user journeys:
   - Registration and login
   - Dashboard loading
   - Number purchase flow
   - AI assistant configuration
   - Call and SMS history viewing
   - Analytics dashboard

### 9. Monitoring

1. Navigate to the **Analytics** tab to view:
   - Web Vitals metrics
   - Visitor analytics
   - Error rates
2. Set up alerts for critical metrics:
   - High error rates
   - Poor performance thresholds
   - Deployment failures

## Asset Optimization

### 1. Image Optimization

1. Ensure all images are using Vercel's Image Optimization:
   - Use `next/image` component
   - Set proper `width` and `height` attributes
   - Use appropriate quality settings

2. Configure Vercel Image Optimization:
   - Navigate to **Settings** > **Image Optimization**
   - Enable the feature
   - Configure caching behavior

### 2. Font Optimization

1. Use Next.js font optimization:
   - Implement `next/font` for all custom fonts
   - Use `font-display: swap` for text visibility during font loading

2. Preload critical fonts:
   - Add appropriate `<link rel="preload">` tags
   - Focus on fonts used above the fold

### 3. Script Optimization

1. Use Next.js Script component:
   - Implement `next/script` for all third-party scripts
   - Set appropriate `strategy` attribute

2. Defer non-critical JavaScript:
   - Use `strategy="lazyOnload"` for non-essential scripts
   - Consider dynamic imports for large components

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check build logs for errors
   - Verify that all dependencies are correctly installed
   - Ensure environment variables are correctly set
   - Check for TypeScript errors or ESLint issues

2. **API Connection Issues**
   - Verify CORS settings on the backend
   - Ensure API URL environment variable is correct
   - Check for network rules that might be blocking connections

3. **Authentication Problems**
   - Verify Supabase environment variables
   - Check Supabase configuration for authentication providers
   - Ensure correct redirect URLs are set

4. **Performance Issues**
   - Use Vercel Analytics to identify bottlenecks
   - Check for large bundle sizes and optimize code splitting
   - Improve image and font loading strategies

## Manual Deployment

If you need to deploy manually rather than using GitHub integration:

1. Install the Vercel CLI:
   ```bash
   npm install -g vercel
   ```

2. Log in to Vercel:
   ```bash
   vercel login
   ```

3. Navigate to your project directory:
   ```bash
   cd front/mainpage
   ```

4. Deploy your application:
   ```bash
   vercel --prod
   ```

## Rollback Procedure

If a deployment causes issues:

1. Navigate to the **Deployments** tab
2. Find the last working deployment
3. Click the three dots menu (⋮) next to it
4. Select **Promote to Production**
5. Confirm the rollback

## Security Considerations

1. Ensure environment variables are properly configured
2. Use Content Security Policy headers to prevent XSS attacks
3. Implement proper authentication guards on all protected routes
4. Avoid exposing sensitive information in client-side code
5. Set up proper rate limiting for API requests

## Post-Deployment Checklist

- [ ] Application loads correctly at the production URL
- [ ] Authentication flow works properly
- [ ] API requests to backend succeed
- [ ] Web Vitals measurements show good performance
- [ ] No JavaScript errors in browser console
- [ ] All images and assets load correctly
- [ ] Responsive design works on various screen sizes
- [ ] Analytics is properly tracking user interactions

## Production Smoke Test Plan

Before announcing the production deployment to users, run through the following smoke test checklist:

### 1. User Authentication
- [ ] New user registration works
- [ ] Email verification process functions correctly
- [ ] User login succeeds with correct credentials
- [ ] User login fails with incorrect credentials
- [ ] Password reset flow functions correctly
- [ ] Session persistence works as expected

### 2. Dashboard Functionality
- [ ] Dashboard loads with correct user data
- [ ] Navigation between sections works
- [ ] User preferences are saved and applied

### 3. Number Management
- [ ] Number search functionality works
- [ ] Number purchase flow completes successfully
- [ ] Purchased numbers appear in the user's account
- [ ] Number configuration changes are saved

### 4. AI Assistant Configuration
- [ ] AI personality selection works
- [ ] Knowledge base document uploads succeed
- [ ] Custom commands can be created and saved
- [ ] Test conversation with AI assistant works

### 5. Call and SMS Handling
- [ ] Call logs display correctly
- [ ] Call details page shows all relevant information
- [ ] Message conversations load properly
- [ ] Message sending functionality works

### 6. Analytics
- [ ] Analytics dashboard loads with data
- [ ] Filtering and date range selection works
- [ ] Graphs and charts render correctly
- [ ] Data export functionality works

### 7. Multi-device Testing
- [ ] Desktop browsers (Chrome, Firefox, Safari, Edge)
- [ ] Mobile browsers (iOS Safari, Android Chrome)
- [ ] Different screen sizes and resolutions
