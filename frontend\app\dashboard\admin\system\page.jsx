'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../../components/ui/tabs';
import AdminPageLayout from '../../../../components/admin/AdminPageLayout';

export default function SystemConfiguration() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [systemConfig, setSystemConfig] = useState({
    general: {
      siteName: 'CallSaver',
      supportEmail: '<EMAIL>',
      maxUploadSize: 10, // MB
      defaultLanguage: 'en-US',
      maintenanceMode: false
    },
    twilio: {
      accountSid: 'AC*********************',
      authToken: '************************',
      defaultFromNumber: '+1**********',
      callbackUrl: 'https://api.callsaver.app/webhooks/twilio',
      messagingServiceSid: 'MG********************'
    },
    email: {
      provider: 'SendGrid',
      apiKey: '************************',
      fromEmail: '<EMAIL>',
      fromName: 'CallSaver',
      templateIds: {
        welcome: 'd-*********************',
        passwordReset: 'd-*********************',
        verifyEmail: 'd-*********************'
      }
    },
    storage: {
      provider: 'AWS S3',
      bucketName: 'callsaver-production',
      region: 'us-west-2',
      accessKeyId: 'AK*********************',
      secretAccessKey: '************************'
    }
  });
  const [systemStatus, setSystemStatus] = useState({
    services: [
      { name: 'API Server', status: 'operational', uptime: '99.98%' },
      { name: 'Database', status: 'operational', uptime: '99.99%' },
      { name: 'Storage', status: 'operational', uptime: '100%' },
      { name: 'Task Queue', status: 'operational', uptime: '99.95%' },
      { name: 'Twilio Integration', status: 'operational', uptime: '99.9%' },
      { name: 'Email Service', status: 'operational', uptime: '99.97%' }
    ],
    lastIncident: '2023-04-15T08:30:00Z',
    currentAlerts: []
  });

  useEffect(() => {
    const fetchSystemData = async () => {
      try {
        setIsLoading(true);
        // In a real implementation, this would be an API call to get system configuration
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data is already set in state
        
      } catch (err) {
        console.error('Error fetching system data:', err);
        setError('Failed to load system configuration. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSystemData();
  }, []);

  const handleSaveConfig = async (section) => {
    try {
      setIsLoading(true);
      // In a real implementation, this would be an API call to save the configuration
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert(`${section} settings saved successfully!`);
    } catch (err) {
      console.error(`Error saving ${section} settings:`, err);
      setError(`Failed to save ${section} settings. Please try again later.`);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <AdminPageLayout
      title="System Configuration"
      description="Configure system settings and view service status."
    >
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
          <p className="text-red-200">{error}</p>
        </div>
      )}

      <Tabs defaultValue="general" className="w-full">
        <TabsList className="bg-gray-800/70 border border-purple-500/20 mb-6">
          <TabsTrigger value="general">General Settings</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
          <TabsTrigger value="email">Email Settings</TabsTrigger>
          <TabsTrigger value="storage">Storage Settings</TabsTrigger>
          <TabsTrigger value="status">System Status</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">General Settings</h2>
          
          <div className="space-y-6">
            <div>
              <label className="text-gray-300 block mb-1">Site Name</label>
              <input 
                type="text" 
                value={systemConfig.general.siteName} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  general: {
                    ...systemConfig.general,
                    siteName: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Support Email</label>
              <input 
                type="email" 
                value={systemConfig.general.supportEmail} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  general: {
                    ...systemConfig.general,
                    supportEmail: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Max Upload Size (MB)</label>
              <input 
                type="number" 
                value={systemConfig.general.maxUploadSize} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  general: {
                    ...systemConfig.general,
                    maxUploadSize: parseInt(e.target.value)
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Default Language</label>
              <select 
                value={systemConfig.general.defaultLanguage} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  general: {
                    ...systemConfig.general,
                    defaultLanguage: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              >
                <option value="en-US">English (US)</option>
                <option value="en-GB">English (UK)</option>
                <option value="es-ES">Spanish</option>
                <option value="fr-FR">French</option>
                <option value="de-DE">German</option>
              </select>
            </div>
            
            <div className="flex items-center">
              <input 
                type="checkbox" 
                id="maintenanceMode" 
                checked={systemConfig.general.maintenanceMode} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  general: {
                    ...systemConfig.general,
                    maintenanceMode: e.target.checked
                  }
                })}
                className="mr-2 h-4 w-4"
                disabled={isLoading}
              />
              <label htmlFor="maintenanceMode" className="text-gray-300">Maintenance Mode</label>
            </div>
            
            <div className="flex justify-end">
              <button 
                onClick={() => handleSaveConfig('General')}
                className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
                disabled={isLoading}
              >
                Save General Settings
              </button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="integrations" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">Twilio Integration</h2>
          
          <div className="space-y-6">
            <div>
              <label className="text-gray-300 block mb-1">Account SID</label>
              <input 
                type="text" 
                value={systemConfig.twilio.accountSid} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  twilio: {
                    ...systemConfig.twilio,
                    accountSid: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Auth Token</label>
              <input 
                type="password" 
                value={systemConfig.twilio.authToken} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  twilio: {
                    ...systemConfig.twilio,
                    authToken: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Default From Number</label>
              <input 
                type="text" 
                value={systemConfig.twilio.defaultFromNumber} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  twilio: {
                    ...systemConfig.twilio,
                    defaultFromNumber: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Callback URL</label>
              <input 
                type="text" 
                value={systemConfig.twilio.callbackUrl} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  twilio: {
                    ...systemConfig.twilio,
                    callbackUrl: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Messaging Service SID</label>
              <input 
                type="text" 
                value={systemConfig.twilio.messagingServiceSid} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  twilio: {
                    ...systemConfig.twilio,
                    messagingServiceSid: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div className="flex justify-end">
              <button 
                onClick={() => handleSaveConfig('Twilio')}
                className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
                disabled={isLoading}
              >
                Save Twilio Settings
              </button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="email" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">Email Settings</h2>
          
          <div className="space-y-6">
            <div>
              <label className="text-gray-300 block mb-1">Email Provider</label>
              <select 
                value={systemConfig.email.provider} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  email: {
                    ...systemConfig.email,
                    provider: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              >
                <option value="SendGrid">SendGrid</option>
                <option value="Mailgun">Mailgun</option>
                <option value="AWS SES">AWS SES</option>
                <option value="SMTP">Custom SMTP</option>
              </select>
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">API Key</label>
              <input 
                type="password" 
                value={systemConfig.email.apiKey} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  email: {
                    ...systemConfig.email,
                    apiKey: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">From Email</label>
              <input 
                type="email" 
                value={systemConfig.email.fromEmail} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  email: {
                    ...systemConfig.email,
                    fromEmail: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">From Name</label>
              <input 
                type="text" 
                value={systemConfig.email.fromName} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  email: {
                    ...systemConfig.email,
                    fromName: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <h3 className="text-lg font-semibold text-white mt-6 mb-2">Email Templates</h3>
            
            <div>
              <label className="text-gray-300 block mb-1">Welcome Email Template ID</label>
              <input 
                type="text" 
                value={systemConfig.email.templateIds.welcome} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  email: {
                    ...systemConfig.email,
                    templateIds: {
                      ...systemConfig.email.templateIds,
                      welcome: e.target.value
                    }
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Password Reset Template ID</label>
              <input 
                type="text" 
                value={systemConfig.email.templateIds.passwordReset} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  email: {
                    ...systemConfig.email,
                    templateIds: {
                      ...systemConfig.email.templateIds,
                      passwordReset: e.target.value
                    }
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Email Verification Template ID</label>
              <input 
                type="text" 
                value={systemConfig.email.templateIds.verifyEmail} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  email: {
                    ...systemConfig.email,
                    templateIds: {
                      ...systemConfig.email.templateIds,
                      verifyEmail: e.target.value
                    }
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div className="flex justify-end">
              <button 
                onClick={() => handleSaveConfig('Email')}
                className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
                disabled={isLoading}
              >
                Save Email Settings
              </button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="storage" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">Storage Settings</h2>
          
          <div className="space-y-6">
            <div>
              <label className="text-gray-300 block mb-1">Storage Provider</label>
              <select 
                value={systemConfig.storage.provider} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  storage: {
                    ...systemConfig.storage,
                    provider: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              >
                <option value="AWS S3">AWS S3</option>
                <option value="Google Cloud Storage">Google Cloud Storage</option>
                <option value="Azure Blob Storage">Azure Blob Storage</option>
                <option value="Local">Local Storage</option>
              </select>
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Bucket Name</label>
              <input 
                type="text" 
                value={systemConfig.storage.bucketName} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  storage: {
                    ...systemConfig.storage,
                    bucketName: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Region</label>
              <input 
                type="text" 
                value={systemConfig.storage.region} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  storage: {
                    ...systemConfig.storage,
                    region: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Access Key ID</label>
              <input 
                type="text" 
                value={systemConfig.storage.accessKeyId} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  storage: {
                    ...systemConfig.storage,
                    accessKeyId: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Secret Access Key</label>
              <input 
                type="password" 
                value={systemConfig.storage.secretAccessKey} 
                onChange={(e) => setSystemConfig({
                  ...systemConfig,
                  storage: {
                    ...systemConfig.storage,
                    secretAccessKey: e.target.value
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div className="flex justify-end">
              <button 
                onClick={() => handleSaveConfig('Storage')}
                className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
                disabled={isLoading}
              >
                Save Storage Settings
              </button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="status" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-white">System Status</h2>
            <button 
              className="bg-purple-600 hover:bg-purple-700 text-white py-1 px-3 rounded-lg text-sm transition-colors"
              onClick={() => {
                // In a real implementation, this would refresh the status data
                setIsLoading(true);
                setTimeout(() => setIsLoading(false), 1000);
              }}
              disabled={isLoading}
            >
              Refresh Status
            </button>
          </div>
          
          <Card className="bg-gray-700/50 border border-gray-600/50 mb-6">
            <CardHeader>
              <CardTitle className="text-white">Service Status</CardTitle>
              <CardDescription className="text-gray-400">Current status of all system services</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-left">
                  <thead className="bg-gray-700/50 text-gray-300">
                    <tr>
                      <th className="px-4 py-2 rounded-tl-lg">Service</th>
                      <th className="px-4 py-2">Status</th>
                      <th className="px-4 py-2 rounded-tr-lg">Uptime</th>
                    </tr>
                  </thead>
                  <tbody className="text-gray-300">
                    {isLoading ? (
                      <tr>
                        <td colSpan="3" className="px-4 py-2 text-center">Loading status...</td>
                      </tr>
                    ) : (
                      systemStatus.services.map((service, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-gray-700/30' : 'bg-gray-700/10'}>
                          <td className="px-4 py-2 font-medium">{service.name}</td>
                          <td className="px-4 py-2">
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              service.status === 'operational' ? 'bg-green-500/20 text-green-300' :
                              service.status === 'degraded' ? 'bg-yellow-500/20 text-yellow-300' :
                              'bg-red-500/20 text-red-300'
                            }`}>
                              {service.status.charAt(0).toUpperCase() + service.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-4 py-2">{service.uptime}</td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Last Incident</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300">
                  {systemStatus.lastIncident ? formatDate(systemStatus.lastIncident) : 'No recent incidents'}
                </p>
              </CardContent>
            </Card>
            
            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Current Alerts</CardTitle>
              </CardHeader>
              <CardContent>
                {systemStatus.currentAlerts.length === 0 ? (
                  <p className="text-gray-300">No active alerts</p>
                ) : (
                  <ul className="space-y-2">
                    {systemStatus.currentAlerts.map((alert, index) => (
                      <li key={index} className="text-gray-300">{alert}</li>
                    ))}
                  </ul>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </AdminPageLayout>
  );
}
