# Developer Workflow & Infrastructure Tasks

## Task Queue

### Task 1
- **Task description:** Set up pre-commit hooks (e.g., using Husky and lint-staged) for automated linting, type checking, and basic security scans.
- **Priority:** Medium
- **Target file/component:** `.husky/`, `.lintstagedrc`, `package.json`
- **Dependencies:** Lin<PERSON> (ESLint), Type checker (TypeScript), Security scanner (e.g., Semgrep - optional) configured.
- **Status:** TODO
- **Tags:** #dev_workflow #git #hooks #husky #linting #type_checking #security #automation

### Task 2
- **Task description:** Implement a CI pipeline (e.g., GitHub Actions) to run tests, linting, and type checks on every push/PR.
- **Priority:** High
- **Target file/component:** `.github/workflows/ci.yml` (or equivalent CI config file)
- **Dependencies:** Testing Task 1, Testing Task 2, Testing Task 3 (Ensure tests exist to be run)
- **Status:** TODO
- **Tags:** #dev_workflow #ci #cd #github_actions #testing #linting #type_checking #automation

### Task 3
- **Task description:** Centralize and type environment variables and configurations using a dedicated config module/library.
- **Priority:** Medium
- **Target file/component:** `back/backend/config/index.ts` (or equivalent central config)
- **Dependencies:** None
- **Status:** TODO
- **Tags:** #dev_workflow #config #environment_variables #typescript #type_safety #refactor

### Task 4
- **Task description:** Create and maintain an `architecture_diagram.mdc` (or similar) documenting key architectural decisions and flows.
- **Priority:** Low
- **Target file/component:** `docs/architecture/architecture_diagram.mdc`
- **Dependencies:** None
- **Status:** TODO
- **Tags:** #dev_workflow #documentation #architecture #mdc
