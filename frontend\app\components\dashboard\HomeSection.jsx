"use client";

import React, { useState, useEffect } from 'react';
import { 
  PhoneIcon, 
  GlobeAltIcon, 
  ShoppingCartIcon, 
  ArrowPathIcon, 
  CheckCircleIcon 
} from '@heroicons/react/24/outline';
import { usePhoneNumbers } from '@/app/hooks/usePhoneNumbers';

export function HomeSection() {
  const { 
    availableNumbers, 
    userNumbers, 
    selectedCountry, 
    supportedCountries, 
    isLoading, 
    selectedNumber,
    fetchAvailableNumbers, 
    fetchUserNumbers, 
    purchaseNumber, 
    purchaseRandomNumber, 
    changeCountry,
    selectNumber
  } = usePhoneNumbers();

  const [view, setView] = useState('welcome'); // welcome, random, choose, success
  const [purchasedNumber, setPurchasedNumber] = useState(null);
  const [connectionError, setConnectionError] = useState(false);

  // Fetch user numbers on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        await fetchUserNumbers();
        setConnectionError(false);
      } catch (error) {
        console.error('Failed to connect to backend:', error);
        setConnectionError(true);
      }
    };
    
    fetchData();
  }, [fetchUserNumbers]);

  // Handle country selection
  const handleCountryChange = (e) => {
    changeCountry(e.target.value);
  };

  // Start random number flow
  const startRandomNumberFlow = () => {
    setView('random');
  };

  // Start choose number flow
  const startChooseNumberFlow = async () => {
    setView('choose');
    try {
      await fetchAvailableNumbers();
      setConnectionError(false);
    } catch (error) {
      console.error('Failed to fetch available numbers:', error);
      setConnectionError(true);
    }
  };

  // Purchase a random number
  const handlePurchaseRandom = async () => {
    try {
      const result = await purchaseRandomNumber();
      if (result) {
        setPurchasedNumber(result);
        setView('success');
      }
    } catch (error) {
      console.error('Failed to purchase random number:', error);
      setConnectionError(true);
    }
  };

  // Purchase selected number
  const handlePurchaseSelected = async () => {
    if (!selectedNumber) return;
    
    try {
      const result = await purchaseNumber(selectedNumber.phoneNumber);
      if (result) {
        setPurchasedNumber(result);
        setView('success');
      }
    } catch (error) {
      console.error('Failed to purchase selected number:', error);
      setConnectionError(true);
    }
  };

  // Handle number selection
  const handleSelectNumber = (number) => {
    selectNumber(number);
  };

  // Reset flow
  const resetFlow = () => {
    setView('welcome');
    setPurchasedNumber(null);
    selectNumber(null);
  };

  // If there's a connection error, show a helpful message
  if (connectionError) {
    return (
      <div className="flex-1 p-6 bg-gray-900/60">
        <div className="max-w-5xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-white mb-2">Home</h1>
            <p className="text-gray-400">Set up your phone number</p>
          </div>
          
          <div className="bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-700 p-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-white mb-2">Connection Error</h2>
              <p className="text-gray-400 mb-6">
                Unable to connect to the backend API server. Please make sure the backend server is running.
              </p>
              <div className="bg-gray-750/50 border border-gray-700 rounded-lg p-4 mb-6 max-w-md mx-auto text-left">
                <h3 className="text-white font-medium mb-2">Troubleshooting Steps:</h3>
                <ol className="list-decimal list-inside text-gray-400 space-y-2">
                  <li>Ensure the backend server is running at <code className="text-amber-400">http://localhost:3005</code></li>
                  <li>Check your network connection</li>
                  <li>Verify that CORS is properly configured on the backend</li>
                  <li>Try refreshing the page</li>
                </ol>
              </div>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition-colors duration-200"
              >
                Retry Connection
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-6 bg-gray-900/60">
      <div className="max-w-5xl mx-auto">
        {/* Top Section - Always Visible */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-white mb-2">Home</h1>
          <p className="text-gray-400">Set up your phone number</p>
        </div>
        
        {/* Phone Numbers Management Section */}
        <div className="bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-700">
          {/* Welcome View */}
          {view === 'welcome' && (
            <div className="p-6">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-indigo-600/20 rounded-lg flex items-center justify-center mr-4">
                  <PhoneIcon className="h-6 w-6 text-indigo-400" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">Get Started with Callsaver</h2>
                  <p className="text-gray-400 mt-1">
                    Use your available credit to buy a phone number and activate your Callsaver system. 
                    You can forward your calls to this number and let the AI handle the rest.
                  </p>
                </div>
              </div>
              
              {userNumbers.length > 0 ? (
                <>
                  <div className="mb-6">
                    <h3 className="text-white font-medium mb-3">Your Phone Numbers</h3>
                    <div className="bg-gray-850 rounded-lg p-4 border border-gray-700">
                      {userNumbers.map((number, index) => (
                        <div 
                          key={index} 
                          className="flex items-center justify-between p-3 rounded-lg mb-2 border border-gray-700 hover:border-indigo-500/30 bg-gray-800/50 transition-all duration-200"
                        >
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-indigo-600/20 rounded-full flex items-center justify-center mr-3">
                              <PhoneIcon className="h-5 w-5 text-indigo-400" />
                            </div>
                            <div>
                              <p className="text-white font-medium">{number.number}</p>
                              <p className="text-xs text-gray-400">{number.country} · {number.type}</p>
                            </div>
                          </div>
                          <div className="flex items-center">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-900/30 text-green-400 border border-green-700/30">
                              <CheckCircleIcon className="h-3 w-3 mr-1" />
                              Active
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-white font-medium mb-3">Add Another Number</h3>
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                      <button
                        onClick={startRandomNumberFlow}
                        className="p-4 rounded-lg bg-gray-750 hover:bg-gray-700 border border-gray-700 hover:border-indigo-500/30 transition-all duration-200"
                      >
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-indigo-600/20 rounded-full flex items-center justify-center mr-3">
                            <ArrowPathIcon className="h-5 w-5 text-indigo-400" />
                          </div>
                          <div className="text-left">
                            <p className="text-white font-medium">Buy a Random Number</p>
                            <p className="text-xs text-gray-400">Quick setup with an automatically selected number</p>
                          </div>
                        </div>
                      </button>
                      
                      <button
                        onClick={startChooseNumberFlow}
                        className="p-4 rounded-lg bg-gray-750 hover:bg-gray-700 border border-gray-700 hover:border-indigo-500/30 transition-all duration-200"
                      >
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-indigo-600/20 rounded-full flex items-center justify-center mr-3">
                            <ShoppingCartIcon className="h-5 w-5 text-indigo-400" />
                          </div>
                          <div className="text-left">
                            <p className="text-white font-medium">Choose My Number</p>
                            <p className="text-xs text-gray-400">Browse and select from available phone numbers</p>
                          </div>
                        </div>
                      </button>
                    </div>
                  </div>
                </>
              ) : (
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <button
                    onClick={startRandomNumberFlow}
                    className="p-4 rounded-lg bg-gray-750 hover:bg-gray-700 border border-gray-700 hover:border-indigo-500/30 transition-all duration-200"
                  >
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-indigo-600/20 rounded-full flex items-center justify-center mr-3">
                        <ArrowPathIcon className="h-5 w-5 text-indigo-400" />
                      </div>
                      <div className="text-left">
                        <p className="text-white font-medium">Buy a Random Number</p>
                        <p className="text-xs text-gray-400">Quick setup with an automatically selected number</p>
                      </div>
                    </div>
                  </button>
                  
                  <button
                    onClick={startChooseNumberFlow}
                    className="p-4 rounded-lg bg-gray-750 hover:bg-gray-700 border border-gray-700 hover:border-indigo-500/30 transition-all duration-200"
                  >
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-indigo-600/20 rounded-full flex items-center justify-center mr-3">
                        <ShoppingCartIcon className="h-5 w-5 text-indigo-400" />
                      </div>
                      <div className="text-left">
                        <p className="text-white font-medium">Choose My Number</p>
                        <p className="text-xs text-gray-400">Browse and select from available phone numbers</p>
                      </div>
                    </div>
                  </button>
                </div>
              )}
            </div>
          )}
          
          {/* Random Number View */}
          {view === 'random' && (
            <div className="p-6">
              <div className="flex items-center mb-6">
                <button 
                  onClick={resetFlow}
                  className="mr-3 text-indigo-400 hover:text-indigo-300 transition-colors duration-200"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                  </svg>
                </button>
                <div>
                  <h2 className="text-xl font-bold text-white">Buy a Random Number</h2>
                  <p className="text-gray-400 mt-1">
                    Select a country and we&apos;ll assign you a number automatically.
                  </p>
                </div>
              </div>
              
              <div className="mb-6">
                <label htmlFor="country" className="block text-sm font-medium text-gray-400 mb-2">
                  Select Country
                </label>
                <div className="relative">
                  <select
                    id="country"
                    name="country"
                    value={selectedCountry}
                    onChange={handleCountryChange}
                    className="block w-full bg-gray-750 border border-gray-700 rounded-lg py-3 px-4 pr-8 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    {supportedCountries.map((country) => (
                      <option key={country.code} value={country.code}>
                        {country.flag} {country.name} ({country.code})
                      </option>
                    ))}
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-750/50 border border-gray-700 rounded-lg p-4 mb-6">
                <div className="flex items-center">
                  <GlobeAltIcon className="h-5 w-5 text-indigo-400 mr-2" />
                  <p className="text-sm text-gray-400">
                    You&apos;re about to purchase a random phone number in {selectedCountry}. 
                    This will cost approximately <span className="text-white font-medium">$5</span> from your credit balance.
                  </p>
                </div>
              </div>
              
              <div className="flex justify-end">
                <button
                  onClick={handlePurchaseRandom}
                  disabled={isLoading}
                  className="px-4 py-2 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </span>
                  ) : "Purchase Random Number"}
                </button>
              </div>
            </div>
          )}
          
          {/* Choose Number View */}
          {view === 'choose' && (
            <div className="p-6">
              <div className="flex items-center mb-6">
                <button 
                  onClick={resetFlow}
                  className="mr-3 text-indigo-400 hover:text-indigo-300 transition-colors duration-200"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                  </svg>
                </button>
                <div>
                  <h2 className="text-xl font-bold text-white">Choose Your Number</h2>
                  <p className="text-gray-400 mt-1">
                    Browse and select from available phone numbers.
                  </p>
                </div>
              </div>
              
              <div className="mb-6">
                <label htmlFor="country" className="block text-sm font-medium text-gray-400 mb-2">
                  Select Country
                </label>
                <div className="relative">
                  <select
                    id="country"
                    name="country"
                    value={selectedCountry}
                    onChange={handleCountryChange}
                    className="block w-full bg-gray-750 border border-gray-700 rounded-lg py-3 px-4 pr-8 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    {supportedCountries.map((country) => (
                      <option key={country.code} value={country.code}>
                        {country.flag} {country.name} ({country.code})
                      </option>
                    ))}
                  </select>
                  <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-400">
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
              </div>
              
              {isLoading ? (
                <div className="py-12 flex justify-center">
                  <div className="flex flex-col items-center">
                    <svg className="animate-spin mb-3 h-8 w-8 text-indigo-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p className="text-indigo-400">Loading available numbers...</p>
                  </div>
                </div>
              ) : (
                <>
                  <div className="mb-6 overflow-hidden">
                    <h3 className="text-white font-medium mb-3">Available Numbers</h3>
                    
                    {availableNumbers.length === 0 ? (
                      <div className="bg-gray-750/50 border border-gray-700 rounded-lg p-6 text-center">
                        <p className="text-gray-400">No numbers available for the selected country. Try another country.</p>
                      </div>
                    ) : (
                      <div className="bg-gray-850 rounded-lg border border-gray-700 max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-transparent">
                        {availableNumbers.map((number, index) => (
                          <div 
                            key={index} 
                            onClick={() => handleSelectNumber(number)}
                            className={`flex items-center justify-between p-3 border-b border-gray-700 last:border-b-0 cursor-pointer transition-all duration-200 ${
                              selectedNumber?.phoneNumber === number.phoneNumber
                                ? 'bg-indigo-900/30 border-l-4 border-l-indigo-500'
                                : 'hover:bg-gray-800/60'
                            }`}
                          >
                            <div className="flex items-center">
                              <div className="w-10 h-10 bg-indigo-600/20 rounded-full flex items-center justify-center mr-3">
                                <PhoneIcon className="h-5 w-5 text-indigo-400" />
                              </div>
                              <div>
                                <p className="text-white font-medium">{number.phoneNumber}</p>
                                <p className="text-xs text-gray-400">{number.locality}, {number.region || number.isoCountry}</p>
                              </div>
                            </div>
                            <div className="flex items-center">
                              <span className="text-gray-400 text-sm mr-2">$5.00</span>
                              {selectedNumber?.phoneNumber === number.phoneNumber && (
                                <CheckCircleIcon className="h-5 w-5 text-indigo-400" />
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  
                  <div className="flex justify-end">
                    <button
                      onClick={handlePurchaseSelected}
                      disabled={!selectedNumber || isLoading}
                      className="px-4 py-2 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isLoading ? (
                        <span className="flex items-center">
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Processing...
                        </span>
                      ) : "Purchase Selected Number"}
                    </button>
                  </div>
                </>
              )}
            </div>
          )}
          
          {/* Success View */}
          {view === 'success' && purchasedNumber && (
            <div className="p-6">
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <CheckCircleIcon className="h-10 w-10 text-green-500" />
                </div>
                <h2 className="text-2xl font-bold text-white mb-2">Number Purchased Successfully!</h2>
                <p className="text-gray-400 mb-6">
                  Your new phone number is ready to use.
                </p>
                
                <div className="bg-gray-850 rounded-lg p-6 max-w-sm mx-auto mb-6 border border-gray-700">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-white mb-1">{purchasedNumber.number}</p>
                    <p className="text-sm text-gray-400">{purchasedNumber.country} · Active</p>
                  </div>
                </div>
                
                <p className="text-gray-400 mb-6">
                  You can now use this number to receive calls and SMS messages through Callsaver.
                </p>
                
                <button
                  onClick={resetFlow}
                  className="px-4 py-2 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition-colors duration-200"
                >
                  Return to Home
                </button>
              </div>
            </div>
          )}
        </div>
        
        {/* Credit Balance Section */}
        <div className="mt-6 bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-700 p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-green-900/20 rounded-full flex items-center justify-center mr-3">
              <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <p className="text-gray-400 text-sm">Credit Balance</p>
              <p className="text-white font-medium">$10.00</p>
            </div>
            <div className="ml-auto">
              <button className="px-3 py-1 rounded-lg bg-green-600/20 text-green-400 text-sm border border-green-600/30 hover:bg-green-600/30 transition-colors duration-200">
                Add Credits
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 