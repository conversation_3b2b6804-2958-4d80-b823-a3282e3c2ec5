import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../lib/apiClient';
import { useUIStore } from '../stores/uiStore';

// Types for Notification data
export interface Notification {
  id: string;
  type: 'missedCall' | 'newVoicemail' | 'lowCredits' | 'paymentFailed' | 'securityAlert' | 'numberExpiry' | 'aiAlert' | 'systemAnnouncement';
  title: string;
  message: string;
  link?: string;
  read: boolean;
  dismissed: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationFilters {
  type?: string | string[];
  read?: boolean;
  dismissed?: boolean;
  limit?: number;
  page?: number;
}

export interface NotificationListResponse {
  notifications: Notification[];
  totalCount: number;
  unreadCount: number;
}

/**
 * Hook for managing notifications
 */
export function useNotifications() {
  const queryClient = useQueryClient();
  const setUnreadNotificationsCount = useUIStore(state => state.setUnreadNotificationsCount);
  
  // Fetch notifications with optional filters
  const getNotifications = (filters: NotificationFilters = {}) => {
    return useQuery({
      queryKey: ['notifications', filters],
      queryFn: async () => {
        const response = await api.get<NotificationListResponse>('/notifications', filters);
        
        // Update global unread count
        if (response.unreadCount !== undefined) {
          setUnreadNotificationsCount(response.unreadCount);
        }
        
        return response;
      },
      staleTime: 60 * 1000, // 1 minute
    });
  };
  
  // Mark notification as read
  const markAsReadMutation = useMutation({
    mutationFn: (notificationId: string) => {
      return api.put<Notification>(`/notifications/${notificationId}/read`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
  
  // Mark notification as dismissed
  const dismissMutation = useMutation({
    mutationFn: (notificationId: string) => {
      return api.put<Notification>(`/notifications/${notificationId}/dismiss`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
  
  // Mark all notifications as read
  const markAllAsReadMutation = useMutation({
    mutationFn: () => {
      return api.put<{ success: boolean, count: number }>('/notifications/read-all');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      setUnreadNotificationsCount(0);
    },
  });
  
  // Mark all notifications as dismissed
  const dismissAllMutation = useMutation({
    mutationFn: () => {
      return api.put<{ success: boolean, count: number }>('/notifications/dismiss-all');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
  
  // Get notification preference settings
  const getNotificationPreferences = () => {
    return useQuery({
      queryKey: ['notification-preferences'],
      queryFn: () => api.get<{
        email: boolean;
        sms: boolean;
        push: boolean;
        inApp: boolean;
        types: Record<string, boolean>;
      }>('/notifications/preferences'),
      staleTime: 30 * 60 * 1000, // 30 minutes
    });
  };
  
  // Update notification preference settings
  const updatePreferencesMutation = useMutation({
    mutationFn: (preferences: {
      email?: boolean;
      sms?: boolean;
      push?: boolean;
      inApp?: boolean;
      types?: Record<string, boolean>;
    }) => {
      return api.put<{ success: boolean }>('/notifications/preferences', preferences);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notification-preferences'] });
    },
  });
  
  return {
    // Queries
    getNotifications,
    getNotificationPreferences,
    
    // Mutations
    markAsRead: markAsReadMutation.mutate,
    isMarkingAsRead: markAsReadMutation.isPending,
    markAsReadError: markAsReadMutation.error,
    
    dismiss: dismissMutation.mutate,
    isDismissing: dismissMutation.isPending,
    dismissError: dismissMutation.error,
    
    markAllAsRead: markAllAsReadMutation.mutate,
    isMarkingAllAsRead: markAllAsReadMutation.isPending,
    markAllAsReadError: markAllAsReadMutation.error,
    
    dismissAll: dismissAllMutation.mutate,
    isDismissingAll: dismissAllMutation.isPending,
    dismissAllError: dismissAllMutation.error,
    
    updatePreferences: updatePreferencesMutation.mutate,
    isUpdatingPreferences: updatePreferencesMutation.isPending,
    updatePreferencesError: updatePreferencesMutation.error,
    
    // Refresh
    refreshNotifications: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  };
}
