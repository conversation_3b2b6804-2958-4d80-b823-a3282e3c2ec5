"use client";

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import useWindowSize from '../hooks/useWindowSize';
import { conversationData } from '../data/conversationData';
import CarouselSlide from './CarouselSlide';

export default function ConversationCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef(null);
  const { width } = useWindowSize();
  
  // Handle manual pagination
  const paginate = (newIndex) => {
    if (newIndex === currentIndex) return;
    
    setDirection(newIndex > currentIndex ? 1 : -1);
    setCurrentIndex(newIndex);
  };
  
  // Variants for the slide animations
  const slideVariants = {
    enter: (direction) => ({
      x: direction > 0 ? width : -width,
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction) => ({
      x: direction < 0 ? width : -width,
      opacity: 0,
    }),
  };
  
  // Handle drag gestures
  const handleDragEnd = (e, { offset, velocity }) => {
    setIsDragging(false);
    
    // Threshold for dragging to change slide (20% of container width)
    const threshold = width * 0.2;
    
    if (Math.abs(offset.x) > threshold || Math.abs(velocity.x) > 0.3) {
      if (offset.x > 0 && currentIndex > 0) {
        // Dragged right, go to previous slide
        setDirection(-1);
        setCurrentIndex(currentIndex - 1);
      } else if (offset.x < 0 && currentIndex < conversationData.length - 1) {
        // Dragged left, go to next slide
        setDirection(1);
        setCurrentIndex(currentIndex + 1);
      }
    }
  };
  
  return (
    <div className="w-full py-12 md:py-24 bg-gradient-to-b from-[#0d0d17] to-[#14141f] overflow-hidden">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
            <span className="bg-clip-text bg-gradient-to-r from-purple-400 to-blue-400 text-transparent">
              AI-Powered Solutions
            </span> for Every Business
          </h2>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto">
            See how Callsaver&apos;s intelligent assistant manages conversations across industries, 
            ensuring no opportunity is missed and every customer is taken care of.
          </p>
        </div>
        
        {/* Carousel container */}
        <div 
          ref={containerRef} 
          className="relative overflow-hidden w-full"
          style={{ minHeight: '600px' }}
        >
          <AnimatePresence initial={false} custom={direction} mode="wait">
            <motion.div
              key={currentIndex}
              custom={direction}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 }
              }}
              drag="x"
              dragConstraints={{ left: 0, right: 0 }}
              dragElastic={0.3}
              onDragStart={() => setIsDragging(true)}
              onDragEnd={handleDragEnd}
              className="absolute w-full"
            >
              <CarouselSlide 
                data={conversationData[currentIndex]}
                isDragging={isDragging}
              />
            </motion.div>
          </AnimatePresence>
          
          {/* Navigation arrows */}
          <button 
            onClick={() => currentIndex > 0 && paginate(currentIndex - 1)}
            className={`absolute left-2 top-1/2 -translate-y-1/2 z-10 p-2 rounded-full bg-gray-800/50 backdrop-blur-sm text-white ${currentIndex === 0 ? 'opacity-30 cursor-not-allowed' : 'opacity-70 hover:opacity-100'}`}
            disabled={currentIndex === 0}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          
          <button 
            onClick={() => currentIndex < conversationData.length - 1 && paginate(currentIndex + 1)}
            className={`absolute right-2 top-1/2 -translate-y-1/2 z-10 p-2 rounded-full bg-gray-800/50 backdrop-blur-sm text-white ${currentIndex === conversationData.length - 1 ? 'opacity-30 cursor-not-allowed' : 'opacity-70 hover:opacity-100'}`}
            disabled={currentIndex === conversationData.length - 1}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
        
        {/* Pagination dots */}
        <div className="flex justify-center mt-8 space-x-2">
          {conversationData.map((_, index) => (
            <button
              key={index}
              onClick={() => paginate(index)}
              className={`h-2 rounded-full transition-all duration-300 ${
                index === currentIndex 
                  ? 'w-6 bg-purple-500' 
                  : 'w-2 bg-gray-600 hover:bg-gray-500'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </div>
  );
} 