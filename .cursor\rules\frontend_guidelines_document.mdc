---
description: 
globs: 
alwaysApply: false
---
---
description: Comprehensive frontend development standards and best practices for CallSaver
globs: ["front/**/*.js", "front/**/*.jsx", "front/**/*.ts", "front/**/*.tsx", "front/**/*.css", "front/**/*.scss"]
alwaysApply: true
version: 1.0.0
---

# Frontend Guidelines Document

## 1. Core Principles

### 1.1 Development Philosophy

- **User-Centric Design:** Prioritize user needs and experiences in all design decisions
- **Component-Based Architecture:** Build interfaces from reusable, composable components
- **Progressive Enhancement:** Ensure core functionality works across all supported environments
- **Performance-First Mindset:** Optimize for speed and efficiency at every level
- **Accessibility by Default:** Design and develop with accessibility as a core requirement
- **Maintainable Code:** Write clean, self-documenting code that's easy to maintain

### 1.2 Quality Standards

- **Visual Excellence:** Create polished, professional UI with attention to detail
- **Responsive Design:** Ensure perfect rendering across all device sizes
- **Interaction Design:** Implement intuitive, satisfying user interactions
- **Error Prevention:** Design to prevent user errors before they occur
- **Clear Feedback:** Provide immediate, helpful feedback for all user actions
- **Consistency:** Maintain consistent patterns, behaviors, and visual language

## 2. Project Structure & Organization

### 2.1 Directory Organization

```
front/
├── mainpage/            # Marketing site (Next.js)
│   ├── app/             # App router components
│   ├── components/      # Shared components
│   │   ├── ui/          # Base UI components
│   │   ├── layout/      # Layout components
│   │   ├── features/    # Feature-specific components
│   │   └── forms/       # Form components
│   ├── hooks/           # Custom React hooks
│   ├── lib/             # Utility functions
│   ├── styles/          # Global styles
│   └── public/          # Static assets
│
└── dashboard/           # User dashboard (Next.js)
    ├── [Same structure as mainpage]
```

### 2.2 Naming Conventions

1. **Files & Directories**
   - React Components: PascalCase (`UserProfile.tsx`)
   - Hooks: camelCase with `use` prefix (`useAuthentication.ts`)
   - Utilities: camelCase (`formatCurrency.ts`)
   - Constants: UPPER_CASE (`CONSTANTS.ts`)
   - Context files: PascalCase with `Context` suffix (`AuthContext.tsx`)

2. **Component Organization**
   - One component per file (except for small related components)
   - Component file should have the same name as the component
   - Co-locate component tests in `__tests__` directory
   - Group related components in feature directories

### 2.3 Import Order & Organization

```typescript
// 1. React and Next.js imports
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Image from 'next/image';

// 2. Third-party libraries
import { motion } from 'framer-motion';
import classNames from 'classnames';

// 3. Custom hooks
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/useToast';

// 4. Components (organized by path depth)
import Button from '@/components/ui/Button';
import { Card, CardHeader, CardContent } from '@/components/ui/Card';
import CallHistory from '@/components/features/calls/CallHistory';

// 5. Utilities and helpers
import { formatPhoneNumber } from '@/lib/formatters';
import { callService } from '@/lib/services';

// 6. Types and interfaces
import type { User, CallRecord } from '@/types';

// 7. Constants
import { API_ENDPOINTS } from '@/constants';

// 8. Styles (if not using CSS modules/Tailwind)
import styles from './ComponentName.module.css';
```

## 3. Component Development

### 3.1 Component Architecture

1. **Component Types**
   - **UI Components:** Reusable, presentational components (buttons, inputs)
   - **Layout Components:** Structure page layout (headers, sidebars)
   - **Feature Components:** Domain-specific functionality
   - **Page Components:** Top-level components for routes

2. **Component Composition**
   - Favor composition over inheritance
   - Use the children prop for flexible content inclusion
   - Implement compound components for complex UI elements
   - Create higher-order components (HOCs) sparingly

### 3.2 Component Implementation

1. **Functional Components**
   - Use functional components with hooks
   - Implement proper TypeScript interfaces for props
   - Apply destructuring for props and state

```typescript
interface UserCardProps {
  user: User;
  showDetails?: boolean;
  onSelect?: (userId: string) => void;
}

const UserCard: React.FC<UserCardProps> = ({
  user,
  showDetails = false,
  onSelect
}) => {
  const handleClick = () => {
    if (onSelect) {
      onSelect(user.id);
    }
  };

  return (
    <Card onClick={handleClick}>
      <CardHeader>
        <h3>{user.name}</h3>
      </CardHeader>
      {showDetails && (
        <CardContent>
          <p>{user.email}</p>
          <p>{user.phoneNumber}</p>
        </CardContent>
      )}
    </Card>
  );
};

export default UserCard;
```

2. **Props Design**
   - Implement sensible defaults for optional props
   - Use prop spreading sparingly and intentionally
   - Document all props with JSDoc comments
   - Group related props into single objects

### 3.3 Custom Hooks

1. **Hook Design Principles**
   - Focus on one specific concern
   - Return minimal necessary values
   - Handle loading, error, and success states
   - Implement proper cleanup in useEffect

```typescript
/**
 * Hook to fetch and manage call history for a user
 */
function useCallHistory(userId: string) {
  const [calls, setCalls] = useState<CallRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isMounted = true;
    
    async function fetchCalls() {
      try {
        setIsLoading(true);
        const response = await callService.getUserCalls(userId);
        
        if (isMounted) {
          setCalls(response.data);
          setError(null);
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error('Unknown error'));
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    }

    fetchCalls();
    
    return () => {
      isMounted = false;
    };
  }, [userId]);

  return { calls, isLoading, error };
}
```

## 4. Styling & Design System

### 4.1 Core Approach

1. **Tailwind CSS Implementation**
   - Use Tailwind utility classes as primary styling method
   - Maintain consistent spacing scales
   - Use design token variables for colors, typography, etc.
   - Leverage Tailwind's theming capabilities for dark mode

2. **Component Styling**
   - Use consistent class naming and organization
   - Apply responsive design using Tailwind breakpoints
   - Implement animations with Tailwind plugins
   - Extract repeated patterns to component classes

### 4.2 Design Tokens

1. **Color System**
   ```javascript
   // tailwind.config.js
   module.exports = {
     theme: {
       colors: {
         primary: {
           50: '#f0f9ff',
           100: '#e0f2fe',
           // ... additional shades
           600: '#0284c7',
           700: '#0369a1',
           // ... additional shades
         },
         // Secondary, accent, neutral colors...
         // Semantic colors (error, warning, success)
       }
     }
   }
   ```

2. **Typography System**
   - Define consistent type scale, font weights, and line heights
   - Implement responsive typography
   - Use semantic font family assignments

3. **Spacing & Layout**
   - Use consistent spacing units
   - Implement aspect ratios for media
   - Define standard container widths
   - Maintain consistent z-index scale

### 4.3 Component Library Integration

1. **Shadcn/ui Implementation**
   - Import and customize only required components
   - Maintain consistent theming across components
   - Extend components with additional variants as needed
   - Document custom component variations

2. **Custom Component Development**
   - Follow Shadcn/ui patterns for consistency
   - Implement proper accessibility attributes
   - Create variants for different use cases
   - Document component API and usage examples

## 5. State Management

### 5.1 State Management Hierarchy

1. **Component State**
   - Use `useState` for simple component-level state
   - Apply `useReducer` for complex state logic
   - Group related state variables

2. **Shared State**
   - React Context + useContext for shared state
   - Create domain-specific contexts
   - Implement proper context providers
   - Consider performance optimization with context splitting

3. **Complex State**
   - Use Zustand for complex global state when necessary
   - Implement selective state subscriptions
   - Create focused stores for specific domains
   - Document store API and state shape

### 5.2 Data Fetching & Management

1. **Fetching Approach**
   - SWR for data fetching, caching, and synchronization
   - Implement optimistic UI updates
   - Handle loading and error states consistently
   - Apply proper cache invalidation strategies

```typescript
// Example SWR implementation
function ProfilePage({ userId }) {
  const { data, error, isLoading, mutate } = useSWR(
    `/api/users/${userId}`,
    fetcher
  );

  if (isLoading) return <Skeleton />;
  if (error) return <ErrorDisplay error={error} />;

  return (
    <div>
      <UserProfile user={data} />
      <Button onClick={() => mutate()}>Refresh</Button>
    </div>
  );
}
```

2. **State Persistence**
   - Use appropriate storage mechanisms (localStorage, cookies)
   - Implement secure storage for sensitive data
   - Handle storage events for cross-tab synchronization
   - Create abstractions for storage operations

## 6. Forms & Validation

### 6.1 Form Implementation

1. **Form Libraries**
   - Use React Hook Form for form state management
   - Implement Zod for schema validation
   - Create reusable form components
   - Maintain consistent form layouts

2. **Form UX Best Practices**
   - Validate on blur for most fields
   - Provide immediate feedback for errors
   - Implement inline validation where appropriate
   - Use clear, specific error messages

```typescript
// Example form implementation
function UserForm({ onSubmit, defaultValues }) {
  const formSchema = z.object({
    name: z.string().min(2, 'Name must be at least 2 characters'),
    email: z.string().email('Invalid email address'),
    phone: z.string().regex(/^\+?[0-9]{10,15}$/, 'Invalid phone number')
  });

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* Additional form fields */}
        <Button type="submit">Submit</Button>
      </form>
    </Form>
  );
}
```

### 6.2 Form Accessibility

1. **Accessible Form Elements**
   - Use semantic HTML form elements
   - Associate labels with inputs
   - Implement proper aria attributes
   - Ensure keyboard navigation

2. **Error Handling**
   - Announce form errors to screen readers
   - Use color and icons to indicate errors
   - Provide clear instructions for correction
   - Implement error summaries for complex forms

## 7. Performance Optimization

### 7.1 Core Metrics & Targets

1. **Page Speed Targets**
   - First Contentful Paint (FCP): < 1.2s
   - Largest Contentful Paint (LCP): < 2.5s
   - First Input Delay (FID): < 100ms
   - Cumulative Layout Shift (CLS): < 0.1
   - Time to Interactive (TTI): < 3.0s

2. **Mobile Performance**
   - Optimize for low-end devices
   - Test on representative devices
   - Implement connection-aware loading strategies
   - Reduce JavaScript bundle size for mobile

### 7.2 Optimization Techniques

1. **Component Optimization**
   - Use `React.memo` for expensive components
   - Implement `useCallback` for function props
   - Apply `useMemo` for expensive calculations
   - Avoid unnecessary re-renders

2. **Asset Optimization**
   - Use Next.js Image component for automatic optimization
   - Implement responsive images with appropriate sizes
   - Lazy load below-the-fold images
   - Preload critical assets

3. **Bundle Optimization**
   - Leverage Next.js automatic code splitting
   - Implement dynamic imports for large components
   - Monitor bundle size with analysis tools
   - Tree-shake unused code

### 7.3 Measurement & Monitoring

1. **Performance Testing**
   - Implement automated Lighthouse testing in CI
   - Use Web Vitals for real-user monitoring
   - Perform regular performance audits
   - Document performance budgets

2. **Debugging Techniques**
   - Use React DevTools profiler
   - Implement performance marks and measures
   - Analyze render cascades
   - Document performance optimizations

## 8. Accessibility (a11y)

### 8.1 Core Requirements

1. **Standards Compliance**
   - WCAG 2.1 Level AA conformance required
   - Section 508 compliance for US regulations
   - Regular automated and manual testing

2. **Core Principles**
   - Perceivable: Information must be presentable to all users
   - Operable: UI components must be operable by all users
   - Understandable: Information and operation must be understandable
   - Robust: Content must be compatible with current and future tools

### 8.2 Implementation Guidelines

1. **Semantic HTML**
   - Use appropriate HTML elements for their intended purpose
   - Implement proper document structure
   - Use ARIA attributes only when necessary
   - Maintain focus management

2. **Keyboard Navigation**
   - Ensure all interactive elements are keyboard accessible
   - Implement logical tab order
   - Provide visible focus indicators
   - Create keyboard shortcuts for common actions

3. **Screen Reader Support**
   - Implement alternative text for images
   - Create accessible names for interactive elements
   - Use appropriate ARIA landmarks
   - Test with screen readers

4. **Visual Design**
   - Maintain minimum contrast ratios (4.5:1 for normal text)
   - Do not rely solely on color to convey information
   - Support text resizing up to 200%
   - Implement reduced motion alternatives

### 8.3 Testing & Validation

1. **Automated Testing**
   - Implement axe-core in testing pipeline
   - Use eslint-plugin-jsx-a11y for static analysis
   - Include accessibility checks in CI/CD

2. **Manual Testing**
   - Regular keyboard navigation testing
   - Screen reader testing (VoiceOver, NVDA, JAWS)
   - Device and browser compatibility testing
   - User testing with people with disabilities

## 9. Testing Strategy

### 9.1 Testing Layers

1. **Unit Testing**
   - Test individual components in isolation
   - Mock dependencies and context
   - Test component rendering and state changes
   - Verify event handlers and user interactions

2. **Integration Testing**
   - Test interactions between components
   - Verify component composition
   - Test context providers with consumers
   - Validate form submissions and data flow

3. **End-to-End Testing**
   - Test complete user flows
   - Validate critical business processes
   - Test across multiple browsers and devices
   - Verify third-party integrations

### 9.2 Implementation Tools

1. **Testing Libraries**
   - Jest for unit and integration tests
   - React Testing Library for component tests
   - Cypress for end-to-end tests
   - Storybook for component documentation and visual testing

2. **Testing Approach**
   ```typescript
   // Example component test
   describe('UserCard', () => {
     it('renders user information correctly', () => {
       const user = {
         id: '123',
         name: 'John Doe',
         email: '<EMAIL>'
       };
       
       const { getByText } = render(<UserCard user={user} />);
       
       expect(getByText('John Doe')).toBeInTheDocument();
     });
     
     it('calls onSelect when clicked', () => {
       const user = { id: '123', name: 'John Doe' };
       const handleSelect = jest.fn();
       
       const { getByText } = render(
         <UserCard user={user} onSelect={handleSelect} />
       );
       
       fireEvent.click(getByText('John Doe'));
       expect(handleSelect).toHaveBeenCalledWith('123');
     });
   });
   ```

### 9.3 Coverage Requirements

1. **Minimum Coverage**
   - Components: 90% code coverage
   - Hooks: 95% code coverage
   - Utilities: 100% code coverage
   - Pages: 80% code coverage

2. **Critical Path Testing**
   - Authentication flows
   - Payment processing
   - User onboarding
   - Data visualization

## 10. Internationalization (i18n)

### 10.1 Implementation Strategy

1. **Translation Management**
   - Use next-intl for translations
   - Organize translations by feature
   - Use ICU message format for complex translations
   - Implement context for translators

2. **Implementation Approach**
   ```typescript
   // Example i18n implementation
   import { useTranslations } from 'next-intl';

   function UserProfile({ user }) {
     const t = useTranslations('UserProfile');
     
     return (
       <div>
         <h2>{t('heading')}</h2>
         <p>{t('joinedDate', { date: user.createdAt })}</p>
         <p>{t('callCount', { count: user.totalCalls })}</p>
       </div>
     );
   }
   ```

### 10.2 Localization Considerations

1. **Text Handling**
   - Account for text expansion/contraction
   - Implement proper pluralization
   - Handle RTL languages
   - Format dates, times, and numbers according to locale

2. **UI Adaptations**
   - Create flexible layouts for different text lengths
   - Implement locale-specific formatting
   - Support currency and numeric formatting
   - Test layouts with various languages

## 11. Browser & Device Support

### 11.1 Browser Support Matrix

1. **Tier 1 Support**
   - Chrome (latest 2 versions)
   - Firefox (latest 2 versions)
   - Safari (latest 2 versions)
   - Edge (latest 2 versions)
   - iOS Safari (latest 2 versions)
   - Android Chrome (latest 2 versions)

2. **Tier 2 Support**
   - Older versions with graceful degradation
   - Internet Explorer not supported

### 11.2 Responsive Design Breakpoints

```css
/* Tailwind breakpoints */
sm: '640px',   /* Small devices (phones) */
md: '768px',   /* Medium devices (tablets) */
lg: '1024px',  /* Large devices (laptops) */
xl: '1280px',  /* Extra large devices (desktops) */
2xl: '1536px', /* 2X large devices (large desktops) */
```

### 11.3 Device Testing Strategy

1. **Testing Approach**
   - Use device labs for physical testing
   - Implement BrowserStack for virtual testing
   - Include device testing in CI/CD pipeline
   - Perform manual testing on critical devices

2. **Test Coverage**
   - Test core functionality across all supported devices
   - Verify responsive breakpoints
   - Test touch interactions
   - Validate performance across device tiers

## 12. Code Quality & Review

### 12.1 Code Linting & Formatting

1. **ESLint Configuration**
   - Extend recommended configurations
   - Implement TypeScript-specific rules
   - Enforce accessibility rules
   - Implement import ordering

2. **Prettier Configuration**
   - Maintain consistent formatting
   - Apply automatic formatting on save
   - Integrate with ESLint
   - Enforce in CI/CD pipeline

### 12.2 Review Checklist

1. **Functional Review**
   - Implements requirements correctly
   - Handles edge cases
   - Works across all supported browsers/devices

2. **Technical Review**
   - Follows project conventions
   - Implements proper types
   - Handles errors correctly
   - Optimizes for performance

3. **Quality Review**
   - Includes proper tests
   - Meets accessibility requirements
   - Implements proper i18n
   - Follows security best practices

## 13. Security Considerations

### 13.1 Frontend Security Practices

1. **Data Protection**
   - Sanitize user inputs
   - Validate data on both client and server
   - Implement Content Security Policy
   - Protect against XSS attacks

2. **Authentication Security**
   - Implement secure token handling
   - Use HttpOnly cookies where appropriate
   - Apply proper session management
   - Implement secure redirects

### 13.2 API Security

1. **Request Security**
   - Use CSRF protection
   - Implement proper authentication headers
   - Validate request parameters
   - Handle sensitive data securely

2. **Response Handling**
   - Validate API responses
   - Handle errors gracefully
   - Protect against JSON hijacking
   - Implement retry mechanisms with backoff

## 14. Build & Deployment

### 14.1 Build Process

1. **Next.js Configuration**
   - Optimize image handling
   - Configure environment variables
   - Implement static optimizations
   - Configure internationalization

2. **Build Pipeline**
   - Lint and test before build
   - Generate production builds
   - Analyze bundle size
   - Create build artifacts

### 14.2 Deployment Strategy

1. **Environment Configuration**
   - Maintain separate configurations per environment
   - Implement feature flags
   - Use environment-specific API endpoints
   - Apply proper error monitoring

2. **Deployment Process**
   - Use CI/CD for automated deployments
   - Implement blue/green deployments
   - Apply post-deployment verification
   - Configure rollback capabilities

## 15. User Experience Standardization

### 15.1 Interaction Patterns
*   **Loading State Standards:** Define and consistently apply standard loading indicators (e.g., skeletons, spinners) for different contexts (page load, data fetching, background operations). Ensure loading states are accessible.
*   **Error Presentation:** Define consistent patterns for displaying errors (inline validation messages, toast notifications, dedicated error sections). Ensure error messages are clear, concise, and helpful. Provide clear paths to resolution.
*   **Success Feedback:** Standardize success feedback mechanisms (e.g., confirmation messages, visual cues) to clearly indicate completed actions.
*   **Input Validation Feedback:** Implement real-time or near-real-time validation feedback for form inputs where appropriate, guiding users before submission.
*   **Progressive Disclosure:** Use progressive disclosure patterns (e.g., accordions, modals, stepped flows) to manage complexity in interfaces, revealing information or options as needed.
*   **Confirmation Patterns:** Standardize confirmation dialogs or steps for destructive or irreversible actions (e.g., deletion, large purchases).

### 15.2 Visual Consistency
*   Strictly adhere to the established design system (Shadcn/ui theme, Tailwind config).
*   Maintain consistent use of typography, color palettes, spacing, and iconography across the application.
*   Ensure consistent layout patterns for similar page types or sections.

## 16. Version History

| Version | Date | Description |
|---------|------|-------------|
| 1.0.0 | 2025-04-13 | Initial comprehensive frontend guidelines document |
| 1.1.0 | 2025-04-14 | Added User Experience Standardization section from `Enhanced-ProjectRules.md`. |

## 17. Related Documents

- [App Flow Document](mdc:.cursor/rules/app_flow_document.mdc)
- [Tech Stack Document](mdc:.cursor/rules/tech_stack_document.mdc)
- [Project Rules](mdc:.cursor/rules/cursor_project_rules.mdc)
- [Backend Structure Document](mdc:.cursor/rules/backend_structure_document.mdc)
- [Accessibility Requirements (Project Rules)](mdc:.cursor/rules/cursor_project_rules.mdc#11-accessibility-requirements-enhanced)
