/**
 * Performance Monitoring Utility
 * 
 * This utility provides tools to measure and report on frontend performance metrics.
 * It captures Core Web Vitals and custom performance metrics.
 */

// Initialize performance monitoring
export const initPerformanceMonitoring = () => {
  if (typeof window === 'undefined' || !window.performance) {
    return;
  }

  // Report Web Vitals
  if ('onLCP' in window) {
    // Polyfill for browsers that don't support Web Vitals
    import('web-vitals').then(({ onLCP, onFID, onCLS, onTTFB, onINP }) => {
      // Largest Contentful Paint
      onLCP(metric => {
        console.log('LCP:', metric.value);
        reportMetric('LCP', metric.value);
      });
      
      // First Input Delay
      onFID(metric => {
        console.log('FID:', metric.value);
        reportMetric('FID', metric.value);
      });
      
      // Cumulative Layout Shift
      onCLS(metric => {
        console.log('CLS:', metric.value);
        reportMetric('CLS', metric.value);
      });
      
      // Time to First Byte
      onTTFB(metric => {
        console.log('TTFB:', metric.value);
        reportMetric('TTFB', metric.value);
      });
      
      // Interaction to Next Paint
      onINP(metric => {
        console.log('INP:', metric.value);
        reportMetric('INP', metric.value);
      });
    });
  }

  // Set up performance observer for custom metrics
  if ('PerformanceObserver' in window) {
    try {
      // Observe paint timing events
      const paintObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          const metricName = entry.name;
          const metricValue = entry.startTime;
          console.log(`${metricName}:`, metricValue);
          reportMetric(metricName, metricValue);
        });
      });
      
      paintObserver.observe({ entryTypes: ['paint'] });
      
      // Observe resource timing events
      const resourceObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          // Only log resources that take more than 500ms to load
          if (entry.duration > 500) {
            console.log(`Slow resource: ${entry.name} - ${entry.duration}ms`);
            reportMetric(`resource-${entry.initiatorType}`, entry.duration, {
              url: entry.name,
              type: entry.initiatorType
            });
          }
        });
      });
      
      resourceObserver.observe({ entryTypes: ['resource'] });
      
      // Observe long tasks
      const longTaskObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          console.log(`Long task: ${entry.duration}ms`);
          reportMetric('long-task', entry.duration);
        });
      });
      
      if (PerformanceObserver.supportedEntryTypes.includes('longtask')) {
        longTaskObserver.observe({ entryTypes: ['longtask'] });
      }
      
      // Observe navigation timing
      const navigationObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          // Calculate key navigation metrics
          const dnsTime = entry.domainLookupEnd - entry.domainLookupStart;
          const tcpTime = entry.connectEnd - entry.connectStart;
          const ttfb = entry.responseStart - entry.requestStart;
          const domContentLoaded = entry.domContentLoadedEventEnd - entry.navigationStart;
          const domComplete = entry.domComplete - entry.navigationStart;
          const loadTime = entry.loadEventEnd - entry.navigationStart;
          
          console.log('DNS lookup time:', dnsTime);
          console.log('TCP connection time:', tcpTime);
          console.log('TTFB:', ttfb);
          console.log('DOMContentLoaded:', domContentLoaded);
          console.log('DOM Complete:', domComplete);
          console.log('Page Load Time:', loadTime);
          
          reportMetric('dns-time', dnsTime);
          reportMetric('tcp-time', tcpTime);
          reportMetric('ttfb', ttfb);
          reportMetric('dom-content-loaded', domContentLoaded);
          reportMetric('dom-complete', domComplete);
          reportMetric('load-time', loadTime);
        });
      });
      
      navigationObserver.observe({ entryTypes: ['navigation'] });
    } catch (e) {
      console.error('Error setting up PerformanceObserver:', e);
    }
  }
};

// Report a performance metric to analytics
export const reportMetric = (name, value, attributes = {}) => {
  // In production, send to analytics service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to Google Analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'performance_metric', {
        metric_name: name,
        metric_value: value,
        ...attributes
      });
    }
    
    // Example: Send to custom analytics endpoint
    try {
      fetch('/api/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name,
          value,
          timestamp: Date.now(),
          url: window.location.pathname,
          ...attributes
        }),
        // Use keepalive to ensure the request completes even if the page is unloading
        keepalive: true
      });
    } catch (e) {
      console.error('Error reporting metric:', e);
    }
  }
  
  // Store metrics in localStorage for debugging
  if (typeof window !== 'undefined' && window.localStorage) {
    try {
      const metrics = JSON.parse(localStorage.getItem('performance_metrics') || '[]');
      metrics.push({
        name,
        value,
        timestamp: Date.now(),
        url: window.location.pathname,
        ...attributes
      });
      
      // Keep only the last 100 metrics to avoid filling up localStorage
      if (metrics.length > 100) {
        metrics.shift();
      }
      
      localStorage.setItem('performance_metrics', JSON.stringify(metrics));
    } catch (e) {
      console.error('Error storing metric in localStorage:', e);
    }
  }
};

// Measure component render time
export const measureComponentRender = (componentName) => {
  const startTime = performance.now();
  
  return () => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    console.log(`${componentName} render time:`, renderTime);
    reportMetric(`component-render-${componentName}`, renderTime);
    
    // Log slow renders (> 50ms)
    if (renderTime > 50) {
      console.warn(`Slow render detected: ${componentName} took ${renderTime}ms to render`);
    }
  };
};

// Custom hook for measuring component render time
export const useRenderTimer = (componentName) => {
  if (typeof window === 'undefined' || !window.performance) {
    return { startRender: () => {}, endRender: () => {} };
  }
  
  return {
    startRender: () => performance.now(),
    endRender: (startTime) => {
      const renderTime = performance.now() - startTime;
      console.log(`${componentName} render time:`, renderTime);
      reportMetric(`component-render-${componentName}`, renderTime);
      return renderTime;
    }
  };
};

// Create a performance mark and measure
export const markAndMeasure = (markName, measureName, startMarkName = null) => {
  if (typeof window === 'undefined' || !window.performance) {
    return;
  }
  
  try {
    performance.mark(markName);
    
    if (startMarkName) {
      performance.measure(measureName, startMarkName, markName);
      const entries = performance.getEntriesByName(measureName);
      if (entries.length > 0) {
        console.log(`${measureName}:`, entries[0].duration);
        reportMetric(measureName, entries[0].duration);
      }
    }
  } catch (e) {
    console.error('Error creating performance mark or measure:', e);
  }
};

// Export a performance dashboard component
export const PerformanceDashboard = () => {
  // Implementation will be in a separate file
  console.warn('PerformanceDashboard imported but not implemented');
  return null;
};
