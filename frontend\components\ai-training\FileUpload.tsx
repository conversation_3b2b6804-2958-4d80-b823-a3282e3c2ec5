'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { DocumentTextIcon, XMarkIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from '../shared/LoadingSpinner';

interface FileUploadProps {
  onUploadSuccess: (formData: FormData) => void;
  isUploading: boolean;
}

export default function FileUpload({ onUploadSuccess, isUploading }: FileUploadProps) {
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Handle file drop
  const onDrop = useCallback((acceptedFiles: File[]) => {
    // Reset error
    setError(null);
    
    // Check if any files were dropped
    if (acceptedFiles.length === 0) {
      return;
    }
    
    // Get the first file
    const droppedFile = acceptedFiles[0];
    
    // Check file size (10MB limit)
    if (droppedFile.size > 10 * 1024 * 1024) {
      setError('File size exceeds 10MB limit');
      return;
    }
    
    // Check file type
    const allowedTypes = [
      'text/plain',
      'text/csv',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/json',
      'text/markdown',
    ];
    
    if (!allowedTypes.includes(droppedFile.type)) {
      setError('File type not supported. Please upload a TXT, CSV, PDF, DOC, DOCX, JSON, or MD file.');
      return;
    }
    
    // Set the file
    setFile(droppedFile);
  }, []);

  // Configure dropzone
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/plain': ['.txt'],
      'text/csv': ['.csv'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
      'application/json': ['.json'],
      'text/markdown': ['.md'],
    },
    disabled: isUploading,
    multiple: false,
  });

  // Handle upload
  const handleUpload = () => {
    if (!file) return;
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'file');
    
    onUploadSuccess(formData);
  };

  // Handle clear file
  const handleClearFile = () => {
    setFile(null);
    setError(null);
  };

  return (
    <div className="space-y-4">
      {/* Dropzone */}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
          isDragActive
            ? 'border-blue-400 bg-blue-50 dark:border-blue-500 dark:bg-blue-900/20'
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
        } ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        <input {...getInputProps()} />
        <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
          {isDragActive
            ? 'Drop the file here...'
            : 'Drag and drop a file here, or click to select a file'}
        </p>
        <p className="mt-1 text-xs text-gray-500 dark:text-gray-500">
          Supported formats: TXT, CSV, PDF, DOC, DOCX, JSON, MD (Max 10MB)
        </p>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3 text-sm text-red-600 dark:text-red-400">
          {error}
        </div>
      )}

      {/* Selected file */}
      {file && !error && (
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-md p-3 flex items-center justify-between">
          <div className="flex items-center">
            <DocumentTextIcon className="h-5 w-5 text-blue-500 dark:text-blue-400 mr-2" />
            <div>
              <p className="text-sm font-medium text-blue-700 dark:text-blue-300">
                {file.name}
              </p>
              <p className="text-xs text-blue-600 dark:text-blue-400">
                {(file.size / 1024).toFixed(1)} KB • {file.type}
              </p>
            </div>
          </div>
          <button
            type="button"
            onClick={handleClearFile}
            disabled={isUploading}
            className="text-blue-500 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Upload button */}
      <div className="flex justify-end">
        <button
          type="button"
          onClick={handleUpload}
          disabled={!file || !!error || isUploading}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isUploading ? (
            <>
              <LoadingSpinner size="small" color="white" />
              <span className="ml-2">Uploading...</span>
            </>
          ) : (
            'Upload File'
          )}
        </button>
      </div>
    </div>
  );
}
