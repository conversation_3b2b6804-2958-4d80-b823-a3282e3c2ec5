/**
 * E2E Test for Number Purchase Journey
 * 
 * This test covers the process of searching for and purchasing a Twilio phone number,
 * ensuring users can add numbers to their account successfully.
 */

describe('Number Purchase Journey', () => {
  beforeEach(() => {
    // Log in before each test
    cy.login();
    
    // Mock API responses for Twilio number search and purchase
    cy.mockTwilioApi();
  });
  
  it('should allow users to search for available numbers', () => {
    // Navigate to the number purchase page
    cy.visit('/dashboard/numbers/purchase');
    
    // Verify page content
    cy.get('h1').should('contain', 'Purchase a New Number');
    
    // Select country (US by default)
    cy.get('select[name="country"]').select('US');
    
    // Enter area code
    cy.get('input[name="areaCode"]').type('555');
    
    // Submit search
    cy.get('button').contains('Search').click();
    
    // Wait for search results
    cy.wait('@searchNumbers');
    
    // Verify number list is displayed
    cy.get('[data-testid="number-list"]').should('be.visible');
    cy.get('[data-testid="number-item"]').should('have.length.at.least', 1);
    
    // Verify number details
    cy.get('[data-testid="number-item"]').first().should('contain', '+1555');
    cy.get('[data-testid="number-item"]').first().should('contain', 'San Francisco');
  });
  
  it('should allow users to purchase a number', () => {
    // Navigate to the number purchase page
    cy.visit('/dashboard/numbers/purchase');
    
    // Search for numbers
    cy.get('select[name="country"]').select('US');
    cy.get('input[name="areaCode"]').type('555');
    cy.get('button').contains('Search').click();
    
    // Wait for search results
    cy.wait('@searchNumbers');
    
    // Select the first number
    cy.get('[data-testid="number-item"]').first().within(() => {
      cy.get('button').contains('Purchase').click();
    });
    
    // Confirm purchase in modal
    cy.get('[data-testid="confirm-modal"]').should('be.visible');
    cy.get('[data-testid="confirm-button"]').click();
    
    // Wait for purchase to complete
    cy.wait('@purchaseNumber');
    
    // Verify success message
    cy.get('[data-testid="success-message"]').should('be.visible');
    cy.get('[data-testid="success-message"]').should('contain', 'successfully purchased');
    
    // Verify redirect to number details
    cy.url().should('include', '/dashboard/numbers/');
  });
  
  it('should display the purchased number in the number list', () => {
    // Navigate to the numbers list page
    cy.visit('/dashboard/numbers');
    
    // Wait for numbers to load
    cy.wait('@getNumbers');
    
    // Verify the purchased number appears in the list
    cy.get('[data-testid="number-card"]').should('have.length.at.least', 1);
    cy.get('[data-testid="number-card"]').first().should('contain', '+1555');
    
    // Verify number status
    cy.get('[data-testid="number-status"]').first().should('contain', 'Active');
  });
  
  it('should handle error states appropriately', () => {
    // Override the search mock to return an error
    cy.intercept('GET', '/api/numbers/search*', {
      statusCode: 500,
      body: { message: 'Unable to search for numbers at this time' }
    }).as('searchError');
    
    // Navigate to the number purchase page
    cy.visit('/dashboard/numbers/purchase');
    
    // Search for numbers
    cy.get('select[name="country"]').select('US');
    cy.get('input[name="areaCode"]').type('555');
    cy.get('button').contains('Search').click();
    
    // Wait for error response
    cy.wait('@searchError');
    
    // Verify error message
    cy.get('[data-testid="error-message"]').should('be.visible');
    cy.get('[data-testid="error-message"]').should('contain', 'Unable to search');
    
    // Now mock an empty result
    cy.intercept('GET', '/api/numbers/search*', {
      statusCode: 200,
      body: { numbers: [] }
    }).as('emptySearch');
    
    // Search again
    cy.get('button').contains('Search').click();
    
    // Wait for empty response
    cy.wait('@emptySearch');
    
    // Verify empty state message
    cy.get('[data-testid="empty-state"]').should('be.visible');
    cy.get('[data-testid="empty-state"]').should('contain', 'No available numbers');
  });
});
