# This file contains environment variables for production deployment
# Override the development variables in .env

# Production environment
NODE_ENV="production"

# API and service URLs
BACKEND_API_URL=https://backend-production-a0a3.up.railway.app/api
APP_URL=https://callsaver.app
WEBSITE_URL=https://callsaver.app

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://tlylzhcdynxbqxiihipe.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRseWx6aGNkeW54YnF4aWloaXBlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMyMDU2OTIsImV4cCI6MjA1ODc4MTY5Mn0.oWZ7PSgHRyU9iu2hqc5GEwxC0g8NveB-5ZhqRrlLn8Y
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRseWx6aGNkeW54YnF4aWloaXBlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzIwNTY5MiwiZXhwIjoyMDU4NzgxNjkyfQ.3hCYrSUMdeze3dhMaBEFcp4nhboo3jpXb-z4pjlGkU8

# NextAuth Configuration
NEXTAUTH_URL="https://callsaver.app"
NEXTAUTH_SECRET="your-nextauth-secret-key"

# CORS settings
CORS_ORIGIN=https://callsaver.app

# Feature flags
FEATURE_MULTILINGUAL=true
FEATURE_AFFILIATE_PROGRAM=true
FEATURE_AI_CALL_INSIGHTS=true
FEATURE_LEAD_SCORING=true
FEATURE_CUSTOM_BRANDING=true

# App Configuration
APP_NAME="CallSaver"
SUPPORT_EMAIL="<EMAIL>"

# Production Frontend Environment Variables
NODE_ENV="production"

# Base URL for API calls (Point to your deployed Railway backend)
NEXT_PUBLIC_API_URL= # Your Railway Backend URL (e.g., https://your-backend.up.railway.app)

# Production Supabase Configuration (MUST match backend)
NEXT_PUBLIC_SUPABASE_URL= # Your Production Supabase URL
NEXT_PUBLIC_SUPABASE_ANON_KEY= # Your Production Supabase Anon Key

# Production Stripe Configuration (Publishable Key Only)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY= # Your Production Stripe Publishable Key (pk_live_...)

# Optional: Analytics
# ANALYTICS_KEY=your-production-analytics-key
