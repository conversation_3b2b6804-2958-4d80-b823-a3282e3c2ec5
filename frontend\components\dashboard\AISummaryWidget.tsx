'use client';

import Link from 'next/link';
import { SparklesIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useDashboard } from '../../hooks/useDashboard';
import ErrorMessage from '../shared/ErrorMessage';
import LoadingSpinner from '../shared/LoadingSpinner';

interface AIInsight {
  id: string;
  timestamp: string;
  summary: string;
  link?: string;
}

interface AISummaryWidgetProps {
  insights: AIInsight[];
  isLoading: boolean;
  error: Error | null;
  onRetry: () => void;
}

export default function AISummaryWidget({ insights, isLoading, error, onRetry }: AISummaryWidgetProps) {
  const { dismissInsight } = useDashboard();

  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <ErrorMessage 
        title="Could not load AI insights" 
        message="We couldn't load your AI insights. Please try again later."
        error={error}
        onRetry={onRetry}
      />
    );
  }

  // Handle empty state
  if (!insights || insights.length === 0) {
    return (
      <div className="text-center py-8">
        <SparklesIcon className="h-12 w-12 text-indigo-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">No AI insights available</h3>
        <p className="text-gray-500 dark:text-gray-400 mt-2">
          As you use CallSaver, our AI will generate insights based on your activity.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {insights.map((insight) => (
        <div 
          key={insight.id} 
          className="relative p-4 bg-indigo-50 dark:bg-indigo-900/30 rounded-lg border border-indigo-100 dark:border-indigo-800"
        >
          <div className="flex items-start">
            <SparklesIcon className="h-5 w-5 text-indigo-600 dark:text-indigo-400 mt-0.5 mr-3 flex-shrink-0" />
            <div className="flex-grow">
              <p className="text-sm text-gray-800 dark:text-gray-200">{insight.summary}</p>
              
              {insight.link && (
                <Link 
                  href={insight.link}
                  className="text-xs text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 font-medium mt-2 inline-block"
                >
                  View details
                </Link>
              )}
            </div>
            <button 
              onClick={() => dismissInsight(insight.id)}
              className="ml-2 p-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150"
              aria-label="Dismiss insight"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
}
