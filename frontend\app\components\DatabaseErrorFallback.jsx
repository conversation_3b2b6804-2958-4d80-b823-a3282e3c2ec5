"use client";

import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';

/**
 * Fallback component to display when database connection issues occur
 */
export default function DatabaseErrorFallback({ error, resetErrorBoundary }) {
  const [retryCount, setRetryCount] = useState(0);
  const [retryTimerActive, setRetryTimerActive] = useState(false);
  const [countdown, setCountdown] = useState(5);

  // Handle retry logic
  const handleRetry = useCallback(() => {
    // Increment retry count
    setRetryCount(prevCount => prevCount + 1);
    
    // Call the resetErrorBoundary prop to trigger a retry
    if (resetErrorBoundary) {
      resetErrorBoundary();
    }
  }, [resetErrorBoundary]);

  // Effect for automatic retry countdown
  useEffect(() => {
    if (!retryTimerActive) return;
    
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setRetryTimerActive(false);
      setCountdown(5);
      handleRetry();
    }
  }, [retryTimerActive, countdown, handleRetry]);

  // Start auto-retry countdown
  const startRetryCountdown = () => {
    setRetryTimerActive(true);
  };

  return (
    <motion.div 
      className="w-full max-w-3xl mx-auto my-8 px-6 py-8 rounded-xl bg-gradient-to-b from-gray-900 to-gray-950 border border-purple-900/30 shadow-xl"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <div className="text-center">
        <div className="flex justify-center mb-6">
          <div className="w-20 h-20 rounded-full flex items-center justify-center bg-red-500/10">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
        </div>
        
        <h2 className="text-2xl font-bold text-white mb-4">Database Connection Issue</h2>
        
        <div className="mb-6 space-y-4">
          <p className="text-gray-300">
            We&apos;re having trouble connecting to our database. This could be due to temporary maintenance or network issues.
          </p>
          
          <div className="px-4 py-3 bg-gray-800/50 rounded-lg text-gray-400 text-sm">
            {retryCount > 0 && (
              <p className="mb-1 text-yellow-400">Retry attempt: {retryCount}</p>
            )}
            <p>{error?.message || 'Could not establish database connection'}</p>
          </div>
        </div>
        
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          {retryTimerActive ? (
            <button 
              className="px-4 py-2 rounded-md bg-indigo-600/50 text-white cursor-not-allowed"
              disabled
            >
              Retrying in {countdown}s...
            </button>
          ) : (
            <button 
              onClick={handleRetry}
              className="px-4 py-2 rounded-md bg-indigo-600 hover:bg-indigo-700 text-white transition-colors"
            >
              Try Again Now
            </button>
          )}
          
          {!retryTimerActive && (
            <button 
              onClick={startRetryCountdown}
              className="px-4 py-2 rounded-md bg-gray-700 hover:bg-gray-600 text-white transition-colors"
            >
              Auto-retry in 5s
            </button>
          )}
        </div>
        
        <div className="mt-8 text-sm text-gray-500">
          <p>If this problem persists, please contact <NAME_EMAIL></p>
        </div>
      </div>
    </motion.div>
  );
} 