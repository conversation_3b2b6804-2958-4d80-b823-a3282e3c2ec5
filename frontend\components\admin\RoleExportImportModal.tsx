'use client';

import { useState, Fragment, useRef } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Role, useRoleManagement } from '../../hooks/useRoleManagement';
import { Spinner } from '../ui/Spinner';
import toast from 'react-hot-toast';

interface RoleExportImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'export' | 'import';
}

export default function RoleExportImportModal({
  isOpen,
  onClose,
  mode,
}: RoleExportImportModalProps) {
  // State
  const [selectedRoleIds, setSelectedRoleIds] = useState<string[]>([]);
  const [exportedJson, setExportedJson] = useState<string>('');
  const [importJson, setImportJson] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get role management functions
  const { roles, exportRoles, importRoles } = useRoleManagement();

  // Handle role selection for export
  const handleRoleToggle = (roleId: string) => {
    if (selectedRoleIds.includes(roleId)) {
      setSelectedRoleIds(selectedRoleIds.filter(id => id !== roleId));
    } else {
      setSelectedRoleIds([...selectedRoleIds, roleId]);
    }
  };

  // Handle export
  const handleExport = () => {
    if (selectedRoleIds.length === 0) {
      setError('Please select at least one role to export');
      return;
    }

    const json = exportRoles(selectedRoleIds);
    if (!json) {
      setError('Failed to export roles');
      return;
    }

    setExportedJson(json);
    setError(null);
  };

  // Handle download
  const handleDownload = () => {
    if (!exportedJson) {
      return;
    }

    const blob = new Blob([exportedJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'callsaver-roles.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      const content = event.target?.result as string;
      setImportJson(content);
    };
    reader.readAsText(file);
  };

  // Handle import
  const handleImport = async () => {
    if (!importJson) {
      setError('Please select a file to import');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const success = await importRoles(importJson);
      if (success) {
        toast.success('Roles imported successfully');
        onClose();
      }
    } catch (err: any) {
      setError(err.message || 'Failed to import roles');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={onClose}
                    disabled={isSubmitting}
                  >
                    <span className="sr-only">Close</span>
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-semibold leading-6 text-gray-900 dark:text-white"
                    >
                      {mode === 'export' ? 'Export Roles' : 'Import Roles'}
                    </Dialog.Title>
                    <div className="mt-4">
                      {mode === 'export' ? (
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              Select roles to export:
                            </h4>
                            <div className="max-h-60 overflow-y-auto border border-gray-300 dark:border-gray-700 rounded-md p-2">
                              {roles.data?.map((role) => (
                                <div key={role.id} className="flex items-center py-1">
                                  <input
                                    type="checkbox"
                                    id={`role-${role.id}`}
                                    checked={selectedRoleIds.includes(role.id)}
                                    onChange={() => handleRoleToggle(role.id)}
                                    className="h-4 w-4 text-blue-600 rounded"
                                  />
                                  <label
                                    htmlFor={`role-${role.id}`}
                                    className="ml-2 text-sm cursor-pointer"
                                  >
                                    <span className="font-medium">{role.name}</span>
                                    {role.isSystem && (
                                      <span className="ml-2 px-1.5 py-0.5 text-xs bg-gray-200 text-gray-700 rounded">
                                        System
                                      </span>
                                    )}
                                  </label>
                                </div>
                              ))}
                            </div>
                          </div>

                          {exportedJson && (
                            <div>
                              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Exported JSON:
                              </h4>
                              <div className="max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-700 rounded-md p-2 bg-gray-50 dark:bg-gray-900">
                                <pre className="text-xs">{exportedJson}</pre>
                              </div>
                              <button
                                type="button"
                                className="mt-2 inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                                onClick={handleDownload}
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-4 w-4 mr-1.5"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                                  />
                                </svg>
                                Download JSON
                              </button>
                            </div>
                          )}

                          {error && (
                            <p className="text-sm text-red-600 dark:text-red-500">{error}</p>
                          )}

                          <div className="mt-5 sm:mt-6 sm:flex sm:flex-row-reverse">
                            <button
                              type="button"
                              className="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 sm:ml-3 sm:w-auto"
                              onClick={handleExport}
                            >
                              Export Selected Roles
                            </button>
                            <button
                              type="button"
                              className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 sm:mt-0 sm:w-auto"
                              onClick={onClose}
                            >
                              Close
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                              Import roles from JSON file:
                            </h4>
                            <div className="mt-2">
                              <input
                                type="file"
                                ref={fileInputRef}
                                accept=".json"
                                onChange={handleFileChange}
                                className="block w-full text-sm text-gray-500 dark:text-gray-400
                                  file:mr-4 file:py-2 file:px-4
                                  file:rounded-md file:border-0
                                  file:text-sm file:font-medium
                                  file:bg-blue-50 file:text-blue-700
                                  dark:file:bg-blue-900/30 dark:file:text-blue-400
                                  hover:file:bg-blue-100 dark:hover:file:bg-blue-900/40"
                                disabled={isSubmitting}
                              />
                            </div>
                          </div>

                          {importJson && (
                            <div>
                              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                JSON to import:
                              </h4>
                              <div className="max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-700 rounded-md p-2 bg-gray-50 dark:bg-gray-900">
                                <pre className="text-xs">{importJson}</pre>
                              </div>
                            </div>
                          )}

                          {error && (
                            <p className="text-sm text-red-600 dark:text-red-500">{error}</p>
                          )}

                          <div className="mt-5 sm:mt-6 sm:flex sm:flex-row-reverse">
                            <button
                              type="button"
                              className="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 sm:ml-3 sm:w-auto"
                              onClick={handleImport}
                              disabled={!importJson || isSubmitting}
                            >
                              {isSubmitting ? (
                                <Spinner size="sm" className="mr-2" />
                              ) : null}
                              Import Roles
                            </button>
                            <button
                              type="button"
                              className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 sm:mt-0 sm:w-auto"
                              onClick={onClose}
                              disabled={isSubmitting}
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
