/**
 * WebRTC Service
 * 
 * This service provides WebRTC functionality for voice calls over data connections.
 * It's used by the eSIM service to enable voice capabilities over eSIM data.
 */

const logger = require('../utils/logger');
const config = require('../config');
const crypto = require('crypto');

/**
 * Initialize the WebRTC service
 * 
 * @param {Object} options - Configuration options
 * @returns {Object} WebRTC service interface
 */
function initializeWebRtcService(options = {}) {
  // Get configuration
  const enabled = options.enabled !== undefined ? options.enabled : config.webrtc?.enabled || false;
  const stunServers = options.stunServers || config.webrtc?.stunServers || 'stun:stun.l.google.com:19302';
  const turnServerUrl = options.turnServerUrl || config.webrtc?.turnServerUrl;
  const turnUsername = options.turnUsername || config.webrtc?.turnUsername;
  const turnCredential = options.turnCredential || config.webrtc?.turnCredential;
  const iceTransportPolicy = options.iceTransportPolicy || config.webrtc?.iceTransportPolicy || 'all';
  
  logger.info('Initializing WebRTC service', { 
    enabled,
    iceTransportPolicy,
    hasTurnServer: !!turnServerUrl
  });
  
  // Storage for active call sessions
  const activeCalls = new Map();
  
  /**
   * Generate ICE server configuration
   * 
   * @returns {Array} ICE server configuration
   */
  function generateIceServers() {
    const iceServers = [];
    
    // Add STUN servers
    if (stunServers) {
      const stunServerList = stunServers.split(',').map(server => server.trim());
      stunServerList.forEach(server => {
        iceServers.push({ urls: server });
      });
    }
    
    // Add TURN server if configured
    if (turnServerUrl && turnUsername && turnCredential) {
      iceServers.push({
        urls: turnServerUrl,
        username: turnUsername,
        credential: turnCredential
      });
    }
    
    return iceServers;
  }
  
  /**
   * Create a new call session
   * 
   * @param {string} callId - Call ID
   * @param {Object} options - Call options
   * @returns {Object} Call session details
   */
  function createCallSession(callId, options = {}) {
    if (!enabled) {
      logger.warn('WebRTC service is disabled');
      throw new Error('WebRTC service is disabled');
    }
    
    const sessionId = callId || crypto.randomUUID();
    const now = new Date();
    
    // Create call session
    const session = {
      id: sessionId,
      from: options.from,
      to: options.to,
      host: options.host || '', // Signaling server host
      status: 'created',
      createdAt: now.toISOString(),
      updatedAt: now.toISOString(),
      userId: options.userId,
      iceServers: generateIceServers(),
      iceTransportPolicy,
      metadata: options.metadata || {}
    };
    
    // Store session
    activeCalls.set(sessionId, session);
    
    logger.info('Created WebRTC call session', { 
      sessionId, 
      from: options.from, 
      to: options.to 
    });
    
    return { ...session };
  }
  
  /**
   * Get call session details
   * 
   * @param {string} callId - Call ID
   * @returns {Object} Call session details
   */
  function getCallSession(callId) {
    if (!activeCalls.has(callId)) {
      logger.warn('WebRTC call session not found', { callId });
      throw new Error(`Call session not found: ${callId}`);
    }
    
    return { ...activeCalls.get(callId) };
  }
  
  /**
   * Update call session status
   * 
   * @param {string} callId - Call ID
   * @param {string} status - New status
   * @returns {Object} Updated call session
   */
  function updateCallStatus(callId, status) {
    if (!activeCalls.has(callId)) {
      logger.warn('WebRTC call session not found', { callId });
      throw new Error(`Call session not found: ${callId}`);
    }
    
    const session = activeCalls.get(callId);
    session.status = status;
    session.updatedAt = new Date().toISOString();
    
    // Store updated session
    activeCalls.set(callId, session);
    
    logger.info('Updated WebRTC call session status', { callId, status });
    
    return { ...session };
  }
  
  /**
   * End a call session
   * 
   * @param {string} callId - Call ID
   * @returns {boolean} Success indicator
   */
  function endCallSession(callId) {
    if (!activeCalls.has(callId)) {
      logger.warn('WebRTC call session not found', { callId });
      return false;
    }
    
    // Update status first
    updateCallStatus(callId, 'ended');
    
    // Remove session after a short delay to allow for final status updates
    setTimeout(() => {
      activeCalls.delete(callId);
      logger.info('Removed WebRTC call session', { callId });
    }, 5000);
    
    return true;
  }
  
  /**
   * Get all active call sessions
   * 
   * @param {Object} filters - Filter criteria
   * @returns {Array} Active call sessions
   */
  function getActiveCalls(filters = {}) {
    const calls = [];
    
    for (const [id, session] of activeCalls.entries()) {
      // Apply filters
      if (filters.userId && session.userId !== filters.userId) {
        continue;
      }
      
      if (filters.status && session.status !== filters.status) {
        continue;
      }
      
      calls.push({ ...session });
    }
    
    return calls;
  }
  
  // Return WebRTC service interface
  return {
    createCallSession,
    getCallSession,
    updateCallStatus,
    endCallSession,
    getActiveCalls,
    isEnabled: () => enabled
  };
}

// Export a singleton instance with default options
module.exports = initializeWebRtcService;
