"use client";

export const conversationData = [
  // Scenario 1: Photography Session
  {
    id: 'photography-session',
    title: 'Photography Session',
    context: 'Client interested in booking a professional photoshoot',
    missedCallAlert: {
      type: 'Photography Booking',
      detail: 'Emma Photography - 1 minute ago'
    },
    categories: [
      { icon: '📸', label: 'Portrait Photography' },
      { icon: '🎨', label: 'Creative Services' }
    ],
    messages: [
      {
        sender: 'ai',
        content: 'Hi! We noticed you just called Emma Photography. <PERSON> is now busy taking magic moments for her customers. How can I assist you? Would you like to book a shooting or do you have any further questions?'
      },
      {
        sender: 'user',
        content: 'I need family portraits done next month'
      },
      {
        sender: 'ai',
        content: 'Wonderful! Family portraits are our specialty. We have several options available next month. Our most popular packages are:\n\n• Classic Family Session (1 hour): $299\n• Extended Family Session (2 hours): $459\n• Deluxe Package (3 hours, multiple locations): $699\n\nAll packages include professional editing and 10 digital images. Do any of these interest you?'
      },
      {
        sender: 'user',
        content: 'The Classic package sounds good. What dates do you have available?'
      },
      {
        sender: 'ai',
        content: 'Great choice! For the Classic Family Session next month, we have the following dates available:\n\n• Saturday, June 4th: 9am or 4pm (golden hour)\n• Sunday, June 5th: 9am only\n• Saturday, June 11th: 4pm only\n• Sunday, June 19th: 9am or 4pm\n\nThe golden hour sessions (4pm) provide beautiful natural lighting and are highly recommended for family portraits. Which date and time works best for you?'
      },
      {
        sender: 'user',
        content: 'June 4th at 4pm would be perfect'
      },
      {
        sender: 'ai',
        content: 'Excellent! I\'ve reserved June 4th at 4pm for your Classic Family Session. To confirm this booking, we require a 50% deposit ($149.50). Would you like to complete this now? Once confirmed, I\'ll send preparation tips and location details to ensure we capture beautiful memories of your family.'
      },
      {
        sender: 'user',
        content: 'Yes, I can pay the deposit now'
      },
      {
        sender: 'ai',
        content: 'Perfect! I\'ve sent a secure payment link to your phone. Once you complete the deposit, your session will be officially booked. Our photographer Alex will be delighted to work with your family on June 4th at 4pm. Is there anything specific you\'d like to achieve with these portraits or any questions about preparing for the session?'
      }
    ],
    actionButtons: [
      { 
        label: 'View Portfolio',
        primary: false
      },
      { 
        label: 'Complete Booking',
        primary: true
      }
    ]
  },
  
  // Scenario 2: Technical Support & Troubleshooting
  {
    id: 'tech-support',
    title: 'Technical Support',
    context: 'Customer\'s internet connection is intermittently dropping',
    missedCallAlert: {
      type: 'Technical Support',
      detail: 'FastNet Internet Support - 1 minute ago'
    },
    categories: [
      { icon: '⚡', label: 'Tech Support' },
      { icon: '🌐', label: 'Internet Troubleshooting' }
    ],
    messages: [
      {
        sender: 'ai',
        content: 'Hi! This is FastNet Support. Sorry we missed your call! Our automated assistant can help troubleshoot your internet connection issues. How can I help you today?'
      },
      {
        sender: 'user',
        content: 'I need help troubleshooting my connection'
      },
      {
        sender: 'ai',
        content: 'I\'d be happy to help. Could you tell me what device(s) you\'re experiencing issues with? (Computer, phone, TV, etc.)'
      },
      {
        sender: 'user',
        content: 'My laptop and smart TV both keep losing connection'
      },
      {
        sender: 'ai',
        content: 'Since multiple devices are affected, let\'s troubleshoot your router:\n\n1. Power cycle your router (unplug for 30 seconds)\n2. Wait 2-3 minutes for it to restart\n\nCould you try that now?'
      },
      {
        sender: 'user',
        content: 'Just did that and it\'s still happening'
      },
      {
        sender: 'ai',
        content: 'I see this requires more advanced troubleshooting. I\'ve scheduled a technician to visit tomorrow between 1-3pm. They\'ll bring a replacement router and check your line quality. Does this time work for you? If you prefer, you can also visit our service center today and pick up a new router immediately.'
      },
      {
        sender: 'user',
        content: 'The technician visit works for me'
      },
      {
        sender: 'ai',
        content: 'Perfect! Your appointment is confirmed for tomorrow between 1-3pm. Our technician will call 30 minutes before arrival. I\'ve also added a $20 service credit to your account for the inconvenience. Is there anything else you need help with today?'
      }
    ],
    actionButtons: [
      { 
        label: 'Reschedule Visit',
        primary: false
      },
      { 
        label: 'Confirm Appointment',
        primary: true
      }
    ]
  },
  
  // Scenario 3: Restaurant Reservation
  {
    id: 'restaurant-booking',
    title: 'Restaurant Booking',
    context: 'Customer interested in making a dinner reservation',
    missedCallAlert: {
      type: 'Restaurant Booking',
      detail: 'Italian Restaurant - 1 minute ago'
    },
    messages: [
      {
        sender: 'ai',
        content: 'Hi! This is Bella Italia Restaurant. Sorry we missed your call! How can I assist you today?'
      },
      {
        sender: 'user',
        content: 'I\'d like to make a dinner reservation'
      },
      {
        sender: 'ai',
        content: 'I\'d be happy to help with a reservation. What day, time, and party size were you thinking of?'
      },
      {
        sender: 'user',
        content: 'This Friday at 7:30 PM for 4 people'
      },
      {
        sender: 'ai',
        content: 'I have availability this Friday at 7:30 PM for 4 people. Would you prefer a table in our main dining room or on our heated patio with garden views?'
      },
      {
        sender: 'user',
        content: 'The patio sounds nice'
      },
      {
        sender: 'ai',
        content: 'Excellent choice! To confirm your patio reservation for 4 this Friday at 7:30 PM, could you provide your name and a contact number?'
      },
      {
        sender: 'user',
        content: 'John Smith, ************'
      },
      {
        sender: 'ai',
        content: 'Thank you, John! Your reservation is confirmed for 4 people this Friday at 7:30 PM on our garden patio. I\'ve also noted that it\'s a special occasion. Our chef is featuring truffle risotto and seafood linguine specials this weekend. Would you like me to send the full menu to your phone?'
      }
    ],
    actionButtons: [
      { 
        label: 'View Menu',
        primary: false
      },
      { 
        label: 'Confirm Reservation',
        primary: true
      }
    ]
  },
  
  // Scenario 4: Auto Service
  {
    id: 'auto-service',
    title: 'Auto Service',
    context: 'Vehicle needs maintenance or repair',
    missedCallAlert: {
      type: 'Auto Service',
      detail: 'Premier Auto Service - 1 minute ago'
    },
    messages: [
      {
        sender: 'ai',
        content: 'Hi! This is Premier Auto Service. Sorry we missed your call! How can our service team help you today?'
      },
      {
        sender: 'user',
        content: 'I need a quote for brake replacement'
      },
      {
        sender: 'ai',
        content: 'I\'d be happy to provide a brake service quote. What\'s the make, model, and year of your vehicle?'
      },
      {
        sender: 'user',
        content: '2018 Honda Civic'
      },
      {
        sender: 'ai',
        content: 'For your 2018 Honda Civic, here are our brake service quotes:\n\n• Front brake pads: $180-$220\n• Complete brake service (all pads and rotors): $650-$780\n\nAll prices include parts, labor, and our 24-month warranty. When would you like to schedule service?'
      },
      {
        sender: 'user',
        content: 'How long would a complete brake service take?'
      },
      {
        sender: 'ai',
        content: 'A complete brake service takes about 2-3 hours. We have an opening tomorrow at 9:00 AM or Friday at 1:00 PM. Both include a complimentary vehicle inspection and free shuttle service. Which would you prefer?'
      },
      {
        sender: 'user',
        content: 'Friday at 1:00 PM works better for me'
      },
      {
        sender: 'ai',
        content: 'Perfect! I\'ve booked your 2018 Honda Civic for a complete brake service this Friday at 1:00 PM. Please arrive 15 minutes early for check-in. Would you like to add our alignment special for $79 (normally $129)? This is recommended with brake work to ensure even tire wear and optimal handling.'
      }
    ],
    actionButtons: [
      { 
        label: 'Add Alignment Special',
        primary: true
      },
      { 
        label: 'Confirm Appointment',
        primary: true
      }
    ]
  },
  
  // Scenario 5: Real Estate
  {
    id: 'real-estate',
    title: 'Real Estate',
    context: 'Interest in property listings or viewings',
    missedCallAlert: {
      type: 'Real Estate',
      detail: 'Skyline Properties - 1 minute ago'
    },
    categories: [
      { icon: '🏠', label: 'Property Inquiry' },
      { icon: '💰', label: 'Sales' }
    ],
    messages: [
      {
        sender: 'ai',
        content: 'Hi! This is Skyline Properties. Sorry we missed your call! How can I help with your property search today? Whether you\'re looking to buy, sell, or rent, I\'d be happy to help you find the perfect property match.'
      },
      {
        sender: 'user',
        content: 'I\'m looking for a 3-bedroom house in the Westridge area'
      },
      {
        sender: 'ai',
        content: 'Great! We have 3 fantastic matches in Westridge:\n\n1. 5678 Oak Street: 3 bed/2.5 bath, 2,100 sq ft - $549,000\n• Colonial-style (built 2015)\n• Open kitchen with stainless appliances\n• Master suite with walk-in closet\n• Finished basement\n• 2-car garage\n• 0.25 acre fenced yard\n• Top-rated school district\n\n2. 2468 Maple Drive: 3 bed/2 bath, 1,780 sq ft - $515,000\n3. 3690 Cedar Court: 3 bed/3 bath, 2,200 sq ft - $545,000\n\nWhich property interests you most? I can provide more details or schedule a viewing at your convenience.'
      },
      {
        sender: 'user',
        content: 'The Oak Street property sounds interesting. Can I see it this weekend?'
      },
      {
        sender: 'ai',
        content: 'I can arrange a private viewing of 5678 Oak Street. We have availability tomorrow at 4:30 PM or Saturday morning at 10:00 AM. Our agent Maria will provide a complete tour and answer all your questions about the neighborhood and financing options. Which time works best?'
      }
    ],
    actionButtons: [
      { 
        label: 'Book Tomorrow 4:30 PM',
        primary: true
      },
      { 
        label: 'Book Saturday 10:00 AM',
        primary: false
      }
    ]
  },
  
  // Scenario 6: Property Maintenance
  {
    id: 'property-maintenance',
    title: 'Property Maintenance',
    context: 'Tenant reporting urgent maintenance issue',
    missedCallAlert: {
      type: 'Property Management',
      detail: 'Tenant from Skyline Apartments'
    },
    categories: [
      { icon: '🔧', label: 'Maintenance' },
      { icon: '🏢', label: 'Property Management' }
    ],
    messages: [
      {
        sender: 'ai',
        content: 'Hi! This is Skyline Properties management. Sorry we missed your call. How can I assist you today?'
      },
      {
        sender: 'user',
        content: 'The AC isn\'t working in unit 304'
      },
      {
        sender: 'ai',
        content: "I'm sorry to hear about your AC issue. Is it completely non-functional or just not cooling properly?"
      },
      {
        sender: 'user',
        content: 'Completely non-functional'
      },
      {
        sender: 'ai',
        content: 'I understand this is urgent, especially with the current weather. I\'ve scheduled an emergency repair for today between 1-3 PM. Our technician Mike will bring replacement parts and has access to your unit. Does this timeframe work for you?'
      },
      {
        sender: 'user',
        content: 'Yes that works fine'
      },
      {
        sender: 'ai',
        content: 'Excellent. Your emergency AC repair is confirmed for today between 1-3 PM. You\'ll receive a text when the technician is on the way. I\'ve also arranged for a portable cooling unit to be delivered within the hour as a temporary solution. Is there anything else you need assistance with in your apartment?'
      }
    ],
    actionButtons: [
      { 
        label: 'Track Technician',
        primary: true 
      },
      { 
        label: 'Report Another Issue',
        primary: false 
      }
    ]
  },
  
  // Scenario 7: Healthcare Appointment
  {
    id: 'healthcare',
    title: 'Healthcare Appointment',
    context: 'Patient scheduling a check-up appointment',
    missedCallAlert: {
      type: 'Healthcare Appointment',
      detail: 'Missed call from patient'
    },
    categories: [
      { icon: '🩺', label: 'Medical Appointment' },
      { icon: '📅', label: 'Scheduling' }
    ],
    messages: [
      {
        sender: 'ai',
        content: 'Hello! This is Westside Medical Clinic. We noticed you tried to call us. How can I help you today?'
      },
      {
        sender: 'user',
        content: 'I need to schedule a check-up appointment'
      },
      {
        sender: 'ai',
        content: 'I can help schedule your check-up. Do you have a preferred doctor at our clinic?'
      },
      {
        sender: 'user',
        content: 'Dr. Johnson if available'
      },
      {
        sender: 'ai',
        content: "Dr. Johnson has availability next Tuesday at 10:00 AM and Thursday at 2:30 PM. Which works better for you?"
      },
      {
        sender: 'user',
        content: 'Thursday at 2:30 PM'
      },
      {
        sender: 'ai',
        content: "Great! Is this for a routine physical or do you have specific concerns to address?"
      },
      {
        sender: 'user',
        content: 'Just a routine physical for work'
      },
      {
        sender: 'ai',
        content: "Perfect! You\'re confirmed with Dr. Johnson for Thursday at 2:30 PM for your routine physical. Please arrive 15 minutes early and bring your insurance card and ID. I\'ve sent the pre-appointment forms to your email to save time. Would you like me to send a calendar invitation with parking instructions?"
      }
    ],
    actionButtons: [
      { 
        label: 'Send Calendar Invite',
        primary: true 
      },
      { 
        label: 'View Pre-Visit Forms',
        primary: false 
      }
    ]
  },
  
  // Scenario 8: E-commerce Order Status
  {
    id: 'ecommerce',
    title: 'E-commerce Support',
    context: 'Order status inquiry via WhatsApp Business',
    missedCallAlert: {
      type: 'Customer Service',
      detail: 'Order #45892 Status Inquiry'
    },
    categories: [
      { icon: '📦', label: 'Order Status' },
      { icon: '💬', label: 'WhatsApp Business' }
    ],
    messages: [
      {
        sender: 'ai',
        content: 'Hello from TechGadgets! 👋 We noticed you tried to reach us. How can we assist you today?'
      },
      {
        sender: 'user',
        content: 'I ordered a laptop last week, order #45892, but haven\'t received any shipping updates'
      },
      {
        sender: 'ai',
        content: "I\'ve located your Quantum Pro Laptop order (#45892). Good news! It was shipped yesterday via Express Delivery (tracking: TG8976543210) and is estimated to arrive this Friday, June 10th. Would you like the tracking link?"
      },
      {
        sender: 'user',
        content: 'Yes please, and is there a way to change delivery to Saturday? No one will be home on Friday.'
      },
      {
        sender: 'ai',
        content: "I\'ve sent the tracking link to your email and have rescheduled your delivery for Saturday, June 11th between 9am-1pm at no extra charge. You\'ll receive a text 30 minutes before arrival. I\'ve also added a premium laptop sleeve ($29 value) to your order as a courtesy for the delay. Is there anything else you need help with?"
      },
      {
        sender: 'user',
        content: 'That\'s perfect, thank you!'
      },
      {
        sender: 'ai',
        content: "You\'re welcome! Your Quantum Pro Laptop will arrive Saturday between 9am-1pm. The driver will send a text 30 minutes before delivery. We\'ve included setup instructions and a 15% discount code for your next purchase. Enjoy your new laptop, and feel free to contact us if you need any technical assistance!"
      }
    ],
    actionButtons: [
      { 
        label: 'Track Package',
        primary: true
      },
      { 
        label: 'View Order Details',
        primary: false
      }
    ]
  }
]; 