# Obtaining Airalo API Credentials

This guide provides detailed steps on how to get your Airalo Partners API credentials to use with the CallSaver eSIM integration.

## Contact Airalo Partners

Based on the API documentation, you'll need to contact Airalo Partners directly to request access to their API. Here are the recommended steps:

1. **Visit the Airalo Partners Website**:
   - Navigate to [https://airalo.com/partners](https://airalo.com/partners) or [https://developer.airalo.com](https://developer.airalo.com)

2. **Submit a Partnership Request**:
   - Look for a "Become a Partner" or "Developer Access" section
   - Fill out the partnership request form with your:
     - Company information
     - Use case for the API integration
     - Expected volume of eSIMs you plan to provision
     - Contact details

3. **Send an Email**:
   - If you can't find a specific form, email [<EMAIL>](mailto:<EMAIL>) or [<EMAIL>](mailto:<EMAIL>) with the subject "CallSaver API Integration Request"
   - Include the same information as mentioned above

4. **Follow Up with a Call**:
   - For faster response, you may want to call their general business line (look for a contact number on their website)
   - Ask to speak with someone in their partnerships or developer relations team

## What to Request

When contacting Airalo, be specific about what you need:

1. **API Access**: Request access to the Airalo Partners API for eSIM provisioning and management
2. **Client Credentials**: Ask for a Client ID and Client Secret for the OAuth 2.0 authentication flow
3. **Sandbox Environment**: Request access to their sandbox/testing environment first
4. **Documentation**: Ask for complete API documentation if different from what you accessed
5. **Support Contact**: Request a technical contact for any integration questions

## Alternative Approaches While Waiting

While waiting for API credentials, you can:

1. **Continue Using the Mock Provider**:
   - Our integration includes a mock provider that simulates Airalo API responses
   - This lets you develop and test the UI and flow without real credentials
   - To use: Set `TELEPHONY_DEFAULT_PROVIDER=mock` in your `.env` file

2. **Use the Test Script with Sample Data**:
   - Modify the test-airalo-connection.js to return sample data instead of making actual API calls
   - This allows you to validate your integration flow with realistic-looking responses

3. **Explore Alternative eSIM Providers**:
   - Consider integrating with other eSIM providers like GigSky or Truphone
   - Each provider has different API structures but similar overall capabilities

## Setting Up the API Credentials Once Received

Once you receive your credentials:

1. **Add to Environment**:
   ```
   AIRALO_CLIENT_ID=your_client_id_here
   AIRALO_CLIENT_SECRET=your_client_secret_here
   AIRALO_API_URL=https://api.airalo.com/v2
   AIRALO_SANDBOX=true  # Start with sandbox mode
   ```

2. **Run the Connection Test**:
   ```bash
   node back/backend/test-airalo-connection.js
   ```

3. **Start with Limited Users**:
   ```
   ESIM_ENABLED_USERS=1,2,3  # Test with specific user IDs first
   ```

## API Credential Security

Remember to follow these security practices:

1. **Never commit credentials** to your repository
2. Store them in environment variables or a secure vault
3. Restrict access to production credentials to authorized personnel only
4. Regularly rotate credentials if the API allows it

## Additional Resources

- **Airalo Developer Documentation**: Once you have access, the full API documentation will be available
- **Airalo Partners Portal**: After your partnership is approved, you may get access to a partners dashboard
- **MCP Integration**: Use the Airalo API MCP server to easily reference the API documentation during development
