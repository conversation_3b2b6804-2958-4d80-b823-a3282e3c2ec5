import { createServerClient } from '@supabase/ssr';
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

// This is the callback route that <PERSON><PERSON><PERSON> Auth will redirect to after authentication
export async function GET(request) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  
  // Debug log
  console.log('Auth callback received with code present:', !!code);
  
  if (code) {
    const cookieStore = cookies();
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          get: (name) => {
            const cookie = cookieStore.get(name);
            return cookie?.value;
          },
          set: (name, value, options) => {
            // Ensure proper cookie options, especially for secure environments
            const isProduction = process.env.NODE_ENV === 'production';
            const maxAge = options?.maxAge || 60 * 60 * 24 * 7; // Default 7 days
            
            cookieStore.set({
              name,
              value,
              ...options,
              secure: isProduction,
              path: options?.path || '/',
              sameSite: options?.sameSite || 'lax',
              maxAge,
            });
          },
          remove: (name, options) => {
            cookieStore.set({
              name,
              value: '',
              maxAge: -1,
              ...options,
              path: options?.path || '/',
            });
          },
        },
      }
    );
    
    try {
      // Exchange the code for a session
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);
      
      if (error) {
        console.error('Error exchanging code for session:', error);
        // Redirect to sign in page with error
        return NextResponse.redirect(new URL('/signin?error=auth_callback_error', request.url));
      }
      
      console.log('Successfully exchanged code for session:', !!data?.session);
      
      // URL to redirect to after sign in process completes - with flag to signal authentication just happened
      return NextResponse.redirect(new URL('/dashboard?just_signed_in=true', request.url));
    } catch (err) {
      console.error('Unexpected error in auth callback:', err);
      return NextResponse.redirect(new URL('/signin?error=unexpected_error', request.url));
    }
  }
  
  // If no code is present, redirect to sign in page
  return NextResponse.redirect(new URL('/signin?error=missing_code', request.url));
} 