# Redis Cluster Implementation Guide

This document provides a comprehensive guide for implementing Redis Cluster in production environments for the CallSaver application. Redis Cluster provides high availability and horizontal scalability beyond what Redis Sentinel can offer.

## Overview

Redis Cluster is a distributed implementation of Redis that provides:
- Data sharding across multiple Redis nodes
- Automatic failover during node failures
- Linear scalability up to 1000 nodes
- No single point of failure

This guide covers:
1. Redis Cluster architecture
2. Deployment strategies
3. Configuration for CallSaver
4. Monitoring and maintenance
5. Client configuration
6. Migration from standalone Redis

## Redis Cluster Architecture

### Key Concepts

- **Master-Replica Model**: Each shard has one master and multiple replicas
- **Sharding**: Data is automatically split across nodes using hash slots (16384 total)
- **Gossip Protocol**: Nodes communicate cluster state via a gossip protocol
- **Automatic Failover**: Replicas are promoted to masters during failures

### Minimum Requirements

For a production Redis Cluster, the minimum recommended setup is:
- 3 master nodes
- 3 replica nodes (one replica per master)
- Nodes distributed across different availability zones/racks

This provides resilience against single node failures and availability zone outages.

## Deployment Strategies

### Physical/VM Deployment

For traditional deployments on physical servers or VMs:

```bash
# Example directory structure on each node
/etc/redis/           # Configuration files
/var/lib/redis/data/  # Data directory
/var/log/redis/       # Log directory
```

### Containerized Deployment

For containerized environments (Docker/Kubernetes):

```yaml
# Example Docker Compose configuration for a single node
version: '3'
services:
  redis-node1:
    image: redis:7
    command: redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - ./redis-node1.conf:/usr/local/etc/redis/redis.conf
      - redis-node1-data:/data
    ports:
      - "6379:6379"
      - "16379:16379"  # Cluster bus port
    networks:
      - redis-cluster
    restart: always

networks:
  redis-cluster:
    driver: bridge

volumes:
  redis-node1-data:
```

### Kubernetes StatefulSet

For Kubernetes deployments:

```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-cluster
spec:
  serviceName: redis-cluster
  replicas: 6  # 3 masters + 3 replicas
  selector:
    matchLabels:
      app: redis-cluster
  template:
    metadata:
      labels:
        app: redis-cluster
    spec:
      containers:
      - name: redis
        image: redis:7
        command:
          - redis-server
          - "/etc/redis/redis.conf"
        ports:
        - containerPort: 6379
          name: client
        - containerPort: 16379
          name: cluster
        volumeMounts:
        - name: redis-config
          mountPath: /etc/redis
        - name: redis-data
          mountPath: /data
      volumes:
      - name: redis-config
        configMap:
          name: redis-cluster-config
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 10Gi
```

## Configuration

### Base Configuration

Each Redis node should have a similar configuration with unique node IDs and network settings:

```
# Redis Cluster node configuration
port 6379
cluster-enabled yes
cluster-config-file nodes.conf
cluster-node-timeout 5000
appendonly yes
appendfsync everysec
dir /var/lib/redis/data/

# Network
bind 0.0.0.0
protected-mode yes
requirepass YourStrongPasswordHere
masterauth YourStrongPasswordHere

# TLS (optional but recommended for production)
tls-port 6380
tls-cert-file /path/to/redis.crt
tls-key-file /path/to/redis.key
tls-ca-cert-file /path/to/ca.crt
tls-auth-clients yes
tls-replication yes
tls-cluster yes

# Performance tuning
maxmemory 4gb
maxmemory-policy volatile-lru
maxmemory-samples 5
```

### Creating the Cluster

After configuring all nodes, create the cluster:

```bash
redis-cli --cluster create \
  ********:6379 ********:6379 ********:6379 \
  ********:6379 ********:6379 ********:6379 \
  --cluster-replicas 1 \
  -a YourStrongPasswordHere
```

This creates a cluster with 3 masters and 3 replicas, with each replica replicating from a different master.

## CallSaver Integration

### Client Configuration

Update the Redis client configuration in the application:

```javascript
// In back/backend/config/index.js
module.exports = {
  // ... other config
  redis: {
    // For production with Redis Cluster
    production: {
      cluster: [
        { host: '********', port: 6379 },
        { host: '********', port: 6379 },
        { host: '********', port: 6379 },
        { host: '********', port: 6379 },
        { host: '********', port: 6379 },
        { host: '********', port: 6379 }
      ],
      password: process.env.REDIS_PASSWORD,
      prefix: process.env.REDIS_PREFIX || 'callsaver:prod:',
      enableTLS: process.env.REDIS_ENABLE_TLS === 'true',
      // Redis Cluster specific options
      clusterOptions: {
        scaleReads: 'slave',  // Read from replicas to distribute load
        maxRedirections: 16,  // Maximum number of redirections to follow
        retryDelayOnFailover: 300, // Retry delay in ms
        retryDelayOnClusterDown: 1000, // Retry delay when cluster is down
        enableOfflineQueue: true, // Queue commands when connection is lost
        enableReadyCheck: true, // Check if cluster is ready before executing commands
        redisOptions: {
          connectTimeout: 10000, // Connection timeout in ms
          commandTimeout: 5000,  // Command timeout in ms
          maxRetriesPerRequest: 3, // Max retries per request
          reconnectOnError: (err) => {
            // Only reconnect on specific errors
            const targetErrors = ['READONLY', 'ETIMEDOUT', 'ECONNREFUSED', 'ECONNRESET'];
            return targetErrors.some(e => err.message.includes(e));
          }
        }
      }
    }
  }
};
```

### Redis Client Implementation

Update the Redis client implementation to support cluster mode:

```javascript
// In back/backend/lib/redis.js
const Redis = require('ioredis');
const config = require('../config');
const logger = require('../utils/loggerEnhanced');
const CircuitBreaker = require('./circuitBreaker');

let redisClient = null;
let circuitBreaker = null;

/**
 * Initialize Redis client based on configuration
 * @returns {Object} Redis client
 */
function initializeRedisClient() {
  const redisConfig = config.redis[config.server.environment] || config.redis;
  
  // Check if we should use cluster mode
  const useCluster = Array.isArray(redisConfig.cluster) && redisConfig.cluster.length > 0;
  
  let client;
  
  if (useCluster) {
    logger.info('Initializing Redis in cluster mode');
    
    // Create Redis Cluster client
    client = new Redis.Cluster(redisConfig.cluster, {
      redisOptions: {
        password: redisConfig.password,
        tls: redisConfig.enableTLS ? {} : undefined,
      },
      ...redisConfig.clusterOptions
    });
    
    // Log cluster nodes
    client.on('ready', () => {
      logger.info('Redis Cluster is ready');
      client.nodes('master').forEach(node => {
        logger.info(`Redis Cluster master: ${node.options.host}:${node.options.port}`);
      });
    });
  } else {
    // Use Sentinel or standalone mode (existing code)
    // ...
  }
  
  // Set up event handlers
  client.on('error', (err) => {
    logger.error('Redis error', { error: err.message });
  });
  
  client.on('connect', () => {
    logger.info('Connected to Redis');
  });
  
  client.on('reconnecting', () => {
    logger.info('Reconnecting to Redis');
  });
  
  client.on('close', () => {
    logger.info('Redis connection closed');
  });
  
  client.on('end', () => {
    logger.info('Redis connection ended');
  });
  
  return client;
}

// Initialize circuit breaker
function initializeCircuitBreaker() {
  return new CircuitBreaker('redis', {
    failureThreshold: 3,
    resetTimeout: 30000,
    fallbackFunction: async (command, ...args) => {
      logger.warn(`Redis circuit breaker fallback for command: ${command}`, { args });
      // Return appropriate fallback values based on command
      if (command === 'get') return null;
      if (command === 'set') return 'OK';
      if (command === 'del') return 0;
      if (command === 'exists') return 0;
      return null;
    }
  });
}

// Initialize Redis and circuit breaker
function initialize() {
  if (!redisClient) {
    redisClient = initializeRedisClient();
    circuitBreaker = initializeCircuitBreaker();
  }
  return redisClient;
}

// Get Redis client
function getClient() {
  if (!redisClient) {
    initialize();
  }
  return redisClient;
}

// Get circuit breaker
function getCircuitBreaker() {
  if (!circuitBreaker) {
    initialize();
  }
  return circuitBreaker;
}

// Check if Redis is connected
function isConnected() {
  return redisClient && redisClient.status === 'ready';
}

// Wrap Redis commands with circuit breaker
async function executeCommand(command, ...args) {
  return circuitBreaker.execute(async () => {
    return redisClient[command](...args);
  }, command, ...args);
}

// Common Redis commands wrapped with circuit breaker
const get = (...args) => executeCommand('get', ...args);
const set = (...args) => executeCommand('set', ...args);
const del = (...args) => executeCommand('del', ...args);
const exists = (...args) => executeCommand('exists', ...args);
const keys = (...args) => executeCommand('keys', ...args);
const scan = (...args) => executeCommand('scan', ...args);
const hget = (...args) => executeCommand('hget', ...args);
const hset = (...args) => executeCommand('hset', ...args);
const hdel = (...args) => executeCommand('hdel', ...args);
const hgetall = (...args) => executeCommand('hgetall', ...args);
const lpush = (...args) => executeCommand('lpush', ...args);
const rpush = (...args) => executeCommand('rpush', ...args);
const lpop = (...args) => executeCommand('lpop', ...args);
const rpop = (...args) => executeCommand('rpop', ...args);
const lrange = (...args) => executeCommand('lrange', ...args);
const ltrim = (...args) => executeCommand('ltrim', ...args);
const incr = (...args) => executeCommand('incr', ...args);
const decr = (...args) => executeCommand('decr', ...args);
const expire = (...args) => executeCommand('expire', ...args);
const ttl = (...args) => executeCommand('ttl', ...args);
const ping = (...args) => executeCommand('ping', ...args);
const quit = async () => {
  if (redisClient) {
    await redisClient.quit();
  }
};

module.exports = {
  initialize,
  getClient,
  getCircuitBreaker,
  isConnected,
  get,
  set,
  del,
  exists,
  keys,
  scan,
  hget,
  hset,
  hdel,
  hgetall,
  lpush,
  rpush,
  lpop,
  rpop,
  lrange,
  ltrim,
  incr,
  decr,
  expire,
  ttl,
  ping,
  quit
};
```

## Monitoring and Maintenance

### Cluster Status

Check cluster status:

```bash
redis-cli -c -h <any-cluster-node> -p 6379 -a YourStrongPasswordHere cluster info
redis-cli -c -h <any-cluster-node> -p 6379 -a YourStrongPasswordHere cluster nodes
```

### Prometheus Metrics

Use the Redis Prometheus exporter for monitoring:

```bash
# Install Redis Prometheus exporter for each node
docker run -d --name redis-exporter-node1 \
  -p 9121:9121 \
  -e REDIS_ADDR=redis://********:6379 \
  -e REDIS_PASSWORD=YourStrongPasswordHere \
  oliver006/redis_exporter
```

### Grafana Dashboard

Create a Grafana dashboard with the following panels:
1. Cluster state (masters/replicas count)
2. Memory usage per node
3. Operations per second per node
4. Keyspace hits/misses
5. Connected clients per node
6. Network traffic
7. Replication lag

### Key Metrics to Monitor

1. **Cluster State**:
   - Number of masters and replicas
   - Cluster state (OK, fail, etc.)

2. **Node Health**:
   - Memory usage
   - CPU usage
   - Network I/O
   - Disk I/O (for persistence)

3. **Performance**:
   - Operations per second
   - Latency
   - Keyspace hits/misses
   - Connected clients

4. **Replication**:
   - Replication lag
   - Replication state

## Scaling the Cluster

### Adding Nodes

To add a new node to the cluster:

1. Configure the new node:
   ```bash
   # On the new node
   redis-server /etc/redis/redis.conf
   ```

2. Add the node to the cluster:
   ```bash
   # Add as a master
   redis-cli --cluster add-node new-node:6379 existing-node:6379 -a YourStrongPasswordHere
   
   # Add as a replica of a specific master
   redis-cli --cluster add-node new-node:6379 existing-node:6379 --cluster-slave --cluster-master-id <master-node-id> -a YourStrongPasswordHere
   ```

3. Rebalance the cluster:
   ```bash
   redis-cli --cluster rebalance existing-node:6379 -a YourStrongPasswordHere
   ```

### Removing Nodes

To remove a node from the cluster:

1. If it's a master, first reassign its slots:
   ```bash
   redis-cli --cluster reshard existing-node:6379 -a YourStrongPasswordHere
   ```

2. Remove the node:
   ```bash
   redis-cli --cluster del-node existing-node:6379 <node-id-to-remove> -a YourStrongPasswordHere
   ```

## Migration from Standalone Redis

### Migration Strategy

1. **Preparation**:
   - Set up the Redis Cluster
   - Update application code to support cluster mode
   - Test with a subset of data

2. **Data Migration**:
   - Option 1: Use Redis replication
     ```bash
     # Configure one cluster node as a replica of the standalone Redis
     redis-cli -h cluster-node -p 6379 -a YourStrongPasswordHere REPLICAOF standalone-redis 6379
     
     # Once synced, promote to master and add to cluster
     redis-cli -h cluster-node -p 6379 -a YourStrongPasswordHere REPLICAOF NO ONE
     ```
   
   - Option 2: Use RDB file
     ```bash
     # On standalone Redis
     redis-cli -h standalone-redis -p 6379 -a YourStrongPasswordHere SAVE
     
     # Copy RDB file to a cluster node and restart
     scp /var/lib/redis/data/dump.rdb user@cluster-node:/var/lib/redis/data/
     ```
   
   - Option 3: Use redis-cli to migrate keys
     ```bash
     # Use redis-cli to scan and migrate keys
     redis-cli --scan --pattern '*' | xargs -L 1 redis-cli DUMP | redis-cli -h cluster-node -p 6379 -a YourStrongPasswordHere RESTORE
     ```

3. **Cutover**:
   - Update application configuration to use cluster
   - Deploy updated application
   - Monitor for issues
   - Keep standalone Redis as fallback temporarily

4. **Verification**:
   - Verify data integrity
   - Verify application functionality
   - Verify performance

5. **Cleanup**:
   - Decommission standalone Redis once stable

## Troubleshooting

### Common Issues

1. **Cluster State Errors**:
   ```bash
   # Check cluster state
   redis-cli -c -h <any-cluster-node> -p 6379 -a YourStrongPasswordHere cluster info
   
   # Fix cluster state if needed
   redis-cli --cluster fix <any-cluster-node>:6379 -a YourStrongPasswordHere
   ```

2. **Slot Migration Issues**:
   ```bash
   # Check slot distribution
   redis-cli -c -h <any-cluster-node> -p 6379 -a YourStrongPasswordHere cluster slots
   
   # Manually reassign slots if needed
   redis-cli --cluster reshard <any-cluster-node>:6379 -a YourStrongPasswordHere
   ```

3. **Split Brain**:
   If network partition causes split brain:
   ```bash
   # Force cluster reconfiguration
   redis-cli --cluster fix <any-cluster-node>:6379 -a YourStrongPasswordHere --cluster-yes
   ```

4. **Client Connection Issues**:
   - Verify network connectivity
   - Check firewall rules
   - Ensure client is configured for cluster mode
   - Verify password authentication

## Conclusion

Redis Cluster provides high availability and horizontal scalability for the CallSaver application. By following this guide, you can implement a robust Redis Cluster that can handle production workloads and scale as needed.

The implementation should be tested thoroughly in a staging environment before deploying to production. Regular backups and monitoring are essential for maintaining a healthy Redis Cluster.

## References

- [Redis Cluster Specification](https://redis.io/topics/cluster-spec)
- [Redis Cluster Tutorial](https://redis.io/topics/cluster-tutorial)
- [Redis Cluster Administration](https://redis.io/topics/cluster-admin)
- [Redis Cluster Client Requirements](https://redis.io/topics/cluster-client-api)
