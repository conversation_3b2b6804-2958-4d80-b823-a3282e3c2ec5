/**
 * eSIM Profile Model
 * 
 * This module defines the data model for eSIM profiles and provides
 * utility functions for validation and data transformation.
 */

const Joi = require('joi');
const { ValidationError } = require('../../utils/errors');

/**
 * eSIM Profile schema validation
 */
const esimProfileSchema = Joi.object({
  id: Joi.string().required(),
  userId: Joi.string().required(),
  provider: Joi.string().required(),
  providerId: Joi.string().required(),
  iccid: Joi.string().allow(null),
  status: Joi.string().valid(
    'provisioned',
    'activated',
    'deactivated',
    'suspended',
    'expired'
  ).required(),
  activationCode: Joi.string().allow(null),
  activatedAt: Joi.date().iso().allow(null),
  expiresAt: Joi.date().iso().allow(null),
  packageId: Joi.string().required(),
  packageName: Joi.string().required(),
  dataLimit: Joi.number().allow(-1).required(), // -1 for unlimited
  dataUsed: Joi.number().min(0).default(0),
  region: Joi.string().allow(null),
  assignedNumber: Joi.string().allow(null),
  metadata: Joi.object().default({}),
  createdAt: Joi.date().iso().default(() => new Date().toISOString()),
  updatedAt: Joi.date().iso().default(() => new Date().toISOString())
});

/**
 * Validate eSIM profile data
 * 
 * @param {Object} profileData - Profile data to validate
 * @param {Object} options - Validation options
 * @returns {Object} Validated profile data
 */
function validateProfile(profileData, options = {}) {
  const { error, value } = esimProfileSchema.validate(profileData, {
    abortEarly: false,
    stripUnknown: true,
    ...options
  });
  
  if (error) {
    throw new ValidationError(`Invalid eSIM profile data: ${error.message}`);
  }
  
  return value;
}

/**
 * Map provider profile to standard format
 * 
 * @param {Object} providerProfile - Provider-specific profile data
 * @param {Object} additionalData - Additional data to include
 * @returns {Object} Standardized profile object
 */
function mapProviderProfile(providerProfile, additionalData = {}) {
  // Extract package details
  const packageDetails = providerProfile.packageDetails || {};
  
  // Create standardized profile
  const profile = {
    id: additionalData.id, // Database ID if available
    userId: additionalData.userId,
    provider: additionalData.provider,
    providerId: providerProfile.id, // Provider's internal ID
    iccid: providerProfile.iccid,
    status: providerProfile.status,
    activationCode: providerProfile.activationCode,
    activatedAt: providerProfile.activated,
    expiresAt: providerProfile.expires,
    packageId: packageDetails.id,
    packageName: packageDetails.name,
    dataLimit: packageDetails.dataAmount,
    dataUsed: providerProfile.dataUsed || 0,
    region: Array.isArray(packageDetails.regions) ? packageDetails.regions[0] : null,
    assignedNumber: providerProfile.assignedNumber,
    metadata: {
      ...providerProfile.metadata,
      ...additionalData.metadata
    },
    createdAt: additionalData.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  return profile;
}

module.exports = {
  validateProfile,
  mapProviderProfile,
  schema: esimProfileSchema
};
