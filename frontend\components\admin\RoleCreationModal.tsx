'use client';

import { useState, Fragment, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Role, CreateRoleParams, useRoleManagement } from '../../hooks/useRoleManagement';
import { Spinner } from '../ui/Spinner';

interface RoleCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialData?: Partial<CreateRoleParams>;
}

export default function RoleCreationModal({
  isOpen,
  onClose,
  initialData,
}: RoleCreationModalProps) {
  // Form state
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [parentId, setParentId] = useState<string | undefined>(undefined);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});

  // Get roles and role management functions
  const { roles, createRole, isCreatingRole } = useRoleManagement();

  // Reset form when modal opens/closes or initialData changes
  useEffect(() => {
    if (isOpen) {
      setName(initialData?.name || '');
      setDescription(initialData?.description || '');
      setParentId(initialData?.parentId);
      setFormErrors({});
    }
  }, [isOpen, initialData]);

  // Validate form
  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    if (!name.trim()) {
      errors.name = 'Role name is required';
    } else if (name.length < 2) {
      errors.name = 'Role name must be at least 2 characters';
    } else if (name.length > 50) {
      errors.name = 'Role name must be less than 50 characters';
    }

    if (description && description.length > 200) {
      errors.description = 'Description must be less than 200 characters';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const roleData: CreateRoleParams = {
      name: name.trim(),
      description: description.trim() || undefined,
      parentId: parentId || undefined,
    };

    createRole(roleData, {
      onSuccess: () => {
        onClose();
      },
    });
  };

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={onClose}
                  >
                    <span className="sr-only">Close</span>
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-semibold leading-6 text-gray-900 dark:text-white"
                    >
                      Create New Role
                    </Dialog.Title>
                    <div className="mt-4">
                      <form onSubmit={handleSubmit} className="space-y-4">
                        {/* Role Name */}
                        <div>
                          <label
                            htmlFor="name"
                            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                          >
                            Role Name <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            id="name"
                            name="name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            className={`mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm ${
                              formErrors.name ? 'border-red-500' : ''
                            }`}
                            placeholder="e.g., Support Agent"
                            disabled={isCreatingRole}
                          />
                          {formErrors.name && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-500">
                              {formErrors.name}
                            </p>
                          )}
                        </div>

                        {/* Description */}
                        <div>
                          <label
                            htmlFor="description"
                            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                          >
                            Description
                          </label>
                          <textarea
                            id="description"
                            name="description"
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            rows={3}
                            className={`mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm ${
                              formErrors.description ? 'border-red-500' : ''
                            }`}
                            placeholder="Describe the role's purpose and responsibilities"
                            disabled={isCreatingRole}
                          />
                          {formErrors.description && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-500">
                              {formErrors.description}
                            </p>
                          )}
                        </div>

                        {/* Parent Role */}
                        <div>
                          <label
                            htmlFor="parentId"
                            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                          >
                            Parent Role
                          </label>
                          <select
                            id="parentId"
                            name="parentId"
                            value={parentId || ''}
                            onChange={(e) => setParentId(e.target.value || undefined)}
                            className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                            disabled={isCreatingRole}
                          >
                            <option value="">None (Top-level role)</option>
                            {roles.data?.map((role) => (
                              <option key={role.id} value={role.id}>
                                {role.name}
                              </option>
                            ))}
                          </select>
                          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            Child roles inherit permissions from their parent
                          </p>
                        </div>

                        {/* Form Actions */}
                        <div className="mt-5 sm:mt-6 sm:flex sm:flex-row-reverse">
                          <button
                            type="submit"
                            className="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 sm:ml-3 sm:w-auto"
                            disabled={isCreatingRole}
                          >
                            {isCreatingRole ? (
                              <Spinner size="sm" className="mr-2" />
                            ) : null}
                            Create Role
                          </button>
                          <button
                            type="button"
                            className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 sm:mt-0 sm:w-auto"
                            onClick={onClose}
                            disabled={isCreatingRole}
                          >
                            Cancel
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
