---
description:
globs:
alwaysApply: false
---
# Help Center Functional Document (`help_center_document.mdc`)

## 1. Purpose and Scope

**Purpose:** Provide users with self-service resources and direct contact options to resolve issues, understand features, and get assistance with the CallSaver platform.

**Scope:**
- Host a searchable Knowledge Base (KB) with FAQs, tutorials, and guides.
- Organize help content into logical categories.
- Display individual help articles or guides.
- Provide a contact form for submitting support requests via email.
- Integrate a live chat support option (if applicable).
- Potentially link to external status pages or community forums.

## 2. User Interactions

- **Search Knowledge Base:** Enter keywords into a search bar to find relevant help articles.
- **Browse Categories:** Navigate through predefined categories (e.g., Getting Started, Billing, Automation, Troubleshooting) to find information.
- **View Article:** Click on a search result or category link to read a help article.
- **Submit Support Ticket:** Fill out a contact form with their name, email, subject, and description of the issue.
- **Initiate Live Chat:** Click a button to open a chat widget and connect with a support agent (if live chat is implemented).
- **Provide Feedback on Articles:** (Optional) Allow users to rate articles or indicate if they were helpful.

## 3. Backend Integrations & Services Used

- **Content Management System (CMS) or Database:** Stores and serves knowledge base articles and categories. Could be a dedicated headless CMS or custom database tables.
- **Search Service (e.g., Algolia, Elasticsearch, or DB Full-Text Search):** Powers the knowledge base search functionality.
- **Support Ticketing System (e.g., Zendesk, Intercom, HubSpot Service Hub):** Receives submitted contact forms and manages support tickets. Alternatively, a simple email service could be used.
- **Live Chat Provider (e.g., Intercom, Drift, Crisp):** Provides the live chat widget and agent interface (if implemented).
- **Email Service:** Sends confirmation emails upon ticket submission.

## 4. Necessary API Endpoints

- `GET /api/help/articles?search=<query>&category=<slug>&page=1&limit=10`: Searches or lists articles.
- `GET /api/help/articles/{articleSlug}`: Fetches the content of a specific article.
- `GET /api/help/categories`: Fetches the list of available help categories.
- `POST /api/support/tickets`: Submits the contact form data to the backend (which then forwards to the ticketing system or email).
- `GET /api/support/chat/config`: (If applicable) Fetches configuration needed to initialize the live chat widget.

## 5. Expected Frontend Component Structure

```
/components
  /help-center
    HelpCenterLayout.tsx        # Main layout for the help section
    HelpSearchBar.tsx           # Search input component
    CategoryList.tsx            # Displays categories/topics
    ArticleList.tsx             # Displays search results or articles in a category
      ArticleListItem.tsx       # Single item in the list
    ArticleViewer.tsx           # Component to display the content of an article
    SupportContactForm.tsx      # Form for submitting support tickets
    LiveChatTrigger.tsx         # Button or element to launch live chat (integrates with provider SDK)
    HelpCenterSkeleton.tsx      # Loading state placeholder
    ArticleFeedback.tsx         # (Optional) Component for rating articles
```

## 6. Data Displayed

- **Search Results:** List of article titles, potentially with snippets, matching the search query.
- **Categories:** List of category names, potentially with icons or descriptions.
- **Article View:** Article title, full content (text, images, videos), last updated date, related articles.
- **Contact Form:** Fields for Name, Email, Subject, Description, potentially file attachments.
- **Live Chat:** Chat widget interface provided by the third-party service.

## 7. State and UI Behavior

- **Loading States:** Show skeletons while searching or loading articles/categories.
- **Search Interaction:** Display search results dynamically as the user types (debounced) or upon submission. Handle "no results found".
- **Article Navigation:** Smooth transition when loading and displaying article content. Potentially show breadcrumbs for navigation.
- **Form Submission:** Disable the form and show a loading indicator during submission. Display a clear success message ("Your request has been submitted") or an error message ("Failed to submit request, please try again").
- **Live Chat:** The chat widget behavior is typically controlled by the third-party provider's SDK. Ensure the trigger button correctly initializes the chat.
- **Responsiveness:** Ensure articles and layout are readable and usable on all screen sizes.

## 8. AI Integration

- **AI-Powered Search:** Use semantic search or NLP models in the backend search service to provide more relevant results, understanding the user's intent beyond simple keyword matching.
- **AI Chatbot:** Implement an AI chatbot (potentially integrated with the live chat widget) to handle initial support queries, answer common questions based on the knowledge base, and triage issues before escalating to a human agent.
- **Article Suggestions:** AI could suggest relevant help articles based on the user's current context within the dashboard (e.g., if they are on the Automation page, suggest automation-related articles).

## 9. Error Handling Rules

- **Article Fetching Errors:** Display an error message if an article or category list cannot be loaded (e.g., "Failed to load help content").
- **Search Errors:** Show a message if the search service is unavailable or returns an error.
- **Form Submission Errors:** Provide specific feedback if possible (e.g., "Invalid email address"), otherwise a generic "Failed to submit request". Ensure required fields are validated client-side.
- **Live Chat Errors:** Handle errors during chat initialization (e.g., "Live chat is currently unavailable"). These are often handled within the provider's SDK.
- **Authentication Errors:** While typically public, ensure sensitive support interactions are linked to the logged-in user if necessary.

## 10. Logging and Usage Tracking Expectations

- **Log:**
    - Knowledge base searches performed (log the search terms).
    - Views of specific help articles (article slug/ID).
    - Support ticket submissions (success/failure, associated user if logged in).
    - Live chat initiation events.
    - Errors encountered while fetching content or submitting forms.
    - Article feedback submissions (rating, helpfulness).
- **Track:**
    - Views of the help center section.
    - Search usage frequency and common search terms (identifies documentation gaps).
    - Most viewed articles.
    - Least helpful articles (based on feedback).
    - Number of support tickets submitted via form vs. live chat initiated.
    - Resolution rates via self-service vs. direct support (requires linking support tickets back).
