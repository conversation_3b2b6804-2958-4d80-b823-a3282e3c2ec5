'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function PhoneNumbersTab() {
  const [phoneNumbers, setPhoneNumbers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [copySuccess, setCopySuccess] = useState('');
  const router = useRouter();

  // Function to fetch the user's phone numbers
  const fetchPhoneNumbers = async () => {
    setIsLoading(true);
    setError('');
    
    try {
      // Use a timestamp to avoid caching
      const timestamp = new Date().getTime();
      console.log('[PhoneNumbersTab] Fetching numbers...');
      const response = await fetch(`/api/numbers/mine?t=${timestamp}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
        },
      });
      
      const data = await response.json();
      console.log('[PhoneNumbersTab] API Response:', data);
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to fetch phone numbers');
      }
      
      if (data.success && Array.isArray(data.numbers)) {
        setPhoneNumbers(data.numbers);
      } else {
        console.warn('[PhoneNumbersTab] API call successful but numbers array missing or invalid.');
        setPhoneNumbers([]); // Ensure it's an empty array if data is invalid
      }
    } catch (err) {
      console.error('[PhoneNumbersTab] Error fetching phone numbers:', err);
      setError(`Failed to load your phone numbers: ${err.message}`);
      setPhoneNumbers([]); // Clear numbers on error
    } finally {
      setIsLoading(false);
      console.log('[PhoneNumbersTab] Fetch complete.');
    }
  };

  // Copy phone number to clipboard
  const copyToClipboard = (number) => {
    if (typeof window !== 'undefined') {
      navigator.clipboard.writeText(number)
        .then(() => {
          setCopySuccess(`${number} copied!`);
          setTimeout(() => setCopySuccess(''), 2000);
        })
        .catch(err => {
          console.error('Failed to copy number:', err);
        });
    }
  };

  // Format phone number for display
  const formatPhoneNumber = (number) => {
    // For US/CA numbers, format as (XXX) XXX-XXXX
    if (number.startsWith('+1') && number.length === 12) {
      return `(${number.substring(2, 5)}) ${number.substring(5, 8)}-${number.substring(8)}`;
    }
    // For NL numbers, format as +31 XX XXX XXXX
    else if (number.startsWith('+31')) {
      // Make sure we have enough digits to format
      if (number.length >= 12) {
        try {
          return `+31 ${number.substring(3, 5)} ${number.substring(5, 8)} ${number.substring(8)}`;
        } catch (e) {
          console.error('Error formatting NL number:', e);
          return number; // Fallback to original format
        }
      }
      return number;
    }
    // Default format for other numbers
    return number;
  };

  // Get flag emoji for country code
  const getCountryFlag = (countryCode) => {
    const flagEmojis = {
      'US': '🇺🇸',
      'CA': '🇨🇦',
      'NL': '🇳🇱',
      'GB': '🇬🇧',
      'AU': '🇦🇺',
      'FR': '🇫🇷',
      'DE': '🇩🇪'
    };
    
    return flagEmojis[countryCode] || '🌍';
  };

  // Handle refresh button click
  const handleRefresh = () => {
    fetchPhoneNumbers();
  };

  // Handle buy number button click
  const handleBuyNumber = () => {
    router.push('/dashboard/buy-number');
  };

  // Fetch phone numbers on component mount - client-side only
  useEffect(() => {
    fetchPhoneNumbers();
  }, []);

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-white">Phone Numbers</h3>
        <div className="flex space-x-2">
          <button
            onClick={handleRefresh}
            className="flex items-center text-gray-400 hover:text-white transition-colors"
            disabled={isLoading}
          >
            {/* Using a simple refresh text instead of Icon for simplicity */}
            <span>{isLoading ? 'Loading...' : 'Refresh'}</span>
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-900/40 border border-red-700 rounded-lg text-red-200">
          {error}
        </div>
      )}

      {copySuccess && (
        <div className="mb-6 p-4 bg-green-900/40 border border-green-700 rounded-lg text-green-200">
          {copySuccess}
        </div>
      )}

      {isLoading ? (
        <div className="flex items-center justify-center p-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      ) : phoneNumbers.length === 0 ? (
        <div className="text-center p-8 bg-gray-800/50 border border-gray-700 rounded-lg">
          <div className="mx-auto h-12 w-12 text-gray-400">📱</div>
          <h3 className="mt-4 text-lg font-medium text-white">No Phone Numbers</h3>
          <p className="mt-2 text-gray-400">
            You don't have any phone numbers associated with your account yet.
          </p>
          <button 
            onClick={handleBuyNumber}
            className="mt-6 px-5 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors"
          >
            Buy a Number
          </button>
        </div>
      ) : (
        <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
          {phoneNumbers.map((number) => (
            <div 
              key={number.id} 
              className="bg-gray-800/70 border border-gray-700 rounded-lg p-4 relative overflow-hidden hover:border-purple-500/50 transition-all"
            >
              <div className="absolute top-0 right-0 bg-gray-700/50 px-2 py-1 text-xs font-medium text-gray-200 rounded-bl-lg">
                {getCountryFlag(number.countryCode)} {number.countryCode}
              </div>
              
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-purple-700/30 rounded-full p-2">
                  <span className="text-purple-300">📱</span>
                </div>
                <div className="ml-3">
                  <h4 className="text-lg font-medium text-white">{formatPhoneNumber(number.number)}</h4>
                  <p className="text-sm text-gray-400">Active since {new Date(number.createdAt).toLocaleDateString()}</p>
                </div>
              </div>
              
              <div className="mt-4 flex justify-between items-center">
                <div className="text-sm text-gray-400">
                  {number.isActive ? (
                    <span className="text-green-500">● Active</span>
                  ) : (
                    <span className="text-red-500">● Inactive</span>
                  )}
                </div>
                
                <button
                  onClick={() => copyToClipboard(number.number)}
                  className="flex items-center text-sm text-gray-300 hover:text-white transition-colors"
                >
                  <span className="mr-1">📋</span>
                  <span>Copy</span>
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 