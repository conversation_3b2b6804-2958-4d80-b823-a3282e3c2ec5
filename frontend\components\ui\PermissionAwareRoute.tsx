'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { usePermissions } from '../../hooks/usePermissions';

interface PermissionAwareRouteProps {
  permission?: string;
  anyPermission?: string[];
  allPermissions?: string[];
  resource?: string;
  action?: string;
  scope?: string;
  children: React.ReactNode;
  fallbackUrl?: string;
  fallbackComponent?: React.ReactNode;
}

/**
 * A component that only renders its children if the user has the required permissions
 * Otherwise, it redirects to the fallback URL or renders the fallback component
 */
const PermissionAwareRoute: React.FC<PermissionAwareRouteProps> = ({
  permission,
  anyPermission,
  allPermissions,
  resource,
  action = 'read',
  scope = 'any',
  children,
  fallbackUrl = '/dashboard',
  fallbackComponent,
}) => {
  const router = useRouter();
  const { 
    hasPermission, 
    hasAnyPermission, 
    hasAllPermissions,
    canAccess,
    isLoading
  } = usePermissions();

  // Check if the user has the required permissions
  let hasAccess = false;

  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (anyPermission) {
    hasAccess = hasAnyPermission(anyPermission);
  } else if (allPermissions) {
    hasAccess = hasAllPermissions(allPermissions);
  } else if (resource) {
    hasAccess = canAccess(resource, action, scope);
  } else {
    // If no permissions are specified, allow access
    hasAccess = true;
  }

  // Redirect if the user doesn't have access
  useEffect(() => {
    if (!isLoading && !hasAccess && !fallbackComponent) {
      router.push(fallbackUrl);
    }
  }, [hasAccess, isLoading, router, fallbackUrl, fallbackComponent]);

  // If still loading, show nothing
  if (isLoading) {
    return null;
  }

  // If the user doesn't have access and there's a fallback component, render it
  if (!hasAccess && fallbackComponent) {
    return <>{fallbackComponent}</>;
  }

  // If the user doesn't have access and there's no fallback component, render nothing
  // (the useEffect will handle the redirect)
  if (!hasAccess) {
    return null;
  }

  // If the user has access, render the children
  return <>{children}</>;
};

export default PermissionAwareRoute;
