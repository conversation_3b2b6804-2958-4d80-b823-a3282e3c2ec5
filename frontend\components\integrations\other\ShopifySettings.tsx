'use client';

import { IntegrationSettings } from '../../../hooks/useIntegrations';

interface ShopifySettingsProps {
  settings: IntegrationSettings;
  onChange: (settings: IntegrationSettings) => void;
}

export default function ShopifySettings({
  settings,
  onChange,
}: ShopifySettingsProps) {
  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    onChange({
      ...settings,
      [name]: checked,
    });
  };

  // Handle select change
  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    onChange({
      ...settings,
      [name]: value,
    });
  };

  return (
    <div className="space-y-4">
      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
        Order Notifications
      </h4>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="notifyOnNewOrders"
            name="notifyOnNewOrders"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.notifyOnNewOrders !== false} // Default to true
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="notifyOnNewOrders"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            New Orders
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Send SMS notification when a new order is placed.
          </p>
        </div>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="notifyOnCanceledOrders"
            name="notifyOnCanceledOrders"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.notifyOnCanceledOrders || false}
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="notifyOnCanceledOrders"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Canceled Orders
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Send SMS notification when an order is canceled.
          </p>
        </div>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="notifyOnFulfilledOrders"
            name="notifyOnFulfilledOrders"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.notifyOnFulfilledOrders || false}
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="notifyOnFulfilledOrders"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Fulfilled Orders
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Send SMS notification when an order is fulfilled.
          </p>
        </div>
      </div>

      <div>
        <label
          htmlFor="notificationPhoneNumber"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Notification Phone Number
        </label>
        <select
          id="notificationPhoneNumber"
          name="notificationPhoneNumber"
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          value={settings.notificationPhoneNumber || ''}
          onChange={handleSelectChange}
        >
          <option value="">Select a phone number</option>
          <option value="+15551234567">+****************</option>
          <option value="+15557654321">+****************</option>
        </select>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Select which CallSaver number should receive order notifications.
        </p>
      </div>

      <div>
        <label
          htmlFor="orderThreshold"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Order Value Threshold
        </label>
        <select
          id="orderThreshold"
          name="orderThreshold"
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          value={settings.orderThreshold || '0'}
          onChange={handleSelectChange}
        >
          <option value="0">All orders</option>
          <option value="50">Orders over $50</option>
          <option value="100">Orders over $100</option>
          <option value="250">Orders over $250</option>
          <option value="500">Orders over $500</option>
        </select>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Only notify for orders above this value.
        </p>
      </div>
    </div>
  );
}
