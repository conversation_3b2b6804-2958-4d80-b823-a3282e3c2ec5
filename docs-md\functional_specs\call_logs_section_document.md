---
description:
globs:
alwaysApply: false
---
# Call Logs Section Functional Document (`call_logs_section_document.mdc`)

## 1. Purpose and Scope

**Purpose:** Provide a detailed, searchable, and filterable record of all incoming and outgoing calls, including voicemails. Offer access to call details, recordings, transcriptions, and AI-generated summaries or analyses.

**Scope:**
- List all call records associated with the user's account/numbers.
- Display key information for each call (caller/callee ID, number used, date/time, duration, direction, status/outcome).
- Provide access to call recordings (if enabled and available).
- Show voicemail recordings and transcriptions.
- Display AI-generated call summaries and sentiment analysis scores.
- Allow filtering by date range, phone number, direction (inbound/outbound), call status (answered, missed, voicemail, blocked), and AI sentiment.
- Enable searching by caller ID or keywords within transcriptions.
- Allow users to add notes or tags to call records.
- Provide options to block specific numbers directly from the log.

## 2. User Interactions

- **View Logs:** Scroll through a paginated list of call records.
- **Filter:** Apply filters using dropdowns, date pickers, and checkboxes.
- **Search:** Enter phone numbers or keywords into a search bar.
- **View Details:** Click on a call record to expand or navigate to a detailed view showing recordings, transcriptions, AI summaries, and notes.
- **Listen to Recordings:** Play call or voicemail recordings using an embedded audio player.
- **Read Transcripts:** View formatted call or voicemail transcriptions.
- **Add Notes/Tags:** Input text into a notes field or select/create tags for a call record.
- **Block Number:** Click a "Block" button associated with an incoming call record.
- **Initiate Action:** (Future) Click-to-call or click-to-message options from a log entry.

## 3. Backend Integrations & Services Used

- **Call Logging Service:** Stores and retrieves detailed call records.
- **Twilio/eSIM Provider Service:** Provides the raw call data (CDRs - Call Detail Records).
- **Storage Service (e.g., S3):** Stores call and voicemail recordings.
- **AI Service:**
    - Provides voicemail transcriptions.
    - Generates call summaries.
    - Performs sentiment analysis on transcripts.
- **Database:** Stores call metadata, transcriptions, AI analyses, user notes, and tags.
- **Number Management Service:** Links calls to specific user numbers.
- **Contact Service:** (Optional) Match caller IDs to saved contacts.

## 4. Necessary API Endpoints

- `GET /api/call-logs?page=1&limit=25&startDate=<date>&endDate=<date>&phoneNumberId=<id|all>&direction=in|out|all&status=answered|missed|voicemail|blocked|all&sentiment=positive|negative|neutral|all&search=<query>`: Fetches a paginated list of call records with filtering and searching.
- `GET /api/call-logs/{callSid}`: Fetches detailed information for a single call record, including links to recordings, transcriptions, and AI data.
- `GET /api/call-logs/{callSid}/recording`: Streams or provides a link to the call/voicemail recording. (Requires authentication/authorization).
- `GET /api/call-logs/{callSid}/transcription`: Fetches the transcription text.
- `GET /api/call-logs/{callSid}/ai-summary`: Fetches the AI-generated summary and sentiment.
- `POST /api/call-logs/{callSid}/notes`: Adds or updates user notes for a call record.
- `POST /api/call-logs/{callSid}/tags`: Adds or removes tags for a call record.
- `POST /api/blocklist`: Adds a phone number to the user's blocklist (triggered from a call log entry).

## 5. Expected Frontend Component Structure

```
/components
  /call-logs
    CallLogsLayout.tsx          # Main layout for the call logs section
    CallLogFilters.tsx          # Container for all filtering controls
      DateRangePicker.tsx       # (Shared component)
      PhoneNumberFilter.tsx     # Dropdown to filter by number
      DirectionFilter.tsx       # Radio buttons/dropdown for Inbound/Outbound
      StatusFilter.tsx          # Checkboxes/dropdown for call status
      SentimentFilter.tsx       # Dropdown for AI sentiment
      SearchBar.tsx             # Input field for searching
    CallLogTable.tsx            # Displays the list of call records
      CallLogRow.tsx            # Represents a single row in the table
      CallLogDetailView.tsx     # Expanded view or modal for call details
        AudioPlayer.tsx         # Component to play recordings
        TranscriptionViewer.tsx # Displays formatted transcription
        AISummaryDisplay.tsx    # Shows AI summary and sentiment
        NotesEditor.tsx         # Input for adding/editing notes
        TagsManager.tsx         # Component for managing tags
    PaginationControls.tsx      # Component for navigating pages
    CallLogsSkeleton.tsx        # Loading state placeholder for the table
```

## 6. Data Displayed

- **Table View:** Caller/Callee Number, Associated User Number, Date/Time, Duration, Direction (In/Out), Status (Answered, Missed, Voicemail, AI Handled, Blocked), AI Sentiment (Icon/Text), Link to Details.
- **Detail View:** All table information plus:
    - Call Recording Player (if available).
    - Voicemail Recording Player (if applicable).
    - Full Transcription Text.
    - AI-Generated Summary.
    - Detailed Sentiment Score/Analysis.
    - User Notes.
    - Assigned Tags.
    - Option to Block Number.

## 7. State and UI Behavior

- **Loading State:** Display skeleton loaders while fetching logs.
- **Filtering/Searching:** Update the log list dynamically as filters or search terms change. Maintain filter state.
- **Pagination:** Load log data page by page. Update pagination controls based on total results.
- **Detail Expansion:** Clicking a row expands it in place or opens a modal/drawer to show details.
- **Audio Playback:** Provide standard audio controls (play, pause, seek, volume). Indicate playback state.
- **Blocking:** Provide confirmation before blocking a number. Update UI to reflect blocked status.
- **Empty State:** Show a message like "No calls match your filters" or "No calls recorded yet."

## 8. AI Integration

- **Voicemail Transcription:** Display automatically generated transcriptions for voicemails.
- **Call Summarization:** Show concise AI-generated summaries of calls (especially longer ones).
- **Sentiment Analysis:** Display sentiment scores (positive, negative, neutral) for calls based on transcription analysis. Allow filtering by sentiment.
- **Keyword Search:** Backend search should leverage transcriptions for keyword matching.
- **AI Handling Indication:** Clearly mark calls that were handled partly or fully by the AI assistant.

## 9. Error Handling Rules

- **API Errors:** Display an error message if fetching logs fails (e.g., "Failed to load call logs"). Provide a retry option. Use toast notifications for errors on secondary actions (adding notes, blocking).
- **Recording/Transcription Errors:** Indicate if a recording or transcription is unavailable or failed to process (e.g., "Recording unavailable", "Transcription failed").
- **Search Errors:** Handle potential errors during complex search queries.
- **Blocking Errors:** Notify the user if blocking a number fails.
- **Authentication Errors:** Redirect to login if the session is invalid.

## 10. Logging and Usage Tracking Expectations

- **Log:**
    - Successful loads of call logs (include filters/page number).
    - API errors during log fetching.
    - Access to call details (log which callSid was viewed).
    - Playback of recordings (callSid).
    - Blocking actions (which number was blocked).
    - Errors encountered during playback, transcription display, or blocking.
    - Note/Tag creation/modification events.
- **Track:**
    - Views of the call logs section.
    - Use of filter controls (which filters are used most).
    - Use of the search functionality (capture search terms).
    - Clicks to view call details.
    - Recording playback events.
    - Blocking actions initiated.
    - Pagination usage.
