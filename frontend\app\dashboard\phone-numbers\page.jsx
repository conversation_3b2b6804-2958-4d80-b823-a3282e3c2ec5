'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs';

export default function PhoneNumbersPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [phoneNumbers, setPhoneNumbers] = useState([]);
  const [availableNumbers, setAvailableNumbers] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState('US');
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);

  // Mock phone number data
  const mockPhoneNumbers = [
    { id: 1, number: '+****************', friendly_name: 'Main Line', capabilities: { voice: true, sms: true, mms: true }, status: 'active', monthly_cost: 1.00, purchase_date: '2023-01-15T10:30:00Z' },
    { id: 2, number: '+****************', friendly_name: 'Support Line', capabilities: { voice: true, sms: true, mms: false }, status: 'active', monthly_cost: 1.00, purchase_date: '2023-02-20T14:45:00Z' },
    { id: 3, number: '+****************', friendly_name: 'Sales Line', capabilities: { voice: true, sms: true, mms: true }, status: 'active', monthly_cost: 1.00, purchase_date: '2023-03-10T09:15:00Z' },
  ];

  // Mock available numbers
  const mockAvailableNumbers = [
    { number: '+****************', capabilities: { voice: true, sms: true, mms: true }, monthly_cost: 1.00 },
    { number: '+****************', capabilities: { voice: true, sms: true, mms: false }, monthly_cost: 1.00 },
    { number: '+****************', capabilities: { voice: true, sms: true, mms: true }, monthly_cost: 1.00 },
    { number: '+****************', capabilities: { voice: true, sms: true, mms: true }, monthly_cost: 1.00 },
    { number: '+****************', capabilities: { voice: true, sms: true, mms: false }, monthly_cost: 1.00 },
  ];

  useEffect(() => {
    const fetchPhoneNumbers = async () => {
      try {
        setIsLoading(true);
        // In a real implementation, this would be an API call to get phone numbers
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setPhoneNumbers(mockPhoneNumbers);
      } catch (err) {
        console.error('Error fetching phone numbers:', err);
        setError('Failed to load phone numbers. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPhoneNumbers();
  }, []);

  const handleSearchNumbers = async () => {
    try {
      setIsSearching(true);
      // In a real implementation, this would be an API call to search for available numbers
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setAvailableNumbers(mockAvailableNumbers);
    } catch (err) {
      console.error('Error searching for numbers:', err);
      setError('Failed to search for available numbers. Please try again later.');
    } finally {
      setIsSearching(false);
    }
  };

  const handlePurchaseNumber = async (number) => {
    try {
      setIsPurchasing(true);
      // In a real implementation, this would be an API call to purchase a number
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Add the purchased number to the list
      const newNumber = {
        id: Math.max(...phoneNumbers.map(n => n.id)) + 1,
        number,
        friendly_name: `New Number ${phoneNumbers.length + 1}`,
        capabilities: mockAvailableNumbers.find(n => n.number === number).capabilities,
        status: 'active',
        monthly_cost: 1.00,
        purchase_date: new Date().toISOString()
      };
      
      setPhoneNumbers([...phoneNumbers, newNumber]);
      
      // Remove the number from the available list
      setAvailableNumbers(availableNumbers.filter(n => n.number !== number));
      
      alert(`Successfully purchased number: ${number}`);
    } catch (err) {
      console.error('Error purchasing number:', err);
      setError('Failed to purchase number. Please try again later.');
    } finally {
      setIsPurchasing(false);
    }
  };

  const handleReleaseNumber = async (id, number) => {
    if (window.confirm(`Are you sure you want to release the number ${number}? This action cannot be undone.`)) {
      try {
        setIsLoading(true);
        // In a real implementation, this would be an API call to release a number
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Remove the number from the list
        setPhoneNumbers(phoneNumbers.filter(n => n.id !== id));
        
        alert(`Successfully released number: ${number}`);
      } catch (err) {
        console.error('Error releasing number:', err);
        setError('Failed to release number. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold text-white mb-6">Phone Numbers</h1>
      
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
          <p className="text-red-200">{error}</p>
        </div>
      )}
      
      <Tabs defaultValue="my-numbers" className="w-full">
        <TabsList className="bg-gray-800/70 border border-purple-500/20 mb-6">
          <TabsTrigger value="my-numbers">My Numbers</TabsTrigger>
          <TabsTrigger value="buy-numbers">Buy Numbers</TabsTrigger>
        </TabsList>
        
        <TabsContent value="my-numbers" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-white">My Phone Numbers</h2>
            <button 
              onClick={() => document.querySelector('[data-value="buy-numbers"]').click()}
              className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
            >
              Buy New Number
            </button>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead className="bg-gray-700/50 text-gray-300">
                <tr>
                  <th className="px-4 py-2 rounded-tl-lg">Number</th>
                  <th className="px-4 py-2">Friendly Name</th>
                  <th className="px-4 py-2">Capabilities</th>
                  <th className="px-4 py-2">Status</th>
                  <th className="px-4 py-2">Monthly Cost</th>
                  <th className="px-4 py-2">Purchase Date</th>
                  <th className="px-4 py-2 rounded-tr-lg">Actions</th>
                </tr>
              </thead>
              <tbody className="text-gray-300">
                {isLoading ? (
                  <tr>
                    <td colSpan="7" className="px-4 py-2 text-center">Loading phone numbers...</td>
                  </tr>
                ) : phoneNumbers.length === 0 ? (
                  <tr>
                    <td colSpan="7" className="px-4 py-2 text-center">No phone numbers found</td>
                  </tr>
                ) : (
                  phoneNumbers.map((number, index) => (
                    <tr key={number.id} className={index % 2 === 0 ? 'bg-gray-700/30' : 'bg-gray-700/10'}>
                      <td className="px-4 py-2 font-medium">{number.number}</td>
                      <td className="px-4 py-2">{number.friendly_name}</td>
                      <td className="px-4 py-2">
                        <div className="flex space-x-2">
                          {number.capabilities.voice && (
                            <span className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">Voice</span>
                          )}
                          {number.capabilities.sms && (
                            <span className="px-2 py-1 bg-green-500/20 text-green-300 rounded-full text-xs">SMS</span>
                          )}
                          {number.capabilities.mms && (
                            <span className="px-2 py-1 bg-purple-500/20 text-purple-300 rounded-full text-xs">MMS</span>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          number.status === 'active' ? 'bg-green-500/20 text-green-300' : 'bg-red-500/20 text-red-300'
                        }`}>
                          {number.status.charAt(0).toUpperCase() + number.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-4 py-2">${number.monthly_cost.toFixed(2)}/mo</td>
                      <td className="px-4 py-2">{formatDate(number.purchase_date)}</td>
                      <td className="px-4 py-2">
                        <div className="flex space-x-2">
                          <button 
                            onClick={() => window.location.href = `/dashboard/phone-numbers/${number.id}/settings`}
                            className="text-blue-400 hover:text-blue-300"
                          >
                            Settings
                          </button>
                          <button 
                            onClick={() => handleReleaseNumber(number.id, number.number)}
                            className="text-red-400 hover:text-red-300"
                          >
                            Release
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>
        
        <TabsContent value="buy-numbers" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-6">Buy New Phone Number</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <label className="text-gray-300 block mb-1">Country</label>
              <select 
                value={selectedCountry} 
                onChange={(e) => setSelectedCountry(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              >
                <option value="US">United States</option>
                <option value="CA">Canada</option>
                <option value="GB">United Kingdom</option>
                <option value="AU">Australia</option>
              </select>
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Search (Optional)</label>
              <input 
                type="text" 
                value={searchTerm} 
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="e.g., 555 or area code"
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              />
            </div>
            
            <div className="flex items-end">
              <button 
                onClick={handleSearchNumbers}
                disabled={isSearching}
                className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors w-full"
              >
                {isSearching ? 'Searching...' : 'Search Numbers'}
              </button>
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead className="bg-gray-700/50 text-gray-300">
                <tr>
                  <th className="px-4 py-2 rounded-tl-lg">Number</th>
                  <th className="px-4 py-2">Capabilities</th>
                  <th className="px-4 py-2">Monthly Cost</th>
                  <th className="px-4 py-2 rounded-tr-lg">Actions</th>
                </tr>
              </thead>
              <tbody className="text-gray-300">
                {isSearching ? (
                  <tr>
                    <td colSpan="4" className="px-4 py-2 text-center">Searching for available numbers...</td>
                  </tr>
                ) : availableNumbers.length === 0 ? (
                  <tr>
                    <td colSpan="4" className="px-4 py-2 text-center">No available numbers found. Try a different search.</td>
                  </tr>
                ) : (
                  availableNumbers.map((number, index) => (
                    <tr key={index} className={index % 2 === 0 ? 'bg-gray-700/30' : 'bg-gray-700/10'}>
                      <td className="px-4 py-2 font-medium">{number.number}</td>
                      <td className="px-4 py-2">
                        <div className="flex space-x-2">
                          {number.capabilities.voice && (
                            <span className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">Voice</span>
                          )}
                          {number.capabilities.sms && (
                            <span className="px-2 py-1 bg-green-500/20 text-green-300 rounded-full text-xs">SMS</span>
                          )}
                          {number.capabilities.mms && (
                            <span className="px-2 py-1 bg-purple-500/20 text-purple-300 rounded-full text-xs">MMS</span>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-2">${number.monthly_cost.toFixed(2)}/mo</td>
                      <td className="px-4 py-2">
                        <button 
                          onClick={() => handlePurchaseNumber(number.number)}
                          disabled={isPurchasing}
                          className="bg-purple-600 hover:bg-purple-700 text-white py-1 px-3 rounded-lg text-sm transition-colors disabled:opacity-50"
                        >
                          {isPurchasing ? 'Purchasing...' : 'Purchase'}
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
