# Accessibility Guidelines for CallSaver.app

This document outlines the accessibility guidelines and best practices for the CallSaver.app frontend components. Following these guidelines will help ensure that our application is accessible to all users, including those with disabilities.

## Table of Contents

1. [General Guidelines](#general-guidelines)
2. [Semantic HTML](#semantic-html)
3. [ARIA Attributes](#aria-attributes)
4. [Keyboard Navigation](#keyboard-navigation)
5. [Color and Contrast](#color-and-contrast)
6. [Screen Readers](#screen-readers)
7. [Forms and Inputs](#forms-and-inputs)
8. [Images and Media](#images-and-media)
9. [Dynamic Content](#dynamic-content)
10. [Testing](#testing)
11. [Resources](#resources)

## General Guidelines

- Aim for WCAG 2.1 AA compliance at minimum
- Use semantic HTML elements whenever possible
- Ensure all interactive elements are keyboard accessible
- Provide alternative text for non-text content
- Maintain sufficient color contrast
- Design with screen readers in mind
- Test with accessibility tools and real users

## Semantic HTML

Use semantic HTML elements to provide meaning and structure to your content:

- Use `<header>`, `<nav>`, `<main>`, `<section>`, `<article>`, `<aside>`, and `<footer>` for page structure
- Use heading elements (`<h1>` through `<h6>`) in a logical hierarchy
- Use `<button>` for clickable actions and `<a>` for navigation
- Use `<ul>`, `<ol>`, and `<li>` for lists
- Use `<table>` for tabular data with appropriate `<th>`, `<td>`, `<caption>`, etc.

```jsx
// Good
<nav>
  <ul>
    <li><a href="/dashboard">Dashboard</a></li>
    <li><a href="/numbers">Numbers</a></li>
  </ul>
</nav>

// Avoid
<div className="nav">
  <div className="nav-item"><a href="/dashboard">Dashboard</a></div>
  <div className="nav-item"><a href="/numbers">Numbers</a></div>
</div>
```

## ARIA Attributes

Use ARIA attributes when semantic HTML is not sufficient:

- Use `aria-label` to provide a label for elements without visible text
- Use `aria-labelledby` to associate an element with its label
- Use `aria-describedby` to provide additional description
- Use `aria-hidden="true"` for decorative elements
- Use `aria-expanded`, `aria-controls`, and `aria-haspopup` for interactive components
- Use `aria-live` regions for dynamic content

```jsx
// Good
<button aria-label="Close dialog" onClick={closeDialog}>
  <svg aria-hidden="true">...</svg>
</button>

// Avoid
<div onClick={closeDialog}>X</div>
```

## Keyboard Navigation

Ensure all interactive elements are keyboard accessible:

- All interactive elements should be focusable and operable with keyboard
- Use logical tab order (tab index of 0 or not specified)
- Provide visible focus indicators
- Implement keyboard shortcuts for common actions
- Ensure custom components handle keyboard events appropriately

```jsx
// Good
<button onClick={handleClick} onKeyDown={handleKeyDown}>
  Click me
</button>

// Avoid
<div onClick={handleClick} className="button">
  Click me
</div>
```

## Color and Contrast

Ensure sufficient color contrast and don't rely solely on color:

- Maintain a contrast ratio of at least 4.5:1 for normal text and 3:1 for large text
- Don't use color as the only means of conveying information
- Provide additional indicators (icons, text, patterns) alongside color
- Test your color combinations with contrast checkers

```jsx
// Good
<div className="error-message">
  <ErrorIcon /> Error: Form submission failed
</div>

// Avoid
<div className="text-red-500">
  Form submission failed
</div>
```

## Screen Readers

Ensure content is accessible to screen readers:

- Use appropriate ARIA roles and attributes
- Provide descriptive alt text for images
- Use visually hidden text for screen readers when necessary
- Test with screen readers like NVDA, JAWS, or VoiceOver

```jsx
// Good
<img src="logo.png" alt="CallSaver.app Logo" />

// Avoid
<img src="logo.png" />
```

## Forms and Inputs

Make forms accessible:

- Associate labels with form controls using `htmlFor` and `id`
- Group related form controls with `fieldset` and `legend`
- Provide clear error messages and validation feedback
- Use appropriate input types (`email`, `tel`, etc.)
- Ensure form controls have accessible names

```jsx
// Good
<div>
  <label htmlFor="email">Email</label>
  <input id="email" type="email" aria-describedby="email-hint" />
  <p id="email-hint">We'll never share your email</p>
</div>

// Avoid
<div>
  <span>Email</span>
  <input type="text" />
  <small>We'll never share your email</small>
</div>
```

## Images and Media

Make images and media accessible:

- Provide alt text for all images (`alt=""` for decorative images)
- Provide captions and transcripts for audio and video
- Ensure media controls are keyboard accessible
- Avoid auto-playing media with sound

```jsx
// Good
<img src="chart.png" alt="Call volume increased by 25% in Q2 2023" />

// Avoid
<img src="chart.png" alt="Chart" />
```

## Dynamic Content

Make dynamic content accessible:

- Use `aria-live` regions for content that updates dynamically
- Announce important changes to screen readers
- Ensure modals and dialogs are accessible
- Provide ways to pause or stop animations and carousels

```jsx
// Good
<div aria-live="polite" aria-atomic="true">
  {message}
</div>

// Avoid
<div>{message}</div>
```

## Testing

Test your components for accessibility:

- Use automated testing tools like axe-core, Lighthouse, or WAVE
- Test with keyboard navigation
- Test with screen readers
- Conduct user testing with people with disabilities

## Resources

- [Web Content Accessibility Guidelines (WCAG)](https://www.w3.org/WAI/standards-guidelines/wcag/)
- [MDN Web Docs: Accessibility](https://developer.mozilla.org/en-US/docs/Web/Accessibility)
- [A11y Project](https://www.a11yproject.com/)
- [Inclusive Components](https://inclusive-components.design/)
- [axe-core](https://github.com/dequelabs/axe-core)
- [React Accessibility](https://reactjs.org/docs/accessibility.html)

## Reusable Accessible Components

We've created several reusable accessible components that you can use in your application:

- `AccessibleButton`: A button component with proper ARIA attributes and keyboard support
- `AccessibleModal`: A modal component with proper focus management and keyboard support
- `AccessibleTooltip`: A tooltip component with proper ARIA attributes
- `AccessibleTabs`: A tabs component with proper ARIA attributes and keyboard support
- `AccessibleDropdown`: A dropdown component with proper ARIA attributes and keyboard support

These components are available in the `components/accessible` directory.
