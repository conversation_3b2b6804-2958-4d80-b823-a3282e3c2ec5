'use client';

import { useState } from 'react';
import { AutomationCondition } from '../../../hooks/useScheduledAutomation';
import { PlusIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { v4 as uuidv4 } from 'uuid';

interface ConditionBuilderProps {
  conditions: AutomationCondition[];
  onChange: (conditions: AutomationCondition[]) => void;
}

// Available fields for conditions
const availableFields = [
  { label: 'Contact Name', value: 'contact.name', type: 'contact' },
  { label: 'Contact Email', value: 'contact.email', type: 'contact' },
  { label: 'Contact Phone', value: 'contact.phone', type: 'contact' },
  { label: 'Contact Group', value: 'contact.group', type: 'contact' },
  { label: 'Last Call Date', value: 'event.lastCallDate', type: 'event' },
  { label: 'Last Message Date', value: 'event.lastMessageDate', type: 'event' },
  { label: 'Call Count', value: 'event.callCount', type: 'event' },
  { label: 'Message Count', value: 'event.messageCount', type: 'event' },
  { label: 'Custom Field', value: 'custom.field', type: 'custom' },
];

// Available operators
const availableOperators = [
  { label: 'Equals', value: 'equals' },
  { label: 'Not Equals', value: 'not_equals' },
  { label: 'Contains', value: 'contains' },
  { label: 'Not Contains', value: 'not_contains' },
  { label: 'Greater Than', value: 'greater_than' },
  { label: 'Less Than', value: 'less_than' },
];

export default function ConditionBuilder({
  conditions,
  onChange,
}: ConditionBuilderProps) {
  const [customFieldName, setCustomFieldName] = useState('');
  
  // Add a new condition
  const addCondition = () => {
    const newCondition: AutomationCondition = {
      id: uuidv4(),
      field: availableFields[0].value,
      operator: 'equals',
      value: '',
      type: availableFields[0].type as 'contact' | 'event' | 'custom',
    };
    
    onChange([...conditions, newCondition]);
  };
  
  // Remove a condition
  const removeCondition = (id: string) => {
    onChange(conditions.filter(condition => condition.id !== id));
  };
  
  // Update a condition
  const updateCondition = (id: string, field: keyof AutomationCondition, value: any) => {
    onChange(
      conditions.map(condition => {
        if (condition.id === id) {
          // If changing the field, also update the type
          if (field === 'field') {
            const selectedField = availableFields.find(f => f.value === value);
            return {
              ...condition,
              [field]: value,
              type: selectedField?.type as 'contact' | 'event' | 'custom',
            };
          }
          
          return { ...condition, [field]: value };
        }
        return condition;
      })
    );
  };
  
  // Add a custom field
  const addCustomField = () => {
    if (customFieldName.trim()) {
      const newField = {
        label: customFieldName,
        value: `custom.${customFieldName.toLowerCase().replace(/\s+/g, '_')}`,
        type: 'custom' as const,
      };
      
      // Add to available fields (in a real app, you'd persist this)
      availableFields.push(newField);
      
      // Reset the input
      setCustomFieldName('');
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Condition Builder</h2>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
          Add conditions to determine when this automation should run. If no conditions are added, the automation will run for all contacts.
        </p>
      </div>

      {/* Conditions list */}
      <div className="space-y-4">
        {conditions.length > 0 ? (
          conditions.map((condition, index) => (
            <div key={condition.id} className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-2 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              {/* Field selector */}
              <div className="flex-1">
                <select
                  value={condition.field}
                  onChange={(e) => updateCondition(condition.id, 'field', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  {availableFields.map((field) => (
                    <option key={field.value} value={field.value}>
                      {field.label}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Operator selector */}
              <div className="flex-1">
                <select
                  value={condition.operator}
                  onChange={(e) => updateCondition(condition.id, 'operator', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  {availableOperators.map((operator) => (
                    <option key={operator.value} value={operator.value}>
                      {operator.label}
                    </option>
                  ))}
                </select>
              </div>
              
              {/* Value input */}
              <div className="flex-1">
                <input
                  type="text"
                  value={condition.value as string}
                  onChange={(e) => updateCondition(condition.id, 'value', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Value"
                />
              </div>
              
              {/* Remove button */}
              <button
                type="button"
                onClick={() => removeCondition(condition.id)}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          ))
        ) : (
          <div className="text-center p-6 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
            <p className="text-gray-500 dark:text-gray-400">
              No conditions added yet. Add a condition to filter when this automation runs.
            </p>
          </div>
        )}
      </div>

      {/* Add condition button */}
      <div>
        <button
          type="button"
          onClick={addCondition}
          className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <PlusIcon className="h-5 w-5 mr-2" />
          Add Condition
        </button>
      </div>

      {/* Custom field section */}
      <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
        <h3 className="text-md font-medium text-gray-900 dark:text-white mb-4">Add Custom Field</h3>
        <div className="flex space-x-2">
          <input
            type="text"
            value={customFieldName}
            onChange={(e) => setCustomFieldName(e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Custom field name"
          />
          <button
            type="button"
            onClick={addCustomField}
            disabled={!customFieldName.trim()}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-blue-300 disabled:cursor-not-allowed"
          >
            Add
          </button>
        </div>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Add custom fields to use in your conditions.
        </p>
      </div>
    </div>
  );
}
