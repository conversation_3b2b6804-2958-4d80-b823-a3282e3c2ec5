---
description: Defines the strategy for building, testing, and deploying the CallSaver application.
---
# Deployment Strategy (`deployment_strategy.mdc`)

## 1. Purpose and Scope

**Purpose:** To define the standardized process for building, testing, and deploying the CallSaver frontend and backend applications across different environments, ensuring reliability, consistency, and minimal downtime.

**Scope:**
- Branching strategy (e.g., Gitflow variation).
- Continuous Integration (CI) process.
- Continuous Deployment/Delivery (CD) process.
- Target environments (Development, Staging, Production).
- Deployment methods (e.g., containerization, serverless, PaaS).
- Database migration handling.
- Rollback procedures.
- Monitoring and post-deployment validation.

## 2. Branching Strategy

- **Model:** Adopt a Gitflow-like model or a simpler GitHub Flow / Trunk-Based Development model depending on team preference and release cadence. (Assume GitHub Flow for simplicity initially).
- **`main` Branch:** Represents production-ready code. Direct commits are disallowed. Merges happen via Pull Requests (PRs) from feature branches after review and CI checks pass.
- **Feature Branches:** Developers create branches off `main` for new features or bug fixes (e.g., `feat/dashboard-widget`, `fix/login-bug`).
- **Pull Requests (PRs):** All changes intended for `main` must go through a PR, requiring code review and successful CI checks before merging.

## 3. Continuous Integration (CI)

- **Trigger:** Automatically triggered on every push to any branch, and especially on PRs targeting `main`.
- **Platform:** [Specify CI/CD Platform - e.g., GitHub Actions, GitLab CI, Jenkins]
- **Pipeline Steps:**
    1.  **Checkout Code:** Fetch the latest code.
    2.  **Setup Environment:** Install dependencies (Node.js, npm/yarn, etc.).
    3.  **Linting:** Run code linters (e.g., ESLint) to enforce code style.
    4.  **Unit Tests:** Execute backend and frontend unit tests. Report coverage.
    5.  **Integration Tests:** Execute backend and frontend integration tests (potentially against test database/mock services).
    6.  **Build:** Create production-ready builds for frontend and backend (e.g., `next build`, `tsc` for backend if needed, Docker image build).
    7.  **Security Scans:** Run dependency vulnerability scans (e.g., `npm audit`, Snyk) and potentially static analysis security testing (SAST).
    8.  **(Optional) Bundle Size Checks:** Check frontend bundle sizes against budget (ref `performance_budget.mdc`).
    9.  **(Optional on PR) Preview Deployment:** Deploy the PR branch to a temporary preview environment for review.
- **Outcome:** Provide clear pass/fail status. Failing checks block PR merges.

## 4. Continuous Deployment/Delivery (CD)

- **Trigger:** Automatically triggered on successful merge to the `main` branch.
- **Environments:**
    - **Staging:** Represents a pre-production environment closely mirroring production.
    - **Production:** Live environment serving end-users.
- **Staging Deployment Pipeline:**
    1.  Triggered after `main` branch CI passes.
    2.  Deploy the built artifacts (e.g., Docker images, static assets) to the Staging environment.
    3.  Run Database Migrations against the Staging database.
    4.  Run End-to-End (E2E) Tests against the Staging environment.
    5.  Perform basic Smoke Tests (automated or manual).
    6.  Notify team of successful staging deployment.
- **Production Deployment Pipeline:**
    1.  **Trigger:** Manual trigger (Continuous Delivery) or automatic trigger after successful Staging deployment and optional approval step (Continuous Deployment). Prefer manual trigger initially for control.
    2.  Deploy the *same* artifacts deployed to Staging to the Production environment.
    3.  Run Database Migrations against the Production database (handle with care, potentially requiring maintenance window or blue/green strategy).
    4.  Perform basic Smoke Tests against Production.
    5.  Monitor application health closely post-deployment.
    6.  Notify team of successful production deployment.

## 5. Deployment Methods & Infrastructure

- **Hosting Platform:** [Specify Hosting - e.g., Vercel for Frontend, AWS ECS/EKS/Lambda for Backend, Supabase for DB/Auth]
- **Backend Deployment:** Containerize the Node.js application (Docker) and deploy to a container orchestration service (e.g., ECS, EKS) or a PaaS (e.g., Heroku, Render). Consider serverless (Lambda) for specific API endpoints or workers if appropriate.
- **Frontend Deployment:** Utilize platform features for Next.js deployment (e.g., Vercel, Netlify) for optimized static generation, SSR, ISR, and CDN distribution.
- **Infrastructure as Code (IaC):** Use tools like Terraform or AWS CDK to manage cloud infrastructure resources declaratively.

## 6. Database Migrations

- **Tool:** Use Prisma Migrate for managing database schema changes.
- **Process:**
    - Migrations are generated during development (`prisma migrate dev`).
    - Migrations are applied automatically during the CD pipeline (`prisma migrate deploy`) against Staging and Production environments.
    - **Caution:** Backward-incompatible schema changes require careful planning (e.g., multi-step deployment) to avoid downtime during deployment when old and new code versions might run concurrently.

## 7. Rollback Procedures

- **Mechanism:** The CD pipeline MUST support quick rollback to a previous stable version.
    - **Artifact-Based:** Re-deploying the previously known-good build artifact (e.g., Docker image, Vercel deployment ID).
    - **Infrastructure:** If using blue/green deployments, switch traffic back to the previous environment.
- **Database:** Rolling back database migrations can be complex and risky. Focus on backward-compatible changes or multi-step deployments. Have backup/restore procedures tested (ref `service_failover_and_redundancy.mdc`).
- **Trigger:** Initiate rollback manually based on critical monitoring alerts or failed post-deployment validation.

## 8. Monitoring and Validation

- Monitor key application metrics (error rates, latency, resource usage), business metrics, and Core Web Vitals immediately following a deployment.
- Use health checks and automated smoke tests post-deployment.
- Utilize feature flags (`feature_flag_strategy.mdc`) for gradual rollouts of significant changes, allowing monitoring of impact on a subset of users before full release.

## 9. Related Documents

- `docs/dev_guides/testing_strategy.mdc`
- `docs/functional_specs/feature_flag_strategy.mdc`
- `docs/functional_specs/env_configuration_rules.mdc`
- `docs/architecture/service_failover_and_redundancy.mdc`
- `docs/platform_ux/performance_budget.mdc`
- `back/backend/prisma/schema.prisma` (Migrations)
- CI/CD configuration files (e.g., `.github/workflows/`)
