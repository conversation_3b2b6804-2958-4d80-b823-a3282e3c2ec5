'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { markAndMeasure } from '../utils/performance/performanceMonitor';

/**
 * Custom hook for optimized data fetching
 * 
 * This hook provides several optimizations for data fetching:
 * - Caching of responses
 * - Deduplication of requests
 * - Automatic retries
 * - Stale-while-revalidate pattern
 * - Performance monitoring
 * - Abort controller support
 * 
 * @param {string} url - The URL to fetch data from
 * @param {Object} options - Fetch options and optimization settings
 * @param {Object} options.fetchOptions - Options for the fetch call
 * @param {boolean} options.enabled - Whether the query is enabled (default: true)
 * @param {number} options.cacheTime - Time in ms to cache the response (default: 5 minutes)
 * @param {number} options.staleTime - Time in ms before data is considered stale (default: 1 minute)
 * @param {boolean} options.deduplicate - Whether to deduplicate identical requests (default: true)
 * @param {number} options.retries - Number of retries on failure (default: 3)
 * @param {number} options.retryDelay - Delay between retries in ms (default: 1000)
 * @param {Function} options.onSuccess - Callback when data is successfully fetched
 * @param {Function} options.onError - Callback when an error occurs
 * @returns {Object} - { data, error, loading, refetch }
 */
const useOptimizedQuery = (url, options = {}) => {
  const {
    fetchOptions = {},
    enabled = true,
    cacheTime = 5 * 60 * 1000, // 5 minutes
    staleTime = 60 * 1000, // 1 minute
    deduplicate = true,
    retries = 3,
    retryDelay = 1000,
    onSuccess,
    onError,
  } = options;

  const [data, setData] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const [dataTimestamp, setDataTimestamp] = useState(null);
  
  const abortControllerRef = useRef(null);
  const retryCountRef = useRef(0);
  const cacheRef = useRef({});
  
  // Generate a cache key based on the URL and fetch options
  const getCacheKey = useCallback(() => {
    const key = `${url}|${JSON.stringify(fetchOptions)}`;
    return key;
  }, [url, fetchOptions]);
  
  // Check if data is stale
  const isDataStale = useCallback(() => {
    if (!dataTimestamp) return true;
    return Date.now() - dataTimestamp > staleTime;
  }, [dataTimestamp, staleTime]);
  
  // Fetch data from the API
  const fetchData = useCallback(async (skipCache = false) => {
    if (!url || !enabled) return;
    
    const cacheKey = getCacheKey();
    
    // Create a new abort controller for this request
    abortControllerRef.current = new AbortController();
    const { signal } = abortControllerRef.current;
    
    // Check if we have a cached response
    if (!skipCache && cacheRef.current[cacheKey] && Date.now() - cacheRef.current[cacheKey].timestamp < cacheTime) {
      setData(cacheRef.current[cacheKey].data);
      setDataTimestamp(cacheRef.current[cacheKey].timestamp);
      return;
    }
    
    // Check if there's already a request in progress for this URL
    if (deduplicate && window.__OPTIMIZED_QUERY_CACHE__?.[cacheKey]?.promise) {
      try {
        const result = await window.__OPTIMIZED_QUERY_CACHE__[cacheKey].promise;
        setData(result);
        setDataTimestamp(Date.now());
        return;
      } catch (err) {
        // If the shared promise fails, continue with a new request
      }
    }
    
    setLoading(true);
    setError(null);
    retryCountRef.current = 0;
    
    // Create a promise for this request that can be shared
    const fetchPromise = (async () => {
      try {
        // Mark the start of the fetch
        markAndMeasure(`fetch-start-${cacheKey}`, `fetch-start-${cacheKey}`);
        
        const response = await fetch(url, {
          ...fetchOptions,
          signal,
        });
        
        // Mark the end of the fetch
        markAndMeasure(`fetch-end-${cacheKey}`, `fetch-${cacheKey}`, `fetch-start-${cacheKey}`);
        
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        
        const result = await response.json();
        
        // Cache the response
        cacheRef.current[cacheKey] = {
          data: result,
          timestamp: Date.now(),
        };
        
        // Call onSuccess callback
        if (onSuccess) {
          onSuccess(result);
        }
        
        return result;
      } catch (err) {
        // Don't retry if the request was aborted
        if (err.name === 'AbortError') {
          throw err;
        }
        
        // Retry the request if we haven't exceeded the retry count
        if (retryCountRef.current < retries) {
          retryCountRef.current++;
          
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, retryDelay * retryCountRef.current));
          
          // Try again
          return fetchPromise();
        }
        
        // Call onError callback
        if (onError) {
          onError(err);
        }
        
        throw err;
      }
    })();
    
    // Store the promise in the global cache
    if (deduplicate) {
      if (!window.__OPTIMIZED_QUERY_CACHE__) {
        window.__OPTIMIZED_QUERY_CACHE__ = {};
      }
      window.__OPTIMIZED_QUERY_CACHE__[cacheKey] = { promise: fetchPromise };
    }
    
    try {
      const result = await fetchPromise;
      setData(result);
      setDataTimestamp(Date.now());
    } catch (err) {
      if (err.name !== 'AbortError') {
        setError(err);
      }
    } finally {
      setLoading(false);
      
      // Clean up the global cache
      if (deduplicate && window.__OPTIMIZED_QUERY_CACHE__?.[cacheKey]) {
        delete window.__OPTIMIZED_QUERY_CACHE__[cacheKey];
      }
    }
  }, [url, fetchOptions, enabled, cacheTime, deduplicate, retries, retryDelay, onSuccess, onError, getCacheKey]);
  
  // Function to manually refetch data
  const refetch = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);
  
  // Fetch data when the component mounts or when dependencies change
  useEffect(() => {
    fetchData();
    
    // Set up a timer to refetch stale data
    const intervalId = setInterval(() => {
      if (isDataStale()) {
        fetchData();
      }
    }, staleTime);
    
    return () => {
      clearInterval(intervalId);
      
      // Abort any in-flight requests when the component unmounts
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchData, isDataStale, staleTime]);
  
  return {
    data,
    error,
    loading,
    refetch,
    isStale: isDataStale(),
  };
};

export default useOptimizedQuery;
