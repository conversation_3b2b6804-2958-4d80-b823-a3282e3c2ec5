'use client';

import { useState } from 'react';
import { useProfile, useUpdateProfile } from '../../../hooks/useSettings';
import AvatarUploader from './AvatarUploader';
import ProfileForm from './ProfileForm';
import LoadingSpinner from '../shared/LoadingSpinner';
import ErrorMessage from '../shared/ErrorMessage';

export default function ProfileSettingsPanel() {
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  
  // Fetch profile data
  const { 
    data: profile, 
    isLoading, 
    isError, 
    error 
  } = useProfile();
  
  // Update profile mutation
  const updateProfile = useUpdateProfile();
  
  // Handle profile update
  const handleProfileUpdate = async (formData: any) => {
    try {
      await updateProfile.mutateAsync(formData);
      setSuccessMessage('Profile updated successfully');
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage(null);
      }, 3000);
    } catch (err) {
      // Error is handled by the mutation
      console.error('Error updating profile:', err);
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }
  
  if (isError) {
    return (
      <ErrorMessage 
        title="Failed to load profile" 
        message="We couldn't load your profile information. Please try again later."
        error={error instanceof Error ? error : undefined}
        onRetry={() => window.location.reload()}
      />
    );
  }
  
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Profile Settings</h2>
      
      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-800 dark:text-green-400">
          {successMessage}
        </div>
      )}
      
      {updateProfile.isError && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-800 dark:text-red-400">
          {updateProfile.error instanceof Error 
            ? updateProfile.error.message 
            : 'An error occurred while updating your profile'}
        </div>
      )}
      
      <div className="space-y-8">
        {/* Avatar uploader */}
        <div className="pb-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Profile Picture</h3>
          <AvatarUploader 
            currentAvatarUrl={profile?.avatarUrl} 
          />
        </div>
        
        {/* Profile form */}
        <ProfileForm 
          userData={profile} 
          isLoading={updateProfile.isPending}
          onSubmit={handleProfileUpdate}
        />
      </div>
    </div>
  );
}
