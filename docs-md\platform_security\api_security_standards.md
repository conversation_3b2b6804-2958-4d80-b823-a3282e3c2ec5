---
description: Defines comprehensive security standards for the CallSaver.app platform API and services.
---
# API Security Standards (`api_security_standards.mdc`)

## 1. Purpose and Scope

**Purpose:** To establish comprehensive security standards for the CallSaver.app platform, ensuring the protection of user data, prevention of unauthorized access, and maintenance of system integrity through multiple layers of security controls.

**Scope:**
- Authentication mechanisms and session management
- Authorization and access control
- Rate limiting and abuse prevention
- Audit logging and security monitoring
- Intrusion detection and response
- API input validation and output encoding
- Security headers and transport security
- Cryptographic standards

## 2. Enhanced Authentication System

### 2.1 Multi-Factor Authentication (MFA)

- **Requirement:** Support multiple forms of MFA for all user accounts.
- **Implementation:**
  - Time-based One-Time Password (TOTP) via authenticator apps
  - SMS-based verification codes (with appropriate security warnings)
  - Email-based verification codes
  - WebAuthn/FIDO2 support for hardware security keys
- **User Experience:**
  - Allow users to enable/disable <PERSON><PERSON> from account settings
  - Provide recovery codes when MFA is enabled
  - Enforce MFA for administrative accounts and make optional for standard users
  - Allow enterprise account administrators to enforce MFA for all users in their organization

### 2.2 Advanced Session Management

- **Device Fingerprinting:**
  - Generate and store a device fingerprint for each login session
  - Use fingerprint to detect suspicious login attempts
  - Factors to include: browser/OS information, screen resolution, timezone, installed plugins (non-PII)
- **Session Controls:**
  - Configurable session timeout (default: 24 hours of inactivity)
  - Ability to view and terminate active sessions
  - Automatic session termination upon password change
  - Session binding to IP address ranges (configurable strictness)
- **Session Tokens:**
  - Use secure, HttpOnly, SameSite=Strict cookies for session tokens
  - Implement token rotation on privilege escalation
  - Use separate short-lived access tokens and longer-lived refresh tokens

### 2.3 Account Lockout Mechanisms

- **Progressive Delays:**
  - Implement exponential backoff for failed login attempts
  - Start with 1-second delay, doubling with each failure
- **Temporary Lockouts:**
  - After 5 failed attempts, implement a 15-minute account lockout
  - After 10 failed attempts, implement a 1-hour account lockout
  - After 20 failed attempts, require account recovery
- **Notification:**
  - Email users about suspicious login attempts
  - Provide IP address and approximate location of attempts
  - Include "This wasn't me" link for immediate account security actions

### 2.4 IP-Based Restrictions

- **Enterprise Feature:**
  - Allow enterprise accounts to define allowed IP ranges
  - Support both IPv4 and IPv6 address ranges
  - Allow temporary exceptions with additional verification
- **Geolocation Restrictions:**
  - Optional country/region blocking
  - Alerts on logins from unusual locations
- **Implementation:**
  - Store allowed IP ranges in database with organization association
  - Check IP restrictions in authentication middleware
  - Log and alert on rejected access attempts

### 2.5 Password Policies

- **Strength Requirements:**
  - Minimum length: 12 characters
  - Complexity: Require 3 of 4 character types (uppercase, lowercase, numbers, special characters)
  - Check against common password lists
  - Implement zxcvbn or similar for password strength estimation
- **Management:**
  - Password expiration: Optional, not enforced by default
  - Password history: Prevent reuse of last 5 passwords
  - Secure password reset flow with expiring tokens
- **Storage:**
  - Use Argon2id for password hashing
  - Implement pepper in addition to per-user salt

## 3. Advanced Rate Limiting

### 3.1 IP-Based Throttling

- **Dynamic Thresholds:**
  - Base rate limits on historical traffic patterns
  - Adjust thresholds based on time of day and day of week
  - Implement "burst" allowances for legitimate spikes
- **Implementation:**
  - Use Redis for distributed rate limit tracking
  - Track requests per IP across multiple time windows (1 minute, 15 minutes, 1 hour)
  - Apply stricter limits to anonymous IPs vs. authenticated users

### 3.2 User-Specific Rate Limiting

- **Account Type Differentiation:**
  - Free tier: Stricter limits
  - Paid tiers: Higher limits based on subscription level
  - Enterprise: Customizable limits
- **Historical Behavior:**
  - Adjust limits based on account age and usage patterns
  - Implement reputation scoring for accounts
  - Gradually increase limits for well-behaved accounts

### 3.3 Endpoint-Specific Protection

- **Categorization:**
  - Authentication endpoints: Strictest limits
  - Read operations: Higher limits
  - Write operations: Moderate limits
  - Sensitive operations: Lowest limits
- **Burst Protection:**
  - Implement token bucket algorithm
  - Allow short bursts of traffic while maintaining long-term rate limits
  - Configure different burst capacities for different endpoint categories

### 3.4 Trusted Source Mechanisms

- **Bypass Criteria:**
  - Verified partner integrations via API keys
  - Whitelisted IP addresses for internal services
  - Enterprise customers with SLA requirements
- **Implementation:**
  - Store bypass rules in database with expiration dates
  - Require administrative approval for bypass creation
  - Log all rate limit bypasses for audit purposes

### 3.5 Notification Integration

- **Alert Conditions:**
  - Repeated rate limit violations from same source
  - Sudden increase in rate limit hits platform-wide
  - Pattern suggesting distributed attack
- **Response Actions:**
  - Automatic temporary IP blocks for severe violations
  - Notification to security team for review
  - User notification for accidental violations from legitimate users

## 4. Comprehensive Audit Logging

### 4.1 Centralized Logging System

- **Architecture:**
  - Structured JSON log format
  - Centralized log storage with tamper-evident properties
  - Separation of application logs from security audit logs
- **Log Shipping:**
  - Real-time log forwarding to secure storage
  - Buffering mechanism for handling connectivity issues
  - Guaranteed delivery with acknowledgments

### 4.2 Tamper-Proof Logging

- **Cryptographic Verification:**
  - Generate HMAC for each log entry
  - Implement sequential log entry numbering
  - Create periodic log digests with digital signatures
- **Storage Security:**
  - Write-once storage policy
  - Encryption at rest
  - Access controls limited to security personnel

### 4.3 Structured Logging Format

- **Standard Fields:**
  - Timestamp (ISO 8601 format with timezone)
  - Event type/category
  - Severity level
  - User identifier (if authenticated)
  - Session identifier
  - Request identifier
  - Source IP address
  - Resource being accessed
  - Action being performed
  - Result of action (success/failure)
  - Error codes/messages (if applicable)
- **Sensitive Data Handling:**
  - No passwords or authentication tokens in logs
  - Mask/truncate sensitive personal data
  - Comply with GDPR/CCPA requirements

### 4.4 Retention Policies

- **Tiered Retention:**
  - High-detail logs: 90 days
  - Medium-detail logs: 1 year
  - Low-detail summary logs: 7 years
- **Compliance Alignment:**
  - Adjust retention based on applicable regulations
  - Implement legal hold mechanism for investigations
  - Support data subject access and deletion requests

### 4.5 Admin Dashboard

- **Features:**
  - Real-time log viewing with filtering
  - Advanced search capabilities
  - Saved searches for common audit scenarios
  - Export functionality for investigations
  - Visual representations of security events
- **Access Controls:**
  - Role-based access to log viewing
  - Audit trail of dashboard usage itself
  - Read-only access by default

## 5. Intrusion Detection System

### 5.1 Behavior Analysis

- **User Behavior Profiling:**
  - Establish baseline behavior for each user
  - Track deviations from normal patterns
  - Consider time of access, location, device, and activity types
- **System-Wide Patterns:**
  - Monitor for unusual traffic patterns
  - Detect anomalous API usage
  - Identify coordinated activities across multiple accounts

### 5.2 Rules-Based Detection

- **Attack Signatures:**
  - SQL injection attempts
  - Cross-site scripting (XSS) patterns
  - Authentication brute force
  - Known vulnerability exploitation patterns
- **Business Logic Attacks:**
  - Unusual transaction patterns
  - Attempts to bypass workflow steps
  - Logical constraint testing

### 5.3 Real-Time Alerting

- **Alert Prioritization:**
  - Critical: Immediate notification via multiple channels
  - High: Notification within 15 minutes
  - Medium: Daily digest
  - Low: Weekly report
- **Notification Channels:**
  - Email for all alerts
  - SMS/push for critical alerts
  - Integration with incident management systems
  - Optional webhook delivery to customer security teams

### 5.4 Automated Response

- **Immediate Actions:**
  - Temporary IP blocking for clear attack patterns
  - Challenge suspicious sessions with additional verification
  - Throttling of suspicious traffic
- **Escalation:**
  - Account lockdown for compromised credentials
  - API key revocation for misuse
  - Service isolation for targeted components

### 5.5 Security Reporting

- **Regular Reports:**
  - Daily security summary
  - Weekly trend analysis
  - Monthly comprehensive review
- **Incident Documentation:**
  - Detailed timeline of events
  - Actions taken and their effectiveness
  - Recommendations for future prevention

## 6. Implementation Guidelines

### 6.1 Authentication Implementation

- Build on existing Supabase authentication
- Implement MFA as an extension to the current auth flow
- Store device fingerprints in a separate collection with user association
- Implement account lockout through a dedicated service with Redis backing

### 6.2 Rate Limiting Implementation

- Enhance existing Redis-based rate limiting
- Create a configuration system for different limit tiers
- Implement bypass mechanism through database-stored rules
- Add notification integration for security events

### 6.3 Audit Logging Implementation

- Create a dedicated AuditLog model in the database
- Implement middleware for capturing security events
- Build admin dashboard as an extension of existing admin interface
- Ensure proper indexing for efficient querying

### 6.4 Intrusion Detection Implementation

- Start with rules-based detection for common attack patterns
- Gradually implement behavioral analysis as data accumulates
- Create a security event processing pipeline
- Implement automated response through existing notification and blocking systems

## 7. Related Documents

- `docs/functional_specs/api_gateway_routes.mdc` (API endpoints)
- `docs/functional_specs/env_configuration_rules.mdc` (Secret management)
- `docs/platform_security/webhook_reliability_and_idempotency.mdc` (Webhook security)
- `docs/architecture/multi_tenant_data_isolation.mdc` (Data isolation)
- `docs/functional_specs/notifications_and_alerts_document.mdc` (Alerting)
- `docs/compliance/gdpr_ccpa_compliance_tracking.mdc` (Compliance requirements)
- `SECURITY_AUDIT.md` (Security audit findings)
