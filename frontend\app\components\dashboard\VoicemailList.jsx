"use client";

import { useState, useRef, useEffect } from 'react';
import { ChatBubbleLeftEllipsisIcon, PlayIcon, PauseIcon } from '@heroicons/react/24/outline';

export default function VoicemailList({ voicemails = [] }) {
  const [expandedVoicemail, setExpandedVoicemail] = useState(null);
  const [playingId, setPlayingId] = useState(null);
  const audioRef = useRef(null);
  
  // Helper function to format phone numbers nicely
  const formatPhoneNumber = (phoneNumber) => {
    if (!phoneNumber) return 'Unknown';
    
    // If it already includes formatting, return as is
    if (phoneNumber.includes(' ') || phoneNumber.includes('-')) {
      return phoneNumber;
    }
    
    // Basic formatting for US numbers
    if (phoneNumber.length === 10) {
      return `(${phoneNumber.substring(0, 3)}) ${phoneNumber.substring(3, 6)}-${phoneNumber.substring(6)}`;
    }
    
    // For international format with country code
    if (phoneNumber.startsWith('+')) {
      // Keep country code, then format rest if it's a US number (+1)
      if (phoneNumber.startsWith('+1') && phoneNumber.length === 12) {
        return `+1 (${phoneNumber.substring(2, 5)}) ${phoneNumber.substring(5, 8)}-${phoneNumber.substring(8)}`;
      }
      return phoneNumber; 
    }
    
    // Default case
    return phoneNumber;
  };
  
  // Format duration in minutes and seconds
  const formatDuration = (seconds) => {
    if (!seconds && seconds !== 0) return 'N/A';
    
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Format timestamp nicely
  const formatTime = (timestamp) => {
    if (!timestamp) return 'Unknown time';
    
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffMs = now - date;
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      
      // Today
      if (diffDays === 0) {
        return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      }
      
      // Yesterday
      if (diffDays === 1) {
        return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      }
      
      // This week (less than 7 days ago)
      if (diffDays < 7) {
        return `${date.toLocaleDateString([], { weekday: 'long' })} at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      }
      
      // Older
      return date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' });
    } catch (e) {
      console.error('Error formatting date:', e);
      return timestamp;
    }
  };
  
  // Control audio playback
  const togglePlayVoicemail = (id, recordingUrl) => {
    if (playingId === id) {
      // Pause current voicemail
      if (audioRef.current) {
        audioRef.current.pause();
      }
      setPlayingId(null);
    } else {
      // Stop any previously playing voicemail
      if (audioRef.current && !audioRef.current.paused) {
        audioRef.current.pause();
      }

      // Start playing the new voicemail
      if (audioRef.current && recordingUrl) {
        audioRef.current.src = recordingUrl;
        audioRef.current.play().catch(error => {
          console.error("Error playing audio:", error);
          setPlayingId(null); // Reset state on error
        });
        setPlayingId(id);
      } else if (!recordingUrl) {
         console.warn(`No recording URL for voicemail ID: ${id}`);
         setPlayingId(null); // Reset state if no URL
      }
    }
  };

  // Effect to handle audio ending
  useEffect(() => {
    const audioElement = audioRef.current;
    const handleAudioEnd = () => {
      setPlayingId(null);
    };

    if (audioElement) {
      audioElement.addEventListener('ended', handleAudioEnd);
      return () => {
        audioElement.removeEventListener('ended', handleAudioEnd);
      };
    }
  }, []);
  
  if (!voicemails || voicemails.length === 0) {
    return (
      <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg h-full">
        <h3 className="text-lg font-medium text-white flex items-center">
          <ChatBubbleLeftEllipsisIcon className="h-5 w-5 mr-2 text-purple-400" />
          Voicemails
        </h3>
        <div className="mt-6 flex flex-col items-center justify-center text-center h-40">
          <p className="text-gray-400">No voicemails available</p>
          <p className="text-gray-500 text-sm mt-2">Missed calls with voicemails will appear here</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
      <h3 className="text-lg font-medium text-white flex items-center">
        <ChatBubbleLeftEllipsisIcon className="h-5 w-5 mr-2 text-purple-400" />
        Voicemails {voicemails.filter(vm => vm.isNew).length > 0 && (
          <span className="ml-2 px-2 py-0.5 bg-purple-600 text-white rounded-full text-xs">
            {voicemails.filter(vm => vm.isNew).length} new
          </span>
        )}
      </h3>
      
      <div className="mt-4 divide-y divide-gray-800/60">
        {voicemails.map((voicemail) => (
          <div key={voicemail.id} className={`py-3 group ${voicemail.isNew ? 'bg-purple-900/10 -mx-5 px-5' : ''}`}>
            <div 
              className="cursor-pointer"
              onClick={() => setExpandedVoicemail(expandedVoicemail === voicemail.id ? null : voicemail.id)}
            >
              <div className="flex justify-between items-start">
                <div className="flex items-start">
                  <button 
                    className="mr-3 mt-0.5 h-6 w-6 flex items-center justify-center rounded-full bg-gray-800 hover:bg-purple-700 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      togglePlayVoicemail(voicemail.id, voicemail.recordingUrl);
                    }}
                  >
                    {playingId === voicemail.id ? (
                      <PauseIcon className="h-3 w-3 text-white" />
                    ) : (
                      <PlayIcon className="h-3 w-3 text-white" />
                    )}
                  </button>
                  <div>
                    <div className="flex items-center">
                      <p className="text-white font-medium">{formatPhoneNumber(voicemail.from)}</p>
                      {voicemail.isNew && (
                        <span className="ml-2 px-2 py-0.5 bg-purple-600/30 text-purple-300 rounded-full text-xs">
                          New
                        </span>
                      )}
                    </div>
                    <p className="text-gray-400 text-xs">
                      {formatTime(voicemail.timestamp)} · {formatDuration(voicemail.duration)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            {expandedVoicemail === voicemail.id && (
              <div className="mt-3 p-3 bg-gray-800/30 rounded-lg border border-gray-700/50 text-sm">
                {voicemail.transcription && (
                  <div className="mb-3">
                    <p className="text-gray-500 text-xs mb-1">Transcription</p>
                    <p className="text-gray-200 text-sm italic">{voicemail.transcription}</p>
                  </div>
                )}
                
                <div className="flex space-x-2 mt-3">
                  <button 
                    className={`flex items-center justify-center py-1.5 px-3 ${
                      playingId === voicemail.id 
                        ? 'bg-purple-700 text-white' 
                        : 'bg-purple-600/50 hover:bg-purple-600/70 text-white'
                    } rounded text-xs font-medium transition-colors`}
                    onClick={() => togglePlayVoicemail(voicemail.id, voicemail.recordingUrl)}
                  >
                    {playingId === voicemail.id ? (
                      <>
                        <PauseIcon className="h-3 w-3 mr-1" />
                        <span>Pause</span>
                      </>
                    ) : (
                      <>
                        <PlayIcon className="h-3 w-3 mr-1" />
                        <span>Play</span>
                      </>
                    )}
                  </button>
                  <button className="flex items-center justify-center py-1.5 px-3 bg-green-600/50 hover:bg-green-600/70 text-white rounded text-xs font-medium transition-colors">
                    Call Back
                  </button>
                  <button className="flex items-center justify-center py-1.5 px-3 bg-red-600/50 hover:bg-red-600/70 text-white rounded text-xs font-medium transition-colors">
                    Delete
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
      
      {voicemails.length > 5 && (
        <div className="mt-4 text-center">
          <button className="text-sm text-purple-400 hover:text-purple-300 transition-colors">
            View All Voicemails
          </button>
        </div>
      )}
      {/* Hidden Audio Player */}
      <audio ref={audioRef} style={{ display: 'none' }} />
    </div>
  );
} 