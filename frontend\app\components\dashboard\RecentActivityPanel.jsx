"use client";

import React, { useState } from 'react';
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ArrowPathIcon, 
  PhoneIcon, 
  ClockIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/solid';

export function RecentActivityPanel() {
  const [filter, setFilter] = useState('all');
  
  // Mock data for recent activities
  const activities = [
    { 
      id: 1, 
      type: 'success', 
      callId: 'CALL-9384',
      issue: 'Network connectivity',
      timestamp: '14 min ago',
      duration: '4:32',
      resolution: 'Automated troubleshooting'
    },
    { 
      id: 2, 
      type: 'failed', 
      callId: 'CALL-9383',
      issue: 'Account access',
      timestamp: '37 min ago',
      duration: '6:18',
      resolution: 'Escalated to support'
    },
    { 
      id: 3, 
      type: 'success', 
      callId: 'CALL-9382',
      issue: 'Software update',
      timestamp: '1 hour ago',
      duration: '3:45',
      resolution: 'Guided installation'
    },
    { 
      id: 4, 
      type: 'in-progress', 
      callId: 'CALL-9381',
      issue: 'Billing inquiry',
      timestamp: '2 hours ago',
      duration: '7:12',
      resolution: 'Awaiting confirmation'
    },
    { 
      id: 5, 
      type: 'success', 
      callId: 'CALL-9380',
      issue: 'Feature request',
      timestamp: '3 hours ago',
      duration: '5:07',
      resolution: 'Added to roadmap'
    }
  ];

  // Filter activities based on selected filter
  const filteredActivities = filter === 'all' 
    ? activities 
    : activities.filter(activity => activity.type === filter);

  // Status indicator component
  const StatusIndicator = ({ type }) => {
    switch (type) {
      case 'success':
        return (
          <span className="flex items-center text-sm text-green-400">
            <CheckCircleIcon className="h-4 w-4 mr-1" />
            Resolved
          </span>
        );
      case 'failed':
        return (
          <span className="flex items-center text-sm text-red-400">
            <XCircleIcon className="h-4 w-4 mr-1" />
            Failed
          </span>
        );
      case 'in-progress':
        return (
          <span className="flex items-center text-sm text-yellow-400">
            <ArrowPathIcon className="h-4 w-4 mr-1 animate-spin" />
            In Progress
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div className="dashboard-card-blue border-glow w-full min-h-[400px] flex flex-col">
      <div className="p-4 sm:p-5 relative z-10 flex-grow flex flex-col">
        <header className="dashboard-card-header mb-4 flex-col sm:flex-row">
          <div className="flex items-center mb-3 sm:mb-0">
            <div className="title-icon-blue flex-shrink-0">
              <ChatBubbleLeftRightIcon className="h-5 w-5" />
            </div>
            <div>
              <h3 className="font-bold text-lg text-blue-400">Recent Activity</h3>
              <p className="text-gray-400 text-sm">Latest call resolution activities</p>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <button 
              onClick={() => setFilter('all')}
              className={`px-3 py-1.5 text-xs sm:text-sm rounded-lg transition-all duration-200 ${
                filter === 'all' 
                  ? 'bg-blue-600/30 text-blue-300 border border-blue-600/40' 
                  : 'text-gray-400 hover:text-gray-300 bg-gray-800/50 border border-gray-700/50 hover:border-gray-600/50'
              }`}
            >
              All
            </button>
            <button 
              onClick={() => setFilter('success')}
              className={`px-3 py-1.5 text-xs sm:text-sm rounded-lg transition-all duration-200 ${
                filter === 'success' 
                  ? 'bg-green-600/30 text-green-300 border border-green-600/40' 
                  : 'text-gray-400 hover:text-gray-300 bg-gray-800/50 border border-gray-700/50 hover:border-gray-600/50'
              }`}
            >
              Resolved
            </button>
            <button 
              onClick={() => setFilter('in-progress')}
              className={`px-3 py-1.5 text-xs sm:text-sm rounded-lg transition-all duration-200 ${
                filter === 'in-progress' 
                  ? 'bg-yellow-600/30 text-yellow-300 border border-yellow-600/40' 
                  : 'text-gray-400 hover:text-gray-300 bg-gray-800/50 border border-gray-700/50 hover:border-gray-600/50'
              }`}
            >
              In Progress
            </button>
            <button 
              onClick={() => setFilter('failed')}
              className={`px-3 py-1.5 text-xs sm:text-sm rounded-lg transition-all duration-200 ${
                filter === 'failed' 
                  ? 'bg-red-600/30 text-red-300 border border-red-600/40' 
                  : 'text-gray-400 hover:text-gray-300 bg-gray-800/50 border border-gray-700/50 hover:border-gray-600/50'
              }`}
            >
              Failed
            </button>
          </div>
        </header>

        <div className="space-y-3 mt-4 flex-grow overflow-y-auto pr-1 max-h-[300px]">
          {filteredActivities.map((activity) => (
            <div 
              key={activity.id} 
              className="bg-gray-800/30 border border-gray-700/50 rounded-lg p-3 hover:border-gray-600/70 transition-colors duration-200 border-glow"
            >
              <div className="flex flex-col sm:flex-row sm:items-center">
                <div className="flex-1 mb-2 sm:mb-0">
                  <div className="flex items-start">
                    <div className="rounded-lg p-2 bg-gray-900/70 mr-3">
                      <PhoneIcon className="h-4 w-4 text-blue-400" />
                    </div>
                    <div>
                      <div className="flex items-center">
                        <span className="font-medium text-white mr-2">{activity.callId}</span>
                        <StatusIndicator type={activity.type} />
                      </div>
                      <p className="text-gray-300 text-sm mt-1">{activity.issue}</p>
                    </div>
                  </div>
                </div>
                
                <div className="flex flex-row sm:flex-col items-start sm:items-end sm:min-w-[120px]">
                  <div className="flex items-center mr-4 sm:mr-0 sm:mb-1">
                    <ClockIcon className="h-3.5 w-3.5 text-blue-400 mr-1" />
                    <span className="text-gray-400 text-xs">{activity.duration}</span>
                  </div>
                  <span className="text-gray-500 text-xs">{activity.timestamp}</span>
                </div>
              </div>
              
              {activity.resolution && (
                <div className="mt-2 pt-2 border-t border-gray-700/30">
                  <p className="text-xs text-gray-400">
                    <span className="text-gray-500">Resolution:</span> {activity.resolution}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>

        {filteredActivities.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500">No activities matching your filter</p>
          </div>
        )}
      </div>
      <div className="card-glow"></div>
    </div>
  );
}