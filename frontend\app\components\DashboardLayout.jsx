import { CogIcon } from '@heroicons/react/24/outline';

  {/* Settings Link */}
  <a
    href="/dashboard/settings"
    className={`flex items-center space-x-2 ${
      pathname.startsWith('/dashboard/settings') ? 'text-white' : 'text-gray-400 hover:text-white'
    } transition-colors duration-200 py-2 px-3 rounded-lg`}
  >
    <CogIcon className="h-5 w-5" />
    <span>Settings</span>
  </a>

  {/* Add nested links if on the settings page */}
  {pathname.startsWith('/dashboard/settings') && (
    <div className="ml-6 mt-2 space-y-2 border-l border-gray-700 pl-3">
      <a
        href="/dashboard/settings/profile"
        className={`block text-sm ${
          pathname === '/dashboard/settings/profile' ? 'text-purple-400' : 'text-gray-400 hover:text-gray-300'
        } transition-colors duration-200 py-1`}
      >
        Profile
      </a>
      <a
        href="/dashboard/settings/billing"
        className={`block text-sm ${
          pathname === '/dashboard/settings/billing' ? 'text-purple-400' : 'text-gray-400 hover:text-gray-300'
        } transition-colors duration-200 py-1`}
      >
        Billing
      </a>
      <a
        href="/dashboard/settings/notifications"
        className={`block text-sm ${
          pathname === '/dashboard/settings/notifications' ? 'text-purple-400' : 'text-gray-400 hover:text-gray-300'
        } transition-colors duration-200 py-1`}
      >
        Notifications
      </a>
      <a
        href="/dashboard/settings/train-ai"
        className={`block text-sm ${
          pathname === '/dashboard/settings/train-ai' ? 'text-purple-400' : 'text-gray-400 hover:text-gray-300'
        } transition-colors duration-200 py-1`}
      >
        Train AI
      </a>
    </div>
  )} 