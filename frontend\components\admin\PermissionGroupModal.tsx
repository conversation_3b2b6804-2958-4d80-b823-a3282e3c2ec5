'use client';

import { useState, Fragment, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { PermissionGroup, Permission, useRoleManagement } from '../../hooks/useRoleManagement';
import { Spinner } from '../ui/Spinner';

interface PermissionGroupModalProps {
  isOpen: boolean;
  onClose: () => void;
  group: PermissionGroup | null;
  allPermissions: Permission[];
}

export default function PermissionGroupModal({
  isOpen,
  onClose,
  group,
  allPermissions,
}: PermissionGroupModalProps) {
  // Form state
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [displayOrder, setDisplayOrder] = useState<number>(0);
  const [selectedPermissionIds, setSelectedPermissionIds] = useState<string[]>([]);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [searchTerm, setSearchTerm] = useState('');

  // Get role management functions
  const { createPermissionGroup, updatePermissionGroup, isCreatingPermissionGroup, isUpdatingPermissionGroup } = useRoleManagement();

  // Reset form when modal opens/closes or group changes
  useEffect(() => {
    if (isOpen) {
      if (group) {
        setName(group.name);
        setDescription(group.description || '');
        setDisplayOrder(group.displayOrder);
        setSelectedPermissionIds(group.permissions.map(p => p.id));
      } else {
        setName('');
        setDescription('');
        setDisplayOrder(0);
        setSelectedPermissionIds([]);
      }
      setSearchTerm('');
      setFormErrors({});
    }
  }, [isOpen, group]);

  // Validate form
  const validateForm = (): boolean => {
    const errors: { [key: string]: string } = {};

    if (!name.trim()) {
      errors.name = 'Group name is required';
    } else if (name.length < 2) {
      errors.name = 'Group name must be at least 2 characters';
    } else if (name.length > 50) {
      errors.name = 'Group name must be less than 50 characters';
    }

    if (description && description.length > 200) {
      errors.description = 'Description must be less than 200 characters';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const groupData = {
      name: name.trim(),
      description: description.trim() || undefined,
      displayOrder,
      permissionIds: selectedPermissionIds,
    };

    if (group) {
      // Update existing group
      updatePermissionGroup({
        groupId: group.id,
        data: groupData,
      }, {
        onSuccess: () => {
          onClose();
        },
      });
    } else {
      // Create new group
      createPermissionGroup(groupData, {
        onSuccess: () => {
          onClose();
        },
      });
    }
  };

  // Handle permission toggle
  const handlePermissionToggle = (permissionId: string) => {
    if (selectedPermissionIds.includes(permissionId)) {
      setSelectedPermissionIds(selectedPermissionIds.filter(id => id !== permissionId));
    } else {
      setSelectedPermissionIds([...selectedPermissionIds, permissionId]);
    }
  };

  // Filter permissions by search term
  const filteredPermissions = searchTerm
    ? allPermissions.filter(
        p =>
          p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          p.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          p.resource.toLowerCase().includes(searchTerm.toLowerCase()) ||
          p.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
          p.scope.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : allPermissions;

  // Group permissions by resource
  const permissionsByResource = filteredPermissions.reduce<{ [key: string]: Permission[] }>(
    (acc, permission) => {
      if (!acc[permission.resource]) {
        acc[permission.resource] = [];
      }
      acc[permission.resource].push(permission);
      return acc;
    },
    {}
  );

  const isSubmitting = isCreatingPermissionGroup || isUpdatingPermissionGroup;

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6">
                <div className="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
                  <button
                    type="button"
                    className="rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={onClose}
                    disabled={isSubmitting}
                  >
                    <span className="sr-only">Close</span>
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left w-full">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-semibold leading-6 text-gray-900 dark:text-white"
                    >
                      {group ? 'Edit Permission Group' : 'Create Permission Group'}
                    </Dialog.Title>
                    <div className="mt-4">
                      <form onSubmit={handleSubmit} className="space-y-4">
                        {/* Group Name */}
                        <div>
                          <label
                            htmlFor="name"
                            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                          >
                            Group Name <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            id="name"
                            name="name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            className={`mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm ${
                              formErrors.name ? 'border-red-500' : ''
                            }`}
                            placeholder="e.g., User Management"
                            disabled={isSubmitting}
                          />
                          {formErrors.name && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-500">
                              {formErrors.name}
                            </p>
                          )}
                        </div>

                        {/* Description */}
                        <div>
                          <label
                            htmlFor="description"
                            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                          >
                            Description
                          </label>
                          <textarea
                            id="description"
                            name="description"
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            rows={2}
                            className={`mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm ${
                              formErrors.description ? 'border-red-500' : ''
                            }`}
                            placeholder="Describe the purpose of this permission group"
                            disabled={isSubmitting}
                          />
                          {formErrors.description && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-500">
                              {formErrors.description}
                            </p>
                          )}
                        </div>

                        {/* Display Order */}
                        <div>
                          <label
                            htmlFor="displayOrder"
                            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                          >
                            Display Order
                          </label>
                          <input
                            type="number"
                            id="displayOrder"
                            name="displayOrder"
                            value={displayOrder}
                            onChange={(e) => setDisplayOrder(parseInt(e.target.value) || 0)}
                            className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                            min="0"
                            disabled={isSubmitting}
                          />
                          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Lower numbers appear first in the list
                          </p>
                        </div>

                        {/* Permissions */}
                        <div>
                          <label
                            htmlFor="permissions"
                            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                          >
                            Permissions
                          </label>

                          {/* Search */}
                          <div className="mb-2">
                            <input
                              type="text"
                              placeholder="Search permissions..."
                              className="w-full p-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-900 rounded-md"
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              disabled={isSubmitting}
                            />
                          </div>

                          {/* Permission list */}
                          <div className="max-h-60 overflow-y-auto border border-gray-300 dark:border-gray-700 rounded-md p-2">
                            {Object.entries(permissionsByResource).map(([resource, permissions]) => (
                              <div key={resource} className="mb-3">
                                <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300 mb-1">
                                  {resource}
                                </h4>
                                <div className="pl-2 space-y-1">
                                  {permissions.map((permission) => (
                                    <div key={permission.id} className="flex items-center">
                                      <input
                                        type="checkbox"
                                        id={`permission-${permission.id}`}
                                        checked={selectedPermissionIds.includes(permission.id)}
                                        onChange={() => handlePermissionToggle(permission.id)}
                                        className="h-4 w-4 text-blue-600 rounded"
                                        disabled={isSubmitting}
                                      />
                                      <label
                                        htmlFor={`permission-${permission.id}`}
                                        className="ml-2 text-sm cursor-pointer"
                                      >
                                        <span className="font-medium">
                                          {permission.displayName || permission.name}
                                        </span>
                                        <span className="text-xs text-gray-500 ml-1">
                                          ({permission.action}
                                          {permission.scope !== 'any' ? `:${permission.scope}` : ''})
                                        </span>
                                      </label>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ))}

                            {Object.keys(permissionsByResource).length === 0 && (
                              <div className="text-center py-4 text-gray-500">
                                No permissions found
                              </div>
                            )}
                          </div>
                          <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Selected: {selectedPermissionIds.length} permissions
                          </div>
                        </div>

                        {/* Form Actions */}
                        <div className="mt-5 sm:mt-6 sm:flex sm:flex-row-reverse">
                          <button
                            type="submit"
                            className="inline-flex w-full justify-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 sm:ml-3 sm:w-auto"
                            disabled={isSubmitting}
                          >
                            {isSubmitting ? (
                              <Spinner size="sm" className="mr-2" />
                            ) : null}
                            {group ? 'Update Group' : 'Create Group'}
                          </button>
                          <button
                            type="button"
                            className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-700 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 sm:mt-0 sm:w-auto"
                            onClick={onClose}
                            disabled={isSubmitting}
                          >
                            Cancel
                          </button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
}
