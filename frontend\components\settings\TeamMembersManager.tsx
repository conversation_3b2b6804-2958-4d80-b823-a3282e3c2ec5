'use client';

import { useState } from 'react';
import Image from 'next/image';
import { 
  TeamMember, 
  useInviteTeamMember, 
  useUpdateTeamMemberRole, 
  useRemoveTeamMember 
} from '../../../hooks/useSettings';

interface TeamMembersManagerProps {
  teamMembers: TeamMember[];
  onSuccess: (message: string) => void;
}

export default function TeamMembersManager({ 
  teamMembers, 
  onSuccess 
}: TeamMembersManagerProps) {
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [memberToRemove, setMemberToRemove] = useState<string | null>(null);
  
  // Invite team member mutation
  const inviteTeamMember = useInviteTeamMember();
  
  // Update team member role mutation
  const updateTeamMemberRole = useUpdateTeamMemberRole();
  
  // Remove team member mutation
  const removeTeamMember = useRemoveTeamMember();
  
  // Form state
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('member');
  
  // Handle invite team member
  const handleInviteTeamMember = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      await inviteTeamMember.mutateAsync({
        email: inviteEmail,
        role: inviteRole
      });
      
      setInviteEmail('');
      setInviteRole('member');
      setShowInviteForm(false);
      onSuccess('Team member invited successfully');
    } catch (err) {
      // Error is handled by the mutation
      console.error('Error inviting team member:', err);
    }
  };
  
  // Handle update team member role
  const handleUpdateTeamMemberRole = async (memberId: string, role: string) => {
    try {
      await updateTeamMemberRole.mutateAsync({ memberId, role });
      onSuccess('Team member role updated successfully');
    } catch (err) {
      // Error is handled by the mutation
      console.error('Error updating team member role:', err);
    }
  };
  
  // Handle remove team member
  const handleRemoveTeamMember = async (memberId: string) => {
    setMemberToRemove(memberId);
    
    try {
      await removeTeamMember.mutateAsync(memberId);
      onSuccess('Team member removed successfully');
    } catch (err) {
      // Error is handled by the mutation
      console.error('Error removing team member:', err);
    } finally {
      setMemberToRemove(null);
    }
  };
  
  // Get role label
  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Admin';
      case 'member':
        return 'Member';
      case 'billing':
        return 'Billing';
      default:
        return role;
    }
  };
  
  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
            Active
          </span>
        );
      case 'invited':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
            Invited
          </span>
        );
      case 'disabled':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
            Disabled
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
            {status}
          </span>
        );
    }
  };
  
  return (
    <div>
      {/* Error Messages */}
      {inviteTeamMember.isError && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-800 dark:text-red-400 text-sm">
          {inviteTeamMember.error instanceof Error 
            ? inviteTeamMember.error.message 
            : 'Failed to invite team member. Please try again.'}
        </div>
      )}
      
      {updateTeamMemberRole.isError && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-800 dark:text-red-400 text-sm">
          {updateTeamMemberRole.error instanceof Error 
            ? updateTeamMemberRole.error.message 
            : 'Failed to update team member role. Please try again.'}
        </div>
      )}
      
      {removeTeamMember.isError && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-800 dark:text-red-400 text-sm">
          {removeTeamMember.error instanceof Error 
            ? removeTeamMember.error.message 
            : 'Failed to remove team member. Please try again.'}
        </div>
      )}
      
      {/* Team Members List */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Team Members</h4>
          <button
            type="button"
            onClick={() => setShowInviteForm(true)}
            className="px-3 py-1.5 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Invite Member
          </button>
        </div>
        
        {teamMembers.length === 0 ? (
          <div className="text-center py-8 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <p className="text-gray-500 dark:text-gray-400">
              No team members yet. Invite someone to get started.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Member
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Role
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Joined
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {teamMembers.map((member) => (
                  <tr key={member.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 relative">
                          {member.avatarUrl ? (
                            <Image 
                              src={member.avatarUrl} 
                              alt={member.name} 
                              width={40} 
                              height={40} 
                              className="rounded-full"
                            />
                          ) : (
                            <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                              <span className="text-gray-500 dark:text-gray-400 font-medium">
                                {member.name.charAt(0).toUpperCase()}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {member.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {member.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <select
                        value={member.role}
                        onChange={(e) => handleUpdateTeamMemberRole(member.id, e.target.value)}
                        disabled={updateTeamMemberRole.isPending}
                        className="block w-full pl-3 pr-10 py-1.5 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="admin">Admin</option>
                        <option value="member">Member</option>
                        <option value="billing">Billing</option>
                      </select>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(member.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {member.joinedAt 
                        ? new Date(member.joinedAt).toLocaleDateString() 
                        : 'Not joined yet'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        type="button"
                        onClick={() => handleRemoveTeamMember(member.id)}
                        disabled={removeTeamMember.isPending && memberToRemove === member.id}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {removeTeamMember.isPending && memberToRemove === member.id 
                          ? 'Removing...' 
                          : 'Remove'}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      
      {/* Invite Form */}
      {showInviteForm && (
        <div className="mt-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Invite Team Member
          </h4>
          
          <form onSubmit={handleInviteTeamMember}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label htmlFor="inviteEmail" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Email Address
                </label>
                <input
                  id="inviteEmail"
                  type="email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  required
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <label htmlFor="inviteRole" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Role
                </label>
                <select
                  id="inviteRole"
                  value={inviteRole}
                  onChange={(e) => setInviteRole(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="admin">Admin</option>
                  <option value="member">Member</option>
                  <option value="billing">Billing</option>
                </select>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowInviteForm(false)}
                className="px-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={inviteTeamMember.isPending || !inviteEmail}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {inviteTeamMember.isPending ? 'Sending Invite...' : 'Send Invite'}
              </button>
            </div>
          </form>
        </div>
      )}
      
      {/* Role Descriptions */}
      <div className="mt-6 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Role Permissions</h4>
        <ul className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
          <li className="flex items-start">
            <span className="font-medium text-gray-700 dark:text-gray-300 mr-2">Admin:</span>
            Full access to all settings, billing, and team management.
          </li>
          <li className="flex items-start">
            <span className="font-medium text-gray-700 dark:text-gray-300 mr-2">Member:</span>
            Can use the application but cannot modify organization settings or billing.
          </li>
          <li className="flex items-start">
            <span className="font-medium text-gray-700 dark:text-gray-300 mr-2">Billing:</span>
            Can manage billing, subscriptions, and payment methods but not other organization settings.
          </li>
        </ul>
      </div>
    </div>
  );
}
