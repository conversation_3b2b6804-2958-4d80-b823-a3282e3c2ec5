import { renderHook, act } from '@testing-library/react-hooks';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { usePermissions, useHasPermission, useHasAnyPermission, useHasAllPermissions } from '../../hooks/usePermissions';
import { useAuthStore } from '../../stores/authStore';
import { api } from '../../lib/apiClient';

// Mock dependencies
jest.mock('../../lib/apiClient', () => ({
  api: {
    get: jest.fn()
  }
}));

jest.mock('../../stores/authStore', () => ({
  useAuthStore: jest.fn()
}));

describe('Permission Hooks', () => {
  // Setup query client for testing
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0
      }
    }
  });
  
  const wrapper = ({ children }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
  
  beforeEach(() => {
    jest.clearAllMocks();
    queryClient.clear();
    
    // Mock the auth store
    useAuthStore.mockImplementation(() => ({
      user: { id: 'user1', role: 'admin' },
      permissions: ['**', 'users:read:any', 'phoneNumbers:*:self'],
      hasPermission: jest.fn().mockImplementation((permission) => {
        const permissions = ['**', 'users:read:any', 'phoneNumbers:*:self'];
        return permissions.includes(permission) || permission.startsWith('phoneNumbers:');
      }),
      hasAnyPermission: jest.fn().mockImplementation((permissions) => {
        return permissions.some(p => p === 'users:read:any' || p.startsWith('phoneNumbers:'));
      }),
      hasAllPermissions: jest.fn().mockImplementation((permissions) => {
        return permissions.every(p => p === 'users:read:any' || p.startsWith('phoneNumbers:'));
      })
    }));
    
    // Mock the API response
    api.get.mockResolvedValue({
      success: true,
      data: {
        permissions: ['**', 'users:read:any', 'phoneNumbers:*:self']
      }
    });
  });
  
  describe('usePermissions', () => {
    test('should return permission checking functions', () => {
      const { result } = renderHook(() => usePermissions(), { wrapper });
      
      expect(result.current.hasPermission).toBeDefined();
      expect(result.current.hasAnyPermission).toBeDefined();
      expect(result.current.hasAllPermissions).toBeDefined();
    });
    
    test('should check if user has a specific permission', () => {
      const { result } = renderHook(() => usePermissions(), { wrapper });
      
      expect(result.current.hasPermission('users:read:any')).toBe(true);
      expect(result.current.hasPermission('phoneNumbers:create:self')).toBe(true);
      expect(result.current.hasPermission('users:delete:any')).toBe(false);
    });
    
    test('should check if user has any of the specified permissions', () => {
      const { result } = renderHook(() => usePermissions(), { wrapper });
      
      expect(result.current.hasAnyPermission(['users:read:any', 'users:delete:any'])).toBe(true);
      expect(result.current.hasAnyPermission(['phoneNumbers:create:self', 'phoneNumbers:delete:self'])).toBe(true);
      expect(result.current.hasAnyPermission(['users:delete:any', 'users:create:any'])).toBe(false);
    });
    
    test('should check if user has all of the specified permissions', () => {
      const { result } = renderHook(() => usePermissions(), { wrapper });
      
      expect(result.current.hasAllPermissions(['users:read:any', 'phoneNumbers:create:self'])).toBe(true);
      expect(result.current.hasAllPermissions(['users:read:any', 'users:delete:any'])).toBe(false);
    });
  });
  
  describe('useHasPermission', () => {
    test('should return true if user has the permission', () => {
      const { result } = renderHook(() => useHasPermission('users:read:any'), { wrapper });
      
      expect(result.current).toBe(true);
    });
    
    test('should return false if user does not have the permission', () => {
      const { result } = renderHook(() => useHasPermission('users:delete:any'), { wrapper });
      
      expect(result.current).toBe(false);
    });
  });
  
  describe('useHasAnyPermission', () => {
    test('should return true if user has any of the permissions', () => {
      const { result } = renderHook(() => useHasAnyPermission(['users:read:any', 'users:delete:any']), { wrapper });
      
      expect(result.current).toBe(true);
    });
    
    test('should return false if user has none of the permissions', () => {
      const { result } = renderHook(() => useHasAnyPermission(['users:delete:any', 'users:create:any']), { wrapper });
      
      expect(result.current).toBe(false);
    });
  });
  
  describe('useHasAllPermissions', () => {
    test('should return true if user has all of the permissions', () => {
      const { result } = renderHook(() => useHasAllPermissions(['users:read:any', 'phoneNumbers:create:self']), { wrapper });
      
      expect(result.current).toBe(true);
    });
    
    test('should return false if user is missing any of the permissions', () => {
      const { result } = renderHook(() => useHasAllPermissions(['users:read:any', 'users:delete:any']), { wrapper });
      
      expect(result.current).toBe(false);
    });
  });
});
