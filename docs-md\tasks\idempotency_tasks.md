# Idempotency & Failure Recovery Tasks

## Task Queue

### Task 1
- **Task description:** Add idempotency keys to webhook handlers (<PERSON><PERSON>, <PERSON><PERSON><PERSON>).
- **Priority:** High
- **Target file/component:** `back/backend/webhooks/stripeHandler.js`, `back/backend/webhooks/twilioHandler.js` (Assuming these paths)
- **Dependencies:** None
- **Status:** TODO
- **Tags:** #idempotency #webhook #stripe #twilio #reliability

### Task 2
- **Task description:** Implement robust retry logic and a dead-letter queue for webhook processing jobs.
- **Priority:** High
- **Target file/component:** `lib/taskQueue/webhookQueue.js` (or `back/backend/queues/webhookQueue.js`), BullMQ configuration
- **Dependencies:** None
- **Status:** TODO
- **Tags:** #idempotency #failure_recovery #webhook #queue #bullmq #reliability

### Task 3
- **Task description:** Add an alert system for failed AI processing jobs.
- **Priority:** Medium
- **Target file/component:** `back/backend/services/aiJobMonitor.js` (or `back/backend/ai/jobs/monitor.js`), Alerting infrastructure (e.g., PagerDuty, Slack integration)
- **Dependencies:** Task 2 (Potentially leverages similar queue monitoring)
- **Status:** TODO
- **Tags:** #failure_recovery #ai #jobs #monitoring #alerting #reliability
