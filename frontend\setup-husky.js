/**
 * <PERSON><PERSON><PERSON> to set up <PERSON><PERSON> and lint-staged for pre-commit hooks
 */
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Setting up <PERSON>sky and lint-staged for pre-commit hooks...');

try {
  // Install Husky and lint-staged
  console.log('Installing <PERSON>sky and lint-staged...');
  execSync('npm install --save-dev husky lint-staged', { stdio: 'inherit' });

  // Add prepare script to package.json
  console.log('Adding prepare script to package.json...');
  const packageJsonPath = path.join(__dirname, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  if (!packageJson.scripts) {
    packageJson.scripts = {};
  }
  
  packageJson.scripts.prepare = 'husky';
  
  // Add lint-staged configuration
  packageJson['lint-staged'] = {
    '*.{js,jsx,ts,tsx}': ['eslint --fix'],
    '*.{json,md}': ['prettier --write']
  };
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));

  // Initialize Husky
  console.log('Initializing Husky...');
  execSync('npx husky init', { stdio: 'inherit' });

  // Create pre-commit hook
  console.log('Creating pre-commit hook...');
  const preCommitPath = path.join(__dirname, '.husky', 'pre-commit');
  fs.writeFileSync(preCommitPath, `#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx lint-staged
`);
  
  // Make pre-commit hook executable
  execSync(`chmod +x ${preCommitPath}`, { stdio: 'inherit' });

  console.log('Successfully set up Husky and lint-staged for pre-commit hooks!');
} catch (error) {
  console.error('Error setting up Husky and lint-staged:', error.message);
  process.exit(1);
}
