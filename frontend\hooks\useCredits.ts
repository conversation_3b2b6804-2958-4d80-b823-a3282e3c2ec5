import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../lib/apiClient';

// Types for Credits and Billing data
export interface CreditBalance {
  available: number;
  pending: number;
  reserved: number;
  updatedAt: string;
}

export interface CreditTransaction {
  id: string;
  amount: number; // Positive for additions, negative for deductions
  type: 'purchase' | 'subscription' | 'usage_call' | 'usage_sms' | 'usage_ai' | 'usage_number' | 'usage_esim' | 'refund' | 'adjustment';
  description: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  createdAt: string;
  balanceAfter?: number;
  relatedEntityType?: string;
  relatedEntityId?: string;
}

export interface Subscription {
  id: string;
  status: 'active' | 'trialing' | 'past_due' | 'canceled' | 'incomplete' | 'incomplete_expired';
  plan: string;
  interval: 'month' | 'year';
  currentPeriodEnd: string;
  trialEndsAt?: string;
  cancelAtPeriodEnd: boolean;
  creditsPerPeriod: number;
}

export interface CreditPackage {
  id: string;
  name: string;
  description: string;
  credits: number;
  price: number; // In cents
  currency: string;
  popular?: boolean;
  stripePriceId: string;
}

export interface TransactionListResponse {
  transactions: CreditTransaction[];
  totalCount: number;
}

export interface TransactionFilters {
  type?: string | string[];
  status?: string | string[];
  startDate?: string;
  endDate?: string;
  limit?: number;
  page?: number;
}

/**
 * Hook for managing credits and billing
 */
export function useCredits() {
  const queryClient = useQueryClient();
  
  // Get current credit balance
  const creditBalanceQuery = useQuery({
    queryKey: ['credits', 'balance'],
    queryFn: () => api.get<CreditBalance>('/credits/balance'),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // Get credit transactions (history)
  const getCreditTransactions = (filters: TransactionFilters = {}) => {
    return useQuery({
      queryKey: ['credits', 'transactions', filters],
      queryFn: () => api.get<TransactionListResponse>('/credits/transactions', filters),
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };
  
  // Get available credit packages
  const creditPackagesQuery = useQuery({
    queryKey: ['credits', 'packages'],
    queryFn: () => api.get<CreditPackage[]>('/credits/packages'),
    staleTime: 60 * 60 * 1000, // 1 hour (these rarely change)
  });
  
  // Get current subscription
  const subscriptionQuery = useQuery({
    queryKey: ['subscription'],
    queryFn: () => api.get<Subscription>('/subscription'),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
  
  // Start checkout process for credit purchase
  const purchaseCreditsMutation = useMutation({
    mutationFn: ({ packageId, quantity = 1 }: { packageId: string, quantity?: number }) => {
      return api.post<{ 
        success: boolean, 
        checkoutUrl: string, 
        sessionId: string 
      }>('/credits/purchase', { packageId, quantity });
    },
  });
  
  // Update subscription
  const updateSubscriptionMutation = useMutation({
    mutationFn: ({ plan, interval }: { plan: string, interval: 'month' | 'year' }) => {
      return api.post<{ 
        success: boolean, 
        checkoutUrl: string, 
        sessionId: string 
      }>('/subscription/update', { plan, interval });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
    },
  });
  
  // Cancel subscription
  const cancelSubscriptionMutation = useMutation({
    mutationFn: ({ atPeriodEnd = true }: { atPeriodEnd?: boolean }) => {
      return api.post<{ success: boolean }>('/subscription/cancel', { atPeriodEnd });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
    },
  });
  
  // Resume subscription
  const resumeSubscriptionMutation = useMutation({
    mutationFn: () => {
      return api.post<{ success: boolean }>('/subscription/resume');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
    },
  });
  
  return {
    // Queries
    balance: {
      data: creditBalanceQuery.data,
      isLoading: creditBalanceQuery.isLoading,
      error: creditBalanceQuery.error,
    },
    getCreditTransactions,
    packages: {
      data: creditPackagesQuery.data,
      isLoading: creditPackagesQuery.isLoading,
      error: creditPackagesQuery.error,
    },
    subscription: {
      data: subscriptionQuery.data,
      isLoading: subscriptionQuery.isLoading,
      error: subscriptionQuery.error,
    },
    
    // Mutations
    purchaseCredits: purchaseCreditsMutation.mutate,
    isPurchasing: purchaseCreditsMutation.isPending,
    purchaseError: purchaseCreditsMutation.error,
    purchaseResult: purchaseCreditsMutation.data,
    
    updateSubscription: updateSubscriptionMutation.mutate,
    isUpdatingSubscription: updateSubscriptionMutation.isPending,
    updateSubscriptionError: updateSubscriptionMutation.error,
    
    cancelSubscription: cancelSubscriptionMutation.mutate,
    isCanceling: cancelSubscriptionMutation.isPending,
    cancelError: cancelSubscriptionMutation.error,
    
    resumeSubscription: resumeSubscriptionMutation.mutate,
    isResuming: resumeSubscriptionMutation.isPending,
    resumeError: resumeSubscriptionMutation.error,
    
    // Refresh
    refreshBalance: () => {
      queryClient.invalidateQueries({ queryKey: ['credits', 'balance'] });
    },
    refreshTransactions: () => {
      queryClient.invalidateQueries({ queryKey: ['credits', 'transactions'] });
    },
    refreshSubscription: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] });
    },
  };
}
