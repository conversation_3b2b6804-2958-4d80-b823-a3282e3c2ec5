"use client";

import { useState, useEffect, useRef } from 'react';
import { motion, useAnimation } from 'framer-motion';
import ChatBubble from './ChatBubble';
import TypingIndicator from './TypingIndicator';
import ActionButton from './ActionButton';

export default function CarouselSlide({ data, isDragging }) {
  const [visibleMessages, setVisibleMessages] = useState([]);
  const [isTyping, setIsTyping] = useState(false);
  const [typingMessageIndex, setTypingMessageIndex] = useState(0);
  const messagesEndRef = useRef(null);
  const controls = useAnimation();

  // Animate the ChatBubbles sequentially
  useEffect(() => {
    if (isDragging) return; // Don't animate when dragging

    async function animateMessages() {
      let currentIndex = 0;
      
      while (currentIndex < data.messages.length) {
        // Start showing typing indicator for AI messages
        if (data.messages[currentIndex].sender === 'ai') {
          setIsTyping(true);
          setTypingMessageIndex(currentIndex);
          
          // Show typing indicator for a duration based on message length
          const typingDuration = Math.min(
            1000 + data.messages[currentIndex].text.length * 20, 
            2500
          );
          
          await new Promise(resolve => setTimeout(resolve, typingDuration));
          setIsTyping(false);
        } else if (data.messages[currentIndex].sender === 'user') {
          // User messages appear with a shorter delay
          await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        // Add the message to visible messages
        setVisibleMessages(prev => [...prev, data.messages[currentIndex]]);
        
        // Scroll to bottom
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
        
        // Wait before showing next message
        await new Promise(resolve => setTimeout(resolve, 700));
        
        currentIndex++;
      }
    }

    // Reset and start animation
    setVisibleMessages([]);
    animateMessages();
    
    // Clean up on unmount or slide change
    return () => {
      setVisibleMessages([]);
      setIsTyping(false);
    };
  }, [data, isDragging]);

  // Scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [visibleMessages]);

  return (
    <div className="w-full max-w-md mx-auto bg-[#111125] rounded-2xl overflow-hidden shadow-2xl">
      {/* Header */}
      <div className="bg-[#1a1a35] p-4 flex items-center justify-between">
        <div className="flex items-center">
          <div className="bg-purple-600 rounded-full w-10 h-10 flex items-center justify-center mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <h3 className="text-white font-bold text-lg">CallSaver Assistant</h3>
            <p className="text-gray-400 text-sm">Always active</p>
          </div>
        </div>
        <div className="flex items-center">
          <span className="relative flex h-3 w-3">
            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
            <span className="relative inline-flex rounded-full h-3 w-3 bg-green-500"></span>
          </span>
        </div>
      </div>
      
      {/* Missed call alert */}
      {data.missedCallAlert && (
        <motion.div 
          className="bg-[#331418] p-4 border-l-4 border-red-500 flex items-start"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <div className="bg-red-500/30 rounded-full p-2 mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <div>
            <h4 className="text-white font-medium">Missed {data.missedCallAlert.type} Call</h4>
            <p className="text-gray-400 text-sm">{data.missedCallAlert.detail}</p>
          </div>
        </motion.div>
      )}
      
      {/* Category tags */}
      {data.categories && data.categories.length > 0 && (
        <div className="px-4 pt-4 pb-2 flex flex-wrap gap-2">
          {data.categories.map((category, index) => (
            <motion.span
              key={index}
              className="bg-purple-700/30 text-purple-300 text-xs px-3 py-1 rounded-full flex items-center"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 + index * 0.1 }}
            >
              {category.icon && (
                <span className="mr-1">{category.icon}</span>
              )}
              {category.label}
            </motion.span>
          ))}
        </div>
      )}
      
      {/* Chat messages */}
      <div className="px-4 py-4 h-80 overflow-y-auto hide-scrollbar" style={{ scrollbarWidth: 'none' }}>
        {data.context && (
          <motion.div
            className="text-center my-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 0.6 }}
            transition={{ delay: 0.2 }}
          >
            <p className="text-xs text-gray-500">{data.context}</p>
          </motion.div>
        )}
        
        {visibleMessages.map((message, index) => (
          <ChatBubble
            key={index}
            message={message}
            isLast={index === visibleMessages.length - 1}
          />
        ))}
        
        {isTyping && (
          <TypingIndicator sender="ai" />
        )}
        
        <div ref={messagesEndRef} />
      </div>
      
      {/* Action buttons */}
      {data.actionButtons && data.actionButtons.length > 0 && (
        <motion.div 
          className="p-4 bg-[#1a1a35]/50 flex justify-center gap-3 flex-wrap"
          initial={{ opacity: 0, y: 20 }}
          animate={{ 
            opacity: visibleMessages.length === data.messages.length ? 1 : 0,
            y: visibleMessages.length === data.messages.length ? 0 : 20
          }}
          transition={{ delay: 0.5 }}
        >
          {data.actionButtons.map((button, index) => (
            <ActionButton
              key={index}
              icon={button.icon}
              label={button.label}
              primary={button.primary}
            />
          ))}
        </motion.div>
      )}
      
      {/* AI assistant status */}
      <motion.div 
        className="p-3 bg-[#1a1a35] border-t border-gray-800 flex items-center justify-between text-xs text-gray-400"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.8 }}
      >
        <div className="flex items-center">
          <span className="relative flex h-2 w-2 mr-2">
            <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
            <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
          </span>
          <span>AI assistant is online</span>
        </div>
        
        <div>100% client satisfaction rating</div>
      </motion.div>
    </div>
  );
} 