/**
 * Data Usage Chart Component
 * 
 * This component displays data usage statistics and charts 
 * for an eSIM profile.
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../../hooks/useToast';
import { Card, Button, Spinner, Alert, Modal, Tabs, TabPanel } from '../../components/ui';
import { formatBytes, formatDate } from '../../utils/formatting';

// We would typically use a charting library like Chart.js or Recharts
// This is a simple placeholder implementation
const ProgressBar = ({ value, max, color = 'blue' }) => {
  const percentage = max === -1 ? 0 : Math.min(100, Math.round((value / max) * 100));
  
  return (
    <div className="relative pt-1">
      <div className="flex items-center justify-between mb-2">
        <div>
          <span className="text-xs font-semibold inline-block text-gray-600">
            {max === -1 ? 'Unlimited Data' : `${percentage}% Used`}
          </span>
        </div>
        <div className="text-right">
          <span className="text-xs font-semibold inline-block text-gray-600">
            {formatBytes(value)} / {max === -1 ? '∞' : formatBytes(max)}
          </span>
        </div>
      </div>
      <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
        <div
          style={{ width: `${percentage}%` }}
          className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-${color}-500`}
        ></div>
      </div>
    </div>
  );
};

export function DataUsageChart({ profile, onClose }) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [usageData, setUsageData] = useState(null);
  const [timeRange, setTimeRange] = useState('week');
  
  const toast = useToast();
  const { apiClient } = useAuth();
  
  // Fetch usage data on component mount
  useEffect(() => {
    fetchUsageData();
  }, []);
  
  // Fetch data usage for the profile
  const fetchUsageData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClient.get(`/api/esim/profiles/${profile.id}/usage`);
      
      if (response.data.success) {
        setUsageData(response.data.data);
      } else {
        setError(response.data.message || 'Failed to load usage data');
      }
    } catch (err) {
      setError(err.message || 'An error occurred while loading usage data');
      toast.error('Failed to load data usage statistics');
    } finally {
      setLoading(false);
    }
  };
  
  // Generate usage history data
  // This is a placeholder function - in a real implementation, 
  // this data would come from the API
  const generateUsageHistory = () => {
    const now = new Date();
    const data = [];
    
    // Generate different history based on time range
    let days = 7;
    if (timeRange === 'month') days = 30;
    if (timeRange === 'day') days = 1;
    
    // Create data points
    for (let i = days; i >= 0; i--) {
      const date = new Date();
      date.setDate(now.getDate() - i);
      
      // Generate a usage amount that increases over time to simulate normal usage
      // but is random enough to look like real data
      const usage = profile.dataUsed * (1 - (i / days)) * (0.7 + Math.random() * 0.3);
      
      data.push({
        date: date.toISOString().split('T')[0],
        usage: Math.max(0, usage)
      });
    }
    
    return data;
  };
  
  // Format the expiry information
  const formatExpiry = () => {
    if (!profile.expiresAt) return 'No expiration date';
    
    const expiryDate = new Date(profile.expiresAt);
    const now = new Date();
    
    // Calculate days remaining
    const diffTime = expiryDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays <= 0) {
      return 'Expired';
    } else if (diffDays === 1) {
      return 'Expires tomorrow';
    } else {
      return `Expires in ${diffDays} days (${formatDate(profile.expiresAt)})`;
    }
  };
  
  return (
    <Modal title="Data Usage" onClose={onClose} size="lg">
      <div className="p-4">
        {error && (
          <Alert type="error" className="mb-4">
            {error}
          </Alert>
        )}
        
        {loading ? (
          <div className="text-center p-8">
            <Spinner size="lg" />
            <p className="mt-4 text-gray-600">Loading usage data...</p>
          </div>
        ) : (
          <div>
            <Card className="p-4 mb-6">
              <h3 className="text-lg font-medium mb-3">Package Information</h3>
              <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                <span className="text-gray-500">Package:</span>
                <span>{profile.packageName || 'No package'}</span>
                
                <span className="text-gray-500">Status:</span>
                <span className="capitalize">{profile.status}</span>
                
                <span className="text-gray-500">Activated:</span>
                <span>{profile.activatedAt ? formatDate(profile.activatedAt) : 'Not activated'}</span>
                
                <span className="text-gray-500">Expiry:</span>
                <span>{formatExpiry()}</span>
                
                <span className="text-gray-500">Provider:</span>
                <span>{profile.provider}</span>
              </div>
            </Card>
            
            <Card className="p-4 mb-6">
              <h3 className="text-lg font-medium mb-3">Current Usage</h3>
              
              <ProgressBar 
                value={profile.dataUsed * 1024 * 1024} 
                max={profile.dataLimit === -1 ? -1 : profile.dataLimit * 1024 * 1024}
                color={profile.dataLimit === -1 ? 'green' : 
                      (profile.dataUsed / profile.dataLimit > 0.9 ? 'red' :
                       profile.dataUsed / profile.dataLimit > 0.7 ? 'yellow' : 'blue')}
              />
              
              <div className="grid grid-cols-2 gap-4 mt-4">
                <div className="bg-gray-50 p-3 rounded-md">
                  <p className="text-sm text-gray-500 mb-1">Used</p>
                  <p className="text-xl font-semibold">
                    {formatBytes(profile.dataUsed * 1024 * 1024)}
                  </p>
                </div>
                
                <div className="bg-gray-50 p-3 rounded-md">
                  <p className="text-sm text-gray-500 mb-1">Remaining</p>
                  <p className="text-xl font-semibold">
                    {profile.dataLimit === -1 
                      ? 'Unlimited' 
                      : formatBytes((profile.dataLimit - profile.dataUsed) * 1024 * 1024)}
                  </p>
                </div>
              </div>
              
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-500">
                  Last updated: {usageData ? formatDate(usageData.lastUpdated) : 'Unknown'}
                </p>
              </div>
            </Card>
            
            <Card className="p-4 mb-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">Usage History</h3>
                
                <div className="flex border rounded overflow-hidden">
                  <button 
                    className={`px-3 py-1 text-sm ${timeRange === 'day' ? 'bg-blue-100 text-blue-700' : 'bg-white'}`}
                    onClick={() => setTimeRange('day')}
                  >
                    Day
                  </button>
                  <button 
                    className={`px-3 py-1 text-sm ${timeRange === 'week' ? 'bg-blue-100 text-blue-700' : 'bg-white'}`}
                    onClick={() => setTimeRange('week')}
                  >
                    Week
                  </button>
                  <button 
                    className={`px-3 py-1 text-sm ${timeRange === 'month' ? 'bg-blue-100 text-blue-700' : 'bg-white'}`}
                    onClick={() => setTimeRange('month')}
                  >
                    Month
                  </button>
                </div>
              </div>
              
              <div className="h-48 relative">
                {/* Simple bar chart visualization */}
                <div className="flex h-full items-end">
                  {generateUsageHistory().map((item, index) => {
                    const height = profile.dataLimit === -1 
                      ? `${Math.min(100, (item.usage / profile.dataUsed) * 100)}%` 
                      : `${Math.min(100, (item.usage / profile.dataLimit) * 100)}%`;
                    
                    return (
                      <div 
                        key={index} 
                        className="flex-1 flex flex-col items-center group"
                      >
                        <div className="w-full px-1">
                          <div 
                            className="bg-blue-500 hover:bg-blue-600 transition-all rounded-t"
                            style={{ height }}
                          ></div>
                        </div>
                        <span className="text-xs mt-1 text-gray-500">
                          {item.date.split('-')[2]}
                        </span>
                        
                        {/* Tooltip */}
                        <div className="absolute bottom-full mb-2 bg-gray-800 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                          {formatDate(item.date)}: {formatBytes(item.usage * 1024 * 1024)}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
              
              <div className="mt-2 text-center">
                <p className="text-xs text-gray-500">
                  Note: Historical data is approximate and may not reflect actual usage patterns.
                </p>
              </div>
            </Card>
            
            <div className="flex justify-between">
              <Button variant="outline" onClick={fetchUsageData}>
                Refresh Data
              </Button>
              <Button onClick={onClose}>Close</Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}
