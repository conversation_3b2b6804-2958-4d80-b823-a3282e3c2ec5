# Codebase Cleanup Tasks

## Task Queue

### Task 1
- **Task description:** Identify and remove all legacy `.original.js` files and unused controller versions.
- **Priority:** Low
- **Target file/component:** `back/backend/controllers/*`, `back/backend/services/*` (Search across backend)
- **Dependencies:** None
- **Status:** DONE
- **Tags:** #cleanup #legacy_code #refactor #maintenance
- **Completed:** Removed the following files:
  - `back/backend/config/index.original.js`
  - `back/backend/server.original.js`
  - `back/backend/utils/logger.original.js`
  - `back/backend/controllers/numberController.original.js`
  - `back/backend/controllers/phoneNumberController.js` (duplicate controller)
  - `back/backend/routes/phoneNumberRoutes.js` (unused routes)

### Task 2
- **Task description:** Normalize frontend folder structure, removing duplicates or empty directories like `front/mainpage/public` and potentially consolidating `client/`.
- **Priority:** Low
- **Target file/component:** `front/`, `client/` (Review overall structure)
- **Dependencies:** None
- **Status:** DONE
- **Tags:** #cleanup #frontend #structure #refactor #maintenance
- **Completed:**
  - Fixed `.gitignore` file to properly exclude build artifacts
  - Verified that `front/mainpage/public` directory exists and contains necessary assets
  - Confirmed that there is no `client/` directory that needs consolidation
  - Updated documentation to reflect the current folder structure

### Task 3
- **Task description:** Address linting issues in the codebase to improve code quality and consistency.
- **Priority:** Medium
- **Target file/component:** `front/mainpage/`, `back/backend/` (Both frontend and backend)
- **Dependencies:** None
- **Status:** DONE
- **Tags:** #cleanup #linting #code_quality #maintenance
- **Completed:**
  - Updated ESLint configurations for frontend and backend
  - Fixed React Hook dependencies in frontend components
  - Addressed accessibility issues in UI components
  - Cleaned up unused imports and variables
  - Standardized error handling in backend
  - Fixed code style issues (semicolons, quotes, indentation)
  - Implemented automated linting in CI/CD pipeline
  - Set up Husky and lint-staged for pre-commit hooks
  - Created installation scripts for ESLint plugins

### Task 4
- **Task description:** Improve accessibility in frontend components to ensure the application is usable by all users, including those with disabilities.
- **Priority:** High
- **Target file/component:** `front/mainpage/components/`, `front/mainpage/app/`
- **Dependencies:** None
- **Status:** DONE
- **Tags:** #accessibility #frontend #a11y #user_experience
- **Completed:**
  - Created reusable accessible components (Button, Modal, Tabs, Tooltip)
  - Developed accessibility guidelines document
  - Created script to audit accessibility issues
  - Implemented script to fix common accessibility issues
  - Added proper ARIA attributes to interactive elements
  - Improved keyboard navigation throughout the application
  - Enhanced screen reader support

### Task 5
- **Task description:** Optimize frontend performance to improve user experience and Core Web Vitals metrics.
- **Priority:** High
- **Target file/component:** `front/mainpage/components/`, `front/mainpage/utils/`, `front/mainpage/hooks/`
- **Dependencies:** None
- **Status:** DONE
- **Tags:** #performance #frontend #optimization #user_experience
- **Completed:**
  - Implemented performance monitoring tools
  - Created performance dashboard for visualizing metrics
  - Developed code splitting and lazy loading components
  - Optimized image loading and rendering
  - Enhanced React component rendering with memoization
  - Improved API data fetching with caching and deduplication
  - Optimized form handling with debouncing and throttling
  - Created comprehensive performance optimization guide
