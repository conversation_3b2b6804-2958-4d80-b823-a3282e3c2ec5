"use client";

import React, { useState } from 'react';
import { CallList } from './CallList';
import { AIResponseAnalyzer } from './AIResponseAnalyzer';
import { CustomerJourney } from './CustomerJourney';
import {
  PhoneIcon,
  UserGroupIcon,
  ArrowTrendingUpIcon,
  ClockIcon,
  ArrowDownTrayIcon,
  PlusCircleIcon,
  ChatBubbleLeftRightIcon,
  UserCircleIcon,
  Cog8ToothIcon
} from '@heroicons/react/24/outline';

export function CRMDashboard() {
  const [activeTab, setActiveTab] = useState('calls');

  // Mock data for stats
  const statsData = [
    {
      title: 'Calls Managed',
      value: '1,248',
      change: '+12.5%',
      period: 'from last month',
      icon: PhoneIcon,
      color: 'bg-indigo-500/20 text-indigo-400'
    },
    {
      title: 'Active Customers',
      value: '356',
      change: '****%',
      period: 'from last month',
      icon: UserGroupIcon,
      color: 'bg-blue-500/20 text-blue-400'
    },
    {
      title: 'Response Rate',
      value: '94.8%',
      change: '****%',
      period: 'from last month',
      icon: ArrowTrendingUpIcon,
      color: 'bg-green-500/20 text-green-400'
    },
    {
      title: 'Avg. Response Time',
      value: '2:42',
      change: '-18.5%',
      period: 'from last month',
      icon: ClockIcon,
      color: 'bg-purple-500/20 text-purple-400'
    }
  ];

  // Mock data for recent activity
  const recentActivity = [
    {
      id: 'act1',
      user: 'Maya Rodriguez',
      action: 'resolved a customer inquiry',
      time: '4 minutes ago',
      icon: ChatBubbleLeftRightIcon
    },
    {
      id: 'act2',
      user: 'James Kim',
      action: 'added notes to call #4582',
      time: '23 minutes ago',
      icon: PhoneIcon
    },
    {
      id: 'act3',
      user: 'Sophia Chen',
      action: 'updated customer profile',
      time: '1 hour ago',
      icon: UserCircleIcon
    },
    {
      id: 'act4',
      user: 'Ryan Johnson',
      action: 'modified system settings',
      time: '3 hours ago',
      icon: Cog8ToothIcon
    }
  ];

  return (
    <div className="bg-gray-900 text-white min-h-screen p-4 md:p-6">
      {/* Stats section */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {statsData.map((stat, index) => (
          <div
            key={index}
            className="border border-gray-800 bg-gray-900 p-5 rounded-xl flex items-start space-x-4"
          >
            <div className={`rounded-full p-3 ${stat.color}`}>
              <stat.icon className="h-5 w-5" />
            </div>
            <div>
              <p className="text-gray-400 text-sm">{stat.title}</p>
              <h3 className="text-2xl font-bold text-white mt-1">{stat.value}</h3>
              <p className="text-xs flex items-center mt-1">
                <span className={stat.change.startsWith('+') ? 'text-green-400' : 'text-red-400'}>
                  {stat.change}
                </span>
                <span className="text-gray-500 ml-1">{stat.period}</span>
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main area - 3/4 width */}
        <div className="lg:col-span-3 border border-gray-800 bg-gray-900 rounded-xl overflow-hidden">
          {/* Tabs */}
          <div className="mb-4 border-b border-gray-800">
            <nav className="flex space-x-6 px-5 pt-5">
              <button
                onClick={() => setActiveTab('calls')}
                className={`py-3 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'calls'
                    ? 'border-indigo-400 text-indigo-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300'
                }`}
              >
                <PhoneIcon className="h-4 w-4 inline mr-2" />
                Call Management
              </button>
              <button
                onClick={() => setActiveTab('analyzer')}
                className={`py-3 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'analyzer'
                    ? 'border-indigo-400 text-indigo-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300'
                }`}
              >
                <ChatBubbleLeftRightIcon className="h-4 w-4 inline mr-2" />
                AI Response Analyzer
              </button>
              <button
                onClick={() => setActiveTab('journey')}
                className={`py-3 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'journey'
                    ? 'border-indigo-400 text-indigo-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300'
                }`}
              >
                <UserCircleIcon className="h-4 w-4 inline mr-2" />
                Customer Journey
              </button>
            </nav>
          </div>

          {/* Tab content */}
          <div className="h-[calc(100vh-270px)]">
            {activeTab === 'calls' && <CallList />}
            {activeTab === 'analyzer' && <AIResponseAnalyzer />}
            {activeTab === 'journey' && <CustomerJourney />}
          </div>
        </div>

        {/* Sidebar - 1/4 width */}
        <div className="border border-gray-800 bg-gray-900 p-4 sm:p-5 rounded-xl">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Recent Activity</h3>
            <div className="flex space-x-2">
              <button className="p-2 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors">
                <ArrowDownTrayIcon className="h-4 w-4 text-gray-400" />
              </button>
              <button className="p-2 rounded-lg bg-gray-800 hover:bg-gray-700 transition-colors">
                <PlusCircleIcon className="h-4 w-4 text-gray-400" />
              </button>
            </div>
          </div>

          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="p-3 bg-gray-800/50 rounded-lg">
                <div className="flex items-start">
                  <div className="bg-gray-700 p-2 rounded-full mr-3">
                    <activity.icon className="h-4 w-4 text-indigo-400" />
                  </div>
                  <div>
                    <p className="text-white text-sm font-medium">{activity.user}</p>
                    <p className="text-gray-400 text-xs">{activity.action}</p>
                    <p className="text-gray-500 text-xs mt-1">{activity.time}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 pt-6 border-t border-gray-800">
            <h4 className="text-sm font-semibold text-white mb-3">Quick Actions</h4>
            <div className="grid grid-cols-2 gap-2">
              <button className="p-3 bg-indigo-600/30 hover:bg-indigo-600/50 rounded-lg text-indigo-300 text-sm font-medium transition-colors text-left">
                Export Reports
              </button>
              <button className="p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-gray-300 text-sm font-medium transition-colors text-left">
                Bulk Update
              </button>
              <button className="p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-gray-300 text-sm font-medium transition-colors text-left">
                Add Contact
              </button>
              <button className="p-3 bg-gray-800 hover:bg-gray-700 rounded-lg text-gray-300 text-sm font-medium transition-colors text-left">
                Schedule Call
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 