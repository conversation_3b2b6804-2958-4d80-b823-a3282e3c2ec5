# CallSaver eSIM Integration

This document provides an overview of the eSIM integration implementation for CallSaver, including setup instructions, testing procedures, and next steps.

## Overview

The eSIM integration allows CallSaver to transition from traditional Twilio phone numbers to eSIM-based connectivity. This enables users to:

1. Create and manage eSIM profiles directly within the CallSaver platform
2. Choose from various data packages by region and size
3. Monitor data usage with visual charts and statistics
4. Manage multiple eSIM profiles for different regions or use cases

The implementation follows a provider-agnostic approach, with an abstraction layer that allows easy switching between different eSIM providers.

## Implementation Status

✅ **Completed**
- eSIM service abstraction layer
- Provider factory pattern for multiple providers
- Airalo provider implementation
- Mock provider for testing
- Frontend components (Profile management, Package selection, QR code, Data usage)
- Telephony service with provider selection logic

⏳ **Pending**
- Airalo API credentials (applied, awaiting approval)
- Production testing with real API
- End-to-end user testing
- Documentation updates

## Testing with Mock Provider

While waiting for the Airalo API credentials (estimated 48-hour approval time), you can test the full eSIM integration using the mock provider.

1. **Setup Environment Variables**:
   ```bash
   cp .env.mock-testing .env
   ```

2. **Run Mock Provider Test**:
   ```bash
   node back/backend/test-mock-provider.js
   ```
   This script tests the complete eSIM lifecycle using the mock provider.

3. **Test UI Flow**:
   Start the backend and frontend servers, then navigate to the eSIM management page to test the UI flow.

See [MOCK_TESTING_GUIDE.md](MOCK_TESTING_GUIDE.md) for detailed testing instructions.

## Implementing with Airalo

Once Airalo API credentials are approved:

1. **Update Environment Variables**:
   ```bash
   cp .env.example-airalo .env
   ```
   Then, update the `.env` file with your actual Airalo credentials.

2. **Test Connection**:
   ```bash
   node back/backend/test-airalo-connection.js
   ```

3. **Gradual Rollout**:
   - Start with specific test users: `ESIM_ENABLED_USERS=1,2,3`
   - Enable for all users when ready: `ESIM_ENABLED=true`

See [AIRALO_INTEGRATION_GUIDE.md](AIRALO_INTEGRATION_GUIDE.md) for detailed integration instructions.

## Architecture

The eSIM integration follows a layered architecture:

1. **User Interface Layer**: React components for eSIM management
2. **API Layer**: Express routes and controllers
3. **Service Layer**: Core business logic and provider abstraction
4. **Provider Layer**: Provider-specific implementations

Key files:
- `back/backend/esim/esimService.js`: Core eSIM service
- `back/backend/esim/providers/`: Provider implementations
- `back/backend/services/telephonyService.js`: Telephony abstraction
- `front/mainpage/components/esim/`: Frontend components

## Extending with New Providers

To add a new eSIM provider:

1. Create a new provider implementation in `back/backend/esim/providers/`
2. Implement the standard provider interface
3. Register the provider in `back/backend/esim/providers/index.js`

No changes to the core services or UI are required when adding new providers.

## Documentation

- [AIRALO_INTEGRATION_GUIDE.md](AIRALO_INTEGRATION_GUIDE.md): Guide for Airalo API integration
- [AIRALO_CREDENTIALS_GUIDE.md](AIRALO_CREDENTIALS_GUIDE.md): Instructions for obtaining Airalo API credentials
- [MOCK_TESTING_GUIDE.md](MOCK_TESTING_GUIDE.md): Guide for testing with the mock provider

## Next Steps

1. **Wait for Airalo API Credentials**: Expected within 48 hours from application
2. **Test with Mock Provider**: Complete thorough testing using the mock provider
3. **Implement with Real Credentials**: When credentials arrive, switch to the real provider
4. **Monitor Integration**: Watch for any issues during initial deployment
5. **Gradual Rollout**: Start with specific users, then expand to all users

## Technical Details

### Mock Provider

The mock provider simulates the behavior of a real eSIM provider without making actual API calls. It includes:

- Simulated network delays and random errors (for testing error handling)
- In-memory storage for profiles, packages, and usage data
- Realistic data behavior (usage increasing over time, etc.)

### Airalo Provider

The Airalo provider integrates with the Airalo Partners API:

- OAuth 2.0 client credentials flow for authentication
- Automatic token refresh when needed
- Comprehensive error handling and logging
- Full support for all eSIM operations

### Telephony Abstraction

The telephony service provides a unified interface for both Twilio and eSIM:

- Transparent provider selection based on config or context
- Fallback mechanism for high availability
- Gradual migration path from Twilio to eSIM
