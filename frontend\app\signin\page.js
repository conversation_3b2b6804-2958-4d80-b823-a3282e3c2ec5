"use client";

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import getSupabaseClient from '../utils/supabaseClient';
import { signIn, signInWithGoogle } from '../actions/auth';
import FallingIcons from '../components/FallingIcons';

export default function SignInPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/dashboard';
  const emailFromSignup = searchParams.get('email') || '';
  const errorFromCallback = searchParams.get('error');
  const [isLoading, setIsLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [error, setError] = useState('');
  const [debugInfo, setDebugInfo] = useState('');
  const [formData, setFormData] = useState({
    email: emailFromSignup,
    password: '',
  });

  // Show message if coming from signup
  useEffect(() => {
    if (emailFromSignup) {
      const signupMessage = document.getElementById('signup-message');
      if (signupMessage) {
        signupMessage.classList.remove('opacity-0');
        setTimeout(() => {
          signupMessage.classList.add('opacity-0');
        }, 5000);
      }
    }

    // Handle error messages from callbacks
    if (errorFromCallback) {
      let errorMessage = 'An error occurred during authentication.';

      if (errorFromCallback === 'auth_callback_error') {
        errorMessage = 'Failed to complete authentication. Please try again.';
      } else if (errorFromCallback === 'missing_code') {
        errorMessage = 'Authentication code was missing. Please try again.';
      } else if (errorFromCallback === 'unexpected_error') {
        errorMessage = 'An unexpected error occurred. Please try again.';
      }

      setError(errorMessage);
    }
  }, [emailFromSignup, errorFromCallback]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setDebugInfo('Starting sign-in process via server action...');

    try {
      // Clear any stored auth state before attempting sign-in
      localStorage.removeItem('supabase_auth');

      // Use ONLY the server action for sign in
      const result = await signIn(formData.email, formData.password);
      setDebugInfo(prev => prev + '\nServer response: ' + JSON.stringify(result, null, 2));

      if (!result.success) {
          const serverError = result?.error;

          if (!serverError) {
            setError('Authentication failed. Please try again.');
            setDebugInfo(prev => prev + '\nUnexpected error format in response');
            setIsLoading(false);
            return;
          }

          setDebugInfo(prev => prev +
            `\nServer-side sign-in error: ${serverError.message}` +
            `\nError details: ${JSON.stringify(serverError, null, 2)}`
          );

          if (serverError.message.includes('not confirmed') || serverError.message.includes('Email not confirmed')) {
            setError('Email not confirmed. Please check your email for the confirmation link.');
          } else if (serverError.message.includes('Invalid login credentials')) {
             setError('Invalid email or password.');
          } else {
            setError(serverError.message || 'An unknown login error occurred.');
          }

          setIsLoading(false);
          return;
      }

      setDebugInfo(prev => prev + '\nServer-side sign-in successful! Session should be set.');

      // Set a flag in localStorage to indicate we just signed in
      // This helps the dashboard layout know to retry session checks
      localStorage.setItem('just_signed_in', 'true');
      localStorage.setItem('sign_in_time', Date.now().toString());

      // Clear any previous retry counts
      localStorage.removeItem('auth_retry_count');

      // No need for client-side checks or delays, server action handles cookies.
      // Perform a full page reload to ensure cookies are read correctly by middleware/client.
      window.location.assign(callbackUrl + '?just_signed_in=true');

    } catch (err) {
      setError('An unexpected error occurred during login. Please try again.');
      console.error('Sign in error:', err);
      setDebugInfo(prev => prev + '\nUnexpected submit error: ' + err.message);
      setIsLoading(false);
    }
  };

  // Handle Google sign-in
  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true);
    setError('');
    setDebugInfo('Starting Google sign-in process...');

    try {
      // Call the server action for Google sign-in
      const result = await signInWithGoogle();
      setDebugInfo(prev => prev + '\nGoogle auth response: ' + JSON.stringify(result, null, 2));

      if (!result.success) {
        const serverError = result?.error;
        setError(serverError?.message || 'Failed to authenticate with Google');
        setDebugInfo(prev => prev + '\nGoogle sign-in error: ' + (serverError?.message || 'Unknown error'));
        setIsGoogleLoading(false);
        return;
      }

      // Redirect to the OAuth URL provided by Supabase
      if (result.url) {
        setDebugInfo(prev => prev + '\nRedirecting to Google OAuth URL: ' + result.url);
        window.location.href = result.url;
      } else {
        setError('Authentication failed. No redirect URL provided.');
        setIsGoogleLoading(false);
      }
    } catch (err) {
      setError('An unexpected error occurred during Google sign-in.');
      console.error('Google sign-in error:', err);
      setDebugInfo(prev => prev + '\nUnexpected Google sign-in error: ' + err.message);
      setIsGoogleLoading(false);
    }
  };



  return (
    <div className="min-h-screen flex flex-col items-center justify-center relative">
      <FallingIcons />

      {/* Back to home link */}
      <div className="absolute top-6 left-6 z-10">
        <Link href="/" className="flex items-center text-white hover:text-purple-300 transition-colors">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Home
        </Link>
      </div>

      <motion.div
        className="w-full max-w-md px-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Logo and title */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center">
            <div className="relative w-12 h-12 mr-2">
              <div className="absolute w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
            </div>
            <h1 className="text-2xl md:text-3xl font-bold text-white">CallSaver</h1>
          </div>
          <h2 className="text-xl text-white/80 mt-2">Sign in to your account</h2>
        </div>

        <div className="bg-gray-900/70 backdrop-blur-lg rounded-2xl border border-purple-500/20 shadow-xl p-6 md:p-8">
          {/* Message when coming from signup */}
          {emailFromSignup && (
            <div id="signup-message" className="mb-6 p-3 bg-green-500/20 border border-green-500/30 rounded-lg text-center transition-opacity duration-500">
              <p className="text-green-200">Your account was created successfully! You can now sign in.</p>
            </div>
          )}

          {error && (
            <div className="mb-6 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
              <p className="text-red-200">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="email" className="block text-sm font-medium text-gray-400 mb-1">
                Email Address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="Enter your email"
                required
              />
            </div>

            <div className="mb-6">
              <div className="flex items-center justify-between mb-1">
                <label htmlFor="password" className="block text-sm font-medium text-gray-400">
                  Password
                </label>
                <Link href="/forgot-password" className="text-sm text-purple-400 hover:text-purple-300">
                  Forgot password?
                </Link>
              </div>
              <input
                id="password"
                name="password"
                type="password"
                value={formData.password}
                onChange={handleChange}
                className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="Enter your password"
                required
              />
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="laser-button w-full py-3 flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Signing in...
                  </>
                ) : (
                  "Sign in"
                )}
              </button>
            </div>
          </form>

          {/* Divider */}
          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-600/50"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-gray-900/70 text-gray-400">Or continue with</span>
            </div>
          </div>

          {/* Google Sign In Button */}
          <button
            type="button"
            onClick={handleGoogleSignIn}
            disabled={isGoogleLoading}
            className="w-full flex items-center justify-center py-2.5 px-4 border border-gray-600 rounded-lg hover:bg-gray-800/50 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors"
          >
            {isGoogleLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Connecting to Google...
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                  <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                  <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                  <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                </svg>
                Sign in with Google
              </>
            )}
          </button>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-400">
              Don&apos;t have an account?{' '}
              <Link href="/signup" className="text-purple-400 hover:text-purple-300 font-medium">
                Sign up
              </Link>
            </p>
          </div>

          {/* Development debug info */}
          {process.env.NODE_ENV !== 'production' && debugInfo && (
            <div className="mt-6 p-3 bg-gray-800/70 border border-gray-700 rounded-lg overflow-auto max-h-60">
              <h3 className="text-gray-400 font-semibold mb-1 text-xs">Debug Info</h3>
              <pre className="text-gray-500 text-xs whitespace-pre-wrap">{debugInfo}</pre>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
}