---
description:
globs:
alwaysApply: false
---
---
description: Defines the strategy for processing, managing state, handling errors, and escalating asynchronous AI tasks.
---
# AI Task Processing and Escalation Strategy (`ai_task_processing_and_escalation.mdc`)

## 1. Purpose and Scope

**Purpose:** To define a robust and scalable architecture for managing asynchronous AI tasks initiated within the CallSaver platform. This covers the lifecycle of an AI task from initiation through completion, error handling, retries, and potential escalation.

**Scope:**
- Task definition and payload structure for AI jobs.
- Integration with the primary task queue (`task_queue.mdc`).
- State management for AI tasks (e.g., `PENDING`, `PROCESSING`, `COMPLETED`, `FAILED`, `RETRYING`, `ESCALATED`).
- Retry mechanisms for transient failures (e.g., network issues, temporary provider outages).
- Dead-letter queue (DLQ) strategy for persistent failures.
- Escalation paths for critical failures or tasks requiring human review.
- Monitoring and alerting for AI task queues and failures.
- Interaction with AI Response Signature Logging (`ai_response_signature_logging.mdc`).

## 2. AI Task Lifecycle and State Management

### 2.1. Task Definition

- AI tasks submitted to the queue should contain:
    - `taskId`: Unique identifier for the task instance.
    - `correlationId`: Identifier linking to the originating request/event (e.g., `callSid`).
    - `taskType`: Specific AI operation (e.g., `TRANSCRIBE_AUDIO`, `SUMMARIZE_TRANSCRIPT`, `ANALYZE_SENTIMENT`, `GENERATE_RESPONSE`).
    - `payload`: Data required for the task (e.g., audio file reference, transcript text, context).
    - `metadata`: Priority, user/org context, timestamp, originating service.
    - `attemptCount`: Current attempt number (starts at 1).
    - `maxAttempts`: Maximum number of retries allowed for this task type.
    - `status`: Current state of the task (see 2.2).

### 2.2. Task States

- `PENDING`: Task created and waiting in the queue.
- `PROCESSING`: Task picked up by an AI worker/service.
- `COMPLETED`: Task finished successfully. Output stored/processed.
- `FAILED_TRANSIENT`: Task failed due to a potentially temporary issue (e.g., API timeout, rate limit). Eligible for retry.
- `RETRYING`: Task is scheduled for a future retry attempt.
- `FAILED_PERMANENT`: Task failed due to a non-recoverable error (e.g., invalid input, persistent provider error) after exhausting retries.
- `ESCALATED`: Task failed permanently and has been flagged for manual review or an alternative process.

## 3. Processing and Error Handling

### 3.1. Task Queue Integration

- Utilize the primary message queue system defined in `task_queue.mdc` (e.g., RabbitMQ, Redis Streams, SQS).
- Define specific queues or routing keys for different AI task types or priorities if needed.

### 3.2. AI Workers

- Dedicated worker services subscribe to AI task queues.
- Workers fetch tasks, execute the AI operation (calling the relevant model via the AI Integration Layer), and update task status.

### 3.3. Retry Strategy

- Implement an exponential backoff strategy for retries on `FAILED_TRANSIENT` errors.
- Configure maximum retry attempts (`maxAttempts`) per task type.
- Increment `attemptCount` on each retry.
- Workers should explicitly handle transient errors (e.g., HTTP 429, 5xx from AI provider) and update task status to `FAILED_TRANSIENT`. The queue mechanism or worker logic schedules the retry.

### 3.4. Dead-Letter Queue (DLQ)

- Tasks reaching `maxAttempts` or encountering `FAILED_PERMANENT` errors should be moved to a designated DLQ.
- The DLQ message should retain the original task payload and metadata, plus error details.
- Implement monitoring and alerting on the DLQ size and contents.

## 4. Escalation Paths

- Define rules for escalating tasks from the DLQ:
    - **Automated Alerting:** Trigger alerts (e.g., PagerDuty, Slack) for specific critical task failures in the DLQ.
    - **Human Review Queue:** Route certain failed tasks (e.g., failed transcriptions for high-priority calls) to a dedicated UI or system for manual review and correction.
    - **Fallback Logic:** Trigger alternative, potentially simpler, fallback logic if a primary AI task fails persistently (e.g., skip summarization if transcription fails repeatedly).
- Escalation rules may depend on task type, priority, or associated customer tier.

## 5. Monitoring and Logging

- **Queue Monitoring:** Track queue lengths, task processing times, and worker health.
- **Failure Rate Monitoring:** Monitor the rate of tasks entering `FAILED_TRANSIENT`, `FAILED_PERMANENT`, and `ESCALATED` states. Set up alerts for unusual spikes.
- **Logging:**
    - Each state transition of an AI task should be logged with `taskId` and `correlationId`.
    - Errors encountered during processing must be logged with detailed context.
    - Successful completions and failures should trigger entries in the AI Response Signature Log (`ai_response_signature_logging.mdc`).

## 6. Implementation Considerations

- Choose a task queue system that supports delayed messages/retries and DLQs effectively.
- Ensure workers are idempotent where possible.
- Design task payloads carefully to be self-contained but avoid excessive size.
- Secure communication between services and the task queue.

## 7. Related Documents

- `docs/functional_specs/task_queue.mdc`
- `docs/functional_specs/ai_integration_layer.mdc`
- `docs/functional_specs/ai_response_signature_logging.mdc`
- `docs/functional_specs/notifications_and_alerts_document.mdc` (for DLQ/escalation alerting)
