"use client";

import { useState, useEffect, useRef } from 'react';
import { ChevronDownIcon, CheckIcon, PhoneIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';

export default function PhoneNumberSelector({ numbers = [], onSelect, selectedNumber = null }) {
  const [isOpen, setIsOpen] = useState(false);
  const [selected, setSelected] = useState(selectedNumber || (numbers.length > 0 ? numbers[0] : null));
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredNumbers, setFilteredNumbers] = useState(numbers);
  const dropdownRef = useRef(null);
  const searchInputRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Update selected number when props change
  useEffect(() => {
    if (selectedNumber) {
      setSelected(selectedNumber);
    } else if (numbers.length > 0 && !selected) {
      setSelected(numbers[0]);
    }
  }, [selectedNumber, numbers, selected]);

  // Filter numbers based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredNumbers(numbers);
      return;
    }
    
    const filtered = numbers.filter(number => {
      const numberStr = number.number || number.phoneNumber || '';
      const friendlyName = number.friendlyName || '';
      const region = number.region || '';
      
      const searchLower = searchTerm.toLowerCase();
      return numberStr.includes(searchTerm) || 
             friendlyName.toLowerCase().includes(searchLower) ||
             region.toLowerCase().includes(searchLower);
    });
    
    setFilteredNumbers(filtered);
  }, [searchTerm, numbers]);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current.focus();
      }, 100);
    }
  }, [isOpen]);

  const handleSelect = (number) => {
    setSelected(number);
    setIsOpen(false);
    setSearchTerm('');
    if (onSelect) {
      onSelect(number);
    }
  };

  const handleDropdownToggle = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setSearchTerm('');
      setFilteredNumbers(numbers);
    }
  };

  // Format phone number for display
  const formatPhoneNumber = (phoneNumber) => {
    if (!phoneNumber) return '';

    // Remove any non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');

    // Format based on length
    if (cleaned.length === 10) { // US number
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    } else if (cleaned.length === 11 && cleaned.startsWith('1')) { // US with country code
      return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
    } else if (cleaned.length > 10) { // International
      return `+${cleaned}`;
    }

    // Return original if we can't format it
    return phoneNumber;
  };

  if (!numbers || numbers.length === 0) {
    return (
      <div className="bg-gray-800 rounded-lg p-3 text-gray-400 text-sm flex items-center">
        <ExclamationCircleIcon className="h-4 w-4 mr-2 text-yellow-400" />
        No phone numbers available. Please purchase a number first.
      </div>
    );
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={handleDropdownToggle}
        className="w-full flex items-center justify-between bg-gray-800 hover:bg-gray-700 rounded-lg p-3 text-white transition-colors"
      >
        <span className="flex items-center">
          <PhoneIcon className="h-4 w-4 mr-2 text-purple-400" />
          {selected ? (
            <span>
              {selected.friendlyName}
              {selected.region && <span className="text-gray-400 text-xs ml-2">({selected.region})</span>}
            </span>
          ) : (
            <span className="text-gray-400">Select a number</span>
          )}
        </span>
        <ChevronDownIcon className={`h-5 w-5 text-gray-400 transition-transform ${isOpen ? 'transform rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute z-20 mt-1 w-full bg-gray-800 rounded-lg shadow-lg py-2 border border-gray-700 max-h-60 overflow-y-auto">
          {/* Search input */}
          <div className="px-3 pb-2">
            <input
              ref={searchInputRef}
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search numbers..."
              className="w-full px-3 py-1.5 bg-gray-700 border border-gray-600 rounded-md text-white text-sm focus:outline-none focus:ring-1 focus:ring-purple-500"
            />
          </div>
          
          {/* Divider */}
          <div className="border-t border-gray-700 my-1"></div>
          
          {/* Number list */}
          {filteredNumbers.length === 0 ? (
            <div className="px-4 py-2 text-gray-400 text-sm">
              No matching numbers found
            </div>
          ) : (
            filteredNumbers.map((number) => (
              <button
                key={number.id || number.number}
                className="w-full text-left px-4 py-2 hover:bg-gray-700 flex items-center justify-between"
                onClick={() => handleSelect(number)}
              >
                <div>
                  <div className="text-white">{number.friendlyName || formatPhoneNumber(number.number || number.phoneNumber)}</div>
                  {number.region && <div className="text-gray-400 text-xs">{number.region}</div>}
                </div>
                {selected && (selected.id === number.id || selected.number === number.number) && (
                  <CheckIcon className="h-5 w-5 text-green-400" />
                )}
              </button>
            ))
          )}
        </div>
      )}
    </div>
  );
}
