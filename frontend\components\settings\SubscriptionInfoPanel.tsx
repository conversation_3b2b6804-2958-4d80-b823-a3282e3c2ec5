'use client';

import { Subscription } from '../../../hooks/useSettings';

interface SubscriptionInfoPanelProps {
  subscription?: Subscription;
}

export default function SubscriptionInfoPanel({ subscription }: SubscriptionInfoPanelProps) {
  if (!subscription) {
    return (
      <div className="text-center py-8 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <p className="text-gray-500 dark:text-gray-400">
          No subscription information available.
        </p>
      </div>
    );
  }
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'long'
    }).format(date);
  };
  
  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'trialing':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'past_due':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'canceled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };
  
  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
      <div className="px-6 py-5 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              {subscription.plan} Plan
            </h4>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {subscription.cancelAtPeriodEnd 
                ? `Cancels on ${formatDate(subscription.currentPeriodEnd)}` 
                : `Renews on ${formatDate(subscription.currentPeriodEnd)}`}
            </p>
          </div>
          <div className="mt-3 sm:mt-0">
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(subscription.status)}`}>
              {subscription.status === 'active' ? 'Active' : 
               subscription.status === 'trialing' ? 'Trial' :
               subscription.status === 'past_due' ? 'Past Due' :
               subscription.status === 'canceled' ? 'Canceled' : 
               subscription.status}
            </span>
          </div>
        </div>
      </div>
      
      <div className="px-6 py-5">
        {/* Credits */}
        <div className="mb-6">
          <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Credits Balance</h5>
          <div className="flex items-center">
            <div className="text-3xl font-bold text-gray-900 dark:text-white">
              {subscription.credits.toLocaleString()}
            </div>
            <div className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              credits remaining
            </div>
          </div>
        </div>
        
        {/* Features */}
        <div>
          <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Plan Features</h5>
          <ul className="space-y-3">
            {subscription.features.map((feature, index) => (
              <li key={index} className="flex justify-between">
                <div className="flex items-center">
                  {feature.included ? (
                    <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  )}
                  <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                    {feature.name}
                  </span>
                </div>
                {feature.limit && (
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {feature.used !== undefined ? `${feature.used} / ${feature.limit}` : feature.limit}
                  </div>
                )}
              </li>
            ))}
          </ul>
        </div>
      </div>
      
      <div className="px-6 py-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Need more credits or want to change your plan?
          </div>
          <div className="mt-3 sm:mt-0">
            <a
              href="/pricing"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Manage Subscription
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
