name: Frontend CI/CD Pipeline

on:
  push:
    branches: [main]
    paths:
      - 'front/mainpage/**'
  pull_request:
    branches: [main]
    paths:
      - 'front/mainpage/**'

jobs:
  lint:
    name: Lint
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./front/mainpage
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'
          cache-dependency-path: './front/mainpage/package-lock.json'

      - name: Install dependencies
        run: npm ci

      - name: Install ESLint plugins
        run: node install-eslint-plugins.js

      - name: Run linting
        run: npm run lint

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: lint
    defaults:
      run:
        working-directory: ./front/mainpage
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'
          cache-dependency-path: './front/mainpage/package-lock.json'

      - name: Install dependencies
        run: npm ci

      - name: Generate Prisma Client
        run: npx prisma generate

      - name: Build Next.js app
        run: npm run build

      - name: Cache build output
        uses: actions/cache@v3
        with:
          path: |
            front/mainpage/.next
            front/mainpage/node_modules/.prisma
          key: ${{ runner.os }}-nextjs-${{ github.sha }}

  deploy:
    name: Deploy to Railway
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    defaults:
      run:
        working-directory: ./front/mainpage
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Restore build cache
        uses: actions/cache@v3
        with:
          path: |
            front/mainpage/.next
            front/mainpage/node_modules/.prisma
          key: ${{ runner.os }}-nextjs-${{ github.sha }}

      - name: Install Railway CLI
        run: npm install -g @railway/cli

      - name: Deploy to Railway
        run: railway up
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_FRONTEND_TOKEN }}