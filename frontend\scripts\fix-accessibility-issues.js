/**
 * <PERSON><PERSON><PERSON> to fix common accessibility issues in existing components
 * 
 * This script scans the codebase for common accessibility issues and fixes them.
 * It focuses on:
 * 1. Adding alt text to images
 * 2. Adding aria-label to buttons without text
 * 3. Adding role attributes to non-semantic elements
 * 4. Adding keyboard event handlers to clickable elements
 * 5. Adding focus management to modals and dialogs
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('Starting accessibility fixes...');

// Define the directory to scan
const componentsDir = path.join(__dirname, '..', 'components');
const appDir = path.join(__dirname, '..', 'app');

// Create a report directory
const reportDir = path.join(__dirname, '..', 'accessibility-reports');
if (!fs.existsSync(reportDir)) {
  fs.mkdirSync(reportDir);
}

// Function to scan files recursively
const scanDirectory = (dir, fileList = []) => {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      scanDirectory(filePath, fileList);
    } else if (/\.(jsx|tsx|js|ts)$/.test(file)) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
};

// Function to fix accessibility issues in a file
const fixAccessibilityIssues = (filePath) => {
  console.log(`Fixing accessibility issues in ${filePath}...`);
  
  let content = fs.readFileSync(filePath, 'utf8');
  let issuesFixed = 0;
  
  // 1. Fix images without alt text
  const imgWithoutAltRegex = /<img([^>]*?)(?!alt=)([^>]*?)>/g;
  content = content.replace(imgWithoutAltRegex, (match, before, after) => {
    issuesFixed++;
    return `<img${before} alt="" ${after}>`;
  });
  
  // 2. Fix buttons without accessible text
  const buttonWithoutTextRegex = /<button([^>]*?)>([^<]*?)<\/button>/g;
  content = content.replace(buttonWithoutTextRegex, (match, attrs, innerContent) => {
    // If the button has no text content but contains an icon
    if (!innerContent.trim() && (attrs.includes('icon') || innerContent.includes('<svg') || innerContent.includes('<i '))) {
      issuesFixed++;
      // Add aria-label if not present
      if (!attrs.includes('aria-label')) {
        return `<button${attrs} aria-label="Button">${innerContent}</button>`;
      }
    }
    return match;
  });
  
  // 3. Fix div elements used as buttons
  const divAsButtonRegex = /<div([^>]*?)onClick([^>]*?)>([^<]*?)<\/div>/g;
  content = content.replace(divAsButtonRegex, (match, before, onClick, innerContent) => {
    issuesFixed++;
    return `<div${before}onClick${onClick} role="button" tabIndex={0} onKeyDown={(e) => e.key === 'Enter' && e.target.click()}>${innerContent}</div>`;
  });
  
  // 4. Fix modals without proper focus management
  if (content.includes('Modal') || content.includes('Dialog') || content.includes('modal') || content.includes('dialog')) {
    // Check if the file contains a modal component
    const modalRegex = /function\s+(\w+Modal|\w+Dialog|\w+Popup)|\s+class\s+(\w+Modal|\w+Dialog|\w+Popup)/;
    if (modalRegex.test(content)) {
      // Check if focus management is missing
      if (!content.includes('useRef') || !content.includes('focus()')) {
        console.log(`  Modal component found in ${filePath} - consider replacing with AccessibleModal`);
      }
    }
  }
  
  // 5. Fix elements with click handlers but no keyboard handlers
  const clickWithoutKeyRegex = /onClick={([^}]*?)}\s*(?!onKeyDown)/g;
  content = content.replace(clickWithoutKeyRegex, (match, handler) => {
    issuesFixed++;
    return `onClick={${handler}} onKeyDown={(e) => e.key === 'Enter' && ${handler}(e)}`;
  });
  
  // Write the fixed content back to the file
  fs.writeFileSync(filePath, content);
  
  return issuesFixed;
};

// Scan the directories
console.log('Scanning directories...');
const componentFiles = scanDirectory(componentsDir);
const appFiles = scanDirectory(appDir);
const allFiles = [...componentFiles, ...appFiles];

console.log(`Found ${allFiles.length} files to scan.`);

// Fix accessibility issues
let totalIssuesFixed = 0;
const fileIssues = [];

allFiles.forEach(file => {
  const issuesFixed = fixAccessibilityIssues(file);
  totalIssuesFixed += issuesFixed;
  
  if (issuesFixed > 0) {
    fileIssues.push({ file, issuesFixed });
  }
});

// Generate a report
const reportPath = path.join(reportDir, 'accessibility-fixes-report.json');
fs.writeFileSync(reportPath, JSON.stringify({
  totalIssuesFixed,
  fileIssues,
  timestamp: new Date().toISOString()
}, null, 2));

console.log(`Fixed ${totalIssuesFixed} accessibility issues in ${fileIssues.length} files.`);
console.log(`Report saved to ${reportPath}`);

// Generate an HTML report
const htmlReportPath = path.join(reportDir, 'accessibility-fixes-report.html');
const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Accessibility Fixes Report</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; line-height: 1.6; }
    h1 { color: #333; }
    table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    tr:nth-child(even) { background-color: #f9f9f9; }
    .summary { margin-bottom: 30px; }
    .issues { color: #d32f2f; }
  </style>
</head>
<body>
  <h1>Accessibility Fixes Report</h1>
  <div class="summary">
    <h2>Summary</h2>
    <p>Fixed <strong>${totalIssuesFixed}</strong> accessibility issues in <strong>${fileIssues.length}</strong> files.</p>
    <p>Generated on: ${new Date().toLocaleString()}</p>
  </div>
  
  <h2>Files with Issues</h2>
  <table>
    <tr>
      <th>File</th>
      <th>Issues Fixed</th>
    </tr>
    ${fileIssues.map(({ file, issuesFixed }) => `
    <tr>
      <td>${file.replace(path.join(__dirname, '..'), '')}</td>
      <td class="issues">${issuesFixed}</td>
    </tr>
    `).join('')}
  </table>
  
  <h2>Next Steps</h2>
  <p>Consider the following next steps to further improve accessibility:</p>
  <ul>
    <li>Replace non-accessible components with the new accessible components in the <code>components/accessible</code> directory.</li>
    <li>Run the accessibility audit script to identify remaining issues.</li>
    <li>Test the application with a screen reader to ensure it's accessible to users with visual impairments.</li>
    <li>Test keyboard navigation throughout the application.</li>
    <li>Ensure color contrast meets WCAG 2.1 AA standards.</li>
  </ul>
</body>
</html>
`;

fs.writeFileSync(htmlReportPath, htmlContent);
console.log(`HTML report saved to ${htmlReportPath}`);
