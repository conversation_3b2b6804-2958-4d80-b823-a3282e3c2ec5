"use client";

import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import Nav from '../../../components/Nav';
import LoadingState from '../../../components/ui/LoadingState';
import ErrorMessage from '../../../components/ui/ErrorMessage';
import { 
  PhoneIcon, 
  CheckCircleIcon, 
  TrashIcon, 
  PlusIcon,
  ClockIcon,
  ChatBubbleLeftEllipsisIcon,
  EnvelopeIcon,
  PhoneArrowUpRightIcon
} from '@heroicons/react/24/outline';

export default function ForwardingSettings() {
  const [isLoading, setIsLoading] = useState(true);
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [userNumbers, setUserNumbers] = useState([]);
  const [selectedNumber, setSelectedNumber] = useState('');
  const [forwardingRules, setForwardingRules] = useState([]);
  const [voicemailEnabled, setVoicemailEnabled] = useState(true);
  const [voicemailDelay, setVoicemailDelay] = useState(20); // seconds
  const [scheduleEnabled, setScheduleEnabled] = useState(false);
  const [businessHours, setBusinessHours] = useState({
    monday: { enabled: true, start: '09:00', end: '17:00' },
    tuesday: { enabled: true, start: '09:00', end: '17:00' },
    wednesday: { enabled: true, start: '09:00', end: '17:00' },
    thursday: { enabled: true, start: '09:00', end: '17:00' },
    friday: { enabled: true, start: '09:00', end: '17:00' },
    saturday: { enabled: false, start: '09:00', end: '17:00' },
    sunday: { enabled: false, start: '09:00', end: '17:00' }
  });
  const [error, setError] = useState(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  // Used for new forward rule form
  const [newForwardNumber, setNewForwardNumber] = useState('');
  const [newForwardDescription, setNewForwardDescription] = useState('');
  const [newForwardDelay, setNewForwardDelay] = useState(0);
  
  const daysOfWeek = [
    { id: 'monday', label: 'Monday' },
    { id: 'tuesday', label: 'Tuesday' },
    { id: 'wednesday', label: 'Wednesday' },
    { id: 'thursday', label: 'Thursday' },
    { id: 'friday', label: 'Friday' },
    { id: 'saturday', label: 'Saturday' },
    { id: 'sunday', label: 'Sunday' }
  ];
  
  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);
      
      try {
        // Check for demo user
        const demoUser = localStorage.getItem('callsaver_demo_user');
        
        if (demoUser) {
          console.log('Demo user detected');
          setUserName('Demo User');
          setUserEmail('<EMAIL>');
          
          // Set some fake demo data
          const demoNumbers = [
            { 
              id: 'demo-123', 
              phoneNumber: '+****************', 
              region: 'US', 
              capabilities: ['voice', 'sms'],
            },
            { 
              id: 'demo-456', 
              phoneNumber: '+****************', 
              region: 'US', 
              capabilities: ['voice'],
            }
          ];
          setUserNumbers(demoNumbers);
          
          // Default to the first phone number
          setSelectedNumber(demoNumbers[0].phoneNumber);
          
          // Set some demo forwarding rules
          setForwardingRules([
            {
              id: 'rule-1',
              phoneNumber: '+****************',
              description: 'My Cell Phone',
              delaySeconds: 0,
              enabled: true
            },
            {
              id: 'rule-2',
              phoneNumber: '+****************',
              description: 'Office Phone',
              delaySeconds: 15,
              enabled: true
            }
          ]);
          
          setIsLoading(false);
          return;
        }
        
        // Handle real user authentication with Supabase
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
        
        if (!supabaseUrl || !supabaseKey) {
          console.error('Supabase credentials missing');
          throw new Error('Authentication configuration error');
        }
        
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        // Get session
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session) {
          window.location.href = '/signin';
          return;
        }
        
        // User is authenticated
        setUserName(session.user.user_metadata?.name || 'User');
        setUserEmail(session.user.email);
        
        // In a real app, fetch user numbers and forwarding settings from backend
        // For now, we'll use empty values
        setUserNumbers([]);
        setForwardingRules([]);
        
      } catch (err) {
        console.error('Page initialization error:', err);
        setError('Authentication error. Please try signing in again.');
      } finally {
        setIsLoading(false);
      }
    };
    
    initializeAuth();
  }, []);
  
  // Handle adding a new forwarding rule
  const handleAddForwardingRule = () => {
    if (!newForwardNumber) {
      setError('Please enter a phone number to forward to');
      return;
    }
    
    // Basic phone number validation
    const phonePattern = /^\+?[0-9\s\-\(\)]{7,20}$/;
    if (!phonePattern.test(newForwardNumber)) {
      setError('Please enter a valid phone number');
      return;
    }
    
    const newRule = {
      id: `rule-${Date.now()}`,
      phoneNumber: newForwardNumber,
      description: newForwardDescription || 'Forwarding Number',
      delaySeconds: parseInt(newForwardDelay, 10) || 0,
      enabled: true
    };
    
    setForwardingRules([...forwardingRules, newRule]);
    
    // Reset form
    setNewForwardNumber('');
    setNewForwardDescription('');
    setNewForwardDelay(0);
    setError(null);
  };
  
  // Handle removing a forwarding rule
  const handleRemoveForwardingRule = (ruleId) => {
    setForwardingRules(forwardingRules.filter(rule => rule.id !== ruleId));
  };
  
  // Handle toggling a rule's enabled state
  const handleToggleRuleEnabled = (ruleId) => {
    setForwardingRules(forwardingRules.map(rule => 
      rule.id === ruleId ? { ...rule, enabled: !rule.enabled } : rule
    ));
  };
  
  // Update a day's business hours
  const handleUpdateBusinessHour = (day, field, value) => {
    setBusinessHours({
      ...businessHours,
      [day]: {
        ...businessHours[day],
        [field]: value
      }
    });
  };
  
  // Handle saving all settings
  const handleSaveSettings = async () => {
    try {
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      
      // In a real app, this would send data to the backend
      // For demo purposes, we'll just show a success message
      setSaveSuccess(true);
      setTimeout(() => setSaveSuccess(false), 3000);
    } catch (err) {
      console.error('Error saving settings:', err);
      setError('Failed to save settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  if (isLoading) {
    return <LoadingState fullPage text="Loading forwarding settings..." />;
  }
  
  return (
    <div className="min-h-screen bg-gray-950 text-white">
      <div className="container mx-auto max-w-7xl px-4 py-6">
        <Nav userName={userName} userEmail={userEmail} />
        
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-white">Call Forwarding Settings</h1>
          <p className="mt-2 text-gray-400">
            Configure how your calls are handled and forwarded
          </p>
        </div>
        
        {error && (
          <ErrorMessage 
            message={error}
            className="mb-6"
            dismissible
            onDismiss={() => setError(null)}
          />
        )}
        
        {saveSuccess && (
          <div className="mb-6 rounded-lg bg-green-950/50 border border-green-800/50 p-4 text-green-200 flex items-start">
            <CheckCircleIcon className="h-5 w-5 text-green-400 mr-3 mt-0.5" />
            <div>
              <p className="font-medium">Settings saved successfully!</p>
              <p className="mt-1 text-sm text-green-300">
                Your call forwarding settings have been updated.
              </p>
            </div>
          </div>
        )}
        
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div className="md:col-span-1">
            <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
              <h2 className="text-lg font-medium text-white mb-4">Your Numbers</h2>
              
              {userNumbers.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-gray-400">You don't have any phone numbers yet</p>
                  <button className="mt-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700">
                    <PlusIcon className="h-4 w-4 mr-1" />
                    Buy a Number
                  </button>
                </div>
              ) : (
                <div className="space-y-2">
                  {userNumbers.map((number) => (
                    <div 
                      key={number.id}
                      onClick={() => setSelectedNumber(number.phoneNumber)}
                      className={`p-3 rounded-lg cursor-pointer ${
                        selectedNumber === number.phoneNumber 
                          ? 'bg-purple-900/30 border border-purple-700/50' 
                          : 'bg-gray-800/50 hover:bg-gray-800/80 border border-transparent'
                      }`}
                    >
                      <div className="flex items-center">
                        <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <div>
                          <p className="text-white font-medium">{number.phoneNumber}</p>
                          <p className="text-gray-400 text-xs">{number.region || 'International'}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
          
          <div className="md:col-span-2">
            <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
              <h2 className="text-lg font-medium text-white mb-4">Call Forwarding</h2>
              
              {userNumbers.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-400">Purchase a phone number to configure call forwarding</p>
                </div>
              ) : (
                <div>
                  <div className="mb-6">
                    <h3 className="text-md font-medium text-white mb-3 flex items-center">
                      <PhoneArrowUpRightIcon className="h-5 w-5 mr-2 text-purple-400" />
                      Forwarding Numbers
                    </h3>
                    
                    {forwardingRules.length === 0 ? (
                      <div className="text-center py-4 bg-gray-800/30 rounded-lg">
                        <p className="text-gray-400">No forwarding numbers configured</p>
                        <p className="text-gray-500 text-sm mt-1">Add a number below to start forwarding calls</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {forwardingRules.map((rule) => (
                          <div key={rule.id} className="p-3 bg-gray-800/30 rounded-lg">
                            <div className="flex justify-between items-center">
                              <div className="flex items-center">
                                <div className="h-6 w-6 mr-3">
                                  <input 
                                    type="checkbox" 
                                    checked={rule.enabled}
                                    onChange={() => handleToggleRuleEnabled(rule.id)}
                                    className="h-4 w-4 rounded bg-gray-700 border-gray-600 text-purple-600 focus:ring-purple-600 focus:ring-offset-gray-900"
                                  />
                                </div>
                                <div>
                                  <p className="text-white font-medium">{rule.phoneNumber}</p>
                                  <div className="flex items-center text-gray-400 text-xs">
                                    <span>{rule.description}</span>
                                    {rule.delaySeconds > 0 && (
                                      <span className="ml-2 px-2 py-0.5 bg-gray-700 rounded-full text-gray-300">
                                        {rule.delaySeconds}s delay
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>
                              <button 
                                onClick={() => handleRemoveForwardingRule(rule.id)}
                                className="text-gray-400 hover:text-red-400"
                              >
                                <TrashIcon className="h-5 w-5" />
                              </button>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                    
                    <div className="mt-4 p-4 bg-gray-800/20 rounded-lg border border-gray-700/30">
                      <h4 className="text-sm font-medium text-white mb-3">Add Forwarding Number</h4>
                      <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
                        <div>
                          <label className="block text-xs font-medium text-gray-400 mb-1">
                            Phone Number
                          </label>
                          <input
                            type="text"
                            value={newForwardNumber}
                            onChange={(e) => setNewForwardNumber(e.target.value)}
                            placeholder="+****************"
                            className="w-full px-3 py-2 rounded-md border-0 bg-gray-800 text-white placeholder-gray-500 focus:ring-2 focus:ring-purple-600 sm:text-sm"
                          />
                        </div>
                        <div>
                          <label className="block text-xs font-medium text-gray-400 mb-1">
                            Description (optional)
                          </label>
                          <input
                            type="text"
                            value={newForwardDescription}
                            onChange={(e) => setNewForwardDescription(e.target.value)}
                            placeholder="My Cell Phone"
                            className="w-full px-3 py-2 rounded-md border-0 bg-gray-800 text-white placeholder-gray-500 focus:ring-2 focus:ring-purple-600 sm:text-sm"
                          />
                        </div>
                      </div>
                      
                      <div className="mt-3">
                        <label className="block text-xs font-medium text-gray-400 mb-1">
                          Ring Delay (seconds)
                        </label>
                        <input
                          type="number"
                          value={newForwardDelay}
                          onChange={(e) => setNewForwardDelay(e.target.value)}
                          min="0"
                          max="60"
                          className="w-full px-3 py-2 rounded-md border-0 bg-gray-800 text-white placeholder-gray-500 focus:ring-2 focus:ring-purple-600 sm:text-sm"
                        />
                        <p className="mt-1 text-xs text-gray-500">
                          How long to wait before ringing this number (0 = ring immediately)
                        </p>
                      </div>
                      
                      <div className="mt-4">
                        <button
                          onClick={handleAddForwardingRule}
                          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700"
                        >
                          <PlusIcon className="h-4 w-4 mr-1" />
                          Add Number
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-8 border-t border-gray-800 pt-6">
                    <h3 className="text-md font-medium text-white mb-3 flex items-center">
                      <ChatBubbleLeftEllipsisIcon className="h-5 w-5 mr-2 text-purple-400" />
                      Voicemail Settings
                    </h3>
                    
                    <div className="p-4 bg-gray-800/20 rounded-lg">
                      <div className="flex items-start">
                        <div className="h-5 pt-0.5">
                          <input 
                            type="checkbox" 
                            checked={voicemailEnabled}
                            onChange={() => setVoicemailEnabled(!voicemailEnabled)}
                            className="h-4 w-4 rounded bg-gray-700 border-gray-600 text-purple-600 focus:ring-purple-600 focus:ring-offset-gray-900"
                          />
                        </div>
                        <div className="ml-3">
                          <label className="text-sm font-medium text-white">
                            Enable Voicemail
                          </label>
                          <p className="text-xs text-gray-400 mt-1">
                            Allow callers to leave a voicemail if the call is not answered
                          </p>
                        </div>
                      </div>
                      
                      {voicemailEnabled && (
                        <div className="mt-4 pl-7">
                          <label className="block text-xs font-medium text-gray-400 mb-1">
                            Voicemail Delay (seconds)
                          </label>
                          <input
                            type="number"
                            value={voicemailDelay}
                            onChange={(e) => setVoicemailDelay(parseInt(e.target.value, 10))}
                            min="5"
                            max="60"
                            className="w-full max-w-xs px-3 py-2 rounded-md border-0 bg-gray-800 text-white placeholder-gray-500 focus:ring-2 focus:ring-purple-600 sm:text-sm"
                          />
                          <p className="mt-1 text-xs text-gray-500">
                            How long to ring before sending to voicemail
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-8 border-t border-gray-800 pt-6">
                    <h3 className="text-md font-medium text-white mb-3 flex items-center">
                      <ClockIcon className="h-5 w-5 mr-2 text-purple-400" />
                      Business Hours
                    </h3>
                    
                    <div className="p-4 bg-gray-800/20 rounded-lg">
                      <div className="flex items-start mb-4">
                        <div className="h-5 pt-0.5">
                          <input 
                            type="checkbox" 
                            checked={scheduleEnabled}
                            onChange={() => setScheduleEnabled(!scheduleEnabled)}
                            className="h-4 w-4 rounded bg-gray-700 border-gray-600 text-purple-600 focus:ring-purple-600 focus:ring-offset-gray-900"
                          />
                        </div>
                        <div className="ml-3">
                          <label className="text-sm font-medium text-white">
                            Enable Business Hours
                          </label>
                          <p className="text-xs text-gray-400 mt-1">
                            Only forward calls during specific hours
                          </p>
                        </div>
                      </div>
                      
                      {scheduleEnabled && (
                        <div className="pl-7">
                          <div className="space-y-3 mt-3">
                            {daysOfWeek.map(day => (
                              <div key={day.id} className="flex flex-wrap items-center">
                                <div className="w-28">
                                  <div className="flex items-center">
                                    <input 
                                      type="checkbox" 
                                      checked={businessHours[day.id].enabled}
                                      onChange={() => handleUpdateBusinessHour(day.id, 'enabled', !businessHours[day.id].enabled)}
                                      className="h-4 w-4 mr-2 rounded bg-gray-700 border-gray-600 text-purple-600 focus:ring-purple-600 focus:ring-offset-gray-900"
                                    />
                                    <span className="text-sm text-white">{day.label}</span>
                                  </div>
                                </div>
                                
                                {businessHours[day.id].enabled && (
                                  <div className="flex items-center mt-1 sm:mt-0">
                                    <input
                                      type="time"
                                      value={businessHours[day.id].start}
                                      onChange={(e) => handleUpdateBusinessHour(day.id, 'start', e.target.value)}
                                      className="px-2 py-1 rounded-md border-0 bg-gray-800 text-white focus:ring-2 focus:ring-purple-600 sm:text-sm"
                                    />
                                    <span className="mx-2 text-gray-400">to</span>
                                    <input
                                      type="time"
                                      value={businessHours[day.id].end}
                                      onChange={(e) => handleUpdateBusinessHour(day.id, 'end', e.target.value)}
                                      className="px-2 py-1 rounded-md border-0 bg-gray-800 text-white focus:ring-2 focus:ring-purple-600 sm:text-sm"
                                    />
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                          
                          <div className="mt-4 pt-4 border-t border-gray-700/30">
                            <label className="block text-xs font-medium text-white mb-2">
                              Outside Business Hours Behavior
                            </label>
                            <div className="space-y-2">
                              <div className="flex items-center">
                                <input 
                                  type="radio" 
                                  id="outside-voicemail" 
                                  name="outside-hours" 
                                  checked 
                                  className="h-4 w-4 rounded-full bg-gray-700 border-gray-600 text-purple-600 focus:ring-purple-600 focus:ring-offset-gray-900"
                                />
                                <label htmlFor="outside-voicemail" className="ml-2 text-sm text-gray-300">
                                  Send to voicemail
                                </label>
                              </div>
                              <div className="flex items-center">
                                <input 
                                  type="radio" 
                                  id="outside-message" 
                                  name="outside-hours" 
                                  className="h-4 w-4 rounded-full bg-gray-700 border-gray-600 text-purple-600 focus:ring-purple-600 focus:ring-offset-gray-900"
                                />
                                <label htmlFor="outside-message" className="ml-2 text-sm text-gray-300">
                                  Play message and hang up
                                </label>
                              </div>
                              <div className="flex items-center">
                                <input 
                                  type="radio" 
                                  id="outside-forward" 
                                  name="outside-hours" 
                                  className="h-4 w-4 rounded-full bg-gray-700 border-gray-600 text-purple-600 focus:ring-purple-600 focus:ring-offset-gray-900"
                                />
                                <label htmlFor="outside-forward" className="ml-2 text-sm text-gray-300">
                                  Forward to specified number
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
              
              <div className="mt-8 flex justify-end">
                <button
                  onClick={handleSaveSettings}
                  disabled={userNumbers.length === 0}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Save Settings
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 