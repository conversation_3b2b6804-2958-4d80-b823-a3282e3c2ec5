import { NextResponse } from 'next/server';

// Log but don't fail on Twilio credentials issues
const TWILIO_ACCOUNT_SID = process.env.TWILIO_ACCOUNT_SID || '**********************************';
const TWILIO_AUTH_TOKEN = process.env.TWILIO_AUTH_TOKEN || '54fd2e6197f5b3d02be2a7c4f1863af4';

// Get base URL for webhooks
const APP_BASE_URL = process.env.APP_BASE_URL || 'https://5f4d-73-170-246-149.ngrok-free.app';

// Helper function to format phone numbers
function formatPhoneNumber(phoneNumber) {
  if (phoneNumber.startsWith('+')) {
    return '+' + phoneNumber.substring(1).replace(/\D/g, '');
  } else {
    return '+' + phoneNumber.replace(/\D/g, '');
  }
}

// Simulation mode - provide realistic call flow
function simulateCall(fromNumber, toNumber, message) {
  // Generate a fake call SID
  const callSid = 'SM' + Date.now() + Math.floor(Math.random() * 1000);
  
  console.log(`[SIMULATION] Call from ${fromNumber} to ${toNumber}`);
  console.log(`[SIMULATION] Message: "${message}"`);
  console.log(`[SIMULATION] Call SID: ${callSid}`);
  
  return {
    success: true,
    message: 'Simulated call initiated successfully',
    callSid,
    status: 'ringing',
    isReal: false
  };
}

export async function POST(request) {
  try {
    // Parse request body
    const body = await request.json();
    const { fromNumber, toNumber, message } = body;
    
    console.log(`[InitiateCall API] Request to call ${toNumber} from ${fromNumber} with message: "${message}"`);
    
    // Validate input
    if (!fromNumber) {
      return NextResponse.json({
        success: false,
        message: 'From number is required'
      }, { status: 400 });
    }
    
    if (!toNumber) {
      return NextResponse.json({
        success: false,
        message: 'To number is required'
      }, { status: 400 });
    }
    
    if (!message) {
      return NextResponse.json({
        success: false,
        message: 'Message is required'
      }, { status: 400 });
    }
    
    // Format phone numbers
    const formattedFromNumber = formatPhoneNumber(fromNumber);
    const formattedToNumber = formatPhoneNumber(toNumber);
    
    // ALWAYS USE SIMULATION MODE - Twilio account has issues
    const simulationResult = simulateCall(formattedFromNumber, formattedToNumber, message);
    
    // Return the simulation response
    return NextResponse.json(simulationResult);
    
  } catch (error) {
    console.error('[InitiateCall API] Error processing request:', error);
    return NextResponse.json({
      success: false,
      message: `Error processing request: ${error.message}`
    }, { status: 400 });
  }
} 