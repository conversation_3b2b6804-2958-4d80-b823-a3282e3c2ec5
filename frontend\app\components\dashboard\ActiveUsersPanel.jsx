"use client";

import React, { useState } from 'react';
import { MultiLineChartComponent } from './ChartComponents';

const ActiveUsersPanel = ({ timeRange = '7days' }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Get time-range specific data
  const getUsersData = () => {
    switch(timeRange) {
      case 'today':
        return [
          { day: '9AM', active: 217, new: 12 },
          { day: '12PM', active: 253, new: 18 },
          { day: '3PM', active: 289, new: 15 },
          { day: '6PM', active: 312, new: 21 },
          { day: 'Now', active: 324, new: 14 },
        ];
      case '7days':
        return [
          { day: 'Mon', active: 320, new: 64 },
          { day: 'Tue', active: 332, new: 42 },
          { day: 'Wed', active: 348, new: 51 },
          { day: 'Thu', active: 338, new: 47 },
          { day: 'Fri', active: 365, new: 62 },
          { day: 'Sat', active: 287, new: 35 },
          { day: 'Sun', active: 293, new: 31 },
        ];
      case '30days':
        return [
          { day: 'Week 1', active: 345, new: 187 },
          { day: 'Week 2', active: 367, new: 163 },
          { day: 'Week 3', active: 389, new: 142 },
          { day: 'Week 4', active: 412, new: 156 },
        ];
      case '90days':
        return [
          { day: 'Sep', active: 298, new: 542 },
          { day: 'Oct', active: 345, new: 487 },
          { day: 'Nov', active: 412, new: 389 },
        ];
      default:
        return [
          { day: 'Mon', active: 320, new: 64 },
          { day: 'Tue', active: 332, new: 42 },
          { day: 'Wed', active: 348, new: 51 },
          { day: 'Thu', active: 338, new: 47 },
          { day: 'Fri', active: 365, new: 62 },
          { day: 'Sat', active: 287, new: 35 },
          { day: 'Sun', active: 293, new: 31 },
        ];
    }
  };
  
  // Get time range title
  const getTimeRangeTitle = () => {
    switch(timeRange) {
      case 'today':
        return 'Today';
      case '7days':
        return 'Weekly';
      case '30days':
        return 'Monthly';
      case '90days':
        return 'Quarterly';
      default:
        return 'Weekly';
    }
  };

  // Mock data for active users
  const usersData = getUsersData();

  // Calculate current active users and percentage growth
  const currentActive = usersData[usersData.length - 1].active;
  const previousActive = usersData[0].active;
  const growth = ((currentActive - previousActive) / previousActive * 100).toFixed(1);
  const isPositive = growth > 0;

  // Calculate total new users this period
  const totalNewUsers = usersData.reduce((sum, item) => sum + item.new, 0);

  // Mock data for user locations
  const userLocations = [
    { country: 'United States', users: 186, percentage: 36, flag: '🇺🇸' },
    { country: 'Germany', users: 94, percentage: 18, flag: '🇩🇪' },
    { country: 'United Kingdom', users: 78, percentage: 15, flag: '🇬🇧' },
    { country: 'Canada', users: 52, percentage: 10, flag: '🇨🇦' },
    { country: 'Other', users: 108, percentage: 21, flag: '🌍' },
  ];
  
  // Additional detailed metrics (for expanded view)
  const detailedMetrics = [
    { name: 'Avg. Session Duration', value: '8:24', change: '+0:45', positive: true },
    { name: 'Pages per Session', value: '4.2', change: '+0.8', positive: true },
    { name: 'Bounce Rate', value: '28%', change: '-5%', positive: true },
    { name: 'Retention Rate', value: '68%', change: '+12%', positive: true },
  ];
  
  const userDevices = [
    { device: 'Mobile', percentage: 65, icon: '📱' },
    { device: 'Desktop', percentage: 28, icon: '💻' },
    { device: 'Tablet', percentage: 7, icon: '📟' },
  ];

  return (
    <div className="bg-gray-900 p-6 rounded-xl border border-gray-700 h-full shadow-md shadow-black/20 relative">
      {/* Expand toggle button */}
      <button 
        onClick={() => setIsExpanded(!isExpanded)}
        className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
        aria-label={isExpanded ? "Collapse panel" : "Expand panel"}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          {isExpanded ? (
            <path fillRule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clipRule="evenodd" />
          ) : (
            <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
          )}
        </svg>
      </button>
      
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-medium text-white">{getTimeRangeTitle()} Active Users</h3>
        <div className="flex items-center">
          <div className={`px-2 py-1 rounded-full text-xs flex items-center shadow-sm ${isPositive ? 'text-green-300 bg-green-900' : 'text-red-300 bg-red-900'}`}>
            {isPositive ? '▲' : '▼'} {growth}%
            {isPositive ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1v-5a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586l-4.293-4.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z" clipRule="evenodd" />
              </svg>
            )}
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4 mb-6">
        <div className="bg-purple-900 rounded-lg p-4 shadow-md shadow-purple-900/30 group hover:bg-purple-800 transition-colors cursor-pointer relative">
          <div className="text-sm text-gray-300 mb-1">Current active</div>
          <div className="text-2xl font-bold text-white">{currentActive}</div>
          <div className="text-xs text-gray-400 mt-1">
            {isPositive ? '+' : '-'}{Math.abs(currentActive - previousActive)} from start of period
          </div>
          <div className="absolute top-2 right-2 bg-purple-800 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-200" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
        <div className="bg-blue-900 rounded-lg p-4 shadow-md shadow-blue-900/30 group hover:bg-blue-800 transition-colors cursor-pointer relative">
          <div className="text-sm text-gray-300 mb-1">New users</div>
          <div className="text-2xl font-bold text-white">{totalNewUsers}</div>
          <div className="text-xs text-gray-400 mt-1">
            This {timeRange === 'today' ? 'day' : timeRange === '7days' ? 'week' : timeRange === '30days' ? 'month' : 'quarter'}
          </div>
          <div className="absolute top-2 right-2 bg-blue-800 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-blue-200" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
      </div>
      
      <div className={`h-40 ${isExpanded ? 'h-56' : ''} transition-all duration-300 mb-6`}>
        <MultiLineChartComponent 
          data={usersData} 
          height={isExpanded ? 224 : 160} 
        />
      </div>
      
      <div className="bg-gray-800 p-4 rounded-lg group hover:bg-gray-750 transition-colors cursor-pointer">
        <div className="flex justify-between items-center mb-3">
          <h4 className="text-sm font-medium text-white">Top User Locations</h4>
          <span className="text-xs bg-blue-900 text-blue-300 px-2 py-0.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">View All</span>
        </div>
        
        <div className="space-y-2">
          {userLocations.map((location, index) => (
            <div key={index} className="flex items-center group/item hover:bg-gray-700 p-1 rounded transition-colors">
              <div className="text-sm text-gray-300 w-6">{location.flag}</div>
              <div className="text-sm text-gray-300 w-28 ml-2">{location.country}</div>
              <div className="flex-1 mx-2">
                <div className="w-full bg-gray-950 h-2 rounded-full">
                  <div 
                    className="bg-gradient-to-r from-purple-600 to-blue-500 h-2 rounded-full shadow-sm" 
                    style={{ width: `${location.percentage}%` }}
                  ></div>
                </div>
              </div>
              <div className="text-xs text-gray-300">{location.users} ({location.percentage}%)</div>
            </div>
          ))}
        </div>
      </div>
      
      {isExpanded && (
        <div className="mt-6 space-y-6 pt-4 border-t border-gray-700 animate-fadeIn">
          <div className="grid grid-cols-2 gap-3">
            {detailedMetrics.map((metric, index) => (
              <div 
                key={index} 
                className="bg-gray-800 p-3 rounded-lg hover:bg-gray-750 transition-colors cursor-pointer"
              >
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-300">{metric.name}</span>
                  <span className={`text-xs px-1.5 py-0.5 rounded-full ${metric.positive ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'}`}>
                    {metric.change}
                  </span>
                </div>
                <div className="text-lg font-medium text-white mt-1">{metric.value}</div>
              </div>
            ))}
          </div>
          
          <div className="bg-gray-800 p-4 rounded-lg">
            <h5 className="text-sm font-medium text-white mb-3">Users by Device</h5>
            <div className="flex items-center justify-between">
              {userDevices.map((device, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div className="text-2xl mb-1">{device.icon}</div>
                  <div className="text-lg font-medium text-white">{device.percentage}%</div>
                  <div className="text-xs text-gray-300">{device.device}</div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="flex justify-end">
            <button className="text-sm text-purple-400 hover:text-purple-300 transition-colors px-2 py-1 rounded hover:bg-purple-900/30">
              Download Analytics Report →
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ActiveUsersPanel; 