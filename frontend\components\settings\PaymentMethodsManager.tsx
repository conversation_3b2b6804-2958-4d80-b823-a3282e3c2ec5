'use client';

import { useState } from 'react';
import { 
  PaymentMethod, 
  useRemovePaymentMethod, 
  useSetDefaultPaymentMethod 
} from '../../../hooks/useSettings';

interface PaymentMethodsManagerProps {
  paymentMethods: PaymentMethod[];
  onSuccess: (message: string) => void;
}

export default function PaymentMethodsManager({ 
  paymentMethods, 
  onSuccess 
}: PaymentMethodsManagerProps) {
  const [methodToRemove, setMethodToRemove] = useState<string | null>(null);
  
  // Remove payment method mutation
  const removePaymentMethod = useRemovePaymentMethod();
  
  // Set default payment method mutation
  const setDefaultPaymentMethod = useSetDefaultPaymentMethod();
  
  // Handle remove payment method
  const handleRemovePaymentMethod = async (methodId: string) => {
    setMethodToRemove(methodId);
    
    try {
      await removePaymentMethod.mutateAsync(methodId);
      onSuccess('Payment method removed successfully');
    } catch (err) {
      // Error is handled by the mutation
      console.error('Error removing payment method:', err);
    } finally {
      setMethodToRemove(null);
    }
  };
  
  // Handle set default payment method
  const handleSetDefaultPaymentMethod = async (methodId: string) => {
    try {
      await setDefaultPaymentMethod.mutateAsync(methodId);
      onSuccess('Default payment method updated successfully');
    } catch (err) {
      // Error is handled by the mutation
      console.error('Error setting default payment method:', err);
    }
  };
  
  // Get card icon
  const getCardIcon = (brand?: string) => {
    switch (brand?.toLowerCase()) {
      case 'visa':
        return (
          <svg className="h-6 w-6" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="32" height="32" rx="4" fill="#F7F9FC" />
            <path d="M11.5 19.5H9L7 12.5H9.5L10.5 17L12.5 12.5H15L11.5 19.5Z" fill="#172B4D" />
            <path d="M15.5 19.5H18L19 12.5H16.5L15.5 19.5Z" fill="#172B4D" />
            <path d="M24.5 12.5H22.5C21.5 12.5 20.5 13.5 20.5 14.5L19.5 19.5H22L22.5 18H24.5L25 19.5H27.5L24.5 12.5ZM23 16.5L23.5 15C23.5 15.5 24 16 24.5 16L23 16.5Z" fill="#172B4D" />
          </svg>
        );
      case 'mastercard':
        return (
          <svg className="h-6 w-6" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="32" height="32" rx="4" fill="#F7F9FC" />
            <path d="M16 21C18.7614 21 21 18.7614 21 16C21 13.2386 18.7614 11 16 11C13.2386 11 11 13.2386 11 16C11 18.7614 13.2386 21 16 21Z" fill="#EB001B" />
            <path d="M16 21C18.7614 21 21 18.7614 21 16C21 13.2386 18.7614 11 16 11" fill="#F79E1B" />
            <path d="M16 21C18.7614 21 21 18.7614 21 16C21 13.2386 18.7614 11 16 11" fill="#FF5F00" />
            <path d="M22 21C24.7614 21 27 18.7614 27 16C27 13.2386 24.7614 11 22 11C19.2386 11 17 13.2386 17 16C17 18.7614 19.2386 21 22 21Z" fill="#F79E1B" />
          </svg>
        );
      case 'amex':
        return (
          <svg className="h-6 w-6" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="32" height="32" rx="4" fill="#F7F9FC" />
            <path d="M7 21H25V11H7V21Z" fill="#006FCF" />
            <path d="M16 18L17.5 16L16 14L14.5 16L16 18Z" fill="white" />
            <path d="M22 18L23.5 16L22 14L20.5 16L22 18Z" fill="white" />
            <path d="M10 18L11.5 16L10 14L8.5 16L10 18Z" fill="white" />
          </svg>
        );
      case 'discover':
        return (
          <svg className="h-6 w-6" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="32" height="32" rx="4" fill="#F7F9FC" />
            <path d="M7 21H25V11H7V21Z" fill="#4D4D4D" />
            <path d="M16 19C17.6569 19 19 17.6569 19 16C19 14.3431 17.6569 13 16 13C14.3431 13 13 14.3431 13 16C13 17.6569 14.3431 19 16 19Z" fill="#FF6600" />
          </svg>
        );
      default:
        return (
          <svg className="h-6 w-6" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="32" height="32" rx="4" fill="#F7F9FC" />
            <path d="M9 11H23C24.1046 11 25 11.8954 25 13V19C25 20.1046 24.1046 21 23 21H9C7.89543 21 7 20.1046 7 19V13C7 11.8954 7.89543 11 9 11Z" stroke="#A5ADBA" strokeWidth="2" />
            <path d="M7 15H25" stroke="#A5ADBA" strokeWidth="2" />
          </svg>
        );
    }
  };
  
  if (paymentMethods.length === 0) {
    return (
      <div className="text-center py-8 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <p className="text-gray-500 dark:text-gray-400 mb-4">
          No payment methods available.
        </p>
        <a
          href="/billing/payment-methods/add"
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Add Payment Method
        </a>
      </div>
    );
  }
  
  return (
    <div>
      {removePaymentMethod.isError && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-800 dark:text-red-400 text-sm">
          {removePaymentMethod.error instanceof Error 
            ? removePaymentMethod.error.message 
            : 'Failed to remove payment method. Please try again.'}
        </div>
      )}
      
      {setDefaultPaymentMethod.isError && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-800 dark:text-red-400 text-sm">
          {setDefaultPaymentMethod.error instanceof Error 
            ? setDefaultPaymentMethod.error.message 
            : 'Failed to set default payment method. Please try again.'}
        </div>
      )}
      
      <div className="space-y-4 mb-6">
        {paymentMethods.map((method) => (
          <div 
            key={method.id} 
            className={`flex items-center justify-between p-4 rounded-lg border ${
              method.isDefault 
                ? 'bg-indigo-50 dark:bg-indigo-900/10 border-indigo-200 dark:border-indigo-800' 
                : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
            }`}
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                {method.type === 'card' ? getCardIcon(method.brand) : (
                  <svg className="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {method.type === 'card' 
                    ? `${method.brand || 'Card'} •••• ${method.last4}` 
                    : `Bank Account •••• ${method.last4}`}
                </p>
                {method.type === 'card' && method.expiryMonth && method.expiryYear && (
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Expires {method.expiryMonth.toString().padStart(2, '0')}/{method.expiryYear.toString().slice(-2)}
                  </p>
                )}
                {method.isDefault && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300 mt-1">
                    Default
                  </span>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {!method.isDefault && (
                <button
                  type="button"
                  onClick={() => handleSetDefaultPaymentMethod(method.id)}
                  disabled={setDefaultPaymentMethod.isPending}
                  className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 text-sm font-medium"
                >
                  {setDefaultPaymentMethod.isPending && setDefaultPaymentMethod.variables === method.id 
                    ? 'Setting...' 
                    : 'Make Default'}
                </button>
              )}
              <button
                type="button"
                onClick={() => handleRemovePaymentMethod(method.id)}
                disabled={removePaymentMethod.isPending || method.isDefault}
                className={`text-sm font-medium ${
                  method.isDefault 
                    ? 'text-gray-400 dark:text-gray-500 cursor-not-allowed' 
                    : 'text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300'
                }`}
              >
                {removePaymentMethod.isPending && methodToRemove === method.id 
                  ? 'Removing...' 
                  : 'Remove'}
              </button>
            </div>
          </div>
        ))}
      </div>
      
      <div className="flex justify-between items-center">
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {method.isDefault ? 'Default payment method cannot be removed.' : ''}
        </p>
        <a
          href="/billing/payment-methods/add"
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Add Payment Method
        </a>
      </div>
    </div>
  );
}
