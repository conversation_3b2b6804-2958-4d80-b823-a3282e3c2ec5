'use client';

import { useState, useEffect } from 'react';
import { AutomationSchedule } from '../../../hooks/useScheduledAutomation';
import { CalendarIcon, ClockIcon } from '@heroicons/react/24/outline';
import { format } from 'date-fns';

interface ScheduleConfiguratorProps {
  schedule: AutomationSchedule;
  onChange: (schedule: AutomationSchedule) => void;
}

export default function ScheduleConfigurator({
  schedule,
  onChange,
}: ScheduleConfiguratorProps) {
  // Local state for date and time inputs
  const [date, setDate] = useState('');
  const [time, setTime] = useState('');
  
  // Initialize date and time from schedule
  useEffect(() => {
    if (schedule.type === 'once' && schedule.dateTime) {
      try {
        const dateObj = new Date(schedule.dateTime);
        setDate(format(dateObj, 'yyyy-MM-dd'));
        setTime(format(dateObj, 'HH:mm'));
      } catch (error) {
        console.error('Invalid date format:', error);
      }
    }
  }, [schedule]);
  
  // Update schedule when date or time changes
  useEffect(() => {
    if (schedule.type === 'once' && date && time) {
      const dateTime = new Date(`${date}T${time}`);
      if (!isNaN(dateTime.getTime())) {
        onChange({
          ...schedule,
          dateTime: dateTime.toISOString(),
        });
      }
    }
  }, [date, time, schedule.type]);
  
  // Handle schedule type change
  const handleTypeChange = (type: 'once' | 'recurring') => {
    onChange({
      ...schedule,
      type,
      // Reset other fields based on type
      dateTime: type === 'once' ? schedule.dateTime : undefined,
      cronExpression: type === 'recurring' ? schedule.cronExpression : undefined,
    });
  };
  
  // Handle cron expression change
  const handleCronChange = (expression: string) => {
    onChange({
      ...schedule,
      cronExpression: expression,
    });
  };
  
  // Handle timezone change
  const handleTimezoneChange = (timezone: string) => {
    onChange({
      ...schedule,
      timezone,
    });
  };
  
  // Handle end date change
  const handleEndDateChange = (endDate?: string) => {
    onChange({
      ...schedule,
      endDate,
    });
  };
  
  // Get human-readable description of cron expression
  const getCronDescription = (expression?: string) => {
    if (!expression) return 'No schedule set';
    
    // This is a simplified implementation
    // In a real app, you would use a library like cron-parser to parse and describe the expression
    try {
      const parts = expression.split(' ');
      if (parts.length !== 5) return 'Invalid cron expression';
      
      const [minute, hour, dayOfMonth, month, dayOfWeek] = parts;
      
      if (minute === '*' && hour === '*' && dayOfMonth === '*' && month === '*' && dayOfWeek === '*') {
        return 'Every minute';
      }
      
      if (minute === '0' && hour === '*' && dayOfMonth === '*' && month === '*' && dayOfWeek === '*') {
        return 'Every hour';
      }
      
      if (minute === '0' && hour === '0' && dayOfMonth === '*' && month === '*' && dayOfWeek === '*') {
        return 'Every day at midnight';
      }
      
      if (minute === '0' && hour === '9' && dayOfMonth === '*' && month === '*' && dayOfWeek === '1-5') {
        return 'Every weekday at 9:00 AM';
      }
      
      return `Custom schedule: ${expression}`;
    } catch (error) {
      return 'Invalid cron expression';
    }
  };
  
  // Common cron expressions
  const commonCronExpressions = [
    { label: 'Every day at 9:00 AM', value: '0 9 * * *' },
    { label: 'Every weekday at 9:00 AM', value: '0 9 * * 1-5' },
    { label: 'Every hour', value: '0 * * * *' },
    { label: 'Every Monday at 10:00 AM', value: '0 10 * * 1' },
    { label: 'First day of month at midnight', value: '0 0 1 * *' },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Schedule Configuration</h2>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
          Set when your automation should run. You can schedule it to run once or on a recurring basis.
        </p>
      </div>

      {/* Schedule type selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Schedule Type
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Once option */}
          <div
            className={`border rounded-lg p-4 cursor-pointer ${
              schedule.type === 'once'
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
            }`}
            onClick={() => handleTypeChange('once')}
          >
            <div className="flex items-center">
              <div className={`p-2 rounded-full ${
                schedule.type === 'once'
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
              }`}>
                <CalendarIcon className="h-5 w-5" />
              </div>
              <div className="ml-3">
                <h3 className={`font-medium ${
                  schedule.type === 'once'
                    ? 'text-blue-700 dark:text-blue-400'
                    : 'text-gray-900 dark:text-white'
                }`}>
                  Run Once
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Schedule for a specific date and time
                </p>
              </div>
            </div>
          </div>

          {/* Recurring option */}
          <div
            className={`border rounded-lg p-4 cursor-pointer ${
              schedule.type === 'recurring'
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30'
                : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
            }`}
            onClick={() => handleTypeChange('recurring')}
          >
            <div className="flex items-center">
              <div className={`p-2 rounded-full ${
                schedule.type === 'recurring'
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
              }`}>
                <ClockIcon className="h-5 w-5" />
              </div>
              <div className="ml-3">
                <h3 className={`font-medium ${
                  schedule.type === 'recurring'
                    ? 'text-blue-700 dark:text-blue-400'
                    : 'text-gray-900 dark:text-white'
                }`}>
                  Recurring
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Schedule to run on a regular basis
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Schedule details based on type */}
      {schedule.type === 'once' ? (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Date picker */}
            <div>
              <label htmlFor="date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                id="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                min={format(new Date(), 'yyyy-MM-dd')}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
            
            {/* Time picker */}
            <div>
              <label htmlFor="time" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Time <span className="text-red-500">*</span>
              </label>
              <input
                type="time"
                id="time"
                value={time}
                onChange={(e) => setTime(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Cron expression selector */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Recurrence Pattern <span className="text-red-500">*</span>
            </label>
            <select
              value={schedule.cronExpression || ''}
              onChange={(e) => handleCronChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              required
            >
              <option value="">Select a pattern</option>
              {commonCronExpressions.map((expr) => (
                <option key={expr.value} value={expr.value}>
                  {expr.label}
                </option>
              ))}
            </select>
            
            {schedule.cronExpression && (
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {getCronDescription(schedule.cronExpression)}
              </p>
            )}
          </div>
          
          {/* End date (optional) */}
          <div>
            <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              End Date (Optional)
            </label>
            <input
              type="date"
              id="endDate"
              value={schedule.endDate || ''}
              onChange={(e) => handleEndDateChange(e.target.value || undefined)}
              min={format(new Date(), 'yyyy-MM-dd')}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              If set, the automation will stop running after this date.
            </p>
          </div>
        </div>
      )}

      {/* Timezone selector */}
      <div>
        <label htmlFor="timezone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Timezone
        </label>
        <select
          id="timezone"
          value={schedule.timezone}
          onChange={(e) => handleTimezoneChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
        >
          <option value="America/New_York">Eastern Time (ET)</option>
          <option value="America/Chicago">Central Time (CT)</option>
          <option value="America/Denver">Mountain Time (MT)</option>
          <option value="America/Los_Angeles">Pacific Time (PT)</option>
          <option value="America/Anchorage">Alaska Time (AKT)</option>
          <option value="Pacific/Honolulu">Hawaii Time (HT)</option>
          <option value="UTC">UTC</option>
        </select>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          All times will be interpreted in this timezone.
        </p>
      </div>
    </div>
  );
}
