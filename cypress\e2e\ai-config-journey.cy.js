/**
 * E2E Test for AI Assistant Configuration Journey
 * 
 * This test covers the process of configuring the AI assistant,
 * including personality selection and knowledge base management.
 */

describe('AI Assistant Configuration Journey', () => {
  beforeEach(() => {
    // Log in before each test
    cy.login();
    
    // Mock API responses
    cy.intercept('GET', '/api/numbers', {
      statusCode: 200,
      body: {
        numbers: [
          {
            id: 'number1',
            phoneNumber: '+15551234567',
            status: 'active',
            aiEnabled: true,
            forwardingEnabled: false,
            createdAt: new Date().toISOString()
          }
        ]
      }
    }).as('getNumbers');
    
    cy.intercept('GET', '/api/numbers/number1/ai-assistant', {
      statusCode: 200,
      body: {
        personality: 'professional',
        knowledgeSources: [
          { id: 'kb1', name: 'Company FAQ', type: 'document' }
        ],
        commands: [
          { id: 'cmd1', trigger: 'hours', response: 'We are open from 9am to 5pm.' }
        ]
      }
    }).as('getAiConfig');
    
    cy.intercept('PUT', '/api/numbers/number1/ai-assistant', {
      statusCode: 200,
      body: {
        success: true
      }
    }).as('updateAiConfig');
    
    cy.intercept('POST', '/api/automation/*/test-response', {
      statusCode: 200,
      body: {
        response: 'This is a test AI response. I am the CallSaver AI assistant. How can I help you today?',
        audioUrl: 'https://example.com/test-audio.mp3'
      }
    }).as('aiResponse');
  });
  
  it('should display AI configuration for a phone number', () => {
    // Navigate to the number details page
    cy.visit('/dashboard/numbers/number1');
    
    // Wait for data to load
    cy.wait('@getNumbers');
    cy.wait('@getAiConfig');
    
    // Verify page content
    cy.get('h1').should('contain', 'Phone Number');
    cy.get('[data-testid="phone-number"]').should('contain', '+15551234567');
    
    // Verify AI configuration tab
    cy.get('button').contains('AI Assistant').click();
    
    // Verify AI configuration form
    cy.get('h3').should('contain', 'AI Assistant Configuration');
    cy.get('[data-testid="personality-selector"]').should('be.visible');
    cy.get('[data-testid="knowledge-uploader"]').should('be.visible');
    cy.get('[data-testid="commands-editor"]').should('be.visible');
  });
  
  it('should allow changing the AI personality', () => {
    // Navigate to the number's AI configuration
    cy.visit('/dashboard/numbers/number1/ai-assistant');
    
    // Wait for data to load
    cy.wait('@getAiConfig');
    
    // Change personality
    cy.get('[data-testid="personality-select"]').select('friendly');
    
    // Save changes
    cy.get('button').contains('Save Changes').click();
    
    // Wait for update request
    cy.wait('@updateAiConfig');
    
    // Verify success message
    cy.get('[data-testid="success-message"]').should('be.visible');
  });
  
  it('should allow adding knowledge sources', () => {
    // Navigate to the number's AI configuration
    cy.visit('/dashboard/numbers/number1/ai-assistant');
    
    // Wait for data to load
    cy.wait('@getAiConfig');
    
    // Add knowledge source
    cy.get('[data-testid="add-knowledge"]').click();
    
    // Save changes
    cy.get('button').contains('Save Changes').click();
    
    // Wait for update request
    cy.wait('@updateAiConfig');
    
    // Verify success message
    cy.get('[data-testid="success-message"]').should('be.visible');
  });
  
  it('should allow adding custom commands', () => {
    // Navigate to the number's AI configuration
    cy.visit('/dashboard/numbers/number1/ai-assistant');
    
    // Wait for data to load
    cy.wait('@getAiConfig');
    
    // Add custom command
    cy.get('[data-testid="add-command"]').click();
    
    // Save changes
    cy.get('button').contains('Save Changes').click();
    
    // Wait for update request
    cy.wait('@updateAiConfig');
    
    // Verify success message
    cy.get('[data-testid="success-message"]').should('be.visible');
  });
  
  it('should allow testing the AI assistant for calls', () => {
    // Navigate to the number's AI configuration
    cy.visit('/dashboard/numbers/number1/ai-assistant');
    
    // Wait for data to load
    cy.wait('@getAiConfig');
    
    // Switch to Test Call tab
    cy.get('button').contains('Test Call').click();
    
    // Verify tester is visible
    cy.get('[data-testid="conversation-tester-call"]').should('be.visible');
    
    // Type a test message
    cy.get('input[placeholder*="what you would say"]').type('Hello, tell me about your services');
    
    // Send message
    cy.get('button[title="Send message"]').click();
    
    // Wait for AI response
    cy.wait('@aiResponse');
    
    // Verify response appears in the conversation
    cy.get('[data-testid="conversation-tester-call"]').should('contain', 'This is a test AI response');
  });
  
  it('should allow testing the AI assistant for SMS', () => {
    // Navigate to the number's AI configuration
    cy.visit('/dashboard/numbers/number1/ai-assistant');
    
    // Wait for data to load
    cy.wait('@getAiConfig');
    
    // Switch to Test SMS tab
    cy.get('button').contains('Test SMS').click();
    
    // Verify tester is visible
    cy.get('[data-testid="conversation-tester-sms"]').should('be.visible');
    
    // Type a test message
    cy.get('input[placeholder*="your message"]').type('Hello, I have a question about pricing');
    
    // Send message
    cy.get('button[title="Send message"]').click();
    
    // Wait for AI response
    cy.wait('@aiResponse');
    
    // Verify response appears in the conversation
    cy.get('[data-testid="conversation-tester-sms"]').should('contain', 'This is a test AI response');
  });
});
