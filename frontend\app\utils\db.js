import postgres from 'postgres'

// Direct connection for persistent applications
const connectionString = process.env.DATABASE_URL
const sql = postgres(connectionString, {
  // Maximum number of connections
  max: 10,
  // Idle timeout after which a connection is closed
  idle_timeout: 30,
  // Connection timeout
  connect_timeout: 15,
  // SSL required for Supabase
  ssl: {
    rejectUnauthorized: false
  }
})

export default sql 