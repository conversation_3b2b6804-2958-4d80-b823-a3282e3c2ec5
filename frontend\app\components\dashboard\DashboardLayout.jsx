'use client';

import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import { useState, useEffect } from 'react';

import { getNavigationItems, isAdmin } from '../../utils/roleUtils'; // Import role utilities
import getSupabaseClient from '../../utils/supabaseClient'; // Import the function

export default function DashboardLayout({ children }) {
  const router = useRouter();
  const pathname = usePathname();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isSidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobileSidebarOpen, setMobileSidebarOpen] = useState(false);

  useEffect(() => {
    // Check for authenticated user
    const checkUser = async () => {
      try {
        // Check for demo user in localStorage first
        const demoUser = localStorage.getItem('callsaver_demo_user');
        if (demoUser) {
          // Use demo user data
          const parsedUser = JSON.parse(demoUser);
          setUser({
            id: parsedUser.id,
            email: parsedUser.email,
            user_metadata: {
              name: parsedUser.name,
              role: parsedUser.role
            }
          });
          setLoading(false);
          return;
        }

        // Get the client instance
        const supabase = getSupabaseClient();
        if (!supabase) {
          console.error('DashboardLayout: Failed to get Supabase client.');
          // Redirect immediately if client fails to initialize
          router.push('/signin');
          return;
        }

        // Check Supabase session
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Session error:', error.message);
          // If we've just signed in (check URL params or localStorage flag)
          if (localStorage.getItem('just_signed_in') || pathname.includes('?just_signed_in=true')) {
            console.log('Recently signed in, retrying session check in 2 seconds...');
            // Set a flag for a retry and delay the redirect
            localStorage.setItem('auth_retry_count', '1');

            // Wait 2 seconds before retrying
            setTimeout(async () => {
              const supabaseRetry = getSupabaseClient(); // Get client again for retry
              if (!supabaseRetry) {
                 console.error('DashboardLayout: Failed to get Supabase client for retry.');
                 router.push('/signin');
                 return;
              }
              const retryResult = await supabaseRetry.auth.getSession();
              if (retryResult.data?.session) {
                // Session found on retry
                console.log('Session found on retry');
                setUser(retryResult.data.session.user);
                setLoading(false);
                localStorage.removeItem('just_signed_in');
                localStorage.removeItem('auth_retry_count');
              } else {
                // Still no session after retry, redirect
                console.log('No session found after retry, redirecting');
                localStorage.removeItem('just_signed_in');
                localStorage.removeItem('auth_retry_count');
                router.push('/signin');
              }
            }, 2000);
            return;
          }

          // Otherwise, redirect immediately
          router.push('/signin');
          return;
        }

        if (!session) {
          console.log('No session found, checking if recently signed in');

          // If we've just signed in, give it another chance with a delay
          if (localStorage.getItem('just_signed_in') || pathname.includes('?just_signed_in=true')) {
            console.log('Recently signed in, retrying session check in 2 seconds...');

            // Check retry count to avoid infinite loops
            const retryCount = parseInt(localStorage.getItem('auth_retry_count') || '0');
            if (retryCount < 3) {
              localStorage.setItem('auth_retry_count', (retryCount + 1).toString());

            // Wait 2 seconds before retrying
            setTimeout(async () => {
              const supabaseRetry = getSupabaseClient(); // Get client again for retry
              if (!supabaseRetry) {
                 console.error('DashboardLayout: Failed to get Supabase client for retry (no session case).');
                 router.push('/signin');
                 return;
              }
              const retryResult = await supabaseRetry.auth.getSession();
              if (retryResult.data?.session) {
                // Session found on retry
                  console.log('Session found on retry');
                  setUser(retryResult.data.session.user);
                  setLoading(false);
                  localStorage.removeItem('just_signed_in');
                  localStorage.removeItem('auth_retry_count');
                } else {
                  // Still no session after retry, redirect
                  console.log('No session found after retry, redirecting');
                  localStorage.removeItem('just_signed_in');
                  localStorage.removeItem('auth_retry_count');
                  router.push('/signin');
                }
              }, 2000);
              return;
            }
          }

          console.log('No session found, redirecting to sign-in');
          // Add a slight delay to prevent rapid redirects
          setTimeout(() => {
            router.push('/signin');
          }, 300);
          return;
        }

        // User is authenticated
        setUser(session.user);
        setLoading(false);
      } catch (error) {
        console.error('Authentication error:', error.message);
        // Add a slight delay to prevent rapid redirects
        setTimeout(() => {
          router.push('/signin');
        }, 300);
      }
    };

    checkUser();
  }, [router, pathname]);

  const handleSignOut = async () => {
    try {
      // Check if demo user
      const demoUser = localStorage.getItem('callsaver_demo_user');
      if (demoUser) {
        localStorage.removeItem('callsaver_demo_user');
      } else {
        const supabase = getSupabaseClient(); // Get client instance
        if (supabase) {
          await supabase.auth.signOut();
        } else {
          console.error("DashboardLayout: Failed to get Supabase client for sign out.");
          // Attempt to clear local session anyway
        }
      }

      // Add a slight delay to prevent rapid redirects
      setTimeout(() => {
        router.push('/signin');
      }, 300);
    } catch (error) {
      console.error('Sign out error:', error);
      router.push('/signin');
    }
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!isSidebarCollapsed);
  };

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!isMobileSidebarOpen);
  };

  // Get navigation items based on user role
  const navigationItems = getNavigationItems(user);

  // Check if user is admin
  const userIsAdmin = isAdmin(user);
  console.log('User is admin:', userIsAdmin);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-950">
        <div className="animate-spin w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full"></div>
        <p className="mt-4 text-gray-400">Loading dashboard...</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-950 text-white flex">
      {/* Mobile sidebar backdrop */}
      {isMobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden"
          onClick={toggleMobileSidebar}
          onKeyDown={(e) => e.key === 'Escape' && toggleMobileSidebar()}
          role="button"
          tabIndex={0}
          aria-label="Close sidebar"
        ></div>
      )}

      {/* Sidebar - Desktop & Mobile */}
      <aside
        className={`
          fixed top-0 left-0 h-full bg-gray-900 border-r border-gray-800 z-30
          transition-all duration-300 ease-in-out
          ${isSidebarCollapsed ? 'w-20' : 'w-64'}
          ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full'}
          lg:translate-x-0
        `}
      >
        {/* Logo - Made clickable to navigate to main website */}
        <div className="flex items-center justify-between h-16 px-4 border-b border-gray-800">
          <Link href="/" className="flex items-center">
            <div className="relative w-10 h-10 mr-2 flex-shrink-0">
              <div className="absolute w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
            </div>
            {!isSidebarCollapsed && <h1 className="text-xl font-bold">CallSaver</h1>}
          </Link>
          {/* Toggle button - desktop only */}
          <button
            onClick={toggleSidebar}
            className="p-1 rounded-md text-gray-400 hover:bg-gray-800 hover:text-white hidden lg:block"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              {isSidebarCollapsed
                ? <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                : <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
              }
            </svg>
          </button>
          {/* Mobile menu button */}
          <button
            className="p-1 rounded-md text-gray-400 hover:bg-gray-800 hover:text-white lg:hidden"
            onClick={toggleMobileSidebar}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

      {/* Navigation */}
      <nav className="mt-4 px-2 overflow-y-auto h-[calc(100vh-12rem)]">
        <ul className="space-y-1">
          {/* Regular navigation items */}
          {navigationItems.filter(item => !item.path.includes('/admin')).map((item) => (
            <li key={item.name}>
              <Link
                href={item.path}
                className={`
                  flex items-center px-3 py-3 rounded-lg transition-colors
                  ${pathname === item.path
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:bg-gray-800 hover:text-white'
                  }
                `}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className={`${isSidebarCollapsed ? 'w-7 h-7' : 'w-5 h-5'} mr-3`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d={item.icon}
                  />
                </svg>
                {!isSidebarCollapsed && <span>{item.name}</span>}
              </Link>
            </li>
          ))}

          {/* Admin section divider */}
          {userIsAdmin && !isSidebarCollapsed && (
            <li className="mt-6 mb-2">
              <div className="px-3">
                <div className="h-px bg-gray-700"></div>
                <p className="text-xs text-gray-500 mt-2">ADMIN SECTIONS</p>
              </div>
            </li>
          )}

          {/* Admin navigation items */}
          {userIsAdmin && navigationItems.filter(item => item.path.includes('/admin')).map((item) => (
            <li key={item.name}>
              <Link
                href={item.path}
                className={`
                  flex items-center px-3 py-3 rounded-lg transition-colors
                  ${pathname === item.path
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-400 hover:bg-gray-800 hover:text-white'
                  }
                `}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className={`${isSidebarCollapsed ? 'w-7 h-7' : 'w-5 h-5'} mr-3`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d={item.icon}
                  />
                </svg>
                {!isSidebarCollapsed && <span>{item.name}</span>}
              </Link>
            </li>
          ))}
        </ul>
      </nav>

        {/* User profile */}
        <div className="absolute bottom-0 left-0 right-0 border-t border-gray-800 p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-purple-700 flex items-center justify-center text-white font-medium flex-shrink-0">
              {user?.email?.charAt(0).toUpperCase() || 'U'}
            </div>
            {!isSidebarCollapsed && (
              <div className="ml-3 flex-grow overflow-hidden">
                <p className="text-sm font-medium truncate">{user?.email || 'User'}</p>
                <button
                  onClick={handleSignOut}
                  className="text-xs text-gray-400 hover:text-white transition-colors"
                >
                  Sign out
                </button>
              </div>
            )}
            {/* Sign out button for collapsed sidebar */}
            {isSidebarCollapsed && (
              <button
                onClick={handleSignOut}
                className="ml-2 p-1.5 rounded-md text-gray-400 hover:bg-gray-800 hover:text-white"
                title="Sign out"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </button>
            )}
          </div>
        </div>
      </aside>

      {/* Main Content Area without any header */}
      <div className={`flex-1 transition-all duration-300 ${isSidebarCollapsed ? 'lg:ml-20' : 'lg:ml-64'}`}>
        {/* Mobile menu button at the top for mobile only */}
        <div className="flex justify-between items-center p-4 lg:hidden">
          <button
            className="text-gray-400 hover:text-white"
            onClick={toggleMobileSidebar}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>

          <h1 className="text-xl font-semibold">
            {navigationItems.find(item => item.path === pathname)?.name || 'Dashboard'}
          </h1>

          <div className="w-6"></div> {/* Empty div for flex spacing */}
        </div>

        {/* Main content */}
        <main className="p-4 lg:p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
