"use client";

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import PhoneFrame from './PhoneFrame';

export default function ConversationCarousel({ conversationData }) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const phoneFrameRef = useRef(null);
  const conversationRef = useRef(null);
  const touchInfoRef = useRef({
    startX: 0,
    startY: 0,
    startTime: 0,
    isHorizontalSwipe: false
  });

  // Use useCallback to memoize functions to prevent unnecessary rerenders
  const navigate = useCallback((index) => {
    // Prevent navigating to the same index
    if (index === currentIndex) return;
    
    setDirection(index > currentIndex ? 1 : -1);
    setCurrentIndex(index);
  }, [currentIndex]);

  const goNext = useCallback(() => {
    if (currentIndex < conversationData.length - 1) {
      navigate(currentIndex + 1);
    }
  }, [currentIndex, conversationData.length, navigate]);

  const goPrev = useCallback(() => {
    if (currentIndex > 0) {
      navigate(currentIndex - 1);
    }
  }, [currentIndex, navigate]);

  // Add keyboard and swipe navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'ArrowLeft') {
        goPrev();
      } else if (e.key === 'ArrowRight') {
        goNext();
      }
    };

    // Add event listeners
    window.addEventListener('keydown', handleKeyDown);
    
    // Clean up on unmount
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentIndex, goNext, goPrev]); // Include goNext and goPrev in dependencies

  // Add safety check for missing data
  if (!conversationData || conversationData.length === 0) {
    return (
      <div className="w-full py-8 text-center bg-background rounded-xl border border-purple-800/20">
        <p className="text-gray-400">Conversation examples are currently unavailable</p>
      </div>
    );
  }

  // Define slide transition variants only for the content
  const slideVariants = {
    enter: (direction) => {
      return {
        x: direction > 0 ? 300 : -300,
        opacity: 0,
      };
    },
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1,
    },
    exit: (direction) => {
      return {
        zIndex: 0,
        x: direction < 0 ? 300 : -300,
        opacity: 0,
      };
    },
  };

  // Function to check if we should allow scroll propagation
  const shouldAllowScrollPropagation = (element, direction) => {
    if (!element) return true;
    
    if (direction === 'up') {
      return element.scrollTop <= 0;
    } else if (direction === 'down') {
      return element.scrollTop + element.clientHeight >= element.scrollHeight - 1;
    }
    
    return false;
  };

  // Handle wheel event with propagation control
  const handleWheel = (e) => {
    // Handle horizontal scrolling for navigation
    if (Math.abs(e.deltaX) > Math.abs(e.deltaY) * 3) {
      e.preventDefault();
      if (e.deltaX > 50 && currentIndex < conversationData.length - 1) {
        goNext();
      } else if (e.deltaX < -50 && currentIndex > 0) {
        goPrev();
      }
      return;
    }
    
    // For vertical scrolling, we always let the event propagate naturally
    // We no longer need to check boundaries or prevent default
    // This ensures seamless scrolling between the conversation and the page
  };

  // Handle touch start
  const handleTouchStart = (e) => {
    const touch = e.touches[0];
    
    touchInfoRef.current = {
      startX: touch.clientX,
      startY: touch.clientY,
      startTime: Date.now(),
      isHorizontalSwipe: false
    };
  };

  // Handle touch move
  const handleTouchMove = (e) => {
    if (!touchInfoRef.current.startX) return;
    
    const touch = e.touches[0];
    const deltaX = touch.clientX - touchInfoRef.current.startX;
    const deltaY = touch.clientY - touchInfoRef.current.startY;
    const container = conversationRef.current;
    
    // Only interfere with the touch event if it's clearly a horizontal swipe
    // for changing conversations
    if (Math.abs(deltaX) > Math.abs(deltaY) * 2.5 && Math.abs(deltaX) > 40) {
      touchInfoRef.current.isHorizontalSwipe = true;
      // Only prevent default for horizontal swipes to change conversations
      e.preventDefault();
    }
    
    // For all other touch moves (vertical scrolling), we let them propagate naturally
    // This ensures the scroll events can bubble up to the parent container
  };

  // Handle touch end
  const handleTouchEnd = (e) => {
    if (!touchInfoRef.current.startX) return;
    
    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchInfoRef.current.startX;
    const deltaY = touch.clientY - touchInfoRef.current.startY;
    const timeElapsed = Date.now() - touchInfoRef.current.startTime;
    
    // Only process as a swipe if it was clearly a horizontal gesture
    if (touchInfoRef.current.isHorizontalSwipe && 
        timeElapsed < 300 && 
        Math.abs(deltaX) > 80) {
        
      if (deltaX > 0 && currentIndex > 0) {
        setDirection(-1);
        setCurrentIndex(currentIndex - 1);
      } else if (deltaX < 0 && currentIndex < conversationData.length - 1) {
        setDirection(1);
        setCurrentIndex(currentIndex + 1);
      }
    }
    
    // Reset touch info
    touchInfoRef.current = {
      startX: 0,
      startY: 0,
      startTime: 0,
      isHorizontalSwipe: false
    };
  };

  return (
    <div className="w-full max-w-[1200px] mx-auto relative bg-background border border-purple-800/20 px-4 py-12 md:px-8 lg:py-14 rounded-2xl shadow-neon-purple">
      {/* Title section */}
      <div className="text-center mb-10 max-w-2xl mx-auto">
        <h2 className="text-3xl font-bold mb-4 laser-gradient-text" data-text="Experience Automated SMS Responses">
          Experience Automated SMS Responses
        </h2>
        <p className="text-lg text-gray-300 subheading-text">
          Provide instant assistance to missed calls with intelligent text messages that feel natural and personalized.
        </p>
      </div>
      
      <div className="relative overflow-visible">
        <div className="flex md:flex-row flex-col items-center justify-center">
          {/* Navigation Arrow - Previous (desktop only) */}
          <button
            onClick={goPrev}
            className={`hidden w-12 h-12 flex-none bg-gray-800/30 backdrop-blur-sm rounded-full shadow-md justify-center items-center border border-purple-500/30 hover:border-purple-500/50 hover:bg-gray-800/50 transition-colors md:mr-8 ${currentIndex === 0 ? 'opacity-50 cursor-not-allowed' : 'opacity-90 cursor-pointer'}`}
            aria-label="Previous scenario"
            disabled={currentIndex === 0}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          {/* Fixed Phone Frame - NOT DRAGGABLE */}
          <div className="w-full max-w-md relative" ref={phoneFrameRef}>
            <div className="relative mx-auto">
              {/* Static Phone frame */}
              <div className="relative w-[320px] h-[650px] bg-black rounded-[55px] p-3 shadow-xl mx-auto border-[14px] border-black overflow-hidden">
                {/* Notch */}
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[130px] h-[30px] bg-black rounded-b-2xl z-30"></div>
                
                {/* Screen with fixed elements */}
                <div className="w-full h-full bg-white rounded-[40px] overflow-hidden relative">
                  {/* Status bar */}
                  <div className="h-7 w-full bg-white flex justify-between items-center px-6 text-xs text-black font-medium z-20 relative">
                    <div>3:16 PM</div>
                    <div className="flex items-center space-x-1.5">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                      </svg>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M17.778 8.222c-4.296-4.296-11.26-4.296-15.556 0A1 1 0 01.808 6.808c5.076-5.077 13.308-5.077 18.384 0a1 1 0 01-1.414 1.414zM14.95 11.05a7 7 0 00-9.9 0 1 1 0 01-1.414-1.414 9 9 0 0112.728 0 1 1 0 01-1.414 1.414zM12.12 13.88a3 3 0 00-4.242 0 1 1 0 01-1.415-1.415 5 5 0 017.072 0 1 1 0 01-1.415 1.415zM9 16a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                      <div>100%</div>
                    </div>
                  </div>
                  
                  {/* Message header */}
                  <div className="h-14 bg-white border-b border-gray-200 flex justify-between items-center px-4 z-10">
                    <div className="flex items-center">
                      <div className="text-gray-500 mr-1.5">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M15 19l-7-7 7-7" />
                        </svg>
                      </div>
                      <div>
                        <div className="font-semibold text-base">Skyline Properties</div>
                        <div className="text-xs text-green-600">(576)-385-7764 • SMS</div>
                      </div>
                    </div>
                    <div className="flex space-x-5">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </div>
                  </div>
                  
                  {/* Animated Content Area - THIS PART IS SWIPEABLE */}
                  <div className="relative h-[495px] overflow-hidden">
                    {/* Navigation Arrows (positioned inside the phone screen) */}
                    <div className="absolute top-1/2 left-0 right-0 z-50 flex justify-between items-center -translate-y-1/2 pointer-events-none px-1">
                      <button
                        onClick={goPrev}
                        className={`w-5 h-5 flex-none pointer-events-auto bg-black/80 backdrop-blur-xl rounded-full shadow-2xl flex justify-center items-center border border-purple-500/60 hover:border-purple-400 transition-all ${currentIndex === 0 ? 'opacity-60 cursor-not-allowed' : 'opacity-100 cursor-pointer'}`}
                        style={{boxShadow: '0 0 8px rgba(139, 92, 246, 0.5)'}}
                        aria-label="Previous scenario"
                        disabled={currentIndex === 0}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-2.5 w-2.5 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M15 19l-7-7 7-7" />
                        </svg>
                      </button>
                      
                      <button
                        onClick={goNext}
                        className={`w-5 h-5 flex-none pointer-events-auto bg-black/80 backdrop-blur-xl rounded-full shadow-2xl flex justify-center items-center border border-purple-500/60 hover:border-purple-400 transition-all ${currentIndex === conversationData.length - 1 ? 'opacity-60 cursor-not-allowed' : 'opacity-100 cursor-pointer'}`}
                        style={{boxShadow: '0 0 8px rgba(139, 92, 246, 0.5)'}}
                        aria-label="Next scenario"
                        disabled={currentIndex === conversationData.length - 1}
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-2.5 w-2.5 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M9 5l7 7-7 7" />
                        </svg>
                      </button>
                    </div>
                    
                    <AnimatePresence initial={false} custom={direction} mode="wait">
                      <motion.div
                        key={currentIndex}
                        custom={direction}
                        variants={slideVariants}
                        initial="enter"
                        animate="center"
                        exit="exit"
                        transition={{
                          x: { type: "spring", stiffness: 300, damping: 30 },
                          opacity: { duration: 0.2 }
                        }}
                        className="absolute inset-0 w-full h-[481px] overflow-y-auto pb-4 touch-auto"
                        ref={conversationRef}
                        onWheel={handleWheel}
                        onTouchStart={handleTouchStart}
                        onTouchMove={handleTouchMove}
                        onTouchEnd={handleTouchEnd}
                      >
                        <PhoneFrame data={conversationData[currentIndex]} showHeader={false} />
                      </motion.div>
                    </AnimatePresence>
                  </div>
                  
                  {/* Message input */}
                  <div className="h-14 bg-white border-t border-gray-200 flex items-center px-2 absolute bottom-0 w-full z-20">
                    <div className="flex-1 bg-[#f1f1f1] rounded-full border border-gray-300 flex items-center px-2 mx-1 h-9">
                      <span className="text-gray-400 text-sm px-2 truncate">Text Message - SMS</span>
                      <div className="flex-grow"></div>
                    </div>
                    <div className="w-10 h-10 bg-gray-100 flex items-center justify-center rounded-full ml-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="w-10 h-10 bg-green-500 flex items-center justify-center rounded-full ml-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Navigation Arrow - Next (desktop only) */}
          <button
            onClick={goNext}
            className={`hidden w-12 h-12 flex-none bg-gray-800/30 backdrop-blur-sm rounded-full shadow-md justify-center items-center border border-purple-500/30 hover:border-purple-500/50 hover:bg-gray-800/50 transition-colors md:ml-8 ${currentIndex === conversationData.length - 1 ? 'opacity-50 cursor-not-allowed' : 'opacity-90 cursor-pointer'}`}
            aria-label="Next scenario"
            disabled={currentIndex === conversationData.length - 1}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        {/* Pagination dots */}
        <div className="flex justify-center space-x-2 mt-8">
          {conversationData.map((_, index) => (
            <button
              key={index}
              className={`h-2 rounded-full transition-all duration-300 ${
                currentIndex === index ? 'w-6 bg-purple-500' : 'w-2 bg-gray-600 hover:bg-gray-500'
              }`}
              onClick={() => navigate(index)}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>

        {/* Scenario label */}
        <div className="text-center mt-4">
          <span className="inline-block px-4 py-1.5 bg-purple-900/30 text-purple-300 rounded-full text-sm font-medium border border-purple-500/30">
            {conversationData[currentIndex].title || "SMS Scenario"}
          </span>
        </div>
      </div>
      
      {/* Features highlight */}
      <div className="grid md:grid-cols-3 gap-6 mt-12 max-w-4xl mx-auto">
        <div className="p-5 rounded-xl bg-gray-800/30 shadow-sm border border-purple-500/20 hover:border-purple-500/30 transition-colors">
          <div className="w-10 h-10 rounded-full bg-purple-900/50 flex items-center justify-center mb-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-300" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold mb-1 text-white">Instant Responses</h3>
          <p className="text-gray-300">Automatically reply to missed calls with intelligent SMS text messages.</p>
        </div>
        
        <div className="p-5 rounded-xl bg-gray-800/30 shadow-sm border border-purple-500/20 hover:border-purple-500/30 transition-colors">
          <div className="w-10 h-10 rounded-full bg-purple-900/50 flex items-center justify-center mb-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-300" viewBox="0 0 20 20" fill="currentColor">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold mb-1 text-white">Context Aware</h3>
          <p className="text-gray-300">Messages are tailored to your business type and caller needs.</p>
        </div>
        
        <div className="p-5 rounded-xl bg-gray-800/30 shadow-sm border border-purple-500/20 hover:border-purple-500/30 transition-colors">
          <div className="w-10 h-10 rounded-full bg-purple-900/50 flex items-center justify-center mb-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-300" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold mb-1 text-white">Never Miss Out</h3>
          <p className="text-gray-300">Engage potential customers even when you can&apos;t answer the phone.</p>
        </div>
      </div>
      
      {/* Call to action */}
      <div className="text-center mt-12">
        <a 
          href="#pricing" 
          className="primary-cta"
        >
          Get Started Now
        </a>
      </div>
    </div>
  );
} 