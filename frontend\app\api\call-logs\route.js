import { NextResponse } from 'next/server';

// Mock call log data for demo purposes
function getMockCallLogs(phoneNumber) {
  return [
    {
      id: 'call1',
      callSid: 'CA123456789',
      from: phoneNumber,
      to: '+491723773552',
      status: 'completed',
      type: 'outbound',
      body: 'Hi there, this is a test message from CallSaver!',
      duration: 15,
      timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 mins ago
    },
    {
      id: 'call2',
      callSid: 'CA987654321',
      from: phoneNumber,
      to: '+491723773552',
      status: 'failed',
      type: 'outbound',
      body: 'This is another test message that failed.',
      errorMessage: 'Number unreachable',
      timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2 hours ago
    },
    {
      id: 'call3',
      callSid: 'CA543216789',
      from: '+491723773552',
      to: phoneNumber,
      status: 'completed',
      type: 'inbound',
      duration: 45,
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(), // 5 hours ago
    }
  ];
}

// Mock message log data for demo purposes
function getMockMessageLogs(phoneNumber) {
  return [
    {
      id: 'msg1',
      messageSid: 'SM123456789',
      from: phoneNumber,
      to: '+491723773552',
      body: 'Your appointment is confirmed for tomorrow at 2 PM.',
      direction: 'outbound',
      status: 'delivered',
      timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 mins ago
    },
    {
      id: 'msg2',
      messageSid: 'SM987654321',
      from: '+491723773552',
      to: phoneNumber,
      body: 'Thanks for the confirmation. See you tomorrow!',
      direction: 'inbound',
      status: 'received',
      timestamp: new Date(Date.now() - 1000 * 60 * 10).toISOString(), // 10 mins ago
    },
    {
      id: 'msg3',
      messageSid: 'SM543216789',
      from: phoneNumber,
      to: '+491723773552',
      body: 'Please bring your ID and insurance card.',
      direction: 'outbound',
      status: 'delivered',
      timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(), // 5 mins ago
    }
  ];
}

export async function GET(request) {
  try {
    // Get phone number from query params
    const { searchParams } = new URL(request.url);
    const phoneNumber = searchParams.get('phoneNumber');
    
    if (!phoneNumber) {
      return NextResponse.json(
        { success: false, message: 'Phone number parameter is required' },
        { status: 400 }
      );
    }
    
    // In production, this would query the database for real call logs
    // Here we just return mock data for demo purposes
    const calls = getMockCallLogs(phoneNumber);
    const messages = getMockMessageLogs(phoneNumber);
    
    return NextResponse.json({
      success: true,
      calls,
      messages
    });
  } catch (error) {
    console.error('Error retrieving call logs:', error);
    return NextResponse.json(
      { success: false, message: `Failed to retrieve call logs: ${error.message}` },
      { status: 500 }
    );
  }
} 