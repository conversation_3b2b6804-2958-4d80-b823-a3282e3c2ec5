"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { 
  PhoneCall, 
  Play, 
  Download, 
  MessageSquare,
  Clock,
  Search,
  Filter,
  RefreshCw,
  Mic,
  CheckCircle,
  XCircle,
  AlertCircle,
  ChevronDown,
  Heart,
  ThumbsUp,
  ThumbsDown
} from "lucide-react";
import { format, formatDistanceToNow, parseISO } from "date-fns";
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  <PERSON>,
  <PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";

// Type definitions for our call logs
interface CallLog {
  id: string;
  callSid: string;
  from: string;
  to: string;
  direction: string;
  status: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  transcriptionText?: string;
  transcriptionStatus?: string;
  callSummary?: string;
  sentiment?: string;
  hasRecording: boolean;
  recordingUrl?: string;
  userId: string;
  phoneNumberId: string;
  createdAt: string;
  updatedAt: string;
}

// Enum for call status
enum CallStatus {
  COMPLETED = "completed",
  NO_ANSWER = "no-answer",
  BUSY = "busy",
  FAILED = "failed",
  CANCELED = "canceled",
  IN_PROGRESS = "in-progress",
  QUEUED = "queued",
  RINGING = "ringing"
}

// Sentiment badge component
const SentimentBadge = ({ sentiment }: { sentiment?: string }) => {
  if (!sentiment) return null;
  
  const sentimentMap: { [key: string]: { icon: React.ReactNode, color: string } } = {
    positive: { icon: <ThumbsUp className="h-3 w-3 mr-1" />, color: "bg-green-100 text-green-800" },
    negative: { icon: <ThumbsDown className="h-3 w-3 mr-1" />, color: "bg-red-100 text-red-800" },
    neutral: { icon: <Heart className="h-3 w-3 mr-1" />, color: "bg-blue-100 text-blue-800" },
  };

  const { icon, color } = sentimentMap[sentiment.toLowerCase()] || 
    { icon: <AlertCircle className="h-3 w-3 mr-1" />, color: "bg-gray-100 text-gray-800" };

  return (
    <Badge variant="outline" className={`flex items-center text-xs ${color}`}>
      {icon} {sentiment.charAt(0).toUpperCase() + sentiment.slice(1)}
    </Badge>
  );
};

// Call Status Badge component
const CallStatusBadge = ({ status }: { status: string }) => {
  const statusConfig: { [key: string]: { icon: React.ReactNode, color: string } } = {
    [CallStatus.COMPLETED]: { icon: <CheckCircle className="h-3 w-3 mr-1" />, color: "bg-green-100 text-green-800" },
    [CallStatus.NO_ANSWER]: { icon: <XCircle className="h-3 w-3 mr-1" />, color: "bg-amber-100 text-amber-800" },
    [CallStatus.BUSY]: { icon: <Clock className="h-3 w-3 mr-1" />, color: "bg-amber-100 text-amber-800" },
    [CallStatus.FAILED]: { icon: <XCircle className="h-3 w-3 mr-1" />, color: "bg-red-100 text-red-800" },
    [CallStatus.CANCELED]: { icon: <XCircle className="h-3 w-3 mr-1" />, color: "bg-gray-100 text-gray-800" },
    [CallStatus.IN_PROGRESS]: { icon: <PhoneCall className="h-3 w-3 mr-1" />, color: "bg-blue-100 text-blue-800" },
    [CallStatus.QUEUED]: { icon: <Clock className="h-3 w-3 mr-1" />, color: "bg-purple-100 text-purple-800" },
    [CallStatus.RINGING]: { icon: <PhoneCall className="h-3 w-3 mr-1" />, color: "bg-yellow-100 text-yellow-800" },
  };

  const { icon, color } = statusConfig[status] || 
    { icon: <AlertCircle className="h-3 w-3 mr-1" />, color: "bg-gray-100 text-gray-800" };

  return (
    <Badge variant="outline" className={`flex items-center text-xs ${color}`}>
      {icon} {status.charAt(0).toUpperCase() + status.slice(1).replace("-", " ")}
    </Badge>
  );
};

// Audio Player component for recordings
const AudioPlayer = ({ url }: { url: string }) => {
  return (
    <div className="flex items-center space-x-2">
      <audio className="w-full" controls src={url}>
        Your browser does not support the audio element.
      </audio>
      <Button size="sm" variant="outline" onClick={() => window.open(url, '_blank')}>
        <Download className="h-4 w-4" />
      </Button>
    </div>
  );
};

// Call Detail Dialog
const CallDetailDialog = ({ 
  isOpen, 
  onClose, 
  call 
}: { 
  isOpen: boolean, 
  onClose: () => void, 
  call: CallLog | null 
}) => {
  if (!call) return null;

  const formatDuration = (seconds?: number) => {
    if (!seconds) return "N/A";
    const min = Math.floor(seconds / 60);
    const sec = seconds % 60;
    return `${min}:${sec.toString().padStart(2, '0')}`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <PhoneCall className="h-5 w-5 mr-2" />
            Call Details
          </DialogTitle>
          <DialogDescription>
            {format(parseISO(call.startTime), "PPpp")}
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Call Info</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="font-medium">From:</span>
                <span>{call.from}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">To:</span>
                <span>{call.to}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Direction:</span>
                <span className="capitalize">{call.direction}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Status:</span>
                <CallStatusBadge status={call.status} />
              </div>
              <div className="flex justify-between">
                <span className="font-medium">Duration:</span>
                <span>{formatDuration(call.duration)}</span>
              </div>
            </CardContent>
          </Card>

          <Card className="md:col-span-2">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Recording & Transcription</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {call.hasRecording && call.recordingUrl ? (
                <AudioPlayer url={call.recordingUrl} />
              ) : (
                <p className="text-sm text-gray-500 italic">No recording available</p>
              )}
              
              {call.transcriptionStatus === "completed" && call.transcriptionText ? (
                <div className="mt-4">
                  <h4 className="text-sm font-medium mb-2">Transcription</h4>
                  <div className="bg-gray-50 p-3 rounded-md text-sm max-h-40 overflow-y-auto">
                    {call.transcriptionText}
                  </div>
                </div>
              ) : call.transcriptionStatus === "in-progress" ? (
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <RefreshCw className="h-3 w-3 animate-spin" />
                  <span>Transcription in progress...</span>
                </div>
              ) : (
                <p className="text-sm text-gray-500 italic">No transcription available</p>
              )}
            </CardContent>
          </Card>

          {call.callSummary && (
            <Card className="md:col-span-3">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  AI Analysis
                  {call.sentiment && <SentimentBadge sentiment={call.sentiment} className="ml-2" />}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <h4 className="text-sm font-medium mb-2">Call Summary</h4>
                <div className="bg-gray-50 p-3 rounded-md text-sm">
                  {call.callSummary}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Main Call Logs Page Component
export default function CallLogsPage() {
  const [callLogs, setCallLogs] = useState<CallLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCall, setSelectedCall] = useState<CallLog | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [filter, setFilter] = useState("all");
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  
  useEffect(() => {
    // Fetch call logs from the API
    const fetchCallLogs = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/call-logs?page=${page}&filter=${filter}&search=${searchTerm}`);
        
        if (!response.ok) {
          throw new Error("Failed to fetch call logs");
        }
        
        const data = await response.json();
        setCallLogs(data.callLogs);
        setTotalPages(data.totalPages || 1);
      } catch (error) {
        console.error("Error fetching call logs:", error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchCallLogs();
  }, [page, filter, searchTerm]);
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Search is handled by the useEffect dependency
  };

  const openCallDetail = (call: CallLog) => {
    setSelectedCall(call);
    setDialogOpen(true);
  };

  const closeCallDetail = () => {
    setDialogOpen(false);
  };

  const formatPhoneNumber = (phoneNumber: string) => {
    if (!phoneNumber) return "";
    
    // Handle E.164 format (e.g., +12125551234)
    if (phoneNumber.startsWith('+')) {
      const countryCode = phoneNumber.slice(1, phoneNumber.length - 10);
      const areaCode = phoneNumber.slice(-10, -7);
      const firstPart = phoneNumber.slice(-7, -4);
      const lastPart = phoneNumber.slice(-4);
      return `+${countryCode} (${areaCode}) ${firstPart}-${lastPart}`;
    }
    
    // Handle 10-digit format
    if (phoneNumber.length === 10) {
      return `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
    }
    
    return phoneNumber;
  };

  return (
    <motion.div 
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="container px-4 py-6 max-w-7xl mx-auto"
    >
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Call Logs</h1>
          <p className="text-gray-500 mt-1">View and manage your call history</p>
        </div>
        
        <div className="mt-4 md:mt-0 flex-shrink-0">
          <Button onClick={() => window.location.reload()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <form onSubmit={handleSearch} className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input 
              placeholder="Search by phone number or transcript content..." 
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </form>
        
        <div className="flex items-center space-x-2">
          <Select value={filter} onValueChange={setFilter}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Calls</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="missed">Missed/No Answer</SelectItem>
              <SelectItem value="inbound">Inbound Only</SelectItem>
              <SelectItem value="outbound">Outbound Only</SelectItem>
              <SelectItem value="with-recording">With Recording</SelectItem>
              <SelectItem value="with-transcription">With Transcription</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <Card>
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="grid grid-cols-3 w-full max-w-md mx-auto">
            <TabsTrigger value="all">All Calls</TabsTrigger>
            <TabsTrigger value="inbound">Inbound</TabsTrigger>
            <TabsTrigger value="outbound">Outbound</TabsTrigger>
          </TabsList>
          
          <TabsContent value="all" className="mt-4">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Direction</TableHead>
                    <TableHead>From</TableHead>
                    <TableHead>To</TableHead>
                    <TableHead>Date & Time</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Recording</TableHead>
                    <TableHead>AI Analysis</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    // Loading state skeleton
                    Array(5).fill(0).map((_, i) => (
                      <TableRow key={`skeleton-${i}`}>
                        {Array(8).fill(0).map((_, j) => (
                          <TableCell key={`cell-${i}-${j}`}>
                            <Skeleton className="h-5 w-full" />
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : callLogs.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-10 text-gray-500">
                        No call logs found. Start making calls to see your history.
                      </TableCell>
                    </TableRow>
                  ) : (
                    callLogs.map((call) => (
                      <TableRow 
                        key={call.id} 
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => openCallDetail(call)}
                      >
                        <TableCell>
                          {call.direction === "inbound" ? (
                            <Badge variant="outline" className="bg-green-50 text-green-800 border-green-200">
                              Inbound
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="bg-blue-50 text-blue-800 border-blue-200">
                              Outbound
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="font-medium">{formatPhoneNumber(call.from)}</TableCell>
                        <TableCell>{formatPhoneNumber(call.to)}</TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="text-xs text-gray-500">{format(parseISO(call.startTime), "MMM d, yyyy")}</span>
                            <span>{format(parseISO(call.startTime), "h:mm a")}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {call.duration ? `${Math.floor(call.duration / 60)}:${(call.duration % 60).toString().padStart(2, '0')}` : "N/A"}
                        </TableCell>
                        <TableCell>
                          <CallStatusBadge status={call.status} />
                        </TableCell>
                        <TableCell>
                          {call.hasRecording ? (
                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                              <Play className="h-4 w-4" />
                            </Button>
                          ) : (
                            <span className="text-gray-400 text-xs">None</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            {call.transcriptionStatus === "completed" && (
                              <Badge variant="outline" className="bg-purple-50 text-purple-800 border-purple-200">
                                <Mic className="h-3 w-3 mr-1" />
                                Transcript
                              </Badge>
                            )}
                            {call.sentiment && <SentimentBadge sentiment={call.sentiment} />}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
            
            <div className="mt-4">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious 
                      onClick={() => setPage(Math.max(1, page - 1))}
                      className={page <= 1 ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNumber = page <= 3 
                      ? i + 1 
                      : page >= totalPages - 2 
                        ? totalPages - 4 + i 
                        : page - 2 + i;
                    
                    if (pageNumber <= 0 || pageNumber > totalPages) return null;
                    
                    return (
                      <PaginationItem key={pageNumber}>
                        <PaginationLink 
                          isActive={page === pageNumber}
                          onClick={() => setPage(pageNumber)}
                        >
                          {pageNumber}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}
                  <PaginationItem>
                    <PaginationNext 
                      onClick={() => setPage(Math.min(totalPages, page + 1))}
                      className={page >= totalPages ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          </TabsContent>
          
          <TabsContent value="inbound" className="mt-4">
            {/* Inbound-specific content would go here if needed */}
          </TabsContent>
          
          <TabsContent value="outbound" className="mt-4">
            {/* Outbound-specific content would go here if needed */}
          </TabsContent>
        </Tabs>
      </Card>
      
      {/* Call Detail Dialog */}
      <CallDetailDialog 
        isOpen={dialogOpen} 
        onClose={closeCallDetail} 
        call={selectedCall} 
      />
    </motion.div>
  );
}
