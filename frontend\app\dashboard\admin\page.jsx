'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '../../../components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card';
import AdminPageLayout from '../../../components/admin/AdminPageLayout';

export default function AdminDashboard() {
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    totalPhoneNumbers: 0,
    totalCalls: 0,
    totalMessages: 0,
    totalRevenue: 0
  });

  useEffect(() => {
    const fetchAdminStats = async () => {
      try {
        // In a real implementation, this would be an API call to get admin stats
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 500));

        // Mock data
        setStats({
          totalUsers: 1250,
          activeUsers: 876,
          totalPhoneNumbers: 1893,
          totalCalls: 45678,
          totalMessages: 123456,
          totalRevenue: 98765
        });
      } catch (err) {
        console.error('Error fetching admin stats:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAdminStats();
  }, []);

  return (
    <AdminPageLayout
      title="Admin Dashboard"
      description="Welcome to the admin dashboard. You have access to all administrative features."
    >

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white">Total Users</CardTitle>
            <CardDescription className="text-gray-400">All registered users</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-white">{isLoading ? '...' : stats.totalUsers}</p>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white">Active Users</CardTitle>
            <CardDescription className="text-gray-400">Users active in last 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-white">{isLoading ? '...' : stats.activeUsers}</p>
          </CardContent>
        </Card>

        <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white">Total Revenue</CardTitle>
            <CardDescription className="text-gray-400">Lifetime revenue</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold text-white">${isLoading ? '...' : stats.totalRevenue.toLocaleString()}</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="bg-gray-800/70 border border-purple-500/20 mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="numbers">Phone Numbers</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="logs">System Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">System Overview</h2>
          <p className="text-gray-300 mb-6">Welcome to the admin dashboard. From here you can manage all aspects of the CallSaver platform.</p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Total Phone Numbers</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : stats.totalPhoneNumbers}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Total Calls</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : stats.totalCalls.toLocaleString()}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Total Messages</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : stats.totalMessages.toLocaleString()}</p>
              </CardContent>
            </Card>
          </div>

          <div className="mt-8">
            <h3 className="text-lg font-semibold text-white mb-4">Admin Sections</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <a href="/dashboard/admin/users" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
                User Management
              </a>
              <a href="/dashboard/admin/security" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
                Security Settings
              </a>
              <a href="/dashboard/admin/tenants" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
                Tenant Management
              </a>
              <a href="/dashboard/admin/billing" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
                Billing & Subscriptions
              </a>
              <a href="/dashboard/admin/analytics" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
                Advanced Analytics
              </a>
              <a href="/dashboard/admin/system" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
                System Configuration
              </a>
              <a href="/dashboard/admin/logs" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
                System Logs
              </a>
              <a href="/dashboard/admin/api" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
                API Management
              </a>
              <a href="/dashboard/admin/notifications" className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg text-center transition-colors">
                Notification Settings
              </a>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="users" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">User Management</h2>
          <p className="text-gray-300 mb-4">For detailed user management, please visit the <a href="/dashboard/admin/users" className="text-purple-400 hover:text-purple-300">User Management</a> page.</p>
        </TabsContent>

        <TabsContent value="numbers" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">Phone Number Management</h2>
          <p className="text-gray-300">This section allows you to manage all phone numbers in the system.</p>
        </TabsContent>

        <TabsContent value="billing" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">Billing Management</h2>
          <p className="text-gray-300">Manage subscription plans, pricing, and view revenue reports.</p>
        </TabsContent>

        <TabsContent value="logs" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">System Logs</h2>
          <p className="text-gray-300">View system logs and activity for troubleshooting and auditing.</p>
        </TabsContent>
      </Tabs>
    </AdminPageLayout>
  );
}
