'use client';

import { useState, useEffect } from 'react';
import sql from '../utils/db';

export default function PostgresExample() {
  const [loading, setLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState('Checking...');
  const [error, setError] = useState(null);

  useEffect(() => {
    async function testConnection() {
      try {
        // Simple query to test the connection - just get the current time from PostgreSQL
        const result = await sql`SELECT NOW()`;
        if (result && result.length > 0) {
          setConnectionStatus('Connected to PostgreSQL! Server time: ' + new Date(result[0].now).toLocaleString());
        } else {
          setConnectionStatus('Connected, but received unexpected result');
        }
      } catch (err) {
        console.error('Connection error:', err);
        setConnectionStatus('Connection failed');
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    testConnection();

    // Clean up function
    return () => {
      // This is important for connection cleanup
      if (sql.end) {
        sql.end().catch(err => {
          console.error('Error closing connection:', err);
        });
      }
    };
  }, []);

  return (
    <div className="bg-gray-900/70 backdrop-blur-lg rounded-2xl border border-purple-500/20 shadow-xl p-6 md:p-8 w-full max-w-md mx-auto my-8">
      <h2 className="text-xl text-white/80 mb-4">PostgreSQL Direct Connection Status</h2>
      <div className="flex items-center">
        <div className={`h-4 w-4 rounded-full mr-3 ${
          loading ? 'bg-yellow-500' :
          error ? 'bg-red-500' : 'bg-green-500'
        }`}></div>
        <p className="text-white">
          {loading ? 'Testing connection...' : connectionStatus}
        </p>
      </div>
      {error && (
        <div className="mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
          <p className="text-red-200 text-sm">{error}</p>
        </div>
      )}
      <p className="text-gray-400 text-sm mt-4">
        Using Direct Connection to Supabase PostgreSQL database
      </p>
    </div>
  );
} 