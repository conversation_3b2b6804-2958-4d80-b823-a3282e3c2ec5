"use client";

import { useState, useEffect } from 'react';
import { PhoneIcon, MicrophoneIcon, PhoneXMarkIcon, SpeakerWaveIcon, SpeakerXMarkIcon } from '@heroicons/react/24/outline';
import PhoneNumberSelector from './PhoneNumberSelector';

export default function CallInterface({ phoneNumbers = [] }) {
  const [selectedNumber, setSelectedNumber] = useState(phoneNumbers.length > 0 ? phoneNumbers[0] : null);
  const [recipient, setRecipient] = useState('');
  const [isCallActive, setIsCallActive] = useState(false);
  const [callStatus, setCallStatus] = useState('idle'); // idle, connecting, active, ended
  const [callDuration, setCallDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(false);
  const [callTimer, setCallTimer] = useState(null);
  const [error, setError] = useState(null);
  
  // Format phone number input as user types
  const formatPhoneNumber = (input) => {
    // Remove all non-digit characters
    const cleaned = input.replace(/\D/g, '');
    
    // Format based on length
    if (cleaned.length <= 3) {
      return cleaned;
    } else if (cleaned.length <= 6) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3)}`;
    } else {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6, 10)}`;
    }
  };

  // Handle phone number input change
  const handleRecipientChange = (e) => {
    const formattedNumber = formatPhoneNumber(e.target.value);
    setRecipient(formattedNumber);
  };
  
  // Handle number selection
  const handleNumberSelect = (number) => {
    setSelectedNumber(number);
    console.log('Selected number:', number);
  };

  // Format call duration for display
  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Start a call
  const startCall = () => {
    if (!selectedNumber) {
      setError('Please select a phone number to call from');
      return;
    }
    
    if (!recipient || recipient.replace(/\D/g, '').length < 10) {
      setError('Please enter a valid phone number');
      return;
    }
    
    setError(null);
    setCallStatus('connecting');
    
    // Simulate API call to initiate call
    setTimeout(() => {
      setCallStatus('active');
      setIsCallActive(true);
      
      // Start call timer
      const timer = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
      
      setCallTimer(timer);
    }, 2000);
  };
  
  // End a call
  const endCall = () => {
    setCallStatus('ended');
    setIsCallActive(false);
    
    // Clear call timer
    if (callTimer) {
      clearInterval(callTimer);
      setCallTimer(null);
    }
    
    // Reset after a delay
    setTimeout(() => {
      setCallStatus('idle');
      setCallDuration(0);
      setIsMuted(false);
      setIsSpeakerOn(false);
    }, 3000);
  };
  
  // Toggle mute
  const toggleMute = () => {
    setIsMuted(!isMuted);
  };
  
  // Toggle speaker
  const toggleSpeaker = () => {
    setIsSpeakerOn(!isSpeakerOn);
  };
  
  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (callTimer) {
        clearInterval(callTimer);
      }
    };
  }, [callTimer]);

  return (
    <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-white flex items-center">
          <PhoneIcon className="h-5 w-5 mr-2 text-purple-400" />
          Make a Call
        </h3>
        {callStatus === 'active' && (
          <span className="px-2 py-1 bg-green-500/20 text-green-300 rounded-full text-xs flex items-center">
            <span className="h-2 w-2 bg-green-400 rounded-full mr-1 animate-pulse"></span>
            Call in progress
          </span>
        )}
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
          <p className="text-red-300 text-sm">{error}</p>
        </div>
      )}

      <div className="space-y-4">
        {!isCallActive && (
          <>
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">
                Select a Phone Number to Call From
              </label>
              <PhoneNumberSelector 
                numbers={phoneNumbers} 
                onSelect={handleNumberSelect} 
                selectedNumber={selectedNumber} 
              />
            </div>

            <div>
              <label htmlFor="recipient" className="block text-sm font-medium text-gray-400 mb-1">
                Recipient
              </label>
              <input
                type="tel"
                id="recipient"
                value={recipient}
                onChange={handleRecipientChange}
                placeholder="Enter phone number"
                className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
              />
            </div>

            <button 
              onClick={startCall}
              disabled={callStatus === 'connecting'}
              className={`w-full py-2 ${
                callStatus === 'connecting' 
                  ? 'bg-purple-600/50 cursor-wait' 
                  : 'bg-purple-600 hover:bg-purple-700 cursor-pointer'
              } text-white font-medium rounded-lg transition-colors flex items-center justify-center`}
            >
              {callStatus === 'connecting' ? (
                <>
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Connecting...
                </>
              ) : (
                <>
                  <PhoneIcon className="h-5 w-5 mr-2" />
                  Start Call
                </>
              )}
            </button>
          </>
        )}

        {isCallActive && (
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 bg-purple-600/30 rounded-full flex items-center justify-center mb-3">
              <PhoneIcon className="h-8 w-8 text-purple-400" />
            </div>
            
            <p className="text-white font-medium text-lg mb-1">
              {recipient}
            </p>
            
            <p className="text-gray-400 mb-4">
              Call duration: {formatDuration(callDuration)}
            </p>
            
            <div className="grid grid-cols-3 gap-4 w-full mb-4">
              <button 
                onClick={toggleMute}
                className={`p-3 rounded-lg flex flex-col items-center ${
                  isMuted 
                    ? 'bg-red-500/20 text-red-300' 
                    : 'bg-gray-800/50 text-gray-300 hover:bg-gray-800'
                }`}
              >
                <MicrophoneIcon className="h-6 w-6 mb-1" />
                <span className="text-xs">{isMuted ? 'Unmute' : 'Mute'}</span>
              </button>
              
              <button 
                onClick={endCall}
                className="p-3 bg-red-600 hover:bg-red-700 text-white rounded-lg flex flex-col items-center"
              >
                <PhoneXMarkIcon className="h-6 w-6 mb-1" />
                <span className="text-xs">End Call</span>
              </button>
              
              <button 
                onClick={toggleSpeaker}
                className={`p-3 rounded-lg flex flex-col items-center ${
                  isSpeakerOn 
                    ? 'bg-green-500/20 text-green-300' 
                    : 'bg-gray-800/50 text-gray-300 hover:bg-gray-800'
                }`}
              >
                {isSpeakerOn ? (
                  <SpeakerWaveIcon className="h-6 w-6 mb-1" />
                ) : (
                  <SpeakerXMarkIcon className="h-6 w-6 mb-1" />
                )}
                <span className="text-xs">Speaker</span>
              </button>
            </div>
          </div>
        )}
      </div>

      {selectedNumber && !isCallActive && (
        <div className="mt-4 p-3 bg-gray-800/50 rounded-lg border border-gray-700/50">
          <p className="text-sm text-gray-400">
            You will call from: <span className="text-white font-medium">{selectedNumber.friendlyName}</span>
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Your recipient will see this number on their caller ID
          </p>
        </div>
      )}
      
      {callStatus === 'ended' && (
        <div className="mt-4 p-3 bg-gray-800/50 rounded-lg border border-gray-700/50">
          <p className="text-sm text-gray-400 text-center">
            Call ended. Duration: <span className="text-white font-medium">{formatDuration(callDuration)}</span>
          </p>
        </div>
      )}
    </div>
  );
}
