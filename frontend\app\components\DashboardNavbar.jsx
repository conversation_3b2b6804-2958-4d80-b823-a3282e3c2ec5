'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Cog6ToothIcon, UserCircleIcon, Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';
import supabaseClient from '../utils/supabaseClient';
import NotificationCenter from './notifications/NotificationCenter';

export default function DashboardNavbar({ user }) {
  const router = useRouter();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleSignOut = async () => {
    // Check if we're using demo mode
    const demoUser = localStorage.getItem('callsaver_demo_user');
    if (demoUser) {
      console.log('Signing out demo user');
      // Clear the demo user from local storage
      localStorage.removeItem('callsaver_demo_user');
      router.push('/signin');
      return;
    }

    // Otherwise use Supabase signout
    try {
      console.log('Signing out Supabase user');
      const { error } = await supabaseClient.auth.signOut();
      if (error) {
        console.error('Error signing out:', error);
      } else {
        console.log('Sign out successful');
      }

      // Always redirect to sign-in page, even if there was an error
      // This ensures the user can get back to the login screen
      router.push('/signin');
    } catch (err) {
      console.error('Unexpected error during sign out:', err);
      // Still redirect to sign-in page
      router.push('/signin');
    }
  };

  return (
    <header className="bg-gray-900/90 backdrop-blur-sm border-b border-gray-800 shadow-md fixed top-0 left-0 right-0 z-40">
      <div className="container mx-auto px-3 sm:px-4 py-2 sm:py-3 flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/dashboard" className="flex items-center">
            <div className="relative w-10 h-10 mr-2">
              <div className="absolute w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
            </div>
            <div className="flex items-center">
              <h1 className="text-lg sm:text-xl font-bold text-white">CallSaver</h1>
              <span className="ml-2 sm:ml-4 px-2 sm:px-3 py-1 bg-gray-800 rounded-full text-xs text-gray-300">Dashboard</span>
            </div>
          </Link>
        </div>

        <div className="flex items-center space-x-2 sm:space-x-3 md:space-x-6">
          {/* Mobile Menu Button */}
          <button
            className="md:hidden p-1.5 text-gray-300 hover:text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50 rounded-md"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            {mobileMenuOpen ? (
              <XMarkIcon className="h-6 w-6" />
            ) : (
              <Bars3Icon className="h-6 w-6" />
            )}
          </button>

          {/* Desktop Navigation Links */}
          <nav className="hidden md:flex items-center space-x-1">
            <Link
              href="/dashboard"
              className="px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors text-sm"
            >
              Dashboard
            </Link>
            <Link
              href="/dashboard/calls"
              className="px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors text-sm"
            >
              Call Logs
            </Link>
            <Link
              href="/dashboard/analytics"
              className="px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors text-sm"
            >
              Analytics
            </Link>
          </nav>

          {/* User Actions */}
          <div className="flex items-center space-x-2">
            {/* Notification Center */}
            <NotificationCenter user={user} />

            {/* Settings Link */}
            <Link
              href="/settings"
              className="p-2 text-gray-300 hover:text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50 rounded-full"
              aria-label="Settings"
            >
              <Cog6ToothIcon className="h-6 w-6" />
            </Link>

            {/* Sign Out Button */}
            <button
              onClick={handleSignOut}
              className="hidden md:flex items-center px-3 py-1.5 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors text-sm"
              aria-label="Sign Out"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              Sign Out
            </button>

            {/* User Menu */}
            <div className="relative group">
              <button className="flex items-center focus:outline-none focus:ring-2 focus:ring-purple-500/50 rounded-full">
                <div className="w-10 h-10 rounded-full bg-purple-600 flex items-center justify-center">
                  <span className="text-white font-semibold">{(user?.name?.[0] || user?.email?.[0] || 'U').toUpperCase()}</span>
                </div>
              </button>

              <div className="absolute right-0 mt-2 w-48 bg-gray-900 border border-gray-800 rounded-lg shadow-lg z-50 hidden group-hover:block origin-top-right">
                <div className="p-3 border-b border-gray-800">
                  <p className="text-white font-medium truncate">{user?.name || user?.email || 'User'}</p>
                  <p className="text-sm text-gray-400 truncate">{user?.email}</p>
                </div>
                <div className="p-2">
                  <Link
                    href="/settings"
                    className="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-800 rounded-md transition-colors text-sm w-full"
                  >
                    <UserCircleIcon className="h-5 w-5 mr-2" />
                    Profile Settings
                  </Link>
                  <button
                    onClick={handleSignOut}
                    className="flex items-center px-3 py-2 text-gray-300 hover:text-white hover:bg-gray-800 rounded-md transition-colors text-sm w-full"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    Sign Out
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden bg-gray-900/95 backdrop-blur-sm border-b border-gray-800">
          <div className="container mx-auto px-4 py-3">
            <nav className="flex flex-col space-y-2">
              <Link
                href="/dashboard"
                className="px-3 py-2.5 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors text-sm"
                onClick={() => setMobileMenuOpen(false)}
              >
                Dashboard
              </Link>
              <Link
                href="/dashboard/calls"
                className="px-3 py-2.5 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors text-sm"
                onClick={() => setMobileMenuOpen(false)}
              >
                Call Logs
              </Link>
              <Link
                href="/dashboard/analytics"
                className="px-3 py-2.5 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors text-sm"
                onClick={() => setMobileMenuOpen(false)}
              >
                Analytics
              </Link>
              <Link
                href="/settings"
                className="px-3 py-2.5 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors text-sm flex items-center"
                onClick={() => setMobileMenuOpen(false)}
              >
                <UserCircleIcon className="h-5 w-5 mr-2" />
                Profile Settings
              </Link>
              <button
                onClick={() => {
                  setMobileMenuOpen(false);
                  handleSignOut();
                }}
                className="px-3 py-2.5 text-gray-300 hover:text-white hover:bg-gray-800/50 rounded-lg transition-colors text-sm w-full text-left flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Sign Out
              </button>
            </nav>
          </div>
        </div>
      )}
    </header>
  );
}