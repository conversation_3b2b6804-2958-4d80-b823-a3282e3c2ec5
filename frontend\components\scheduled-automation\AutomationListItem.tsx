'use client';

import { ScheduledAutomation } from '../../hooks/useScheduledAutomation';
import { Switch } from '@headlessui/react';
import { 
  PhoneIcon, 
  ChatBubbleLeftRightIcon, 
  CalendarIcon, 
  ClockIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { formatDistanceToNow, format } from 'date-fns';

interface AutomationListItemProps {
  automation: ScheduledAutomation;
  onEdit: () => void;
  onDelete: () => void;
  onToggle: (isEnabled: boolean) => void;
  isToggling: boolean;
  isDeleting: boolean;
}

export default function AutomationListItem({
  automation,
  onEdit,
  onDelete,
  onToggle,
  isToggling,
  isDeleting,
}: AutomationListItemProps) {
  const isEnabled = automation.status === 'enabled';
  
  // Format dates for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, { addSuffix: true });
    } catch (error) {
      return 'Invalid date';
    }
  };
  
  // Format next run time
  const formatNextRun = (dateString?: string) => {
    if (!dateString) return 'Not scheduled';
    
    try {
      const date = new Date(dateString);
      return format(date, 'MMM d, yyyy h:mm a');
    } catch (error) {
      return 'Invalid date';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex flex-col md:flex-row md:items-center justify-between">
        <div className="flex items-start space-x-3">
          {/* Icon based on automation type */}
          <div className={`p-2 rounded-full ${
            automation.type === 'call' 
              ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' 
              : 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400'
          }`}>
            {automation.type === 'call' ? (
              <PhoneIcon className="h-5 w-5" />
            ) : (
              <ChatBubbleLeftRightIcon className="h-5 w-5" />
            )}
          </div>
          
          {/* Automation details */}
          <div>
            <h3 className="font-medium text-gray-900 dark:text-white">{automation.name}</h3>
            {automation.description && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{automation.description}</p>
            )}
            
            {/* Schedule summary */}
            <div className="flex items-center mt-2 text-sm text-gray-500 dark:text-gray-400">
              <CalendarIcon className="h-4 w-4 mr-1" />
              <span>{automation.scheduleSummary}</span>
            </div>
            
            {/* Last/next run info */}
            <div className="flex flex-col sm:flex-row sm:space-x-4 mt-1">
              {automation.lastRun && (
                <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                  <span className="font-medium mr-1">Last run:</span>
                  {formatDate(automation.lastRun)}
                </div>
              )}
              
              {automation.nextRun && (
                <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                  <ClockIcon className="h-3 w-3 mr-1" />
                  <span className="font-medium mr-1">Next run:</span>
                  {formatNextRun(automation.nextRun)}
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Actions */}
        <div className="flex items-center space-x-4 mt-4 md:mt-0">
          {/* Toggle switch */}
          <div className="flex items-center">
            <Switch
              checked={isEnabled}
              onChange={() => onToggle(!isEnabled)}
              disabled={isToggling}
              className={`${
                isEnabled ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'
              } relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
            >
              <span
                className={`${
                  isEnabled ? 'translate-x-6' : 'translate-x-1'
                } inline-block h-4 w-4 transform rounded-full bg-white transition-transform`}
              />
            </Switch>
            <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
              {isEnabled ? 'Enabled' : 'Disabled'}
            </span>
          </div>
          
          {/* Edit button */}
          <button
            onClick={onEdit}
            className="p-1 rounded-full text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <PencilIcon className="h-5 w-5" />
          </button>
          
          {/* Delete button */}
          <button
            onClick={onDelete}
            disabled={isDeleting}
            className="p-1 rounded-full text-gray-400 hover:text-red-500 dark:hover:text-red-400 focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            <TrashIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  );
}
