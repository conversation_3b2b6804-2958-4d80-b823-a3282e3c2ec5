"use client";

import { useEffect, useCallback } from 'react';

function ClientScrollReveal() {
  // Optimized scroll reveal logic, moved from app/page.js
  const handleScrollReveal = useCallback(() => {
    // Check for window/document existence is inherent in useEffect
    const reveals = document.querySelectorAll('.scroll-reveal');
    
    for (let i = 0; i < reveals.length; i++) {
      const windowHeight = window.innerHeight;
      const elementTop = reveals[i].getBoundingClientRect().top;
      const elementVisible = 150; // Adjust as needed
      
      if (elementTop < windowHeight - elementVisible) {
        reveals[i].classList.add('revealed');
      } else {
        // Optional: remove class if element scrolls back up out of view
        // reveals[i].classList.remove('revealed');
      }
    }
  }, []);
  
  useEffect(() => {
    let isScrolling;
    const debouncedScrollHandler = () => {
      window.clearTimeout(isScrolling);
      isScrolling = setTimeout(() => {
        handleScrollReveal();
      }, 15); // Slightly increased debounce time
    };
    
    // Add event listener only on the client
    window.addEventListener('scroll', debouncedScrollHandler, { passive: true });
    
    // Initial check in case elements are already in view
    handleScrollReveal(); 
    
    // Cleanup listener on component unmount
    return () => {
      window.removeEventListener('scroll', debouncedScrollHandler);
      window.clearTimeout(isScrolling);
    };
  }, [handleScrollReveal]); // Dependency array ensures effect runs once on mount

  // This component doesn't render anything itself
  return null;
}

export default ClientScrollReveal; 