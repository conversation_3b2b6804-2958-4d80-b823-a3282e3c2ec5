'use client';

import { useState } from 'react';
import { useSessions, useTerminateSession, Session } from '../../../hooks/useSettings';
import LoadingSpinner from '../shared/LoadingSpinner';
import ErrorMessage from '../shared/ErrorMessage';

interface SessionsManagerProps {
  onSuccess: () => void;
}

export default function SessionsManager({ onSuccess }: SessionsManagerProps) {
  const [sessionToTerminate, setSessionToTerminate] = useState<string | null>(null);
  
  // Fetch sessions
  const { 
    data: sessions, 
    isLoading, 
    isError, 
    error 
  } = useSessions();
  
  // Terminate session mutation
  const terminateSession = useTerminateSession();
  
  // Handle session termination
  const handleTerminateSession = async (sessionId: string) => {
    setSessionToTerminate(sessionId);
    
    try {
      await terminateSession.mutateAsync(sessionId);
      onSuccess();
    } catch (err) {
      // Error is handled by the mutation
      console.error('Error terminating session:', err);
    } finally {
      setSessionToTerminate(null);
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short'
    }).format(date);
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center py-6">
        <LoadingSpinner size="md" />
      </div>
    );
  }
  
  if (isError) {
    return (
      <ErrorMessage 
        title="Failed to load sessions" 
        message="We couldn't load your active sessions. Please try again later."
        error={error instanceof Error ? error : undefined}
        onRetry={() => window.location.reload()}
      />
    );
  }
  
  if (!sessions || sessions.length === 0) {
    return (
      <div className="text-center py-6 text-gray-500 dark:text-gray-400">
        No active sessions found.
      </div>
    );
  }
  
  return (
    <div>
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
        These are your currently active sessions. You can terminate any session except your current one.
      </p>
      
      {terminateSession.isError && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-800 dark:text-red-400 text-sm">
          {terminateSession.error instanceof Error 
            ? terminateSession.error.message 
            : 'Failed to terminate session. Please try again.'}
        </div>
      )}
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Device
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Location
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Last Active
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {sessions.map((session) => (
              <tr key={session.id} className={session.isCurrent ? 'bg-indigo-50 dark:bg-indigo-900/10' : ''}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      {session.browser.toLowerCase().includes('chrome') ? (
                        <svg className="h-5 w-5 text-gray-400" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
                          <path d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z" />
                        </svg>
                      ) : session.browser.toLowerCase().includes('firefox') ? (
                        <svg className="h-5 w-5 text-gray-400" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
                          <path d="M12 6c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6-2.69-6-6-6zm0 10c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4z" />
                        </svg>
                      ) : (
                        <svg className="h-5 w-5 text-gray-400" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
                        </svg>
                      )}
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {session.device}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {session.browser}
                      </div>
                      {session.isCurrent && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 mt-1">
                          Current
                        </span>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-white">
                    {session.location || 'Unknown location'}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {session.ip}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {formatDate(session.lastActive)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  {session.isCurrent ? (
                    <span className="text-gray-400 dark:text-gray-500">
                      Current Session
                    </span>
                  ) : (
                    <button
                      type="button"
                      onClick={() => handleTerminateSession(session.id)}
                      disabled={terminateSession.isPending && sessionToTerminate === session.id}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {terminateSession.isPending && sessionToTerminate === session.id 
                        ? 'Terminating...' 
                        : 'Terminate'}
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
