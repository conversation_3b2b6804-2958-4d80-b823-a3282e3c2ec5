const express = require('express');
const router = express.Router();
const automationController = require('../controllers/automationController');

// Define routes for automation
router.get('/', automationController.getAutomations);
router.post('/', automationController.createAutomation);
router.get('/:id', automationController.getAutomationById);
router.put('/:id', automationController.updateAutomation);
router.delete('/:id', automationController.deleteAutomation);

module.exports = router;