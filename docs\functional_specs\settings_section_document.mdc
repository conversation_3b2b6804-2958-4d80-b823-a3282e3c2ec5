---
description: 
globs: 
alwaysApply: false
---
# Settings Section Functional Document (`settings_section_document.mdc`)

## 1. Purpose and Scope

**Purpose:** Allow users to manage their personal profile information, account security settings, notification preferences, API access keys, and view their subscription/billing details. For Admins, this section may also include organization-level settings.

**Scope:**
- **Profile Management:** Update user name, email (potentially with verification), and profile picture.
- **Security:** Change account password, potentially enable/manage Multi-Factor Authentication (MFA).
- **Notification Preferences:** Configure which events trigger email or in-app notifications (e.g., new voicemail, low credits, AI alerts).
- **API Keys:** Generate, view (partially masked), label, and revoke API keys for programmatic access to the CallSaver API.
- **Billing & Subscription:** Display current plan/subscription status, credit balance, and provide a link to the billing portal (e.g., Stripe customer portal) for managing payment methods and viewing invoices.
- **Organization Settings (Admin Only):** Manage organization name, potentially default settings for new users or numbers.
- **User Management (Admin Only):** This might be a subsection here or its own top-level item (`user_roles_and_permissions.mdc`). If included here, it covers inviting, managing roles, and removing users.

## 2. User Interactions

- **Edit Profile:** Modify name, email, and upload a profile picture via form fields.
- **Change Password:** Enter current password and new password in a dedicated form.
- **Configure Notifications:** Use checkboxes or toggles to enable/disable specific notification types for different channels (email, in-app).
- **Manage API Keys:**
    - Click "Generate New Key".
    - Provide a label for the key.
    - Copy the generated key (shown only once).
    - View a list of existing keys (showing label, prefix, creation date).
    - Click "Revoke" next to a key, followed by confirmation.
- **Access Billing:** Click a button/link to be redirected to the external billing portal.
- **Manage Users (Admin):** Interact with the user list as defined in `user_roles_and_permissions.mdc`.
- **Edit Org Settings (Admin):** Modify organization name or other global settings via forms.

## 3. Backend Integrations & Services Used

- **User Service / Authentication Service:** Handles profile updates, password changes, MFA management, API key generation/validation/revocation.
- **Notification Service:** Stores and manages user notification preferences.
- **Billing Service (e.g., Stripe Integration):** Provides subscription status, credit balance, and generates links to the customer billing portal.
- **Database:** Stores user profile data, notification preferences, hashed API keys (or references), organization settings.

## 4. Necessary API Endpoints

- `GET /api/users/me`: Fetches current user profile information, including role and potentially basic subscription status.
- `PUT /api/users/me`: Updates user profile information (name, email - may trigger verification flow).
- `POST /api/users/me/change-password`: Handles password change requests.
- `GET /api/users/me/notifications`: Fetches current notification preferences.
- `PUT /api/users/me/notifications`: Updates notification preferences.
- `GET /api/api-keys`: Fetches a list of the user's API keys (masked, with labels and metadata).
- `POST /api/api-keys`: Generates a new API key with a given label. Returns the full key *once*.
- `DELETE /api/api-keys/{keyId}`: Revokes an existing API key.
- `GET /api/billing/portal-url`: Generates a session URL to redirect the user to the Stripe (or other provider) customer portal.
- `GET /api/organization`: (Admin only) Fetches organization settings.
- `PUT /api/organization`: (Admin only) Updates organization settings.
*Note: User management endpoints are listed in `user_roles_and_permissions.mdc`.*

## 5. Expected Frontend Component Structure

```
/components
  /settings
    SettingsLayout.tsx            # Main layout, potentially with tabs/sections
    ProfileSettingsForm.tsx       # Form for editing name, email, picture
    PasswordChangeForm.tsx        # Form for changing password
    NotificationPreferences.tsx   # Component with toggles/checkboxes for notifications
    ApiKeyManager.tsx             # Section to display and manage API keys
      ApiKeyList.tsx              # Table/List of existing keys
      GenerateApiKeyForm.tsx      # Button/Modal to generate a new key
      RevokeApiKeyDialog.tsx      # Confirmation dialog for revoking
    BillingInfoPanel.tsx          # Displays subscription status and link to portal
    OrganizationSettingsForm.tsx  # (Admin only) Form for org settings
    SettingsSkeleton.tsx          # Loading state placeholder
    # UserManagement components might be nested here if not top-level
```

## 6. Data Displayed

- **Profile:** User's name, email address, profile picture.
- **Security:** Password change fields. MFA status/setup options (if implemented).
- **Notifications:** List of notification types with toggles for enabled channels (e.g., "New Voicemail" -> Email [on/off], In-App [on/off]).
- **API Keys:** List of keys showing Label, Key Prefix (e.g., `sk_live_...abcd`), Creation Date, Revoke button. The full key is shown only immediately after generation.
- **Billing:** Current Subscription Plan Name, Status (Active, Canceled, Trial), Credit Balance, Button/Link to "Manage Billing".
- **Organization (Admin):** Organization Name field.

## 7. State and UI Behavior

- **Loading States:** Show skeletons while fetching settings data (profile, notifications, API keys).
- **Form Validation:** Validate inputs for profile (email format), password change (complexity rules, matching fields).
- **Feedback:** Provide clear success/error toast notifications for all update actions (profile save, password change, notification save, API key generation/revocation).
- **API Key Handling:**
    - Display the full API key clearly *only once* upon generation, with a prominent "Copy" button. Emphasize that it won't be shown again.
    - Mask keys in the list view (show only prefix/suffix).
    - Require confirmation before revoking a key.
- **Billing Redirect:** The "Manage Billing" button should fetch the portal URL from the API and then perform a browser redirect.
- **Role-Based Visibility:** Sections like Organization Settings and User Management should only be visible to users with the Admin role.

## 8. AI Integration

- **Minimal Direct Integration:** Settings are primarily user/account configuration.
- **Future Potential:**
    - AI could analyze usage and suggest optimizing notification preferences (e.g., "You receive many low-priority alerts via email, consider disabling them?").
    - AI could potentially detect anomalous API key usage patterns (requires backend monitoring).

## 9. Error Handling Rules

- **Profile Update Errors:** Show specific errors (e.g., "Invalid email format", "Email already in use", "Failed to save profile").
- **Password Change Errors:** Show errors like "Incorrect current password", "New passwords do not match", "Password does not meet complexity requirements".
- **Notification Save Errors:** Generic "Failed to save notification preferences".
- **API Key Errors:**
    - Generation: "Failed to generate API key."
    - Revocation: "Failed to revoke API key."
- **Billing Portal Errors:** "Failed to retrieve billing portal link. Please try again later."
- **Permission Denied:** Gracefully handle attempts by non-admins to access admin-only settings sections (UI should prevent this, but API must enforce).
- **Authentication Errors:** Redirect to login if the session is invalid.

## 10. Logging and Usage Tracking Expectations

- **Log:**
    - Successful profile updates (note which fields changed).
    - Password change attempts (success/failure).
    - Notification preference updates.
    - API key generation events (user, key label).
    - API key revocation events (user, keyId).
    - Access attempts to the billing portal link.
    - Organization setting updates (admin user, changed fields).
    - Errors encountered during any settings modification.
- **Track:**
    - Views of the settings section and its subsections (Profile, Security, Notifications, API Keys, Billing, Org Settings).
    - Interactions with forms (saves attempted/succeeded).
    - API key generation and revocation actions.
    - Clicks on the "Manage Billing" link.
