'use client';

import React, { useState, useEffect } from 'react';
import { Bar, Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const PerformanceDashboard = () => {
  const [metrics, setMetrics] = useState([]);
  const [activeTab, setActiveTab] = useState('webVitals');
  
  // Load metrics from localStorage on component mount
  useEffect(() => {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        const storedMetrics = JSON.parse(localStorage.getItem('performance_metrics') || '[]');
        setMetrics(storedMetrics);
      } catch (e) {
        console.error('Error loading metrics from localStorage:', e);
      }
    }
  }, []);
  
  // Group metrics by name
  const groupedMetrics = metrics.reduce((acc, metric) => {
    if (!acc[metric.name]) {
      acc[metric.name] = [];
    }
    acc[metric.name].push(metric);
    return acc;
  }, {});
  
  // Filter Web Vitals metrics
  const webVitalsMetrics = ['LCP', 'FID', 'CLS', 'TTFB', 'INP'].reduce((acc, name) => {
    if (groupedMetrics[name]) {
      acc[name] = groupedMetrics[name];
    }
    return acc;
  }, {});
  
  // Filter component render metrics
  const componentRenderMetrics = Object.keys(groupedMetrics)
    .filter(name => name.startsWith('component-render-'))
    .reduce((acc, name) => {
      acc[name.replace('component-render-', '')] = groupedMetrics[name];
      return acc;
    }, {});
  
  // Filter resource metrics
  const resourceMetrics = Object.keys(groupedMetrics)
    .filter(name => name.startsWith('resource-'))
    .reduce((acc, name) => {
      acc[name] = groupedMetrics[name];
      return acc;
    }, {});
  
  // Filter navigation metrics
  const navigationMetrics = ['dns-time', 'tcp-time', 'ttfb', 'dom-content-loaded', 'dom-complete', 'load-time'].reduce((acc, name) => {
    if (groupedMetrics[name]) {
      acc[name] = groupedMetrics[name];
    }
    return acc;
  }, {});
  
  // Prepare chart data for Web Vitals
  const webVitalsChartData = {
    labels: Object.keys(webVitalsMetrics),
    datasets: [
      {
        label: 'Latest Value',
        data: Object.entries(webVitalsMetrics).map(([name, metrics]) => {
          const latestMetric = metrics[metrics.length - 1];
          return latestMetric.value;
        }),
        backgroundColor: 'rgba(75, 192, 192, 0.6)',
      },
    ],
  };
  
  // Prepare chart data for component renders
  const componentRenderChartData = {
    labels: Object.keys(componentRenderMetrics),
    datasets: [
      {
        label: 'Average Render Time (ms)',
        data: Object.entries(componentRenderMetrics).map(([name, metrics]) => {
          const sum = metrics.reduce((acc, metric) => acc + metric.value, 0);
          return sum / metrics.length;
        }),
        backgroundColor: 'rgba(153, 102, 255, 0.6)',
      },
    ],
  };
  
  // Prepare chart data for navigation metrics
  const navigationChartData = {
    labels: Object.keys(navigationMetrics),
    datasets: [
      {
        label: 'Latest Value (ms)',
        data: Object.entries(navigationMetrics).map(([name, metrics]) => {
          const latestMetric = metrics[metrics.length - 1];
          return latestMetric.value;
        }),
        backgroundColor: 'rgba(255, 159, 64, 0.6)',
      },
    ],
  };
  
  // Prepare time series data for a specific metric
  const getTimeSeriesData = (metricName) => {
    if (!groupedMetrics[metricName]) {
      return null;
    }
    
    const metrics = groupedMetrics[metricName];
    
    return {
      labels: metrics.map((metric, index) => index),
      datasets: [
        {
          label: metricName,
          data: metrics.map(metric => metric.value),
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.5)',
        },
      ],
    };
  };
  
  // Chart options
  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Performance Metrics',
      },
    },
  };
  
  return (
    <div className="p-4 bg-white rounded-lg shadow">
      <h2 className="text-xl font-bold mb-4">Performance Dashboard</h2>
      
      {/* Tabs */}
      <div className="flex mb-4 border-b">
        <button
          className={`px-4 py-2 ${activeTab === 'webVitals' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-500'}`}
          onClick={() => setActiveTab('webVitals')}
        >
          Web Vitals
        </button>
        <button
          className={`px-4 py-2 ${activeTab === 'components' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-500'}`}
          onClick={() => setActiveTab('components')}
        >
          Component Renders
        </button>
        <button
          className={`px-4 py-2 ${activeTab === 'navigation' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-500'}`}
          onClick={() => setActiveTab('navigation')}
        >
          Navigation
        </button>
        <button
          className={`px-4 py-2 ${activeTab === 'timeSeries' ? 'border-b-2 border-blue-500 text-blue-500' : 'text-gray-500'}`}
          onClick={() => setActiveTab('timeSeries')}
        >
          Time Series
        </button>
      </div>
      
      {/* Tab content */}
      <div className="mt-4">
        {activeTab === 'webVitals' && (
          <div>
            <h3 className="text-lg font-semibold mb-2">Core Web Vitals</h3>
            {Object.keys(webVitalsMetrics).length > 0 ? (
              <div className="h-64">
                <Bar data={webVitalsChartData} options={chartOptions} />
              </div>
            ) : (
              <p className="text-gray-500">No Web Vitals metrics available.</p>
            )}
            
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
              {Object.entries(webVitalsMetrics).map(([name, metrics]) => {
                const latestMetric = metrics[metrics.length - 1];
                let status = 'good';
                
                // Determine status based on metric thresholds
                if (name === 'LCP') {
                  if (latestMetric.value > 2500) status = 'needs-improvement';
                  if (latestMetric.value > 4000) status = 'poor';
                } else if (name === 'FID') {
                  if (latestMetric.value > 100) status = 'needs-improvement';
                  if (latestMetric.value > 300) status = 'poor';
                } else if (name === 'CLS') {
                  if (latestMetric.value > 0.1) status = 'needs-improvement';
                  if (latestMetric.value > 0.25) status = 'poor';
                } else if (name === 'INP') {
                  if (latestMetric.value > 200) status = 'needs-improvement';
                  if (latestMetric.value > 500) status = 'poor';
                }
                
                return (
                  <div key={name} className="bg-gray-100 p-4 rounded">
                    <h4 className="font-semibold">{name}</h4>
                    <p className={`text-xl ${
                      status === 'good' ? 'text-green-500' :
                      status === 'needs-improvement' ? 'text-yellow-500' :
                      'text-red-500'
                    }`}>
                      {latestMetric.value.toFixed(2)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {name === 'LCP' && 'Largest Contentful Paint (ms)'}
                      {name === 'FID' && 'First Input Delay (ms)'}
                      {name === 'CLS' && 'Cumulative Layout Shift'}
                      {name === 'TTFB' && 'Time to First Byte (ms)'}
                      {name === 'INP' && 'Interaction to Next Paint (ms)'}
                    </p>
                  </div>
                );
              })}
            </div>
          </div>
        )}
        
        {activeTab === 'components' && (
          <div>
            <h3 className="text-lg font-semibold mb-2">Component Render Times</h3>
            {Object.keys(componentRenderMetrics).length > 0 ? (
              <div className="h-64">
                <Bar data={componentRenderChartData} options={chartOptions} />
              </div>
            ) : (
              <p className="text-gray-500">No component render metrics available.</p>
            )}
            
            <div className="mt-4">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Component</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Render Time (ms)</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Max Render Time (ms)</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Render Count</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {Object.entries(componentRenderMetrics).map(([name, metrics]) => {
                    const sum = metrics.reduce((acc, metric) => acc + metric.value, 0);
                    const avg = sum / metrics.length;
                    const max = Math.max(...metrics.map(metric => metric.value));
                    
                    return (
                      <tr key={name}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{name}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{avg.toFixed(2)}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{max.toFixed(2)}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{metrics.length}</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}
        
        {activeTab === 'navigation' && (
          <div>
            <h3 className="text-lg font-semibold mb-2">Navigation Metrics</h3>
            {Object.keys(navigationMetrics).length > 0 ? (
              <div className="h-64">
                <Bar data={navigationChartData} options={chartOptions} />
              </div>
            ) : (
              <p className="text-gray-500">No navigation metrics available.</p>
            )}
            
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
              {Object.entries(navigationMetrics).map(([name, metrics]) => {
                const latestMetric = metrics[metrics.length - 1];
                
                return (
                  <div key={name} className="bg-gray-100 p-4 rounded">
                    <h4 className="font-semibold">{name.replace(/-/g, ' ').toUpperCase()}</h4>
                    <p className="text-xl">{latestMetric.value.toFixed(2)} ms</p>
                  </div>
                );
              })}
            </div>
          </div>
        )}
        
        {activeTab === 'timeSeries' && (
          <div>
            <h3 className="text-lg font-semibold mb-2">Time Series Data</h3>
            
            <div className="mb-4">
              <label htmlFor="metricSelect" className="block text-sm font-medium text-gray-700">
                Select Metric
              </label>
              <select
                id="metricSelect"
                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                onChange={(e) => {
                  const timeSeriesData = getTimeSeriesData(e.target.value);
                  if (timeSeriesData) {
                    setTimeSeriesData(timeSeriesData);
                  }
                }}
              >
                <option value="">Select a metric</option>
                {Object.keys(groupedMetrics).map(name => (
                  <option key={name} value={name}>{name}</option>
                ))}
              </select>
            </div>
            
            {/* Time series chart */}
            <div className="h-64">
              {getTimeSeriesData(Object.keys(groupedMetrics)[0]) && (
                <Line
                  data={getTimeSeriesData(Object.keys(groupedMetrics)[0])}
                  options={{
                    ...chartOptions,
                    plugins: {
                      ...chartOptions.plugins,
                      title: {
                        ...chartOptions.plugins.title,
                        text: `${Object.keys(groupedMetrics)[0]} Over Time`,
                      },
                    },
                  }}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceDashboard;
