/**
 * eSIM Provider Factory
 * 
 * This module manages the creation and discovery of eSIM providers.
 * It loads available providers and provides a factory function to create provider instances.
 */

const logger = require('../../utils/logger');
const fs = require('fs');
const path = require('path');

// Map to store provider constructors
const providers = new Map();

// Load available providers from the providers directory
function loadProviders() {
  const providersDir = __dirname;
  
  try {
    // Get all provider files except index.js and any test files
    const providerFiles = fs.readdirSync(providersDir)
      .filter(file => 
        file.endsWith('.js') && 
        file !== 'index.js' && 
        !file.endsWith('.test.js') &&
        !file.endsWith('.spec.js')
      );
    
    // Load each provider
    for (const file of providerFiles) {
      try {
        // Get provider name from filename (without extension)
        const providerName = path.basename(file, '.js');
        
        // Skip if not a JS file or is a test file
        if (providerName === 'index' || providerName.endsWith('.test') || providerName.endsWith('.spec')) {
          continue;
        }
        
        // Load provider
        const providerModule = require(path.join(providersDir, file));
        
        // Store provider constructor
        providers.set(providerName, providerModule);
        
        logger.info('Loaded eSIM provider: ' + providerName);
      } catch (err) {
        logger.error(`Error loading eSIM provider ${file}:`, err);
      }
    }
    
    logger.info('Available eSIM providers', { providers: Array.from(providers.keys()) });
  } catch (err) {
    logger.error('Error loading eSIM providers:', err);
  }
}

// Load providers on module initialization
loadProviders();

/**
 * Create a provider instance
 * 
 * @param {string} providerName - Provider name
 * @param {Object} config - Provider configuration
 * @returns {Object} Provider instance
 */
function createProvider(providerName, config = {}) {
  // Default to mock provider if the requested provider is not found
  if (!providers.has(providerName)) {
    logger.warn(`eSIM provider ${providerName} not found, using mock provider`);
    providerName = 'mock';
    
    // If mock provider is also not available, throw an error
    if (!providers.has(providerName)) {
      throw new Error(`eSIM provider ${providerName} not found`);
    }
  }
  
  // Get provider constructor
  const providerConstructor = providers.get(providerName);
  
  // Create and return provider instance
  return providerConstructor(config);
}

module.exports = {
  createProvider,
  getAvailableProviders: () => Array.from(providers.keys())
};
