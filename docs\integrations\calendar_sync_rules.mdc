---
description: 
globs: 
alwaysApply: false
---
---
description: Defines rules and logic for synchronizing availability and appointments with external calendar providers.
---
# Calendar Synchronization Rules (`calendar_sync_rules.mdc`)

## 1. Purpose and Scope

**Purpose:** To define the strategy, rules, and technical approach for synchronizing user availability and appointment data between the CallSaver platform and external calendar providers (initially focusing on Google Calendar and Microsoft Outlook Calendar).

**Scope:**
- Supported calendar providers.
- Authentication and authorization flow (OAuth 2.0).
- Synchronization direction (one-way, two-way).
- Data mapping between CallSaver appointments/availability and calendar events.
- Synchronization triggers (manual, periodic, event-driven).
- Conflict resolution strategies.
- Handling recurring events and time zones.
- Error handling and user notifications.
- UI for managing connections and sync settings.

## 2. Supported Providers and Authentication

- **Initial Providers:** Google Calendar, Microsoft Outlook Calendar (Office 365/Exchange).
- **Authentication:** Utilize OAuth 2.0 protocol for users to grant CallSaver permission to access their calendar data.
    - Implement standard OAuth flows for each provider.
    - Securely store refresh tokens and manage access tokens per user connection.
    - Handle token expiration and revocation gracefully.
    - Request appropriate scopes (e.g., read/write access to calendar events).

## 3. Synchronization Model

- **Primary Model:** Two-way synchronization for appointments created *through CallSaver*. One-way synchronization (read-only) for external events affecting availability.
- **Availability Sync (External -> CallSaver):**
    - Read events marked as "Busy" from the user's connected external calendar(s).
    - Block corresponding time slots in the CallSaver availability schedule (ref `appointment_scheduler_document.mdc`).
    - This prevents CallSaver from scheduling appointments during times blocked by external events.
- **Appointment Sync (CallSaver -> External):**
    - When an appointment is created, updated, or deleted in CallSaver:
        - Create, update, or delete a corresponding event in the user's connected external calendar(s).
        - Include relevant appointment details (attendee info - respecting privacy, date/time, potentially a link back to CallSaver).
    - **Busy Status:** Mark event as "Busy".
    - **CallSaver Metadata:** Store CallSaver `appointmentId` in an extended property or unique identifier field of the calendar event for tracking.
- **Appointment Sync (External -> CallSaver - Limited):**
    - **Updates/Deletes:** If a user updates or deletes an appointment *event* in their external calendar *that was originally created by CallSaver*, attempt to detect this change (via event IDs and potentially webhooks if available/reliable) and reflect the update/deletion back in CallSaver. This is complex and may have limitations.
    - **New External Events:** Events created directly in the external calendar are generally treated as availability blockers (see Availability Sync), not imported as CallSaver appointments unless a specific import feature is built.

## 4. Data Mapping

- **CallSaver Appointment -> Calendar Event:**
    - **Title:** "CallSaver Appointment with [Attendee Name/Number]" or similar.
    - **Start/End Time:** Map directly. Handle time zones correctly.
    - **Attendees:** Add the user and potentially the external contact (if email is available and consent/privacy allows).
    - **Description:** Include key details, link to CallSaver appointment.
    - **Busy Status:** Mark event as "Busy".
    - **CallSaver Metadata:** Store CallSaver `appointmentId` in an extended property or unique identifier field of the calendar event for tracking.
- **External Busy Event -> CallSaver Availability:**
    - Map event start/end times to blocked slots in CallSaver availability rules.
    - Ignore events marked as "Free".

## 5. Synchronization Triggers

- **Manual:** User explicitly triggers a sync via UI settings.
- **On Action:** Trigger sync immediately after a CallSaver appointment is created/updated/deleted.
- **Periodic:** Run background jobs periodically (e.g., every 15-60 minutes) to poll external calendars for changes affecting availability. Frequency should be configurable and mindful of API rate limits.
- **Webhooks (Optional/Provider-Dependent):** Utilize push notifications/webhooks from calendar providers (if available and reliable, e.g., Google Calendar Push Notifications) for near real-time updates instead of polling. This is generally more efficient but requires public endpoints and robust handling.

## 6. Conflict Resolution

- **Availability:** External calendar events generally take precedence in blocking CallSaver availability.
- **Appointments:**
    - If an update fails due to concurrent modification (e.g., detected via ETags), retry the update or notify the user.
    - Define behavior if a user modifies a CallSaver-created event directly in the external calendar (e.g., attempt to sync back, notify user of discrepancy, or make CallSaver the source of truth). Two-way sync of modifications initiated externally is complex. Prioritize syncing changes made within CallSaver reliably.

## 7. Technical Considerations

- **Time Zones:** Store all dates/times in UTC internally. Correctly handle time zone conversions when interacting with external calendar APIs and displaying information to users.
- **Recurring Events:** Handle recurring events from external calendars correctly when determining availability. Syncing recurring appointments *from* CallSaver *to* external calendars requires careful mapping.
- **API Rate Limits:** Implement respectful polling frequencies and backoff strategies to avoid exceeding provider API rate limits.
- **Error Handling:** Robustly handle API errors, authentication failures, token expirations, and transient network issues. Log errors clearly.
- **User Notifications:** Inform users about successful connection, sync errors, or actions requiring their attention (e.g., re-authentication). Refer to `notifications_and_alerts_document.mdc`.

## 8. UI/UX

- Provide a clear interface in user settings to:
    - Connect/disconnect calendar accounts.
    - View connection status.
    - Configure basic sync preferences (e.g., which calendar to sync to/from).
    - Manually trigger a sync.
    - View sync history or error logs (optional).

## 9. Related Documents

- `docs/functional_specs/appointment_scheduler_document.mdc`
- `docs/functional_specs/api_gateway_routes.mdc` (potential new endpoints for connection management)
- `docs/functional_specs/session_management_strategy.mdc` (secure token storage)
- `docs/functional_specs/notifications_and_alerts_document.mdc`
- `docs/compliance/gdpr_ccpa_compliance_tracking.mdc` (handling PII in calendar events)
