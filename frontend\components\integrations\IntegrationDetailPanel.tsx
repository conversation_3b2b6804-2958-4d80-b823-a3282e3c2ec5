'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { Integration } from './IntegrationsLayout';
import LoadingSpinner from '../shared/LoadingSpinner';
import { Tab } from '@headlessui/react';
import { 
  ArrowPathIcon, 
  ArrowRightOnRectangleIcon, 
  ArrowLeftOnRectangleIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';

interface IntegrationDetailPanelProps {
  integration: Integration;
}

export default function IntegrationDetailPanel({
  integration,
}: IntegrationDetailPanelProps) {
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);
  const queryClient = useQueryClient();

  // Connect integration mutation
  const connectMutation = useMutation({
    mutationFn: async () => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.post(`/api/integrations/${integration.id}/connect`);
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['integrations'] });
      
      // Show success toast
      console.log(`Connected to ${integration.name} successfully`);
    },
    onError: () => {
      // Show error toast
      console.error(`Failed to connect to ${integration.name}`);
    },
  });

  // Disconnect integration mutation
  const disconnectMutation = useMutation({
    mutationFn: async () => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.post(`/api/integrations/${integration.id}/disconnect`);
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['integrations'] });
      
      // Show success toast
      console.log(`Disconnected from ${integration.name} successfully`);
    },
    onError: () => {
      // Show error toast
      console.error(`Failed to disconnect from ${integration.name}`);
    },
  });

  // Sync integration mutation
  const syncMutation = useMutation({
    mutationFn: async () => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.post(`/api/integrations/${integration.id}/sync`);
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['integrations'] });
      
      // Show success toast
      console.log(`Synced ${integration.name} successfully`);
    },
    onError: () => {
      // Show error toast
      console.error(`Failed to sync ${integration.name}`);
    },
  });

  // Handle connect button click
  const handleConnect = () => {
    connectMutation.mutate();
  };

  // Handle disconnect button click
  const handleDisconnect = () => {
    disconnectMutation.mutate();
  };

  // Handle sync button click
  const handleSync = () => {
    syncMutation.mutate();
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div className="p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex-shrink-0 text-gray-400 dark:text-gray-500">
              {integration.icon}
            </div>
            <div className="ml-3">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                {integration.name}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {integration.provider}
              </p>
            </div>
          </div>
          {integration.status === 'connected' ? (
            <button
              type="button"
              onClick={handleDisconnect}
              disabled={disconnectMutation.isPending}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {disconnectMutation.isPending ? (
                <>
                  <LoadingSpinner size="small" color="gray" />
                  <span className="ml-2">Disconnecting...</span>
                </>
              ) : (
                <>
                  <ArrowLeftOnRectangleIcon className="h-4 w-4 mr-1" />
                  Disconnect
                </>
              )}
            </button>
          ) : (
            <button
              type="button"
              onClick={handleConnect}
              disabled={connectMutation.isPending}
              className="inline-flex items-center px-3 py-1.5 border border-transparent shadow-sm text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {connectMutation.isPending ? (
                <>
                  <LoadingSpinner size="small" color="white" />
                  <span className="ml-2">Connecting...</span>
                </>
              ) : (
                <>
                  <ArrowRightOnRectangleIcon className="h-4 w-4 mr-1" />
                  Connect
                </>
              )}
            </button>
          )}
        </div>
      </div>

      {integration.status === 'connected' && (
        <Tab.Group selectedIndex={selectedTabIndex} onChange={setSelectedTabIndex}>
          <Tab.List className="flex border-b border-gray-200 dark:border-gray-700">
            <Tab
              className={({ selected }) =>
                `flex-1 py-3 px-4 text-sm font-medium text-center focus:outline-none ${
                  selected
                    ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`
              }
            >
              Details
            </Tab>
            <Tab
              className={({ selected }) =>
                `flex-1 py-3 px-4 text-sm font-medium text-center focus:outline-none ${
                  selected
                    ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                }`
              }
            >
              Settings
            </Tab>
          </Tab.List>
          <Tab.Panels className="p-4 sm:p-6">
            <Tab.Panel>
              <div className="space-y-6">
                {/* Account Info */}
                {integration.accountInfo && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Account Information
                    </h3>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-md p-3">
                      <dl className="grid grid-cols-1 gap-x-4 gap-y-3 sm:grid-cols-2">
                        {integration.accountInfo.name && (
                          <div className="sm:col-span-1">
                            <dt className="text-xs font-medium text-gray-500 dark:text-gray-400">
                              Name
                            </dt>
                            <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                              {integration.accountInfo.name}
                            </dd>
                          </div>
                        )}
                        {integration.accountInfo.email && (
                          <div className="sm:col-span-1">
                            <dt className="text-xs font-medium text-gray-500 dark:text-gray-400">
                              Email
                            </dt>
                            <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                              {integration.accountInfo.email}
                            </dd>
                          </div>
                        )}
                        {integration.accountInfo.plan && (
                          <div className="sm:col-span-1">
                            <dt className="text-xs font-medium text-gray-500 dark:text-gray-400">
                              Plan
                            </dt>
                            <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                              {integration.accountInfo.plan}
                            </dd>
                          </div>
                        )}
                        {integration.lastSynced && (
                          <div className="sm:col-span-1">
                            <dt className="text-xs font-medium text-gray-500 dark:text-gray-400">
                              Last Synced
                            </dt>
                            <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                              {new Date(integration.lastSynced).toLocaleString()}
                            </dd>
                          </div>
                        )}
                      </dl>
                    </div>
                  </div>
                )}

                {/* Sync Button */}
                <div>
                  <button
                    type="button"
                    onClick={handleSync}
                    disabled={syncMutation.isPending}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {syncMutation.isPending ? (
                      <>
                        <LoadingSpinner size="small" color="gray" />
                        <span className="ml-2">Syncing...</span>
                      </>
                    ) : (
                      <>
                        <ArrowPathIcon className="h-4 w-4 mr-1" />
                        Sync Now
                      </>
                    )}
                  </button>
                </div>

                {/* Integration Description */}
                <div>
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    About this Integration
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {integration.description}
                  </p>
                </div>
              </div>
            </Tab.Panel>
            <Tab.Panel>
              <IntegrationSettings integration={integration} />
            </Tab.Panel>
          </Tab.Panels>
        </Tab.Group>
      )}

      {integration.status === 'disconnected' && (
        <div className="p-4 sm:p-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-md p-4 mb-4">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-1">
              Connect to {integration.name}
            </h3>
            <p className="text-sm text-blue-700 dark:text-blue-200">
              {integration.description}
            </p>
          </div>

          <div className="space-y-4">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Benefits of connecting:
            </h3>
            <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
              {integration.category === 'calendar' && (
                <>
                  <li>Automatically sync appointments and events</li>
                  <li>Schedule calls and meetings directly from CallSaver</li>
                  <li>Avoid double-booking with real-time availability</li>
                </>
              )}
              {integration.category === 'communication' && (
                <>
                  <li>Send notifications and messages automatically</li>
                  <li>Keep your team informed about important calls</li>
                  <li>Streamline your communication workflow</li>
                </>
              )}
              {integration.category === 'crm' && (
                <>
                  <li>Sync contacts and leads automatically</li>
                  <li>Log calls and conversations in your CRM</li>
                  <li>Keep customer information up-to-date</li>
                </>
              )}
              {integration.category === 'automation' && (
                <>
                  <li>Create powerful automation workflows</li>
                  <li>Connect CallSaver to thousands of other apps</li>
                  <li>Save time with automated tasks and processes</li>
                </>
              )}
              {integration.category === 'other' && (
                <>
                  <li>Extend CallSaver's functionality</li>
                  <li>Integrate with your existing tools</li>
                  <li>Customize your workflow to fit your needs</li>
                </>
              )}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}

interface IntegrationSettingsProps {
  integration: Integration;
}

function IntegrationSettings({ integration }: IntegrationSettingsProps) {
  const [syncFrequency, setSyncFrequency] = useState('hourly');
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [dataMapping, setDataMapping] = useState({
    contacts: true,
    events: true,
    messages: true,
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would save the settings to the server
    console.log('Settings saved:', {
      syncFrequency,
      notificationsEnabled,
      dataMapping,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Sync Settings */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Sync Settings
        </h3>
        <div className="space-y-4">
          <div>
            <label htmlFor="syncFrequency" className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
              Sync Frequency
            </label>
            <select
              id="syncFrequency"
              value={syncFrequency}
              onChange={(e) => setSyncFrequency(e.target.value)}
              className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="realtime">Real-time</option>
              <option value="hourly">Hourly</option>
              <option value="daily">Daily</option>
              <option value="manual">Manual only</option>
            </select>
          </div>

          <div className="flex items-center">
            <input
              id="notificationsEnabled"
              type="checkbox"
              checked={notificationsEnabled}
              onChange={(e) => setNotificationsEnabled(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-700 rounded"
            />
            <label htmlFor="notificationsEnabled" className="ml-2 block text-sm text-gray-600 dark:text-gray-400">
              Enable notifications for sync events
            </label>
          </div>
        </div>
      </div>

      {/* Data Mapping */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Data Mapping
        </h3>
        <div className="space-y-2">
          <div className="flex items-center">
            <input
              id="mapContacts"
              type="checkbox"
              checked={dataMapping.contacts}
              onChange={(e) => setDataMapping({ ...dataMapping, contacts: e.target.checked })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-700 rounded"
            />
            <label htmlFor="mapContacts" className="ml-2 block text-sm text-gray-600 dark:text-gray-400">
              Sync contacts
            </label>
          </div>
          <div className="flex items-center">
            <input
              id="mapEvents"
              type="checkbox"
              checked={dataMapping.events}
              onChange={(e) => setDataMapping({ ...dataMapping, events: e.target.checked })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-700 rounded"
            />
            <label htmlFor="mapEvents" className="ml-2 block text-sm text-gray-600 dark:text-gray-400">
              Sync events and appointments
            </label>
          </div>
          <div className="flex items-center">
            <input
              id="mapMessages"
              type="checkbox"
              checked={dataMapping.messages}
              onChange={(e) => setDataMapping({ ...dataMapping, messages: e.target.checked })}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-700 rounded"
            />
            <label htmlFor="mapMessages" className="ml-2 block text-sm text-gray-600 dark:text-gray-400">
              Sync messages and communications
            </label>
          </div>
        </div>
      </div>

      {/* Advanced Settings */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Advanced Settings
        </h3>
        <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-md p-3 text-sm text-yellow-700 dark:text-yellow-200">
          <p>
            Advanced settings are available in the {integration.provider} dashboard. 
            <a href="#" className="ml-1 text-yellow-600 dark:text-yellow-300 underline">
              Open {integration.provider} dashboard
            </a>
          </p>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          type="submit"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
        >
          <Cog6ToothIcon className="h-4 w-4 mr-1" />
          Save Settings
        </button>
      </div>
    </form>
  );
}
