"use client";

import React from 'react';
import { ArrowUpIcon, ArrowDownIcon } from '@heroicons/react/24/solid';
import { 
  PhoneIcon, 
  ClockIcon, 
  CheckCircleIcon, 
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';

// Component that renders a metric card with futuristic styling
export function MetricCard({ title, value, change, description, color = 'blue', invertChange = false, icon }) {
  // Determine if change is positive (considering the invert flag)
  const isPositive = invertChange ? change < 0 : change > 0;
  
  // Icon mapping
  const iconMap = {
    phone: <PhoneIcon className="h-6 w-6" />,
    clock: <ClockIcon className="h-6 w-6" />,
    checkCircle: <CheckCircleIcon className="h-6 w-6" />,
    currencyDollar: <CurrencyDollarIcon className="h-6 w-6" />
  };

  // Dynamic classes based on color prop
  const colorClasses = {
    blue: {
      border: 'border-blue-600/40',
      text: 'text-blue-400',
      shadow: 'before:shadow-neon-blue',
      gradient: 'from-blue-900/20 to-blue-800/10',
      highlight: 'bg-blue-600/20',
      icon: 'bg-blue-900/40 text-blue-400'
    },
    purple: {
      border: 'border-purple-600/40',
      text: 'text-purple-400',
      shadow: 'before:shadow-neon-purple',
      gradient: 'from-purple-900/20 to-purple-800/10',
      highlight: 'bg-purple-600/20',
      icon: 'bg-purple-900/40 text-purple-400'
    },
    green: {
      border: 'border-green-600/40',
      text: 'text-green-400',
      shadow: 'before:shadow-neon-green',
      gradient: 'from-green-900/20 to-green-800/10',
      highlight: 'bg-green-600/20',
      icon: 'bg-green-900/40 text-green-400'
    },
    yellow: {
      border: 'border-yellow-600/40',
      text: 'text-yellow-400',
      shadow: 'before:shadow-neon-yellow',
      gradient: 'from-yellow-900/20 to-yellow-800/10',
      highlight: 'bg-yellow-600/20',
      icon: 'bg-yellow-900/40 text-yellow-400'
    },
    indigo: {
      border: 'border-indigo-600/40',
      text: 'text-indigo-400',
      shadow: 'before:shadow-neon-indigo',
      gradient: 'from-indigo-900/20 to-indigo-800/10',
      highlight: 'bg-indigo-600/20',
      icon: 'bg-indigo-900/40 text-indigo-400'
    }
  };

  const currentColor = colorClasses[color] || colorClasses.blue;

  return (
    <div className={`
      dashboard-card border-glow ${currentColor.shadow}
      transition-all duration-300 hover:scale-[1.02] group
      min-h-[150px] flex flex-col
      border border-gray-800/60 rounded-xl bg-gray-900/90
      backdrop-blur-sm relative overflow-hidden
    `}>
      {/* Top right ambient light effect */}
      <div className="absolute -top-10 -right-10 w-24 h-24 rounded-full blur-xl opacity-20 bg-gradient-to-br from-white via-transparent to-transparent"></div>
      
      {/* Bottom edge light bar */}
      <div className={`absolute bottom-0 left-0 right-0 h-0.5 ${currentColor.text} opacity-30`}></div>
      
      <div className="p-5 relative z-10 flex-grow flex flex-col h-full">
        <div className="flex justify-between items-start mb-3 flex-wrap">
          <div className="flex items-center mr-2 mb-1">
            {icon && (
              <div className={`p-2 rounded-md ${currentColor.icon} mr-2 shadow-lg border border-${color}-500/20`}>
                {icon}
              </div>
            )}
            <h3 className={`font-medium text-lg ${currentColor.text}`}>{title}</h3>
          </div>
          <div className={`
            ${isPositive ? 'text-green-400' : 'text-red-400'} 
            flex items-center text-sm font-medium bg-gradient-to-r ${isPositive ? 'from-green-900/30 to-green-800/20' : 'from-red-900/30 to-red-800/20'} 
            py-1 px-2 rounded-md border ${isPositive ? 'border-green-500/20' : 'border-red-500/20'}
            whitespace-nowrap min-w-[60px] max-w-[80px] justify-center
          `}>
            {isPositive ? (
              <ArrowUpIcon className="h-3.5 w-3.5 mr-1 flex-shrink-0" />
            ) : (
              <ArrowDownIcon className="h-3.5 w-3.5 mr-1 flex-shrink-0" />
            )}
            <span className="truncate">{Math.abs(change)}%</span>
          </div>
        </div>
        
        <div className="mb-4 flex-grow flex flex-col justify-center">
          <div className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-white to-gray-300 text-transparent bg-clip-text">
            {value}
          </div>
          {description && (
            <p className="text-gray-400 text-xs mt-2">{description}</p>
          )}
        </div>
        
        {/* Gradient bar showing the change trend */}
        <div className="h-1.5 w-full bg-gray-800 rounded-full overflow-hidden mt-auto">
          <div
            className={`h-full rounded-full bg-gradient-to-r ${isPositive ? 'from-green-500 to-green-400' : 'from-red-500 to-red-400'} transition-all duration-500 ease-out`}
            style={{ width: `${Math.min(Math.abs(change) * 5, 100)}%` }}
          ></div>
        </div>
      </div>
      
      {/* Ambient hover glow effect */}
      <div className="card-glow"></div>
    </div>
  );
} 