'use client';

import { useState, useEffect } from 'react';
import supabase from '../utils/supabase';

export default function SupabaseExample() {
  const [loading, setLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState('Checking...');

  useEffect(() => {
    async function checkConnection() {
      try {
        // Simple query to test the connection
        const { data, error } = await supabase
          .from('your_table_name') // Replace with an actual table in your Supabase
          .select('count(*)', { count: 'exact', head: true });
        
        if (error) {
          console.error('Supabase connection error:', error);
          setConnectionStatus(`Error: ${error.message}`);
        } else {
          setConnectionStatus('Connected to Supabase!');
        }
      } catch (error) {
        console.error('Unexpected error:', error);
        setConnectionStatus(`Unexpected error: ${error.message}`);
      } finally {
        setLoading(false);
      }
    }

    checkConnection();
  }, []);

  return (
    <div className="bg-gray-900/70 backdrop-blur-lg rounded-2xl border border-purple-500/20 shadow-xl p-6 md:p-8 w-full max-w-md mx-auto my-8">
      <h2 className="text-xl text-white/80 mb-4">Supabase Connection Status</h2>
      <div className="flex items-center">
        <div className={`h-4 w-4 rounded-full mr-3 ${
          connectionStatus.includes('Error') 
            ? 'bg-red-500' 
            : connectionStatus === 'Connected to Supabase!' 
              ? 'bg-green-500' 
              : 'bg-yellow-500'
        }`}></div>
        <p className="text-white">
          {loading ? 'Testing connection...' : connectionStatus}
        </p>
      </div>
      <p className="text-gray-400 text-sm mt-4">
        Using Direct Connection with Node.js as recommended for your CallSaver app.
      </p>
    </div>
  );
} 