'use client';

import { useState } from 'react';
import LoadingSpinner from '../shared/LoadingSpinner';

interface DataInputFormProps {
  onSubmit: (text: string) => void;
  isSubmitting: boolean;
}

export default function DataInputForm({ onSubmit, isSubmitting }: DataInputFormProps) {
  const [text, setText] = useState('');
  const [error, setError] = useState<string | null>(null);

  // Handle text change
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
    
    // Reset error if text is not empty
    if (e.target.value.trim()) {
      setError(null);
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate text
    if (!text.trim()) {
      setError('Please enter some text');
      return;
    }
    
    // Submit text
    onSubmit(text);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="trainingText" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Training Text
        </label>
        <textarea
          id="trainingText"
          rows={10}
          value={text}
          onChange={handleTextChange}
          disabled={isSubmitting}
          placeholder="Enter training data here. For example, provide information about your business, products, services, or common customer questions and answers."
          className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
        {error && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">
            {error}
          </p>
        )}
      </div>

      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-md p-3 text-sm text-blue-700 dark:text-blue-300">
        <h3 className="font-medium">Tips for effective training:</h3>
        <ul className="mt-1 list-disc list-inside text-blue-600 dark:text-blue-400 space-y-1">
          <li>Include common questions and their answers</li>
          <li>Add specific information about your products or services</li>
          <li>Provide examples of how to handle different customer scenarios</li>
          <li>Use clear, concise language</li>
        </ul>
      </div>

      <div className="flex justify-end">
        <button
          type="submit"
          disabled={isSubmitting || !text.trim()}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSubmitting ? (
            <>
              <LoadingSpinner size="small" color="white" />
              <span className="ml-2">Submitting...</span>
            </>
          ) : (
            'Submit'
          )}
        </button>
      </div>
    </form>
  );
}
