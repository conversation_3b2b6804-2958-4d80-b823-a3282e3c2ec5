---
description: Defines rules and best practices for managing environment variables and application configuration.
---
# Environment Configuration Rules (`env_configuration_rules.mdc`)

## 1. Purpose and Scope

**Purpose:** To establish clear rules and best practices for managing environment variables and application configuration across different deployment environments (e.g., development, testing, staging, production) for the CallSaver platform.

**Scope:**
- Environment variable naming conventions.
- `.env` file usage and management.
- Loading and parsing configuration in backend and frontend.
- Configuration validation.
- Handling secrets and sensitive information.
- Environment-specific settings.
- Consistency between backend and frontend configuration access.

## 2. Naming Conventions

- Use uppercase `SNAKE_CASE` for environment variable names (e.g., `DATABASE_URL`, `TWILIO_AUTH_TOKEN`).
- Prefix variables logically where appropriate (e.g., `STRIPE_SECRET_KEY`, `STRIPE_PUBLISHABLE_KEY`, `NEXT_PUBLIC_API_BASE_URL`).
- Use `NEXT_PUBLIC_` prefix for variables intended to be exposed to the frontend browser bundle (Next.js specific convention). Be extremely cautious about what is exposed publicly.

## 3. `.env` File Management

- Use `.env` files for managing environment variables locally during development.
- **Standard Files:**
    - `.env`: Default local development values. **Should NOT be committed to Git.**
    - `.env.example`: Template file containing all required environment variables with placeholder or non-sensitive default values. **Should be committed to Git.** Developers copy this to `.env` and fill in secrets.
    - `.env.test`: Variables specifically for automated testing environments (if needed). May or may not be committed depending on sensitivity.
    - `.env.production`, `.env.staging`: Generally **NOT** used directly. Configuration for these environments should be injected via the deployment platform's environment variable management system.
- **Loading:** Use libraries like `dotenv` (often integrated into frameworks like Next.js) to load variables from `.env` files into `process.env` during local development.

## 4. Configuration Loading and Parsing

- **Backend (Node.js):**
    - Access variables via `process.env.VARIABLE_NAME`.
    - Implement a dedicated configuration module (e.g., `back/backend/config/index.js`) that:
        - Loads variables from `process.env`.
        - Performs type casting (e.g., string to number, boolean).
        - Provides default values for non-essential variables if appropriate.
        - Validates required variables at application startup (see Section 5).
        - Exports a structured configuration object.
- **Frontend (Next.js):**
    - Access server-side only variables via `process.env` in server components or API routes.
    - Access variables exposed to the browser via `process.env.NEXT_PUBLIC_VARIABLE_NAME`.
    - Avoid exposing secrets using the `NEXT_PUBLIC_` prefix. Sensitive configuration needed by the frontend should typically be fetched from a dedicated backend API endpoint after authentication.
    - Consider a shared configuration structure or fetching mechanism if complex config is needed client-side.

## 5. Configuration Validation

- **Startup Validation:** The application (especially the backend) MUST validate the presence and basic format of critical environment variables upon startup.
- **Mechanism:** Use a validation library (e.g., `zod`, `joi`, `envalid`) within the configuration module to define schemas for expected environment variables, their types, and whether they are required.
- **Action:** If validation fails (e.g., required variable missing), the application should fail to start with a clear error message indicating the missing/invalid configuration. This prevents runtime errors due to misconfiguration.

## 6. Secrets Management

- **NEVER** commit secrets (API keys, database passwords, signing keys) directly to the Git repository, including in `.env.example`.
- **Local Development:** Store secrets in the local `.env` file (which is gitignored).
- **Staging/Production:** Inject secrets securely using the deployment platform's environment variable management or a dedicated secrets management service (e.g., AWS Secrets Manager, HashiCorp Vault, Doppler).
- **Access Control:** Limit access to production secrets to authorized personnel only.

## 7. Environment-Specific Settings

- Use environment variables to control behavior differences between environments:
    - `NODE_ENV`: Standard variable (`development`, `test`, `production`).
    - Database connection URLs.
    - External service API keys (use test keys in dev/staging).
    - Logging levels.
    - Feature flag provider SDK keys (if environment-specific).
    - Base URLs for frontend/backend communication.

## 8. Related Documents

- `.env.example` (Template file)
- `docs/functional_specs/cursor_project_rules.mdc`
- `docs/security_audit.md` (if exists)
- Deployment platform documentation regarding environment variable injection.
- `back/backend/config/index.js` (or equivalent configuration module)
