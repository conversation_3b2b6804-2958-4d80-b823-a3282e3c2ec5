'use server';

import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// Create a new Prisma client
const prisma = new PrismaClient();

// This route provides a diagnostic tool to verify phone numbers in the database
export async function GET(request) {
  console.log('[API Check] Running phone number database check');
  
  try {
    // Get authentication info from Supabase
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      { 
        cookies: { 
          get: (name) => cookieStore.get(name)?.value
        } 
      }
    );
    
    // Get user session for authentication
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      console.log('[API Check] No valid session found');
      return NextResponse.json({
        success: false,
        message: 'Authentication required',
        session: null
      }, { status: 401 });
    }
    
    // Get basic session info for debugging
    const sessionInfo = {
      userId: session.user.id,
      email: session.user.email,
      tokenExpiry: new Date(session.expires_at * 1000).toISOString()
    };
    
    // Get user from database
    const user = await prisma.user.findUnique({
      where: { 
        email: session.user.email 
      },
      select: {
        id: true,
        email: true,
        name: true,
        credits: true,
        twilioAccountSid: true,
        createdAt: true
      }
    });
    
    if (!user) {
      console.log('[API Check] User not found in database');
      return NextResponse.json({
        success: false,
        message: 'User not found in database',
        session: sessionInfo,
        user: null,
        phoneNumbers: []
      });
    }
    
    // Query all PhoneNumber records for this user
    const phoneNumbers = await prisma.phoneNumber.findMany({
      where: { 
        userId: user.id 
      },
      orderBy: { 
        createdAt: 'asc' 
      }
    });
    
    console.log(`[API Check] Found ${phoneNumbers.length} phone numbers for user ${user.id}`);
    
    // Return diagnostic information
    return NextResponse.json({
      success: true,
      message: 'Database check completed successfully',
      session: sessionInfo,
      user: {
        ...user,
        // Format dates for readability
        createdAt: user.createdAt?.toISOString()
      },
      phoneNumbers: phoneNumbers.map(num => ({
        ...num,
        // Format dates for readability
        createdAt: num.createdAt?.toISOString(),
        updatedAt: num.updatedAt?.toISOString(),
        lastTrainedAt: num.lastTrainedAt?.toISOString()
      })),
      countsOnly: {
        phoneNumberCount: phoneNumbers.length
      }
    });
  } catch (error) {
    console.error('[API Check] Error in database check:', error);
    return NextResponse.json({ 
      success: false, 
      message: `Database check error: ${error.message}`,
      error: error.toString()
    }, { status: 500 });
  } finally {
    // Disconnect Prisma
    await prisma.$disconnect();
  }
} 