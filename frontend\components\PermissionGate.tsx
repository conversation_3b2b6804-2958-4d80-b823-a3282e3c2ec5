import React from 'react';
import { usePermissions } from '../hooks/usePermissions';

interface PermissionGateProps {
  permission?: string;
  anyPermission?: string[];
  allPermissions?: string[];
  resource?: string;
  action?: string;
  scope?: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  loading?: React.ReactNode;
}

/**
 * A component that conditionally renders content based on user permissions
 *
 * @param permission - A single permission to check
 * @param anyPermission - An array of permissions, any of which will grant access
 * @param allPermissions - An array of permissions, all of which are required for access
 * @param resource - The resource to check access for (used with action and scope)
 * @param action - The action to check (default: 'read')
 * @param scope - The scope to check (default: 'any')
 * @param children - Content to render if the user has the required permissions
 * @param fallback - Optional content to render if the user doesn't have the required permissions
 * @param loading - Optional content to render while permissions are being loaded
 */
export const PermissionGate: React.FC<PermissionGateProps> = ({
  permission,
  anyPermission,
  allPermissions,
  resource,
  action = 'read',
  scope = 'any',
  children,
  fallback = null,
  loading = null,
}) => {
  const {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccess,
    isLoading
  } = usePermissions();

  // If permissions are still loading, show the loading content
  if (isLoading) {
    return <>{loading}</>;
  }

  // Check if the user has the required permissions
  let hasAccess = false;

  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (anyPermission) {
    hasAccess = hasAnyPermission(anyPermission);
  } else if (allPermissions) {
    hasAccess = hasAllPermissions(allPermissions);
  } else if (resource) {
    hasAccess = canAccess(resource, action, scope);
  } else {
    // If no permissions are specified, allow access
    hasAccess = true;
  }

  // Render the appropriate content
  return hasAccess ? <>{children}</> : <>{fallback}</>;
};

export default PermissionGate;
