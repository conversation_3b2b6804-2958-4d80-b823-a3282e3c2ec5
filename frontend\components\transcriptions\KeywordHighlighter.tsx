'use client';

import { useState, useEffect } from 'react';
import { ChevronDownIcon, ChevronUpIcon, AdjustmentsHorizontalIcon } from '@heroicons/react/24/outline';

interface Keyword {
  text: string;
  relevance: number;
}

interface Entity {
  text: string;
  type: string;
  relevance: number;
}

interface Category {
  name: string;
  count: number;
  items: Array<{ text: string; type: string; relevance: number }>;
}

interface KeywordHighlighterProps {
  transcriptionText: string;
  keywords?: Keyword[];
  entities?: Entity[];
}

export default function KeywordHighlighter({
  transcriptionText,
  keywords = [],
  entities = []
}: KeywordHighlighterProps) {
  const [activeFilter, setActiveFilter] = useState<string | null>(null);
  const [showTooltip, setShowTooltip] = useState<{ text: string; x: number; y: number } | null>(null);
  const [showFilters, setShowFilters] = useState(true);
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [categories, setCategories] = useState<Category[]>([]);

  // Combine keywords and entities for highlighting
  const allHighlights = [
    ...keywords.map(k => ({
      text: k.text,
      type: 'keyword',
      relevance: k.relevance
    })),
    ...entities.map(e => ({
      text: e.text,
      type: e.type,
      relevance: e.relevance
    }))
  ];

  // Get unique entity types for filtering
  const entityTypes = Array.from(new Set(entities.map(e => e.type)));

  // Group highlights by category
  useEffect(() => {
    // Create categories
    const newCategories: Category[] = [
      {
        name: 'keyword',
        count: keywords.length,
        items: keywords.map(k => ({ text: k.text, type: 'keyword', relevance: k.relevance }))
      }
    ];

    // Add entity categories
    entityTypes.forEach(type => {
      const typeEntities = entities.filter(e => e.type === type);
      newCategories.push({
        name: type,
        count: typeEntities.length,
        items: typeEntities.map(e => ({ text: e.text, type: e.type, relevance: e.relevance }))
      });
    });

    // Sort categories by count (descending)
    newCategories.sort((a, b) => b.count - a.count);

    // Initialize expanded state
    const initialExpandedState: Record<string, boolean> = {};
    newCategories.forEach(cat => {
      initialExpandedState[cat.name] = true;
    });

    setCategories(newCategories);
    setExpandedCategories(initialExpandedState);
  }, [keywords, entities, entityTypes]);

  // Filter highlights based on active filter and search term
  const filteredHighlights = allHighlights
    .filter(h => {
      // Filter by type if active filter is set
      if (activeFilter && activeFilter !== h.type) {
        return false;
      }

      // Filter by search term if set
      if (searchTerm && !h.text.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      return true;
    });

  // Helper function to get highlight color
  const getHighlightColor = (type: string) => {
    switch (type) {
      case 'keyword':
        return 'bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200';
      case 'person':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
      case 'organization':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200';
      case 'location':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
      case 'date':
        return 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  // Helper function to get badge color
  const getBadgeColor = (type: string) => {
    switch (type) {
      case 'keyword':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200';
      case 'person':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'organization':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'location':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'date':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  // Function to highlight text
  const highlightText = () => {
    if (!transcriptionText) return 'No transcription available';
    if (filteredHighlights.length === 0) return transcriptionText;

    // Sort highlights by length (descending) to avoid nested highlights
    const sortedHighlights = [...filteredHighlights].sort((a, b) =>
      b.text.length - a.text.length
    );

    let result = transcriptionText;
    let offset = 0;

    // Replace each keyword with a highlighted version
    sortedHighlights.forEach(highlight => {
      const regex = new RegExp(`\\b${highlight.text}\\b`, 'gi');
      let match;
      let tempResult = '';
      let lastIndex = 0;

      while ((match = regex.exec(result)) !== null) {
        const matchIndex = match.index + offset;

        // Add text before the match
        tempResult += result.substring(lastIndex, match.index);

        // Add the highlighted match
        tempResult += `<span class="${getHighlightColor(highlight.type)}" data-highlight="${highlight.text}" data-type="${highlight.type}" data-relevance="${highlight.relevance}">${match[0]}</span>`;

        lastIndex = match.index + match[0].length;
        offset += tempResult.length - lastIndex;
      }

      // Add any remaining text
      tempResult += result.substring(lastIndex);
      result = tempResult;
    });

    return result;
  };

  // Handle mouse events for tooltips
  const handleMouseEnter = (e: React.MouseEvent<HTMLDivElement>) => {
    const target = e.target as HTMLElement;
    if (target.hasAttribute('data-highlight')) {
      const text = target.getAttribute('data-highlight') || '';
      const type = target.getAttribute('data-type') || '';
      const relevance = target.getAttribute('data-relevance') || '';

      setShowTooltip({
        text: `${text} (${type.charAt(0).toUpperCase() + type.slice(1)}, relevance: ${parseFloat(relevance).toFixed(2)})`,
        x: e.clientX,
        y: e.clientY
      });
    }
  };

  // Toggle category expansion
  const toggleCategory = (category: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle clicking on a keyword/entity in the sidebar
  const handleItemClick = (item: { text: string; type: string }) => {
    // Find the item in the text and scroll to it
    const highlightedText = document.querySelector(`[data-highlight="${item.text}"]`);
    if (highlightedText) {
      highlightedText.scrollIntoView({ behavior: 'smooth', block: 'center' });

      // Flash effect
      highlightedText.classList.add('animate-pulse');
      setTimeout(() => {
        highlightedText.classList.remove('animate-pulse');
      }, 1500);
    }
  };

  const handleMouseLeave = () => {
    setShowTooltip(null);
  };

  return (
    <div>
      {/* Enhanced filter UI */}
      {(keywords.length > 0 || entities.length > 0) && (
        <div className="mb-4 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
          {/* Header with toggle */}
          <div className="bg-gray-50 dark:bg-gray-800 px-4 py-3 flex justify-between items-center">
            <div className="flex items-center">
              <AdjustmentsHorizontalIcon className="h-5 w-5 text-gray-500 dark:text-gray-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Entities & Keywords
              </h3>
              <span className="ml-2 text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-0.5 rounded-full">
                {allHighlights.length}
              </span>
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              {showFilters ? (
                <ChevronUpIcon className="h-5 w-5" />
              ) : (
                <ChevronDownIcon className="h-5 w-5" />
              )}
            </button>
          </div>

          {/* Expanded filter UI */}
          {showFilters && (
            <div className="p-4">
              {/* Search input */}
              <div className="mb-4">
                <input
                  type="text"
                  placeholder="Search entities and keywords..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white text-sm"
                />
              </div>

              {/* Filter buttons */}
              <div className="mb-4 flex flex-wrap gap-2">
                <button
                  onClick={() => setActiveFilter(null)}
                  className={`px-3 py-1 text-xs font-medium rounded-full ${
                    activeFilter === null
                      ? 'bg-gray-800 text-white dark:bg-white dark:text-gray-800'
                      : 'bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                  }`}
                >
                  All
                </button>

                {categories.map(category => (
                  <button
                    key={category.name}
                    onClick={() => setActiveFilter(activeFilter === category.name ? null : category.name)}
                    className={`px-3 py-1 text-xs font-medium rounded-full ${
                      activeFilter === category.name
                        ? 'bg-gray-800 text-white dark:bg-white dark:text-gray-800'
                        : getBadgeColor(category.name)
                    }`}
                  >
                    {category.name.charAt(0).toUpperCase() + category.name.slice(1)}s ({category.count})
                  </button>
                ))}
              </div>

              {/* Categorized items */}
              <div className="space-y-3">
                {categories.map(category => (
                  <div key={category.name} className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
                    {/* Category header */}
                    <div
                      className={`px-3 py-2 flex justify-between items-center cursor-pointer ${getBadgeColor(category.name)}`}
                      onClick={() => toggleCategory(category.name)}
                    >
                      <div className="font-medium">
                        {category.name.charAt(0).toUpperCase() + category.name.slice(1)}s
                        <span className="ml-2 text-xs bg-gray-200/50 dark:bg-gray-700/50 px-2 py-0.5 rounded-full">
                          {category.count}
                        </span>
                      </div>
                      {expandedCategories[category.name] ? (
                        <ChevronUpIcon className="h-4 w-4" />
                      ) : (
                        <ChevronDownIcon className="h-4 w-4" />
                      )}
                    </div>

                    {/* Category items */}
                    {expandedCategories[category.name] && (
                      <div className="p-2 bg-white dark:bg-gray-800 max-h-40 overflow-y-auto">
                        <div className="grid grid-cols-2 gap-1">
                          {category.items
                            .filter(item => !searchTerm || item.text.toLowerCase().includes(searchTerm.toLowerCase()))
                            .sort((a, b) => b.relevance - a.relevance)
                            .map((item, idx) => (
                              <div
                                key={`${item.text}-${idx}`}
                                className="text-xs px-2 py-1 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 rounded truncate"
                                onClick={() => handleItemClick(item)}
                                title={`${item.text} (relevance: ${item.relevance.toFixed(2)})`}
                              >
                                {item.text}
                              </div>
                            ))
                          }
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Highlighted text */}
      <div
        className="text-gray-800 dark:text-gray-200 whitespace-pre-wrap p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
        dangerouslySetInnerHTML={{ __html: highlightText() }}
        onMouseMove={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      />

      {/* Summary of highlighted items */}
      {filteredHighlights.length > 0 && (
        <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
          Showing {filteredHighlights.length} highlighted {filteredHighlights.length === 1 ? 'item' : 'items'}
          {activeFilter && ` of type "${activeFilter}"`}
          {searchTerm && ` matching "${searchTerm}"`}
        </div>
      )}

      {/* Tooltip */}
      {showTooltip && (
        <div
          className="fixed bg-gray-900 text-white text-xs rounded py-1 px-2 z-50 pointer-events-none"
          style={{
            left: `${showTooltip.x + 10}px`,
            top: `${showTooltip.y + 10}px`
          }}
        >
          {showTooltip.text}
        </div>
      )}
    </div>
  );
}
