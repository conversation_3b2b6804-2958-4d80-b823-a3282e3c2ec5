import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    // Parse the form data from <PERSON><PERSON><PERSON> (comes as form-urlencoded)
    const formData = await request.formData();
    
    // Extract the relevant call data
    const callSid = formData.get('CallSid');
    const callStatus = formData.get('CallStatus');
    const from = formData.get('From');
    const to = formData.get('To');
    const direction = formData.get('Direction');
    const timestamp = new Date().toISOString();
    
    // Log the status update
    console.log(`[Voice Status Webhook] Call ${callSid} status update: ${callStatus}`);
    console.log(`[Voice Status Webhook] From: ${from}, To: ${to}, Direction: ${direction}`);
    
    // Here you would typically:
    // 1. Update a database record of the call
    // 2. Trigger any notifications needed
    // 3. Update UI state if using WebSockets
    
    // Store this status update in your database
    // This is where you'd connect to your database to store the call status
    // For now, we'll just log it
    
    // Return a successful response to <PERSON><PERSON><PERSON>
    return NextResponse.json({
      success: true,
      message: 'Status update received',
      timestamp,
      callSid,
      callStatus
    });
    
  } catch (error) {
    console.error('[Voice Status Webhook] Error processing status update:', error);
    
    // Return an error response
    return NextResponse.json({
      success: false,
      message: `Error processing status update: ${error.message}`
    }, { status: 500 });
  }
} 