"use client";

import { useEffect, useState, useCallback } from "react";
import { motion } from "framer-motion";
import { 
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>hart, Line, <PERSON><PERSON><PERSON>, Pie, Cell,
  XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer
} from "recharts";
import { CircularProgressbar, buildStyles } from 'react-circular-progressbar';
import 'react-circular-progressbar/dist/styles.css';
import { 
  Phone, MessageSquare, Clock, Calendar, Users, TrendingUp,
  Filter, RefreshCw, Download, Bot, PhoneOff, AlertCircle,
  CheckCircle2, Timer
} from "lucide-react";
import { format, subDays } from "date-fns";

// Note: Import components directly for now - we'll properly fix paths later
// These are mock imports since we're not executing this code
const Card = ({ className, children }) => <div className={className}>{children}</div>;
const CardContent = ({ className, children }) => <div className={className}>{children}</div>;
const CardDescription = ({ className, children }) => <div className={className}>{children}</div>;
const CardHeader = ({ className, children }) => <div className={className}>{children}</div>;
const CardTitle = ({ className, children }) => <div className={className}>{children}</div>;
const CardFooter = ({ className, children }) => <div className={className}>{children}</div>;
const Button = ({ children, className, variant }) => <button className={className}>{children}</button>;
const Select = ({ children }) => <select>{children}</select>;
const SelectContent = ({ children }) => <div>{children}</div>;
const SelectItem = ({ children, value }) => <option value={value}>{children}</option>;
const SelectTrigger = ({ children }) => <div>{children}</div>;
const SelectValue = ({ children }) => <div>{children}</div>;
const Tabs = ({ children, defaultValue }) => <div>{children}</div>;
const TabsContent = ({ children, value }) => <div>{children}</div>;
const TabsList = ({ children }) => <div>{children}</div>;
const TabsTrigger = ({ children, value }) => <div>{children}</div>;

// Types for analytics data
interface DailyTrend {
  date: string;
  inbound: number;
  outbound: number;
  total: number;
}

interface PeakHour {
  hour: number;
  count: number;
}

interface TopCaller {
  phoneNumber: string;
  callCount: number;
}

interface AnalyticsData {
  totalCalls: number;
  totalMessages: number;
  totalDuration: number;
  callsByDirection: {
    inbound: number;
    outbound: number;
  };
  messagesByDirection: {
    inbound: number;
    outbound: number;
  };
  callTrend: DailyTrend[];
  messageTrend: DailyTrend[];
  callsByStatus: {
    completed: number;
    noAnswer: number;
    busy: number;
    failed: number;
    canceled: number;
  };
  peakCallHours: PeakHour[];
  topCallers: TopCaller[];
  missedCalls: number;
  missedCallRate: number;
  aiHandoffRate: number;
  aiHandledCalls: number;
  aiHandledMessages: number;
  smsResponseRate: number;
  smsResponseTime: number;
  organizationId: string;
}

// StatCard Component
const StatCard = ({ 
  title, 
  value, 
  icon, 
  trend, 
  description, 
  loading = false
}: {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: number;
  description?: string;
  loading?: boolean;
}) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{loading ? '...' : value}</div>
        {description && (
          <p className="text-xs text-muted-foreground">{description}</p>
        )}
        {trend !== undefined && (
          <div className="flex items-center pt-1">
            <span className={`text-xs ${trend > 0 ? 'text-green-500' : trend < 0 ? 'text-red-500' : 'text-gray-500'}`}>
              {trend > 0 ? '+' : ''}{trend}%
            </span>
            <TrendingUp className={`h-3 w-3 ml-1 ${trend > 0 ? 'text-green-500' : trend < 0 ? 'text-red-500' : 'text-gray-500'}`} />
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Chart Card Component
const ChartCard = ({ 
  title, 
  description, 
  children 
}: { 
  title: string;
  description?: string;
  children: React.ReactNode;
}) => {
  return (
    <Card className="col-span-3">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="pl-2">
        {children}
      </CardContent>
    </Card>
  );
};

// Main Analytics Page Component
export default function AnalyticsPage() {
  const [loading, setLoading] = useState<boolean>(true);
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [timeRange, setTimeRange] = useState<string>("7d");
  
  // Format duration from seconds to hours:minutes:seconds
  const formatDuration = useCallback((seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    return `${hours}h ${minutes}m ${remainingSeconds}s`;
  }, []);

  // Calculate trend percentage
  const calculateTrend = useCallback((current: number, previous: number): number => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return Math.round(((current - previous) / previous) * 100);
  }, []);

  // Get comparative trends for current vs previous period
  const getComparativeTrends = useCallback(() => {
    if (!data) return { calls: 0, messages: 0 };
    
    // For simplicity in mock data, we'll just create random trends
    const callTrend = timeRange === "7d" ? Math.random() * 30 - 15 : Math.random() * 20 - 10;
    const messageTrend = timeRange === "7d" ? Math.random() * 25 - 10 : Math.random() * 15 - 5;
    
    return {
      calls: Math.round(callTrend),
      messages: Math.round(messageTrend),
      aiHandled: Math.round(Math.random() * 40 - 15),
      missedRate: Math.round(Math.random() * 10 - 5)
    };
  }, [data, timeRange]);

  // Generate mock data for development
  const generateMockData = useCallback((): AnalyticsData => {
    // Base metrics
    const totalCalls = Math.floor(Math.random() * 500) + 100;
    const inboundCalls = Math.floor(totalCalls * 0.65);
    const outboundCalls = totalCalls - inboundCalls;
    
    const totalMessages = Math.floor(Math.random() * 800) + 200;
    const inboundMessages = Math.floor(totalMessages * 0.58); 
    const outboundMessages = totalMessages - inboundMessages;
    
    // Derived metrics
    const missedCalls = Math.floor(inboundCalls * (Math.random() * 0.2 + 0.1)); // 10-30% missed rate
    const noAnswerCalls = missedCalls;
    const busyCalls = Math.floor(missedCalls * 0.4);
    const missedCallRate = +(missedCalls / inboundCalls * 100).toFixed(1);
    
    // AI metrics
    const aiHandledCalls = Math.floor(inboundCalls * (Math.random() * 0.3 + 0.4)); // 40-70% AI handled
    const aiHandledMessages = Math.floor(inboundMessages * (Math.random() * 0.3 + 0.5)); // 50-80% AI handled
    const aiHandoffRate = +(aiHandledCalls / inboundCalls * 100).toFixed(1);
    
    // SMS metrics
    const smsResponseRate = +(Math.random() * 20 + 75).toFixed(1); // 75-95% response rate
    const smsResponseTime = +(Math.random() * 60 + 30).toFixed(1); // 30-90 seconds avg response
    
    // Generate call trends (last 7/30 days)
    const callTrend = Array.from({ length: timeRange === "7d" ? 7 : 30 }, (_, i) => {
      const date = format(subDays(new Date(), timeRange === "7d" ? 7 - i : 30 - i), "MMM dd");
      const dayInbound = Math.floor(Math.random() * 30) + 5;
      const dayOutbound = Math.floor(Math.random() * 20) + 5;
      return {
        date,
        inbound: dayInbound,
        outbound: dayOutbound,
        total: dayInbound + dayOutbound
      };
    });
    
    // Generate message trends
    const messageTrend = Array.from({ length: timeRange === "7d" ? 7 : 30 }, (_, i) => {
      const date = format(subDays(new Date(), timeRange === "7d" ? 7 - i : 30 - i), "MMM dd");
      const dayInbound = Math.floor(Math.random() * 50) + 10;
      const dayOutbound = Math.floor(Math.random() * 30) + 5;
      return {
        date,
        inbound: dayInbound,
        outbound: dayOutbound,
        total: dayInbound + dayOutbound
      };
    });
    
    // Generate peak hours data
    const peakCallHours = Array.from({ length: 24 }, (_, hour) => {
      return {
        hour,
        count: hour >= 8 && hour <= 18 
          ? Math.floor(Math.random() * 50) + 20 
          : Math.floor(Math.random() * 15)
      };
    });
    
    // Generate top callers
    const topCallers = Array.from({ length: 5 }, (_, i) => {
      return {
        phoneNumber: `+1${Math.floor(Math.random() * 900) + 100}${Math.floor(Math.random() * 900) + 100}${Math.floor(Math.random() * 9000) + 1000}`,
        callCount: Math.floor(Math.random() * 30) + 5
      };
    });
    
    return {
      totalCalls,
      totalMessages,
      totalDuration: Math.floor(Math.random() * 300000) + 50000, // seconds
      callsByDirection: {
        inbound: inboundCalls,
        outbound: outboundCalls
      },
      messagesByDirection: {
        inbound: inboundMessages,
        outbound: outboundMessages
      },
      callTrend,
      messageTrend,
      callsByStatus: {
        completed: Math.floor(totalCalls * 0.7),
        noAnswer: noAnswerCalls,
        busy: busyCalls,
        failed: Math.floor(totalCalls * 0.05),
        canceled: Math.floor(totalCalls * 0.05)
      },
      peakCallHours,
      topCallers,
      missedCalls,
      missedCallRate,
      aiHandoffRate,
      aiHandledCalls,
      aiHandledMessages,
      smsResponseRate,
      smsResponseTime,
      organizationId: "org_123456" // Mock organization ID for tenant-specific analytics
    };
  }, [timeRange]);
  
  // Fetch data from the API
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/analytics?timeRange=${timeRange}`);
      const data = await response.json();
      setData(data);
    } catch (error) {
      console.error("Error fetching analytics data:", error);
    } finally {
      setLoading(false);
    }
  }, [timeRange]);
  
  // Fetch analytics data
  useEffect(() => {
    // In a production environment, we'd fetch from the API
    if (process.env.NODE_ENV === "production") {
      fetchData();
    } else {
      // Use mock data in development
      const mockData = generateMockData();
      setData(mockData);
      setLoading(false);
    }
  }, [fetchData, generateMockData]);

  // Get trend values
  const trends = getComparativeTrends();
  
  if (!data) {
    return <div className="p-8">Loading analytics data...</div>;
  }

  return (
    <motion.div 
      className="p-8"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Analytics Dashboard</h1>
        <div className="flex items-center space-x-2">
          <Select
            value={timeRange}
            onValueChange={(value) => setTimeRange(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button 
            variant="outline" 
            size="icon"
            onClick={() => {
              setLoading(true);
              setTimeout(() => {
                const mockData = generateMockData();
                setData(mockData);
                setLoading(false);
              }, 500);
            }}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Tabs defaultValue="calls">
        <TabsList className="mb-4">
          <TabsTrigger value="calls">Call Analytics</TabsTrigger>
          <TabsTrigger value="messages">Message Analytics</TabsTrigger>
          <TabsTrigger value="ai">AI Performance</TabsTrigger>
        </TabsList>
        
        <TabsContent value="calls" className="space-y-4">
          <div className="grid grid-cols-4 gap-4">
            <StatCard
              title="Total Calls"
              value={data.totalCalls}
              icon={<Phone className="h-4 w-4" />}
              trend={trends.calls}
              loading={loading}
            />
            <StatCard
              title="Total Duration"
              value={formatDuration(data.totalDuration)}
              icon={<Clock className="h-4 w-4" />}
              loading={loading}
            />
            <StatCard
              title="Missed Call Rate"
              value={`${data.missedCallRate}%`}
              icon={<PhoneOff className="h-4 w-4" />}
              trend={trends.missedRate}
              loading={loading}
            />
            <StatCard
              title="AI Handoff Rate"
              value={`${data.aiHandoffRate}%`}
              icon={<Bot className="h-4 w-4" />}
              trend={trends.aiHandled}
              loading={loading}
            />
          </div>
          
          <div className="grid grid-cols-3 gap-4">
            <ChartCard title="Call Trends" description="Call volume over time">
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={data.callTrend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="inbound" 
                    stroke="#8884d8" 
                    activeDot={{ r: 8 }} 
                  />
                  <Line 
                    type="monotone" 
                    dataKey="outbound" 
                    stroke="#82ca9d" 
                  />
                </LineChart>
              </ResponsiveContainer>
            </ChartCard>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Call Direction</CardTitle>
              </CardHeader>
              <CardContent className="flex justify-center">
                <div className="w-48 h-48">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={[
                          { name: 'Inbound', value: data.callsByDirection.inbound },
                          { name: 'Outbound', value: data.callsByDirection.outbound },
                        ]}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        <Cell fill="#8884d8" />
                        <Cell fill="#82ca9d" />
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card className="col-span-1">
              <CardHeader>
                <CardTitle>Call Status</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={240}>
                  <BarChart
                    layout="vertical"
                    data={[
                      { name: 'Completed', value: data.callsByStatus.completed },
                      { name: 'No Answer', value: data.callsByStatus.noAnswer },
                      { name: 'Busy', value: data.callsByStatus.busy },
                      { name: 'Failed', value: data.callsByStatus.failed },
                      { name: 'Canceled', value: data.callsByStatus.canceled },
                    ]}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="name" type="category" />
                    <Tooltip />
                    <Bar dataKey="value" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
          
          <div className="grid grid-cols-3 gap-4">
            <ChartCard title="Peak Call Hours" description="Call volume by hour of day">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={data.peakCallHours}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </ChartCard>
            
            <Card className="col-span-2">
              <CardHeader>
                <CardTitle>Top Callers</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.topCallers.map((caller, i) => (
                    <div key={i} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                          <Users className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="text-sm font-medium">{caller.phoneNumber}</p>
                          <p className="text-xs text-muted-foreground">
                            {caller.callCount} calls
                          </p>
                        </div>
                      </div>
                      <div className="h-2 w-24 bg-gray-100 rounded-full">
                        <div 
                          className="h-2 bg-primary rounded-full" 
                          style={{ 
                            width: `${Math.min(100, caller.callCount / Math.max(...data.topCallers.map(c => c.callCount)) * 100)}%` 
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="messages" className="space-y-4">
          {/* Message analytics content would go here */}
          <div className="grid grid-cols-4 gap-4">
            <StatCard
              title="Total Messages"
              value={data.totalMessages}
              icon={<MessageSquare className="h-4 w-4" />}
              trend={trends.messages}
              loading={loading}
            />
            <StatCard
              title="SMS Response Rate"
              value={`${data.smsResponseRate}%`}
              icon={<CheckCircle2 className="h-4 w-4" />}
              trend={2.5}
              loading={loading}
            />
            <StatCard
              title="Avg Response Time"
              value={`${data.smsResponseTime}s`}
              icon={<Timer className="h-4 w-4" />}
              trend={-3.2}
              loading={loading}
            />
            <StatCard
              title="AI Handled"
              value={data.aiHandledMessages}
              icon={<Bot className="h-4 w-4" />}
              trend={8.7}
              loading={loading}
            />
          </div>
        </TabsContent>
        
        <TabsContent value="ai" className="space-y-4">
          {/* AI performance content would go here */}
          <div className="grid grid-cols-4 gap-4">
            <div className="col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">AI Call Handling</CardTitle>
                </CardHeader>
                <CardContent className="flex justify-center">
                  <div className="w-36 h-36">
                    <CircularProgressbar
                      value={data.aiHandoffRate}
                      text={`${data.aiHandoffRate}%`}
                      strokeWidth={10}
                      styles={buildStyles({
                        textColor: '#333',
                        pathColor: '#8884d8',
                        trailColor: '#d6d6d6',
                        textSize: '16px'
                      })}
                    />
                  </div>
                </CardContent>
                <CardFooter className="text-center text-sm text-muted-foreground">
                  {data.aiHandledCalls} of {data.callsByDirection.inbound} inbound calls
                </CardFooter>
              </Card>
            </div>
            
            <div className="col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">AI Message Handling</CardTitle>
                </CardHeader>
                <CardContent className="flex justify-center">
                  <div className="w-36 h-36">
                    <CircularProgressbar
                      value={(data.aiHandledMessages / data.messagesByDirection.inbound * 100).toFixed(1)}
                      text={`${(data.aiHandledMessages / data.messagesByDirection.inbound * 100).toFixed(1)}%`}
                      strokeWidth={10}
                      styles={buildStyles({
                        textColor: '#333',
                        pathColor: '#82ca9d',
                        trailColor: '#d6d6d6',
                        textSize: '16px'
                      })}
                    />
                  </div>
                </CardContent>
                <CardFooter className="text-center text-sm text-muted-foreground">
                  {data.aiHandledMessages} of {data.messagesByDirection.inbound} inbound messages
                </CardFooter>
              </Card>
            </div>
            
            <div className="col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>AI Satisfaction Score</CardTitle>
                </CardHeader>
                <CardContent className="h-52 flex items-center justify-center">
                  <div className="text-center">
                    <p className="text-4xl font-bold text-primary">92%</p>
                    <p className="text-sm text-muted-foreground mt-2">Based on customer feedback</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </motion.div>
  );
}
