'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../../components/ui/tabs';
import AdminPageLayout from '../../../../components/admin/AdminPageLayout';

export default function ApiManagement() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [apiKeys, setApiKeys] = useState([]);
  const [webhooks, setWebhooks] = useState([]);
  const [rateLimits, setRateLimits] = useState({
    defaultLimit: 100,
    premiumLimit: 500,
    enterpriseLimit: 2000,
    ipBasedLimiting: true,
    userBasedLimiting: true
  });
  const [selectedApiKey, setSelectedApiKey] = useState(null);
  const [selectedWebhook, setSelectedWebhook] = useState(null);
  const [isApiKeyModalOpen, setIsApiKeyModalOpen] = useState(false);
  const [isWebhookModalOpen, setIsWebhookModalOpen] = useState(false);

  // Mock data
  const mockApiKeys = [
    { id: 1, name: 'Production API Key', key: 'cs_live_***********************************', environment: 'production', createdAt: '2023-01-15T09:20:00Z', lastUsed: '2023-05-14T14:45:00Z', status: 'active' },
    { id: 2, name: 'Development API Key', key: 'cs_dev_************************************', environment: 'development', createdAt: '2023-02-10T13:45:00Z', lastUsed: '2023-05-12T16:15:00Z', status: 'active' },
    { id: 3, name: 'Test API Key', key: 'cs_test_***********************************', environment: 'test', createdAt: '2023-03-05T11:10:00Z', lastUsed: '2023-04-30T11:20:00Z', status: 'inactive' },
  ];

  const mockWebhooks = [
    { id: 1, name: 'New Call Notification', url: 'https://example.com/webhooks/calls', events: ['call.created', 'call.completed'], secret: 'whsec_*********************************', createdAt: '2023-01-20T10:30:00Z', status: 'active' },
    { id: 2, name: 'Message Status Updates', url: 'https://example.com/webhooks/messages', events: ['message.sent', 'message.delivered', 'message.failed'], secret: 'whsec_*********************************', createdAt: '2023-02-15T14:45:00Z', status: 'active' },
    { id: 3, name: 'Billing Notifications', url: 'https://example.com/webhooks/billing', events: ['invoice.created', 'payment.succeeded', 'payment.failed'], secret: 'whsec_*********************************', createdAt: '2023-03-10T09:15:00Z', status: 'inactive' },
  ];

  useEffect(() => {
    const fetchApiData = async () => {
      try {
        setIsLoading(true);
        // In a real implementation, this would be an API call to get API keys and webhooks
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setApiKeys(mockApiKeys);
        setWebhooks(mockWebhooks);
      } catch (err) {
        console.error('Error fetching API data:', err);
        setError('Failed to load API management data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchApiData();
  }, []);

  const handleCreateApiKey = () => {
    setSelectedApiKey({
      id: null,
      name: '',
      environment: 'development',
      status: 'active'
    });
    setIsApiKeyModalOpen(true);
  };

  const handleEditApiKey = (apiKey) => {
    setSelectedApiKey(apiKey);
    setIsApiKeyModalOpen(true);
  };

  const handleDeleteApiKey = (apiKeyId) => {
    if (window.confirm('Are you sure you want to delete this API key? This action cannot be undone and may break integrations using this key.')) {
      // In a real implementation, this would be an API call to delete the API key
      console.log(`Deleting API key with ID: ${apiKeyId}`);
      // Then refetch the API keys
      setApiKeys(apiKeys.filter(key => key.id !== apiKeyId));
    }
  };

  const handleCreateWebhook = () => {
    setSelectedWebhook({
      id: null,
      name: '',
      url: '',
      events: [],
      status: 'active'
    });
    setIsWebhookModalOpen(true);
  };

  const handleEditWebhook = (webhook) => {
    setSelectedWebhook(webhook);
    setIsWebhookModalOpen(true);
  };

  const handleDeleteWebhook = (webhookId) => {
    if (window.confirm('Are you sure you want to delete this webhook? This action cannot be undone and may break integrations using this webhook.')) {
      // In a real implementation, this would be an API call to delete the webhook
      console.log(`Deleting webhook with ID: ${webhookId}`);
      // Then refetch the webhooks
      setWebhooks(webhooks.filter(webhook => webhook.id !== webhookId));
    }
  };

  const handleSaveRateLimits = async () => {
    try {
      setIsLoading(true);
      // In a real implementation, this would be an API call to save rate limits
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('Rate limits saved successfully!');
    } catch (err) {
      console.error('Error saving rate limits:', err);
      setError('Failed to save rate limits. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };

  return (
    <AdminPageLayout
      title="API Management"
      description="Manage API keys, webhooks, and rate limits."
    >
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
          <p className="text-red-200">{error}</p>
        </div>
      )}

      <Tabs defaultValue="apiKeys" className="w-full">
        <TabsList className="bg-gray-800/70 border border-purple-500/20 mb-6">
          <TabsTrigger value="apiKeys">API Keys</TabsTrigger>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
          <TabsTrigger value="rateLimits">Rate Limits</TabsTrigger>
          <TabsTrigger value="documentation">API Documentation</TabsTrigger>
        </TabsList>

        <TabsContent value="apiKeys" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-white">API Keys</h2>
            <button 
              className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
              onClick={handleCreateApiKey}
            >
              Create API Key
            </button>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead className="bg-gray-700/50 text-gray-300">
                <tr>
                  <th className="px-4 py-2 rounded-tl-lg">Name</th>
                  <th className="px-4 py-2">Key</th>
                  <th className="px-4 py-2">Environment</th>
                  <th className="px-4 py-2">Created</th>
                  <th className="px-4 py-2">Last Used</th>
                  <th className="px-4 py-2">Status</th>
                  <th className="px-4 py-2 rounded-tr-lg">Actions</th>
                </tr>
              </thead>
              <tbody className="text-gray-300">
                {isLoading ? (
                  <tr>
                    <td colSpan="7" className="px-4 py-2 text-center">Loading API keys...</td>
                  </tr>
                ) : apiKeys.length === 0 ? (
                  <tr>
                    <td colSpan="7" className="px-4 py-2 text-center">No API keys found</td>
                  </tr>
                ) : (
                  apiKeys.map((apiKey, index) => (
                    <tr key={apiKey.id} className={index % 2 === 0 ? 'bg-gray-700/30' : 'bg-gray-700/10'}>
                      <td className="px-4 py-2 font-medium">{apiKey.name}</td>
                      <td className="px-4 py-2">{apiKey.key}</td>
                      <td className="px-4 py-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          apiKey.environment === 'production' ? 'bg-purple-500/20 text-purple-300' :
                          apiKey.environment === 'development' ? 'bg-blue-500/20 text-blue-300' :
                          'bg-green-500/20 text-green-300'
                        }`}>
                          {apiKey.environment}
                        </span>
                      </td>
                      <td className="px-4 py-2">{formatDate(apiKey.createdAt)}</td>
                      <td className="px-4 py-2">{formatDate(apiKey.lastUsed)}</td>
                      <td className="px-4 py-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          apiKey.status === 'active' ? 'bg-green-500/20 text-green-300' :
                          'bg-red-500/20 text-red-300'
                        }`}>
                          {apiKey.status}
                        </span>
                      </td>
                      <td className="px-4 py-2">
                        <div className="flex space-x-2">
                          <button 
                            onClick={() => handleEditApiKey(apiKey)}
                            className="text-blue-400 hover:text-blue-300"
                          >
                            Edit
                          </button>
                          <button 
                            onClick={() => handleDeleteApiKey(apiKey.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>

        <TabsContent value="webhooks" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-white">Webhooks</h2>
            <button 
              className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
              onClick={handleCreateWebhook}
            >
              Create Webhook
            </button>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead className="bg-gray-700/50 text-gray-300">
                <tr>
                  <th className="px-4 py-2 rounded-tl-lg">Name</th>
                  <th className="px-4 py-2">URL</th>
                  <th className="px-4 py-2">Events</th>
                  <th className="px-4 py-2">Created</th>
                  <th className="px-4 py-2">Status</th>
                  <th className="px-4 py-2 rounded-tr-lg">Actions</th>
                </tr>
              </thead>
              <tbody className="text-gray-300">
                {isLoading ? (
                  <tr>
                    <td colSpan="6" className="px-4 py-2 text-center">Loading webhooks...</td>
                  </tr>
                ) : webhooks.length === 0 ? (
                  <tr>
                    <td colSpan="6" className="px-4 py-2 text-center">No webhooks found</td>
                  </tr>
                ) : (
                  webhooks.map((webhook, index) => (
                    <tr key={webhook.id} className={index % 2 === 0 ? 'bg-gray-700/30' : 'bg-gray-700/10'}>
                      <td className="px-4 py-2 font-medium">{webhook.name}</td>
                      <td className="px-4 py-2">{webhook.url}</td>
                      <td className="px-4 py-2">
                        <div className="flex flex-wrap gap-1">
                          {webhook.events.map((event, i) => (
                            <span key={i} className="px-2 py-1 rounded-full text-xs bg-blue-500/20 text-blue-300">
                              {event}
                            </span>
                          ))}
                        </div>
                      </td>
                      <td className="px-4 py-2">{formatDate(webhook.createdAt)}</td>
                      <td className="px-4 py-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          webhook.status === 'active' ? 'bg-green-500/20 text-green-300' :
                          'bg-red-500/20 text-red-300'
                        }`}>
                          {webhook.status}
                        </span>
                      </td>
                      <td className="px-4 py-2">
                        <div className="flex space-x-2">
                          <button 
                            onClick={() => handleEditWebhook(webhook)}
                            className="text-blue-400 hover:text-blue-300"
                          >
                            Edit
                          </button>
                          <button 
                            onClick={() => handleDeleteWebhook(webhook.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </TabsContent>

        <TabsContent value="rateLimits" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">API Rate Limits</h2>
          
          <div className="space-y-6">
            <div>
              <label className="text-gray-300 block mb-1">Default Rate Limit (requests per minute)</label>
              <input 
                type="number" 
                value={rateLimits.defaultLimit} 
                onChange={(e) => setRateLimits({
                  ...rateLimits,
                  defaultLimit: parseInt(e.target.value)
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Premium Rate Limit (requests per minute)</label>
              <input 
                type="number" 
                value={rateLimits.premiumLimit} 
                onChange={(e) => setRateLimits({
                  ...rateLimits,
                  premiumLimit: parseInt(e.target.value)
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div>
              <label className="text-gray-300 block mb-1">Enterprise Rate Limit (requests per minute)</label>
              <input 
                type="number" 
                value={rateLimits.enterpriseLimit} 
                onChange={(e) => setRateLimits({
                  ...rateLimits,
                  enterpriseLimit: parseInt(e.target.value)
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading}
              />
            </div>
            
            <div className="flex items-center">
              <input 
                type="checkbox" 
                id="ipBasedLimiting" 
                checked={rateLimits.ipBasedLimiting} 
                onChange={(e) => setRateLimits({
                  ...rateLimits,
                  ipBasedLimiting: e.target.checked
                })}
                className="mr-2 h-4 w-4"
                disabled={isLoading}
              />
              <label htmlFor="ipBasedLimiting" className="text-gray-300">Enable IP-based rate limiting</label>
            </div>
            
            <div className="flex items-center">
              <input 
                type="checkbox" 
                id="userBasedLimiting" 
                checked={rateLimits.userBasedLimiting} 
                onChange={(e) => setRateLimits({
                  ...rateLimits,
                  userBasedLimiting: e.target.checked
                })}
                className="mr-2 h-4 w-4"
                disabled={isLoading}
              />
              <label htmlFor="userBasedLimiting" className="text-gray-300">Enable user-based rate limiting</label>
            </div>
            
            <div className="flex justify-end">
              <button 
                onClick={handleSaveRateLimits}
                className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
                disabled={isLoading}
              >
                Save Rate Limits
              </button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="documentation" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">API Documentation</h2>
          
          <div className="space-y-6">
            <p className="text-gray-300">
              Manage your API documentation settings and access the API reference.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="bg-gray-700/50 border border-gray-600/50">
                <CardHeader>
                  <CardTitle className="text-white">API Reference</CardTitle>
                  <CardDescription className="text-gray-400">Access the complete API reference documentation</CardDescription>
                </CardHeader>
                <CardContent>
                  <a 
                    href="/api/docs" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors inline-block"
                  >
                    View API Reference
                  </a>
                </CardContent>
              </Card>
              
              <Card className="bg-gray-700/50 border border-gray-600/50">
                <CardHeader>
                  <CardTitle className="text-white">OpenAPI Specification</CardTitle>
                  <CardDescription className="text-gray-400">Download the OpenAPI specification file</CardDescription>
                </CardHeader>
                <CardContent>
                  <a 
                    href="/api/openapi.json" 
                    download
                    className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors inline-block"
                  >
                    Download OpenAPI Spec
                  </a>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* API Key Modal */}
      {isApiKeyModalOpen && selectedApiKey && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold text-white mb-4">
              {selectedApiKey.id ? 'Edit API Key' : 'Create API Key'}
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className="text-gray-300 block mb-1">Name</label>
                <input 
                  type="text" 
                  value={selectedApiKey.name} 
                  onChange={(e) => setSelectedApiKey({...selectedApiKey, name: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                />
              </div>
              
              <div>
                <label className="text-gray-300 block mb-1">Environment</label>
                <select 
                  value={selectedApiKey.environment} 
                  onChange={(e) => setSelectedApiKey({...selectedApiKey, environment: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                >
                  <option value="development">Development</option>
                  <option value="test">Test</option>
                  <option value="production">Production</option>
                </select>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="apiKeyStatus" 
                  checked={selectedApiKey.status === 'active'} 
                  onChange={(e) => setSelectedApiKey({
                    ...selectedApiKey, 
                    status: e.target.checked ? 'active' : 'inactive'
                  })}
                  className="mr-2 h-4 w-4"
                />
                <label htmlFor="apiKeyStatus" className="text-gray-300">Active</label>
              </div>
            </div>
            
            <div className="flex justify-end mt-6 space-x-2">
              <button 
                onClick={() => setIsApiKeyModalOpen(false)}
                className="bg-gray-700 text-white px-4 py-2 rounded-lg"
              >
                Cancel
              </button>
              <button 
                onClick={() => {
                  // In a real implementation, this would be an API call to save the API key
                  console.log('Saving API key:', selectedApiKey);
                  setIsApiKeyModalOpen(false);
                  
                  // Update the API key list
                  if (selectedApiKey.id) {
                    setApiKeys(apiKeys.map(key => 
                      key.id === selectedApiKey.id ? selectedApiKey : key
                    ));
                  } else {
                    // Add new API key with a mock ID and key
                    const newApiKey = {
                      ...selectedApiKey,
                      id: Math.max(...apiKeys.map(k => k.id), 0) + 1,
                      key: `cs_${selectedApiKey.environment.substring(0, 4)}_${Array(35).fill(0).map(() => '*').join('')}`,
                      createdAt: new Date().toISOString(),
                      lastUsed: new Date().toISOString()
                    };
                    setApiKeys([...apiKeys, newApiKey]);
                  }
                }}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Webhook Modal */}
      {isWebhookModalOpen && selectedWebhook && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold text-white mb-4">
              {selectedWebhook.id ? 'Edit Webhook' : 'Create Webhook'}
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className="text-gray-300 block mb-1">Name</label>
                <input 
                  type="text" 
                  value={selectedWebhook.name} 
                  onChange={(e) => setSelectedWebhook({...selectedWebhook, name: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                />
              </div>
              
              <div>
                <label className="text-gray-300 block mb-1">URL</label>
                <input 
                  type="url" 
                  value={selectedWebhook.url} 
                  onChange={(e) => setSelectedWebhook({...selectedWebhook, url: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                />
              </div>
              
              <div>
                <label className="text-gray-300 block mb-1">Events</label>
                <div className="space-y-2">
                  {['call.created', 'call.completed', 'message.sent', 'message.delivered', 'message.failed', 'invoice.created', 'payment.succeeded', 'payment.failed'].map((event) => (
                    <div key={event} className="flex items-center">
                      <input 
                        type="checkbox" 
                        id={`event-${event}`} 
                        checked={selectedWebhook.events.includes(event)} 
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedWebhook({
                              ...selectedWebhook,
                              events: [...selectedWebhook.events, event]
                            });
                          } else {
                            setSelectedWebhook({
                              ...selectedWebhook,
                              events: selectedWebhook.events.filter(e => e !== event)
                            });
                          }
                        }}
                        className="mr-2 h-4 w-4"
                      />
                      <label htmlFor={`event-${event}`} className="text-gray-300">{event}</label>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex items-center">
                <input 
                  type="checkbox" 
                  id="webhookStatus" 
                  checked={selectedWebhook.status === 'active'} 
                  onChange={(e) => setSelectedWebhook({
                    ...selectedWebhook, 
                    status: e.target.checked ? 'active' : 'inactive'
                  })}
                  className="mr-2 h-4 w-4"
                />
                <label htmlFor="webhookStatus" className="text-gray-300">Active</label>
              </div>
            </div>
            
            <div className="flex justify-end mt-6 space-x-2">
              <button 
                onClick={() => setIsWebhookModalOpen(false)}
                className="bg-gray-700 text-white px-4 py-2 rounded-lg"
              >
                Cancel
              </button>
              <button 
                onClick={() => {
                  // In a real implementation, this would be an API call to save the webhook
                  console.log('Saving webhook:', selectedWebhook);
                  setIsWebhookModalOpen(false);
                  
                  // Update the webhook list
                  if (selectedWebhook.id) {
                    setWebhooks(webhooks.map(webhook => 
                      webhook.id === selectedWebhook.id ? selectedWebhook : webhook
                    ));
                  } else {
                    // Add new webhook with a mock ID and secret
                    const newWebhook = {
                      ...selectedWebhook,
                      id: Math.max(...webhooks.map(w => w.id), 0) + 1,
                      secret: `whsec_${Array(33).fill(0).map(() => '*').join('')}`,
                      createdAt: new Date().toISOString()
                    };
                    setWebhooks([...webhooks, newWebhook]);
                  }
                }}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </AdminPageLayout>
  );
}
