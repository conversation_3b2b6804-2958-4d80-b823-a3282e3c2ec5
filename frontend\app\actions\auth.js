'use server';

import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { headers } from 'next/headers';

// Get session server-side
export async function getSession() {
  const cookieStore = cookies();

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        get: (name) => cookieStore.get(name)?.value,
        set: () => {}, // We don't need to set in this case
        remove: () => {}, // We don't need to remove in this case
      },
    }
  );

  return supabase.auth.getSession();
}

// Sign up with email/password
export async function signUp({ email, password, name }) {
  const cookieStore = cookies();
  const headersList = headers();
  const origin = headersList.get('origin') || process.env.APP_URL || 'http://localhost:3000';

  // Basic validation
  if (!email || !password) {
    return {
      success: false,
      message: 'Email and password are required'
    };
  }

  if (password.length < 8) {
    return {
      success: false,
      message: 'Password must be at least 8 characters long'
    };
  }

  try {
    console.log(`Attempting sign up for email: ${email}, password: ${'*'.repeat(password?.length || 0)}`);

    // Create the server client with cookie handling for server actions
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          get: (name) => {
            const cookie = cookieStore.get(name);
            return cookie?.value;
          },
          set: (name, value, options) => {
            const isProduction = process.env.NODE_ENV === 'production';
            const maxAge = options?.maxAge || 60 * 60 * 24 * 7; // Default 7 days

            cookieStore.set({
              name,
              value,
              ...options,
              secure: isProduction,
              maxAge,
              path: options?.path || '/'
            });
          },
          remove: (name, options) => {
            cookieStore.set({
              name,
              value: '',
              maxAge: -1,
              ...options,
              path: options?.path || '/'
            });
          },
        },
      }
    );

    // Construct the email redirect URL for confirmation
    const emailRedirectTo = `${origin}/dashboard`;
    console.log(`Email redirect URL set to: ${emailRedirectTo}`);

    // Attempt sign up
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo,
        data: {
          name: name || email.split('@')[0], // Use name if provided, otherwise use part of email
        }
      }
    });

    if (error) {
      console.error('Supabase auth error during sign up:', error);
      return {
        success: false,
        message: error.message
      };
    }

    // Handle different scenarios
    if (data.user && !data.session) {
      console.log('Sign up successful, email confirmation required');
      return {
        success: true,
        needsEmailConfirmation: true,
        message: 'Sign up successful. Please check your email for confirmation.'
      };
    } else if (data.user && data.session) {
      console.log('Sign up successful, user created and logged in immediately');
      return {
        success: true,
        needsEmailConfirmation: false,
        message: 'Account created successfully. You are now logged in.'
      };
    } else {
      console.warn('Unexpected response format from Supabase sign up');
      return {
        success: false,
        message: 'An unexpected error occurred during sign up'
      };
    }
  } catch (err) {
    console.error('Unexpected error during sign up:', err);
    return {
      success: false,
      message: 'An unexpected error occurred during sign up'
    };
  }
}

// Sign in with email/password
export async function signIn(email, password) {
  const cookieStore = cookies();

  try {
    // Log the input parameters (mask password for security)
    console.log(`Attempting sign in for email: ${email}, password: ${'*'.repeat(password?.length || 0)}`);
    console.log('Environment check - NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
    console.log('Environment check - NEXT_PUBLIC_SUPABASE_ANON_KEY available:', !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);

    // Create the server client with improved cookie handling
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          get: (name) => {
            const cookie = cookieStore.get(name);
            console.log(`Cookie get: ${name}`, cookie ? 'exists' : 'not found');
            return cookie?.value;
          },
          set: (name, value, options) => {
            // Ensure the secure flag is set according to environment
            // For local development, secure should typically be false
            const isProduction = process.env.NODE_ENV === 'production';
            const maxAge = options?.maxAge || 60 * 60 * 24 * 7; // Default 7 days

            console.log(`Setting cookie: ${name}, maxAge: ${maxAge}, secure: ${isProduction}`);

            try {
              cookieStore.set({
                name,
                value,
                ...options,
                secure: isProduction,
                maxAge,
                path: options?.path || '/'
              });
            } catch (error) {
              console.error(`Error setting cookie ${name}:`, error);
            }
          },
          remove: (name, options) => {
            console.log(`Removing cookie: ${name}`);
            try {
              cookieStore.set({
                name,
                value: '',
                maxAge: -1,
                ...options,
                path: options?.path || '/'
              });
            } catch (error) {
              console.error(`Error removing cookie ${name}:`, error);
            }
          },
        },
      }
    );

    // Log authentication attempt
    console.log('Supabase client created, attempting signInWithPassword');

    // Attempt sign in
    const result = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    // Log the result (exclude sensitive data)
    if (result.error) {
      console.error('Supabase auth error:', result.error);
      // Return a serializable error object
      return {
        success: false,
        error: {
          message: result.error.message || 'Authentication failed'
        }
      };
    } else {
      console.log('Auth successful, session established');
      // Log session details (excluding sensitive data)
      const { data: { session } } = await supabase.auth.getSession();
      console.log('Session user ID:', session?.user?.id);
      console.log('Session expires at:', session?.expires_at);

      // Ensure cookies are properly set for the session
      if (session) {
        // Force refresh the session to ensure cookies are properly set
        await supabase.auth.refreshSession();
      }

      // Return a serializable success object
      return {
        success: true,
        user: {
          id: session?.user?.id,
          email: session?.user?.email,
          name: session?.user?.user_metadata?.name || '',
        }
      };
    }
  } catch (err) {
    console.error('Unexpected error during sign in:', err);
    // Return a serializable error object
    return {
      success: false,
      error: {
        message: 'An unexpected error occurred during sign in'
      }
    };
  }
}

// Sign in with Google OAuth
export async function signInWithGoogle() {
  const cookieStore = cookies();
  const headersList = headers();
  const origin = headersList.get('origin') || process.env.APP_URL || 'http://localhost:3000';

  try {
    console.log('Attempting sign in with Google OAuth');

    // Create the server client with cookie handling
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          get: (name) => {
            const cookie = cookieStore.get(name);
            return cookie?.value;
          },
          set: (name, value, options) => {
            const isProduction = process.env.NODE_ENV === 'production';
            const maxAge = options?.maxAge || 60 * 60 * 24 * 7; // Default 7 days

            cookieStore.set({
              name,
              value,
              ...options,
              secure: isProduction,
              maxAge,
              path: options?.path || '/'
            });
          },
          remove: (name, options) => {
            cookieStore.set({
              name,
              value: '',
              maxAge: -1,
              ...options,
              path: options?.path || '/'
            });
          },
        },
      }
    );

    // Construct the redirect URL
    const redirectTo = `${origin}/auth/callback`;
    console.log(`Google OAuth redirect URL set to: ${redirectTo}`);

    // Initiate Google OAuth sign in
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    });

    if (error) {
      console.error('Supabase auth error during Google sign in:', error);
      return {
        success: false,
        error: {
          message: error.message || 'Authentication with Google failed'
        }
      };
    }

    // Return the URL to redirect to
    return {
      success: true,
      url: data.url
    };
  } catch (err) {
    console.error('Unexpected error during Google sign in:', err);
    return {
      success: false,
      error: {
        message: 'An unexpected error occurred during Google sign in'
      }
    };
  }
}

// Sign out
export async function signOut() {
  const cookieStore = cookies();

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        get: (name) => cookieStore.get(name)?.value,
        set: (name, value, options) => {
          cookieStore.set({ name, value, ...options });
        },
        remove: (name, options) => {
          cookieStore.set({ name, value: '', ...options });
        },
      },
    }
  );

  return supabase.auth.signOut();
}