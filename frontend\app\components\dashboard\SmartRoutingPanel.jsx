"use client";

import { useState } from 'react';
import { 
  ArrowsRightLeftIcon, 
  CheckCircleIcon,
  XCircleIcon,
  BellAlertIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

export default function SmartRoutingPanel() {
  const [isEnabled, setIsEnabled] = useState(true);
  const [escalationThreshold, setEscalationThreshold] = useState(0.7);
  const [autoEscalateNegative, setAutoEscalateNegative] = useState(true);
  const [notifyBySms, setNotifyBySms] = useState(true);
  const [notifyByEmail, setNotifyByEmail] = useState(true);
  const [notifyInDashboard, setNotifyInDashboard] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState(null);

  const handleSave = async () => {
    setIsSaving(true);
    setSaveSuccess(false);
    setSaveError(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate successful save
      setSaveSuccess(true);
      
      // Reset success message after 3 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      setSaveError('Failed to save smart routing settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-white flex items-center">
          <ArrowsRightLeftIcon className="h-5 w-5 mr-2 text-purple-400" />
          Smart Routing
        </h3>
        <div className="flex items-center">
          <span className="text-sm text-gray-400 mr-2">Smart Routing</span>
          <button 
            onClick={() => setIsEnabled(!isEnabled)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 ${
              isEnabled ? 'bg-purple-600' : 'bg-gray-700'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                isEnabled ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>
      </div>

      {saveSuccess && (
        <div className="mb-4 p-3 bg-green-500/20 border border-green-500/30 rounded-lg">
          <p className="text-green-300 text-sm flex items-center">
            <CheckCircleIcon className="h-4 w-4 mr-2" />
            Smart routing settings saved successfully
          </p>
        </div>
      )}

      {saveError && (
        <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
          <p className="text-red-300 text-sm flex items-center">
            <XCircleIcon className="h-4 w-4 mr-2" />
            {saveError}
          </p>
        </div>
      )}

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-400 mb-1">
            Escalation Threshold
          </label>
          <div className="flex items-center">
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={escalationThreshold}
              onChange={(e) => setEscalationThreshold(parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              disabled={!isEnabled}
            />
            <span className="ml-2 text-white font-medium w-10 text-center">
              {escalationThreshold.toFixed(1)}
            </span>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Higher values require more confidence before escalation
          </p>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <label className="text-sm font-medium text-gray-400">
              Auto-Escalate Negative Sentiment
            </label>
            <p className="text-xs text-gray-500 mt-1">
              Automatically escalate conversations with negative sentiment
            </p>
          </div>
          <button 
            onClick={() => setAutoEscalateNegative(!autoEscalateNegative)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 ${
              autoEscalateNegative ? 'bg-purple-600' : 'bg-gray-700'
            }`}
            disabled={!isEnabled}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                autoEscalateNegative ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
        </div>

        <div className="p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg mb-4">
          <div className="flex items-start">
            <InformationCircleIcon className="h-5 w-5 text-blue-400 mr-2 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-blue-300">
              Smart routing detects when human intervention is needed and creates notifications for urgent or complex queries.
            </p>
          </div>
        </div>

        <div className="border-t border-gray-800 pt-4">
          <h4 className="text-sm font-medium text-white flex items-center mb-3">
            <BellAlertIcon className="h-4 w-4 mr-2 text-purple-400" />
            Notification Preferences
          </h4>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-400">
                  SMS Notifications
                </label>
                <p className="text-xs text-gray-500 mt-1">
                  Receive text messages for escalations
                </p>
              </div>
              <button 
                onClick={() => setNotifyBySms(!notifyBySms)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 ${
                  notifyBySms ? 'bg-purple-600' : 'bg-gray-700'
                }`}
                disabled={!isEnabled}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    notifyBySms ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-400">
                  Email Notifications
                </label>
                <p className="text-xs text-gray-500 mt-1">
                  Receive emails for escalations
                </p>
              </div>
              <button 
                onClick={() => setNotifyByEmail(!notifyByEmail)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 ${
                  notifyByEmail ? 'bg-purple-600' : 'bg-gray-700'
                }`}
                disabled={!isEnabled}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    notifyByEmail ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-400">
                  Dashboard Notifications
                </label>
                <p className="text-xs text-gray-500 mt-1">
                  Show notifications in dashboard
                </p>
              </div>
              <button 
                onClick={() => setNotifyInDashboard(!notifyInDashboard)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 ${
                  notifyInDashboard ? 'bg-purple-600' : 'bg-gray-700'
                }`}
                disabled={!isEnabled}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    notifyInDashboard ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        </div>

        <button
          onClick={handleSave}
          disabled={isSaving || !isEnabled}
          className={`w-full py-2 ${
            isSaving 
              ? 'bg-purple-600/50 cursor-wait' 
              : isEnabled 
                ? 'bg-purple-600 hover:bg-purple-700 cursor-pointer' 
                : 'bg-gray-700 cursor-not-allowed'
          } text-white font-medium rounded-lg transition-colors flex items-center justify-center`}
        >
          {isSaving ? (
            <>
              <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
              Saving...
            </>
          ) : (
            'Save Settings'
          )}
        </button>
      </div>
    </div>
  );
}
