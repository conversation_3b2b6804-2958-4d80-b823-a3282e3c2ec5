"use client";

import React from 'react';
import { 
  QueryClient, 
  QueryClientProvider, 
  QueryCache, 
  MutationCache 
} from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: true,
      refetchOnMount: true,
      refetchOnReconnect: true,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
  queryCache: new QueryCache({
    onError: (error, query) => {
      // Log errors globally but don't show a toast for all errors
      console.error(`Query Error:`, error, query);
    },
  }),
  mutationCache: new MutationCache({
    onError: (error, variables, context, mutation) => {
      // Log mutation errors globally
      console.error(`Mutation Error:`, error, variables);
    },
  }),
});

interface QueryProviderProps {
  children: React.ReactNode;
}

/**
 * React Query Provider 
 * Wraps the application to provide React Query functionality
 */
export function QueryProvider({ children }: QueryProviderProps) {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV !== 'production' && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
}

// Export the QueryClient to allow manual interaction with the cache
export { queryClient };
