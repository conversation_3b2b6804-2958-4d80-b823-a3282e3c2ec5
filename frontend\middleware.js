import { createServerClient } from '@supabase/ssr';
import { NextResponse } from 'next/server';

// Middleware to handle Supabase auth and security headers
export async function middleware(req) {
  try {
    // Create a new response object
    const res = NextResponse.next();

    // Add security headers
    const securityHeaders = {
      'X-DNS-Prefetch-Control': 'on',
      'X-XSS-Protection': '1; mode=block',
      'X-Frame-Options': 'SAMEORIGIN',
      'X-Content-Type-Options': 'nosniff',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=(self), interest-cohort=()',
    };

    // Add security headers to the response
    Object.entries(securityHeaders).forEach(([key, value]) => {
      res.headers.set(key, value);
    });

    // Create a Supabase client specifically for the middleware
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        cookies: {
          get: (name) => {
            const cookie = req.cookies.get(name);
            return cookie?.value;
          },
          set: (name, value, options) => {
            // Ensure proper cookie options, especially for secure environments
            const isProduction = process.env.NODE_ENV === 'production';
            
            res.cookies.set({
              name,
              value,
              ...options,
              secure: isProduction,
              path: options?.path || '/',
              sameSite: options?.sameSite || 'lax',
              maxAge: options?.maxAge || 60 * 60 * 24 * 7, // Default to 7 days if not specified
            });
          },
          remove: (name, options) => {
            res.cookies.set({
              name,
              value: '',
              maxAge: -1,
              ...options,
              path: options?.path || '/',
            });
          },
        },
      }
    );

    // Check if this is a just_signed_in redirection
    const isPostSignIn = req.nextUrl.searchParams.get('just_signed_in') === 'true';
    
    // Handle session refresh for post-sign-in requests to ensure session is properly established
    if (isPostSignIn) {
      try {
        await supabase.auth.refreshSession();
        console.log('Refreshed session after sign in');
      } catch (refreshError) {
        console.error('Error refreshing session after sign in:', refreshError);
      }
    } else {
      // For regular requests, try to refresh the session if needed
      try {
        await supabase.auth.refreshSession();
      } catch (refreshError) {
        console.error('Regular session refresh error:', refreshError);
      }
    }

    // Now get the session data (which might have been updated by refreshSession)
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Error getting session in middleware:', error);
    }

    // Debug log for auth issues
    if (req.nextUrl.pathname.startsWith('/dashboard')) {
      console.log('Dashboard access attempt, session exists:', !!data?.session);
    }

    // Check if the requested path is a protected route (dashboard)
    if (req.nextUrl.pathname.startsWith('/dashboard')) {
      // If user is not authenticated, redirect to sign-in page
      if (!data?.session) {
        console.log('Unauthorized dashboard access attempt - redirecting to sign-in');
        return NextResponse.redirect(new URL('/signin', req.url));
      }
    }

    // For all other routes, return the next response with cookies
    return res;
  } catch (error) {
    // Log any errors but don't block the request
    console.error('Error in auth middleware:', error);
    // Return the next response
    return NextResponse.next();
  }
}

// Specify which routes the middleware should run on
export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)'],
};