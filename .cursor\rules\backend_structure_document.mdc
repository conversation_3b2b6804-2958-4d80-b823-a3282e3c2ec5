---
description: 
globs: 
alwaysApply: false
---
---
description: Comprehensive backend architecture and organization guidelines for CallSaver
globs: ["back/backend/**/*.js", "back/backend/**/*.ts"]
alwaysApply: true
version: 1.0.0
---

# Backend Structure Document

## 1. Architecture Overview

### 1.1 Core Principles

- **Modular Monolith:** Organized with clear boundaries between functional domains
- **Service-Oriented Design:** Business logic encapsulated in well-defined service modules
- **Clean Architecture:** Separation of concerns with distinct layers (controllers, services, data access)
- **API-First Design:** Well-documented, versioned, and consistent REST APIs
- **Scalability:** Horizontally scalable components with stateless request handling
- **Error Handling:** Comprehensive error management across all application layers
- **Security:** Defense-in-depth approach with multiple security controls

### 1.2 System Components Diagram

```
┌─────────────────────────────────────┐
│            API Layer                │
│  ┌─────────────┐   ┌─────────────┐  │
│  │   Routes    │──▶│ Controllers │  │
│  └─────────────┘   └──────┬──────┘  │
└────────────────────────────│────────┘
                             │
                             ▼
┌─────────────────────────────────────┐
│          Service Layer              │
│  ┌─────────────┐   ┌─────────────┐  │
│  │  Business   │   │  External   │  │
│  │   Logic     │◀──▶│   APIs     │  │
│  └──────┬──────┘   └─────────────┘  │
└─────────│───────────────────────────┘
          │
          ▼
┌─────────────────────────────────────┐
│          Data Access Layer          │
│  ┌─────────────┐   ┌─────────────┐  │
│  │   Models    │◀──▶│ Repositories│  │
│  └─────────────┘   └─────────────┘  │
└─────────────────────────────────────┘
          │
          ▼
┌─────────────────────────────────────┐
│          Data Storage               │
│  ┌─────────────┐   ┌─────────────┐  │
│  │ PostgreSQL  │   │  MongoDB    │  │
│  │  (Supabase) │   │  (Railway)  │  │
│  └─────────────┘   └─────────────┘  │
└─────────────────────────────────────┘
```

## 2. Directory Structure

### 2.1 Primary Organization

```
/back/backend/
├── server.js              # Application entry point
├── config/                # Configuration management
├── controllers/           # Request handlers, input validation
├── routes/                # API endpoint definitions
├── services/              # Business logic implementation
├── models/                # Data models and schemas
├── middleware/            # Custom middleware functions
├── utils/                 # Shared utility functions
├── esim/                  # eSIM integration components
├── tests/                 # Test suite
├── scripts/               # Maintenance and deployment scripts
└── docs/                  # Internal documentation
```

### 2.2 Detailed Breakdown

#### 2.2.1 `/config`
- `index.js` - Centralized configuration export
- `db.js` - Database connection configuration
- `services.js` - External service credentials and endpoints
- `logging.js` - Logging configuration

#### 2.2.2 `/controllers`
- Domain-specific controller modules:
  - `authController.js` - Authentication operations 
  - `userController.js` - User management operations
  - `numberController.js` - Phone number management
  - `webhooks/` - Webhook handlers (e.g., `smsWebhookController.js`, `callWebhookController.js`)
  - `automationController.js` - Call/SMS handling automation rules

#### 2.2.3 `/routes`
- API route definitions organized by domain:
  - `authRoutes.js` - Authentication endpoints
  - `userRoutes.js` - User management endpoints
  - `numberRoutes.js` - Number management endpoints
  - `webhookRoutes.js` - Webhook endpoints
  - `automationRoutes.js` - Automation configuration endpoints

#### 2.2.4 `/services`
- Business logic modules with single responsibility:
  - `twilioClientService.js` - Twilio API interactions
  - `authService.js` - Authentication business logic
  - `userService.js` - User operations
  - `aiService.js` - AI model interactions
  - `creditService.js` - Subscription and credit management
  - `vectorDbService.js` - Vector database operations
  - `notificationService.js` - User notifications
  - `escalationSettingsService.js` - Escalation configuration

#### 2.2.5 `/models`
- Data models with schema definitions:
  - Prisma schema files for PostgreSQL
  - Mongoose schema files for MongoDB
  - Type definitions (if using TypeScript)

#### 2.2.6 `/middleware`
- Reusable middleware functions:
  - `authMiddleware.js` - JWT validation and user authentication
  - `validationMiddleware.js` - Request data validation
  - `schemaValidation.js` - JSON schema validation
  - `errorMiddleware.js` - Error handling and formatting
  - `twilioWebhookAuthMiddleware.js` - Twilio webhook authentication

#### 2.2.7 `/utils`
- Shared utility functions:
  - `errorTracker.js` - Error logging and monitoring
  - `validation.js` - Input validation helpers
  - `formatter.js` - Response formatting utilities
  - `encryption.js` - Data encryption utilities
  - `caching.js` - Caching utilities

#### 2.2.8 `/esim` (Enhanced Rules)
*   **Purpose:** Contains all components related to eSIM integration, provisioning, and management.
*   **Provider Abstraction:** All interactions with specific eSIM provider APIs (e.g., Airalo) MUST go through an abstraction layer (`esimService.js`, `providers/`). This layer should define a common interface, allowing for easier addition or switching of providers. Implement the Adapter pattern.
*   **Core Components:**
    *   `providers/`: Provider-specific implementations (e.g., `airaloProvider.js`, `mockProvider.js`). Each provider implements the standard interface defined by the abstraction layer.
    *   `esimService.js`: The core service orchestrating eSIM operations, using the provider abstraction layer.
    *   `controllers/esimController.js`: Handles API requests related to eSIMs.
    *   `routes/esimRoutes.js`: Defines API endpoints for eSIM operations.
    *   `models/esimProfile.js`: Data model definition (if not solely relying on Prisma schema).
    *   `webrtcService.js`: Handles related WebRTC signaling/setup if voice is tightly coupled.
*   **State Machine:** Model the eSIM lifecycle (e.g., `REQUESTED`, `PROVISIONING`, `ACTIVE`, `INACTIVE`, `DELETED`) as a formal state machine within `esimService.js` or dedicated state management logic. Document all states and valid transitions. Implement recovery paths for failed transitions.
*   **Activation Workflow:** Document and implement the complete eSIM activation workflow, including QR code generation/delivery, device scanning, activation confirmation polling/webhooks, and error handling.
*   **Security:** Adhere to strict security standards for handling provider API keys and sensitive profile data (see Section 5). Use secure storage and encryption.
*   **Fallback Mechanisms:** Implement explicit fallback logic for critical operations (e.g., provisioning failure, activation timeout).
*   **Testing:** Include comprehensive tests for the abstraction layer, individual providers (using mocks), state transitions, and activation workflows. Test activation on actual devices regularly.
*   **Roaming & Network Selection:** Document expected roaming behavior and any logic for automatic network selection based on provider capabilities.
*   **Data Package Management:** Implement clear logic for managing data packages, including activation, usage tracking, and warnings/notifications for depletion.
*   **Multi-Device/Profile Support:** Design data models and services considering potential future support for multiple eSIM profiles per user or multiple devices.

## 3. Coding Standards

### 3.1 File & Module Organization

1. **File Naming**
   - Use camelCase for files (e.g., `userService.js`)
   - Use PascalCase for class files (e.g., `UserModel.js`)
   - Use descriptive names that clearly indicate purpose

2. **Module Structure**
   - Begin each module with imports, grouped and ordered:
     ```javascript
     // Built-in Node.js modules
     const fs = require('fs');
     const path = require('path');
     
     // External dependencies
     const express = require('express');
     const { v4: uuidv4 } = require('uuid');
     
     // Internal modules - services, utils, etc.
     const userService = require('../services/userService');
     const { validateUser } = require('../utils/validation');
     ```
   
   - Export pattern for modules:
     ```javascript
     // Option 1: Service object pattern
     const userService = {
       createUser: async (userData) => { /* ... */ },
       updateUser: async (userId, updates) => { /* ... */ },
       // More methods...
     };
     
     module.exports = userService;
     
     // Option 2: Class pattern (when stateful behavior required)
     class TwilioClientService {
       constructor(config) {
         this.client = new Twilio(config.accountSid, config.authToken);
       }
       
       async sendSms(to, body) { /* ... */ }
       // More methods...
     }
     
     module.exports = TwilioClientService;
     ```

### 3.2 Code Style & Conventions

1. **Variable Naming**
   - Use camelCase for variables and function names
   - Use PascalCase for classes and constructors
   - Use UPPER_SNAKE_CASE for constants

2. **Function Structure**
   - Prefer async/await over Promise chains
   - Keep functions focused on a single responsibility
   - Maximum function length: 30 lines (aim for < 20)
   - Document complex functions with JSDoc comments

3. **Error Handling**
   - Use try/catch blocks for all async operations
   - Implement centralized error handling middleware
   - Use custom error classes with appropriate HTTP status codes
   - Log errors with sufficient context for debugging

4. **Asynchronous Patterns**
   ```javascript
   // Preferred pattern
   async function getUserData(userId) {
     try {
       const user = await userService.findById(userId);
       if (!user) {
         throw new NotFoundError('User not found');
       }
       return user;
     } catch (error) {
       logger.error(`Error fetching user ${userId}:`, error);
       throw error; // Re-throw for middleware handling
     }
   }
   ```

### 3.3 Documentation Requirements

1. **JSDoc Comments**
   - All public functions must have JSDoc comments
   - Include parameter descriptions and return values
   - Document potential errors/exceptions
   
   ```javascript
   /**
    * Creates a new user in the system
    * 
    * @param {Object} userData - User information
    * @param {string} userData.email - User's email address
    * @param {string} userData.password - User's password (will be hashed)
    * @param {string} [userData.name] - User's full name (optional)
    * @returns {Promise<Object>} Created user object (without password)
    * @throws {ValidationError} If user data is invalid
    * @throws {DuplicateError} If email already exists
    */
   async function createUser(userData) {
     // Implementation...
   }
   ```

2. **API Documentation**
   - Maintain OpenAPI/Swagger specifications for all endpoints
   - Include request/response examples
   - Document all possible response codes

## 4. Database Interaction

### 4.1 PostgreSQL (via Prisma)

1. **Schema Definition**
   - Define all models in `prisma/schema.prisma`
   - Use explicit field types and relationships
   - Include comprehensive documentation comments
   - Add appropriate indexes for query optimization

2. **Query Patterns**
   - Use Prisma Client for all database operations
   - Implement transactions for multi-step operations
   - Apply pagination for list queries
   - Use appropriate filters and selects to minimize data transfer

3. **Example Usage**
   ```javascript
   // Service layer example
   async function updateUserSettings(userId, settings) {
     try {
       return await prisma.$transaction(async (tx) => {
         // Verify user exists
         const user = await tx.user.findUnique({
           where: { id: userId }
         });
         
         if (!user) {
           throw new NotFoundError('User not found');
         }
         
         // Update settings
         return tx.userSettings.upsert({
           where: { userId },
           update: settings,
           create: { userId, ...settings }
         });
       });
     } catch (error) {
       handleDatabaseError(error); // Centralized error handler
     }
   }
   ```

### 4.2 MongoDB (via Mongoose)

1. **Schema Definition**
   - Define schemas in dedicated model files
   - Include validation rules directly in schema
   - Apply appropriate indexes

2. **Query Patterns**
   - Use lean() for read-only queries
   - Implement appropriate projection to minimize data transfer
   - Use aggregation pipeline for complex queries

## 5. API Design Principles

### 5.1 Endpoint Structure

1. **URL Design**
   - Use resource-based URLs (`/users`, `/numbers`)
   - Apply consistent REST patterns
   - Include API versioning (`/api/v1/users`)

2. **HTTP Methods**
   - GET: Retrieve resources
   - POST: Create resources
   - PUT: Update (replace) resources
   - PATCH: Partial updates
   - DELETE: Remove resources

3. **Status Codes**
   - 200: Success (GET, PUT, PATCH)
   - 201: Created (POST)
   - 204: No Content (DELETE)
   - 400: Bad Request (validation errors)
   - 401: Unauthorized (authentication required)
   - 403: Forbidden (insufficient permissions)
   - 404: Not Found
   - 500: Internal Server Error

### 5.2 Request/Response Format

1. **Request Format**
   ```json
   {
     "data": {
       "property1": "value1",
       "property2": "value2"
     }
   }
   ```

2. **Response Format**
   ```json
   {
     "success": true,
     "data": {
       "id": "123",
       "property1": "value1",
       "property2": "value2",
       "createdAt": "2025-04-13T18:30:00Z"
     },
     "meta": {
       "processingTime": "120ms"
     }
   }
   ```

3. **Error Response Format**
   ```json
   {
     "success": false,
     "error": {
       "code": "VALIDATION_ERROR",
       "message": "Invalid input data",
       "details": [
         {
           "field": "email",
           "message": "Must be a valid email address"
         }
       ]
     }
   }
   ```

### 5.3 Validation

1. **Request Validation**
   - Validate all incoming requests against defined schemas
   - Use dedicated middleware for validation
   - Return detailed validation errors

2. **Example Schema**
   ```javascript
   const createUserSchema = {
     type: 'object',
     required: ['data'],
     properties: {
       data: {
         type: 'object',
         required: ['email', 'password'],
         properties: {
           email: {
             type: 'string',
             format: 'email'
           },
           password: {
             type: 'string',
             minLength: 8
           },
           name: {
             type: 'string'
           }
         }
       }
     }
   };
   ```

## 6. Security Guidelines

### 6.1 Authentication & Authorization

1. **JWT Implementation**
   - Generate secure tokens with appropriate expiration
   - Include minimal necessary claims
   - Validate tokens on every protected request
   - Implement refresh token rotation

2. **Authorization Model**
   - Role-based access control for system features
   - Resource-based permissions for user data
   - Validate permissions for all operations

### 6.2 Data Protection

1. **Encryption Requirements**
   - Encrypt sensitive data at rest (e.g., API keys, credentials)
   - Use environment variables for secrets
   - Implement field-level encryption for PII when needed

2. **Input Sanitization**
   - Sanitize all user inputs to prevent injection attacks
   - Validate input types and formats before processing
   - Escape output to prevent XSS in any API responses

### 6.3 API Security

1. **Rate Limiting**
   - Implement per-endpoint and per-user rate limits
   - Apply graduated rate limits based on user tier
   - Return standardized rate limit response headers

2. **Webhook Security**
   - Validate webhook signatures
   - Implement webhook secret rotation
   - Apply rate limiting to webhook endpoints

## 7. Error Handling & Logging

### 7.1 Error Management

1. **Error Classification**
   - Operational errors (expected, handled gracefully)
   - Programming errors (bugs, unexpected behavior)
   - External system errors (third-party service failures)

2. **Custom Error Classes**
   ```javascript
   class ApplicationError extends Error {
     constructor(message, code, statusCode) {
       super(message);
       this.name = this.constructor.name;
       this.code = code;
       this.statusCode = statusCode;
     }
   }
   
   class ValidationError extends ApplicationError {
     constructor(message, details) {
       super(message, 'VALIDATION_ERROR', 400);
       this.details = details;
     }
   }
   ```

### 7.2 Logging Strategy

1. **Log Levels**
   - ERROR: System errors, unexpected exceptions
   - WARN: Handled exceptions, operational issues
   - INFO: Significant operations, system state changes
   - DEBUG: Detailed operational information

2. **Log Structure**
   ```javascript
   {
     "level": "error",
     "timestamp": "2025-04-13T18:30:00Z",
     "service": "userService",
     "message": "Failed to create user",
     "userId": "123", // Context-specific fields
     "requestId": "req-456",
     "error": {
       "name": "ValidationError",
       "message": "Email already in use",
       "stack": "..." // In development only
     }
   }
   ```

## 8. Testing Requirements

### 8.1 Test Organization

1. **Directory Structure**
   ```
   /tests
   ├── unit/
   │   ├── services/
   │   ├── utils/
   │   └── middleware/
   ├── integration/
   │   ├── api/
   │   ├── database/
   │   └── external/
   └── e2e/
   ```

2. **Naming Convention**
   - `[filename].test.js` or `[filename].spec.js`

### 8.2 Test Coverage Requirements

1. **Minimum Coverage**
   - Services: 90%
   - Utils: 95%
   - Controllers: 85%
   - Middleware: 90%

2. **Critical Path Testing**
   - Authentication flows
   - Payment processing
   - Telephony integration
   - eSIM provisioning

### 8.3 Test Implementation

1. **Unit Tests**
   ```javascript
   describe('userService.createUser', () => {
     it('should create a new user with valid data', async () => {
       // Setup
       const userData = { email: '<EMAIL>', password: 'password123' };
       const mockPrisma = { user: { create: jest.fn().mockResolvedValue({ id: '123', ...userData }) } };
       
       // Execute
       const result = await userService.createUser(userData, mockPrisma);
       
       // Verify
       expect(mockPrisma.user.create).toHaveBeenCalledWith({ data: expect.objectContaining(userData) });
       expect(result).toHaveProperty('id', '123');
       expect(result).not.toHaveProperty('password');
     });
   });
   ```

2. **Integration Tests**
   ```javascript
   describe('POST /api/v1/users', () => {
     it('should create a new user and return 201', async () => {
       const response = await request(app)
         .post('/api/v1/users')
         .send({
           data: {
             email: '<EMAIL>',
             password: 'securePass123'
           }
         });
       
       expect(response.status).toBe(201);
       expect(response.body.success).toBe(true);
       expect(response.body.data).toHaveProperty('id');
     });
   });
   ```

## 9. Performance Considerations

### 9.1 Query Optimization

1. **Database Queries**
   - Analyze and optimize slow queries
   - Apply appropriate indexing strategy
   - Use query-specific projections

2. **Caching Strategy**
   - Implement Redis caching for frequent queries
   - Apply TTL based on data volatility
   - Implement cache invalidation patterns

### 9.2 Resource Management

1. **Connection Pooling**
   - Configure appropriate pool sizes for databases
   - Implement connection reuse for external APIs
   - Monitor connection utilization

2. **Memory Management**
   - Avoid memory leaks in long-running processes
   - Implement resource cleanup in error cases
   - Monitor memory usage patterns

## 10. Deployment & DevOps

### 10.1 Environment Configuration

1. **Environment Variables**
   - Define all configuration via environment variables
   - Document all required variables
   - Provide defaults for non-critical settings

2. **Configuration Validation**
   - Validate critical environment variables on startup
   - Fail fast if required configuration is missing

### 10.2 Deployment Process

1. **Build Pipeline**
   - Lint code
   - Run tests
   - Build production assets
   - Generate documentation

2. **Deployment Strategy**
   - Blue/green deployment
   - Canary releases for high-risk changes
   - Automated rollback capability

## 11. Version History

| Version | Date | Description |
|---------|------|-------------|
| 1.0.0 | 2025-04-13 | Initial comprehensive backend structure document |

## 12. Related Documents

- [App Flow Document](mdc:.cursor/rules/app_flow_document.mdc)
- [Tech Stack Document](mdc:.cursor/rules/tech_stack_document.mdc)
- [Implementation Plan](mdc:.cursor/rules/implementation_plan.mdc)
