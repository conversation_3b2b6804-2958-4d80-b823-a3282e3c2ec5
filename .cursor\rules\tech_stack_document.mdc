---
description: 
globs: 
alwaysApply: false
---
---
description: Comprehensive technology stack documentation for CallSaver platform
globs: ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx"]
alwaysApply: true
version: 1.0.0
---

# Technology Stack Document

## 1. Overview

This document defines the comprehensive technology stack used across the CallSaver platform. It serves as the authoritative reference for all technology decisions, ensuring consistency, maintainability, and scalability across the codebase.

## 2. Core Technology Platforms

### 2.1 Frontend Technology

| Category | Technology | Version | Purpose |
|----------|------------|---------|---------|
| Framework | Next.js | 14.x | React framework for server-rendered and static websites |
| UI Library | React | 18.x | Component-based UI development |
| Styling | Tailwind CSS | 3.x | Utility-first CSS framework |
| Component Library | Shadcn/ui | Latest | Accessible and customizable component system |
| State Management | React Context | - | Default for shared component state |
| Advanced State | Zustand | 4.x | Lightweight state management for complex global state |
| Data Fetching | SWR | 2.x | React Hooks for remote data fetching |
| Form Management | React Hook Form | 7.x | Performant form state management |
| Schema Validation | Zod | 3.x | TypeScript-first schema validation |
| Internationalization | next-intl | 3.x | Complete internationalization framework |
| Type System | TypeScript | 5.x | Static type checking |
| Animation | Framer Motion | 10.x | Production-ready motion library |
| Visualization | D3.js | 7.x | Data visualization library |
| Testing | Jest/RTL | Latest | Unit and component testing |
| E2E Testing | Cypress | 13.x | End-to-end testing framework |

### 2.2 Backend Technology

| Category | Technology | Version | Purpose |
|----------|------------|---------|---------|
| Framework | Express.js | 4.x | Web application framework for Node.js |
| Runtime | Node.js | 20.x LTS | JavaScript runtime |
| API Style | REST | - | Primary API architecture pattern |
| Database ORM | Prisma | 5.x | Type-safe ORM for PostgreSQL |
| Database ODM | Mongoose | 8.x | MongoDB object modeling |
| Authentication | Supabase Auth | Latest | Authentication and authorization system |
| Validation | JSON Schema | - | Request/response validation |
| Real-time Communication | Socket.IO | 4.x | WebSocket-based real-time engine |
| Job Queue | BullMQ | 4.x | Redis-based queue for background jobs |
| Testing | Jest | 29.x | Testing framework |
| API Testing | Supertest | 6.x | HTTP assertions library |

### 2.3 DevOps & Infrastructure

| Category | Technology | Version | Purpose |
|----------|------------|---------|---------|
| Hosting | Railway | - | Backend, databases, services deployment |
| Frontend Hosting | Vercel | - | Next.js deployment and edge functions |
| CI/CD | GitHub Actions | - | Continuous integration and deployment |
| Containerization | Docker | - | Application containerization |
| Version Control | Git/GitHub | - | Source code management |
| Infrastructure as Code | Terraform | 1.x | Infrastructure provisioning |
| Monitoring | DataDog | - | Application performance monitoring |
| Logging | Pino | 8.x | Structured logging for Node.js |
| Error Tracking | Sentry | - | Real-time error tracking |

## 3. Data Storage Solutions

### 3.1 Primary Database: PostgreSQL

| Aspect | Details |
|--------|---------|
| Version | 15.x |
| Hosting | Supabase or Railway |
| Purpose | Core application data, user accounts, structured data |
| Access Pattern | Prisma ORM |
| Replication | Read replicas for scaling read operations |
| Backup Strategy | Daily automated backups, point-in-time recovery |
| Key Features | JSONB support, full-text search, transactions |

#### Key Data Models
- Users and authentication
- Account information
- Phone numbers and eSIM profiles
- Subscription and billing data
- Call and message metadata
- Configuration and settings

### 3.2 Secondary Database: MongoDB

| Aspect | Details |
|--------|---------|
| Version | 7.x |
| Hosting | Railway |
| Purpose | Flexible schema data, logs, analytics |
| Access Pattern | Mongoose ODM |
| Scaling Strategy | Sharding for horizontal scaling |
| Backup Strategy | Daily automated backups |
| Key Features | Document model, aggregation pipeline, indexing |

#### Key Data Models
- Call and message logs (detailed)
- AI-generated content history
- Activity and audit logs
- Analytics data
- Unstructured metadata

### 3.3 Caching Layer: Redis

| Aspect | Details |
|--------|---------|
| Version | 7.x |
| Hosting | Upstash or Railway |
| Purpose | Caching, session storage, job queues |
| Access Pattern | ioredis client |
| Key Features | In-memory storage, pub/sub, Lua scripting |
| Data Expiration | TTL-based automatic expiration |

#### Key Use Cases
- API response caching
- Rate limiting
- Session storage
- Background job queue
- Real-time feature support

### 3.4 Vector Database: Pinecone

| Aspect | Details |
|--------|---------|
| Version | Latest |
| Hosting | Pinecone Cloud |
| Purpose | Vector search for conversation context |
| Access Pattern | Pinecone SDK |
| Key Features | Vector search, metadata filtering, upsert API |

#### Key Use Cases
- Conversation memory for AI assistants
- Semantic search of call transcripts
- Similar customer query matching
- Context retrieval for AI generation

## 4. External APIs & Services

### 4.1 eSIM Provider API

| Aspect | Details |
|--------|---------|
| Provider | TBD (Airalo, Truphone, etc.) |
| Integration Pattern | REST API |
| Authentication | API key + secret |
| Rate Limits | Provider-specific |
| Redundancy | Fallback provider capability |

#### Key Functionalities
- eSIM profile creation
- QR code generation
- Profile management
- Usage monitoring
- Package purchasing

### 4.2 Voice Communication: WebRTC

| Aspect | Details |
|--------|---------|
| Implementation | Custom WebRTC stack |
| Signaling | Socket.IO over secure WebSockets |
| Media Servers | TURN/STUN infrastructure |
| Call Quality | Adaptive bitrate, packet loss concealment |
| Fallback | PSTN gateway for reliability |

#### Key Functionalities
- Voice calling
- Call recording
- Real-time transcription
- Voice AI integration
- Call quality metrics

### 4.3 AI Services: OpenAI

| Aspect | Details |
|--------|---------|
| API Version | Latest |
| Models | GPT-3.5 Turbo, GPT-4 |
| Integration Pattern | REST API |
| Authentication | API key |
| Redundancy | Fallback to alternative models |

#### Key Functionalities
- Conversation generation
- Call summarization
- Intent recognition
- Entity extraction
- Context-aware responses

### 4.4 Payment Processing: Stripe

| Aspect | Details |
|--------|---------|
| API Version | Latest |
| Integration Pattern | REST API + Webhooks |
| Authentication | API key (secret) |
| Features | Payment intents, subscriptions, invoicing |
| Compliance | PCI DSS Level 1 |

#### Key Functionalities
- Subscription management
- Usage-based billing
- Payment method handling
- Invoice generation
- Credit management

### 4.5 Email Service: SendGrid/Mailgun

| Aspect | Details |
|--------|---------|
| API Version | Latest |
| Integration Pattern | REST API + SMTP |
| Authentication | API key |
| Features | Templates, delivery tracking, analytics |
| Redundancy | Multi-provider fallback |

#### Key Functionalities
- Transactional emails
- Marketing campaigns
- Email templates
- Delivery tracking
- Bounce handling

## 5. Frontend Architecture

### 5.1 Application Structure

```
front/
├── mainpage/            # Marketing site
│   ├── app/             # Next.js App Router
│   ├── components/      # UI components
│   ├── lib/             # Utilities and services
│   ├── hooks/           # Custom React hooks
│   ├── styles/          # Global styles
│   └── public/          # Static assets
│
└── dashboard/           # User dashboard
    ├── app/             # Next.js App Router
    ├── components/      # UI components
    ├── lib/             # Utilities and services
    ├── hooks/           # Custom React hooks
    ├── styles/          # Global styles
    └── public/          # Static assets
```

### 5.2 Component Architecture

- **Atomic Design Methodology**
  - Atoms: Basic UI elements (buttons, inputs)
  - Molecules: Combinations of atoms (form fields, search bars)
  - Organisms: Complex UI sections (header, sidebar)
  - Templates: Page layouts
  - Pages: Specific instances of templates

- **State Management Patterns**
  - Local state: `useState` for component-specific state
  - Shared state: React Context for related component trees
  - Global state: Zustand for application-wide state
  - Server state: SWR for remote data management

### 5.3 Rendering Strategy

- **Server Components**: Used for static or database-dependent content
- **Client Components**: Used for interactive elements
- **Static Generation**: For marketing pages and documentation
- **Server-Side Rendering**: For dynamic, personalized pages
- **Incremental Static Regeneration**: For semi-dynamic content

### 5.4 Performance Optimization

- **Code Splitting**: Automatic with Next.js route-based splitting
- **Image Optimization**: Using Next.js Image component
- **Font Optimization**: Using next/font for Web Fonts
- **Bundle Analysis**: Regular webpack bundle analysis
- **Prefetching**: Strategic prefetching for common navigation paths

## 6. Backend Architecture

### 6.1 Application Structure

```
back/backend/
├── server.js              # Application entry point
├── config/                # Configuration management
├── controllers/           # Request handlers
├── routes/                # API endpoint definitions
├── services/              # Business logic
├── models/                # Data models
├── middleware/            # Express middleware
├── utils/                 # Utility functions
├── esim/                  # eSIM-specific modules
├── tests/                 # Test suite
└── scripts/               # Utility scripts
```

### 6.2 API Architecture

- **RESTful Design**: Resource-oriented API design
- **Versioning**: URL-based versioning (e.g., `/api/v1/`)
- **Authentication**: JWT-based authentication
- **Validation**: Request validation with JSON Schema
- **Error Handling**: Standardized error responses
- **Documentation**: OpenAPI/Swagger specification

### 6.3 Service Layer Architecture

- **Domain-Driven Design**: Organize services by business domain
- **Single Responsibility**: Each service handles one business concern
- **Dependency Injection**: Services receive dependencies via parameters
- **Transactional Integrity**: Database operations wrapped in transactions
- **Error Propagation**: Consistent error handling patterns

### 6.4 Background Processing

- **Job Queue**: BullMQ for reliable processing
- **Workers**: Dedicated worker processes for CPU-intensive tasks
- **Scheduling**: Cron-based scheduled jobs
- **Idempotency**: Guaranteed exactly-once processing
- **Monitoring**: Job completion, failure rates, processing time

## 7. Data Flow Architecture

### 7.1 Authentication Flow

```
┌───────────┐       ┌────────────────┐       ┌──────────┐
│  Browser  │       │  Auth Service   │       │ Supabase │
└─────┬─────┘       └────────┬───────┘       └────┬─────┘
      │                      │                     │
      │  Login Request       │                     │
      │─────────────────────>│                     │
      │                      │                     │
      │                      │  Verify Credentials │
      │                      │────────────────────>│
      │                      │                     │
      │                      │  User Data + Token  │
      │                      │<────────────────────│
      │                      │                     │
      │  JWT + User Data     │                     │
      │<─────────────────────│                     │
      │                      │                     │
┌─────┴─────┐       ┌────────┴───────┐       ┌────┴─────┐
│  Browser  │       │  Auth Service   │       │ Supabase │
└───────────┘       └────────────────┘       └──────────┘
```

### 7.2 Call Handling Flow

```
┌─────────┐      ┌──────────┐      ┌─────────────┐      ┌─────────┐      ┌─────────┐
│  Caller │      │ Twilio/  │      │CallSaver API│      │   AI    │      │Database │
└────┬────┘      │  eSIM    │      │             │      │ Service │      │         │
     │           └────┬─────┘      └──────┬──────┘      └────┬────┘      └────┬────┘
     │                │                   │                  │                │
     │ Dial Number    │                   │                  │                │
     │───────────────>│                   │                  │                │
     │                │                   │                  │                │
     │                │ Webhook/API Call  │                  │                │
     │                │──────────────────>│                  │                │
     │                │                   │                  │                │
     │                │                   │ Fetch Rules      │                │
     │                │                   │───────────────────────────────────>
     │                │                   │                  │                │
     │                │                   │ Return Rules     │                │
     │                │                   │<───────────────────────────────────
     │                │                   │                  │                │
     │                │                   │ Generate Response│                │
     │                │                   │─────────────────>│                │
     │                │                   │                  │                │
     │                │                   │ AI Response      │                │
     │                │                   │<─────────────────│                │
     │                │                   │                  │                │
     │                │ Voice Response    │                  │                │
     │                │<──────────────────│                  │                │
     │                │                   │                  │                │
     │ AI Response    │                   │                  │                │
     │<───────────────│                   │                  │                │
     │                │                   │ Log Interaction  │                │
     │                │                   │───────────────────────────────────>
     │                │                   │                  │                │
┌────┴────┐      ┌────┴─────┐      ┌──────┴──────┐      ┌────┴────┐      ┌────┴────┐
│  Caller │      │ Twilio/  │      │CallSaver API│      │   AI    │      │Database │
└─────────┘      │  eSIM    │      │             │      │ Service │      │         │
                 └──────────┘      └─────────────┘      └─────────┘      └─────────┘
```

### 7.3 eSIM Provisioning Flow

```
┌─────────┐      ┌──────────┐      ┌────────────┐      ┌─────────────┐
│  User   │      │ Frontend │      │ eSIM API   │      │ eSIM Provider│
└────┬────┘      └────┬─────┘      └──────┬─────┘      └──────┬──────┘
     │                │                   │                   │
     │ Request eSIM   │                   │                   │
     │───────────────>│                   │                   │
     │                │                   │                   │
     │                │ Create Profile    │                   │
     │                │──────────────────>│                   │
     │                │                   │                   │
     │                │                   │ Provision eSIM    │
     │                │                   │──────────────────>│
     │                │                   │                   │
     │                │                   │ Return Profile    │
     │                │                   │<──────────────────│
     │                │                   │                   │
     │                │ QR Code + Details │                   │
     │                │<──────────────────│                   │
     │                │                   │                   │
     │ Display QR Code│                   │                   │
     │<───────────────│                   │                   │
     │                │                   │                   │
     │ Scan QR Code   │                   │                   │
     │                │                   │                   │
     │ Activate Device│                   │                   │
     │──────────────────────────────────────────────────────>│
     │                │                   │                   │
     │                │                   │ Activation Status │
     │                │                   │<──────────────────│
     │                │                   │                   │
     │                │ Updated Status    │                   │
     │                │<──────────────────│                   │
     │                │                   │                   │
     │ Confirmation   │                   │                   │
     │<───────────────│                   │                   │
     │                │                   │                   │
┌────┴────┐      ┌────┴─────┐      ┌──────┴─────┐      ┌──────┴──────┐
│  User   │      │ Frontend │      │ eSIM API   │      │ eSIM Provider│
└─────────┘      └──────────┘      └────────────┘      └─────────────┘
```

## 8. Security Architecture

### 8.1 Authentication & Authorization

- **Authentication Methods**
  - Email/password with Supabase Auth
  - OAuth providers (Google, Microsoft, etc.)
  - Magic link authentication
  - JWT for API authentication

- **Authorization Model**
  - Role-based access control (RBAC)
  - Permission-based authorization
  - Resource-level access controls
  - API endpoint protection

### 8.2 Data Protection

- **Data at Rest**
  - Database encryption
  - Field-level encryption for sensitive data
  - Secure key management
  - Regular security audits

- **Data in Transit**
  - TLS 1.3 for all communications
  - HTTPS-only for all endpoints
  - Secure WebSocket connections
  - Certificate management and rotation

### 8.3 API Security

- **Request Protection**
  - API key validation
  - CSRF protection
  - Rate limiting
  - Input validation and sanitization

- **Response Security**
  - Content Security Policy
  - Secure headers
  - Minimal data exposure
  - Error message sanitization

## 9. Monitoring & Observability

### 9.1 Logging Strategy

- **Log Levels**
  - ERROR: System errors requiring attention
  - WARN: Potential issues
  - INFO: Operational events
  - DEBUG: Detailed information for troubleshooting

- **Log Format**
  ```json
  {
    "level": "info",
    "time": "2025-04-13T18:30:00Z",
    "msg": "User login successful",
    "service": "auth-service",
    "userId": "user-123",
    "requestId": "req-456",
    "metadata": { "browser": "Chrome", "ip": "********" }
  }
  ```

### 9.2 Metrics Collection

- **System Metrics**
  - Server CPU, memory, disk usage
  - Database connection pool usage
  - API request rates and response times
  - Error rates and status codes

- **Business Metrics**
  - User registrations and active users
  - Call and message volumes
  - AI interaction statistics
  - Revenue and subscription metrics

### 9.3 Alerting Strategy

- **Critical Alerts**
  - Service availability issues
  - Error rate spikes
  - Authentication failures
  - Payment processing failures

- **Warning Alerts**
  - Elevated response times
  - Approaching resource limits
  - Unusual traffic patterns
  - Failed background jobs

## 10. DevOps & Deployment

### 10.1 Environment Strategy

| Environment | Purpose | Deployment Trigger | Data |
|-------------|---------|-------------------|------|
| Development | Local development | Manual | Sanitized test data |
| Testing | Automated tests | CI on push | Test fixtures |
| Staging | Pre-production validation | CI on merge to develop | Anonymized production clone |
| Production | Live system | Manual deploy from main | Production data |

### 10.2 CI/CD Pipeline

```
┌─────────┐      ┌─────────┐      ┌─────────┐      ┌─────────┐      ┌─────────┐
│  Commit │──────│   Lint  │──────│   Test  │──────│  Build  │──────│ Deploy  │
└─────────┘      └─────────┘      └─────────┘      └─────────┘      └─────────┘
                      │                │                │                │
                      │                │                │                │
                ┌─────┴────┐     ┌─────┴────┐     ┌─────┴────┐     ┌─────┴────┐
                │Code Style│     │Unit Tests│     │Production│     │Environment│
                │Check     │     │Integration│     │Build     │     │Deployment│
                │          │     │Tests     │     │          │     │          │
                └──────────┘     └──────────┘     └──────────┘     └──────────┘
```

### 10.3 Infrastructure as Code

- **Terraform Modules**
  - Core infrastructure provisioning
  - Database configuration
  - Networking setup
  - Security group management

- **Docker Containerization**
  - Consistent development environments
  - Reproducible builds
  - Scalable deployment
  - Version-controlled configuration

## 11. Documentation Structure

### 11.1 Developer Documentation

- **API Reference**
  - Endpoint specifications
  - Request/response formats
  - Authentication requirements
  - Rate limiting information

- **Code Documentation**
  - Architecture overview
  - Component documentation
  - Workflow diagrams
  - Function/class documentation

### 11.2 Operational Documentation

- **Runbooks**
  - Deployment procedures
  - Incident response
  - Backup and restore
  - Scaling operations

- **SRE Documentation**
  - Monitoring setup
  - Alert handling
  - Performance optimization
  - Disaster recovery

## 12. Version History

| Version | Date | Description |
|---------|------|-------------|
| 1.0.0 | 2025-04-13 | Initial comprehensive technology stack document |

## 13. Related Documents

- [Backend Structure Document](mdc:.cursor/rules/backend_structure_document.mdc)
- [Frontend Guidelines Document](mdc:.cursor/rules/frontend_guidelines_document.mdc)
- [Implementation Plan](mdc:.cursor/rules/implementation_plan.mdc)
- [App Flow Document](mdc:.cursor/rules/app_flow_document.mdc)
