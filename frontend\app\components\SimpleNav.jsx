"use client";

import Link from 'next/link';
import { createClient } from '@supabase/supabase-js';

export default function SimpleNav() {
  // Handle logout click
  const handleLogout = async () => {
    const demoUser = localStorage.getItem('callsaver_demo_user');
    
    if (demoUser) {
      // For demo users, just remove the demo flag
      localStorage.removeItem('callsaver_demo_user');
      window.location.href = '/signin';
      return;
    }
    
    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
      
      if (!supabaseUrl || !supabaseKey) {
        console.error('Supabase credentials missing');
        window.location.href = '/signin';
        return;
      }
      
      const supabase = createClient(supabaseUrl, supabaseKey);
      await supabase.auth.signOut();
      window.location.href = '/signin';
    } catch (error) {
      console.error('Error signing out:', error);
      window.location.href = '/signin';
    }
  };

  return (
    <nav className="mb-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/dashboard" className="text-2xl font-bold text-white flex items-center">
            <span className="text-purple-500">Call</span>
            <span>Saver</span>
          </Link>
        </div>
        
        <div className="flex items-center">
          <button 
            className="text-gray-400 hover:text-white px-3 py-1.5 bg-gray-800/70 hover:bg-gray-800 rounded-lg transition-colors"
            onClick={handleLogout}
          >
            Sign Out
          </button>
        </div>
      </div>
    </nav>
  );
}
