// Export all permission-aware components
export { default as PermissionGate } from '../../PermissionGate';
export { default as PermissionAwareLink } from '../PermissionAwareLink';
export { default as PermissionAwareButton } from '../PermissionAwareButton';
export { default as PermissionAwareRoute } from '../PermissionAwareRoute';
export { default as PermissionAwareMenu } from '../PermissionAwareMenu';
export { default as PermissionAwareSidebar } from '../PermissionAwareSidebar';
export { default as PermissionAwareNavbar } from '../PermissionAwareNavbar';
export { default as PermissionAwareFeature } from '../PermissionAwareFeature';
export { default as PermissionTooltip } from '../PermissionTooltip';

// Export types
export type { MenuItem } from '../PermissionAwareMenu';

// Export navigation utilities
export {
  getNavigationItems,
  baseNavigationItems,
  adminNavigationItems,
  developerNavigationItems
} from '../../../utils/navigationUtils';

// Export feature flag hooks
export { useFeatureFlag, useIsFeatureEnabled, useIsResourceFeatureEnabled } from '../../../hooks/useFeatureFlag';
