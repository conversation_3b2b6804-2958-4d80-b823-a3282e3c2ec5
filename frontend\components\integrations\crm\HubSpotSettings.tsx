'use client';

import { IntegrationSettings } from '../../../hooks/useIntegrations';

interface HubSpotSettingsProps {
  settings: IntegrationSettings;
  onChange: (settings: IntegrationSettings) => void;
}

export default function HubSpotSettings({
  settings,
  onChange,
}: HubSpotSettingsProps) {
  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    onChange({
      ...settings,
      [name]: checked,
    });
  };

  // Handle select change
  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    onChange({
      ...settings,
      [name]: value,
    });
  };

  return (
    <div className="space-y-4">
      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
        Data Synchronization
      </h4>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="syncContacts"
            name="syncContacts"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.syncContacts !== false} // Default to true
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="syncContacts"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Sync Contacts
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Synchronize contacts between CallSaver and HubSpot.
          </p>
        </div>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="logCalls"
            name="logCalls"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.logCalls !== false} // Default to true
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="logCalls"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Log Calls
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Create call activities in HubSpot for calls handled by CallSaver.
          </p>
        </div>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="createTasks"
            name="createTasks"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.createTasks || false}
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="createTasks"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Create Tasks
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Create tasks in HubSpot for missed calls and voicemails.
          </p>
        </div>
      </div>

      <div>
        <label
          htmlFor="contactSyncDirection"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Contact Sync Direction
        </label>
        <select
          id="contactSyncDirection"
          name="contactSyncDirection"
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          value={settings.contactSyncDirection || 'both'}
          onChange={handleSelectChange}
        >
          <option value="both">Two-way (CallSaver ↔ HubSpot)</option>
          <option value="to_hubspot">One-way (CallSaver → HubSpot)</option>
          <option value="from_hubspot">One-way (HubSpot → CallSaver)</option>
        </select>
      </div>
    </div>
  );
}
