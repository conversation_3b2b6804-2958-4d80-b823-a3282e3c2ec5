"use client";

import React, { useState, useEffect } from 'react';
import { 
  XMarkIcon, 
  Bars3Icon,
  ChartBarIcon, 
  ClockIcon, 
  CheckCircleIcon, 
  StarIcon, 
  PhoneIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import { MetricCard } from './MetricCard';
import { DashboardSidebar } from './DashboardSidebar';
import { RecentActivityPanel } from './RecentActivityPanel';
import { ConversionRatePanel } from './ConversionRatePanel';
import { BarChartComponent, LineChartComponent, DonutChartComponent } from './ChartComponents';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3005/api';

export function AnalyticsDashboard() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [timeframe, setTimeframe] = useState('week');

  // Fetch analytics data from the backend
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_URL}/analytics/dashboard?timeframe=${timeframe}`);
        
        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }
        
        const data = await response.json();
        setAnalyticsData(data.data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching analytics data:', err);
        setError(err.message || 'Failed to fetch analytics data');
        setLoading(false);
      }
    };

    fetchAnalyticsData();
  }, [timeframe]);

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-full w-full bg-gray-900 text-white">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500 mb-4"></div>
          <p className="text-lg">Loading analytics data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-full w-full bg-gray-900 text-white">
        <div className="flex flex-col items-center text-center max-w-md">
          <div className="bg-red-900/30 p-4 rounded-full mb-4">
            <XMarkIcon className="h-12 w-12 text-red-500" />
          </div>
          <h3 className="text-xl font-semibold mb-2">Failed to load analytics</h3>
          <p className="text-gray-400 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-indigo-700 hover:bg-indigo-600 rounded-md transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Prepare data for display if analytics data is available
  const dashboardData = analyticsData ? {
    callVolume: {
      value: analyticsData.calls.summary.totalCalls,
      change: +8.2, // This could be calculated if we have historical data
      description: 'Total call volume this month'
    },
    smsChatsStarted: {
      value: analyticsData.usage.sms.used,
      change: +12.8, // This could be calculated if we have historical data
      description: 'Number of SMS threads started after a missed call'
    },
    resolutionRate: {
      value: analyticsData.aiPerformance.summary.successRate,
      change: +3.7, // This could be calculated if we have historical data
      description: 'First-time resolution rate (%)'
    },
    customerSatisfaction: {
      value: (analyticsData.aiPerformance.sentimentAnalysis.positive / 20).toFixed(1),
      change: +0.3, // This could be calculated if we have historical data
      description: 'Customer satisfaction score (1-5)'
    }
  } : null;

  // Prepare chart data
  const callTrendData = analyticsData ? 
    analyticsData.calls.timeline.labels.map((label, index) => ({
      name: label.split('-')[2], // Extract day from date
      value: analyticsData.calls.timeline.callVolume[index]
    })) : [];

  const categoryData = analyticsData?.aiPerformance.topScenarios.map(scenario => ({
    name: scenario.name,
    value: scenario.count
  })) || [];

  const satisfactionData = analyticsData ? [
    { name: 'Positive', value: analyticsData.aiPerformance.sentimentAnalysis.positive },
    { name: 'Neutral', value: analyticsData.aiPerformance.sentimentAnalysis.neutral },
    { name: 'Negative', value: analyticsData.aiPerformance.sentimentAnalysis.negative }
  ] : [];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-gray-950 text-white relative overflow-hidden">
      {/* Ambient background effects */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none"></div>
      <div className="absolute -top-40 -left-40 w-80 h-80 bg-indigo-600 rounded-full filter blur-[100px] opacity-10 animate-pulse-slow"></div>
      <div className="absolute -bottom-40 -right-40 w-80 h-80 bg-purple-600 rounded-full filter blur-[100px] opacity-10 animate-pulse-slow delay-1000"></div>
      
      {/* Mobile sidebar toggle */}
      <button 
        className="fixed top-4 left-4 z-50 lg:hidden bg-gray-800 p-2 rounded-lg shadow-lg border border-gray-700 hover:border-indigo-500 transition-colors duration-200"
        onClick={() => setSidebarOpen(!sidebarOpen)}
      >
        {sidebarOpen ? 
          <XMarkIcon className="h-6 w-6 text-indigo-400" /> : 
          <Bars3Icon className="h-6 w-6 text-indigo-400" />
        }
      </button>

      {/* Centered container for the entire dashboard */}
      <div className="mx-auto max-w-7xl">
        {/* Fixed-height container for sidebar and main content */}
        <div className="flex h-[calc(100vh-2rem)] items-stretch">
          {/* Sidebar */}
          <aside className={`
            lg:w-64 flex-shrink-0 z-40 overflow-hidden
            fixed lg:relative top-0 h-full
            transition-transform duration-300 ease-in-out
            ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          `}>
            <div className="h-full overflow-y-auto">
              <DashboardSidebar />
          </div>
          </aside>

          {/* Overlay for mobile when sidebar is open */}
          {sidebarOpen && (
            <div 
              className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden backdrop-blur-xs"
              onClick={() => setSidebarOpen(false)}
            ></div>
          )}
          
          {/* Main Content */}
          <main className="flex-1 p-4 md:p-6 relative z-10 w-full lg:pl-6 overflow-y-auto">
            <div className="max-w-7xl mx-auto">
              {/* Dashboard Header */}
              <div className="dashboard-card border-glow before:shadow-neon-blue mb-6 p-5 w-full border border-gray-800/60 rounded-xl bg-gray-900/90 backdrop-blur-sm relative overflow-hidden">
                <div className="relative z-10">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div className="flex items-center mb-4 md:mb-0">
                      <div className="p-2 rounded-md bg-blue-900/40 text-blue-400 mr-3 shadow-lg border border-blue-500/20">
                        <ChartBarIcon className="h-6 w-6" />
                      </div>
                      <div>
                        <h1 className="text-2xl md:text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400">
                          Call Analytics Dashboard
                        </h1>
                        <p className="text-gray-400 mt-1">Monitor and analyze your call center performance</p>
                      </div>
                    </div>
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                      <div className="flex items-center space-x-2">
                        <label htmlFor="timeframe" className="text-xs text-gray-400">Timeframe:</label>
                        <select
                          id="timeframe"
                          value={timeframe}
                          onChange={(e) => setTimeframe(e.target.value)}
                          className="bg-gray-800 border border-gray-700 text-gray-200 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-1"
                        >
                          <option value="day">Today</option>
                          <option value="week">This Week</option>
                          <option value="month">This Month</option>
                          <option value="year">This Year</option>
                        </select>
                      </div>
                      <div className="flex items-center">
                        <span className="text-xs text-gray-400 mr-2">Last updated: </span>
                        <span className="text-sm text-white">{new Date().toLocaleTimeString()}</span>
                        <div className="ml-3 px-2 py-1 rounded-md bg-gray-800/80 border border-gray-700/40 flex items-center">
                          <span className="inline-block h-2 w-2 rounded-full bg-green-500 animate-pulse mr-1.5"></span>
                          <span className="text-xs text-gray-300">Live</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="card-glow"></div>
              </div>
        
              {/* Key Metrics Section - Using consistent heights and equal spacing */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <MetricCard 
                  title="Call Volume" 
                  value={dashboardData.callVolume.value} 
                  change={dashboardData.callVolume.change}
                  description={dashboardData.callVolume.description}
                  color="blue"
                  icon={<PhoneIcon className="h-5 w-5" />}
                />
                <MetricCard 
                  title="SMS Chats" 
                  value={dashboardData.smsChatsStarted.value} 
                  change={dashboardData.smsChatsStarted.change} 
                  description={dashboardData.smsChatsStarted.description}
                  color="purple"
                  icon={<ChatBubbleLeftRightIcon className="h-5 w-5" />}
                />
                <MetricCard 
                  title="Resolution Rate" 
                  value={`${dashboardData.resolutionRate.value}%`} 
                  change={dashboardData.resolutionRate.change} 
                  description={dashboardData.resolutionRate.description}
                  color="green"
                  icon={<CheckCircleIcon className="h-5 w-5" />}
                />
                <MetricCard 
                  title="Customer Satisfaction" 
                  value={dashboardData.customerSatisfaction.value} 
                  change={dashboardData.customerSatisfaction.change} 
                  description={dashboardData.customerSatisfaction.description}
                  color="yellow"
                  icon={<StarIcon className="h-5 w-5" />}
                />
              </div>

              {/* Charts Section - Using uniform grid layout with 2x2 structure */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                {/* First Row of Charts */}
                <div className="dashboard-card border-glow before:shadow-neon-blue min-h-[400px] border border-gray-800/60 rounded-xl bg-gray-900/90 backdrop-blur-sm relative overflow-hidden">
                  <div className="flex flex-col h-full p-5 relative z-10">
                    <div className="dashboard-card-header mb-4 flex-col sm:flex-row">
                      <div className="dashboard-card-title mb-2 sm:mb-0">
                        <div className="p-2 rounded-md bg-blue-900/40 text-blue-400 mr-2 shadow-lg border border-blue-500/20 flex-shrink-0">
                          <ChartBarIcon className="h-5 w-5" />
                        </div>
                        <h3 className="text-blue-400 font-medium text-lg">Daily Call Volume</h3>
                      </div>
                      <div className="flex items-center flex-shrink-0">
                        <span className="text-xs text-gray-400 bg-gray-800/60 py-1 px-2 rounded border border-gray-700/30 whitespace-nowrap">{timeframe}-trend</span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-400 mb-6">Trend over the selected time period</p>
                    <div className="flex-grow chart-container">
                      <BarChartComponent data={callTrendData} height={240} />
                    </div>
                    <div className="card-glow"></div>
                  </div>
                </div>
                
                <div className="dashboard-card border-glow before:shadow-neon-purple min-h-[400px] border border-gray-800/60 rounded-xl bg-gray-900/90 backdrop-blur-sm relative overflow-hidden">
                  <div className="flex flex-col h-full p-5 relative z-10">
                    <div className="dashboard-card-header mb-4 flex-col sm:flex-row">
                      <div className="dashboard-card-title mb-2 sm:mb-0">
                        <div className="p-2 rounded-md bg-purple-900/40 text-purple-400 mr-2 shadow-lg border border-purple-500/20 flex-shrink-0">
                          <PhoneIcon className="h-5 w-5" />
                        </div>
                        <h3 className="text-purple-400 font-medium text-lg">Call Categories</h3>
                      </div>
                      <div className="flex items-center flex-shrink-0">
                        <span className="text-xs text-gray-400 bg-gray-800/60 py-1 px-2 rounded border border-gray-700/30 whitespace-nowrap">Distribution</span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-400 mb-6">Distribution by issue type</p>
                    <div className="flex-grow chart-container">
                      <DonutChartComponent data={categoryData} height={240} />
                    </div>
                    <div className="card-glow"></div>
                  </div>
                </div>

                {/* Second Row of Cards - Minutes Usage & Sentiment Analysis */}
                <div className="dashboard-card border-glow before:shadow-neon-green min-h-[400px] border border-gray-800/60 rounded-xl bg-gray-900/90 backdrop-blur-sm relative overflow-hidden">
                  <div className="flex flex-col h-full p-5 relative z-10">
                    <div className="dashboard-card-header mb-4 flex-col sm:flex-row">
                      <div className="dashboard-card-title mb-2 sm:mb-0">
                        <div className="p-2 rounded-md bg-green-900/40 text-green-400 mr-2 shadow-lg border border-green-500/20 flex-shrink-0">
                          <ClockIcon className="h-5 w-5" />
                        </div>
                        <h3 className="text-green-400 font-medium text-lg">Minutes Usage</h3>
                      </div>
                      <div className="flex items-center flex-shrink-0">
                        <span className="text-xs text-gray-400 bg-gray-800/60 py-1 px-2 rounded border border-gray-700/30 whitespace-nowrap">Plan Usage</span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-400 mb-6">Minutes used vs. available</p>
                    <div className="flex-grow flex flex-col">
                      <div className="mb-6">
                        <div className="flex justify-between mb-2">
                          <span className="text-sm text-gray-400">Used: {analyticsData?.usage.minutes.used} min</span>
                          <span className="text-sm text-gray-400">Available: {analyticsData?.usage.minutes.total} min</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2.5">
                          <div className="bg-green-600 h-2.5 rounded-full" style={{ width: `${analyticsData?.usage.minutes.percentUsed}%` }}></div>
                        </div>
                        <div className="mt-2 text-right">
                          <span className="text-sm text-gray-400">{analyticsData?.usage.minutes.percentUsed}% used</span>
                        </div>
                      </div>
                      
                      <div className="mb-6">
                        <div className="flex justify-between mb-2">
                          <span className="text-sm text-gray-400">Used: {analyticsData?.usage.sms.used} SMS</span>
                          <span className="text-sm text-gray-400">Available: {analyticsData?.usage.sms.total} SMS</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2.5">
                          <div className="bg-purple-600 h-2.5 rounded-full" style={{ width: `${analyticsData?.usage.sms.percentUsed}%` }}></div>
                        </div>
                        <div className="mt-2 text-right">
                          <span className="text-sm text-gray-400">{analyticsData?.usage.sms.percentUsed}% used</span>
                        </div>
                      </div>
                      
                      <div className="text-center mt-4">
                        <p className="text-sm text-gray-400">Daily usage trend for the past week</p>
                        <LineChartComponent 
                          data={analyticsData?.usage.dailyUsage.labels.map((label, index) => ({
                            date: label,
                            minutes: analyticsData.usage.dailyUsage.minutes[index],
                            sms: analyticsData.usage.dailyUsage.sms[index]
                          })) || []}
                          height={120}
                        />
                      </div>
                    </div>
                    <div className="card-glow"></div>
                  </div>
                </div>
                
                <div className="dashboard-card border-glow before:shadow-neon-yellow min-h-[400px] border border-gray-800/60 rounded-xl bg-gray-900/90 backdrop-blur-sm relative overflow-hidden">
                  <div className="flex flex-col h-full p-5 relative z-10">
                    <div className="dashboard-card-header mb-4 flex-col sm:flex-row">
                      <div className="dashboard-card-title mb-2 sm:mb-0">
                        <div className="p-2 rounded-md bg-yellow-900/40 text-yellow-400 mr-2 shadow-lg border border-yellow-500/20 flex-shrink-0">
                          <StarIcon className="h-5 w-5" />
                        </div>
                        <h3 className="text-yellow-400 font-medium text-lg">Sentiment Analysis</h3>
                      </div>
                      <div className="flex items-center flex-shrink-0">
                        <span className="text-xs text-gray-400 bg-gray-800/60 py-1 px-2 rounded border border-gray-700/30 whitespace-nowrap">Customer Feedback</span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-400 mb-6">Sentiment distribution from customer interactions</p>
                    <div className="flex-grow chart-container">
                      <DonutChartComponent data={satisfactionData} height={240} colors={['#10B981', '#94A3B8', '#EF4444']} />
                    </div>
                    <div className="card-glow"></div>
                  </div>
                </div>
              </div>

              {/* Dashboard Footer */}
              <div className="w-full dashboard-card-blue border-glow mb-6">
                <div className="p-4 sm:p-5 relative z-10">
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div className="flex items-center">
                      <div className="title-icon-indigo">
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                      </div>
                      <div>
                        <div className="text-sm text-gray-400">AI Performance</div>
                        <div className="text-sm font-medium text-white">{analyticsData?.aiPerformance.summary.successRate}% Success Rate</div>
                      </div>
                    </div>
        
                    <div className="flex items-center">
                      <div className="title-icon-purple">
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z" />
                        </svg>
                      </div>
                      <div>
                        <div className="text-sm text-gray-400">Total Interactions</div>
                        <div className="text-sm font-medium text-white">{analyticsData?.aiPerformance.summary.totalInteractions}</div>
                      </div>
                    </div>
        
                    <div className="flex items-center">
                      <div className="title-icon-blue">
                        <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                      </div>
                      <div>
                        <div className="text-sm text-gray-400">Avg Response Time</div>
                        <div className="text-sm font-medium text-white">{analyticsData?.aiPerformance.summary.avgResponseTime} seconds</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="card-glow"></div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}