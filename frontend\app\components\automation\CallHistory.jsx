"use client";

import { useState } from 'react';
import { FiChevronDown, FiChevronUp, FiPhoneCall, FiMessageSquare, FiClock } from 'react-icons/fi';
import { format } from 'date-fns';
import CallStatusDisplay from './CallStatusDisplay';

export default function CallHistory({ calls = [], messages = [] }) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [activeTab, setActiveTab] = useState('calls');
  
  // Combine and sort calls and messages by timestamp if needed
  const sortedCalls = [...calls].sort((a, b) => 
    new Date(b.timestamp) - new Date(a.timestamp)
  );
  
  const sortedMessages = [...messages].sort((a, b) => 
    new Date(b.timestamp) - new Date(a.timestamp)
  );
  
  return (
    <div className="mt-8 border border-gray-700 rounded-md bg-gray-800/50">
      <div 
        className="flex items-center justify-between p-4 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="font-medium flex items-center">
          <FiClock className="mr-2" />
          Activity History
        </div>
        <div>
          {isExpanded ? <FiChevronUp /> : <FiChevronDown />}
        </div>
      </div>
      
      {isExpanded && (
        <div className="p-4 border-t border-gray-700">
          {/* Tabs */}
          <div className="flex border-b border-gray-700 mb-4">
            <button
              className={`py-2 px-4 font-medium flex items-center ${
                activeTab === 'calls' 
                  ? 'text-blue-400 border-b-2 border-blue-400' 
                  : 'text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('calls')}
            >
              <FiPhoneCall className="mr-2" />
              Calls ({sortedCalls.length})
            </button>
            <button
              className={`py-2 px-4 font-medium flex items-center ${
                activeTab === 'messages' 
                  ? 'text-blue-400 border-b-2 border-blue-400' 
                  : 'text-gray-400 hover:text-gray-300'
              }`}
              onClick={() => setActiveTab('messages')}
            >
              <FiMessageSquare className="mr-2" />
              Messages ({sortedMessages.length})
            </button>
          </div>
          
          {/* Content */}
          {activeTab === 'calls' ? (
            <div>
              {sortedCalls.length > 0 ? (
                sortedCalls.map(call => (
                  <CallStatusDisplay key={call.id} call={call} />
                ))
              ) : (
                <div className="text-center py-8 text-gray-400">
                  No call history yet. Use the /call command to make your first call.
                </div>
              )}
            </div>
          ) : (
            <div>
              {sortedMessages.length > 0 ? (
                sortedMessages.map(message => (
                  <div 
                    key={message.id}
                    className="p-3 rounded-md border border-gray-700 bg-gray-800 mb-3"
                  >
                    <div className="flex justify-between">
                      <div className="font-medium flex items-center">
                        <FiMessageSquare className="mr-2 text-blue-400" />
                        SMS to {message.to}
                      </div>
                      <div className="text-sm text-gray-400">
                        {format(new Date(message.timestamp), 'MMM d, h:mm a')}
                      </div>
                    </div>
                    <div className="mt-2 text-sm">{message.body}</div>
                    {message.status && (
                      <div className={`mt-1 text-xs ${
                        message.status === 'delivered' 
                          ? 'text-green-400' 
                          : message.status === 'failed' 
                            ? 'text-red-400' 
                            : 'text-gray-400'
                      }`}>
                        Status: {message.status}
                      </div>
                    )}
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-400">
                  No message history yet. Use the /sms command to send your first message.
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
} 