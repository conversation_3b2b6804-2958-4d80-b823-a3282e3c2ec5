// This file is for use in Expo mobile apps
// It can be copied to your Expo project
// Import this in your Expo app with the appropriate path

import { createClient } from '@supabase/supabase-js';
import * as SecureStore from 'expo-secure-store';

// Secure storage adapter for Expo
const ExpoSecureStoreAdapter = {
  getItem: (key) => {
    return SecureStore.getItemAsync(key);
  },
  setItem: (key, value) => {
    SecureStore.setItemAsync(key, value);
  },
  removeItem: (key) => {
    SecureStore.deleteItemAsync(key);
  },
};

// Create the client with mobile environment variables
const supabaseMobile = createClient(
  process.env.EXPO_PUBLIC_SUPABASE_URL,
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
  {
    auth: {
      storage: ExpoSecureStoreAdapter,
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
    },
  }
);

export default supabaseMobile; 