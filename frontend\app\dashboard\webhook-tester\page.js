"use client";

import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import Nav from '../../components/Nav';
import LoadingState from '../../components/ui/LoadingState';
import ErrorMessage from '../../components/ui/ErrorMessage';
import { PhoneIcon, ArrowsRightLeftIcon, CheckCircleIcon, ClipboardIcon } from '@heroicons/react/24/outline';

export default function WebhookTester() {
  const [isLoading, setIsLoading] = useState(true);
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [webhookUrl, setWebhookUrl] = useState('');
  const [testType, setTestType] = useState('incoming_call');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [testResult, setTestResult] = useState(null);
  const [userNumbers, setUserNumbers] = useState([]);
  const [error, setError] = useState(null);
  const [copiedToClipboard, setCopiedToClipboard] = useState(false);
  
  // Test types and their descriptions
  const testTypes = [
    {
      id: 'incoming_call',
      name: 'Incoming Call',
      description: 'Test what happens when a call comes in to your number',
      example: {
        event: 'incoming_call',
        from: '+15551234567',
        to: '+15557654321',
        callId: 'call_123456789',
        timestamp: new Date().toISOString()
      }
    },
    {
      id: 'call_status',
      name: 'Call Status Update',
      description: 'Test call status changes (answered, completed, etc)',
      example: {
        event: 'call_status',
        callId: 'call_123456789',
        status: 'completed',
        duration: 45,
        timestamp: new Date().toISOString()
      }
    },
    {
      id: 'voicemail',
      name: 'New Voicemail',
      description: 'Test receiving a new voicemail notification',
      example: {
        event: 'voicemail',
        from: '+15551234567',
        to: '+15557654321',
        callId: 'call_123456789',
        voicemailUrl: 'https://api.callsaver.app/voicemails/vm_123456.mp3',
        duration: 30,
        timestamp: new Date().toISOString()
      }
    }
  ];
  
  useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);
      
      try {
        // Check for demo user
        const demoUser = localStorage.getItem('callsaver_demo_user');
        
        if (demoUser) {
          console.log('Demo user detected');
          setUserName('Demo User');
          setUserEmail('<EMAIL>');
          
          // Set some fake demo data
          setWebhookUrl('https://webhook.site/1234-5678-90ab-cdef');
          setUserNumbers([
            { 
              id: 'demo-123', 
              phoneNumber: '+****************', 
              region: 'US', 
              capabilities: ['voice', 'sms'],
            },
            { 
              id: 'demo-456', 
              phoneNumber: '+****************', 
              region: 'US', 
              capabilities: ['voice'],
            }
          ]);
          
          // Default to the first phone number
          setPhoneNumber('+****************');
          
          setIsLoading(false);
          return;
        }
        
        // Handle real user authentication with Supabase
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
        
        if (!supabaseUrl || !supabaseKey) {
          console.error('Supabase credentials missing');
          throw new Error('Authentication configuration error');
        }
        
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        // Get session
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session) {
          window.location.href = '/signin';
          return;
        }
        
        // User is authenticated
        setUserName(session.user.user_metadata?.name || 'User');
        setUserEmail(session.user.email);
        
        // In a real app, fetch webhook URLs and user numbers from backend
        // For now, we'll use default values
        setWebhookUrl('');
        setUserNumbers([]);
        
      } catch (err) {
        console.error('Page initialization error:', err);
        setError('Authentication error. Please try signing in again.');
      } finally {
        setIsLoading(false);
      }
    };
    
    initializeAuth();
  }, []);
  
  // Generate the test payload based on the selected test type
  const generateTestPayload = () => {
    const testTypeObj = testTypes.find(type => type.id === testType);
    const payload = { ...testTypeObj.example };
    
    // Use selected phone number if available
    if (phoneNumber && (testType === 'incoming_call' || testType === 'voicemail')) {
      payload.to = phoneNumber;
    }
    
    return payload;
  };
  
  // Handle test webhook request
  const handleTestWebhook = async () => {
    if (!webhookUrl) {
      setError('Please enter a webhook URL to test');
      return;
    }
    
    try {
      setTestResult(null);
      const payload = generateTestPayload();
      
      // Show loading state for 1 second for better UX
      setIsLoading(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, this would make an actual API call
      // For demo purposes, we'll simulate a successful response
      const demoResponse = {
        success: true,
        statusCode: 200,
        response: {
          message: 'Webhook delivered successfully',
          timestamp: new Date().toISOString()
        },
        request: {
          url: webhookUrl,
          method: 'POST',
          body: payload
        }
      };
      
      setTestResult(demoResponse);
      setError(null);
    } catch (err) {
      console.error('Error testing webhook:', err);
      setError('Failed to test webhook. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Copy webhook URL to clipboard
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedToClipboard(true);
      setTimeout(() => setCopiedToClipboard(false), 2000);
    }).catch(err => {
      console.error('Failed to copy text: ', err);
    });
  };
  
  // Format JSON for display
  const formatJson = (json) => {
    return JSON.stringify(json, null, 2);
  };
  
  if (isLoading) {
    return <LoadingState fullPage text="Loading webhook tester..." />;
  }
  
  return (
    <div className="min-h-screen bg-gray-950 text-white">
      <div className="container mx-auto max-w-7xl px-4 py-6">
        <Nav userName={userName} userEmail={userEmail} />
        
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-white">Webhook Tester</h1>
          <p className="mt-2 text-gray-400">
            Test your webhook endpoints with simulated CallSaver events
          </p>
        </div>
        
        {error && (
          <ErrorMessage 
            message={error}
            className="mb-6"
            dismissible
            onDismiss={() => setError(null)}
          />
        )}
        
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <div className="lg:col-span-1">
            <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
              <h2 className="text-lg font-medium text-white mb-4">Configuration</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Webhook URL
                  </label>
                  <div className="mt-1 flex rounded-md shadow-sm">
                    <input
                      type="text"
                      value={webhookUrl}
                      onChange={(e) => setWebhookUrl(e.target.value)}
                      className="flex-1 min-w-0 block w-full px-3 py-2 rounded-md border-0 bg-gray-800 text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-600 sm:text-sm"
                      placeholder="https://your-webhook-endpoint.com/hook"
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    URL that will receive the webhook POST request
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Event Type
                  </label>
                  <select
                    value={testType}
                    onChange={(e) => setTestType(e.target.value)}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-0 bg-gray-800 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-purple-600 sm:text-sm"
                  >
                    {testTypes.map((type) => (
                      <option key={type.id} value={type.id}>
                        {type.name}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-xs text-gray-500">
                    {testTypes.find(type => type.id === testType)?.description}
                  </p>
                </div>
                
                {(testType === 'incoming_call' || testType === 'voicemail') && (
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      Phone Number
                    </label>
                    <select
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-0 bg-gray-800 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-purple-600 sm:text-sm"
                    >
                      <option value="">Select a number</option>
                      {userNumbers.map((number) => (
                        <option key={number.id} value={number.phoneNumber}>
                          {number.phoneNumber}
                        </option>
                      ))}
                    </select>
                    <p className="mt-1 text-xs text-gray-500">
                      Phone number that will receive the call or voicemail
                    </p>
                  </div>
                )}
                
                <button
                  onClick={handleTestWebhook}
                  disabled={!webhookUrl}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ArrowsRightLeftIcon className="mr-2 h-5 w-5" />
                  Test Webhook
                </button>
              </div>
            </div>
          </div>
          
          <div className="lg:col-span-2">
            <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-medium text-white">Request Preview</h2>
                <button
                  onClick={() => copyToClipboard(formatJson(generateTestPayload()))}
                  className="flex items-center text-xs text-purple-400 hover:text-purple-300"
                >
                  {copiedToClipboard ? (
                    <>
                      <CheckCircleIcon className="h-4 w-4 mr-1" />
                      Copied!
                    </>
                  ) : (
                    <>
                      <ClipboardIcon className="h-4 w-4 mr-1" />
                      Copy JSON
                    </>
                  )}
                </button>
              </div>
              
              <div className="bg-gray-800/50 rounded-md p-4 overflow-auto max-h-80">
                <pre className="text-gray-300 text-sm">
                  {formatJson(generateTestPayload())}
                </pre>
              </div>
              
              {testResult && (
                <>
                  <h3 className="text-md font-medium text-white mt-6 mb-2">Response</h3>
                  <div className="bg-gray-800/50 rounded-md p-4 overflow-auto max-h-80">
                    <pre className="text-gray-300 text-sm">
                      {formatJson(testResult)}
                    </pre>
                  </div>
                  
                  {testResult.success && (
                    <div className="mt-4 flex items-center text-green-500">
                      <CheckCircleIcon className="h-5 w-5 mr-2" />
                      <span>Webhook successfully delivered!</span>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
        
        <div className="mt-6 bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
          <h2 className="text-lg font-medium text-white mb-4">Setting Up Your Webhook</h2>
          
          <div className="prose prose-invert max-w-none text-sm">
            <p>
              CallSaver can notify your application about events such as incoming calls, status changes,
              and new voicemails by sending HTTP POST requests to a webhook URL you provide.
            </p>
            
            <h3>How to Set Up Your Webhook</h3>
            <ol>
              <li>
                Create a publicly accessible endpoint on your server that can accept POST requests.
              </li>
              <li>
                Configure your endpoint to process JSON payloads.
              </li>
              <li>
                Add the webhook URL in your CallSaver settings.
              </li>
              <li>
                Use this tool to test your webhook implementation.
              </li>
            </ol>
            
            <h3>Webhook Response Requirements</h3>
            <p>
              Your webhook endpoint should respond with a 200 OK status code to acknowledge receipt
              of the webhook. Additional response data is not required but can be useful for debugging.
            </p>
            
            <h3>Webhook Security</h3>
            <p>
              For security purposes, CallSaver signs each webhook request with a signature header.
              You should verify this signature in your webhook handler to ensure the request is authentic.
            </p>
            
            <div className="bg-gray-800/50 rounded-md p-4 text-xs mt-4">
              <p className="font-mono">
                # Example signature verification in Python<br />
                import hmac<br />
                import hashlib<br /><br />
                
                def verify_signature(payload, signature, secret):<br />
                &nbsp;&nbsp;computed = hmac.new(secret.encode(), payload.encode(), hashlib.sha256).hexdigest()<br />
                &nbsp;&nbsp;return hmac.compare_digest(computed, signature)<br />
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 