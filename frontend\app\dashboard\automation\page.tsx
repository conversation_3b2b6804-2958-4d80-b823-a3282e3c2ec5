'use client';

import { Suspense } from 'react';
import AutomationLayout from '../../../components/automation/AutomationLayout';
import AutomationSkeleton from '../../../components/automation/AutomationSkeleton';

export default function AutomationPage() {
  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
        Automation
      </h1>
      <Suspense fallback={<AutomationSkeleton />}>
        <AutomationLayout />
      </Suspense>
    </div>
  );
}
