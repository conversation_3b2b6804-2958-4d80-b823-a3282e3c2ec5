'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { usePermissions } from '../../hooks/usePermissions';

export interface MenuItem {
  name: string;
  path: string;
  icon?: React.ReactNode | string;
  permission?: string;
  anyPermission?: string[];
  allPermissions?: string[];
  resource?: string;
  action?: string;
  scope?: string;
  children?: MenuItem[];
}

interface PermissionAwareMenuProps {
  items: MenuItem[];
  className?: string;
  itemClassName?: string;
  activeItemClassName?: string;
  iconClassName?: string;
  childrenClassName?: string;
  renderIcon?: (icon: React.ReactNode | string) => React.ReactNode;
}

/**
 * A menu component that only renders items the user has permission to access
 */
const PermissionAwareMenu: React.FC<PermissionAwareMenuProps> = ({
  items,
  className = '',
  itemClassName = '',
  activeItemClassName = '',
  iconClassName = '',
  childrenClassName = '',
  renderIcon,
}) => {
  const pathname = usePathname();
  const { 
    hasPermission, 
    hasAnyPermission, 
    hasAllPermissions,
    canAccess
  } = usePermissions();

  // Check if the user has permission to access a menu item
  const hasAccessToItem = (item: MenuItem): boolean => {
    if (item.permission) {
      return hasPermission(item.permission);
    } else if (item.anyPermission) {
      return hasAnyPermission(item.anyPermission);
    } else if (item.allPermissions) {
      return hasAllPermissions(item.allPermissions);
    } else if (item.resource) {
      return canAccess(item.resource, item.action || 'read', item.scope || 'any');
    }
    return true;
  };

  // Filter items based on permissions
  const filteredItems = items.filter(hasAccessToItem);

  // If there are no items the user has permission to access, don't render anything
  if (filteredItems.length === 0) {
    return null;
  }

  // Render the menu
  return (
    <ul className={className}>
      {filteredItems.map((item) => {
        const isActive = pathname === item.path;
        const hasChildren = item.children && item.children.length > 0;
        const filteredChildren = hasChildren
          ? item.children!.filter(hasAccessToItem)
          : [];
        
        return (
          <li key={item.path}>
            <Link
              href={item.path}
              className={`${itemClassName} ${isActive ? activeItemClassName : ''}`}
            >
              {item.icon && (
                <span className={iconClassName}>
                  {renderIcon ? renderIcon(item.icon) : item.icon}
                </span>
              )}
              <span>{item.name}</span>
            </Link>
            
            {hasChildren && filteredChildren.length > 0 && (
              <ul className={childrenClassName}>
                {filteredChildren.map((child) => {
                  const isChildActive = pathname === child.path;
                  
                  return (
                    <li key={child.path}>
                      <Link
                        href={child.path}
                        className={`${itemClassName} ${isChildActive ? activeItemClassName : ''}`}
                      >
                        {child.icon && (
                          <span className={iconClassName}>
                            {renderIcon ? renderIcon(child.icon) : child.icon}
                          </span>
                        )}
                        <span>{child.name}</span>
                      </Link>
                    </li>
                  );
                })}
              </ul>
            )}
          </li>
        );
      })}
    </ul>
  );
};

export default PermissionAwareMenu;
