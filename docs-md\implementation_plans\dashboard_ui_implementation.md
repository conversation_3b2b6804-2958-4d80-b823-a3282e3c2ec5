---
description:
globs:
alwaysApply: false
---
# Dashboard UI Implementation Plan (`dashboard_ui_implementation.mdc`)

## 1. Overview

This document outlines the frontend implementation plan for the CallSaver Dashboard section. It details the components, data fetching, state management, user interactions, and error handling based on `dashboard_section_document.mdc` and `component_state_mapping.mdc`. The dashboard serves as the primary overview page for users after login.

## 2. Component Hierarchy

The dashboard UI will be built using the following React components within the Next.js app router structure (`app/dashboard/page.tsx` likely being the entry point). Components will reside in `front/mainpage/components/dashboard/`.

```
/components
  /dashboard
    DashboardLayout.tsx         # Main layout container, fetches initial data.
    MetricCard.tsx              # Reusable card for displaying a single metric (e.g., Total Calls, Active Automations). Props: title, value, icon, isLoading, link?.
    RecentActivityFeed.tsx      # Fetches and displays lists of recent events (calls, messages, etc.). Props: type, limit.
      ActivityItem.tsx          # Renders a single item in the feed. Props: itemData (type, timestamp, details, link).
    QuickLinks.tsx              # Section for static or dynamically generated quick navigation links. Props: links[].
    CreditBalanceWidget.tsx     # Fetches and displays current credit balance. Props: isLoading, balance, linkToBilling.
    AISummaryWidget.tsx         # (Optional) Fetches and displays AI insights. Props: isLoading, insights[].
    DashboardSkeleton.tsx       # Loading state placeholder for the entire dashboard or specific widgets.
  /shared                       # Shared components used across sections
    LoadingSpinner.tsx
    ErrorMessage.tsx
    EmptyState.tsx
```

## 3. Data Sources & Backend Integration

Data will be fetched from the backend API Gateway using React Query (`@tanstack/react-query`) for server state management.

| Component / Feature       | API Endpoint                                                     | Method | React Query Hook        | Query Key                                       | Data Shape (Expected)                                                                                                |
| :------------------------ | :--------------------------------------------------------------- | :----- | :---------------------- | :---------------------------------------------- | :------------------------------------------------------------------------------------------------------------------- |
| **Dashboard Summary**     | `GET /api/dashboard/summary`                                     | GET    | `useQuery`              | `['dashboard', 'summary']`                      | `{ totalCallsToday: number, totalCallsWeek: number, totalMessagesToday: number, activeAutomations: number, creditBalance: number }` |
| **Recent Activity Feed**  | `GET /api/dashboard/recent-activity?type={type}&limit={limit}` | GET    | `useQuery`              | `['dashboard', 'activity', { type, limit }]`    | `[{ id: string, type: 'call'\|'sms'\|'voicemail'\|'alert', timestamp: string, details: string, link?: string }, ...]`      |
| **AI Insights** (Optional)| `GET /api/dashboard/ai-insights?limit={limit}`                 | GET    | `useQuery`              | `['dashboard', 'ai-insights', { limit }]`       | `[{ id: string, timestamp: string, summary: string, link?: string }, ...]`                                           |
| **Credit Balance** (Alt.) | `GET /api/users/me/balance`                                      | GET    | `useQuery`              | `['user', 'balance']` (May use summary data)  | `{ balance: number }`                                                                                                |

**Backend Integration Notes:**
- All API calls will originate from client components or Route Handlers within the Next.js app router.
- Authentication tokens (likely JWT in HttpOnly cookies) will be automatically included by the browser. No manual header manipulation needed for standard calls.
- Base URL for API calls will be configured via environment variables.

## 4. UI State Management

State management follows the strategy defined in `component_state_mapping.mdc`:

- **Server State (React Query):**
    - `useQuery` hooks in `DashboardLayout`, `RecentActivityFeed`, `CreditBalanceWidget`, `AISummaryWidget` will manage fetching, caching, and background refresh for their respective data.
    - Query keys will be structured as shown in the table above.
    - `isLoading`, `isError`, `error`, `data`, `isFetching` states provided by `useQuery` will drive the UI presentation.
- **Global Client State (Zustand/Context):**
    - Minimal use expected on the dashboard itself, potentially consuming `isAuthenticated` or basic user info (like name for greeting) from a global store.
    - Credit balance might be mirrored/synced with a global store if displayed persistently in the main app header.
- **Local Component State (`useState`):**
    - Used for UI elements like potential dropdowns for time range selection on metric cards (if implemented).
    - State within `ActivityItem` if it has interactive elements not tied to the core data.

## 5. UI/UX Behavior

- **Initial Load:** `DashboardLayout` fetches summary data. `DashboardSkeleton` or individual widget skeletons (`MetricCard` skeleton, `RecentActivityFeed` skeleton) are displayed based on `isLoading` state from `useQuery`.
- **Data Display:** Once data is loaded (`data` is available), components render the fetched information. `MetricCard` displays numbers, `RecentActivityFeed` maps over the activity array to render `ActivityItem` components.
- **Empty States:** If `data` is fetched successfully but represents an empty state (e.g., `recent-activity` returns an empty array), display an appropriate message using the shared `EmptyState` component (e.g., "No recent activity").
- **Interactivity:**
    - `MetricCard` values might link to relevant sections (e.g., "Total Calls" links to `/call-logs`).
    - `ActivityItem` components link to detailed views (e.g., a specific call log entry).
    - `QuickLinks` provide direct navigation.
- **Responsiveness:** Components will use TailwindCSS utility classes for responsive layout adjustments across different screen sizes (mobile, tablet, desktop). Flexbox and Grid layouts will be primary tools.
- **Data Refresh:** React Query handles background refetching (`stale-while-revalidate`). A manual refresh button could be added, triggering `queryClient.invalidateQueries(['dashboard'])`.

## 6. Error Handling

- **API Fetch Errors:**
    - If the initial `GET /api/dashboard/summary` fails (`isError` is true in `DashboardLayout`), display a prominent error message using the shared `ErrorMessage` component, potentially covering the main content area, with a retry option (`refetch()` from `useQuery`).
    - If individual widget fetches fail (e.g., `RecentActivityFeed`), display an `ErrorMessage` within that specific widget's boundary, allowing the rest of the dashboard to render.
    - Use toast notifications (e.g., using `react-hot-toast`) for non-critical background refresh errors.
- **Authentication Errors:** If API calls return 401/403, a global error handler (likely integrated with React Query or Axios interceptors) should redirect the user to the login page.
- **Error Messages:** Display user-friendly messages (e.g., "Could not load recent activity.") possibly augmented with error details (`error.message`) in development mode or logged for debugging.

## 7. AI-Enhanced UI

- If the `AISummaryWidget` is implemented:
    - It fetches data from `GET /api/dashboard/ai-insights`.
    - Displays loading state via skeleton.
    - Renders AI-generated summaries or alerts upon successful fetch.
    - Handles empty state ("No AI insights available.") and error state ("Could not load AI insights.").
    - Insights may link to relevant calls, messages, or AI configuration sections.

## 8. Implementation Notes

- Ensure consistent styling using TailwindCSS according to `frontend_guidelines_document.mdc`.
- Prioritize loading performance; consider code splitting if components become very large.
- Implement accessibility best practices (semantic HTML, ARIA attributes).
- Add logging for key events and errors as specified in `dashboard_section_document.mdc`.
