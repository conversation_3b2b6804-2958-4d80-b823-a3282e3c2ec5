import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../lib/apiClient';
import toast from 'react-hot-toast';

// Types
export interface User {
  id: string;
  name: string;
  email: string;
  roleId: string;
  roleName?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateUserRoleParams {
  userId: string;
  roleId: string;
}

/**
 * Hook for managing users
 */
export function useUserManagement() {
  const queryClient = useQueryClient();

  // Fetch users with optional search term
  const usersQuery = (searchTerm: string = '') => {
    return useQuery({
      queryKey: ['admin', 'users', searchTerm],
      queryFn: async () => {
        const response = await api.get<User[]>(`/admin/users${searchTerm ? `?search=${searchTerm}` : ''}`);
        return response;
      },
    });
  };

  // Get user details
  const userDetailsQuery = (userId: string) => {
    return useQuery({
      queryKey: ['admin', 'users', userId],
      queryFn: async () => {
        const response = await api.get<User>(`/admin/users/${userId}`);
        return response;
      },
      enabled: !!userId, // Only run if userId is provided
    });
  };

  // Update user role mutation
  const updateUserRoleMutation = useMutation({
    mutationFn: async (data: UpdateUserRoleParams) => {
      const response = await api.put(`/admin/users/${data.userId}/role`, {
        roleId: data.roleId,
      });
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });
      toast.success('User role updated successfully');
    },
    onError: (error) => {
      toast.error('Failed to update user role');
      console.error('Error updating user role:', error);
    },
  });

  return {
    // Queries
    usersQuery,
    userDetailsQuery,

    // Mutations
    updateUserRole: updateUserRoleMutation.mutate,
    isUpdatingUserRole: updateUserRoleMutation.isPending,
    updateUserRoleError: updateUserRoleMutation.error,

    // Refresh
    refreshUsers: () => queryClient.invalidateQueries({ queryKey: ['admin', 'users'] }),
  };
}
