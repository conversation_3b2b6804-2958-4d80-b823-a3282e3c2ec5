# Permission System Migration Guide

This guide explains how to migrate from the old role-based middleware to the new permission-based middleware.

## Overview

We've implemented a new permission-based access control system that provides more granular control over what actions users can perform. This system replaces the old role-based middleware (`requireRole`, `isAdmin`, `isDeveloper`) with a new permission-based middleware.

## Why Migrate?

The new permission system offers several advantages:

1. **More Granular Control**: Permissions are more specific than roles, allowing for finer-grained access control.
2. **Explicit Permissions**: Roles are explicitly mapped to permissions, preventing privilege escalation.
3. **Resource Ownership**: The system can check if a user owns a resource before allowing access.
4. **Flexible Scoping**: Permissions can be scoped to self, organization, or any resource.

## Migration Steps

### 1. Update Imports

Replace:
```javascript
const { requireRole, isAdmin, isDeveloper } = require('../middleware/authMiddlewareEnhanced');
```

With:
```javascript
const { protect, csrfProtection } = require('../middleware/authMiddlewareEnhanced');
const { requirePermission, requireAnyPermission, requireAllPermissions } = require('../middleware/permissionMiddleware');
```

### 2. Replace Role-Based Middleware

#### Replace `isAdmin`

Replace:
```javascript
router.use(isAdmin);
```

With:
```javascript
router.use(requirePermission('resourceType:*:any')); // Replace resourceType with the appropriate resource
```

#### Replace `isDeveloper`

Replace:
```javascript
router.use(isDeveloper);
```

With:
```javascript
router.use(requirePermission('developer:access:self'));
```

#### Replace `requireRole('admin')`

Replace:
```javascript
router.get('/route', protect, requireRole('admin'), controller.method);
```

With:
```javascript
router.get('/route', protect, requirePermission('resourceType:action:any'), controller.method);
```

#### Replace `requireRole(['admin', 'manager'])`

Replace:
```javascript
router.get('/route', protect, requireRole(['admin', 'manager']), controller.method);
```

With:
```javascript
router.get('/route', protect, requireAnyPermission(['resourceType:action:any', 'resourceType:action:org']), controller.method);
```

### 3. Add Resource Ownership Checks

For routes that need to check resource ownership:

```javascript
// Get a resource by ID from a specific model
const getPhoneNumber = getResourceById('id', 'phoneNumber');

// Use the resource in permission check
router.get('/numbers/:id', protect, requirePermission('phoneNumbers:read:self', getPhoneNumber), controller.getPhoneNumber);
```

## Permission Format

Permissions follow a structured format:

```
resource:action[:scope]
```

Where:
- **resource**: The type of resource being accessed (e.g., `users`, `phoneNumbers`, `calls`)
- **action**: The operation being performed (e.g., `read`, `create`, `update`, `delete`)
- **scope**: Optional scope of access (e.g., `self`, `org`, `any`)

### Special Permission Wildcards

- `*`: Wildcard for all resources or actions (e.g., `*:read`, `users:*`)
- `**`: Super wildcard grants all permissions (only for admin)

## Common Permission Mappings

| Old Middleware | New Permission Middleware |
|----------------|---------------------------|
| `isAdmin` | `requirePermission('**')` |
| `isDeveloper` | `requirePermission('developer:access:self')` |
| `requireRole('admin')` | `requirePermission('resourceType:action:any')` |
| `requireRole('member')` | `requirePermission('resourceType:action:self')` |
| `requireOwnership('id', 'phoneNumber')` | `requirePermission('phoneNumbers:read:self', getResourceById('id', 'phoneNumber'))` |

## Testing

After migrating to the new permission system, make sure to test your routes thoroughly to ensure that access control is working as expected. The new system includes comprehensive tests in:

- `back/backend/tests/services/accessControlService.test.js`
- `back/backend/tests/middleware/permissionMiddleware.test.js`

## Need Help?

If you need help migrating to the new permission system, please refer to the following resources:

- `docs/platform_security/role_based_access_control.mdc` - Documentation for the new permission system
- `back/backend/config/rolePermissions.js` - Configuration file defining role-permission mappings
- `back/backend/services/accessControlService.js` - Service for checking permissions
- `back/backend/middleware/permissionMiddleware.js` - Middleware for enforcing permissions
