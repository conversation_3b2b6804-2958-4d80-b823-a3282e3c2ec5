// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Login command to handle authentication
Cypress.Commands.add('login', (email = '<EMAIL>', password = 'password123') => {
  cy.session([email, password], () => {
    // Visit the login page
    cy.visit('/login');
    
    // Fill in the login form
    cy.get('input[name="email"]').type(email);
    cy.get('input[name="password"]').type(password);
    
    // Submit the form
    cy.get('button[type="submit"]').click();
    
    // Verify that login was successful
    cy.url().should('include', '/dashboard');
  });
});

// Mock command to handle Twilio API responses
Cypress.Commands.add('mockTwilioApi', () => {
  cy.intercept('GET', '/api/numbers/search*', {
    statusCode: 200,
    body: {
      numbers: [
        { phoneNumber: '+15551234567', locality: 'San Francisco', region: 'CA' },
        { phoneNumber: '+15552345678', locality: 'Los Angeles', region: 'CA' },
        { phoneNumber: '+15553456789', locality: 'New York', region: 'NY' }
      ]
    }
  }).as('searchNumbers');
  
  cy.intercept('POST', '/api/numbers/purchase', {
    statusCode: 200,
    body: {
      id: 'new-number-id',
      phoneNumber: '+15551234567',
      status: 'active',
      createdAt: new Date().toISOString()
    }
  }).as('purchaseNumber');
  
  cy.intercept('GET', '/api/numbers', {
    statusCode: 200,
    body: {
      numbers: [
        {
          id: 'number1',
          phoneNumber: '+15551234567',
          status: 'active',
          aiEnabled: true,
          forwardingEnabled: false,
          createdAt: new Date().toISOString()
        }
      ]
    }
  }).as('getNumbers');
});

// Mock command to handle AI API responses
Cypress.Commands.add('mockAiApi', () => {
  cy.intercept('POST', '/api/automation/*/test-response', {
    statusCode: 200,
    body: {
      response: 'This is a test AI response. I am the CallSaver AI assistant. How can I help you today?',
      audioUrl: 'https://example.com/test-audio.mp3'
    }
  }).as('aiResponse');
  
  cy.intercept('GET', '/api/analytics*', {
    statusCode: 200,
    fixture: 'analytics.json'
  }).as('analytics');
});

// Command to wait for loading states to resolve
Cypress.Commands.add('waitForLoadingToComplete', () => {
  // Wait for any loading spinners or states to disappear
  cy.get('.loading-spinner', { timeout: 10000, log: false })
    .should('not.exist');
});
