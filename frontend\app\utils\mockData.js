// Mock data for testing purposes

export const mockPhoneNumbers = [
  {
    id: "pn_1",
    number: "+16205268448",
    friendlyName: "+16205268448 (US)",
    region: "United States",
    countryCode: "US",
    isActive: true,
    capabilities: {
      voice: true,
      sms: true,
      fax: false
    },
    dateCreated: "2023-10-15T14:30:00.000Z"
  },
  {
    id: "pn_2",
    number: "+31970102886898",
    friendlyName: "+31970102886898 (NL)",
    region: "Netherlands",
    countryCode: "NL",
    isActive: true,
    capabilities: {
      voice: true,
      sms: true,
      fax: false
    },
    dateCreated: "2024-01-20T09:45:00.000Z"
  }
];

export const mockCallHistory = {
  totalCalls: 24,
  answeredCalls: 18,
  missedCalls: 6,
  callDuration: "3:45:20",
  callsByDay: [
    { day: "Mon", count: 5 },
    { day: "Tue", count: 3 },
    { day: "Wed", count: 7 },
    { day: "Thu", count: 2 },
    { day: "Fri", count: 4 },
    { day: "Sat", count: 2 },
    { day: "Sun", count: 1 }
  ]
};

export const mockRecentCalls = [
  {
    id: "call_1",
    from: "+14155552671",
    to: "+16205268448",
    duration: 245, // seconds
    status: "completed",
    direction: "inbound",
    timestamp: "2024-04-10T15:30:00.000Z"
  },
  {
    id: "call_2",
    from: "+12125557890",
    to: "+16205268448",
    duration: 0,
    status: "no-answer",
    direction: "inbound",
    timestamp: "2024-04-09T11:15:00.000Z"
  },
  {
    id: "call_3",
    from: "+31970102886898",
    to: "+14155552671",
    duration: 180,
    status: "completed",
    direction: "outbound",
    timestamp: "2024-04-08T09:45:00.000Z"
  }
];

export const mockRecentMessages = [
  {
    id: "msg_1",
    from: "+14155552671",
    to: "+16205268448",
    body: "When will my order be delivered?",
    status: "received",
    timestamp: "2024-04-10T16:45:00.000Z"
  },
  {
    id: "msg_2",
    from: "+16205268448",
    to: "+14155552671",
    body: "Your order #12345 will be delivered tomorrow between 2-4pm.",
    status: "sent",
    timestamp: "2024-04-10T16:50:00.000Z"
  }
];

export const mockVoicemails = [
  {
    id: "vm_1",
    from: "+14155552671",
    to: "+16205268448",
    duration: 45,
    transcription: "Hi, I'm calling about my recent order. Please call me back when you get a chance.",
    status: "new",
    timestamp: "2024-04-09T14:20:00.000Z"
  }
];

export const mockUsageStats = {
  callMinutes: "120:45",
  smsCount: 87,
  aiResponses: 42,
  creditsUsed: 15
};
