---
description: 
globs: 
alwaysApply: false
---
---
description: Defines guidelines and best practices for developing SDKs for the CallSaver API.
---
# Developer SDK Guidelines (`developer_sdk_guidelines.mdc`)

## 1. Purpose and Scope

**Purpose:** To provide clear guidelines, standards, and best practices for the development of official and community Software Development Kits (SDKs) interacting with the CallSaver public API. This ensures consistency, quality, usability, and maintainability across different languages and platforms.

**Scope:**
- Core principles for SDK design.
- Language support priorities.
- Authentication handling (API Keys).
- API endpoint mapping and request/response handling.
- Error handling and reporting.
- Type safety and data modeling.
- Asynchronous operations.
- Configuration and customization.
- Testing requirements.
- Documentation standards.
- Versioning and release strategy.
- Community contribution guidelines (if applicable).

## 2. Core Principles

- **Developer Experience:** Prioritize ease of use, clear documentation, and intuitive API design within the SDK.
- **Consistency:** Maintain consistency with the CallSaver REST API structure and naming conventions.
- **Reliability:** Implement robust error handling, retries for transient network issues, and clear reporting of failures.
- **Performance:** Be mindful of performance implications, especially regarding network requests and object instantiation.
- **Maintainability:** Write clean, well-structured, and testable code.
- **Security:** Handle API keys and sensitive data securely. Do not expose secrets unnecessarily.

## 3. Language Support

- **Priority Languages (Initial):** Define the primary languages for official SDKs (e.g., Node.js/TypeScript, Python).
- **Community Support:** Encourage and provide guidelines for community contributions for other languages.

## 4. Authentication

- SDKs MUST support authentication using API Keys obtained via the CallSaver platform.
- Provide clear mechanisms for users to configure their API Key (e.g., via constructor, environment variables, configuration methods).
- Handle adding the `Authorization: Bearer <API_KEY>` header automatically to requests.
- Refer to `session_management_strategy.mdc` and `api_gateway_routes.mdc` for API key specifics (though SDKs primarily use API keys, not session tokens).

## 5. API Interaction

- **Endpoint Mapping:** Provide methods that clearly map to the public CallSaver REST API endpoints defined in `api_gateway_routes.mdc`.
- **Request Building:** Handle request body serialization (e.g., JSON) and query parameter encoding.
- **Response Parsing:** Deserialize response bodies (e.g., JSON) into language-native objects or structures.
- **HTTP Client:** Utilize a standard, well-maintained HTTP client library for the target language. Allow users to potentially configure or inject their own client for advanced scenarios (e.g., custom proxy settings).

## 6. Error Handling

- Define a clear hierarchy of custom exception/error types specific to the SDK.
- Distinguish between network errors, API errors (4xx, 5xx based on status codes), and SDK-specific errors (e.g., configuration errors).
- Surface error messages and relevant details (e.g., request ID if available from response headers) returned by the API.
- Implement optional, configurable retry logic for transient network errors or specific server-side errors (e.g., 429 Too Many Requests, 503 Service Unavailable) with exponential backoff.

## 7. Type Safety and Data Modeling

- **Strong Typing:** For statically-typed languages (TypeScript, Java, C#), provide strong types for API request parameters and response objects.
- **Data Models:** Define clear data models/classes/structs representing API resources.
- **Validation:** Consider adding optional client-side validation for common parameter formats (e.g., email, phone number format) to provide early feedback, but server-side validation remains the source of truth.

## 8. Asynchronous Operations

- SDK methods performing network requests MUST be asynchronous, using the standard async patterns for the target language (e.g., Promises/async-await in JS/TS, asyncio in Python, Tasks in C#).

## 9. Configuration

- Allow configuration of:
    - API Key.
    - Base API URL (defaulting to production, but allowing overrides for testing/staging).
    - Request timeouts.
    - Retry policies (number of retries, backoff factor).
    - Custom HTTP client/agent (optional).

## 10. Testing

- **Unit Tests:** Cover core SDK logic, data model validation, configuration, and error handling (mocking HTTP requests).
- **Integration Tests:** Test actual interactions against a dedicated test environment or using mock servers (e.g., using tools like `nock` or `msw` for JS) that simulate API behavior, including error responses. Aim for high test coverage.
- Refer to `testing_strategy.mdc`.

## 11. Documentation

- Provide comprehensive documentation including:
    - Installation instructions.
    - Quick start guide.
    - Authentication guide.
    - Detailed examples for each API endpoint/SDK method.
    - API reference for all public classes and methods.
    - Error handling guide.
    - Configuration options.
    - Contribution guidelines (if applicable).
- Use standard documentation generation tools for the target language (e.g., TypeDoc, Sphinx, Javadoc).
- Host documentation publicly (e.g., GitHub Pages, Read the Docs).

## 12. Versioning and Release

- Follow Semantic Versioning (SemVer).
- Maintain a clear `CHANGELOG.md`.
- Publish official SDKs to standard package managers (e.g., npm, PyPI, Maven Central).
- Align SDK versioning with major API versions where breaking changes occur.

## 13. Related Documents

- `docs/functional_specs/api_gateway_routes.mdc`
- `docs/functional_specs/session_management_strategy.mdc`
- `docs/functional_specs/user_roles_and_permissions.mdc`
- `docs/dev_guides/testing_strategy.mdc`
