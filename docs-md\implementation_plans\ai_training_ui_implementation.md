# AI Training UI Implementation Plan

## Overview
This document outlines the implementation plan for the AI Training UI within the CallSaver.app dashboard. This component allows users to manage and configure the AI assistant's training data, voice models, knowledge base, and custom commands.

## Component Hierarchy
```mermaid
graph TD
    A[AITrainingDashboardPage] --> B(TrainingDataManager);
    A --> C(VoiceTester);
    A --> D(VoiceModelSelector);
    A --> E(KnowledgeBaseManager);
    A --> F(CustomCommandConfigurator);

    B --> B1(FileUpload{props: onUploadSuccess});
    B --> B2(DataInputForm{props: onSubmit});
    B --> B3(TrainingDataTable{props: data, onEdit, onDelete});

    C --> C1(AudioRecorder{props: onRecordingComplete});
    C --> C2(PlaybackControls{props: audioUrl});
    C --> C3(RealtimeTranscript{props: transcript});

    D --> D1(ModelDropdown{props: models, selectedModel, onSelect});

    E --> E1(KBArticleList{props: articles, onSelect});
    E --> E2(KBArticleEditor{props: article, onSave});
    E --> E3(VectorDBStatus{props: status});

    F --> F1(CommandList{props: commands, onSelect});
    F --> F2(CommandEditor{props: command, onSave});
```

*   **AITrainingDashboardPage**: Main container component.
    *   State: `currentView` (e.g., 'data', 'test', 'voice', 'kb', 'commands'), `isLoading`
*   **TrainingDataManager**: Handles uploading/inputting training data.
    *   **FileUpload**: Component for uploading files (CSV, TXT). Props: `onUploadSuccess(data)`.
    *   **DataInputForm**: Form for manual data entry. Props: `onSubmit(formData)`.
    *   **TrainingDataTable**: Displays existing training data. Props: `data`, `onEdit(item)`, `onDelete(itemId)`.
*   **VoiceTester**: Interface for real-time voice testing.
    *   **AudioRecorder**: Records user audio. Props: `onRecordingComplete(audioBlob)`.
    *   **PlaybackControls**: Plays back recorded or generated audio. Props: `audioUrl`.
    *   **RealtimeTranscript**: Displays live transcription during testing. Props: `transcript`.
*   **VoiceModelSelector**: Allows selection of OpenAI voice models.
    *   **ModelDropdown**: Dropdown list of available voices. Props: `models`, `selectedModel`, `onSelect(modelId)`.
*   **KnowledgeBaseManager**: Manages knowledge base articles.
    *   **KBArticleList**: Lists existing articles. Props: `articles`, `onSelect(articleId)`.
    *   **KBArticleEditor**: WYSIWYG editor for articles. Props: `article`, `onSave(articleData)`.
    *   **VectorDBStatus**: Shows the status of vector database indexing. Props: `status`.
*   **CustomCommandConfigurator**: Configures custom voice commands.
    *   **CommandList**: Lists defined commands. Props: `commands`, `onSelect(commandId)`.
    *   **CommandEditor**: Form to define command triggers and actions. Props: `command`, `onSave(commandData)`.

## Data Flow
1.  **Data Upload/Input**: User uploads file via `FileUpload` or enters data via `DataInputForm`. Data sent to backend API (`/api/ai/training-data`). `TrainingDataManager` fetches updated list via TanStack Query.
2.  **Voice Testing**: User records audio via `AudioRecorder`. Blob sent to backend (`/api/ai/test-voice`). Backend processes with selected OpenAI model, returns audio response URL and transcript. `VoiceTester` updates state.
3.  **Voice Selection**: User selects model in `ModelDropdown`. `onSelect` updates parent state, potentially triggering backend call (`/api/ai/config`) to save preference.
4.  **KB Management**: User selects article in `KBArticleList`. `KnowledgeBaseManager` fetches article content (`/api/ai/kb/{id}`). User edits in `KBArticleEditor`, `onSave` calls backend (`/api/ai/kb/{id}` or `/api/ai/kb`). Backend handles vector DB updates. Status fetched periodically (`/api/ai/kb/status`).
5.  **Command Config**: User selects command in `CommandList`. `CustomCommandConfigurator` fetches details (`/api/ai/commands/{id}`). User edits in `CommandEditor`, `onSave` calls backend (`/api/ai/commands/{id}` or `/api/ai/commands`).

## API Integration
*   `POST /api/ai/training-data`: Upload/add training data. Body: `{ type: 'file'/'text', content: '...' }`. Returns: `{ success: true }`.
*   `GET /api/ai/training-data`: Fetch list of training data. Returns: `[{ id, type, preview, createdAt }]`.
*   `PUT /api/ai/training-data/{id}`: Update specific training data. Body: `{ content: '...' }`. Returns: `{ success: true }`.
*   `DELETE /api/ai/training-data/{id}`: Delete training data. Returns: `{ success: true }`.
*   `POST /api/ai/test-voice`: Send audio for testing. Body: `{ audioBlob, modelId }`. Returns: `{ audioUrl, transcript }`.
*   `GET /api/ai/config`: Fetch current AI config (selected voice model). Returns: `{ voiceModelId }`.
*   `PUT /api/ai/config`: Update AI config. Body: `{ voiceModelId }`. Returns: `{ success: true }`.
*   `GET /api/ai/voices`: Fetch available OpenAI voice models. Returns: `[{ id, name, description }]`.
*   `GET /api/ai/kb`: Fetch list of KB articles. Returns: `[{ id, title, summary, createdAt }]`.
*   `POST /api/ai/kb`: Create new KB article. Body: `{ title, content }`. Returns: `{ id, ... }`.
*   `GET /api/ai/kb/{id}`: Fetch specific KB article content. Returns: `{ id, title, content, ... }`.
*   `PUT /api/ai/kb/{id}`: Update KB article. Body: `{ title, content }`. Returns: `{ success: true }`.
*   `DELETE /api/ai/kb/{id}`: Delete KB article. Returns: `{ success: true }`.
*   `GET /api/ai/kb/status`: Get vector DB indexing status. Returns: `{ status: 'idle'/'indexing'/'error', progress: 0-100 }`.
*   `GET /api/ai/commands`: Fetch list of custom commands. Returns: `[{ id, triggerPhrase, action, createdAt }]`.
*   `POST /api/ai/commands`: Create new command. Body: `{ triggerPhrase, action }`. Returns: `{ id, ... }`.
*   `GET /api/ai/commands/{id}`: Fetch specific command details. Returns: `{ id, triggerPhrase, action, ... }`.
*   `PUT /api/ai/commands/{id}`: Update command. Body: `{ triggerPhrase, action }`. Returns: `{ success: true }`.
*   `DELETE /api/ai/commands/{id}`: Delete command. Returns: `{ success: true }`.

## State Management
*   **TanStack Query**: Used for fetching, caching, and managing server state related to:
    *   Training data list (`/api/ai/training-data`)
    *   Available voice models (`/api/ai/voices`)
    *   KB article list (`/api/ai/kb`)
    *   KB article content (`/api/ai/kb/{id}`)
    *   KB status (`/api/ai/kb/status`) - potentially with polling
    *   Custom command list (`/api/ai/commands`)
    *   Command details (`/api/ai/commands/{id}`)
    *   AI config (`/api/ai/config`)
*   **Zustand**: Used for managing client-side UI state, such as:
    *   Current view/tab within the AI Training dashboard (`AITrainingDashboardPage` state).
    *   State of forms before submission (e.g., `KBArticleEditor`, `CommandEditor`).
    *   Real-time state during voice testing (recording status, transcript).
    *   Loading states specific to UI interactions not covered by TanStack Query's `isLoading`.

## User Interactions
*   **Uploading Data**: User clicks upload button, selects file. Progress shown. On success, table updates.
*   **Manual Entry**: User types in form, clicks submit. Table updates.
*   **Editing/Deleting Data**: User clicks edit/delete icon in table. Modal/form appears for editing, confirmation for deletion.
*   **Voice Testing**: User clicks record, speaks, clicks stop. Audio sent for processing. Response audio/transcript displayed.
*   **Selecting Voice**: User clicks dropdown, selects a voice. Selection is saved.
*   **Managing KB**: User clicks article to view/edit. Editor loads content. User saves changes. Vector DB status updates periodically.
*   **Managing Commands**: User clicks command to view/edit. Editor loads details. User saves changes.

## Error Handling
*   **API Errors**: Use TanStack Query's error state to display user-friendly messages (e.g., "Failed to load training data"). Provide retry options where applicable. Use toasts for transient errors (e.g., failed save).
*   **Upload Errors**: Display specific errors (e.g., "Invalid file type", "File too large").
*   **Voice Test Errors**: Handle API errors from OpenAI, network issues, microphone access errors. Display clear messages.
*   **Validation Errors**: Use form validation libraries (e.g., react-hook-form with Zod) to provide inline feedback on forms.
*   **KB Indexing Errors**: Display error status from `/api/ai/kb/status` with potential details or logs link.

## Implementation Notes
*   **File Uploads**: Consider using a library like `react-dropzone`. Handle large files potentially with chunking if necessary (backend support required).
*   **Real-time Audio**: Use `MediaRecorder` API for recording. WebSockets might be needed for real-time transcription display during testing if latency is critical.
*   **OpenAI Integration**: Ensure secure handling of API keys (backend). Use latest OpenAI SDKs. Map available voice models dynamically.
*   **Vector Database**: Backend service needs to handle embedding generation and updates asynchronously. UI should reflect the indexing status.
*   **WYSIWYG Editor**: Use a robust library like TipTap or TinyMCE for the KB editor.
*   **Security**: Protect all endpoints with authentication/authorization middleware (NextAuth.js). Validate all user inputs on the backend.

## Related Components
*   **DashboardLayout**: Provides the overall structure and navigation.
*   **NotificationSystem**: Used for displaying success/error toasts.
*   **AuthenticationContext**: Provides user session information.

## Link to prompt_to_mdc_router.mdc
*   **Primary Purpose**: Defines the implementation plan for the AI Training user interface.
*   **Frontend Components**: `AITrainingDashboardPage`, `TrainingDataManager`, `VoiceTester`, `VoiceModelSelector`, `KnowledgeBaseManager`, `CustomCommandConfigurator`, and their sub-components.
*   **API Endpoints**: `/api/ai/training-data`, `/api/ai/test-voice`, `/api/ai/config`, `/api/ai/voices`, `/api/ai/kb`, `/api/ai/commands` (and their variations with IDs).
*   **Dependencies**: Relies on functional specifications defined in `ai_integration_layer.mdc` and potentially `ai_prompt_training_strategy.mdc`. Assumes backend API endpoints are implemented.
