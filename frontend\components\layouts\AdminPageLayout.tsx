import React, { ReactNode } from 'react';
import { useAuthStore } from '../../stores/authStore';
import PermissionGate from '../PermissionGate';

interface AdminPageLayoutProps {
  children: ReactNode;
  title: string;
  description?: string;
}

const AdminPageLayout: React.FC<AdminPageLayoutProps> = ({
  children,
  title,
  description,
}) => {
  const { hasPermission } = useAuthStore();
  
  // Check if user has admin permissions
  const hasAdminAccess = hasPermission('**') || hasPermission('admin:access:any');

  if (!hasAdminAccess) {
    return (
      <div className="flex flex-col items-center justify-center h-[70vh]">
        <div className="text-red-500 text-6xl mb-4">🔒</div>
        <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
        <p className="text-gray-600 mb-6">
          You do not have permission to access this page.
        </p>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">{title}</h1>
        {description && <p className="text-gray-500 mt-1">{description}</p>}
      </div>
      {children}
    </div>
  );
};

export default AdminPageLayout;
