"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { 
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>C<PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from "recharts";
import { CircularProgressbar, buildStyles } from 'react-circular-progressbar';
import 'react-circular-progressbar/dist/styles.css';
import { 
  Phone, 
  MessageSquare, 
  Clock, 
  Calendar,
  Users,
  TrendingUp,
  Filter,
  RefreshCw,
  Download,
  Bot,
  PhoneOff,
  AlertCircle,
  CheckCircle2,
  Timer
} from "lucide-react";
import { format, subDays } from "date-fns";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

// Types
interface AnalyticsData {
  totalCalls: number;
  totalMessages: number;
  totalDuration: number;
  callsByDirection: {
    inbound: number;
    outbound: number;
  };
  messagesByDirection: {
    inbound: number;
    outbound: number;
  };
  callTrend: DailyTrend[];
  messageTrend: DailyTrend[];
  callsByStatus: {
    completed: number;
    noAnswer: number;
    busy: number;
    failed: number;
    canceled: number;
  };
  peakCallHours: {
    hour: number;
    count: number;
  }[];
  topCallers: {
    phoneNumber: string;
    callCount: number;
  }[];
  // New metrics
  missedCalls: number;
  missedCallRate: number;
  aiHandoffRate: number;
  aiHandledCalls: number;
  aiHandledMessages: number;
  smsResponseRate: number;
  smsResponseTime: number; // Average response time in minutes
  organizationId: string; // For tenant-specific analytics
}

interface DailyTrend {
  date: string;
  inbound: number;
  outbound: number;
  total: number;
}

// Stat Card Component
const StatCard = ({ 
  title, 
  value, 
  icon, 
  description, 
  trend 
}: { 
  title: string;
  value: string | number;
  icon: React.ReactNode;
  description?: string;
  trend?: { value: number; positive: boolean };
}) => {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && <p className="text-xs text-muted-foreground">{description}</p>}
        {trend && (
          <div className={`flex items-center mt-1 text-xs ${trend.positive ? 'text-green-600' : 'text-red-600'}`}>
            <TrendingUp className={`h-3 w-3 mr-1 ${!trend.positive && 'transform rotate-180'}`} />
            <span>{trend.value}% from last period</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Chart Card Component
const ChartCard = ({ 
  title, 
  description, 
  children 
}: { 
  title: string;
  description?: string;
  children: React.ReactNode;
}) => {
  return (
    <Card className="h-full">
      <CardHeader className="pb-4">
        <CardTitle className="text-base font-medium">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="pt-0">
        {children}
      </CardContent>
    </Card>
  );
};
