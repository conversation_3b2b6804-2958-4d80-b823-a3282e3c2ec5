"use client";

import { useState, useEffect, useCallback } from 'react';
import { 
  PhoneIcon, 
  CheckCircleIcon, 
  XCircleIcon, 
  ArrowPathIcon, 
  CogIcon, 
  ChartBarIcon,
  PlusCircleIcon,
  PencilIcon,
  TrashIcon,
  ClipboardIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/outline';
import { NumbersAPI } from '../../utils/api';

export default function NumbersList({ numbers = [] }) {
  const [showDetails, setShowDetails] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [phoneNumbers, setPhoneNumbers] = useState(numbers);
  const [error, setError] = useState(null);
  const [copiedNumber, setCopiedNumber] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredNumbers, setFilteredNumbers] = useState([]);
  const [sortBy, setSortBy] = useState('dateCreated');
  const [sortDirection, setSortDirection] = useState('desc');

  // Normalize phone numbers data
  const normalizePhoneNumber = useCallback((num) => ({
    id: num.id || num.sid || Math.random().toString(36).substring(2, 10),
    number: num.number || num.phoneNumber || '',
    friendlyName: num.friendlyName || num.label || num.number || num.phoneNumber || '',
    region: num.region || num.locality || '',
    countryCode: num.countryCode || num.isoCountry || 'US',
    isActive: num.isActive !== undefined ? num.isActive : 
              num.active !== undefined ? num.active : true,
    capabilities: num.capabilities || {
      voice: true,
      sms: true,
      fax: false
    },
    dateCreated: num.createdAt || num.dateCreated || new Date().toISOString()
  }), []);

  // Fetch numbers from API if none are provided
  useEffect(() => {
    if (numbers && numbers.length > 0) {
      const normalizedNumbers = numbers.map(normalizePhoneNumber);
      setPhoneNumbers(normalizedNumbers);
      return;
    }

    const fetchNumbers = async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log('Fetching phone numbers from API...');
        const response = await NumbersAPI.getUserNumbers();
        
        // Try to get locally stored numbers from localStorage
        let localNumbers = [];
        if (typeof window !== 'undefined') {
          try {
            const storedNumbers = localStorage.getItem('callsaver_purchased_numbers');
            if (storedNumbers) {
              localNumbers = JSON.parse(storedNumbers);
            }
          } catch (localStorageError) {
            console.error('Error reading from localStorage:', localStorageError);
          }
        }

        // Handle different response formats
        let processedNumbers = [];
        
        if (response && response.success && response.numbers) {
          processedNumbers = response.numbers;
        } else if (response && response.data && response.data.phoneNumbers) {
          processedNumbers = response.data.phoneNumbers;
        } else if (response && response.data && Array.isArray(response.data)) {
          processedNumbers = response.data;
        } else if (response && Array.isArray(response)) {
          processedNumbers = response;
        }
        
        // Merge API numbers with local storage numbers
        const combinedNumbers = [...processedNumbers, ...localNumbers];
        
        // Create a Set of IDs to deduplicate
        const uniqueIds = new Set();
        const uniqueNumbers = combinedNumbers.filter(num => {
          const id = num.id || num.sid || num.phoneNumber || num.number;
          if (!id || uniqueIds.has(id)) return false;
          uniqueIds.add(id);
          return true;
        });
        
        // Normalize phone numbers
        const normalizedNumbers = uniqueNumbers.map(normalizePhoneNumber);
        
        setPhoneNumbers(normalizedNumbers);
        setError(null);
      } catch (err) {
        console.error('Error fetching phone numbers:', err);
        setError(`Failed to load your phone numbers: ${err.message || 'Unknown error'}. Please try again later.`);
        
        // Even if API fails, try to get numbers from localStorage
        try {
          if (typeof window !== 'undefined') {
            const storedNumbers = localStorage.getItem('callsaver_purchased_numbers');
            if (storedNumbers) {
              const localNumbers = JSON.parse(storedNumbers);
              if (localNumbers.length > 0) {
                const normalizedNumbers = localNumbers.map(normalizePhoneNumber);
                setPhoneNumbers(normalizedNumbers);
                setError(null);
                setIsLoading(false);
                return;
              }
            }
          }
        } catch (localStorageError) {
          console.error('Error reading from localStorage after API failure:', localStorageError);
        }
        
        setPhoneNumbers([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchNumbers();
  }, [numbers, normalizePhoneNumber]);

  // Filter and sort numbers
  useEffect(() => {
    let filtered = [...phoneNumbers];
    
    // Apply search filter
    if (searchTerm) {
      const search = searchTerm.toLowerCase();
      filtered = filtered.filter(number => 
        number.number.toLowerCase().includes(search) ||
        number.friendlyName.toLowerCase().includes(search) ||
        number.region.toLowerCase().includes(search) ||
        number.countryCode.toLowerCase().includes(search)
      );
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      let valueA, valueB;
      
      switch (sortBy) {
        case 'number':
          valueA = a.number;
          valueB = b.number;
          break;
        case 'region':
          valueA = a.region;
          valueB = b.region;
          break;
        case 'dateCreated':
          valueA = new Date(a.dateCreated);
          valueB = new Date(b.dateCreated);
          break;
        case 'status':
          valueA = a.isActive ? 1 : 0;
          valueB = b.isActive ? 1 : 0;
          break;
        default:
          valueA = a.number;
          valueB = b.number;
      }
      
      // Handle string comparison
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return sortDirection === 'asc' 
          ? valueA.localeCompare(valueB) 
          : valueB.localeCompare(valueA);
      }
      
      // Handle date or number comparison
      return sortDirection === 'asc' 
        ? valueA - valueB 
        : valueB - valueA;
    });
    
    setFilteredNumbers(filtered);
  }, [phoneNumbers, searchTerm, sortBy, sortDirection]);

  // Format phone number for display
  const formatPhoneNumber = (phoneNumber) => {
    if (!phoneNumber) return '';

    // Remove any non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');

    // Format based on length
    if (cleaned.length === 10) { // US number
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    } else if (cleaned.length === 11 && cleaned.startsWith('1')) { // US with country code
      return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
    } else if (cleaned.length > 10) { // International
      return `+${cleaned}`;
    }

    // Return original if we can't format it
    return phoneNumber;
  };

  // Handle refresh button click
  const handleRefresh = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await NumbersAPI.getUserNumbers();
      
      // Try to get locally stored numbers from localStorage
      let localNumbers = [];
      if (typeof window !== 'undefined') {
        try {
          const storedNumbers = localStorage.getItem('callsaver_purchased_numbers');
          if (storedNumbers) {
            localNumbers = JSON.parse(storedNumbers);
          }
        } catch (localStorageError) {
          console.error('Error reading from localStorage during refresh:', localStorageError);
        }
      }
      
      // Handle different response formats
      let processedNumbers = [];
      
      if (response && response.success && response.numbers) {
        processedNumbers = response.numbers;
      } else if (response && response.data && response.data.phoneNumbers) {
        processedNumbers = response.data.phoneNumbers;
      } else if (response && response.data && Array.isArray(response.data)) {
        processedNumbers = response.data;
      } else if (response && Array.isArray(response)) {
        processedNumbers = response;
      }
      
      // Merge API numbers with local storage numbers
      const combinedNumbers = [...processedNumbers, ...localNumbers];
      
      // Create a Set of IDs to deduplicate
      const uniqueIds = new Set();
      const uniqueNumbers = combinedNumbers.filter(num => {
        const id = num.id || num.sid || num.phoneNumber || num.number;
        if (!id || uniqueIds.has(id)) return false;
        uniqueIds.add(id);
        return true;
      });
      
      // Normalize phone numbers
      const normalizedNumbers = uniqueNumbers.map(normalizePhoneNumber);
      
      setPhoneNumbers(normalizedNumbers);
    } catch (err) {
      console.error('Error refreshing phone numbers:', err);
      setError('Failed to refresh your phone numbers. Please try again later.');
      
      // Even if API fails, try to get numbers from localStorage
      try {
        if (typeof window !== 'undefined') {
          const storedNumbers = localStorage.getItem('callsaver_purchased_numbers');
          if (storedNumbers) {
            const localNumbers = JSON.parse(storedNumbers);
            if (localNumbers.length > 0) {
              const normalizedNumbers = localNumbers.map(normalizePhoneNumber);
              setPhoneNumbers(normalizedNumbers);
              setError(null);
              setIsLoading(false);
              return;
            }
          }
        }
      } catch (localStorageError) {
        console.error('Error reading from localStorage after refresh API failure:', localStorageError);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Copy number to clipboard
  const copyToClipboard = (number) => {
    navigator.clipboard.writeText(number).then(() => {
      setCopiedNumber(number);
      setTimeout(() => setCopiedNumber(null), 2000);
    });
  };

  // Toggle sort direction or change sort field
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortDirection('asc');
    }
  };

  if (isLoading) {
    return (
      <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-white flex items-center">
            <PhoneIcon className="h-5 w-5 mr-2 text-purple-400" />
            Your Numbers
          </h3>
        </div>
        <div className="mt-4 p-5 flex flex-col items-center justify-center text-center">
          <div className="animate-spin h-8 w-8 border-2 border-purple-500 rounded-full border-t-transparent"></div>
          <p className="text-gray-400 mt-3">Loading your phone numbers...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-white flex items-center">
            <PhoneIcon className="h-5 w-5 mr-2 text-purple-400" />
            Your Numbers
          </h3>
          <button
            onClick={handleRefresh}
            className="p-1.5 bg-gray-800 rounded-full hover:bg-gray-700 transition-colors"
            title="Refresh numbers"
          >
            <ArrowPathIcon className="h-4 w-4 text-gray-400" />
          </button>
        </div>
        <div className="mt-4 p-4 bg-red-900/20 border border-red-800/40 rounded-lg text-red-300 text-sm">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (!phoneNumbers || phoneNumbers.length === 0) {
    return (
      <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-white flex items-center">
            <PhoneIcon className="h-5 w-5 mr-2 text-purple-400" />
            Your Numbers
          </h3>
          <button
            onClick={handleRefresh}
            className="p-1.5 bg-gray-800 rounded-full hover:bg-gray-700 transition-colors"
            title="Refresh numbers"
          >
            <ArrowPathIcon className="h-4 w-4 text-gray-400" />
          </button>
        </div>
        <div className="mt-4 p-5 flex flex-col items-center justify-center text-center">
          <p className="text-gray-400">You don&apos;t have any phone numbers yet.</p>
          <p className="text-gray-500 text-sm mt-2">Purchase a number to start making and receiving calls.</p>
          <a 
            href="/dashboard/buy-number"
            className="mt-4 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm font-medium transition-colors flex items-center"
          >
            <PlusCircleIcon className="h-4 w-4 mr-2" />
            Buy a Number
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-white flex items-center">
          <PhoneIcon className="h-5 w-5 mr-2 text-purple-400" />
          Your Numbers ({phoneNumbers.length})
        </h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleRefresh}
            className="p-1.5 bg-gray-800 rounded-full hover:bg-gray-700 transition-colors"
            title="Refresh numbers"
          >
            <ArrowPathIcon className="h-4 w-4 text-gray-400" />
          </button>
          <a 
            href="/dashboard/buy-number"
            className="p-1.5 bg-gray-800 rounded-full hover:bg-gray-700 transition-colors"
            title="Buy a new number"
          >
            <PlusCircleIcon className="h-4 w-4 text-gray-400" />
          </a>
        </div>
      </div>

      {/* Search and sort controls */}
      <div className="mb-4 flex flex-col sm:flex-row gap-2">
        <div className="relative flex-grow">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search numbers..."
            className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white text-sm"
          />
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300"
            >
              ×
            </button>
          )}
        </div>
        <div className="flex space-x-2">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white text-sm"
          >
            <option value="dateCreated">Sort by: Date</option>
            <option value="number">Sort by: Number</option>
            <option value="region">Sort by: Region</option>
            <option value="status">Sort by: Status</option>
          </select>
          <button
            onClick={() => setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')}
            className="px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none hover:bg-gray-700 text-white text-sm"
          >
            {sortDirection === 'asc' ? '↑' : '↓'}
          </button>
        </div>
      </div>

      <div className="mt-4 space-y-3">
        {filteredNumbers.map((number) => (
          <div key={number.id} className="group">
            <div
              className="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-800/80 transition-colors cursor-pointer"
              onClick={() => setShowDetails(showDetails === number.id ? null : number.id)}
            >
              <div className="flex justify-between items-center">
                <div className="flex-grow">
                  <div className="flex items-center">
                    <p className="text-white font-medium">{formatPhoneNumber(number.number)}</p>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        copyToClipboard(number.number);
                      }}
                      className="ml-2 p-1 text-gray-400 hover:text-gray-300 transition-colors"
                      title="Copy number"
                    >
                      <ClipboardIcon className="h-3.5 w-3.5" />
                    </button>
                    {copiedNumber === number.number && (
                      <span className="ml-2 text-xs text-green-400">Copied!</span>
                    )}
                  </div>
                  <p className="text-gray-400 text-xs">
                    {number.region || 'International'} ·
                    {number.dateCreated ? ` Purchased ${new Date(number.dateCreated).toLocaleDateString()}` : ''}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  {number.isActive ? (
                    <span className="px-2 py-1 bg-green-500/20 text-green-300 rounded-full text-xs font-medium flex items-center">
                      <CheckCircleIcon className="h-3 w-3 mr-1" />
                      Active
                    </span>
                  ) : (
                    <span className="px-2 py-1 bg-red-500/20 text-red-300 rounded-full text-xs font-medium flex items-center">
                      <XCircleIcon className="h-3 w-3 mr-1" />
                      Inactive
                    </span>
                  )}
                </div>
              </div>
            </div>

            {showDetails === number.id && (
              <div className="mt-2 p-4 bg-gray-800/30 rounded-lg border border-gray-700/50 text-sm">
                <h4 className="font-medium text-gray-300 mb-2">Capabilities</h4>
                <div className="flex flex-wrap gap-2 mb-3">
                  {/* Handle both array and object capabilities formats */}
                  {Array.isArray(number.capabilities) ? (
                    // Array format
                    <>
                      {number.capabilities.includes('voice') && (
                        <span className="px-2 py-1 bg-purple-500/20 text-purple-300 rounded-full text-xs">Voice</span>
                      )}
                      {number.capabilities.includes('sms') && (
                        <span className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">SMS</span>
                      )}
                      {number.capabilities.includes('mms') && (
                        <span className="px-2 py-1 bg-green-500/20 text-green-300 rounded-full text-xs">MMS</span>
                      )}
                      {number.capabilities.includes('fax') && (
                        <span className="px-2 py-1 bg-orange-500/20 text-orange-300 rounded-full text-xs">Fax</span>
                      )}
                    </>
                  ) : (
                    // Object format
                    <>
                      {number.capabilities?.voice && (
                        <span className="px-2 py-1 bg-purple-500/20 text-purple-300 rounded-full text-xs">Voice</span>
                      )}
                      {number.capabilities?.sms && (
                        <span className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">SMS</span>
                      )}
                      {number.capabilities?.mms && (
                        <span className="px-2 py-1 bg-green-500/20 text-green-300 rounded-full text-xs">MMS</span>
                      )}
                      {number.capabilities?.fax && (
                        <span className="px-2 py-1 bg-orange-500/20 text-orange-300 rounded-full text-xs">Fax</span>
                      )}
                    </>
                  )}
                </div>

                {/* Additional details */}
                {number.sid && (
                  <div className="mb-3">
                    <h4 className="font-medium text-gray-300 mb-1">Twilio SID</h4>
                    <p className="text-gray-400 text-xs font-mono bg-gray-800/50 p-1.5 rounded overflow-x-auto">
                      {number.sid}
                    </p>
                  </div>
                )}

                <div className="grid grid-cols-2 gap-2 mt-4">
                  <button className="py-1.5 px-3 bg-purple-600/50 hover:bg-purple-600/70 text-white rounded text-xs font-medium transition-colors flex items-center justify-center">
                    <CogIcon className="h-3 w-3 mr-1" />
                    Configure Routing
                  </button>
                  <button className="py-1.5 px-3 bg-gray-700/50 hover:bg-gray-700/70 text-white rounded text-xs font-medium transition-colors flex items-center justify-center">
                    <ChartBarIcon className="h-3 w-3 mr-1" />
                    View Analytics
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
      
      {/* Show message if filtered results are empty but we have numbers */}
      {phoneNumbers.length > 0 && filteredNumbers.length === 0 && (
        <div className="p-4 text-center text-gray-400">
          No numbers match your search. Try a different search term.
        </div>
      )}
    </div>
  );
}
