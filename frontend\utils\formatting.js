/**
 * Formatting Utilities
 * 
 * This module provides utility functions for formatting various types of data
 * including dates, currency values, file sizes, and more.
 */

/**
 * Format a date into a human-readable string
 * 
 * @param {string|Date} dateInput - Date to format
 * @param {Object} options - Formatting options
 * @returns {string} Formatted date string
 */
export function formatDate(dateInput, options = {}) {
  if (!dateInput) return 'N/A';
  
  const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
  
  // Default options for date formatting
  const defaultOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    ...options
  };
  
  try {
    return new Intl.DateTimeFormat('en-US', defaultOptions).format(date);
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid date';
  }
}

/**
 * Format a file size in bytes to a human-readable string
 * 
 * @param {number} bytes - Size in bytes
 * @param {number} decimals - Number of decimal places to show
 * @returns {string} Formatted size string (e.g., "1.5 MB")
 */
export function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  if (!bytes || isNaN(bytes)) return 'N/A';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Format a number as currency
 * 
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (e.g., 'USD', 'EUR')
 * @param {string} locale - Locale to use for formatting
 * @returns {string} Formatted currency string
 */
export function formatCurrency(amount, currency = 'USD', locale = 'en-US') {
  if (amount === undefined || amount === null) return 'N/A';
  
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
    }).format(amount);
  } catch (error) {
    console.error('Error formatting currency:', error);
    return `${amount} ${currency}`;
  }
}

/**
 * Format a phone number in E.164 format to a more readable format
 * 
 * @param {string} phoneNumber - Phone number in E.164 format (e.g., +12125551234)
 * @returns {string} Formatted phone number
 */
export function formatPhoneNumber(phoneNumber) {
  if (!phoneNumber) return 'N/A';
  
  // Handle US phone numbers specifically
  if (phoneNumber.startsWith('+1') && phoneNumber.length === 12) {
    return phoneNumber.replace(/^\+1(\d{3})(\d{3})(\d{4})$/, '($1) $2-$3');
  }
  
  // Generic international format for other numbers
  return phoneNumber.replace(/^\+(\d{1,3})(\d{3})(\d{3})(\d{4})$/, '+$1 $2 $3 $4');
}

/**
 * Format a duration in seconds to a human-readable string
 * 
 * @param {number} seconds - Duration in seconds
 * @param {boolean} includeSeconds - Whether to include seconds in the output
 * @returns {string} Formatted duration string
 */
export function formatDuration(seconds, includeSeconds = true) {
  if (seconds === undefined || seconds === null || isNaN(seconds)) return 'N/A';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  let result = '';
  
  if (hours > 0) {
    result += `${hours}h `;
  }
  
  if (hours > 0 || minutes > 0) {
    result += `${minutes}m `;
  }
  
  if (includeSeconds) {
    result += `${remainingSeconds}s`;
  }
  
  return result.trim();
}

/**
 * Format a percentage value
 * 
 * @param {number} value - Value to format as percentage
 * @param {number} decimals - Number of decimal places to show
 * @returns {string} Formatted percentage string
 */
export function formatPercentage(value, decimals = 1) {
  if (value === undefined || value === null || isNaN(value)) return 'N/A';
  
  return `${value.toFixed(decimals)}%`;
}

/**
 * Truncate a string to a maximum length and add an ellipsis if needed
 * 
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length
 * @param {string} ellipsis - Ellipsis string to add
 * @returns {string} Truncated string
 */
export function truncateText(text, maxLength = 50, ellipsis = '...') {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  
  return text.substring(0, maxLength - ellipsis.length) + ellipsis;
}

/**
 * Format a number with thousands separators
 * 
 * @param {number} value - Number to format
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted number
 */
export function formatNumber(value, decimals = 0) {
  if (value === undefined || value === null || isNaN(value)) return 'N/A';
  
  return value.toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
}
