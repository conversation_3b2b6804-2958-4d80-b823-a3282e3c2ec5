import { PrismaClient } from '@prisma/client';

// Track connection attempts for better error handling
let connectionAttempts = 0;
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY_MS = 1000;

// Initialize the Prisma client singleton
let prismaClientInstance = null;

/**
 * Get a singleton instance of the Prisma client with retry logic
 * @returns {Promise<PrismaClient>} Prisma client instance
 */
export async function getPrismaClient() {
  if (prismaClientInstance) {
    return prismaClientInstance;
  }

  // Create a new client if one doesn't exist yet
  prismaClientInstance = await initPrismaWithRetry();
  return prismaClientInstance;
}

/**
 * Initialize the Prisma client with retry logic
 */
async function initPrismaWithRetry() {
  try {
    // Initialize a new PrismaClient instance
    const prisma = new PrismaClient({
      log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
    });

    // Test the connection with a simple query
    await prisma.$connect();
    
    // Successful connection, reset connection attempts
    connectionAttempts = 0;
    
    // Register cleanup handlers
    registerCleanup(prisma);
    
    return prisma;
  } catch (error) {
    // Increment connection attempts
    connectionAttempts++;
    
    console.error(`Database connection attempt ${connectionAttempts} failed:`, error);
    
    if (connectionAttempts < MAX_RETRY_ATTEMPTS) {
      console.log(`Retrying database connection in ${RETRY_DELAY_MS}ms...`);
      
      // Wait for a delay before retrying
      await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY_MS));
      
      // Retry connection
      return initPrismaWithRetry();
    }
    
    // Max retries reached, throw enhanced error
    const enhancedError = new Error(`Failed to connect to database after ${MAX_RETRY_ATTEMPTS} attempts`);
    enhancedError.originalError = error;
    enhancedError.attemptsMade = connectionAttempts;
    throw enhancedError;
  }
}

/**
 * Registers cleanup handlers for graceful shutdown
 */
function registerCleanup(prisma) {
  // Handle graceful shutdown for Node.js process signals
  const cleanup = async () => {
    try {
      console.log('Closing database connections...');
      await prisma.$disconnect();
      console.log('Database connections closed.');
    } catch (e) {
      console.error('Error during database disconnection:', e);
      process.exit(1);
    }
    
    process.exit(0);
  };

  // Register cleanup handlers
  process.on('SIGINT', cleanup);
  process.on('SIGTERM', cleanup);
}

/**
 * Execute a database operation with automatic retry on transient errors
 * @param {Function} operation Function that performs the database operation
 * @param {Object} options Configuration options
 * @returns {Promise<any>} Result of the database operation
 */
export async function executeWithRetry(operation, options = {}) {
  const {
    retries = 2,
    retryDelay = 500,
    retryableErrors = ['P1000', 'P1001', 'P1002', 'P1008', 'P1017']
  } = options;
  
  let lastError;
  
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const prisma = await getPrismaClient();
      return await operation(prisma);
    } catch (error) {
      lastError = error;
      
      // Check if the error is retryable
      const isRetryable = retryableErrors.includes(error.code) || 
                          error.message.includes('connection') ||
                          error.message.includes('timeout');
                          
      // If we've reached max retries or error isn't retryable, throw
      if (attempt === retries || !isRetryable) {
        console.error(`Database operation failed after ${attempt + 1} attempts:`, error);
        throw error;
      }
      
      // Wait before retrying
      const delay = retryDelay * Math.pow(2, attempt); // Exponential backoff
      console.log(`Retrying database operation in ${delay}ms (attempt ${attempt + 1}/${retries})...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  // This should never be reached due to the throw in the catch block above
  throw lastError;
} 