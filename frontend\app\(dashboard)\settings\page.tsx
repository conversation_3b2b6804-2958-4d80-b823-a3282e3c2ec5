'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import SettingsLayout from '../../../components/settings/SettingsLayout';
import ProfileSettingsPanel from '../../../components/settings/ProfileSettingsPanel';
import SecuritySettingsPanel from '../../../components/settings/SecuritySettingsPanel';
import NotificationPreferencesPanel from '../../../components/settings/NotificationPreferencesPanel';
import ApiKeyManagementPanel from '../../../components/settings/ApiKeyManagementPanel';
import BillingSubscriptionPanel from '../../../components/settings/BillingSubscriptionPanel';
import OrganizationSettingsPanel from '../../../components/settings/OrganizationSettingsPanel';

// Define the available tabs
const TABS = [
  { id: 'profile', label: 'Profile' },
  { id: 'security', label: 'Security' },
  { id: 'notifications', label: 'Notifications' },
  { id: 'api-keys', label: 'API Keys' },
  { id: 'billing', label: 'Billing & Subscription' },
  { id: 'organization', label: 'Organization' }
];

export default function SettingsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get the active tab from URL query parameter or default to 'profile'
  const initialTab = searchParams.get('tab') || 'profile';
  const [activeTab, setActiveTab] = useState(
    TABS.some(tab => tab.id === initialTab) ? initialTab : 'profile'
  );

  // Check if user is admin (for organization tab)
  // This would typically come from a user context or auth state
  const isAdmin = true; // Placeholder - replace with actual admin check

  // Handle tab change
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    
    // Update URL query parameter without full page reload
    const params = new URLSearchParams(searchParams);
    params.set('tab', tabId);
    router.push(`/settings?${params.toString()}`, { scroll: false });
  };

  // Filter tabs based on user role
  const availableTabs = TABS.filter(tab => 
    tab.id !== 'organization' || isAdmin
  );

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-1">
          Manage your account preferences, security, and subscription details
        </p>
      </div>

      <SettingsLayout
        activeTab={activeTab}
        tabs={availableTabs}
        onTabChange={handleTabChange}
      >
        {activeTab === 'profile' && <ProfileSettingsPanel />}
        {activeTab === 'security' && <SecuritySettingsPanel />}
        {activeTab === 'notifications' && <NotificationPreferencesPanel />}
        {activeTab === 'api-keys' && <ApiKeyManagementPanel />}
        {activeTab === 'billing' && <BillingSubscriptionPanel />}
        {activeTab === 'organization' && isAdmin && <OrganizationSettingsPanel />}
      </SettingsLayout>
    </div>
  );
}
