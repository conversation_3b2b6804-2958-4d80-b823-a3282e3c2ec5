V solution. contract contract America. <PERSON>, <PERSON>. <PERSON><PERSON> had EV3 visa.# Environment configuration for eSIM mock testing
# Copy these variables to your .env file to enable mock testing

# Required database URLs (mock values for testing)
DATABASE_URL=postgresql://mock:mock@localhost:5432/mockdb?schema=public
DIRECT_URL=postgresql://mock:mock@localhost:5432/mockdb?schema=public

# eSIM General Configuration
ESIM_ENABLED=true

# Use mock provider for testing
TELEPHONY_DEFAULT_PROVIDER=mock

# Set to true to enable fallback to Twilio if eSIM operations fail
TELEPHONY_PARALLEL_OPERATION=false

# WebRTC Configuration for Voice over eSIM (optional for testing)
WEBRTC_ENABLED=true
WEBRTC_STUN_SERVERS=stun:stun.l.google.com:19302,stun:stun1.l.google.com:19302
