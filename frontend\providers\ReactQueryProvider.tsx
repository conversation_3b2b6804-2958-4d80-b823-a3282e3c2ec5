'use client';

import { ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Define query client configuration options
const defaultQueryClientOptions = {
  queries: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true,
    retry: (failureCount: number, error: any) => {
      // Don't retry on 4xx errors (auth, bad request)
      if (error?.response?.status >= 400 && error?.response?.status < 500) {
        return false;
      }
      return failureCount < 3; // Retry 3 times on other errors
    },
  },
  mutations: {
    onError: (error: Error, variables: any, context: any) => {
      // Global mutation error logging
      console.error('Mutation failed:', error);
      // In a real app, you might show a toast notification here
    },
  },
};

interface ReactQueryProviderProps {
  children: ReactNode;
}

export function ReactQueryProvider({ children }: ReactQueryProviderProps) {
  // Create a client
  const queryClient = new QueryClient({
    defaultOptions: defaultQueryClientOptions,
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {/* Include React Query Devtools in non-production environments */}
      {process.env.NODE_ENV !== 'production' && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  );
}
