'use client';

import { usePathname } from 'next/navigation';
import Navbar from './Navbar';

export default function ConditionalNavbar() {
  const pathname = usePathname();
  
  // Don't show the navbar on dashboard pages
  if (pathname && pathname.startsWith('/dashboard')) {
    return null;
  }
  
  // Apply padding to main content only when navbar is visible
  if (pathname && !pathname.startsWith('/dashboard')) {
    document.documentElement.style.setProperty('--main-padding-top', '7rem');
  } else {
    document.documentElement.style.setProperty('--main-padding-top', '0');
  }
  
  // Show navbar on non-dashboard pages
  return <Navbar />;
} 