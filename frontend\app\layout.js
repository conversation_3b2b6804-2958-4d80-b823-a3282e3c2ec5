import './globals.css';
import { LanguageProvider } from './i18n/LanguageContext';
import GlobalBackgroundOverlay from './components/GlobalBackgroundOverlay';
// Remove direct dynamic import from here
import { Inter } from 'next/font/google';
import { SessionProvider } from './providers/SessionProvider';

// Import dynamically loaded components from the client component
// Temporarily comment out dynamic imports to test layout rendering
// import {
//   ErrorBoundary,
//   PerformanceOptimizer,
//   ConditionalNavbar,
//   WebVitalsInit
// } from './components/DynamicImports';

// Temporarily comment out dynamic imports to test layout rendering
import {
  // ErrorBoundary, // Keep commented
  // PerformanceOptimizer, // Keep commented
  ConditionalNavbar, // Restore this dynamic import
  // WebVitalsInit // Keep commented
} from './components/DynamicImports';

// Use standard ErrorBoundary for now if needed, or remove temporarily
// import ErrorBoundary from './components/ErrorBoundary'; // Keep commented
// import ConditionalNavbar from './components/ConditionalNavbar'; // Remove standard import

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'CallSaver - Never Miss A Customer Call Again',
  description: 'AI-Powered Call Management Platform',
  // Add additional metadata
  openGraph: {
    title: 'CallSaver - Never Miss A Customer Call Again',
    description: 'AI-Powered Call Management Platform',
    type: 'website',
    images: ['/og-image.jpg'],
  },
};

// Define viewport metadata separately (Next.js 14+ requirement)
export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#0d0d17',
};

// Define caching headers for better performance
export const revalidate = 3600; // Revalidate at most once per hour

export default function RootLayout({ children }) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <style dangerouslySetInnerHTML={{
          __html: `
            :root {
              --main-padding-top: 0;
            }
          `
        }} />
        <script dangerouslySetInnerHTML={{
          __html: `
            document.addEventListener('DOMContentLoaded', function() {
              setTimeout(() => {
                document.querySelectorAll('.scroll-reveal').forEach(el => {
                  el.style.opacity = '1';
                  el.style.transform = 'translateY(0)';
                });
              }, 100);
            });
          `
        }} />
      </head>
      <body className={`${inter.className} bg-[#0d0d17] min-h-screen overflow-x-hidden`}>
        <GlobalBackgroundOverlay />
        {/* <PerformanceOptimizer /> */} {/* Commented out */}
        <SessionProvider>
          <LanguageProvider>
            <div className="min-h-screen flex flex-col relative z-10">
              <ConditionalNavbar /> {/* Use the dynamically imported version */}
              {/* <ErrorBoundary showReset={true}> */} {/* Commented out ErrorBoundary wrapper */}
                <main className="flex-grow relative" style={{ paddingTop: 'var(--main-padding-top)' }}>{children}</main>
              {/* </ErrorBoundary> */} {/* Commented out ErrorBoundary wrapper */}
            </div>
          </LanguageProvider>
        </SessionProvider>

        {/* Web Vitals Initialization - Using client component */}
        {/* <WebVitalsInit /> */} {/* Commented out */}
      </body>
    </html>
  );
}
