"use client";

import { motion } from 'framer-motion';
import { useRef, useState, useEffect, useCallback, useMemo } from 'react';
import { useInView } from 'framer-motion';
import { CheckIcon } from '@heroicons/react/24/outline';
import { useLanguage } from '../i18n/LanguageContext';

export default function FeatureCards() {
  const { isRTL } = useLanguage();
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.2 });
  const [particleProps, setParticleProps] = useState([]);
  
  // Memoize the features array to prevent it from recreating on every render
  const features = useMemo(() => [
    {
      id: 1,
      title: "Smart Call Routing",
      description: "Directs calls based on priority and availability, ensuring important calls are never missed and are directed to the right person.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
        </svg>
      ),
      highlights: [
        "Intelligent call prioritization",
        "Customizable routing rules",
        "Automatic call distribution"
      ],
      accentClass: "bg-gradient-to-r from-indigo-500 to-blue-500",
      iconBgClass: "bg-gradient-to-br from-indigo-500/60 to-blue-500/60",
      checkColor: "text-white",
      laserColor: "laser-particle-blue",
      bgClass: "bg-gradient-to-br from-indigo-600/50 to-blue-800/50 border-indigo-400/30",
      titleColor: "text-white",
      descriptionColor: "text-blue-100"
    },
    {
      id: 2,
      title: "AI Call Summaries",
      description: "Get the key points without listening to recordings. Our AI generates concise, accurate summaries of every conversation.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      highlights: [
        "Automated call summaries",
        "Action item extraction",
        "Follow-up reminders"
      ],
      accentClass: "bg-gradient-to-r from-purple-500 to-indigo-500",
      iconBgClass: "bg-gradient-to-br from-purple-500/60 to-indigo-500/60",
      checkColor: "text-white",
      laserColor: "laser-particle-purple",
      bgClass: "bg-gradient-to-br from-purple-600/50 to-indigo-800/50 border-purple-400/30",
      titleColor: "text-white",
      descriptionColor: "text-purple-100"
    },
    {
      id: 3,
      title: "Voice & Text Transcription",
      description: "Every call documented and searchable with high-accuracy transcription that captures even nuanced conversations.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
        </svg>
      ),
      highlights: [
        "Calendar synchronization",
        "CRM data capture",
        "Custom workflow automation"
      ],
      accentClass: "bg-gradient-to-r from-green-500 to-emerald-500",
      iconBgClass: "bg-gradient-to-br from-green-500/60 to-emerald-500/60",
      checkColor: "text-white",
      laserColor: "laser-particle-green",
      bgClass: "bg-gradient-to-br from-green-600/50 to-emerald-800/50 border-green-400/30",
      titleColor: "text-white",
      descriptionColor: "text-green-100"
    },
    {
      id: 4,
      title: "24/7 Revenue Protection",
      description: "Never miss another sales opportunity. Our system works around the clock to ensure every potential customer is engaged and converted.",
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      highlights: [
        "Always-on conversion",
        "After-hours sales capture",
        "Zero missed opportunities"
      ],
      accentClass: "bg-gradient-to-r from-pink-500 to-rose-500",
      iconBgClass: "bg-gradient-to-br from-pink-500/60 to-rose-500/60",
      checkColor: "text-white",
      laserColor: "laser-particle-pink",
      bgClass: "bg-gradient-to-br from-pink-600/50 to-rose-800/50 border-pink-400/30",
      titleColor: "text-white",
      descriptionColor: "text-pink-100"
    }
  ], []);
  
  // Generate random particle properties on client-side only
  useEffect(() => {
    const newParticleProps = [];
    
    features.forEach((feature) => {
      const featureParticles = [];
      for (let i = 0; i < 3; i++) {
        featureParticles.push({
          width: Math.random() * 4 + 2,
          height: Math.random() * 4 + 2,
          left: Math.random() * 80 + 10,
          top: Math.random() * 80 + 10,
          xMove: Math.random() * 40 - 20,
          yMove: Math.random() * 40 - 20,
          duration: Math.random() * 3 + 2
        });
      }
      newParticleProps.push(featureParticles);
    });
    
    setParticleProps(newParticleProps);
  }, [features]);
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const headingVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };
  
  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: (i) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
        ease: "easeOut"
      }
    }),
    hover: {
      y: -8,
      scale: 1.03,
      boxShadow: "0 10px 25px rgba(80, 60, 200, 0.2)",
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10
      }
    }
  };

  return (
    <section id="features" ref={ref} className="py-10 relative overflow-hidden neon-bg-glow">
      {/* Background gradient elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-0 left-1/4 w-1/3 h-1/3 bg-purple-900/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-1/3 h-1/3 bg-blue-900/10 rounded-full blur-3xl"></div>
      </div>
      
      <div className="container max-w-7xl mx-auto px-4">
        <motion.div 
          className="text-center mb-12"
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={headingVariants}
        >
          <h2 className="heading-lg laser-gradient-text mb-4" data-text="Revolutionary AI Voice & Text">
            Revolutionary AI Voice & Text
          </h2>
          <p className="subheading-text max-w-3xl mx-auto">
            Transform how your business handles communication with AI that actually <span className="text-purple-400 font-medium">talks and listens</span> to your customers—not just responds to texts. Save time, increase revenue, and deliver exceptional service 24/7.
          </p>
        </motion.div>
        
        {/* Grid layout for consistent card sizing - improved spacing and consistency */}
        <motion.div 
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 features-grid"
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.id}
              custom={index}
              variants={cardVariants}
              whileHover="hover"
              className={`laser-card p-6 rounded-xl relative group min-h-[320px] flex flex-col ${feature.bgClass} border border-opacity-30 shadow-[0_0_15px_rgba(80,60,200,0.15)] backdrop-blur-md`}
              role="article"
              aria-labelledby={`feature-title-${feature.id}`}
            >
              {/* Card glow effect */}
              <motion.span 
                className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                animate={{
                  boxShadow: [
                    '0 0 0px rgba(128, 0, 255, 0)',
                    '0 0 15px rgba(128, 0, 255, 0.3)',
                    '0 0 0px rgba(128, 0, 255, 0)'
                  ]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              
              {/* Floating particle effect inside card */}
              <div className="absolute inset-0 overflow-hidden rounded-xl pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                {particleProps[index]?.map((particle, i) => (
                  <motion.div
                    key={i}
                    className={`absolute rounded-full ${feature.laserColor}`}
                    style={{
                      width: `${particle.width}px`,
                      height: `${particle.height}px`,
                      left: `${particle.left}%`,
                      top: `${particle.top}%`,
                      opacity: 0.7,
                      filter: 'blur(2px)'
                    }}
                    animate={{
                      x: [0, particle.xMove],
                      y: [0, particle.yMove],
                      opacity: [0.7, 0.9, 0.7]
                    }}
                    transition={{
                      duration: particle.duration,
                      repeat: Infinity,
                      repeatType: 'reverse',
                      ease: 'easeInOut'
                    }}
                  />
                ))}
              </div>
              
              <div className="flex items-start mb-3">
                <div className={`p-2 rounded-lg ${feature.iconBgClass} mr-3 laser-icon`}>
                  <motion.div 
                    className="text-white"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ type: "spring", stiffness: 300, damping: 10 }}
                  >
                    {feature.icon}
                  </motion.div>
                </div>
                <h3 
                  id={`feature-title-${feature.id}`}
                  className={`text-xl font-bold ${feature.titleColor} group-hover:text-white transition-colors duration-300 pt-1`}
                >
                  {feature.title}
                </h3>
              </div>
              
              <p className={`text-base ${feature.descriptionColor} mb-5 leading-relaxed`}>
                {feature.description}
              </p>
              
              <ul className="space-y-2.5 mt-auto">
                {feature.highlights.map((highlight, i) => (
                  <li key={i} className="flex items-center text-sm feature-highlight py-1.5 px-2.5 rounded-md bg-white/5">
                    <motion.span 
                      className={`mr-2.5 ${feature.checkColor} flex-shrink-0`}
                      whileHover={{ scale: 1.2, rotate: 5 }}
                      style={{ animationDelay: `${i * 0.5}s` }}
                      aria-hidden="true"
                    >
                      <CheckIcon className="h-5 w-5" />
                    </motion.span>
                    <span className="text-white">
                      {highlight}
                    </span>
                  </li>
                ))}
              </ul>
              
              {/* Animated accent line */}
              <div className={`h-1 w-16 mt-4 rounded-full ${feature.accentClass} relative overflow-hidden`}>
                <motion.div
                  className="absolute inset-0"
                  animate={{
                    boxShadow: [
                      '0 0 0px rgba(128, 0, 255, 0)',
                      '0 0 10px rgba(128, 0, 255, 0.5)',
                      '0 0 0px rgba(128, 0, 255, 0)'
                    ]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
                <motion.div
                  className="absolute inset-0 bg-white/10"
                  animate={{
                    x: ['-100%', '100%']
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    repeatDelay: 1
                  }}
                />
              </div>
            </motion.div>
          ))}
        </motion.div>
        
        <div className="laser-divider mt-10 mx-auto" />
      </div>
    </section>
  );
} 