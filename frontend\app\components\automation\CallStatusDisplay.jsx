"use client";

import { useState, useEffect } from 'react';
import { <PERSON>Phone, FiPhoneOff, FiPhoneCall, FiPhoneIncoming, FiAlertCircle } from 'react-icons/fi';
import { formatDistanceToNow } from 'date-fns';

export default function CallStatusDisplay({ call }) {
  const [timeAgo, setTimeAgo] = useState('');
  
  useEffect(() => {
    if (!call?.timestamp) return;
    
    // Update the "time ago" text
    const updateTimeAgo = () => {
      setTimeAgo(formatDistanceToNow(new Date(call.timestamp), { addSuffix: true }));
    };
    
    updateTimeAgo();
    const interval = setInterval(updateTimeAgo, 30000); // Update every 30 seconds
    
    return () => clearInterval(interval);
  }, [call?.timestamp]);
  
  if (!call) return null;
  
  const getStatusIcon = () => {
    switch (call.status) {
      case 'initiated':
        return <FiPhone className="text-blue-400" />;
      case 'ringing':
        return <FiPhoneIncoming className="text-yellow-400 animate-pulse" />;
      case 'in-progress':
        return <FiPhoneCall className="text-green-400 animate-pulse" />;
      case 'completed':
        return <FiPhoneOff className="text-green-400" />;
      case 'failed':
      case 'busy':
      case 'no-answer':
        return <FiAlertCircle className="text-red-400" />;
      default:
        return <FiPhone className="text-gray-400" />;
    }
  };
  
  const getStatusColor = () => {
    switch (call.status) {
      case 'initiated': return 'border-blue-400 bg-blue-400/10';
      case 'ringing': return 'border-yellow-400 bg-yellow-400/10';
      case 'in-progress': return 'border-green-400 bg-green-400/10';
      case 'completed': return 'border-green-400 bg-green-400/10';
      case 'failed':
      case 'busy':
      case 'no-answer': 
        return 'border-red-400 bg-red-400/10';
      default: return 'border-gray-400 bg-gray-400/10';
    }
  };
  
  const getStatusText = () => {
    switch (call.status) {
      case 'initiated': return 'Call Initiated';
      case 'ringing': return 'Phone Ringing';
      case 'in-progress': return 'Call in Progress';
      case 'completed': return 'Call Completed';
      case 'failed': return 'Call Failed';
      case 'busy': return 'Number Busy';
      case 'no-answer': return 'No Answer';
      default: return 'Unknown Status';
    }
  };
  
  return (
    <div className={`flex items-center p-3 rounded-md border ${getStatusColor()} mb-3`}>
      <div className="mr-3 text-xl">
        {getStatusIcon()}
      </div>
      
      <div className="flex-1">
        <div className="flex justify-between">
          <div className="font-medium">{getStatusText()}</div>
          <div className="text-sm text-gray-400">{timeAgo}</div>
        </div>
        
        <div className="flex justify-between mt-1">
          <div className="text-sm">
            <span className="text-gray-400">To:</span> {call.to}
          </div>
          
          {call.duration !== undefined && (
            <div className="text-sm">
              <span className="text-gray-400">Duration:</span> {formatDuration(call.duration)}
            </div>
          )}
        </div>
        
        {call.errorMessage && (
          <div className="text-sm text-red-400 mt-1">{call.errorMessage}</div>
        )}
      </div>
    </div>
  );
}

function formatDuration(seconds) {
  if (!seconds) return '0s';
  
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  
  if (mins === 0) return `${secs}s`;
  return `${mins}m ${secs}s`;
} 