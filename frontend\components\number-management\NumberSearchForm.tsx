'use client';

import { useState } from 'react';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { SearchParams } from '../../hooks/useNumberManagement';

interface NumberSearchFormProps {
  onSubmit: (params: SearchParams) => void;
  isSearching: boolean;
}

// Country options for the dropdown
const COUNTRY_OPTIONS = [
  { value: 'US', label: 'United States (+1)' },
  { value: 'CA', label: 'Canada (+1)' },
  { value: 'GB', label: 'United Kingdom (+44)' },
  { value: 'AU', label: 'Australia (+61)' },
  // Add more countries as needed
];

// Number type options
const NUMBER_TYPE_OPTIONS = [
  { value: 'local', label: 'Local' },
  { value: 'tollfree', label: 'Toll-Free' },
  { value: 'mobile', label: 'Mobile' },
];

// Capability options
const CAPABILITY_OPTIONS = [
  { value: 'voice', label: 'Voice' },
  { value: 'sms', label: 'SMS' },
  { value: 'mms', label: 'MMS' },
  { value: 'fax', label: 'Fax' },
];

export default function NumberSearchForm({ onSubmit, isSearching }: NumberSearchFormProps) {
  const [country, setCountry] = useState('US');
  const [areaCode, setAreaCode] = useState('');
  const [capabilities, setCapabilities] = useState<string[]>(['voice', 'sms']);
  const [type, setType] = useState('local');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      country,
      areaCode,
      capabilities,
      type
    });
  };

  const handleCapabilityChange = (capability: string) => {
    if (capabilities.includes(capability)) {
      setCapabilities(capabilities.filter(c => c !== capability));
    } else {
      setCapabilities([...capabilities, capability]);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sm:p-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-4 sm:mb-6">
        {/* Country Selection */}
        <div>
          <label htmlFor="country" className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Country
          </label>
          <select
            id="country"
            name="country"
            className="block w-full pl-3 pr-10 py-2 text-sm border border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md bg-white dark:bg-gray-800"
            value={country}
            onChange={(e) => setCountry(e.target.value)}
          >
            {COUNTRY_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Area Code */}
        <div>
          <label htmlFor="areaCode" className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Area Code
          </label>
          <input
            type="text"
            name="areaCode"
            id="areaCode"
            placeholder="e.g. 415"
            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm bg-white dark:bg-gray-800"
            value={areaCode}
            onChange={(e) => setAreaCode(e.target.value)}
          />
        </div>

        {/* Number Type */}
        <div>
          <label htmlFor="type" className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Number Type
          </label>
          <select
            id="type"
            name="type"
            className="block w-full pl-3 pr-10 py-2 text-sm border border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md bg-white dark:bg-gray-800"
            value={type}
            onChange={(e) => setType(e.target.value)}
          >
            {NUMBER_TYPE_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Search Button (on mobile, this will be full width below the inputs) */}
        <div className="sm:hidden">
          <button
            type="submit"
            disabled={isSearching}
            className="w-full flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSearching ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Searching...
              </>
            ) : (
              <>
                <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                Search
              </>
            )}
          </button>
        </div>
      </div>

      {/* Capabilities */}
      <div className="mb-4 sm:mb-6">
        <label className="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Capabilities
        </label>
        <div className="flex flex-wrap gap-2 sm:gap-4">
          {CAPABILITY_OPTIONS.map((option) => (
            <label key={option.value} className="inline-flex items-center">
              <input
                type="checkbox"
                className="form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                checked={capabilities.includes(option.value)}
                onChange={() => handleCapabilityChange(option.value)}
              />
              <span className="ml-2 text-xs sm:text-sm text-gray-700 dark:text-gray-300">{option.label}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Search Button (hidden on mobile, shown on larger screens) */}
      <div className="hidden sm:block">
        <button
          type="submit"
          disabled={isSearching}
          className="flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isSearching ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Searching...
            </>
          ) : (
            <>
              <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
              Search Numbers
            </>
          )}
        </button>
      </div>
    </form>
  );
}
