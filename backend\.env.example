# Server Configuration
PORT=3006

# Database
DATABASE_URL="postgresql://postgres.fayjmfdjudtldfpwxlpu:[YOUR-PASSWORD]@aws-0-eu-central-1.pooler.supabase.co:6543/postgres?pgbouncer=true"
DIRECT_URL="postgresql://postgres.fayjmfdjudtldfpwxlpu:[YOUR-PASSWORD]@aws-0-eu-central-1.pooler.supabase.co:5432/postgres"

# Twilio Configuration
TWILIO_ACCOUNT_SID="your_twilio_account_sid"
TWILIO_AUTH_TOKEN="your_twilio_auth_token"

# JWT Authentication
JWT_SECRET="your-secret-key-for-jwt-token-generation"
JWT_EXPIRES_IN="1d"

# External Services
OPENAI_API_KEY="your_openai_api_key"

# Stripe
STRIPE_SECRET_KEY="your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="your_stripe_webhook_secret"

# CORS Configuration
FRONTEND_URL="http://localhost:3000"

# Logging
LOG_LEVEL="info"
