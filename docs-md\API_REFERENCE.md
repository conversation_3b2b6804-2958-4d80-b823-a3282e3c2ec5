---
title: CallSaver.app API Reference
description: Comprehensive API documentation for the CallSaver.app backend services
date: 2025-04-28
status: Required
---

# CallSaver.app API Reference

## Overview

This document provides a comprehensive reference for all API endpoints in the CallSaver.app application. It includes authentication requirements, request/response formats, and example usage.

## Base URL

- **Development**: `http://localhost:3000/api`
- **Staging**: `https://staging-api.callsaver.app/api`
- **Production**: `https://api.callsaver.app/api`

## Authentication

Most API endpoints require authentication using JWT tokens. Include the token in the `Authorization` header:

```
Authorization: Bearer <jwt_token>
```

To obtain a token, use the `/api/auth/login` endpoint.

### Cross-Site Request Forgery (CSRF) Protection

For browser-based requests, include the CSRF token in the `X-CSRF-Token` header. The token can be obtained from the `/api/auth/csrf-token` endpoint.

## Error Handling

All API errors follow a standard format:

```json
{
  "status": "error",
  "code": "ERROR_CODE",
  "message": "Human-readable error message",
  "details": {
    // Additional error details (optional)
  }
}
```

Common error codes:
- `UNAUTHORIZED`: Authentication required or invalid
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `VALIDATION_ERROR`: Invalid input data
- `RATE_LIMITED`: Too many requests
- `INTERNAL_ERROR`: Server error

## Rate Limiting

API endpoints are subject to rate limiting. The current limits are:
- Authentication endpoints: 10 requests per minute
- Standard endpoints: 60 requests per minute
- Sensitive operations: 30 requests per minute

When rate limited, the API returns a `429 Too Many Requests` status code with headers:
- `X-RateLimit-Limit`: Maximum allowed requests
- `X-RateLimit-Remaining`: Remaining requests in the window
- `X-RateLimit-Reset`: Time in seconds until the limit resets

## API Endpoints

### Authentication

#### Login

```
POST /api/auth/login
```

Request body:
```json
{
  "email": "<EMAIL>",
  "password": "secure_password"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "name": "User Name",
      "role": "member"
    }
  }
}
```

#### Refresh Token

```
POST /api/auth/refresh-token
```

Request body:
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

#### Logout

```
POST /api/auth/logout
```

Response:
```json
{
  "status": "success",
  "message": "Logged out successfully"
}
```

#### Get CSRF Token

```
GET /api/auth/csrf-token
```

Response:
```json
{
  "status": "success",
  "data": {
    "csrfToken": "abcdef123456"
  }
}
```

### User Management

#### Get Current User

```
GET /api/users/me
```

Response:
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "name": "User Name",
      "credits": 100,
      "role": "member",
      "organizationId": "org_id",
      "createdAt": "2025-01-01T00:00:00.000Z",
      "updatedAt": "2025-04-01T00:00:00.000Z"
    }
  }
}
```

#### Update User Profile

```
PATCH /api/users/me
```

Request body:
```json
{
  "name": "Updated Name",
  "phone": "+1234567890"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>",
      "name": "Updated Name",
      "phone": "+1234567890",
      "updatedAt": "2025-04-28T00:00:00.000Z"
    }
  }
}
```

#### Change Password

```
POST /api/users/change-password
```

Request body:
```json
{
  "currentPassword": "current_password",
  "newPassword": "new_password",
  "confirmPassword": "new_password"
}
```

Response:
```json
{
  "status": "success",
  "message": "Password updated successfully"
}
```

### Phone Number Management

#### List Phone Numbers

```
GET /api/numbers
```

Response:
```json
{
  "status": "success",
  "data": {
    "numbers": [
      {
        "id": "number_id_1",
        "number": "+12345678901",
        "countryCode": "US",
        "provider": "twilio",
        "providerId": "twilio_sid",
        "capabilities": {
          "voice": true,
          "sms": true,
          "mms": false
        },
        "status": "active",
        "assignedAt": "2025-01-01T00:00:00.000Z"
      },
      // More numbers...
    ],
    "pagination": {
      "totalItems": 10,
      "itemsPerPage": 25,
      "currentPage": 1,
      "totalPages": 1
    }
  }
}
```

Query parameters:
- `page` (default: 1): Page number
- `limit` (default: 25): Items per page
- `status` (optional): Filter by status (`active`, `inactive`, `porting`)

#### Get Phone Number Details

```
GET /api/numbers/:numberId
```

Response:
```json
{
  "status": "success",
  "data": {
    "number": {
      "id": "number_id",
      "number": "+12345678901",
      "countryCode": "US",
      "provider": "twilio",
      "providerId": "twilio_sid",
      "capabilities": {
        "voice": true,
        "sms": true,
        "mms": false
      },
      "status": "active",
      "assignedAt": "2025-01-01T00:00:00.000Z",
      "configuration": {
        "forwardingEnabled": true,
        "forwardingNumber": "+10987654321",
        "voicemailEnabled": true,
        "transcriptionEnabled": true,
        "aiAssistantEnabled": false
      }
    }
  }
}
```

#### Purchase New Number

```
POST /api/numbers/purchase
```

Request body:
```json
{
  "countryCode": "US",
  "areaCode": "415",
  "capabilities": {
    "voice": true,
    "sms": true
  }
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "number": {
      "id": "number_id",
      "number": "+14155551234",
      "countryCode": "US",
      "provider": "twilio",
      "providerId": "twilio_sid",
      "capabilities": {
        "voice": true,
        "sms": true,
        "mms": false
      },
      "status": "active",
      "assignedAt": "2025-04-28T00:00:00.000Z"
    },
    "creditsUsed": 10
  }
}
```

#### Search Available Numbers

```
GET /api/numbers/available
```

Query parameters:
- `countryCode` (required): Country code (e.g., "US")
- `areaCode` (optional): Area code (e.g., "415")
- `contains` (optional): Pattern to match in number
- `limit` (default: 10): Number of results to return

Response:
```json
{
  "status": "success",
  "data": {
    "availableNumbers": [
      {
        "number": "+14155551234",
        "friendlyName": "(415) 555-1234",
        "countryCode": "US",
        "capabilities": {
          "voice": true,
          "sms": true,
          "mms": false
        },
        "costInCredits": 10
      },
      // More numbers...
    ]
  }
}
```

#### Update Number Configuration

```
PATCH /api/numbers/:numberId
```

Request body:
```json
{
  "configuration": {
    "forwardingEnabled": true,
    "forwardingNumber": "+10987654321",
    "voicemailEnabled": true,
    "transcriptionEnabled": true,
    "aiAssistantEnabled": true
  }
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "number": {
      "id": "number_id",
      "number": "+12345678901",
      "configuration": {
        "forwardingEnabled": true,
        "forwardingNumber": "+10987654321",
        "voicemailEnabled": true,
        "transcriptionEnabled": true,
        "aiAssistantEnabled": true
      },
      "updatedAt": "2025-04-28T00:00:00.000Z"
    }
  }
}
```

#### Release Number

```
DELETE /api/numbers/:numberId
```

Response:
```json
{
  "status": "success",
  "message": "Number released successfully"
}
```

### Call Logs

#### List Call Logs

```
GET /api/call-logs
```

Query parameters:
- `page` (default: 1): Page number
- `limit` (default: 25): Items per page
- `phoneNumberId` (optional): Filter by phone number
- `direction` (optional): Filter by direction (`inbound`, `outbound-api`, `outbound-dial`)
- `status` (optional): Filter by status
- `startDate` (optional): Filter by start date (ISO format)
- `endDate` (optional): Filter by end date (ISO format)

Response:
```json
{
  "status": "success",
  "data": {
    "callLogs": [
      {
        "id": "call_id_1",
        "callSid": "twilio_call_sid",
        "from": "+10987654321",
        "to": "+12345678901",
        "status": "completed",
        "direction": "inbound",
        "duration": 120,
        "startTime": "2025-04-28T10:00:00.000Z",
        "endTime": "2025-04-28T10:02:00.000Z",
        "hasRecording": true,
        "hasTranscription": true,
        "hasSummary": true,
        "createdAt": "2025-04-28T10:00:00.000Z"
      },
      // More call logs...
    ],
    "pagination": {
      "totalItems": 50,
      "itemsPerPage": 25,
      "currentPage": 1,
      "totalPages": 2
    }
  }
}
```

#### Get Call Log Details

```
GET /api/call-logs/:callId
```

Response:
```json
{
  "status": "success",
  "data": {
    "callLog": {
      "id": "call_id",
      "callSid": "twilio_call_sid",
      "from": "+10987654321",
      "to": "+12345678901",
      "status": "completed",
      "direction": "inbound",
      "duration": 120,
      "price": 0.015,
      "priceUnit": "USD",
      "startTime": "2025-04-28T10:00:00.000Z",
      "endTime": "2025-04-28T10:02:00.000Z",
      "recordingSid": "recording_sid",
      "recordingUrl": "https://api.twilio.com/recordings/recording_sid",
      "recordingDuration": 120,
      "hasRecording": true,
      "transcriptionSid": "transcription_sid",
      "transcriptionText": "This is the transcribed text of the call...",
      "transcriptionStatus": "completed",
      "hasTranscription": true,
      "sentimentScore": 0.75,
      "sentimentData": {
        "overall": 0.75,
        "segments": [
          {"text": "...", "score": 0.8},
          {"text": "...", "score": 0.7}
        ]
      },
      "keywords": [
        {"text": "appointment", "relevance": 0.9},
        {"text": "schedule", "relevance": 0.85}
      ],
      "entities": [
        {"type": "DATE", "text": "next Monday", "relevance": 0.95},
        {"type": "TIME", "text": "3 PM", "relevance": 0.95}
      ],
      "summaryText": "Caller requested to schedule an appointment for next Monday at 3 PM.",
      "summarySentiment": "positive",
      "summaryTopics": ["appointment", "scheduling"],
      "summaryActionItems": ["Schedule appointment for Monday at 3 PM"],
      "summaryStatus": "completed",
      "hasSummary": true,
      "createdAt": "2025-04-28T10:00:00.000Z",
      "updatedAt": "2025-04-28T10:05:00.000Z"
    }
  }
}
```

#### Get Call Recording

```
GET /api/call-logs/:callId/recording
```

This endpoint streams the call recording audio file.

#### Delete Call Log

```
DELETE /api/call-logs/:callId
```

Response:
```json
{
  "status": "success",
  "message": "Call log deleted successfully"
}
```

### Messages

#### List Messages

```
GET /api/messages
```

Query parameters:
- `page` (default: 1): Page number
- `limit` (default: 25): Items per page
- `phoneNumberId` (optional): Filter by phone number
- `direction` (optional): Filter by direction (`inbound`, `outbound`)
- `status` (optional): Filter by status
- `startDate` (optional): Filter by start date (ISO format)
- `endDate` (optional): Filter by end date (ISO format)

Response:
```json
{
  "status": "success",
  "data": {
    "messages": [
      {
        "id": "message_id_1",
        "messageSid": "twilio_message_sid",
        "from": "+10987654321",
        "to": "+12345678901",
        "body": "Hello there!",
        "direction": "inbound",
        "status": "received",
        "createdAt": "2025-04-28T10:00:00.000Z"
      },
      // More messages...
    ],
    "pagination": {
      "totalItems": 50,
      "itemsPerPage": 25,
      "currentPage": 1,
      "totalPages": 2
    }
  }
}
```

#### Get Message Details

```
GET /api/messages/:messageId
```

Response:
```json
{
  "status": "success",
  "data": {
    "message": {
      "id": "message_id",
      "messageSid": "twilio_message_sid",
      "from": "+10987654321",
      "to": "+12345678901",
      "body": "Hello there!",
      "direction": "inbound",
      "status": "received",
      "numSegments": 1,
      "price": 0.0075,
      "priceUnit": "USD",
      "sentimentScore": 0.65,
      "sentimentLabel": "positive",
      "aiProcessed": true,
      "aiResponse": "How can I help you today?",
      "createdAt": "2025-04-28T10:00:00.000Z",
      "updatedAt": "2025-04-28T10:00:05.000Z"
    }
  }
}
```

#### Send Message

```
POST /api/messages
```

Request body:
```json
{
  "to": "+10987654321",
  "from": "+12345678901",
  "body": "This is a test message"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "message": {
      "id": "message_id",
      "messageSid": "twilio_message_sid",
      "from": "+12345678901",
      "to": "+10987654321",
      "body": "This is a test message",
      "direction": "outbound",
      "status": "queued",
      "createdAt": "2025-04-28T10:00:00.000Z"
    }
  }
}
```

#### Delete Message

```
DELETE /api/messages/:messageId
```

Response:
```json
{
  "status": "success",
  "message": "Message deleted successfully"
}
```

### AI Assistant

#### Configure AI Assistant

```
PUT /api/numbers/:phoneNumberId/ai-assistant
```

Request body:
```json
{
  "enabled": true,
  "personality": "friendly",
  "responseStyle": "concise",
  "initialGreeting": "Hello! This is the AI assistant for Example Company.",
  "capabilities": {
    "callSummary": true,
    "sentimentAnalysis": true,
    "appointmentScheduling": true,
    "faq": true
  },
  "knowledgeBaseIds": ["kb_123", "kb_456"]
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "aiAssistant": {
      "phoneNumberId": "number_id",
      "enabled": true,
      "personality": "friendly",
      "responseStyle": "concise",
      "initialGreeting": "Hello! This is the AI assistant for Example Company.",
      "capabilities": {
        "callSummary": true,
        "sentimentAnalysis": true,
        "appointmentScheduling": true,
        "faq": true
      },
      "knowledgeBaseIds": ["kb_123", "kb_456"],
      "updatedAt": "2025-04-28T10:00:00.000Z"
    }
  }
}
```

#### Add Knowledge Base Document

```
POST /api/numbers/:phoneNumberId/ai-assistant/knowledge
```

Request body:
```json
{
  "title": "Product Information",
  "content": "Our product has the following features: ...",
  "type": "faq",
  "metadata": {
    "category": "product",
    "tags": ["features", "specifications"]
  }
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "document": {
      "id": "doc_id",
      "title": "Product Information",
      "type": "faq",
      "metadata": {
        "category": "product",
        "tags": ["features", "specifications"]
      },
      "createdAt": "2025-04-28T10:00:00.000Z"
    }
  }
}
```

### Automation

#### List Automation Rules

```
GET /api/automations
```

Response:
```json
{
  "status": "success",
  "data": {
    "automationRules": [
      {
        "id": "rule_id_1",
        "name": "After Hours Greeting",
        "trigger": {
          "type": "incomingCall",
          "conditions": {
            "timeRange": {
              "start": "18:00",
              "end": "09:00",
              "timezone": "America/New_York"
            },
            "phoneNumberId": "number_id"
          }
        },
        "actions": [
          {
            "type": "playMessage",
            "data": {
              "message": "Thank you for calling. Our office is currently closed.",
              "voice": "female"
            }
          },
          {
            "type": "voicemail"
          }
        ],
        "status": "active",
        "createdAt": "2025-01-01T00:00:00.000Z",
        "updatedAt": "2025-01-01T00:00:00.000Z"
      },
      // More rules...
    ]
  }
}
```

#### Create Automation Rule

```
POST /api/automations
```

Request body:
```json
{
  "name": "SMS Auto-Reply",
  "trigger": {
    "type": "incomingSms",
    "conditions": {
      "phoneNumberId": "number_id",
      "keywords": ["help", "support", "assistance"]
    }
  },
  "actions": [
    {
      "type": "sendSms",
      "data": {
        "message": "Thank you for contacting us. A representative will respond shortly."
      }
    },
    {
      "type": "notification",
      "data": {
        "type": "email",
        "recipient": "<EMAIL>",
        "subject": "New Support SMS Received",
        "message": "A new support SMS has been received and auto-reply sent."
      }
    }
  ],
  "status": "active"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "automationRule": {
      "id": "rule_id",
      "name": "SMS Auto-Reply",
      "trigger": {
        "type": "incomingSms",
        "conditions": {
          "phoneNumberId": "number_id",
          "keywords": ["help", "support", "assistance"]
        }
      },
      "actions": [
        {
          "type": "sendSms",
          "data": {
            "message": "Thank you for contacting us. A representative will respond shortly."
          }
        },
        {
          "type": "notification",
          "data": {
            "type": "email",
            "recipient": "<EMAIL>",
            "subject": "New Support SMS Received",
            "message": "A new support SMS has been received and auto-reply sent."
          }
        }
      ],
      "status": "active",
      "createdAt": "2025-04-28T10:00:00.000Z",
      "updatedAt": "2025-04-28T10:00:00.000Z"
    }
  }
}
```

### Dashboard

#### Get Dashboard Summary

```
GET /api/dashboard/summary
```

Response:
```json
{
  "status": "success",
  "data": {
    "totalCallsToday": 12,
    "totalCallsWeek": 78,
    "totalMessagesToday": 25,
    "totalMessagesWeek": 143,
    "activeAutomations": 5,
    "creditBalance": 85,
    "activeNumbers": 3
  }
}
```

#### Get Recent Activity

```
GET /api/dashboard/recent-activity
```

Query parameters:
- `type` (optional): Filter by activity type (`call`, `sms`, `voicemail`, `alert`)
- `limit` (default: 10): Number of results to return

Response:
```json
{
  "status": "success",
  "data": {
    "activities": [
      {
        "id": "activity_id_1",
        "type": "call",
        "timestamp": "2025-04-28T10:30:00.000Z",
        "details": {
          "from": "+10987654321",
          "to": "+12345678901",
          "duration": 120,
          "status": "completed"
        },
        "link": "/call-logs/call_id_1"
      },
      {
        "id": "activity_id_2",
        "type": "sms",
        "timestamp": "2025-04-28T10:15:00.000Z",
        "details": {
          "from": "+12345678901",
          "to": "+10987654321",
          "body": "Hello there!",
          "status": "delivered"
        },
        "link": "/messages/message_id_2"
      },
      // More activities...
    ]
  }
}
```

### Credits

#### Get Credit Balance

```
GET /api/credits/balance
```

Response:
```json
{
  "status": "success",
  "data": {
    "balance": 85,
    "lastUpdated": "2025-04-28T09:00:00.000Z"
  }
}
```

#### Get Credit Transaction History

```
GET /api/credits/transactions
```

Query parameters:
- `page` (default: 1): Page number
- `limit` (default: 25): Items per page
- `type` (optional): Filter by transaction type (`purchase`, `usage`, `refund`, `adjustment`)
- `startDate` (optional): Filter by start date (ISO format)
- `endDate` (optional): Filter by end date (ISO format)

Response:
```json
{
  "status": "success",
  "data": {
    "transactions": [
      {
        "id": "transaction_id_1",
        "type": "purchase",
        "amount": 100,
        "balance": 180,
        "description": "Credit purchase",
        "reference": "payment_id_123",
        "createdAt": "2025-04-25T08:00:00.000Z"
      },
      {
        "id": "transaction_id_2",
        "type": "usage",
        "amount": -10,
        "balance": 170,
        "description": "Phone number purchase",
        "reference": "number_id_456",
        "createdAt": "2025-04-25T09:00:00.000Z"
      },
      // More transactions...
    ],
    "pagination": {
      "totalItems": 50,
      "itemsPerPage": 25,
      "currentPage": 1,
      "totalPages": 2
    }
  }
}
```

#### Purchase Credits

```
POST /api/credits/purchase
```

Request body:
```json
{
  "amount": 100,
  "paymentMethodId": "pm_card_visa",
  "couponCode": "WELCOME20"
}
```

Response:
```json
{
  "status": "success",
  "data": {
    "transaction": {
      "id": "transaction_id",
      "type": "purchase",
      "amount": 100,
      "balance": 185,
      "description": "Credit purchase",
      "reference": "payment_id_789",
      "createdAt": "2025-04-28T10:00:00.000Z"
    },
    "payment": {
      "id": "payment_id_789",
      "amount": 9.99,
      "currency": "USD",
      "status": "succeeded"
    }
  }
}
```

### Webhooks

These endpoints are used by Twilio and other services to send event notifications to the CallSaver.app backend. They don't require authentication but use signature verification for security.

#### Twilio Voice Webhook

```
POST /api/webhooks/twilio/voice
```

This endpoint receives incoming call events from Twilio.

#### Twilio Message Webhook

```
POST /api/webhooks/twilio/message
```

This endpoint receives incoming SMS events from Twilio.

#### Stripe Webhook

```
POST /api/webhooks/stripe
```

This endpoint receives payment events from Stripe.

## API Versioning

All API endpoints are prefixed with `/api`. The current API version is v1, which is implied without a version prefix. Future versions will use `/api/v2`, `/api/v3`, etc.

## Data Formats

- All timestamps are in ISO 8601 format (e.g., `2025-04-28T10:00:00.000Z`)
- Phone numbers are in E.164 format (e.g., `+12345678901`)
- Monetary values are decimal numbers (e.g., `9.99`)

## Testing

To test the API in development, you can use the provided Postman collection:

[Download Postman Collection](https://api.callsaver.app/docs/postman-collection.json)

## SDK

For client applications, we recommend using our official JavaScript SDK:

```bash
npm install callsaver-api-sdk
```

Usage example:

```javascript
const CallSaverAPI = require('callsaver-api-sdk');

const api = new CallSaverAPI({
  apiKey: 'your_api_key',
  baseUrl: 'https://api.callsaver.app/api'
});

// Get user profile
api.users.getProfile()
  .then(user => console.log(user))
  .catch(error => console.error(error));

// Send SMS
api.messages.send({
  to: '+10987654321',
  from: '+12345678901',
  body: 'Hello from the SDK!'
})
  .then(message => console.log(message))
  .catch(error => console.error(error));
```
