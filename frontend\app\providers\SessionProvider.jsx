'use client';

import { createContext, useState, useEffect, useContext, useRef } from 'react';
import getSupabaseClient from '../utils/supabaseClient'; // Import the function

// Create a context for the session
const SessionContext = createContext(null);

export function SessionProvider({ children }) {
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);
  const lastEventRef = useRef({ type: null, timestamp: 0 });
  const debounceTimerRef = useRef(null);

  useEffect(() => {
    let mounted = true;

    // Get the initial session
    const getInitialSession = async () => {
      try {
        // Check for demo user in localStorage first
        const demoUser = localStorage.getItem('callsaver_demo_user');
        if (demoUser) {
          console.log('Demo user found in localStorage');
          const parsedUser = JSON.parse(demoUser);
          // Create a mock session for the demo user
          if (mounted) {
            setSession({
              user: {
                id: parsedUser.id,
                email: parsedUser.email,
                user_metadata: {
                  name: parsedUser.name,
                  role: parsedUser.role
                }
              },
              expires_at: Date.now() + 24 * 60 * 60 * 1000 // 24 hours from now
            });
            setLoading(false);
          }
          return;
        }

        // Get the client instance
        const supabase = getSupabaseClient();
        if (!supabase) {
          console.error('Failed to get Supabase client in SessionProvider');
          if (mounted) setLoading(false);
          return; // Don't proceed without a client
        }

        // Try to get the session from Supabase
        const { data, error } = await supabase.auth.getSession();
        if (error) {
          console.error('Error getting initial session:', error);
          if (mounted) setSession(null);
        } else {
          console.log('Initial session check:', data.session ? 'Session found' : 'No session');
          if (mounted) setSession(data.session);
        }
      } catch (err) {
        console.error('Unexpected error getting session:', err);
        if (mounted) setSession(null);
      } finally {
        if (mounted) setLoading(false);
      }
    };

    // Debounced session update function
    const updateSession = (newSession, eventType) => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      debounceTimerRef.current = setTimeout(() => {
        if (mounted) {
          setSession(newSession);
          setLoading(false);
          lastEventRef.current = { type: eventType, timestamp: Date.now() };
        }
      }, 100); // 100ms debounce
    };

    // Get the client instance for the listener
    const supabase = getSupabaseClient();
    if (!supabase) {
      console.error('Failed to get Supabase client for auth listener');
      setLoading(false); // Ensure loading state is updated
      return;
    }

    // Set up auth state listener with improved debouncing
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, newSession) => {
        // Skip if it's the same event type within 1 second
        const now = Date.now();
        const timeSinceLastEvent = now - lastEventRef.current.timestamp;
        if (
          event === lastEventRef.current.type && 
          timeSinceLastEvent < 1000
        ) {
          console.log('Skipping duplicate auth event:', event);
          return;
        }

        console.log('Auth state changed:', event);

        // Handle specific auth events
        switch (event) {
          case 'SIGNED_IN':
            updateSession(newSession, event);
            break;
          case 'SIGNED_OUT':
            updateSession(null, event);
            break;
          case 'TOKEN_REFRESHED':
          case 'USER_UPDATED':
            if (newSession) {
              updateSession(newSession, event);
            }
            break;
          default:
            // For other events, only update if there's a meaningful change
            if (newSession?.user?.id !== session?.user?.id) {
              updateSession(newSession, event);
            }
            break;
        }
      }
    );

    getInitialSession();

    // Clean up
    return () => {
      mounted = false;
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      subscription?.unsubscribe();
    };
  }, []);

  const value = {
    session,
    loading,
    isAuthenticated: !!session,
    user: session?.user || null,
  };

  return (
    <SessionContext.Provider value={value}>
      {children}
    </SessionContext.Provider>
  );
}

// Hook to use the session context
export function useSession() {
  const context = useContext(SessionContext);
  if (!context) {
    throw new Error('useSession must be used within a SessionProvider');
  }
  return context;
}

export default SessionProvider;
