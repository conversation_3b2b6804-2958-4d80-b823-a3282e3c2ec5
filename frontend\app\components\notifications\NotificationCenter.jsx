"use client";

import { useState, useEffect, useRef } from 'react';
import { BellIcon, XMarkIcon, CheckIcon } from '@heroicons/react/24/outline';
import NotificationItem from './NotificationItem';

const NotificationCenter = ({ user }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const dropdownRef = useRef(null);
  
  // Sample notification data - in a real app, this would come from an API
  const sampleNotifications = [
    {
      id: '1',
      type: 'missed_call',
      title: 'Missed Call',
      message: 'You missed a call from <PERSON>',
      time: '10 minutes ago',
      isRead: false,
      actionUrl: '/dashboard/calls',
      icon: 'phone'
    },
    {
      id: '2',
      type: 'voicemail',
      title: 'New Voicemail',
      message: '<PERSON> left a voicemail (1:24)',
      time: '1 hour ago',
      isRead: false,
      actionUrl: '/dashboard/voicemails',
      icon: 'voicemail'
    },
    {
      id: '3',
      type: 'system',
      title: 'System Update',
      message: 'CallSaver has been updated to version 2.1.0',
      time: '3 hours ago',
      isRead: true,
      actionUrl: '/dashboard',
      icon: 'system'
    },
    {
      id: '4',
      type: 'ai_insight',
      title: 'AI Insight',
      message: 'New customer sentiment analysis is available',
      time: '1 day ago',
      isRead: true,
      actionUrl: '/dashboard/analytics',
      icon: 'ai'
    },
    {
      id: '5',
      type: 'billing',
      title: 'Payment Successful',
      message: 'Your subscription has been renewed',
      time: '2 days ago',
      isRead: true,
      actionUrl: '/settings/billing',
      icon: 'billing'
    }
  ];
  
  // Load notifications
  useEffect(() => {
    const fetchNotifications = async () => {
      setIsLoading(true);
      
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // In a real app, you would fetch notifications from your API
        setNotifications(sampleNotifications);
        
        // Calculate unread count
        const unread = sampleNotifications.filter(notification => !notification.isRead).length;
        setUnreadCount(unread);
      } catch (error) {
        console.error('Error fetching notifications:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchNotifications();
    
    // Set up polling for new notifications (every 30 seconds)
    const intervalId = setInterval(fetchNotifications, 30000);
    
    return () => clearInterval(intervalId);
  }, []);
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Mark a notification as read
  const markAsRead = (id) => {
    setNotifications(prevNotifications => 
      prevNotifications.map(notification => 
        notification.id === id 
          ? { ...notification, isRead: true } 
          : notification
      )
    );
    
    // Update unread count
    setUnreadCount(prevCount => Math.max(0, prevCount - 1));
  };
  
  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prevNotifications => 
      prevNotifications.map(notification => ({ ...notification, isRead: true }))
    );
    
    setUnreadCount(0);
  };
  
  // Delete a notification
  const deleteNotification = (id) => {
    const notification = notifications.find(n => n.id === id);
    const wasUnread = notification && !notification.isRead;
    
    setNotifications(prevNotifications => 
      prevNotifications.filter(notification => notification.id !== id)
    );
    
    // Update unread count if needed
    if (wasUnread) {
      setUnreadCount(prevCount => Math.max(0, prevCount - 1));
    }
  };
  
  return (
    <div className="relative" ref={dropdownRef}>
      {/* Notification Bell */}
      <button
        className="relative p-2 text-gray-300 hover:text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50 rounded-full"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Notifications"
      >
        <BellIcon className="h-6 w-6" />
        
        {/* Notification Badge */}
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>
      
      {/* Notification Dropdown */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 sm:w-96 bg-gray-900 border border-gray-800 rounded-lg shadow-lg z-50 overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between px-4 py-3 border-b border-gray-800">
            <h3 className="text-lg font-medium text-white">Notifications</h3>
            <div className="flex space-x-2">
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="p-1 text-gray-400 hover:text-white rounded-full hover:bg-gray-800 transition-colors"
                  title="Mark all as read"
                >
                  <CheckIcon className="h-5 w-5" />
                </button>
              )}
              <button
                onClick={() => setIsOpen(false)}
                className="p-1 text-gray-400 hover:text-white rounded-full hover:bg-gray-800 transition-colors"
                title="Close"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
          
          {/* Notification List */}
          <div className="max-h-96 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="loading-pulse"></div>
              </div>
            ) : notifications.length > 0 ? (
              <div>
                {notifications.map(notification => (
                  <NotificationItem
                    key={notification.id}
                    notification={notification}
                    onMarkAsRead={markAsRead}
                    onDelete={deleteNotification}
                    onClose={() => setIsOpen(false)}
                  />
                ))}
              </div>
            ) : (
              <div className="py-8 px-4 text-center">
                <p className="text-gray-400">No notifications</p>
              </div>
            )}
          </div>
          
          {/* Footer */}
          <div className="px-4 py-3 border-t border-gray-800 text-center">
            <a 
              href="/settings/notifications" 
              className="text-sm text-purple-400 hover:text-purple-300 transition-colors"
              onClick={() => setIsOpen(false)}
            >
              Notification Settings
            </a>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationCenter;
