"use client";

import { useState } from 'react';
import { 
  CalendarIcon, 
  ChevronLeftIcon, 
  ChevronRightIcon,
  ClockIcon,
  UserIcon,
  PhoneIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

export default function AppointmentsCalendar() {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [showAddModal, setShowAddModal] = useState(false);
  const [showMonthDropdown, setShowMonthDropdown] = useState(false);
  const [showYearDropdown, setShowYearDropdown] = useState(false);
  
  // Sample appointments data - in a real app, this would come from an API
  const [appointments, setAppointments] = useState([
    {
      id: 1,
      title: "Call with <PERSON>",
      date: new Date(2025, 3, 15, 10, 30),
      duration: 30,
      contact: {
        name: "<PERSON>",
        phone: "+****************"
      },
      notes: "Discuss new project proposal"
    },
    {
      id: 2,
      title: "Follow-up with <PERSON>",
      date: new Date(2025, 3, 16, 14, 0),
      duration: 15,
      contact: {
        name: "<PERSON>",
        phone: "+****************"
      },
      notes: "Review contract details"
    },
    {
      id: 3,
      title: "Product demo for Acme Corp",
      date: new Date(2025, 3, 18, 11, 0),
      duration: 45,
      contact: {
        name: "Michael Brown",
        phone: "+****************"
      },
      notes: "Show new features and pricing"
    }
  ]);

  // Get days in month
  const getDaysInMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  // Get day of week for first day of month (0 = Sunday, 6 = Saturday)
  const getFirstDayOfMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const daysInMonth = getDaysInMonth(currentMonth);
    const firstDayOfMonth = getFirstDayOfMonth(currentMonth);
    const days = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(null);
    }

    // Add days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(i);
    }

    return days;
  };

  // Format date for display
  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  // Format time for display
  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true
    });
  };

  // Navigate to previous month
  const goToPreviousMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1));
  };

  // Navigate to next month
  const goToNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1));
  };

  // Handle month selection
  const handleMonthSelect = (month) => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), month, 1));
    setShowMonthDropdown(false);
  };

  // Handle year selection
  const handleYearSelect = (year) => {
    setCurrentMonth(new Date(year, currentMonth.getMonth(), 1));
    setShowYearDropdown(false);
  };

  // Generate array of years (current year -5 to current year +5)
  const getYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 5; i <= currentYear + 5; i++) {
      years.push(i);
    }
    return years;
  };

  // Month names array
  const monthNames = [
    'January', 'February', 'March', 'April', 
    'May', 'June', 'July', 'August', 
    'September', 'October', 'November', 'December'
  ];

  // Check if a date has appointments
  const hasAppointments = (day) => {
    if (!day) return false;
    
    const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
    return appointments.some(appointment => {
      const appointmentDate = new Date(appointment.date);
      return (
        appointmentDate.getDate() === date.getDate() &&
        appointmentDate.getMonth() === date.getMonth() &&
        appointmentDate.getFullYear() === date.getFullYear()
      );
    });
  };

  // Get appointments for selected date
  const getAppointmentsForDate = (date) => {
    return appointments.filter(appointment => {
      const appointmentDate = new Date(appointment.date);
      return (
        appointmentDate.getDate() === date.getDate() &&
        appointmentDate.getMonth() === date.getMonth() &&
        appointmentDate.getFullYear() === date.getFullYear()
      );
    }).sort((a, b) => a.date - b.date);
  };

  // Handle date selection
  const handleDateSelect = (day) => {
    if (!day) return;
    
    const newSelectedDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
    setSelectedDate(newSelectedDate);
  };

  // Calendar days
  const calendarDays = generateCalendarDays();
  
  // Selected date appointments
  const selectedDateAppointments = getAppointmentsForDate(selectedDate);

  return (
    <div className="bg-gray-900/90 backdrop-blur-sm rounded-xl border border-purple-500/30 p-6 shadow-lg">
      <div className="flex justify-between items-center mb-5">
        <h3 className="text-xl font-bold text-white flex items-center">
          <CalendarIcon className="h-6 w-6 mr-2 text-purple-400" />
          Appointments
        </h3>
        <button 
          onClick={() => setShowAddModal(true)}
          className="p-2 bg-purple-600 rounded-full hover:bg-purple-700 transition-colors"
          title="Add appointment"
        >
          <PlusIcon className="h-5 w-5 text-white" />
        </button>
      </div>

      {/* Calendar navigation with dropdowns */}
      <div className="flex justify-between items-center mb-5">
        <button 
          onClick={goToPreviousMonth}
          className="p-2 bg-gray-800 rounded-full hover:bg-gray-700 transition-colors"
        >
          <ChevronLeftIcon className="h-5 w-5 text-gray-300" />
        </button>
        
        <div className="flex items-center space-x-2 relative">
          {/* Month dropdown */}
          <div className="relative">
            <button 
              onClick={() => {
                setShowMonthDropdown(!showMonthDropdown);
                setShowYearDropdown(false);
              }}
              className="text-white text-lg font-medium hover:text-purple-300 transition-colors flex items-center"
            >
              {currentMonth.toLocaleDateString('en-US', { month: 'long' })}
              <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            
            {showMonthDropdown && (
              <div className="absolute z-10 mt-1 w-40 bg-gray-800 border border-gray-700 rounded-lg shadow-lg py-1 max-h-60 overflow-y-auto">
                {monthNames.map((month, index) => (
                  <button
                    key={month}
                    onClick={() => handleMonthSelect(index)}
                    className={`w-full text-left px-4 py-2 text-sm ${
                      index === currentMonth.getMonth() 
                        ? 'bg-purple-600 text-white' 
                        : 'text-gray-300 hover:bg-gray-700'
                    }`}
                  >
                    {month}
                  </button>
                ))}
              </div>
            )}
          </div>
          
          {/* Year dropdown */}
          <div className="relative">
            <button 
              onClick={() => {
                setShowYearDropdown(!showYearDropdown);
                setShowMonthDropdown(false);
              }}
              className="text-white text-lg font-medium hover:text-purple-300 transition-colors flex items-center"
            >
              {currentMonth.getFullYear()}
              <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            
            {showYearDropdown && (
              <div className="absolute z-10 mt-1 w-24 bg-gray-800 border border-gray-700 rounded-lg shadow-lg py-1 max-h-60 overflow-y-auto">
                {getYearOptions().map(year => (
                  <button
                    key={year}
                    onClick={() => handleYearSelect(year)}
                    className={`w-full text-left px-4 py-2 text-sm ${
                      year === currentMonth.getFullYear() 
                        ? 'bg-purple-600 text-white' 
                        : 'text-gray-300 hover:bg-gray-700'
                    }`}
                  >
                    {year}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
        
        <button 
          onClick={goToNextMonth}
          className="p-2 bg-gray-800 rounded-full hover:bg-gray-700 transition-colors"
        >
          <ChevronRightIcon className="h-5 w-5 text-gray-300" />
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Calendar grid - made more square */}
        <div>
          <div className="grid grid-cols-7 gap-1 mb-2">
            {/* Day headers */}
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
              <div key={index} className="text-center text-xs font-medium text-gray-400 py-2">
                {day}
              </div>
            ))}
            
            {/* Calendar days */}
            {calendarDays.map((day, index) => (
              <div 
                key={index} 
                className={`
                  text-center py-3 rounded-md cursor-pointer text-sm
                  ${day === null ? 'invisible' : ''}
                  ${day === selectedDate.getDate() && 
                    currentMonth.getMonth() === selectedDate.getMonth() && 
                    currentMonth.getFullYear() === selectedDate.getFullYear() 
                    ? 'bg-purple-600 text-white font-medium' 
                    : 'hover:bg-gray-800 text-gray-300'}
                  ${hasAppointments(day) && !(day === selectedDate.getDate() && 
                    currentMonth.getMonth() === selectedDate.getMonth() && 
                    currentMonth.getFullYear() === selectedDate.getFullYear()) 
                    ? 'border-b-2 border-purple-500' : ''}
                `}
                onClick={() => handleDateSelect(day)}
              >
                {day}
              </div>
            ))}
          </div>
        </div>

        {/* Selected date appointments */}
        <div className="border-l border-gray-800 pl-6">
          <h4 className="text-white font-medium text-lg mb-4">
            {formatDate(selectedDate)}
          </h4>
          
          {selectedDateAppointments.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-48 bg-gray-800/30 rounded-lg border border-gray-700/50">
              <p className="text-gray-400 text-sm mb-2">No appointments scheduled for this day.</p>
              <button
                onClick={() => setShowAddModal(true)}
                className="px-4 py-2 bg-purple-600/50 hover:bg-purple-600 rounded-lg text-white text-sm transition-colors"
              >
                Add Appointment
              </button>
            </div>
          ) : (
            <div className="space-y-3 max-h-64 overflow-y-auto pr-2 custom-scrollbar">
              {selectedDateAppointments.map(appointment => (
                <div key={appointment.id} className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/50 hover:border-purple-500/50 transition-colors">
                  <div className="flex justify-between items-start">
                    <h5 className="text-white font-medium">{appointment.title}</h5>
                    <span className="text-purple-400 text-xs flex items-center bg-purple-500/10 px-2 py-1 rounded-full">
                      <ClockIcon className="h-3 w-3 mr-1" />
                      {formatTime(appointment.date)}
                    </span>
                  </div>
                  <div className="mt-2 text-xs text-gray-400 flex items-center">
                    <UserIcon className="h-3 w-3 mr-1" />
                    {appointment.contact.name}
                  </div>
                  <div className="text-xs text-gray-400 flex items-center">
                    <PhoneIcon className="h-3 w-3 mr-1" />
                    {appointment.contact.phone}
                  </div>
                  {appointment.notes && (
                    <div className="mt-2 text-xs text-gray-300 bg-gray-800/70 p-2 rounded">
                      {appointment.notes}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Add appointment modal (simplified) */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
          <div className="bg-gray-900 rounded-xl border border-gray-800 p-6 w-full max-w-md">
            <h3 className="text-lg font-medium text-white mb-4">Add New Appointment</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Title
                </label>
                <input
                  type="text"
                  placeholder="Appointment title"
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Date
                  </label>
                  <input
                    type="date"
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Time
                  </label>
                  <input
                    type="time"
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Contact Name
                </label>
                <input
                  type="text"
                  placeholder="Contact name"
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Phone Number
                </label>
                <input
                  type="tel"
                  placeholder="Phone number"
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Notes
                </label>
                <textarea
                  rows={3}
                  placeholder="Additional notes"
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-none"
                ></textarea>
              </div>
              
              <div className="flex justify-end space-x-3 pt-2">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => {
                    // In a real app, this would save the appointment
                    setShowAddModal(false);
                  }}
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                >
                  Save
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
