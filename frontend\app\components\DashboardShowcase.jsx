'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import Image from 'next/image';

const integrations = [
  { name: 'Google Calendar', icon: '/icons/gcal.svg', color: 'bg-blue-500', connected: true },
  { name: 'Outlook Calendar', icon: '/icons/outlook.svg', color: 'bg-blue-600', connected: false },
  { name: 'Zapier', icon: '/icons/zapier.svg', color: 'bg-orange-500', connected: true },
  { name: 'Stripe', icon: '/icons/stripe.svg', color: 'bg-purple-600', connected: true },
  { name: 'PayPal', icon: '/icons/paypal.svg', color: 'bg-blue-700', connected: false },
  { name: 'Shopify', icon: '/icons/shopify.svg', color: 'bg-green-500', connected: true },
  { name: 'WooCommerce', icon: '/icons/woo.svg', color: 'bg-purple-500', connected: false },
  { name: '<PERSON><PERSON><PERSON><PERSON>', icon: '/icons/hubspot.svg', color: 'bg-orange-600', connected: true },
  { name: 'Zoh<PERSON>', icon: '/icons/zoho.svg', color: 'bg-blue-500', connected: false },
];

const recentCases = [
  { id: 'CS-1234', customer: 'John D.', type: 'Appointment', status: 'Resolved', date: '2 hours ago' },
  { id: 'CS-1235', customer: 'Sarah L.', type: 'Technical Support', status: 'Resolved', date: '5 hours ago' },
  { id: 'CS-1236', customer: 'Michael R.', type: 'Product Inquiry', status: 'Resolved', date: '1 day ago' },
  { id: 'CS-1237', customer: 'Emma T.', type: 'Booking', status: 'Resolved', date: '1 day ago' },
];

export default function DashboardShowcase() {
  const [activeTab, setActiveTab] = useState('automation');

  const handleTabClick = (tab) => {
    setActiveTab(tab);
  };

  // Animation variants for tab content
  const tabContentVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.3,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <div className="w-full max-w-6xl mx-auto overflow-hidden rounded-2xl bg-gradient-to-br from-gray-900/75 to-gray-950/75 border border-gray-700/30 shadow-2xl backdrop-blur-xl">
      {/* Dashboard Header */}
      <div className="p-6 border-b border-gray-700/30 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-indigo-600/20 backdrop-blur-sm">
            <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-indigo-400">
              <rect x="3" y="3" width="18" height="18" rx="2" />
              <path d="M9 3v18M3 9h18" />
            </svg>
          </div>
          <div>
            <h2 className="text-xl font-bold text-white">Callsaver Assistant</h2>
            <p className="text-gray-400 text-sm">Business communication automation</p>
          </div>
        </div>
        <div className="flex bg-gray-800/30 backdrop-blur-sm rounded-lg p-1 overflow-x-auto w-full sm:w-auto md:overflow-visible">
          {['overview', 'automation', 'integrations'].map((tab) => (
            <button
              key={tab}
              className={`px-4 py-2 text-sm rounded-md transition-all whitespace-nowrap ${
                activeTab === tab 
                  ? 'bg-indigo-600/90 text-white shadow-md' 
                  : 'text-gray-300 hover:text-white hover:bg-gray-700/30'
              }`}
              onClick={() => handleTabClick(tab)}
              aria-selected={activeTab === tab}
              role="tab"
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Dashboard Content */}
      <div className="p-6">
        {/* Overview Tab Content */}
        {activeTab === 'overview' && (
          <motion.div 
            variants={tabContentVariants}
            initial="hidden"
            animate="visible"
            className="grid md:grid-cols-12 gap-6"
          >
            {/* KPI Summary Section */}
            <motion.div 
              variants={itemVariants}
              className="md:col-span-12 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4"
            >
              {/* Total Missed Calls */}
              <div className="bg-gray-800/30 backdrop-blur-md border border-gray-700/30 rounded-2xl p-5 shadow-lg">
                <div className="flex justify-between items-start">
                  <h3 className="text-gray-300 font-medium text-sm">Total Missed Calls</h3>
                  <div className="p-1.5 rounded-md bg-red-500/20">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-red-400">
                      <path d="M16 2v4M8 2v4M3 10h18M5 4h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2z" />
                      <path d="M12 14l2 2M14 14l-2 2" />
                    </svg>
                  </div>
                </div>
                <div className="mt-4">
                  <span className="text-3xl font-bold text-white">156</span>
                  <span className="text-gray-400 text-sm ml-1">this month</span>
                </div>
                <div className="mt-2 flex items-center text-xs font-medium text-green-400">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="mr-1">
                    <path d="M18 15l-6-6-6 6" />
                  </svg>
                  13% fewer than last month
                </div>
              </div>

              {/* SMS Conversations */}
              <div className="bg-gray-800/30 backdrop-blur-md border border-gray-700/30 rounded-2xl p-5 shadow-lg">
                <div className="flex justify-between items-start">
                  <h3 className="text-gray-300 font-medium text-sm">SMS Conversations</h3>
                  <div className="p-1.5 rounded-md bg-blue-500/20">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-blue-400">
                      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                    </svg>
                  </div>
                </div>
                <div className="mt-4">
                  <span className="text-3xl font-bold text-white">324</span>
                  <span className="text-gray-400 text-sm ml-1">total</span>
                </div>
                <div className="mt-2 flex items-center text-xs font-medium text-green-400">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="mr-1">
                    <path d="M6 9l6 6 6-6" />
                  </svg>
                  27% more than last month
                </div>
              </div>

              {/* Customer Satisfaction */}
              <div className="bg-gray-800/30 backdrop-blur-md border border-gray-700/30 rounded-2xl p-5 shadow-lg">
                <div className="flex justify-between items-start">
                  <h3 className="text-gray-300 font-medium text-sm">Customer Satisfaction</h3>
                  <div className="p-1.5 rounded-md bg-yellow-500/20">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-yellow-400">
                      <path d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20zm0 18a8 8 0 1 1 0-16 8 8 0 0 1 0 16z"/>
                      <path d="M8 13a4 4 0 0 0 8 0"/>
                      <circle cx="9" cy="9" r="1"/>
                      <circle cx="15" cy="9" r="1"/>
                    </svg>
                  </div>
                </div>
                <div className="mt-4">
                  <span className="text-3xl font-bold text-white">96%</span>
                  <span className="text-gray-400 text-sm ml-1">positive</span>
                </div>
                <div className="mt-2 flex items-center text-xs font-medium text-green-400">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="mr-1">
                    <path d="M6 9l6 6 6-6" />
                  </svg>
                  3% increase since last month
                </div>
              </div>

              {/* Conversion Rate */}
              <div className="bg-gray-800/30 backdrop-blur-md border border-gray-700/30 rounded-2xl p-5 shadow-lg">
                <div className="flex justify-between items-start">
                  <h3 className="text-gray-300 font-medium text-sm">Conversion Rate</h3>
                  <div className="p-1.5 rounded-md bg-green-500/20">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-green-400">
                      <polyline points="23 6 13.5 15.5 8.5 10.5 1 18" />
                      <polyline points="17 6 23 6 23 12" />
                    </svg>
                  </div>
                </div>
                <div className="mt-4">
                  <span className="text-3xl font-bold text-white">42%</span>
                  <span className="text-gray-400 text-sm ml-1">rate</span>
                </div>
                <div className="mt-2 flex items-center text-xs font-medium text-green-400">
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="mr-1">
                    <path d="M6 9l6 6 6-6" />
                  </svg>
                  5% higher than target
                </div>
              </div>
            </motion.div>

            {/* Activity Chart */}
            <motion.div 
              variants={itemVariants}
              className="md:col-span-8 bg-gray-800/30 backdrop-blur-md rounded-2xl p-5 border border-gray-700/30 shadow-lg"
            >
              <div className="flex items-start justify-between mb-6">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-1">Activity Trends</h3>
                  <p className="text-gray-400 text-sm">Last 7 days performance metrics</p>
                </div>
                <div className="flex space-x-2">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-indigo-500 mr-1"></div>
                    <span className="text-xs text-gray-400">Calls</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-green-500 mr-1"></div>
                    <span className="text-xs text-gray-400">Conversions</span>
                  </div>
                </div>
              </div>
              
              <div className="h-64 w-full">
                {/* Fake chart - in production would use Chart.js or similar */}
                <div className="h-full w-full flex items-end justify-between gap-2 px-1">
                  {[35, 45, 30, 60, 75, 50, 65].map((height, index) => (
                    <div key={index} className="h-full flex flex-col justify-end items-center gap-1">
                      <div className="w-full flex flex-col gap-1 items-center">
                        <div className="w-8 bg-indigo-500/80 rounded-t-sm" style={{ height: `${height}%` }}></div>
                        <div className="w-8 bg-green-500/80 rounded-t-sm" style={{ height: `${height * 0.6}%` }}></div>
                      </div>
                      <span className="text-xs text-gray-500 mt-1">{['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'][index]}</span>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>

            {/* Recent Cases */}
            <motion.div
              variants={itemVariants}
              className="md:col-span-4 bg-gray-800/30 backdrop-blur-md rounded-2xl p-5 border border-gray-700/30 shadow-lg"
            >
              <h3 className="text-lg font-semibold text-white mb-3">Recent Cases</h3>
              <div className="space-y-3">
                {recentCases.map((caseItem, index) => (
                  <div key={index} className="p-3 bg-gray-800/40 rounded-lg border border-gray-700/30">
                    <div className="flex justify-between items-start">
                      <div>
                        <span className="text-xs text-indigo-400 font-medium">{caseItem.id}</span>
                        <h4 className="text-sm text-white font-medium mt-0.5">{caseItem.customer}</h4>
                        <p className="text-xs text-gray-400 mt-0.5">{caseItem.type}</p>
                      </div>
                      <div className="flex flex-col items-end">
                        <span className="px-2 py-0.5 text-xs bg-green-500/20 text-green-400 rounded-full">
                          {caseItem.status}
                        </span>
                        <span className="text-gray-500 text-xs mt-1.5">
                          {caseItem.date}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <button className="w-full mt-3 py-2 px-3 bg-indigo-600/30 hover:bg-indigo-600/50 border border-indigo-500/30 text-white text-sm rounded-lg transition-colors">
                View All Cases
              </button>
            </motion.div>
          </motion.div>
        )}

        {/* Automation Tab Content */}
        {activeTab === 'automation' && (
          <motion.div 
            variants={tabContentVariants}
            initial="hidden"
            animate="visible"
            className="grid md:grid-cols-12 gap-6"
          >
            {/* AI Training Section */}
            <motion.div 
              variants={itemVariants}
              className="md:col-span-6 rounded-2xl bg-gray-800/30 backdrop-blur-md border border-gray-700/30 p-5 flex flex-col h-full shadow-lg"
            >
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-1">Train Your AI Assistant</h3>
                  <p className="text-gray-400 text-sm mb-4">Upload files to create a custom-trained AI for your business</p>
                </div>
                <div className="p-2 rounded-full bg-indigo-600/20">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-indigo-400">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                    <polyline points="17 8 12 3 7 8" />
                    <line x1="12" y1="3" x2="12" y2="15" />
                  </svg>
                </div>
              </div>

              <div className="mt-2 border-2 border-dashed border-gray-700/50 rounded-lg p-5 text-center flex-1 flex flex-col justify-center items-center bg-gray-800/20 backdrop-blur-sm">
                <div className="mb-3 p-3 rounded-full bg-indigo-600/20 inline-block">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-indigo-400">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
                    <polyline points="14 2 14 8 20 8" />
                    <line x1="12" y1="18" x2="12" y2="12" />
                    <line x1="9" y1="15" x2="15" y2="15" />
                  </svg>
                </div>
                <h4 className="text-white font-medium mb-1">Upload Files to Train Your Assistant</h4>
                <p className="text-gray-400 text-sm mb-3">PDFs, Docs, FAQs, Scripts, Menus, Catalogs</p>
                <div className="mb-2 flex flex-wrap gap-2 justify-center">
                  <span className="px-3 py-1 text-xs bg-gray-700/50 rounded-full text-gray-300">Company FAQ</span>
                  <span className="px-3 py-1 text-xs bg-gray-700/50 rounded-full text-gray-300">Call Scripts</span>
                  <span className="px-3 py-1 text-xs bg-gray-700/50 rounded-full text-gray-300">Product Catalog</span>
                </div>
                <button className="mt-2 px-4 py-2 bg-indigo-600/80 hover:bg-indigo-600/90 text-white rounded-lg text-sm transition-colors">
                  Upload Files
                </button>
                <div className="mt-4 text-xs text-gray-500">
                  Last trained: 2 days ago · 12 documents · 98.2% accuracy
                </div>
              </div>
            </motion.div>

            {/* Smart Automation Section */}
            <motion.div 
              variants={itemVariants}
              className="md:col-span-6 rounded-2xl bg-gray-800/30 backdrop-blur-md border border-gray-700/30 p-5 shadow-lg"
            >
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-1">Smart Automation</h3>
                  <p className="text-gray-400 text-sm mb-4">Automatically handle follow-ups, scheduling, and confirmations</p>
                </div>
                <div className="p-2 rounded-full bg-indigo-600/20">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-indigo-400">
                    <circle cx="12" cy="12" r="10" />
                    <path d="M16.2 7.8l-2 6.3-6.4 2.1 2-6.3z" />
                  </svg>
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg p-4 border border-gray-700/30">
                  <div className="flex justify-between items-start">
                    <h5 className="text-gray-300 font-medium text-sm">Auto-Scheduled Appointments</h5>
                    <div className="p-1 rounded-md bg-green-500/10">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-green-400">
                        <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
                        <path d="M7 13l3 3 7-7" />
                      </svg>
                    </div>
                  </div>
                  <div className="mt-3">
                    <span className="text-2xl font-bold text-white">12</span>
                    <span className="text-gray-400 text-sm ml-1">this week</span>
                  </div>
                  <div className="mt-1 text-xs text-green-400">+24% from last week</div>
                </div>

                <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg p-4 border border-gray-700/30">
                  <div className="flex justify-between items-start">
                    <h5 className="text-gray-300 font-medium text-sm">Customer Follow-ups</h5>
                    <div className="p-1 rounded-md bg-blue-500/10">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-blue-400">
                        <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" />
                      </svg>
                    </div>
                  </div>
                  <div className="mt-3">
                    <span className="text-2xl font-bold text-white">8</span>
                    <span className="text-gray-400 text-sm ml-1">in progress</span>
                  </div>
                  <div className="mt-1 text-xs text-blue-400">5 completed today</div>
                </div>

                <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg p-4 border border-gray-700/30">
                  <div className="flex justify-between items-start">
                    <h5 className="text-gray-300 font-medium text-sm">AI SMS Messages</h5>
                    <div className="p-1 rounded-md bg-purple-500/10">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-purple-400">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                      </svg>
                    </div>
                  </div>
                  <div className="mt-3">
                    <span className="text-2xl font-bold text-white">32</span>
                    <span className="text-gray-400 text-sm ml-1">sent</span>
                  </div>
                  <div className="mt-1 text-xs text-purple-400">91% response rate</div>
                </div>

                <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg p-4 border border-gray-700/30">
                  <div className="flex justify-between items-start">
                    <h5 className="text-gray-300 font-medium text-sm">Appointment Confirmations</h5>
                    <div className="p-1 rounded-md bg-yellow-500/10">
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-yellow-400">
                        <path d="M8 19C4.667 19 2 17.233 2 13.8889C2 10.2222 6 5 12 2C12 2 22 9 22 13.8889C22 17.2337 19.3333 19 16 19C14 19 13 18 12 17C11 18 10 19 8 19Z" />
                      </svg>
                    </div>
                  </div>
                  <div className="mt-3">
                    <span className="text-2xl font-bold text-white">18</span>
                    <span className="text-gray-400 text-sm ml-1">confirmed</span>
                  </div>
                  <div className="mt-1 text-xs text-yellow-400">2 confirmations pending</div>
                </div>
              </div>
            </motion.div>

            {/* Integrations Section */}
            <motion.div 
              variants={itemVariants}
              className="md:col-span-8 rounded-2xl bg-gray-800/30 backdrop-blur-md border border-gray-700/30 p-5 shadow-lg"
            >
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-1">Connect Your Tools</h3>
                  <p className="text-gray-400 text-sm mb-4">Integrate with your favorite business tools to automate your workflow</p>
                </div>
                <div className="p-2 rounded-full bg-indigo-600/20">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-indigo-400">
                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71" />
                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71" />
                  </svg>
                </div>
              </div>

              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 lg:grid-cols-9 gap-2 mt-2">
                {integrations.map((integration, index) => (
                  <div key={index} className="group flex flex-col items-center justify-center p-3 rounded-lg bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 hover:bg-gray-700/40 transition cursor-pointer">
                    <div className={`w-8 h-8 rounded-full mb-2 flex items-center justify-center ${integration.color}/20`}>
                      <div className="w-4 h-4 bg-gray-300 rounded-sm"></div>
                    </div>
                    <span className="text-xs text-gray-300 group-hover:text-white text-center">{integration.name}</span>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Scheduled Follow-Up Example */}
            <motion.div 
              variants={itemVariants}
              className="md:col-span-4 rounded-2xl bg-gray-800/30 backdrop-blur-md border border-gray-700/30 p-5 shadow-lg"
            >
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-white mb-1">Follow-Up Scheduler</h3>
                  <p className="text-gray-400 text-sm mb-4">Automatically schedule follow-up messages based on customer requests</p>
                </div>
                <div className="p-2 rounded-full bg-indigo-600/20">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-indigo-400">
                    <circle cx="12" cy="12" r="10" />
                    <polyline points="12 6 12 12 16 14" />
                  </svg>
                </div>
              </div>

              <div className="bg-gray-800/40 backdrop-blur-sm rounded-lg p-4 border border-gray-700/30 relative">
                <div className="absolute -right-2 -top-2">
                  <div className="px-3 py-1 bg-green-500 text-xs font-medium text-white rounded-full shadow-md animate-pulse">
                    Active
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="p-2 rounded-full bg-blue-500/20 mt-1">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-blue-400">
                      <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" />
                    </svg>
                  </div>
                  
                  <div className="flex-1">
                    <div className="text-gray-400 text-xs mb-1">Customer message</div>
                    <div className="bg-gray-700/30 backdrop-blur-sm rounded-lg p-3 mb-3">
                      <p className="text-sm text-gray-300">
                        &ldquo;I&apos;m busy right now. Can you text me again in 10 minutes with those pricing details?&rdquo;
                      </p>
                    </div>
                    
                    <div className="text-gray-400 text-xs mb-1">AI Response</div>
                    <div className="bg-indigo-900/30 backdrop-blur-sm rounded-lg p-3 border border-indigo-800/30">
                      <p className="text-sm text-gray-300">
                        &ldquo;No problem! I&apos;ll follow up with our pricing details in 10 minutes. Talk to you soon!&rdquo;
                      </p>
                    </div>
                    
                    <div className="mt-3 flex items-center justify-between">
                      <div className="flex items-center gap-1">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-green-400">
                          <circle cx="12" cy="12" r="10" />
                          <polyline points="12 6 12 12 16 14" />
                        </svg>
                        <span className="text-xs text-green-400 font-medium">
                          Scheduled follow-up in 10 minutes
                        </span>
                      </div>
                      <div className="text-gray-500 text-xs">6 minutes remaining</div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-3 p-2 bg-gray-800/50 backdrop-blur-sm rounded-lg flex items-center justify-between">
                <div className="text-xs text-gray-400">Smart follow-ups booked this week</div>
                <div className="text-white font-semibold">42</div>
              </div>
            </motion.div>
          </motion.div>
        )}

        {/* Integrations Tab Content */}
        {activeTab === 'integrations' && (
          <motion.div 
            variants={tabContentVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-12 gap-6"
          >
            <motion.div 
              variants={itemVariants}
              className="md:col-span-12 rounded-2xl bg-gray-800/30 backdrop-blur-md border border-gray-700/30 p-5 shadow-lg"
            >
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h3 className="text-lg font-semibold text-white">Integration Center</h3>
                  <p className="text-gray-400 text-sm mt-1">Connect your favorite tools and services to enhance your workflow</p>
                </div>
                <button className="px-4 py-2 bg-indigo-600/80 hover:bg-indigo-600/90 text-white rounded-lg text-sm">
                  Add New Integration
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
                {integrations.map((integration, index) => (
                  <div key={index} className="bg-gray-800/40 backdrop-blur-sm rounded-xl border border-gray-700/30 p-4 flex items-start">
                    <div className={`p-3 rounded-xl ${integration.color}/20 mr-3`}>
                      <div className="w-6 h-6 bg-gray-300 rounded-sm"></div>
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="font-medium text-white">{integration.name}</h4>
                        <div className={`h-5 w-10 rounded-full p-0.5 transition-colors ${integration.connected ? 'bg-green-500' : 'bg-gray-600'}`}>
                          <div className={`h-4 w-4 rounded-full bg-white transform transition-transform ${integration.connected ? 'translate-x-5' : 'translate-x-0'}`}></div>
                        </div>
                      </div>
                      
                      <p className="text-gray-400 text-sm mb-3">
                        {integration.connected 
                          ? "Connected and working properly" 
                          : "Not connected - click to set up"}
                      </p>
                      
                      <div className="flex space-x-2">
                        {integration.connected ? (
                          <>
                            <button className="px-3 py-1.5 bg-gray-700/50 text-gray-300 text-xs rounded hover:bg-gray-700/70 transition-colors">
                              Settings
                            </button>
                            <button className="px-3 py-1.5 bg-gray-700/50 text-gray-300 text-xs rounded hover:bg-gray-700/70 transition-colors">
                              View Data
                            </button>
                          </>
                        ) : (
                          <button className="px-3 py-1.5 bg-indigo-600/70 text-white text-xs rounded hover:bg-indigo-600/90 transition-colors">
                            Connect
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="md:col-span-6 rounded-2xl bg-gray-800/30 backdrop-blur-md border border-gray-700/30 p-5 shadow-lg"
            >
              <h3 className="text-lg font-semibold text-white mb-4">Integration Activity</h3>
              <div className="space-y-3">
                {[
                  { service: 'Google Calendar', action: 'Synced 12 appointments', time: '5 minutes ago', icon: 'bg-blue-500' },
                  { service: 'Stripe', action: 'Processed payment #8391', time: '1 hour ago', icon: 'bg-purple-600' },
                  { service: 'Zapier', action: 'Workflow triggered', time: '3 hours ago', icon: 'bg-orange-500' },
                  { service: 'HubSpot', action: 'Contact updated', time: '5 hours ago', icon: 'bg-orange-600' },
                ].map((activity, index) => (
                  <div key={index} className="flex items-center p-3 bg-gray-800/40 backdrop-blur-sm rounded-lg border border-gray-700/30">
                    <div className={`w-8 h-8 rounded-full mr-3 flex items-center justify-center ${activity.icon}/20`}>
                      <div className="w-4 h-4 bg-gray-300 rounded-sm"></div>
                    </div>
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-white">{activity.service}</h4>
                      <p className="text-xs text-gray-400">{activity.action}</p>
                    </div>
                    <div className="text-xs text-gray-500">
                      {activity.time}
                    </div>
                  </div>
                ))}
              </div>
              <button className="w-full mt-4 py-2 px-3 bg-indigo-600/30 hover:bg-indigo-600/50 border border-indigo-500/30 text-white text-sm rounded-lg transition-colors">
                View All Activity
              </button>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="md:col-span-6 rounded-2xl bg-gray-800/30 backdrop-blur-md border border-gray-700/30 p-5 shadow-lg"
            >
              <h3 className="text-lg font-semibold text-white mb-4">Available Integrations</h3>
              <div className="bg-gray-800/40 backdrop-blur-sm rounded-xl border border-gray-700/30 p-4 mb-4">
                <h4 className="font-medium text-white mb-2">Suggested For You</h4>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                  {[
                    { name: 'Square', type: 'Payment', icon: 'bg-blue-600' },
                    { name: 'Mailchimp', type: 'Email', icon: 'bg-yellow-500' },
                    { name: 'Salesforce', type: 'CRM', icon: 'bg-blue-500' },
                  ].map((app, index) => (
                    <div key={index} className="p-3 bg-gray-800/60 rounded-lg border border-gray-700/30 flex flex-col items-center">
                      <div className={`w-10 h-10 rounded-lg mb-2 flex items-center justify-center ${app.icon}/20`}>
                        <div className="w-5 h-5 bg-gray-300 rounded-sm"></div>
                      </div>
                      <span className="text-sm text-white font-medium">{app.name}</span>
                      <span className="text-xs text-gray-400">{app.type}</span>
                    </div>
                  ))}
                </div>
              </div>
              <div className="bg-indigo-900/20 backdrop-blur-sm rounded-xl border border-indigo-700/30 p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium text-white">Pro Integrations</h4>
                    <p className="text-gray-400 text-xs mt-1">Unlock premium integrations with enterprise tools</p>
                  </div>
                  <span className="px-2 py-1 bg-indigo-600/70 text-white text-xs rounded-full">
                    Premium
                  </span>
                </div>
                <button className="w-full mt-4 py-2 px-3 bg-indigo-600/60 hover:bg-indigo-600/80 text-white text-sm rounded-lg transition-colors">
                  Upgrade to Access
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </div>
    </div>
  );
} 