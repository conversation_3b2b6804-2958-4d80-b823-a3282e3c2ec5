---
description: Defines the architecture, technology choice, and principles for the asynchronous task queue system.
---
# Task Queue Architecture (`task_queue_architecture.mdc`)

## 1. Purpose and Scope

**Purpose:** To define the architecture, chosen technology, queue structures, message formats, and reliability guarantees for the asynchronous task processing system used within CallSaver.

**Scope:**
- Chosen message queue technology (e.g., RabbitMQ, Redis Streams, SQS, BullMQ).
- Rationale for technology choice.
- High-level architecture (brokers, producers, consumers/workers).
- Key queue definitions (e.g., for AI tasks, webhook processing, notifications).
- Standard message format/contract.
- Reliability features (persistence, acknowledgments, retries, DLQs).
- Monitoring and scaling considerations.

## 2. Technology Choice

- **Chosen Technology:** [Specify Technology Here - e.g., RabbitMQ, Redis Streams with BullMQ, AWS SQS]
- **Rationale:** [Provide Justification Here - e.g., Based on factors like:]
    - **Reliability:** Support for message persistence, acknowledgments, dead-letter queues.
    - **Scalability:** Ability to handle expected load and scale horizontally.
    - **Features:** Support for routing options (direct, topic, fanout), delayed messages, prioritized queues (if needed).
    - **Operational Overhead:** Managed service vs. self-hosted considerations.
    - **Ecosystem/Client Libraries:** Availability of robust client libraries for Node.js.
    - **Cost:** Pricing model.
- *(Placeholder: Assume a technology like RabbitMQ or BullMQ on Redis is chosen for robust features unless a managed service like SQS is preferred)*

## 3. High-Level Architecture

- **Producers:** Backend services (API handlers, webhook controllers, scheduled job runners) produce messages (tasks) and publish them to the queue system.
- **Broker(s):** The chosen message queue system (e.g., RabbitMQ server, Redis instance, SQS service) routes messages to appropriate queues.
- **Consumers (Workers):** Dedicated background worker services subscribe to specific queues, consume messages, execute the required task logic, and acknowledge message processing. Multiple instances of workers can run for scalability.

## 4. Queue Definitions

Define logical queues for different types of tasks to allow for separate processing, scaling, and prioritization if necessary. Examples:

- `ai_tasks_queue`: For general AI processing tasks (summarization, sentiment).
- `transcription_queue`: Potentially separate for high-volume transcription jobs.
- `webhook_stripe_queue`: For processing Stripe webhook events.
- `webhook_twilio_voice_queue`: For processing Twilio voice events.
- `webhook_twilio_message_queue`: For processing Twilio message events.
- `notification_dispatch_queue`: For sending out user notifications (email, SMS, push).
- `low_priority_tasks_queue`: For background maintenance or non-critical jobs.

*Note: Specific queue names and routing (e.g., using exchanges/topics in RabbitMQ) should be detailed based on the chosen technology.*

## 5. Standard Message Format

- Messages published to the queue SHOULD follow a consistent JSON format:
  ```json
  {
    "taskId": "uuid-for-this-specific-job-instance",
    "correlationId": "uuid-linking-to-originating-request-or-event",
    "taskType": "SPECIFIC_TASK_TYPE_ENUM", // e.g., "PROCESS_STRIPE_EVENT", "TRANSCRIBE_AUDIO"
    "payload": {
      // Data specific to the taskType
      "eventId": "stripe-event-id", // Example for Stripe
      "audioUrl": "s3://...", // Example for Transcription
      "...": "..."
    },
    "metadata": {
      "timestamp": "iso8601-utc-timestamp",
      "originatingService": "api-gateway", // Service that produced the message
      "organizationId": "tenant-org-id", // CRITICAL for multi-tenancy
      "userId": "user-id-if-applicable",
      "priority": 5, // Optional priority
      "attemptCount": 1 // Added/updated by worker/queue system
    }
  }
  ```

## 6. Reliability Guarantees

- **Persistence:** Messages SHOULD be persisted by the broker to survive restarts.
- **Acknowledgments:** Consumers MUST explicitly acknowledge messages only after successful processing. If a worker crashes before acknowledging, the message should be redelivered by the broker to another worker.
- **Retries:** Implement retry logic (ideally with exponential backoff) for transient processing failures within workers or via queue system features. Refer to `ai_task_processing_and_escalation.mdc` and `webhook_reliability_and_idempotency.mdc`.
- **Dead-Letter Queues (DLQs):** Configure DLQs for relevant primary queues. Messages that fail repeatedly (exceed max retries) or encounter non-recoverable errors SHOULD be routed to a corresponding DLQ for investigation.

## 7. Monitoring and Scaling

- **Queue Metrics:** Monitor key metrics like queue depth (message count), message publish rate, message consumption rate, number of active consumers, and acknowledgment latency.
- **Worker Metrics:** Monitor worker health, resource utilization (CPU/memory), error rates, and task processing duration.
- **DLQ Monitoring:** Monitor the size of DLQs and alert when messages arrive.
- **Scaling:** Scale worker instances horizontally based on queue depth or processing latency metrics. Ensure the chosen broker technology can handle the required scale.

## 8. Related Documents

- `docs/functional_specs/ai_task_processing_and_escalation.mdc`
- `docs/platform_security/webhook_reliability_and_idempotency.mdc`
- `docs/architecture/service_failover_and_redundancy.mdc` (Broker/worker resilience)
- `docs/architecture/multi_tenant_data_isolation.mdc` (Ensuring workers respect tenancy)
- `docs/functional_specs/notifications_and_alerts_document.mdc` (Alerting on queue issues)
