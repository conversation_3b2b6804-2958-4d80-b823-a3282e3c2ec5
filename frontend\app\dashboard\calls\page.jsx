'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card';

export default function CallHistoryPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [calls, setCalls] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState('all');
  const [callType, setCallType] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedCall, setSelectedCall] = useState(null);
  const [isCallDetailsOpen, setIsCallDetailsOpen] = useState(false);

  // Mock call data
  const mockCalls = [
    { id: 1, from: '+1 (555) 123-4567', to: '+****************', direction: 'inbound', status: 'completed', duration: 125, timestamp: '2023-05-15T10:30:00Z', recording_url: 'https://example.com/recordings/1', transcription: 'Hello, this is a test call. I would like to inquire about your services.', ai_summary: 'Customer inquiring about services', handled_by: 'AI Assistant' },
    { id: 2, from: '+1 (555) 234-5678', to: '+****************', direction: 'inbound', status: 'missed', duration: 0, timestamp: '2023-05-14T14:45:00Z', recording_url: null, transcription: null, ai_summary: null, handled_by: null },
    { id: 3, from: '+****************', to: '+1 (555) 345-6789', direction: 'outbound', status: 'completed', duration: 210, timestamp: '2023-05-14T11:20:00Z', recording_url: 'https://example.com/recordings/3', transcription: 'Hi there, I\'m calling to follow up on our previous conversation about your account.', ai_summary: 'Follow-up call about account', handled_by: 'John Doe' },
    { id: 4, from: '+1 (555) 456-7890', to: '+****************', direction: 'inbound', status: 'completed', duration: 45, timestamp: '2023-05-13T16:15:00Z', recording_url: 'https://example.com/recordings/4', transcription: 'I need to reschedule my appointment for next week.', ai_summary: 'Customer rescheduling appointment', handled_by: 'AI Assistant' },
    { id: 5, from: '+****************', to: '+1 (555) 567-8901', direction: 'outbound', status: 'no-answer', duration: 0, timestamp: '2023-05-13T09:10:00Z', recording_url: null, transcription: null, ai_summary: null, handled_by: 'Jane Smith' },
    { id: 6, from: '+1 (555) 678-9012', to: '+****************', direction: 'inbound', status: 'completed', duration: 180, timestamp: '2023-05-12T13:25:00Z', recording_url: 'https://example.com/recordings/6', transcription: 'I\'m calling about the invoice I received yesterday. There seems to be a discrepancy in the charges.', ai_summary: 'Billing inquiry about invoice discrepancy', handled_by: 'AI Assistant' },
    { id: 7, from: '+1 (555) 789-0123', to: '+****************', direction: 'inbound', status: 'voicemail', duration: 30, timestamp: '2023-05-11T15:40:00Z', recording_url: 'https://example.com/recordings/7', transcription: 'This is Bob calling. Please call me back when you get a chance.', ai_summary: 'Voicemail requesting callback', handled_by: null },
    { id: 8, from: '+****************', to: '+1 (555) 890-1234', direction: 'outbound', status: 'completed', duration: 95, timestamp: '2023-05-10T10:05:00Z', recording_url: 'https://example.com/recordings/8', transcription: 'Hello, I\'m calling to confirm your appointment for tomorrow at 2 PM.', ai_summary: 'Appointment confirmation call', handled_by: 'John Doe' },
    { id: 9, from: '+****************', to: '+****************', direction: 'inbound', status: 'completed', duration: 150, timestamp: '2023-05-09T14:30:00Z', recording_url: 'https://example.com/recordings/9', transcription: 'I\'m interested in upgrading my current plan. Can you tell me about the available options?', ai_summary: 'Customer interested in plan upgrade', handled_by: 'AI Assistant' },
    { id: 10, from: '+****************', to: '+****************', direction: 'outbound', status: 'completed', duration: 75, timestamp: '2023-05-08T11:15:00Z', recording_url: 'https://example.com/recordings/10', transcription: 'This is a follow-up call regarding your recent support ticket. Has the issue been resolved?', ai_summary: 'Follow-up on support ticket', handled_by: 'Jane Smith' },
  ];

  useEffect(() => {
    const fetchCalls = async () => {
      try {
        setIsLoading(true);
        // In a real implementation, this would be an API call to get calls
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Filter calls based on search term and filters
        let filteredCalls = [...mockCalls];
        
        // Apply search filter
        if (searchTerm) {
          filteredCalls = filteredCalls.filter(call => 
            call.from.includes(searchTerm) || 
            call.to.includes(searchTerm) ||
            (call.transcription && call.transcription.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (call.ai_summary && call.ai_summary.toLowerCase().includes(searchTerm.toLowerCase()))
          );
        }
        
        // Apply date range filter
        if (dateRange !== 'all') {
          const now = new Date();
          let startDate;
          
          switch (dateRange) {
            case 'today':
              startDate = new Date(now.setHours(0, 0, 0, 0));
              break;
            case 'yesterday':
              startDate = new Date(now.setDate(now.getDate() - 1));
              startDate.setHours(0, 0, 0, 0);
              break;
            case 'week':
              startDate = new Date(now.setDate(now.getDate() - 7));
              break;
            case 'month':
              startDate = new Date(now.setMonth(now.getMonth() - 1));
              break;
            default:
              startDate = null;
          }
          
          if (startDate) {
            filteredCalls = filteredCalls.filter(call => new Date(call.timestamp) >= startDate);
          }
        }
        
        // Apply call type filter
        if (callType !== 'all') {
          filteredCalls = filteredCalls.filter(call => {
            if (callType === 'inbound') return call.direction === 'inbound';
            if (callType === 'outbound') return call.direction === 'outbound';
            if (callType === 'missed') return call.status === 'missed' || call.status === 'no-answer';
            if (callType === 'voicemail') return call.status === 'voicemail';
            return true;
          });
        }
        
        // Pagination
        const itemsPerPage = 10;
        const totalPages = Math.ceil(filteredCalls.length / itemsPerPage);
        setTotalPages(totalPages);
        
        // Get current page items
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedCalls = filteredCalls.slice(startIndex, endIndex);
        
        setCalls(paginatedCalls);
      } catch (err) {
        console.error('Error fetching calls:', err);
        setError('Failed to load call history. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCalls();
  }, [searchTerm, dateRange, callType, currentPage]);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
  };

  const formatDuration = (seconds) => {
    if (!seconds) return '0:00';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleViewCallDetails = (call) => {
    setSelectedCall(call);
    setIsCallDetailsOpen(true);
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold text-white mb-6">Call History</h1>
      
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
          <p className="text-red-200">{error}</p>
        </div>
      )}
      
      <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 mb-6">
        <CardHeader>
          <CardTitle className="text-white">Call Analytics</CardTitle>
          <CardDescription className="text-gray-400">Overview of your call activity</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-gray-700/50 rounded-lg p-4">
              <h3 className="text-gray-400 text-sm mb-1">Total Calls</h3>
              <p className="text-2xl font-bold text-white">{mockCalls.length}</p>
            </div>
            <div className="bg-gray-700/50 rounded-lg p-4">
              <h3 className="text-gray-400 text-sm mb-1">Inbound Calls</h3>
              <p className="text-2xl font-bold text-white">{mockCalls.filter(call => call.direction === 'inbound').length}</p>
            </div>
            <div className="bg-gray-700/50 rounded-lg p-4">
              <h3 className="text-gray-400 text-sm mb-1">Outbound Calls</h3>
              <p className="text-2xl font-bold text-white">{mockCalls.filter(call => call.direction === 'outbound').length}</p>
            </div>
            <div className="bg-gray-700/50 rounded-lg p-4">
              <h3 className="text-gray-400 text-sm mb-1">Missed Calls</h3>
              <p className="text-2xl font-bold text-white">{mockCalls.filter(call => call.status === 'missed' || call.status === 'no-answer').length}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
        <CardHeader>
          <CardTitle className="text-white">Call History</CardTitle>
          <CardDescription className="text-gray-400">View and search your call records</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
            <div className="w-full md:w-1/3">
              <input 
                type="text" 
                placeholder="Search calls..." 
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              />
            </div>
            <div className="flex flex-wrap gap-2">
              <select 
                value={dateRange} 
                onChange={(e) => setDateRange(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              >
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="yesterday">Yesterday</option>
                <option value="week">Last 7 Days</option>
                <option value="month">Last 30 Days</option>
              </select>
              <select 
                value={callType} 
                onChange={(e) => setCallType(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              >
                <option value="all">All Calls</option>
                <option value="inbound">Inbound</option>
                <option value="outbound">Outbound</option>
                <option value="missed">Missed</option>
                <option value="voicemail">Voicemail</option>
              </select>
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead className="bg-gray-700/50 text-gray-300">
                <tr>
                  <th className="px-4 py-2 rounded-tl-lg">Date & Time</th>
                  <th className="px-4 py-2">From</th>
                  <th className="px-4 py-2">To</th>
                  <th className="px-4 py-2">Type</th>
                  <th className="px-4 py-2">Status</th>
                  <th className="px-4 py-2">Duration</th>
                  <th className="px-4 py-2">Handled By</th>
                  <th className="px-4 py-2 rounded-tr-lg">Actions</th>
                </tr>
              </thead>
              <tbody className="text-gray-300">
                {isLoading ? (
                  <tr>
                    <td colSpan="8" className="px-4 py-2 text-center">Loading call history...</td>
                  </tr>
                ) : calls.length === 0 ? (
                  <tr>
                    <td colSpan="8" className="px-4 py-2 text-center">No calls found</td>
                  </tr>
                ) : (
                  calls.map((call, index) => (
                    <tr key={call.id} className={index % 2 === 0 ? 'bg-gray-700/30' : 'bg-gray-700/10'}>
                      <td className="px-4 py-2">{formatDate(call.timestamp)}</td>
                      <td className="px-4 py-2">{call.from}</td>
                      <td className="px-4 py-2">{call.to}</td>
                      <td className="px-4 py-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          call.direction === 'inbound' ? 'bg-blue-500/20 text-blue-300' : 'bg-green-500/20 text-green-300'
                        }`}>
                          {call.direction === 'inbound' ? 'Inbound' : 'Outbound'}
                        </span>
                      </td>
                      <td className="px-4 py-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          call.status === 'completed' ? 'bg-green-500/20 text-green-300' :
                          call.status === 'missed' || call.status === 'no-answer' ? 'bg-red-500/20 text-red-300' :
                          call.status === 'voicemail' ? 'bg-yellow-500/20 text-yellow-300' :
                          'bg-gray-500/20 text-gray-300'
                        }`}>
                          {call.status.charAt(0).toUpperCase() + call.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-4 py-2">{formatDuration(call.duration)}</td>
                      <td className="px-4 py-2">{call.handled_by || 'N/A'}</td>
                      <td className="px-4 py-2">
                        <button 
                          onClick={() => handleViewCallDetails(call)}
                          className="text-blue-400 hover:text-blue-300"
                        >
                          Details
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-between items-center mt-4">
              <div className="text-gray-400">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <button 
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="bg-gray-700 text-white px-3 py-1 rounded-lg disabled:opacity-50"
                >
                  Previous
                </button>
                <button 
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="bg-gray-700 text-white px-3 py-1 rounded-lg disabled:opacity-50"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Call Details Modal */}
      {isCallDetailsOpen && selectedCall && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-white">Call Details</h2>
              <button 
                onClick={() => setIsCallDetailsOpen(false)}
                className="text-gray-400 hover:text-white"
              >
                ✕
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <h3 className="text-gray-400 text-sm">Date & Time</h3>
                <p className="text-white">{formatDate(selectedCall.timestamp)}</p>
              </div>
              <div>
                <h3 className="text-gray-400 text-sm">Duration</h3>
                <p className="text-white">{formatDuration(selectedCall.duration)}</p>
              </div>
              <div>
                <h3 className="text-gray-400 text-sm">From</h3>
                <p className="text-white">{selectedCall.from}</p>
              </div>
              <div>
                <h3 className="text-gray-400 text-sm">To</h3>
                <p className="text-white">{selectedCall.to}</p>
              </div>
              <div>
                <h3 className="text-gray-400 text-sm">Type</h3>
                <p className="text-white">{selectedCall.direction === 'inbound' ? 'Inbound' : 'Outbound'}</p>
              </div>
              <div>
                <h3 className="text-gray-400 text-sm">Status</h3>
                <p className="text-white">{selectedCall.status.charAt(0).toUpperCase() + selectedCall.status.slice(1)}</p>
              </div>
              <div>
                <h3 className="text-gray-400 text-sm">Handled By</h3>
                <p className="text-white">{selectedCall.handled_by || 'N/A'}</p>
              </div>
            </div>
            
            {selectedCall.recording_url && (
              <div className="mb-6">
                <h3 className="text-white font-medium mb-2">Recording</h3>
                <audio controls className="w-full">
                  <source src={selectedCall.recording_url} type="audio/mpeg" />
                  Your browser does not support the audio element.
                </audio>
              </div>
            )}
            
            {selectedCall.transcription && (
              <div className="mb-6">
                <h3 className="text-white font-medium mb-2">Transcription</h3>
                <div className="bg-gray-700/50 rounded-lg p-4 text-gray-300">
                  {selectedCall.transcription}
                </div>
              </div>
            )}
            
            {selectedCall.ai_summary && (
              <div>
                <h3 className="text-white font-medium mb-2">AI Summary</h3>
                <div className="bg-gray-700/50 rounded-lg p-4 text-gray-300">
                  {selectedCall.ai_summary}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
