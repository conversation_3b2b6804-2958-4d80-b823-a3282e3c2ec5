# Airalo eSIM Integration Guide

This guide will walk you through the process of setting up the Airalo eSIM provider integration for CallSaver, based on the official Airalo Partners API.

## Step 1: Request Access to Airalo Partners API

1. **Contact Airalo Partners**:
   - Reach out to Airalo to request access to their Partners API
   - You will need to provide information about your application and use case

2. **Receive API Credentials**:
   - Once approved, you will receive:
     - A Client ID (unique identifier for your application)
     - A Client Secret (confidential key associated with your Client ID)
   - **IMPORTANT**: Store these credentials securely as they grant access to the API

## Step 2: Understanding the Authentication Flow

Airalo Partners API uses OAuth 2.0 Client Credentials flow for authentication:

1. **Token Endpoint**: `/v2/token`
2. **Request Method**: POST
3. **Required Parameters**:
   - `client_id`: Your application's unique identifier
   - `client_secret`: Your confidential key
   - `grant_type`: Must be set to "client_credentials"
4. **Response**: An access token that is valid for 1 year
5. **Token Usage**: The access token must be included in the Authorization header for all API requests

## Step 3: Configure CallSaver

1. **Update Environment Variables**:
   - Add the following variables to your `.env` file:

   ```
   # Airalo eSIM Provider Configuration
   AIRALO_CLIENT_ID=your_client_id_here
   AIRALO_CLIENT_SECRET=your_client_secret_here
   AIRALO_API_URL=https://api.airalo.com/v2  # Based on actual endpoint
   AIRALO_SANDBOX=true  # Set to false for production
   
   # eSIM Configuration
   ESIM_ENABLED=true
   TELEPHONY_DEFAULT_PROVIDER=airalo  # To use Airalo as default provider
   ```

2. **Update Configuration**:
   - Ensure the config is loaded correctly in `back/backend/config/index.js`
   - Make sure the `esim` and `airalo` sections are included from `config/updates.js`

## Step 4: Test the Integration

1. **Verify Authentication**:
   - Start your development server
   - Check the logs to ensure the access token is retrieved successfully

2. **Test API Connection**:
   - Run the test script to verify API connectivity:

   ```bash
   node back/backend/test-airalo-connection.js
   ```

3. **Create Test eSIM**:
   - Use the UI or API to create a test eSIM profile
   - Verify that the QR code is generated correctly
   - Test the eSIM on a compatible device

## Step 5: Move to Production

Once testing is complete and you're ready to go live:

1. **Disable Sandbox Mode** (if applicable):
   - Update your `.env` file: `AIRALO_SANDBOX=false`
   - Ensure you have sufficient credit in your production Airalo account

2. **Update Authentication Handling**:
   - Implement token caching to avoid unnecessary authentication requests
   - Add token refresh logic when the token expires or becomes invalid

3. **Set Up Monitoring**:
   - Configure monitoring for your eSIM usage and API calls
   - Set up alerts for token expiration or API errors

4. **Gradual Rollout**:
   - Use the user targeting feature to gradually roll out to actual users:
   ```
   ESIM_ENABLED_USERS=1,2,3  # Comma-separated list of user IDs
   ```

## API Endpoints

Based on the provided documentation, these are the key endpoints you'll be working with:

1. **Authentication**: 
   - `POST /v2/token`
   - Provides the access token required for all other requests

2. **Products/Packages**:
   - Endpoints for retrieving available eSIM data packages
   - Package details include data amount, validity period, price, and regional availability

3. **eSIM Management**:
   - Endpoints for provisioning new eSIMs
   - Managing activation via QR codes
   - Checking usage and status

## Troubleshooting

- **Authentication Issues**:
  - Verify client ID and secret are correct
  - Check if the token has expired (typical validity is 1 year)
  - Ensure you're including the token correctly in request headers

- **eSIM Activation Problems**:
  - Verify the device is eSIM compatible
  - Check if the QR code is generated properly
  - Ensure the activation code format is correct

- **Data Issues**:
  - Monitor your Airalo dashboard for usage metrics
  - Compare with your internal tracking to ensure consistency

## Additional Resources

- **API Documentation**: You can access the full API documentation through the Apidog MCP configuration
- **Support**: Contact Airalo Partners support for issues specific to their API
- **Internal Resources**: Review our implementation in `back/backend/esim/providers/airaloProvider.js`

If you encounter any issues during integration, please contact the development team for assistance.
