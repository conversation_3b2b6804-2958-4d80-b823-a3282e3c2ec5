import { NextResponse } from 'next/server';

// Real Twilio numbers as requested by the user
function getUserPhoneNumbers() {
  return [
    {
      id: 'phone1',
      number: '+16205268448',
      countryCode: 'US',
      providerId: 'twilio',
      isActive: true,
      webhook: 'https://your-ngrok-url.ngrok-free.app/api/webhook/voice',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(), // 30 days ago
    },
    {
      id: 'phone2',
      number: '+31970102886',
      countryCode: 'NL',
      providerId: 'twilio',
      isActive: true,
      webhook: 'https://your-ngrok-url.ngrok-free.app/api/webhook/sms',
      createdAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(), // 15 days ago
    }
  ];
}

export async function GET() {
  try {
    // Return the real Twilio numbers as specified by the user
    const phoneNumbers = getUserPhoneNumbers();
    
    return NextResponse.json({
      success: true,
      phoneNumbers
    });
  } catch (error) {
    console.error('Error retrieving phone numbers:', error);
    return NextResponse.json(
      { success: false, message: `Failed to retrieve phone numbers: ${error.message}` },
      { status: 500 }
    );
  }
}
