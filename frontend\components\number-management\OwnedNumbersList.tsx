'use client';

import { useState } from 'react';
import { PhoneIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import OwnedNumberItem from './OwnedNumberItem';
import EmptyState from '../shared/EmptyState';
import { OwnedNumber } from '../../hooks/useNumberManagement';

interface OwnedNumbersListProps {
  numbers: OwnedNumber[];
  onConfigClick: (numberId: string) => void;
  onReleaseClick: (numberId: string) => void;
}

export default function OwnedNumbersList({
  numbers,
  onConfigClick,
  onReleaseClick
}: OwnedNumbersListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');

  // Filter numbers based on search term and filter type
  const filteredNumbers = numbers.filter(number => {
    const matchesSearch =
      number.phoneNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      number.friendlyName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter =
      filterType === 'all' ||
      number.type.toLowerCase() === filterType.toLowerCase();

    return matchesSearch && matchesFilter;
  });

  // Get unique number types for filter dropdown
  const numberTypes = ['all', ...new Set(numbers.map(number => number.type.toLowerCase()))];

  // Handle empty state
  if (numbers.length === 0) {
    return (
      <EmptyState
        title="No phone numbers yet"
        message="You don't have any phone numbers yet. Get started by purchasing your first number."
        icon={PhoneIcon}
        action={{
          label: "Get a Number",
          onClick: () => {
            // This would typically change the tab to the search tab
            // For now, we'll just log it
            console.log("Navigate to search tab");
          }
        }}
      />
    );
  }

  return (
    <div>
      {/* Search and filter controls */}
      <div className="mb-4 sm:mb-6 flex flex-col sm:flex-row gap-3 sm:gap-4">
        <div className="relative flex-grow">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 text-sm border border-gray-300 dark:border-gray-700 rounded-md leading-5 bg-white dark:bg-gray-800 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search phone numbers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="sm:w-48">
          <label htmlFor="number-type-filter" className="sr-only">Filter by number type</label>
          <select
            id="number-type-filter"
            aria-label="Filter by number type"
            className="block w-full pl-3 pr-10 py-2 text-sm border border-gray-300 dark:border-gray-700 rounded-md leading-5 bg-white dark:bg-gray-800 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
          >
            {numberTypes.map((type) => (
              <option key={type} value={type}>
                {type === 'all' ? 'All Types' : type.charAt(0).toUpperCase() + type.slice(1)}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Numbers list */}
      {filteredNumbers.length === 0 ? (
        <div className="text-center py-4 sm:py-8">
          <p className="text-sm sm:text-base text-gray-500 dark:text-gray-400">
            No phone numbers match your search criteria.
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {filteredNumbers.map((number) => (
            <OwnedNumberItem
              key={number.id}
              numberData={number}
              onConfigClick={() => onConfigClick(number.id)}
              onReleaseClick={() => onReleaseClick(number.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
}
