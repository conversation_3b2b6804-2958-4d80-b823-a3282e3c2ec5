import React, { useState, useEffect } from 'react';

export function Tabs({ defaultValue, className, children, ...props }) {
  const [activeTab, setActiveTab] = useState(defaultValue);

  // Pass the active tab value to children
  const childrenWithProps = React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, { activeTab, setActiveTab });
    }
    return child;
  });

  return (
    <div className={className} {...props}>
      {childrenWithProps}
    </div>
  );
}

export function TabsList({ className, children, activeTab, setActiveTab, ...props }) {
  // Pass the active tab value to children
  const childrenWithProps = React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, { activeTab, setActiveTab });
    }
    return child;
  });

  return (
    <div className={`flex space-x-2 ${className}`} {...props}>
      {childrenWithProps}
    </div>
  );
}

export function TabsTrigger({ value, className, children, activeTab, setActiveTab, ...props }) {
  const isActive = activeTab === value;

  return (
    <button
      data-value={value}
      className={`px-4 py-2 rounded-lg transition-colors ${
        isActive 
          ? 'bg-purple-600 text-white' 
          : 'bg-transparent text-gray-400 hover:text-white hover:bg-gray-700/50'
      } ${className}`}
      onClick={() => setActiveTab(value)}
      {...props}
    >
      {children}
    </button>
  );
}

export function TabsContent({ value, className, children, activeTab, ...props }) {
  if (activeTab !== value) return null;

  return (
    <div className={className} {...props}>
      {children}
    </div>
  );
}
