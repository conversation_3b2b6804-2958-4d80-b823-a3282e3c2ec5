import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { api } from '../lib/apiClient';

// Types for Automation data
export interface AutomationRule {
  id: string;
  name: string;
  description?: string;
  type: 'call' | 'sms' | 'voicemail';
  isActive: boolean;
  priority: number;
  conditions: AutomationCondition[];
  actions: AutomationAction[];
  createdAt: string;
  updatedAt: string;
}

export interface AutomationCondition {
  id: string;
  field: 'time' | 'day' | 'caller_number' | 'caller_name' | 'repeat_caller' | 'keywords' | 'sentiment';
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'starts_with' | 'ends_with' | 'greater_than' | 'less_than' | 'between';
  value: any;
  valueTwo?: any; // For "between" operator
}

export interface AutomationAction {
  id: string;
  type: 'forward_call' | 'send_to_voicemail' | 'play_message' | 'send_sms' | 'record' | 'transcribe' | 'analyze_sentiment' | 'ai_assistant' | 'webhook';
  configuration: Record<string, any>;
}

export interface AIAssistantConfig {
  id: string;
  phoneNumberId: string;
  name: string;
  personality: string;
  capabilities: string[];
  knowledgeEnabled: boolean;
  isTraining: boolean;
  trainingStatus?: string;
  lastTrainedAt?: string;
  accuracy?: number;
}

export interface AIAssistantUpdateParams {
  name?: string;
  personality?: string;
  capabilities?: string[];
  knowledgeEnabled?: boolean;
}

export interface AutomationActionParams {
  ruleId: string;
  action: 'enable' | 'disable' | 'reorder';
  data?: any; // For reordering: { newPriority: number } or similar
}

/**
 * Hook for managing automation and AI assistant configurations
 */
export function useAutomation() {
  const queryClient = useQueryClient();
  
  // Fetch automation rules for a phone number
  const getAutomationRules = (phoneNumberId: string, enabled?: boolean) => {
    return useQuery({
      queryKey: ['automation', 'rules', phoneNumberId, { enabled }],
      queryFn: () => api.get<AutomationRule[]>(`/automation/rules?phoneNumberId=${phoneNumberId}${enabled !== undefined ? `&enabled=${enabled}` : ''}`),
      enabled: !!phoneNumberId,
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };
  
  // Get a single automation rule details
  const getAutomationRule = (ruleId: string) => {
    return useQuery({
      queryKey: ['automation', 'rule', ruleId],
      queryFn: () => api.get<AutomationRule>(`/automation/rules/${ruleId}`),
      enabled: !!ruleId,
    });
  };
  
  // Get AI assistant configuration for a phone number
  const getAIAssistantConfig = (phoneNumberId: string) => {
    return useQuery({
      queryKey: ['automation', 'ai-assistant', phoneNumberId],
      queryFn: () => api.get<AIAssistantConfig>(`/automation/ai-assistant/${phoneNumberId}`),
      enabled: !!phoneNumberId,
    });
  };
  
  // Create automation rule
  const createRuleMutation = useMutation({
    mutationFn: (rule: Omit<AutomationRule, 'id' | 'createdAt' | 'updatedAt'>) => {
      return api.post<AutomationRule>('/automation/rules', rule);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['automation', 'rules', data.id] });
    },
  });
  
  // Update automation rule
  const updateRuleMutation = useMutation({
    mutationFn: ({ ruleId, rule }: { ruleId: string, rule: Partial<AutomationRule> }) => {
      return api.put<AutomationRule>(`/automation/rules/${ruleId}`, rule);
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['automation', 'rules'] });
      queryClient.invalidateQueries({ queryKey: ['automation', 'rule', data.id] });
    },
  });
  
  // Delete automation rule
  const deleteRuleMutation = useMutation({
    mutationFn: (ruleId: string) => {
      return api.delete<{ success: boolean }>(`/automation/rules/${ruleId}`);
    },
    onSuccess: (_, ruleId) => {
      queryClient.invalidateQueries({ queryKey: ['automation', 'rules'] });
    },
  });
  
  // Enable/disable/reorder rule
  const ruleActionMutation = useMutation({
    mutationFn: ({ ruleId, action, data }: AutomationActionParams) => {
      return api.post<AutomationRule>(`/automation/rules/${ruleId}/${action}`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['automation', 'rules'] });
    },
  });
  
  // Update AI assistant configuration
  const updateAIAssistantMutation = useMutation({
    mutationFn: ({ phoneNumberId, config }: { phoneNumberId: string, config: AIAssistantUpdateParams }) => {
      return api.put<AIAssistantConfig>(`/automation/ai-assistant/${phoneNumberId}`, config);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['automation', 'ai-assistant', variables.phoneNumberId] });
    },
  });
  
  // Train AI assistant
  const trainAIAssistantMutation = useMutation({
    mutationFn: (phoneNumberId: string) => {
      return api.post<{ success: boolean, message: string }>(`/automation/ai-assistant/${phoneNumberId}/train`);
    },
    onSuccess: (_, phoneNumberId) => {
      queryClient.invalidateQueries({ queryKey: ['automation', 'ai-assistant', phoneNumberId] });
    },
  });
  
  // Upload knowledge document
  const uploadKnowledgeDocMutation = useMutation({
    mutationFn: ({ phoneNumberId, formData }: { phoneNumberId: string, formData: FormData }) => {
      // We need to use axios directly for formData uploads since our api helper doesn't support the config parameter
      const url = `${process.env.NEXT_PUBLIC_API_BASE_URL || '/api'}/automation/ai-assistant/${phoneNumberId}/knowledge`;
      return axios.post<{ success: boolean, documentId: string }>(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        withCredentials: true,
      }).then(response => response.data);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['automation', 'ai-assistant', variables.phoneNumberId] });
    },
  });
  
  return {
    // Queries
    getAutomationRules,
    getAutomationRule,
    getAIAssistantConfig,
    
    // Mutations
    createRule: createRuleMutation.mutate,
    isCreatingRule: createRuleMutation.isPending,
    createRuleError: createRuleMutation.error,
    
    updateRule: updateRuleMutation.mutate,
    isUpdatingRule: updateRuleMutation.isPending,
    updateRuleError: updateRuleMutation.error,
    
    deleteRule: deleteRuleMutation.mutate,
    isDeletingRule: deleteRuleMutation.isPending,
    deleteRuleError: deleteRuleMutation.error,
    
    ruleAction: ruleActionMutation.mutate,
    isPerformingRuleAction: ruleActionMutation.isPending,
    ruleActionError: ruleActionMutation.error,
    
    updateAIAssistant: updateAIAssistantMutation.mutate,
    isUpdatingAIAssistant: updateAIAssistantMutation.isPending,
    updateAIAssistantError: updateAIAssistantMutation.error,
    
    trainAIAssistant: trainAIAssistantMutation.mutate,
    isTrainingAIAssistant: trainAIAssistantMutation.isPending,
    trainAIAssistantError: trainAIAssistantMutation.error,
    
    uploadKnowledgeDoc: uploadKnowledgeDocMutation.mutate,
    isUploadingKnowledgeDoc: uploadKnowledgeDocMutation.isPending,
    uploadKnowledgeDocError: uploadKnowledgeDocMutation.error,
    
    // Refresh
    refreshAutomationRules: (phoneNumberId: string) => {
      queryClient.invalidateQueries({ queryKey: ['automation', 'rules', phoneNumberId] });
    },
    refreshAIAssistantConfig: (phoneNumberId: string) => {
      queryClient.invalidateQueries({ queryKey: ['automation', 'ai-assistant', phoneNumberId] });
    },
  };
}
