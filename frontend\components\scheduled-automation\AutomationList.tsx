'use client';

import { useState } from 'react';
import { ScheduledAutomation, useScheduledAutomation } from '../../hooks/useScheduledAutomation';
import AutomationListItem from './AutomationListItem';
import EmptyState from '../shared/EmptyState';
import { PhoneIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';

interface AutomationListProps {
  automations: ScheduledAutomation[];
  onEdit: (automation: ScheduledAutomation) => void;
}

export default function AutomationList({ automations, onEdit }: AutomationListProps) {
  const [filter, setFilter] = useState<'all' | 'call' | 'sms'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  
  const { toggleAutomation, deleteAutomation, isTogglingStatus, isDeleting } = useScheduledAutomation();

  // Handle toggling automation status
  const handleToggle = (id: string, isEnabled: boolean) => {
    toggleAutomation({ id, isEnabled });
  };

  // Handle deleting an automation
  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this automation? This action cannot be undone.')) {
      deleteAutomation(id);
    }
  };

  // Filter automations based on type and search term
  const filteredAutomations = automations.filter(automation => {
    // Filter by type
    if (filter !== 'all' && automation.type !== filter) {
      return false;
    }
    
    // Filter by search term
    if (searchTerm && !automation.name.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    
    return true;
  });

  return (
    <div>
      {/* Filter and search controls */}
      <div className="flex flex-col md:flex-row justify-between mb-4 space-y-2 md:space-y-0">
        <div className="flex space-x-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-3 py-1 rounded-md ${
              filter === 'all'
                ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
            }`}
          >
            All
          </button>
          <button
            onClick={() => setFilter('call')}
            className={`px-3 py-1 rounded-md flex items-center ${
              filter === 'call'
                ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
            }`}
          >
            <PhoneIcon className="h-4 w-4 mr-1" />
            Calls
          </button>
          <button
            onClick={() => setFilter('sms')}
            className={`px-3 py-1 rounded-md flex items-center ${
              filter === 'sms'
                ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
            }`}
          >
            <ChatBubbleLeftRightIcon className="h-4 w-4 mr-1" />
            SMS
          </button>
        </div>
        
        <div>
          <input
            type="text"
            placeholder="Search automations..."
            className="px-3 py-1 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Automation list */}
      {filteredAutomations.length > 0 ? (
        <div className="space-y-4">
          {filteredAutomations.map(automation => (
            <AutomationListItem
              key={automation.id}
              automation={automation}
              onEdit={() => onEdit(automation)}
              onDelete={() => handleDelete(automation.id)}
              onToggle={(isEnabled) => handleToggle(automation.id, isEnabled)}
              isToggling={isTogglingStatus}
              isDeleting={isDeleting}
            />
          ))}
        </div>
      ) : (
        <EmptyState
          title="No automations found"
          description={
            searchTerm || filter !== 'all'
              ? "Try adjusting your filters or search term."
              : "Create your first automation to start automating your calls and messages."
          }
          icon={
            filter === 'call' ? PhoneIcon : 
            filter === 'sms' ? ChatBubbleLeftRightIcon : 
            undefined
          }
        />
      )}
    </div>
  );
}
