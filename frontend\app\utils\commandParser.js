/**
 * Parse user input to extract command type, phone number, and message
 * @param {string} input - User input text
 * @returns {Object|null} Parsed command object or null if not a valid command
 */
export function parseCommand(input) {
  if (!input || typeof input !== 'string') return null;
  
  const trimmedInput = input.trim();
  
  // Check for call command
  const callRegex = /^\/call\s+(\+\d+)\s+Say:\s+(.+)$/i;
  const callMatch = trimmedInput.match(callRegex);
  
  if (callMatch) {
    return {
      type: 'call',
      number: callMatch[1],
      message: callMatch[2].trim()
    };
  }
  
  // Check for SMS command
  const smsRegex = /^\/sms\s+(\+\d+)\s+(.+)$/i;
  const smsMatch = trimmedInput.match(smsRegex);
  
  if (smsMatch) {
    return {
      type: 'sms',
      number: smsMatch[1],
      message: smsMatch[2].trim()
    };
  }
  
  // Check for help command
  if (trimmedInput.toLowerCase() === '/help') {
    return { type: 'help' };
  }
  
  // Not a recognized command
  return null;
}

/**
 * Validate phone number format
 * @param {string} number - Phone number to validate
 * @returns {boolean} True if valid
 */
export function validatePhoneNumber(number) {
  // Simple E.164 format validation
  const regex = /^\+[1-9]\d{1,14}$/;
  return regex.test(number);
} 