'use client';

import { IntegrationSettings } from '../../hooks/useIntegrations';

interface DefaultSettingsProps {
  settings: IntegrationSettings;
  onChange: (settings: IntegrationSettings) => void;
}

export default function DefaultSettings({
  settings,
  onChange,
}: DefaultSettingsProps) {
  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    onChange({
      ...settings,
      [name]: checked,
    });
  };

  // Handle select change
  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    onChange({
      ...settings,
      [name]: value,
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <label
          htmlFor="syncFrequency"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Sync Frequency
        </label>
        <select
          id="syncFrequency"
          name="syncFrequency"
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          value={settings.syncFrequency || '15'}
          onChange={handleSelectChange}
        >
          <option value="5">Every 5 minutes</option>
          <option value="15">Every 15 minutes</option>
          <option value="30">Every 30 minutes</option>
          <option value="60">Every hour</option>
          <option value="360">Every 6 hours</option>
          <option value="720">Every 12 hours</option>
          <option value="1440">Every day</option>
        </select>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="enableNotifications"
            name="enableNotifications"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.enableNotifications || false}
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="enableNotifications"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Enable Notifications
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Receive notifications about sync status and errors.
          </p>
        </div>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="syncOnStartup"
            name="syncOnStartup"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.syncOnStartup || false}
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="syncOnStartup"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Sync on Startup
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Automatically sync when the application starts.
          </p>
        </div>
      </div>
    </div>
  );
}
