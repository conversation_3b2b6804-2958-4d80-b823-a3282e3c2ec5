'use client';

import { useState } from 'react';
import { NotificationType } from '../../../hooks/useSettings';

interface NotificationTypesFormProps {
  notificationTypes: NotificationType[];
  isLoading: boolean;
  onUpdate: (types: NotificationType[]) => void;
}

export default function NotificationTypesForm({ 
  notificationTypes, 
  isLoading, 
  onUpdate 
}: NotificationTypesFormProps) {
  const [localTypes, setLocalTypes] = useState<NotificationType[]>(notificationTypes);
  
  // Handle toggle notification type
  const handleToggleType = (id: string) => {
    const updatedTypes = localTypes.map(type => 
      type.id === id 
        ? { ...type, enabled: !type.enabled } 
        : type
    );
    
    setLocalTypes(updatedTypes);
  };
  
  // Handle toggle channel for a type
  const handleToggleChannel = (typeId: string, channel: 'email' | 'sms' | 'inApp') => {
    const updatedTypes = localTypes.map(type => 
      type.id === typeId 
        ? { 
            ...type, 
            channels: { 
              ...type.channels, 
              [channel]: !type.channels[channel] 
            } 
          } 
        : type
    );
    
    setLocalTypes(updatedTypes);
  };
  
  // Handle save changes
  const handleSaveChanges = () => {
    onUpdate(localTypes);
  };
  
  // Check if changes were made
  const hasChanges = JSON.stringify(notificationTypes) !== JSON.stringify(localTypes);
  
  return (
    <div>
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
        Choose which events you want to be notified about and through which channels.
      </p>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Notification Type
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Enabled
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Email
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                SMS
              </th>
              <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                In-App
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {localTypes.map((type) => (
              <tr key={type.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {type.name}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {type.description}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-center">
                  <button
                    type="button"
                    onClick={() => handleToggleType(type.id)}
                    disabled={isLoading}
                    className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                      type.enabled 
                        ? 'bg-indigo-600' 
                        : 'bg-gray-200 dark:bg-gray-600'
                    }`}
                    role="switch"
                    aria-checked={type.enabled}
                  >
                    <span 
                      className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                        type.enabled 
                          ? 'translate-x-5' 
                          : 'translate-x-0'
                      }`} 
                    />
                  </button>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-center">
                  <input
                    type="checkbox"
                    checked={type.channels.email}
                    onChange={() => handleToggleChannel(type.id, 'email')}
                    disabled={isLoading || !type.enabled}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded disabled:opacity-50"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-center">
                  <input
                    type="checkbox"
                    checked={type.channels.sms}
                    onChange={() => handleToggleChannel(type.id, 'sms')}
                    disabled={isLoading || !type.enabled}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded disabled:opacity-50"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-center">
                  <input
                    type="checkbox"
                    checked={type.channels.inApp}
                    onChange={() => handleToggleChannel(type.id, 'inApp')}
                    disabled={isLoading || !type.enabled}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded disabled:opacity-50"
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      <div className="flex justify-end mt-6">
        <button
          type="button"
          onClick={handleSaveChanges}
          disabled={isLoading || !hasChanges}
          className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Saving...' : 'Save Changes'}
        </button>
      </div>
    </div>
  );
}
