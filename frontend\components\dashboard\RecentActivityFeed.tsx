'use client';

import { useState } from 'react';
import ActivityItem from './ActivityItem';
import EmptyState from '../shared/EmptyState';
import ErrorMessage from '../shared/ErrorMessage';
import LoadingSpinner from '../shared/LoadingSpinner';
import { PhoneIcon, ChatBubbleLeftRightIcon, SpeakerWaveIcon, BellAlertIcon } from '@heroicons/react/24/outline';

interface RecentActivity {
  id: string;
  type: 'call' | 'sms' | 'voicemail' | 'alert';
  timestamp: string;
  details: string;
  link?: string;
}

interface RecentActivityFeedProps {
  data: RecentActivity[];
  isLoading: boolean;
  error: Error | null;
  onRetry: () => void;
  limit?: number;
}

export default function RecentActivityFeed({
  data,
  isLoading,
  error,
  onRetry,
  limit = 5
}: RecentActivityFeedProps) {
  const [activeFilter, setActiveFilter] = useState<string>('all');

  // Get icon based on activity type
  const getIconForType = (type: string) => {
    switch (type) {
      case 'call':
        return PhoneIcon;
      case 'sms':
        return ChatBubbleLeftRightIcon;
      case 'voicemail':
        return SpeakerWaveIcon;
      case 'alert':
        return BellAlertIcon;
      default:
        return BellAlertIcon;
    }
  };

  // Filter activities based on selected filter
  const filteredActivities = activeFilter === 'all'
    ? data
    : data.filter(activity => activity.type === activeFilter);

  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <LoadingSpinner />
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <ErrorMessage
        title="Could not load recent activity"
        message="We couldn't load your recent activity. Please try again later."
        error={error}
        onRetry={onRetry}
      />
    );
  }

  // Handle empty state
  if (!data || data.length === 0) {
    return (
      <EmptyState
        title="No recent activity"
        message="You don't have any recent activity yet. As you use CallSaver, your activity will appear here."
        icon={BellAlertIcon}
      />
    );
  }

  return (
    <div>
      {/* Filter tabs */}
      <div className="flex space-x-1 sm:space-x-2 mb-3 sm:mb-4 overflow-x-auto pb-2 -mx-1 px-1">
        <button
          type="button"
          onClick={() => setActiveFilter('all')}
          className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap ${
            activeFilter === 'all'
              ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200'
              : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          All
        </button>
        <button
          type="button"
          onClick={() => setActiveFilter('call')}
          className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap ${
            activeFilter === 'call'
              ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200'
              : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          Calls
        </button>
        <button
          type="button"
          onClick={() => setActiveFilter('sms')}
          className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap ${
            activeFilter === 'sms'
              ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200'
              : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          Messages
        </button>
        <button
          type="button"
          onClick={() => setActiveFilter('voicemail')}
          className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap ${
            activeFilter === 'voicemail'
              ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200'
              : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          Voicemails
        </button>
        <button
          type="button"
          onClick={() => setActiveFilter('alert')}
          className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap ${
            activeFilter === 'alert'
              ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200'
              : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          Alerts
        </button>
      </div>

      {/* Activity list */}
      <div className="space-y-3 sm:space-y-4">
        {filteredActivities.length === 0 ? (
          <EmptyState
            title={`No ${activeFilter} activity`}
            message={`You don't have any ${activeFilter} activity yet.`}
            icon={getIconForType(activeFilter)}
          />
        ) : (
          filteredActivities.slice(0, limit).map((activity) => (
            <ActivityItem
              key={activity.id}
              itemData={activity}
              icon={getIconForType(activity.type)}
            />
          ))
        )}
      </div>

      {/* View all link */}
      {filteredActivities.length > limit && (
        <div className="mt-3 sm:mt-4 text-center">
          <a
            href={activeFilter === 'all' ? '/activity' : `/activity?type=${activeFilter}`}
            className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 font-medium text-sm sm:text-base"
          >
            View all {activeFilter === 'all' ? 'activity' : activeFilter}
          </a>
        </div>
      )}
    </div>
  );
}
