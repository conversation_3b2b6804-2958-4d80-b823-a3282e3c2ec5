import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';
import PermissionGate from '../PermissionGate';
import { Spinner } from '../ui/Spinner';
import toast from 'react-hot-toast';
import { useRoleManagement } from '../../hooks/useRoleManagement';
import RoleCreationModal from './RoleCreationModal';
import RoleDeletionConfirmationDialog from './RoleDeletionConfirmationDialog';
import RoleCloneModal from './RoleCloneModal';
import RoleExportImportModal from './RoleExportImportModal';

// Types are now imported from useRoleManagement

const RoleManagement: React.FC = () => {
  const { hasPermission } = useAuthStore();
  const [selectedRole, setSelectedRole] = useState<any | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isCloneModalOpen, setIsCloneModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [rolePermissions, setRolePermissions] = useState<string[]>([]);


  // Use the role management hook
  const {
    roles,
    permissions: permissionsData,
    updateRolePermissions,
    isUpdatingRolePermissions
  } = useRoleManagement();

  // Handle role selection
  const handleRoleSelect = (role: any) => {
    setSelectedRole(role);
    setRolePermissions(role.permissions);
    setEditMode(false);
  };

  // Handle permission toggle
  const handlePermissionToggle = (permissionId: string) => {
    if (rolePermissions.includes(permissionId)) {
      setRolePermissions(rolePermissions.filter((id) => id !== permissionId));
    } else {
      setRolePermissions([...rolePermissions, permissionId]);
    }
  };

  // Handle save
  const handleSave = () => {
    if (selectedRole) {
      updateRolePermissions({
        roleId: selectedRole.id,
        permissions: rolePermissions,
      });
    }
  };

  // Get permissions from groups and ungrouped
  const permissionGroups = permissionsData?.data?.groups || [];
  const ungroupedPermissions = permissionsData?.data?.ungrouped || [];

  // Create a map of all permissions for easy lookup
  const allPermissions = React.useMemo(() => {
    const map = new Map<string, Permission>();

    // Add permissions from groups
    permissionGroups.forEach(group => {
      group.permissions.forEach(permission => {
        map.set(permission.id, permission);
      });
    });

    // Add ungrouped permissions
    ungroupedPermissions.forEach(permission => {
      map.set(permission.id, permission);
    });

    return map;
  }, [permissionGroups, ungroupedPermissions]);

  if (roles.isLoading || permissionsData.isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <PermissionGate permission="roles:read:any">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold mb-6">Role Management</h2>

        {/* Role Creation Modal */}
        <RoleCreationModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
        />

        {/* Role Deletion Confirmation Dialog */}
        <RoleDeletionConfirmationDialog
          role={selectedRole}
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
        />

        {/* Role Clone Modal */}
        <RoleCloneModal
          role={selectedRole}
          isOpen={isCloneModalOpen}
          onClose={() => setIsCloneModalOpen(false)}
        />

        {/* Role Export Modal */}
        <RoleExportImportModal
          isOpen={isExportModalOpen}
          onClose={() => setIsExportModalOpen(false)}
          mode="export"
        />

        {/* Role Import Modal */}
        <RoleExportImportModal
          isOpen={isImportModalOpen}
          onClose={() => setIsImportModalOpen(false)}
          mode="import"
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Role List */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Roles</h3>
              <div className="flex space-x-2">
                <PermissionGate permission="roles:read:any">
                  <button
                    type="button"
                    className="px-3 py-1.5 bg-gray-200 text-gray-700 text-sm rounded-md hover:bg-gray-300"
                    onClick={() => setIsExportModalOpen(true)}
                    title="Export roles"
                  >
                    Export
                  </button>
                </PermissionGate>
                <PermissionGate permission="roles:create:any">
                  <button
                    type="button"
                    className="px-3 py-1.5 bg-gray-200 text-gray-700 text-sm rounded-md hover:bg-gray-300"
                    onClick={() => setIsImportModalOpen(true)}
                    title="Import roles"
                  >
                    Import
                  </button>
                </PermissionGate>
                <PermissionGate permission="roles:create:any">
                  <button
                    type="button"
                    className="px-3 py-1.5 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600"
                    onClick={() => setIsCreateModalOpen(true)}
                  >
                    New Role
                  </button>
                </PermissionGate>
              </div>
            </div>
            <div className="space-y-2">
              {roles.data?.map((role) => (
                <div
                  key={role.id}
                  className={`p-3 rounded-md cursor-pointer transition-colors ${
                    selectedRole?.id === role.id
                      ? 'bg-blue-100 border-l-4 border-blue-500'
                      : 'bg-white hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div
                      className="flex items-center gap-2 flex-1"
                      onClick={() => handleRoleSelect(role)}
                    >
                      <div className="font-medium">{role.name}</div>
                      <div className="flex gap-1">
                        {role.isSystem && (
                          <span className="px-1.5 py-0.5 text-xs bg-gray-200 text-gray-700 rounded">
                            System
                          </span>
                        )}
                        {role.isDefault && (
                          <span className="px-1.5 py-0.5 text-xs bg-green-200 text-green-700 rounded">
                            Default
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Role actions */}
                    <div className="flex items-center space-x-2">
                      {/* Clone button */}
                      <PermissionGate permission="roles:create:any">
                        <button
                          type="button"
                          className="text-blue-500 hover:text-blue-700 p-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRoleSelect(role);
                            setIsCloneModalOpen(true);
                          }}
                          title="Clone role"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </button>
                      </PermissionGate>

                      {/* Delete button */}
                      <PermissionGate permission="roles:delete:any">
                        <button
                          type="button"
                          className="text-red-500 hover:text-red-700 p-1"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRoleSelect(role);
                            setIsDeleteModalOpen(true);
                          }}
                          disabled={role.isSystem || role.children.length > 0}
                          title={role.isSystem ? 'Cannot delete system role' :
                                 role.children.length > 0 ? 'Cannot delete role with child roles' :
                                 'Delete role'}
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </PermissionGate>
                    </div>
                  </div>
                  <div className="text-sm text-gray-500 mt-1">{role.description}</div>
                  {role.parentName && (
                    <div className="text-xs text-gray-400 mt-1">
                      Inherits from: <span className="font-medium">{role.parentName}</span>
                    </div>
                  )}
                  {role.children.length > 0 && (
                    <div className="text-xs text-gray-400 mt-1">
                      Parent to: {role.children.map(child => child.name).join(', ')}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Permissions */}
          <div className="md:col-span-2">
            {selectedRole ? (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold">
                    Permissions for {selectedRole.name}
                  </h3>
                  <div className="space-x-2">
                    {editMode ? (
                      <>
                        <button
                          type="button"
                          className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                          onClick={() => {
                            setRolePermissions(selectedRole.permissions);
                            setEditMode(false);
                          }}
                        >
                          Cancel
                        </button>
                        <button
                          type="button"
                          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                          onClick={handleSave}
                          disabled={isUpdatingRolePermissions}
                        >
                          {isUpdatingRolePermissions ? (
                            <Spinner size="sm" className="text-white" />
                          ) : (
                            'Save'
                          )}
                        </button>
                      </>
                    ) : (
                      <PermissionGate permission="roles:update:any">
                        <button
                          type="button"
                          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                          onClick={() => setEditMode(true)}
                        >
                          Edit
                        </button>
                      </PermissionGate>
                    )}
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Display permissions by groups */}
                  {permissionGroups.map((group) => (
                    <div key={group.id} className="border rounded-md p-4">
                      <h4 className="font-medium mb-2">{group.name}</h4>
                      {group.description && (
                        <p className="text-sm text-gray-500 mb-3">{group.description}</p>
                      )}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        {group.permissions.map((permission) => (
                          <div
                            key={permission.id}
                            className="flex items-center space-x-2"
                          >
                            <input
                              type="checkbox"
                              id={permission.id}
                              checked={rolePermissions.includes(permission.id)}
                              onChange={() => handlePermissionToggle(permission.id)}
                              disabled={!editMode}
                              className="h-4 w-4 text-blue-600 rounded"
                            />
                            <label
                              htmlFor={permission.id}
                              className="text-sm cursor-pointer"
                            >
                              <span className="font-medium">
                                {permission.displayName || `${permission.action}${permission.scope !== 'any' ? `:${permission.scope}` : ''}`}
                              </span>
                              <span className="text-xs text-gray-500 block">
                                {permission.description}
                              </span>
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}

                  {/* Display ungrouped permissions */}
                  {ungroupedPermissions.length > 0 && (
                    <div className="border rounded-md p-4">
                      <h4 className="font-medium mb-2">Other Permissions</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        {ungroupedPermissions.map((permission) => (
                          <div
                            key={permission.id}
                            className="flex items-center space-x-2"
                          >
                            <input
                              type="checkbox"
                              id={permission.id}
                              checked={rolePermissions.includes(permission.id)}
                              onChange={() => handlePermissionToggle(permission.id)}
                              disabled={!editMode}
                              className="h-4 w-4 text-blue-600 rounded"
                            />
                            <label
                              htmlFor={permission.id}
                              className="text-sm cursor-pointer"
                            >
                              <span className="font-medium">
                                {permission.displayName || `${permission.action}${permission.scope !== 'any' ? `:${permission.scope}` : ''}`}
                              </span>
                              <span className="text-xs text-gray-500 block">
                                {permission.description}
                              </span>
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex justify-center items-center h-64 bg-gray-50 rounded-lg">
                <p className="text-gray-500">Select a role to view permissions</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </PermissionGate>
  );
};

export default RoleManagement;


