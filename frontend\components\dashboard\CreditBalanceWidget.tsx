'use client';

import Link from 'next/link';
import { CreditCardIcon, PlusCircleIcon } from '@heroicons/react/24/outline';

interface CreditBalanceWidgetProps {
  balance: number;
  isLoading: boolean;
  linkToBilling: string;
}

export default function CreditBalanceWidget({ balance, isLoading, linkToBilling }: CreditBalanceWidgetProps) {
  // Format the balance with 2 decimal places
  const formattedBalance = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(balance);

  // Determine the status color based on the balance
  const getStatusColor = (balance: number) => {
    if (balance <= 10) return 'text-red-600 dark:text-red-400';
    if (balance <= 50) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-green-600 dark:text-green-400';
  };

  // Determine the status message based on the balance
  const getStatusMessage = (balance: number) => {
    if (balance <= 10) return 'Low balance';
    if (balance <= 50) return 'Moderate balance';
    return 'Good balance';
  };

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded-lg mb-4"></div>
        <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className="p-2 bg-indigo-100 dark:bg-indigo-900 rounded-full mr-3">
            <CreditCardIcon className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Available Credits</p>
            <p className="text-2xl font-semibold text-gray-900 dark:text-white">{formattedBalance}</p>
          </div>
        </div>
        <span className={`text-sm font-medium ${getStatusColor(balance)}`}>
          {getStatusMessage(balance)}
        </span>
      </div>

      <div className="mt-4 grid grid-cols-2 gap-3">
        <Link 
          href={linkToBilling}
          className="flex items-center justify-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors duration-150"
        >
          View Billing
        </Link>
        <Link 
          href={`${linkToBilling}/add-credits`}
          className="flex items-center justify-center px-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-700 dark:text-white rounded-lg transition-colors duration-150"
        >
          <PlusCircleIcon className="h-4 w-4 mr-2" />
          Add Credits
        </Link>
      </div>
    </div>
  );
}
