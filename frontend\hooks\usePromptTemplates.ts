import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { PromptTemplate } from '../components/ai-training/PromptTemplateEditor';

/**
 * Hook for managing prompt templates
 */
export function usePromptTemplates() {
  const queryClient = useQueryClient();
  
  // Fetch all prompt templates
  const getPromptTemplates = (category?: string) => {
    return useQuery({
      queryKey: ['ai', 'prompt-templates', { category }],
      queryFn: async () => {
        // In development, use mock data
        if (process.env.NODE_ENV === 'development') {
          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 800));
          
          // Mock data
          const templates = [
            {
              id: '1',
              name: 'Call Summary',
              description: 'Template for summarizing phone calls',
              template: 'You are a helpful assistant summarizing a customer service call. Please provide a concise summary of the following conversation: {{transcription}}',
              category: 'summarization',
              version: '1.0',
              isActive: true,
              createdAt: '2023-06-01T10:30:00Z',
              updatedAt: '2023-06-01T10:30:00Z',
            },
            {
              id: '2',
              name: 'SMS Response',
              description: 'Template for responding to SMS messages',
              template: 'You are a helpful SMS assistant. Keep responses concise and helpful (under 160 characters when possible). Respond to: {{message}}',
              category: 'messaging',
              version: '1.2',
              isActive: true,
              createdAt: '2023-06-05T14:45:00Z',
              updatedAt: '2023-06-10T09:15:00Z',
            },
            {
              id: '3',
              name: 'Voice Call Greeting',
              description: 'Template for initial voice call greeting',
              template: 'You are a voice assistant making a phone call. Introduce yourself briefly and state the purpose of the call: {{purpose}}',
              category: 'voice',
              version: '1.0',
              isActive: false,
              createdAt: '2023-06-10T09:15:00Z',
              updatedAt: '2023-06-10T09:15:00Z',
            },
          ] as PromptTemplate[];
          
          // Filter by category if provided
          return category 
            ? templates.filter(t => t.category === category)
            : templates;
        }
        
        // In production, fetch from API
        const url = category 
          ? `/api/ai/prompt-templates?category=${category}`
          : '/api/ai/prompt-templates';
        
        const { data } = await axios.get<PromptTemplate[]>(url);
        return data;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };
  
  // Get a single prompt template by ID
  const getPromptTemplate = (id: string) => {
    return useQuery({
      queryKey: ['ai', 'prompt-templates', id],
      queryFn: async () => {
        // In development, use mock data
        if (process.env.NODE_ENV === 'development') {
          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 500));
          
          // Mock data based on ID
          const templates = {
            '1': {
              id: '1',
              name: 'Call Summary',
              description: 'Template for summarizing phone calls',
              template: 'You are a helpful assistant summarizing a customer service call. Please provide a concise summary of the following conversation: {{transcription}}',
              category: 'summarization',
              version: '1.0',
              isActive: true,
              createdAt: '2023-06-01T10:30:00Z',
              updatedAt: '2023-06-01T10:30:00Z',
            },
            '2': {
              id: '2',
              name: 'SMS Response',
              description: 'Template for responding to SMS messages',
              template: 'You are a helpful SMS assistant. Keep responses concise and helpful (under 160 characters when possible). Respond to: {{message}}',
              category: 'messaging',
              version: '1.2',
              isActive: true,
              createdAt: '2023-06-05T14:45:00Z',
              updatedAt: '2023-06-10T09:15:00Z',
            },
            '3': {
              id: '3',
              name: 'Voice Call Greeting',
              description: 'Template for initial voice call greeting',
              template: 'You are a voice assistant making a phone call. Introduce yourself briefly and state the purpose of the call: {{purpose}}',
              category: 'voice',
              version: '1.0',
              isActive: false,
              createdAt: '2023-06-10T09:15:00Z',
              updatedAt: '2023-06-10T09:15:00Z',
            },
          };
          
          return templates[id as keyof typeof templates] || null;
        }
        
        // In production, fetch from API
        const { data } = await axios.get<PromptTemplate>(`/api/ai/prompt-templates/${id}`);
        return data;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };
  
  // Create a new prompt template
  const createPromptTemplate = () => {
    return useMutation({
      mutationFn: async (template: Omit<PromptTemplate, 'id' | 'version' | 'createdAt' | 'updatedAt'>) => {
        // In development, use mock data
        if (process.env.NODE_ENV === 'development') {
          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Return mock success
          return { 
            success: true, 
            id: `${Date.now()}`,
            ...template,
            version: '1.0',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
        }
        
        // In production, call API
        const { data } = await axios.post('/api/ai/prompt-templates', template);
        return data;
      },
      onSuccess: () => {
        // Invalidate queries to refetch data
        queryClient.invalidateQueries({ queryKey: ['ai', 'prompt-templates'] });
      },
    });
  };
  
  // Update an existing prompt template
  const updatePromptTemplate = () => {
    return useMutation({
      mutationFn: async (template: Partial<PromptTemplate> & { id: string }) => {
        // In development, use mock data
        if (process.env.NODE_ENV === 'development') {
          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 800));
          
          // Return mock success
          return { 
            success: true,
            ...template,
            updatedAt: new Date().toISOString(),
          };
        }
        
        // In production, call API
        const { data } = await axios.put(`/api/ai/prompt-templates/${template.id}`, template);
        return data;
      },
      onSuccess: (_, variables) => {
        // Invalidate queries to refetch data
        queryClient.invalidateQueries({ queryKey: ['ai', 'prompt-templates'] });
        queryClient.invalidateQueries({ queryKey: ['ai', 'prompt-templates', variables.id] });
      },
    });
  };
  
  // Delete a prompt template
  const deletePromptTemplate = () => {
    return useMutation({
      mutationFn: async (id: string) => {
        // In development, use mock data
        if (process.env.NODE_ENV === 'development') {
          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 600));
          
          // Return mock success
          return { success: true };
        }
        
        // In production, call API
        const { data } = await axios.delete(`/api/ai/prompt-templates/${id}`);
        return data;
      },
      onSuccess: () => {
        // Invalidate queries to refetch data
        queryClient.invalidateQueries({ queryKey: ['ai', 'prompt-templates'] });
      },
    });
  };
  
  // Toggle template active status
  const toggleTemplateStatus = () => {
    return useMutation({
      mutationFn: async ({ id, isActive }: { id: string; isActive: boolean }) => {
        // In development, use mock data
        if (process.env.NODE_ENV === 'development') {
          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 500));
          
          // Return mock success
          return { success: true };
        }
        
        // In production, call API
        const { data } = await axios.patch(`/api/ai/prompt-templates/${id}`, { isActive });
        return data;
      },
      onSuccess: (_, variables) => {
        // Invalidate queries to refetch data
        queryClient.invalidateQueries({ queryKey: ['ai', 'prompt-templates'] });
        queryClient.invalidateQueries({ queryKey: ['ai', 'prompt-templates', variables.id] });
      },
    });
  };
  
  return {
    getPromptTemplates,
    getPromptTemplate,
    createPromptTemplate,
    updatePromptTemplate,
    deletePromptTemplate,
    toggleTemplateStatus,
  };
}
