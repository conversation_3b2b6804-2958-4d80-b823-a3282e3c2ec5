'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePermissions } from '../../hooks/usePermissions';
import PermissionAwareLink from './PermissionAwareLink';

interface NavItem {
  name: string;
  href: string;
  permission?: string;
  resource?: string;
  action?: string;
  scope?: string;
}

interface PermissionAwareNavbarProps {
  logoUrl?: string;
  logoText?: string;
  navItems?: NavItem[];
  className?: string;
  isAuthenticated?: boolean;
  userEmail?: string;
  userName?: string;
  userAvatarUrl?: string;
  onSignOut?: () => void;
}

/**
 * A navbar component that only shows navigation items the user has permission to access
 */
const PermissionAwareNavbar: React.FC<PermissionAwareNavbarProps> = ({
  logoUrl = '/logo.svg',
  logoText = 'CallSaver',
  navItems = [],
  className = '',
  isAuthenticated = false,
  userEmail = '',
  userName = '',
  userAvatarUrl = '',
  onSignOut,
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { isAdmin, isDeveloper } = usePermissions();

  // Toggle mobile menu
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // Toggle user menu
  const toggleUserMenu = () => {
    setIsUserMenuOpen(!isUserMenuOpen);
  };

  // Close menus when clicking outside
  const closeMenus = () => {
    setIsMenuOpen(false);
    setIsUserMenuOpen(false);
  };

  return (
    <nav className={`bg-gray-900 text-white shadow-md ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and main nav */}
          <div className="flex">
            <div className="flex-shrink-0 flex items-center">
              <Link href="/" className="flex items-center">
                <img className="h-8 w-auto" src={logoUrl} alt={logoText} />
                <span className="ml-2 text-xl font-bold">{logoText}</span>
              </Link>
            </div>
            
            {/* Desktop navigation */}
            <div className="hidden sm:ml-6 sm:flex sm:space-x-8">
              {navItems.map((item) => (
                <PermissionAwareLink
                  key={item.href}
                  href={item.href}
                  permission={item.permission}
                  resource={item.resource}
                  action={item.action}
                  scope={item.scope}
                  className="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-gray-300 hover:text-white hover:border-gray-300"
                  activeClassName="border-blue-500 text-white"
                >
                  {item.name}
                </PermissionAwareLink>
              ))}
            </div>
          </div>
          
          {/* User section */}
          <div className="flex items-center">
            {isAuthenticated ? (
              <div className="ml-3 relative">
                <div>
                  <button
                    type="button"
                    className="flex text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white"
                    id="user-menu-button"
                    aria-expanded={isUserMenuOpen}
                    aria-haspopup="true"
                    onClick={toggleUserMenu}
                  >
                    <span className="sr-only">Open user menu</span>
                    {userAvatarUrl ? (
                      <img
                        className="h-8 w-8 rounded-full"
                        src={userAvatarUrl}
                        alt={userName || userEmail}
                      />
                    ) : (
                      <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                        <span className="text-white font-medium">
                          {userName ? userName.charAt(0).toUpperCase() : userEmail.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                  </button>
                </div>
                
                {/* User dropdown menu */}
                {isUserMenuOpen && (
                  <div
                    className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
                    role="menu"
                    aria-orientation="vertical"
                    aria-labelledby="user-menu-button"
                    tabIndex={-1}
                  >
                    <div className="px-4 py-2 text-sm text-gray-700 border-b">
                      <div className="font-medium">{userName || 'User'}</div>
                      <div className="text-gray-500 truncate">{userEmail}</div>
                    </div>
                    
                    <Link
                      href="/dashboard"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      role="menuitem"
                      onClick={closeMenus}
                    >
                      Dashboard
                    </Link>
                    
                    <Link
                      href="/dashboard/settings"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      role="menuitem"
                      onClick={closeMenus}
                    >
                      Settings
                    </Link>
                    
                    {isAdmin() && (
                      <Link
                        href="/dashboard/admin"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        role="menuitem"
                        onClick={closeMenus}
                      >
                        Admin Panel
                      </Link>
                    )}
                    
                    {isDeveloper() && (
                      <Link
                        href="/dashboard/developer"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        role="menuitem"
                        onClick={closeMenus}
                      >
                        Developer Tools
                      </Link>
                    )}
                    
                    <button
                      className="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-gray-100"
                      role="menuitem"
                      onClick={() => {
                        closeMenus();
                        if (onSignOut) onSignOut();
                      }}
                    >
                      Sign out
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex space-x-4">
                <Link
                  href="/login"
                  className="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium"
                >
                  Log in
                </Link>
                <Link
                  href="/signup"
                  className="bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700"
                >
                  Sign up
                </Link>
              </div>
            )}
            
            {/* Mobile menu button */}
            <div className="flex items-center sm:hidden ml-4">
              <button
                type="button"
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                aria-controls="mobile-menu"
                aria-expanded={isMenuOpen}
                onClick={toggleMenu}
              >
                <span className="sr-only">Open main menu</span>
                {isMenuOpen ? (
                  <svg
                    className="block h-6 w-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                ) : (
                  <svg
                    className="block h-6 w-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  </svg>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="sm:hidden" id="mobile-menu">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navItems.map((item) => (
              <PermissionAwareLink
                key={item.href}
                href={item.href}
                permission={item.permission}
                resource={item.resource}
                action={item.action}
                scope={item.scope}
                className="block px-3 py-2 rounded-md text-base font-medium text-gray-300 hover:bg-gray-700 hover:text-white"
                activeClassName="bg-gray-900 text-white"
                onClick={closeMenus}
              >
                {item.name}
              </PermissionAwareLink>
            ))}
          </div>
        </div>
      )}
    </nav>
  );
};

export default PermissionAwareNavbar;
