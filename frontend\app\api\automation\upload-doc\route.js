import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid'; // For generating unique file names if needed

export const dynamic = 'force-dynamic';

// Define allowed MIME types for security
const ALLOWED_MIME_TYPES = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
  'text/plain', // .txt
  'text/csv', // .csv
];
const MAX_FILE_SIZE_MB = 10; // Set a max file size limit (e.g., 10MB)

export async function POST(request) {
  const supabase = createRouteHandlerClient({ cookies });

  try {
    // 1. Get User Session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
      console.error('Error getting session or no session found:', sessionError);
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 401 });
    }
    const userId = session.user.id;

    // 2. Parse FormData
    const formData = await request.formData();
    const file = formData.get('file');
    const phoneNumber = formData.get('phoneNumber'); // Get phone number

    if (!file) {
      return NextResponse.json({ success: false, message: 'No file provided' }, { status: 400 });
    }

    if (!phoneNumber) {
      return NextResponse.json({ success: false, message: 'No phone number provided' }, { status: 400 });
    }

    // 3. Validate File
    if (!ALLOWED_MIME_TYPES.includes(file.type)) {
      return NextResponse.json({ success: false, message: `Invalid file type. Allowed types: ${ALLOWED_MIME_TYPES.join(', ')}` }, { status: 400 });
    }
    if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
      return NextResponse.json({ success: false, message: `File size exceeds ${MAX_FILE_SIZE_MB}MB limit` }, { status: 400 });
    }

    // 4. Upload to Supabase Storage
    // Use user ID in the path for organization and RLS policies
    const fileExtension = file.name.split('.').pop();
    const uniqueFileName = `${uuidv4()}.${fileExtension}`; // Generate unique name to avoid conflicts
    const storagePath = `${userId}/${uniqueFileName}`; // Path like: user_id/unique_file_name.pdf
    const bucketName = 'training_documents'; // Make sure this bucket exists and has policies set

    console.log(`[API /upload-doc] Uploading ${file.name} to ${bucketName}/${storagePath}`);

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from(bucketName)
      .upload(storagePath, file, {
        cacheControl: '3600', // Optional: Cache control
        upsert: false, // Don't overwrite existing files with the same name (shouldn't happen with UUID)
      });

    if (uploadError) {
      console.error('Error uploading file to Supabase Storage:', uploadError);
      throw new Error(`Storage upload failed: ${uploadError.message}`);
    }

    console.log('[API /upload-doc] File uploaded successfully:', uploadData);

    // 5. Insert Record into Database
    const { data: dbData, error: dbError } = await supabase
      .from('training_documents')
      .insert({
        user_id: userId,
        file_name: file.name, // Original file name
        storage_path: storagePath, // Path within the bucket
        file_size: file.size,
        mime_type: file.type,
        status: 'processing', // Initial status
        phone_number: phoneNumber, // Store the associated phone number
      })
      .select('id') // Select the ID of the newly inserted row
      .single(); // Expect only one row back

    if (dbError) {
      console.error('Error inserting document record into database:', dbError);
      // Attempt to delete the orphaned file from storage if DB insert fails
      try {
        await supabase.storage.from(bucketName).remove([storagePath]);
        console.warn(`[API /upload-doc] Rolled back storage upload for ${storagePath} due to DB error.`);
      } catch (rollbackError) {
        console.error(`[API /upload-doc] CRITICAL: Failed to rollback storage upload for ${storagePath} after DB error:`, rollbackError);
      }
      throw new Error(`Database insert failed: ${dbError.message}`);
    }

    console.log(`[API /upload-doc] Document record created with ID: ${dbData.id}`);

    // TODO: Trigger background processing/embedding job here if needed

    // Log the successful upload with phone number
    console.log(`[API /upload-doc] Document uploaded for phone number: ${phoneNumber}`);

    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully',
      documentId: dbData.id,
      phoneNumber: phoneNumber,
      fileName: file.name
    });

  } catch (error) {
    console.error('[API /upload-doc] POST Error:', error);
    return NextResponse.json({ success: false, message: error.message || 'File upload failed' }, { status: 500 });
  }
}
