'use client';

import { useState, useEffect, useRef } from 'react';
import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { Mic, Pause, Play, Send, Trash, Volume2, Bot, User, Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { CircularProgressbar, buildStyles } from 'react-circular-progressbar';
import 'react-circular-progressbar/dist/styles.css';

// Types
interface AIConversationTesterProps {
  numberId: string;
  mode: 'call' | 'sms';
}

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  isProcessing?: boolean;
  audioUrl?: string;
}

interface TestConfig {
  useRealAI: boolean;
  simulateLatency: boolean;
  useVoiceInput: boolean;
  useVoiceOutput: boolean;
}

// Main Component
export default function AIConversationTester({
  numberId,
  mode
}: AIConversationTesterProps) {
  // State
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState<string | null>(null);
  const [config, setConfig] = useState<TestConfig>({
    useRealAI: true,
    simulateLatency: true,
    useVoiceInput: mode === 'call',
    useVoiceOutput: mode === 'call'
  });

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Effect for scrolling to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Effect for cleanup on unmount
  useEffect(() => {
    return () => {
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }
      
      if (audioRef.current) {
        audioRef.current.pause();
      }
    };
  }, []);

  // AI Response Mutation
  const aiResponseMutation = useMutation({
    mutationFn: async (messageContent: string) => {
      const endpoint = mode === 'call' 
        ? `/api/automation/call/test-response` 
        : `/api/automation/sms/test-response`;
      
      // In development or if simulateLatency is true, add artificial delay
      if (config.simulateLatency || process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1500));
      }
      
      // In development, return mock data
      if (process.env.NODE_ENV === 'development') {
        const aiResponses = [
          "Thank you for reaching out! I'm the CallSaver AI assistant. How can I help you today?",
          "I understand you're asking about our services. We offer call management, voicemail transcription, and AI-powered responses. What specific information are you looking for?",
          "That's a great question. Our pricing starts at $9.99/month for the basic plan, which includes one phone number and 100 minutes of call time. Would you like me to tell you about our other plans as well?",
          "I've noted down your appointment request for next Tuesday at 2 PM. Is there anything else you'd like me to help with?",
          "I'm sorry to hear you're experiencing issues. Let me help troubleshoot. First, could you tell me what specific error you're seeing?"
        ];
        
        return {
          response: aiResponses[Math.floor(Math.random() * aiResponses.length)],
          audioUrl: mode === 'call' ? 'https://example.com/mock-audio.mp3' : undefined
        };
      }
      
      // In production, call API
      const { data } = await axios.post(endpoint, {
        numberId,
        message: messageContent,
        useRealAI: config.useRealAI,
        generateAudio: config.useVoiceOutput && mode === 'call'
      });
      
      return data;
    },
    onSuccess: (data, variables) => {
      // Add AI response to messages
      const newMessage: Message = {
        id: `ai-${Date.now()}`,
        content: data.response,
        sender: 'ai',
        timestamp: new Date(),
        audioUrl: data.audioUrl
      };
      
      // Update the messages array by replacing the placeholder with the actual response
      setMessages(prevMessages => 
        prevMessages.map(msg => 
          msg.isProcessing && msg.sender === 'ai' 
            ? newMessage 
            : msg
        )
      );
      
      // Play audio if voice output is enabled and we're in call mode
      if (config.useVoiceOutput && mode === 'call' && data.audioUrl) {
        playAudio(newMessage.id, data.audioUrl);
      }
    },
    onError: (error) => {
      console.error('Error getting AI response:', error);
      
      // Update the processing message with an error
      setMessages(prevMessages => 
        prevMessages.map(msg => 
          msg.isProcessing && msg.sender === 'ai' 
            ? {
                ...msg,
                content: "I'm sorry, but I encountered an error while processing your request. Please try again.",
                isProcessing: false
              } 
            : msg
        )
      );
    }
  });

  // Speech Recognition setup (if available in browser)
  const startRecording = () => {
    if (typeof window === 'undefined' || (!window.SpeechRecognition && !window.webkitSpeechRecognition)) {
      alert("Speech recognition is not supported in your browser");
      return;
    }
    
    const SpeechRecognitionAPI = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognitionAPI();
    
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = 'en-US';
    
    recognition.onresult = (event) => {
      const transcript = Array.from(event.results)
        .map((result: any) => result[0].transcript)
        .join('');
      
      setInput(transcript);
    };
    
    recognition.onerror = (event) => {
      console.error('Speech recognition error', event.error);
      setIsRecording(false);
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }
    };
    
    recognition.onend = () => {
      setIsRecording(false);
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }
    };
    
    recognition.start();
    setIsRecording(true);
    setRecordingTime(0);
    
    // Start timer
    recordingTimerRef.current = setInterval(() => {
      setRecordingTime(prev => prev + 1);
      // Auto-stop after 60 seconds
      if (recordingTime >= 60) {
        if (recognition) {
          recognition.stop();
        }
        if (recordingTimerRef.current) {
          clearInterval(recordingTimerRef.current);
        }
        setIsRecording(false);
      }
    }, 1000);
  };

  const stopRecording = () => {
    if (typeof window !== 'undefined' && (window.SpeechRecognition || window.webkitSpeechRecognition)) {
      const SpeechRecognitionAPI = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognitionAPI();
      recognition.stop();
    }
    
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
    }
    
    setIsRecording(false);
  };

  // Play audio function
  const playAudio = (messageId: string, url: string) => {
    if (audioRef.current) {
      audioRef.current.pause();
    }
    
    audioRef.current = new Audio(url);
    audioRef.current.onended = () => setIsPlaying(null);
    audioRef.current.play();
    setIsPlaying(messageId);
  };

  // Stop audio function
  const stopAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(null);
    }
  };

  // Send message function
  const sendMessage = () => {
    if (!input.trim()) return;
    
    // Add user message
    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content: input.trim(),
      sender: 'user',
      timestamp: new Date()
    };
    
    // Add placeholder for AI response
    const aiPlaceholder: Message = {
      id: `ai-placeholder-${Date.now()}`,
      content: "",
      sender: 'ai',
      timestamp: new Date(),
      isProcessing: true
    };
    
    setMessages(prev => [...prev, userMessage, aiPlaceholder]);
    setInput('');
    
    // Trigger AI response
    aiResponseMutation.mutate(userMessage.content);
  };

  // Handle config changes
  const handleConfigChange = (key: keyof TestConfig) => {
    setConfig(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Handle clear chat
  const clearChat = () => {
    setMessages([]);
    stopAudio();
  };

  return (
    <div className="flex flex-col h-[600px] rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm bg-white dark:bg-gray-800 overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <div>
          <h3 className="font-medium text-gray-900 dark:text-white">
            {mode === 'call' ? 'Call AI Assistant Tester' : 'SMS AI Assistant Tester'}
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Test your AI's responses before deploying
          </p>
        </div>
        <div className="flex space-x-2">
          <button
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            onClick={clearChat}
            title="Clear conversation"
          >
            <Trash size={18} />
          </button>
        </div>
      </div>
      
      {/* Configuration panel */}
      <div className="p-3 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 flex flex-wrap gap-4">
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="useRealAI"
            checked={config.useRealAI}
            onChange={() => handleConfigChange('useRealAI')}
            className="h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
          />
          <label htmlFor="useRealAI" className="text-sm text-gray-700 dark:text-gray-300">
            Use real AI (costs credits)
          </label>
        </div>
        
        <div className="flex items-center gap-2">
          <input
            type="checkbox"
            id="simulateLatency"
            checked={config.simulateLatency}
            onChange={() => handleConfigChange('simulateLatency')}
            className="h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
          />
          <label htmlFor="simulateLatency" className="text-sm text-gray-700 dark:text-gray-300">
            Simulate latency
          </label>
        </div>
        
        {mode === 'call' && (
          <>
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="useVoiceInput"
                checked={config.useVoiceInput}
                onChange={() => handleConfigChange('useVoiceInput')}
                className="h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="useVoiceInput" className="text-sm text-gray-700 dark:text-gray-300">
                Voice input
              </label>
            </div>
            
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="useVoiceOutput"
                checked={config.useVoiceOutput}
                onChange={() => handleConfigChange('useVoiceOutput')}
                className="h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="useVoiceOutput" className="text-sm text-gray-700 dark:text-gray-300">
                Voice output
              </label>
            </div>
          </>
        )}
      </div>
      
      {/* Message history */}
      <div className="flex-1 p-4 overflow-y-auto bg-gray-50 dark:bg-gray-900">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center text-gray-500 dark:text-gray-400">
            <Bot size={48} className="mb-4 opacity-20" />
            <p className="mb-2 max-w-md">
              {mode === 'call' 
                ? 'Test how your AI assistant will respond to voice calls.' 
                : 'Test how your AI assistant will respond to SMS messages.'}
            </p>
            <p className="text-sm">
              Start a conversation to see your AI in action.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map(message => (
              <div 
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`
                  max-w-[80%] rounded-lg p-3 
                  ${message.sender === 'user' 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white'}
                `}>
                  <div className="flex items-center mb-1">
                    <span className="flex items-center text-xs opacity-75">
                      {message.sender === 'user' ? (
                        <>
                          <User size={12} className="mr-1" />
                          <span>You</span>
                        </>
                      ) : (
                        <>
                          <Bot size={12} className="mr-1" />
                          <span>AI Assistant</span>
                        </>
                      )}
                    </span>
                    {!message.isProcessing && (
                      <span className="ml-2 text-xs opacity-50">
                        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                    )}
                  </div>
                  
                  {message.isProcessing ? (
                    <div className="flex items-center text-sm">
                      <Loader2 size={16} className="mr-2 animate-spin" />
                      <span>Generating response...</span>
                    </div>
                  ) : (
                    <div className="text-sm whitespace-pre-wrap">
                      {message.content}
                    </div>
                  )}
                  
                  {/* Audio controls for AI responses with audio */}
                  {message.sender === 'ai' && message.audioUrl && !message.isProcessing && (
                    <div className="mt-2 flex items-center">
                      <button
                        className={`p-1 rounded-full ${
                          isPlaying === message.id 
                            ? 'bg-gray-300 text-gray-700 dark:bg-gray-600 dark:text-gray-300' 
                            : 'bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400'
                        }`}
                        onClick={() => isPlaying === message.id 
                          ? stopAudio() 
                          : playAudio(message.id, message.audioUrl!)
                        }
                        title={isPlaying === message.id ? 'Pause audio playback' : 'Play audio response'}
                      >
                        {isPlaying === message.id ? (
                          <Pause size={14} />
                        ) : (
                          <Play size={14} />
                        )}
                      </button>
                      <span className="ml-2 text-xs opacity-75">
                        {isPlaying === message.id ? 'Playing audio...' : 'Play audio'}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
      
      {/* Input area */}
      <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <form 
          className="flex items-center gap-2"
          onSubmit={(e) => {
            e.preventDefault();
            sendMessage();
          }}
        >
          {config.useVoiceInput && mode === 'call' && (
            <div className="relative">
              <button
                type="button"
                className={`p-2 rounded-full ${
                  isRecording 
                    ? 'bg-red-100 text-red-600 animate-pulse' 
                    : 'bg-gray-100 text-gray-500 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300'
                }`}
                onClick={isRecording ? stopRecording : startRecording}
                disabled={aiResponseMutation.isPending}
                title={isRecording ? 'Stop recording' : 'Start voice recording'}
              >
                <Mic size={18} />
              </button>
              
              {isRecording && (
                <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 w-14 h-14">
                  <CircularProgressbar
                    value={recordingTime}
                    maxValue={60}
                    text={`${recordingTime}s`}
                    styles={buildStyles({
                      textSize: '30px',
                      pathColor: '#ef4444',
                      textColor: '#ef4444',
                      trailColor: '#fee2e2',
                    })}
                  />
                </div>
              )}
            </div>
          )}
          
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={`Type ${mode === 'call' ? 'what you would say' : 'your message'}...`}
            className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            disabled={isRecording || aiResponseMutation.isPending}
          />
          
          <button
            type="submit"
            className="p-2 rounded-full bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={!input.trim() || aiResponseMutation.isPending}
            title="Send message"
          >
            <Send size={18} />
          </button>
        </form>
      </div>
    </div>
  );
}

// Add necessary types for browsers that may not have them
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}
