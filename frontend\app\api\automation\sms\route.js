import { NextResponse } from 'next/server';
import { validatePhoneNumber } from '@/utils/commandParser';

// Helper to generate unique IDs
function generateId() {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

export async function POST(request) {
  try {
    // Parse request body
    const body = await request.json();
    const { from, to, message } = body;
    
    // Validate required parameters
    if (!from || !to || !message) {
      return NextResponse.json(
        { success: false, message: 'Missing required parameters: from, to, message' },
        { status: 400 }
      );
    }
    
    // Validate phone number format
    if (!validatePhoneNumber(to)) {
      return NextResponse.json(
        { success: false, message: 'Invalid destination phone number format. Use E.164 format (e.g., +**********)' },
        { status: 400 }
      );
    }
    
    // For demo purposes, we'll simulate a successful SMS
    // In production, this would call Twilio or another provider API
    const messageId = generateId();
    const messageSid = `SM${Date.now()}`;
    
    console.log(`Sending SMS from ${from} to ${to}: ${message}`);
    
    // In production: Call Twilio or other provider API
    // const sms = await twilioClient.messages.create({
    //   to: to,
    //   from: from,
    //   body: message,
    //   statusCallback: `${process.env.API_BASE_URL}/api/callbacks/sms-status`
    // });
    
    // In production: Save message to database
    // const messageLog = await prisma.message.create({
    //   data: {
    //     messageSid: sms.sid,
    //     from: from,
    //     to: to,
    //     body: message,
    //     direction: 'OUTBOUND'
    //   }
    // });
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'SMS sent successfully',
      messageId: messageId,
      messageSid: messageSid
    });
  } catch (error) {
    console.error('Error sending SMS:', error);
    return NextResponse.json(
      { success: false, message: `Failed to send SMS: ${error.message}` },
      { status: 500 }
    );
  }
}
