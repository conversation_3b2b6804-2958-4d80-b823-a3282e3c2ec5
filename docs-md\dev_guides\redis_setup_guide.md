# Redis Setup Guide for CallSaver Development

This guide provides instructions for setting up Redis for the CallSaver development environment. Redis is used for caching, rate limiting, session management, and task queue operations in the CallSaver application.

## Automated Setup

We provide a setup script that automates the Redis installation and configuration process. This is the recommended approach for most developers.

### Using the Setup Script

1. Navigate to the project root directory:
   ```bash
   cd callsaver.app
   ```

2. Run the Redis setup script:
   ```bash
   node back/backend/scripts/setup-redis-dev.js
   ```

3. Follow the prompts in the script:
   - If Redis is not installed, the script will provide installation instructions for your operating system.
   - If Redis is already running, you can choose to update the configuration.
   - If Redis is installed but not running, you can choose to start it.

The script will:
- Check if Redis is installed
- Create a Redis configuration file in `config/redis/redis.conf`
- Create a Redis data directory in `data/redis/`
- Update your `.env` file with Redis connection information
- Optionally start the Redis server

## Manual Setup

If you prefer to set up Redis manually, follow these steps:

### 1. Install Redis

#### Windows
1. Download Redis for Windows from [https://github.com/microsoftarchive/redis/releases](https://github.com/microsoftarchive/redis/releases)
2. Run the installer and follow the instructions
3. Add Redis to your PATH environment variable

#### macOS
1. Install Homebrew if not already installed:
   ```bash
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```
2. Install Redis:
   ```bash
   brew install redis
   ```
3. Start Redis:
   ```bash
   brew services start redis
   ```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis
```

#### Linux (CentOS/RHEL)
```bash
sudo yum install redis
sudo systemctl start redis
```

### 2. Configure Redis

Create a Redis configuration file at `config/redis/redis.conf` with the following content:

```
# Redis configuration for development environment

# Network
bind 127.0.0.1
port 6379
protected-mode yes

# General
daemonize no
pidfile /path/to/callsaver.app/data/redis/redis.pid
loglevel notice
logfile /path/to/callsaver.app/data/redis/redis.log

# Memory management
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistence
dir /path/to/callsaver.app/data/redis
appendonly yes
appendfsync everysec
```

Replace `/path/to/callsaver.app` with the actual path to your project directory.

### 3. Update Environment Variables

Add the following to your `.env` file:

```
# Redis configuration
REDIS_URL=redis://localhost:6379
REDIS_PREFIX=callsaver:dev:
```

### 4. Start Redis

Start Redis using the configuration file:

```bash
redis-server /path/to/callsaver.app/config/redis/redis.conf
```

## Verifying Redis Installation

To verify that Redis is running correctly:

1. Open a terminal and run:
   ```bash
   redis-cli ping
   ```

2. If Redis is running, you should see:
   ```
   PONG
   ```

3. You can also check the Redis connection in the application:
   ```bash
   node back/backend/test-redis-connection.js
   ```

## Redis CLI Commands

Here are some useful Redis CLI commands for development:

```bash
# Connect to Redis CLI
redis-cli

# List all keys
KEYS *

# Get value of a key
GET key_name

# Delete a key
DEL key_name

# Monitor Redis commands in real-time
MONITOR

# Get Redis server info
INFO

# Clear all keys (use with caution!)
FLUSHALL
```

## Redis in Production

For production environments, additional security and performance configurations are required:

1. Set a strong password using `requirepass` in the Redis configuration
2. Configure proper memory limits based on server capacity
3. Set up Redis persistence for data durability
4. Consider using Redis Sentinel or Redis Cluster for high availability

See the [Redis Implementation Guide](../architecture/redis_implementation_guide.mdc) for more details on production Redis configuration.

## Troubleshooting

### Redis Connection Issues

If the application cannot connect to Redis:

1. Verify Redis is running:
   ```bash
   redis-cli ping
   ```

2. Check Redis configuration in `.env` file:
   ```
   REDIS_URL=redis://localhost:6379
   ```

3. Ensure Redis is listening on the expected port:
   ```bash
   netstat -an | grep 6379
   ```

4. Check Redis logs for errors:
   ```bash
   cat data/redis/redis.log
   ```

### Redis Performance Issues

If Redis is running slowly:

1. Check memory usage:
   ```bash
   redis-cli info memory
   ```

2. Monitor Redis commands to identify slow operations:
   ```bash
   redis-cli monitor
   ```

3. Use the Redis SLOWLOG to identify slow commands:
   ```bash
   redis-cli slowlog get 10
   ```

## Additional Resources

- [Redis Documentation](https://redis.io/documentation)
- [Redis Best Practices](https://redis.io/topics/optimization)
- [Redis Security](https://redis.io/topics/security)
