'use client';

import { useState, useRef } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { RadioGroup } from '@headlessui/react';
import { CheckCircleIcon, PlayIcon, PauseIcon } from '@heroicons/react/24/solid';
import LoadingSpinner from '../shared/LoadingSpinner';

interface VoiceModel {
  id: string;
  name: string;
  gender: 'male' | 'female';
  description: string;
  previewUrl: string;
  isDefault: boolean;
}

export default function VoiceModelSelector() {
  const [selectedModelId, setSelectedModelId] = useState<string | null>(null);
  const [playingId, setPlayingId] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const queryClient = useQueryClient();

  // Fetch voice models
  const {
    data: voiceModels,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['ai', 'voice-models'],
    queryFn: async () => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));

        // Return mock data
        const models: VoiceModel[] = [
          {
            id: 'alloy',
            name: 'Alloy',
            gender: 'female',
            description: 'Versatile and balanced, suitable for general content.',
            previewUrl: 'https://cdn.openai.com/API/docs/audio/alloy.wav',
            isDefault: true,
          },
          {
            id: 'echo',
            name: 'Echo',
            gender: 'male',
            description: 'Deep and resonant, good for narration.',
            previewUrl: 'https://cdn.openai.com/API/docs/audio/echo.wav',
            isDefault: false,
          },
          {
            id: 'fable',
            name: 'Fable',
            gender: 'female',
            description: 'Warm and soft, ideal for storytelling.',
            previewUrl: 'https://cdn.openai.com/API/docs/audio/fable.wav',
            isDefault: false,
          },
          {
            id: 'onyx',
            name: 'Onyx',
            gender: 'male',
            description: 'Clear and authoritative, good for presentations.',
            previewUrl: 'https://cdn.openai.com/API/docs/audio/onyx.wav',
            isDefault: false,
          },
          {
            id: 'nova',
            name: 'Nova',
            gender: 'female',
            description: 'Energetic and professional, suitable for customer service.',
            previewUrl: 'https://cdn.openai.com/API/docs/audio/nova.wav',
            isDefault: false,
          },
          {
            id: 'shimmer',
            name: 'Shimmer',
            gender: 'female',
            description: 'Bright and friendly, good for educational content.',
            previewUrl: 'https://cdn.openai.com/API/docs/audio/shimmer.wav',
            isDefault: false,
          },
        ];

        // Set the selected model to the default one
        const defaultModel = models.find(model => model.isDefault);
        if (defaultModel) {
          setSelectedModelId(defaultModel.id);
        }

        return models;
      }

      // In production, fetch from API
      const { data } = await axios.get<VoiceModel[]>('/api/ai/voice-models');

      // Set the selected model to the default one
      const defaultModel = data.find(model => model.isDefault);
      if (defaultModel) {
        setSelectedModelId(defaultModel.id);
      }

      return data;
    },
    staleTime: 60 * 60 * 1000, // 1 hour
  });

  // Update default voice model mutation
  const updateDefaultModelMutation = useMutation({
    mutationFn: async (modelId: string) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));

        // Return mock success
        return { success: true };
      }

      // In production, call API
      const { data } = await axios.post('/api/ai/voice-models/default', { modelId });
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['ai', 'voice-models'] });

      // Show success toast
      console.log('Default voice model updated successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to update default voice model');
    },
  });

  // Handle model selection
  const handleModelSelect = (modelId: string) => {
    setSelectedModelId(modelId);
    updateDefaultModelMutation.mutate(modelId);
  };

  // Play audio preview
  const playAudio = (modelId: string, url: string) => {
    // Stop current audio if playing
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }

    // Create new audio element
    const audio = new Audio(url);
    audioRef.current = audio;

    // Play audio
    audio.play();
    setPlayingId(modelId);

    // Handle audio ended
    audio.onended = () => {
      setPlayingId(null);
    };
  };

  // Stop audio preview
  const stopAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setPlayingId(null);
    }
  };

  // If loading, show loading spinner
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" color="blue" />
      </div>
    );
  }

  // If error, show error message
  if (error || !voiceModels) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-md">
        <h3 className="text-sm font-medium text-red-800 dark:text-red-300">
          Failed to load voice models
        </h3>
        <p className="mt-2 text-sm text-red-700 dark:text-red-200">
          We couldn't load the available voice models. Please try again later.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Voice Models
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Select a voice model for your AI assistant to use when speaking to customers.
        </p>
      </div>

      <RadioGroup value={selectedModelId} onChange={handleModelSelect}>
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {voiceModels.map((model) => (
            <RadioGroup.Option
              key={model.id}
              value={model.id}
              className={({ checked }) =>
                `${
                  checked
                    ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                    : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                }
                relative rounded-lg border p-4 shadow-sm cursor-pointer focus:outline-none`
              }
            >
              {({ checked }) => (
                <>
                  <div className="flex justify-between">
                    <div className="flex items-center">
                      <div className="text-sm">
                        <RadioGroup.Label
                          as="p"
                          className={`font-medium ${
                            checked
                              ? 'text-blue-900 dark:text-blue-300'
                              : 'text-gray-900 dark:text-white'
                          }`}
                        >
                          {model.name}
                        </RadioGroup.Label>
                        <RadioGroup.Description
                          as="span"
                          className={`inline ${
                            checked
                              ? 'text-blue-700 dark:text-blue-400'
                              : 'text-gray-500 dark:text-gray-400'
                          }`}
                        >
                          <span className="block text-xs">
                            {model.gender === 'male' ? 'Male' : 'Female'}
                          </span>
                        </RadioGroup.Description>
                      </div>
                    </div>
                    {checked && (
                      <div className="shrink-0 text-blue-600 dark:text-blue-400">
                        <CheckCircleIcon className="h-6 w-6" />
                      </div>
                    )}
                  </div>
                  <RadioGroup.Description
                    as="div"
                    className={`mt-2 text-sm ${
                      checked
                        ? 'text-blue-700 dark:text-blue-400'
                        : 'text-gray-500 dark:text-gray-400'
                    }`}
                  >
                    <p>{model.description}</p>
                    <div className="mt-3">
                      <button
                        type="button"
                        onClick={(e) => {
                          e.preventDefault();
                          playingId === model.id
                            ? stopAudio()
                            : playAudio(model.id, model.previewUrl);
                        }}
                        className={`inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-full ${
                          playingId === model.id
                            ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300'
                            : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                        }`}
                      >
                        {playingId === model.id ? (
                          <>
                            <PauseIcon className="h-3 w-3 mr-1" />
                            Stop Preview
                          </>
                        ) : (
                          <>
                            <PlayIcon className="h-3 w-3 mr-1" />
                            Play Preview
                          </>
                        )}
                      </button>
                    </div>
                  </RadioGroup.Description>
                </>
              )}
            </RadioGroup.Option>
          ))}
        </div>
      </RadioGroup>

      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-md p-4 text-sm">
        <h3 className="font-medium text-blue-800 dark:text-blue-300 mb-1">
          About Voice Models
        </h3>
        <p className="text-blue-700 dark:text-blue-200">
          Voice models determine how your AI assistant sounds when speaking to customers. Choose a voice that best represents your brand and will resonate with your audience. You can preview each voice before making your selection.
        </p>
      </div>
    </div>
  );
}
