import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../lib/apiClient';
import toast from 'react-hot-toast';

// Types for integrations
export interface Integration {
  id: string;
  name: string;
  provider: string;
  description: string;
  icon?: React.ReactNode; // Icon is added on the frontend
  status: 'connected' | 'disconnected' | 'pending' | 'error';
  category: 'calendar' | 'communication' | 'crm' | 'automation' | 'other';
  lastSynced?: string;
  accountInfo?: {
    name?: string;
    email?: string;
    plan?: string;
  };
  authType: 'oauth' | 'apikey' | 'webhook';
  authUrl?: string;
  webhookUrl?: string;
}

export interface IntegrationSettings {
  [key: string]: any;
}

export interface IntegrationStatus {
  status: 'connected' | 'disconnected' | 'pending' | 'error';
  details?: string;
}

export interface IntegrationConnectParams {
  integrationId: string;
  apiKey?: string;
  webhookConfig?: any;
  settings?: IntegrationSettings;
}

/**
 * Hook for managing external integrations
 */
export function useIntegrations() {
  const queryClient = useQueryClient();
  
  // Fetch all integrations
  const integrationsQuery = useQuery({
    queryKey: ['integrations'],
    queryFn: () => api.get<Integration[]>('/integrations'),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // Fetch a specific integration
  const getIntegration = (integrationId: string) => {
    return useQuery({
      queryKey: ['integrations', integrationId],
      queryFn: () => api.get<Integration>(`/integrations/${integrationId}`),
      staleTime: 5 * 60 * 1000, // 5 minutes
      enabled: !!integrationId,
    });
  };
  
  // Fetch integration status
  const getIntegrationStatus = (integrationId: string) => {
    return useQuery({
      queryKey: ['integrations', integrationId, 'status'],
      queryFn: () => api.get<IntegrationStatus>(`/integrations/${integrationId}/status`),
      staleTime: 60 * 1000, // 1 minute - more frequent updates for status
      enabled: !!integrationId,
      // Poll for status updates if pending
      refetchInterval: (data) => {
        return data?.status === 'pending' ? 5000 : false;
      },
    });
  };
  
  // Fetch integration settings
  const getIntegrationSettings = (integrationId: string) => {
    return useQuery({
      queryKey: ['integrations', integrationId, 'settings'],
      queryFn: () => api.get<IntegrationSettings>(`/integrations/${integrationId}/settings`),
      staleTime: 5 * 60 * 1000, // 5 minutes
      enabled: !!integrationId,
    });
  };
  
  // Connect integration (for non-OAuth or to finalize OAuth)
  const connectIntegrationMutation = useMutation({
    mutationFn: (params: IntegrationConnectParams) => {
      return api.post<{ success: boolean }>(`/integrations/${params.integrationId}/connect`, {
        apiKey: params.apiKey,
        webhookConfig: params.webhookConfig,
        settings: params.settings,
      });
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['integrations'] });
      queryClient.invalidateQueries({ queryKey: ['integrations', variables.integrationId] });
      
      // Show success toast
      toast.success('Integration connected successfully');
    },
    onError: (error: any) => {
      // Show error toast
      toast.error(error.message || 'Failed to connect integration');
    },
  });
  
  // Update integration settings
  const updateSettingsMutation = useMutation({
    mutationFn: ({ integrationId, settings }: { integrationId: string; settings: IntegrationSettings }) => {
      return api.put<{ success: boolean }>(`/integrations/${integrationId}/settings`, settings);
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['integrations', variables.integrationId, 'settings'] });
      
      // Show success toast
      toast.success('Integration settings updated');
    },
    onError: (error: any) => {
      // Show error toast
      toast.error(error.message || 'Failed to update integration settings');
    },
  });
  
  // Disconnect integration
  const disconnectIntegrationMutation = useMutation({
    mutationFn: (integrationId: string) => {
      return api.delete<{ success: boolean }>(`/integrations/${integrationId}`);
    },
    onSuccess: (_, integrationId) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['integrations'] });
      queryClient.invalidateQueries({ queryKey: ['integrations', integrationId] });
      
      // Show success toast
      toast.success('Integration disconnected');
    },
    onError: (error: any) => {
      // Show error toast
      toast.error(error.message || 'Failed to disconnect integration');
    },
  });
  
  // Trigger manual sync for an integration
  const syncIntegrationMutation = useMutation({
    mutationFn: (integrationId: string) => {
      return api.post<{ success: boolean }>(`/integrations/${integrationId}/sync`);
    },
    onSuccess: (_, integrationId) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['integrations', integrationId] });
      
      // Show success toast
      toast.success('Sync initiated');
    },
    onError: (error: any) => {
      // Show error toast
      toast.error(error.message || 'Failed to sync integration');
    },
  });
  
  return {
    // Queries
    integrationsQuery,
    getIntegration,
    getIntegrationStatus,
    getIntegrationSettings,
    
    // Mutations
    connectIntegration: connectIntegrationMutation.mutate,
    updateSettings: updateSettingsMutation.mutate,
    disconnectIntegration: disconnectIntegrationMutation.mutate,
    syncIntegration: syncIntegrationMutation.mutate,
    
    // Loading states
    isConnecting: connectIntegrationMutation.isPending,
    isUpdatingSettings: updateSettingsMutation.isPending,
    isDisconnecting: disconnectIntegrationMutation.isPending,
    isSyncing: syncIntegrationMutation.isPending,
  };
}
