'use client';

import { useState } from 'react';
import { IntegrationSettings } from '../../../hooks/useIntegrations';

interface SlackSettingsProps {
  settings: IntegrationSettings;
  onChange: (settings: IntegrationSettings) => void;
}

export default function SlackSettings({
  settings,
  onChange,
}: SlackSettingsProps) {
  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    onChange({
      ...settings,
      [name]: checked,
    });
  };

  // Handle select change
  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    onChange({
      ...settings,
      [name]: value,
    });
  };

  // Handle text input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    onChange({
      ...settings,
      [name]: value,
    });
  };

  return (
    <div className="space-y-6">
      {/* Default Channel */}
      <div>
        <label
          htmlFor="defaultChannel"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Default Channel
        </label>
        <input
          type="text"
          name="defaultChannel"
          id="defaultChannel"
          className="mt-1 block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
          placeholder="#general"
          value={settings.defaultChannel || ''}
          onChange={handleInputChange}
        />
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Channel where notifications will be sent by default.
        </p>
      </div>

      {/* Notification Settings */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Notification Settings
        </h4>

        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="notifyOnMissedCalls"
              name="notifyOnMissedCalls"
              type="checkbox"
              className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
              checked={settings.notifyOnMissedCalls !== false} // Default to true
              onChange={handleCheckboxChange}
            />
          </div>
          <div className="ml-3 text-sm">
            <label
              htmlFor="notifyOnMissedCalls"
              className="font-medium text-gray-700 dark:text-gray-300"
            >
              Missed Calls
            </label>
            <p className="text-gray-500 dark:text-gray-400">
              Send notification when a call is missed.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="notifyOnVoicemails"
              name="notifyOnVoicemails"
              type="checkbox"
              className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
              checked={settings.notifyOnVoicemails !== false} // Default to true
              onChange={handleCheckboxChange}
            />
          </div>
          <div className="ml-3 text-sm">
            <label
              htmlFor="notifyOnVoicemails"
              className="font-medium text-gray-700 dark:text-gray-300"
            >
              Voicemails
            </label>
            <p className="text-gray-500 dark:text-gray-400">
              Send notification when a voicemail is received.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="notifyOnSms"
              name="notifyOnSms"
              type="checkbox"
              className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
              checked={settings.notifyOnSms !== false} // Default to true
              onChange={handleCheckboxChange}
            />
          </div>
          <div className="ml-3 text-sm">
            <label
              htmlFor="notifyOnSms"
              className="font-medium text-gray-700 dark:text-gray-300"
            >
              SMS Messages
            </label>
            <p className="text-gray-500 dark:text-gray-400">
              Send notification when an SMS is received.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="notifyOnAppointments"
              name="notifyOnAppointments"
              type="checkbox"
              className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
              checked={settings.notifyOnAppointments || false}
              onChange={handleCheckboxChange}
            />
          </div>
          <div className="ml-3 text-sm">
            <label
              htmlFor="notifyOnAppointments"
              className="font-medium text-gray-700 dark:text-gray-300"
            >
              Appointments
            </label>
            <p className="text-gray-500 dark:text-gray-400">
              Send notification when an appointment is scheduled or changed.
            </p>
          </div>
        </div>
      </div>

      {/* Message Format */}
      <div>
        <label
          htmlFor="messageFormat"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Message Format
        </label>
        <select
          id="messageFormat"
          name="messageFormat"
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          value={settings.messageFormat || 'detailed'}
          onChange={handleSelectChange}
        >
          <option value="detailed">Detailed (with all information)</option>
          <option value="compact">Compact (summary only)</option>
          <option value="minimal">Minimal (alert only)</option>
        </select>
      </div>
    </div>
  );
}
