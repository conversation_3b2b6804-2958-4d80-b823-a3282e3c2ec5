'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import { useUploadAvatar } from '../../../hooks/useSettings';

interface AvatarUploaderProps {
  currentAvatarUrl?: string;
}

export default function AvatarUploader({ currentAvatarUrl }: AvatarUploaderProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Upload avatar mutation
  const uploadAvatar = useUploadAvatar();
  
  // Default avatar if none provided
  const avatarUrl = currentAvatarUrl || '/images/default-avatar.png';
  
  // Handle file selection
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Check file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }
    
    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB');
      return;
    }
    
    // Create preview URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
    
    // Upload the file
    uploadAvatar.mutate(file);
  };
  
  // Trigger file input click
  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };
  
  return (
    <div className="flex flex-col sm:flex-row items-center gap-6">
      <div className="relative">
        <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-gray-200 dark:border-gray-700">
          <Image 
            src={previewUrl || avatarUrl} 
            alt="Profile" 
            width={96} 
            height={96} 
            className="object-cover w-full h-full"
            onError={() => setPreviewUrl(null)} // Reset to default on error
          />
          
          {uploadAvatar.isPending && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <div className="animate-spin w-8 h-8 border-4 border-white border-t-transparent rounded-full"></div>
            </div>
          )}
        </div>
        
        <button 
          type="button"
          onClick={handleUploadClick}
          disabled={uploadAvatar.isPending}
          className="absolute bottom-0 right-0 bg-indigo-600 hover:bg-indigo-700 text-white p-1.5 rounded-full shadow-lg transition-colors disabled:bg-gray-400"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
          </svg>
        </button>
      </div>
      
      <div>
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">Profile Picture</h4>
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
          Upload a profile picture. JPG, PNG or GIF. Max 5MB.
        </p>
        <button
          type="button"
          onClick={handleUploadClick}
          disabled={uploadAvatar.isPending}
          className="px-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          {uploadAvatar.isPending ? 'Uploading...' : 'Change Picture'}
        </button>
      </div>
      
      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileSelect}
        accept="image/*"
        className="hidden"
      />
      
      {uploadAvatar.isError && (
        <div className="mt-2 text-sm text-red-600 dark:text-red-400">
          {uploadAvatar.error instanceof Error 
            ? uploadAvatar.error.message 
            : 'Failed to upload image. Please try again.'}
        </div>
      )}
    </div>
  );
}
