/**
 * Package Selector Component
 * 
 * This component displays available eSIM data packages and allows
 * users to select and purchase them.
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../../hooks/useToast';
import { Card, Button, Spinner, Alert, Modal, RadioGroup, RadioOption } from '../../components/ui';
import { formatBytes, formatCurrency } from '../../utils/formatting';

export function PackageSelector({ profile, onPurchase, onClose, isNewProfile = false }) {
  const [packages, setPackages] = useState([]);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [region, setRegion] = useState('global');
  const [filters, setFilters] = useState({
    minData: '',
    maxPrice: '',
    provider: ''
  });
  
  const toast = useToast();
  const { apiClient } = useAuth();
  
  // Fetch available packages on component mount
  useEffect(() => {
    fetchPackages();
  }, [region]);
  
  // Fetch available packages
  const fetchPackages = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Build query params
      const params = new URLSearchParams();
      params.append('region', region);
      
      if (filters.minData && !isNaN(filters.minData)) {
        params.append('minData', filters.minData);
      }
      
      if (filters.maxPrice && !isNaN(filters.maxPrice)) {
        params.append('maxPrice', filters.maxPrice);
      }
      
      if (filters.provider) {
        params.append('provider', filters.provider);
      }
      
      // If this is for an existing profile, use its provider
      if (profile && !isNewProfile && profile.provider) {
        params.set('provider', profile.provider);
      }
      
      const response = await apiClient.get(`/api/esim/packages?${params.toString()}`);
      
      if (response.data.success) {
        setPackages(response.data.data);
        
        // Select first package by default if none selected
        if (!selectedPackage && response.data.data.length > 0) {
          setSelectedPackage(response.data.data[0].id);
        }
      } else {
        setError(response.data.message || 'Failed to load available packages');
      }
    } catch (err) {
      setError(err.message || 'An error occurred while loading packages');
      toast.error('Failed to load available packages');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle filter change
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  // Apply filters
  const applyFilters = () => {
    fetchPackages();
  };
  
  // Handle purchase
  const handlePurchase = () => {
    if (!selectedPackage) {
      toast.error('Please select a package');
      return;
    }
    
    onPurchase(selectedPackage);
  };
  
  return (
    <Modal 
      title={isNewProfile ? "Create eSIM Profile" : "Purchase Data Package"} 
      onClose={onClose}
      size="lg"
    >
      <div className="p-4">
        {error && (
          <Alert type="error" className="mb-4">
            {error}
          </Alert>
        )}
        
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-3">
            {isNewProfile ? "Select a data package for your new eSIM" : "Select a data package"}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label htmlFor="region" className="block text-sm font-medium text-gray-700 mb-1">
                Region
              </label>
              <select
                id="region"
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={region}
                onChange={(e) => setRegion(e.target.value)}
              >
                <option value="global">Global</option>
                <option value="europe">Europe</option>
                <option value="asia">Asia</option>
                <option value="americas">Americas</option>
                <option value="africa">Africa</option>
                <option value="oceania">Oceania</option>
              </select>
            </div>
            
            <div className="flex items-end gap-2">
              <div className="flex-1">
                <label htmlFor="provider" className="block text-sm font-medium text-gray-700 mb-1">
                  Provider
                </label>
                <select
                  id="provider"
                  name="provider"
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                  value={filters.provider}
                  onChange={handleFilterChange}
                  disabled={!isNewProfile && profile}
                >
                  <option value="">Any Provider</option>
                  <option value="airalo">Airalo</option>
                  <option value="truphone">Truphone</option>
                  <option value="gigsky">GigSky</option>
                </select>
              </div>
              
              <Button size="sm" onClick={applyFilters}>
                Filter
              </Button>
            </div>
          </div>
        </div>
        
        {loading ? (
          <div className="text-center p-8">
            <Spinner size="lg" />
            <p className="mt-4 text-gray-600">Loading available packages...</p>
          </div>
        ) : packages.length === 0 ? (
          <div className="text-center p-8 bg-gray-50 rounded-lg">
            <p className="text-gray-600 mb-4">No packages available for the selected criteria.</p>
            <Button variant="outline" onClick={() => {
              setRegion('global');
              setFilters({
                minData: '',
                maxPrice: '',
                provider: ''
              });
            }}>
              Reset Filters
            </Button>
          </div>
        ) : (
          <div>
            <RadioGroup 
              name="package"
              value={selectedPackage}
              onChange={setSelectedPackage}
              className="space-y-3"
            >
              {packages.map(pkg => (
                <RadioOption key={pkg.id} value={pkg.id}>
                  <Card className={`p-4 ${selectedPackage === pkg.id ? 'ring-2 ring-blue-500' : ''}`}>
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="text-lg font-medium">{pkg.name}</h4>
                        <p className="text-sm text-gray-600">{pkg.description}</p>
                      </div>
                      <span className="text-lg font-bold">
                        {formatCurrency(pkg.price, pkg.currency)}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-2 text-sm mt-2">
                      <span className="text-gray-500">Data:</span>
                      <span>{pkg.dataAmount === -1 
                        ? 'Unlimited' 
                        : formatBytes(pkg.dataAmount * 1024 * 1024)}
                      </span>
                      
                      <span className="text-gray-500">Validity:</span>
                      <span>{pkg.validity} days</span>
                      
                      <span className="text-gray-500">Coverage:</span>
                      <span>{pkg.regions.join(', ')}</span>
                    </div>
                  </Card>
                </RadioOption>
              ))}
            </RadioGroup>
            
            <div className="mt-6 pt-4 border-t flex justify-between">
              <Button variant="outline" onClick={onClose}>Cancel</Button>
              <Button 
                onClick={handlePurchase} 
                disabled={!selectedPackage}
              >
                {isNewProfile 
                  ? "Create Profile" 
                  : "Purchase Package"
                }
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}
