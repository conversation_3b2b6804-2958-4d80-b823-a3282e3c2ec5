'use client';

import React, { useEffect } from 'react';
import { initPerformanceMonitoring } from '../../utils/performance/performanceMonitor';

/**
 * PerformanceOptimizer Component
 * 
 * This component implements various performance optimizations for the application:
 * 1. Prefetches critical resources
 * 2. Sets up lazy loading for non-critical elements
 * 3. Optimizes performance when page visibility changes
 * 4. Implements resource hints (preconnect, dns-prefetch)
 * 5. Initializes performance monitoring
 */
const PerformanceOptimizer = () => {
  // Initialize performance monitoring and optimizations
  useEffect(() => {
    // Define functions inside useEffect to avoid dependency issues
    /**
     * Initialize performance monitoring
     */
    const setupPerformanceMonitoring = () => {
      initPerformanceMonitoring();
    };

    /**
     * Prefetch critical resources for common user flows
     */
    const prefetchCriticalResources = () => {
      // Only run on production to avoid unnecessary requests during development
      if (process.env.NODE_ENV !== 'production') return;

      // Helper function to prefetch a URL
      const prefetch = (url) => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = url;
        document.head.appendChild(link);
      };

      // Prefetch critical paths that users are likely to navigate to
      setTimeout(() => {
        prefetch('/dashboard');
        prefetch('/crm');
        // Add more critical paths as needed
      }, 3000); // Delay prefetching to prioritize initial page load
    };

    /**
     * Set up lazy loading for non-critical elements
     */
    const setupLazyLoading = () => {
      if (typeof window === 'undefined' || !window.IntersectionObserver) return;

      // Create an observer for lazy-loaded elements
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (!entry.isIntersecting) return;
          
          const target = entry.target;
          
          // Handle different types of elements
          if (target.dataset.lazySrc) {
            // For images
            target.src = target.dataset.lazySrc;
            target.removeAttribute('data-lazy-src');
          } else if (target.dataset.lazyBg) {
            // For background images
            target.style.backgroundImage = `url(${target.dataset.lazyBg})`;
            target.removeAttribute('data-lazy-bg');
          } else if (target.dataset.lazyComponent) {
            // Load components dynamically - the component should handle its own loading
            target.dataset.lazyLoaded = 'true';
            target.removeAttribute('data-lazy-component');
          }
          
          // Once loaded, no need to observe anymore
          observer.unobserve(target);
        });
      }, {
        rootMargin: '200px', // Start loading when element is 200px from viewport
        threshold: 0.01 // Trigger when at least 1% of the element is visible
      });
      
      // Observe all elements with lazy loading attributes
      document.querySelectorAll('[data-lazy-src], [data-lazy-bg], [data-lazy-component]').forEach(el => {
        observer.observe(el);
      });

      // Clean up observer on unmount
      return () => {
        observer.disconnect();
      };
    };

    /**
     * Optimize performance when page visibility changes
     */
    const setupVisibilityHandling = () => {
      if (typeof document === 'undefined') return;
      
      // Handler function for visibility changes
      const handleVisibilityChange = () => {
        if (document.hidden) {
          // Page is not visible
          document.body.classList.add('performance-paused');
          // Signal to other components that page is hidden
          window.dispatchEvent(new CustomEvent('callsaver:visibility-hidden'));
        } else {
          // Page is visible again
          document.body.classList.remove('performance-paused');
          // Signal to other components that page is visible
          window.dispatchEvent(new CustomEvent('callsaver:visibility-visible'));
        }
      };

      // Add event listener
      document.addEventListener('visibilitychange', handleVisibilityChange);

      // Clean up event listener on unmount
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      };
    };

    /**
     * Set up resource hints for external domains
     */
    const setupResourceHints = () => {
      if (typeof document === 'undefined') return;

      // Helper function to create resource hints
      const createResourceHint = (rel, href) => {
        const link = document.createElement('link');
        link.rel = rel;
        link.href = href;
        document.head.appendChild(link);
      };

      // DNS prefetch for external domains
      createResourceHint('dns-prefetch', 'https://fonts.googleapis.com');
      createResourceHint('dns-prefetch', 'https://fonts.gstatic.com');
      createResourceHint('dns-prefetch', 'https://cdn.jsdelivr.net');

      // Preconnect to critical domains
      createResourceHint('preconnect', 'https://fonts.googleapis.com');
      createResourceHint('preconnect', 'https://fonts.gstatic.com');
      createResourceHint('preconnect', 'https://cdn.jsdelivr.net');

      // Preload critical fonts
      createResourceHint('preload', 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
    };

    /**
     * Set up image optimization
     */
    const setupImageOptimization = () => {
      if (typeof window === 'undefined') return;

      // Add loading="lazy" to all images that don't have it
      document.querySelectorAll('img:not([loading])').forEach(img => {
        img.loading = 'lazy';
      });

      // Add decoding="async" to all images that don't have it
      document.querySelectorAll('img:not([decoding])').forEach(img => {
        img.decoding = 'async';
      });
    };

    /**
     * Set up font optimization
     */
    const setupFontOptimization = () => {
      if (typeof document === 'undefined') return;

      // Add font-display: swap to all font faces
      const style = document.createElement('style');
      style.textContent = `
        @font-face {
          font-display: swap !important;
        }
      `;
      document.head.appendChild(style);
    };

    // Execute the optimization functions
    setupPerformanceMonitoring();
    prefetchCriticalResources();
    const lazyLoadingCleanup = setupLazyLoading();
    const visibilityHandlingCleanup = setupVisibilityHandling();
    setupResourceHints();
    setupImageOptimization();
    setupFontOptimization();
    
    // Return a cleanup function that calls all the individual cleanup functions
    return () => {
      if (lazyLoadingCleanup) lazyLoadingCleanup();
      if (visibilityHandlingCleanup) visibilityHandlingCleanup();
    };
  }, []);

  // This component doesn't render anything visible
  return null;
};

export default PerformanceOptimizer;
