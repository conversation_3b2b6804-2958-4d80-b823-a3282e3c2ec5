'use client';

import Link from 'next/link';
import { useEffect } from 'react';

import { useAuthStore } from '../../stores/authStore';
import { isAdmin } from '../utils/roleUtils';

export default function AdminTest() {
  const user = useAuthStore(state => state.user);
  const userIsAdmin = isAdmin(user);

  useEffect(() => {
    console.log('Admin test page loaded');
    console.log('User:', user);
    console.log('Is admin:', userIsAdmin);
  }, [user, userIsAdmin]);

  if (!user) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold mb-4">Loading user data...</h1>
      </div>
    );
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Admin Test Page</h1>

      <div className="bg-gray-800 p-4 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-2 text-white">User Information</h2>
        <pre className="bg-gray-900 p-4 rounded text-green-400 overflow-auto">
          {JSON.stringify(user, null, 2)}
        </pre>
      </div>

      <div className="bg-gray-800 p-4 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-2 text-white">Admin Status</h2>
        <p className="text-lg">
          Is Admin: <span className={userIsAdmin ? "text-green-500 font-bold" : "text-red-500 font-bold"}>
            {userIsAdmin ? "YES" : "NO"}
          </span>
        </p>
      </div>

      <div className="flex space-x-4">
        <Link href="/dashboard" className="px-4 py-2 bg-blue-600 text-white rounded-lg">
          Back to Dashboard
        </Link>
        <Link href="/dashboard/admin" className="px-4 py-2 bg-purple-600 text-white rounded-lg">
          Go to Admin Dashboard
        </Link>
      </div>
    </div>
  );
}
