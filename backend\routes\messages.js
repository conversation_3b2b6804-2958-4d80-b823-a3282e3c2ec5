const express = require('express');
const router = express.Router();
const messageController = require('../controllers/messageController');

// Define routes for messages
router.get('/', messageController.getMessages);
router.post('/', messageController.createMessage);
router.get('/:id', messageController.getMessageById);
router.put('/:id', messageController.updateMessage);
router.delete('/:id', messageController.deleteMessage);

module.exports = router;