import { usePermissions } from './usePermissions';

/**
 * Interface for feature flag configuration
 */
export interface FeatureFlagConfig {
  // The name of the feature flag
  name: string;
  
  // The permission required to access this feature
  permission?: string;
  
  // Array of permissions, any of which will grant access
  anyPermission?: string[];
  
  // Array of permissions, all of which are required for access
  allPermissions?: string[];
  
  // Resource-based permission check
  resource?: string;
  action?: string;
  scope?: string;
  
  // Default state if no permission is specified
  defaultEnabled?: boolean;
  
  // Description of the feature (for debugging)
  description?: string;
}

/**
 * Hook for checking if a feature flag is enabled based on user permissions
 * 
 * @param config - Feature flag configuration
 * @returns Whether the feature is enabled for the current user
 */
export function useFeatureFlag(config: FeatureFlagConfig): boolean {
  const { 
    hasPermission, 
    hasAnyPermission, 
    hasAllPermissions,
    canAccess,
    isLoading
  } = usePermissions();
  
  // If permissions are still loading, return the default state
  if (isLoading) {
    return config.defaultEnabled ?? false;
  }
  
  // Check if the user has the required permissions
  if (config.permission) {
    return hasPermission(config.permission);
  } 
  
  if (config.anyPermission && config.anyPermission.length > 0) {
    return hasAnyPermission(config.anyPermission);
  } 
  
  if (config.allPermissions && config.allPermissions.length > 0) {
    return hasAllPermissions(config.allPermissions);
  } 
  
  if (config.resource) {
    return canAccess(
      config.resource, 
      config.action || 'read', 
      config.scope || 'any'
    );
  }
  
  // If no permissions are specified, return the default state
  return config.defaultEnabled ?? true;
}

/**
 * Convenience hook for checking if a feature is enabled by name
 * 
 * @param name - Feature name
 * @param permission - Permission required to access this feature
 * @returns Whether the feature is enabled for the current user
 */
export function useIsFeatureEnabled(
  name: string, 
  permission: string
): boolean {
  return useFeatureFlag({ name, permission });
}

/**
 * Convenience hook for checking if a feature is enabled by resource
 * 
 * @param name - Feature name
 * @param resource - Resource to check
 * @param action - Action to check (default: 'use')
 * @param scope - Scope to check (default: 'any')
 * @returns Whether the feature is enabled for the current user
 */
export function useIsResourceFeatureEnabled(
  name: string,
  resource: string,
  action: string = 'use',
  scope: string = 'any'
): boolean {
  return useFeatureFlag({ 
    name, 
    resource, 
    action, 
    scope 
  });
}
