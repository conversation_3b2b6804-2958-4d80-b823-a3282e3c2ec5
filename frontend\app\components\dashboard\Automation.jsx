"use client";

import { useState } from 'react';
import PhoneSelector from '../automation/PhoneSelector';
import AIChatAssistant from '../automation/AIChatAssistant';
import ActivityHistory from '../automation/ActivityHistory';
import ChatIntegrationTest from '../automation/ChatIntegrationTest';

export default function Automation() {
  const [selectedPhoneNumber, setSelectedPhoneNumber] = useState(null);
  const [activeTab, setActiveTab] = useState('train');
  
  const handlePhoneSelected = (phoneNumber) => {
    setSelectedPhoneNumber(phoneNumber);
  };
  
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-2">Automation</h1>
      <p className="text-gray-400 mb-6">
        Train your AI assistant and connect external tools for automated tasks.
      </p>
      
      {/* Tabs */}
      <div className="flex border-b border-gray-700 mb-6 overflow-x-auto">
        <button 
          onClick={() => setActiveTab('train')}
          className={`py-2 px-4 font-medium transition-colors whitespace-nowrap ${
            activeTab === 'train' 
              ? 'border-b-2 border-blue-500 text-blue-400' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          Train Your AI
        </button>
        <button 
          onClick={() => setActiveTab('test')}
          className={`py-2 px-4 font-medium transition-colors whitespace-nowrap ${
            activeTab === 'test' 
              ? 'border-b-2 border-blue-500 text-blue-400' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          Test AI
        </button>
        <button 
          onClick={() => setActiveTab('tools')}
          className={`py-2 px-4 font-medium transition-colors whitespace-nowrap ${
            activeTab === 'tools' 
              ? 'border-b-2 border-blue-500 text-blue-400' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          Connect Tools
        </button>
        <button 
          onClick={() => setActiveTab('rules')}
          className={`py-2 px-4 font-medium transition-colors whitespace-nowrap ${
            activeTab === 'rules' 
              ? 'border-b-2 border-blue-500 text-blue-400' 
              : 'text-gray-400 hover:text-white'
          }`}
        >
          Automation Rules
        </button>
      </div>
      
      {activeTab === 'train' && (
        <>
          {/* Section 1: Phone Selector */}
          <div className="mb-6">
            <PhoneSelector onPhoneSelected={handlePhoneSelected} />
          </div>
          
          {/* Section 2: AI Chat Assistant */}
          <div className="mb-6">
            <AIChatAssistant phoneNumber={selectedPhoneNumber} />
          </div>
          
          {/* Section 3: Activity History */}
          <div className="mb-6">
            <ActivityHistory phoneNumber={selectedPhoneNumber} />
          </div>
        </>
      )}
      
      {activeTab === 'test' && (
        <div className="mb-6">
          <div className="mb-4">
            <h2 className="text-xl font-semibold mb-2">OpenAI Integration Test</h2>
            <p className="text-gray-400">
              Test your OpenAI API connection and verify that GPT-4-turbo is working correctly.
            </p>
          </div>
          <ChatIntegrationTest />
        </div>
      )}
      
      {activeTab === 'tools' && (
        <div className="p-8 text-center text-gray-400 border border-dashed border-gray-700 rounded-lg">
          <h3 className="text-xl mb-2">Connect External Tools</h3>
          <p>This feature is coming soon. You'll be able to connect various APIs and services.</p>
        </div>
      )}
      
      {activeTab === 'rules' && (
        <div className="p-8 text-center text-gray-400 border border-dashed border-gray-700 rounded-lg">
          <h3 className="text-xl mb-2">Automation Rules</h3>
          <p>Create advanced automation rules to handle calls and messages. Coming soon!</p>
        </div>
      )}
    </div>
  );
}
