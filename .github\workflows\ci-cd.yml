name: CallSaver.app CI/CD

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test-backend:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: testdb
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
      
      redis:
        image: redis:alpine
        ports:
          - 6379:6379
        options: --health-cmd "redis-cli ping" --health-interval 10s --health-timeout 5s --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'back/backend/package-lock.json'
      
      - name: Install dependencies
        run: npm ci
        working-directory: ./back/backend
      
      - name: Run Prisma migrations
        run: npx prisma migrate dev --name init
        working-directory: ./back/backend
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/testdb
          DIRECT_URL: postgresql://postgres:postgres@localhost:5432/testdb
      
      - name: Run linting
        run: npm run lint
        working-directory: ./back/backend
      
      - name: Run tests
        run: npm test
        working-directory: ./back/backend
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/testdb
          DIRECT_URL: postgresql://postgres:postgres@localhost:5432/testdb
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test-secret
          JWT_REFRESH_SECRET: test-refresh-secret
          TWILIO_ACCOUNT_SID: ${{ secrets.TEST_TWILIO_ACCOUNT_SID }}
          TWILIO_AUTH_TOKEN: ${{ secrets.TEST_TWILIO_AUTH_TOKEN }}
  
  test-frontend:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'front/mainpage/package-lock.json'
      
      - name: Install dependencies
        run: npm ci
        working-directory: ./front/mainpage
      
      - name: Run linting
        run: npm run lint
        working-directory: ./front/mainpage
      
      - name: Run tests
        run: npm test
        working-directory: ./front/mainpage
        env:
          NEXT_PUBLIC_API_URL: http://localhost:3000/api
  
  deploy-backend:
    needs: [test-backend, test-frontend]
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Install Railway CLI
        run: npm i -g @railway/cli
      
      - name: Deploy Backend to Railway
        run: railway up
        working-directory: ./back/backend
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
  
  deploy-frontend:
    needs: [test-backend, test-frontend]
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Install Vercel CLI
        run: npm install --global vercel@latest
      
      - name: Deploy Frontend to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./front/mainpage
          vercel-args: '--prod'
