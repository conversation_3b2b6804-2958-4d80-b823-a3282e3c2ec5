import { MenuItem } from '../components/ui/PermissionAwareMenu';

/**
 * Base navigation items for all authenticated users
 */
export const baseNavigationItems: MenuItem[] = [
  {
    name: 'Dashboard',
    path: '/dashboard',
    icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6',
    resource: 'dashboard',
    action: 'read',
    scope: 'self'
  },
  {
    name: 'Phone Numbers',
    path: '/dashboard/numbers',
    icon: 'M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z',
    resource: 'phoneNumbers',
    action: 'read',
    scope: 'self'
  },
  {
    name: 'Automation',
    path: '/dashboard/automation',
    icon: 'M13 10V3L4 14h7v7l9-11h-7z',
    resource: 'automation',
    action: 'read',
    scope: 'self'
  },
  {
    name: 'Call Logs',
    path: '/dashboard/call-logs',
    icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2',
    resource: 'calls',
    action: 'read',
    scope: 'self'
  },
  {
    name: 'Messages',
    path: '/dashboard/messages',
    icon: 'M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z',
    resource: 'messages',
    action: 'read',
    scope: 'self'
  },
  {
    name: 'Analytics',
    path: '/dashboard/analytics',
    icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
    resource: 'analytics',
    action: 'read',
    scope: 'self'
  },
  {
    name: 'Settings',
    path: '/dashboard/settings',
    icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z',
    resource: 'settings',
    action: 'read',
    scope: 'self'
  },
];

/**
 * Admin-specific navigation items
 */
export const adminNavigationItems: MenuItem[] = [
  {
    name: 'Admin Dashboard',
    path: '/dashboard/admin',
    icon: 'M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 11-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 004.486-6.336l-3.276 3.277a3.004 3.004 0 01-2.25-2.25l3.276-3.276a4.5 4.5 0 00-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437l1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008z',
    permission: 'admin:access:any'
  },
  {
    name: 'User Management',
    path: '/dashboard/admin/users',
    icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z',
    permission: 'users:read:any'
  },
  {
    name: 'Permissions',
    path: '/dashboard/admin/permissions',
    icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
    permission: 'roles:read:any'
  },
  {
    name: 'Permission Debugger',
    path: '/dashboard/admin/permissions/debug',
    icon: 'M12 19l9 2-9-18-9 18 9-2zm0 0v-8',
    permission: 'permissions:read:any'
  },
  {
    name: 'Billing',
    path: '/dashboard/admin/billing',
    icon: 'M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2zM10 8.5a.5.5 0 11-1 0 .5.5 0 011 0zm5 5a.5.5 0 11-1 0 .5.5 0 011 0z',
    permission: 'billing:read:any'
  },
  {
    name: 'System Settings',
    path: '/dashboard/admin/settings',
    icon: 'M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01',
    permission: 'settings:update:any'
  },
];

/**
 * Developer-specific navigation items
 */
export const developerNavigationItems: MenuItem[] = [
  {
    name: 'Developer Dashboard',
    path: '/dashboard/developer',
    icon: 'M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4',
    permission: 'developer:access:self'
  },
  {
    name: 'API Keys',
    path: '/dashboard/developer/api-keys',
    icon: 'M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z',
    permission: 'developer:access:self'
  },
  {
    name: 'Webhooks',
    path: '/dashboard/developer/webhooks',
    icon: 'M13 10V3L4 14h7v7l9-11h-7z',
    permission: 'developer:access:self'
  },
  {
    name: 'Documentation',
    path: '/dashboard/developer/docs',
    icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
    permission: 'developer:access:self'
  },
  {
    name: 'Permission Examples',
    path: '/dashboard/examples/permissions',
    icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
    permission: 'developer:access:self'
  },
];

/**
 * Get navigation items based on permissions
 * @returns Navigation items the user has permission to access
 */
export function getNavigationItems(): MenuItem[] {
  // Combine all navigation items
  return [...baseNavigationItems, ...adminNavigationItems, ...developerNavigationItems];
}
