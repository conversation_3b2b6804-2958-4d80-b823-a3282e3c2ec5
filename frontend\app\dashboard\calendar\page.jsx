'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import 'react-big-calendar/lib/css/react-big-calendar.css';

// Set up the localizer for the calendar
const localizer = momentLocalizer(moment);

export default function BookingCalendarPage() {
  const [events, setEvents] = useState([]);
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showEventModal, setShowEventModal] = useState(false);
  const [newEvent, setNewEvent] = useState({
    title: '',
    start: new Date(),
    end: new Date(new Date().setHours(new Date().getHours() + 1)),
    description: '',
    phoneNumber: '',
    email: '',
    status: 'confirmed'
  });
  const [syncStatus, setSyncStatus] = useState({
    google: false,
    outlook: false,
    lastSynced: null
  });

  // Fetch calendar events from API
  useEffect(() => {
    const fetchCalendarEvents = async () => {
      try {
        setIsLoading(true);
        // Replace with actual API endpoint when backend is ready
        const response = await fetch('/api/calendar/events');
        
        if (!response.ok) {
          throw new Error('Failed to fetch calendar events');
        }
        
        const data = await response.json();
        // Convert string dates to Date objects
        const formattedEvents = data.map(event => ({
          ...event,
          start: new Date(event.start),
          end: new Date(event.end)
        }));
        
        setEvents(formattedEvents);
      } catch (err) {
        console.error('Error fetching calendar events:', err);
        setError('Failed to load calendar events. Please try again later.');
        // Use mock data for now
        const formattedMockEvents = mockCalendarEvents.map(event => ({
          ...event,
          start: new Date(event.start),
          end: new Date(event.end)
        }));
        setEvents(formattedMockEvents);
      } finally {
        setIsLoading(false);
      }
    };

    // Fetch sync status
    const fetchSyncStatus = async () => {
      try {
        // Replace with actual API endpoint when backend is ready
        const response = await fetch('/api/calendar/sync-status');
        
        if (!response.ok) {
          throw new Error('Failed to fetch calendar sync status');
        }
        
        const data = await response.json();
        setSyncStatus(data);
      } catch (err) {
        console.error('Error fetching calendar sync status:', err);
        // Use mock data
        setSyncStatus({
          google: true,
          outlook: false,
          lastSynced: new Date().toISOString()
        });
      }
    };

    fetchCalendarEvents();
    fetchSyncStatus();
  }, []);

  // Handle event selection
  const handleSelectEvent = (event) => {
    setSelectedEvent(event);
    setShowEventModal(true);
  };

  // Handle creating a new event slot
  const handleSelectSlot = ({ start, end }) => {
    setNewEvent({
      ...newEvent,
      start,
      end
    });
    setSelectedEvent(null);
    setShowEventModal(true);
  };

  // Handle saving an event
  const handleSaveEvent = async (e) => {
    e.preventDefault();
    
    const eventToSave = selectedEvent 
      ? { ...selectedEvent, ...newEvent } 
      : newEvent;
    
    try {
      setIsLoading(true);
      
      // Replace with actual API endpoint when backend is ready
      const response = await fetch('/api/calendar/events', {
        method: selectedEvent ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(eventToSave),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to ${selectedEvent ? 'update' : 'create'} event`);
      }
      
      const savedEvent = await response.json();
      
      // Update events list
      if (selectedEvent) {
        setEvents(events.map(event => 
          event.id === savedEvent.id ? { ...savedEvent, start: new Date(savedEvent.start), end: new Date(savedEvent.end) } : event
        ));
      } else {
        setEvents([...events, { ...savedEvent, start: new Date(savedEvent.start), end: new Date(savedEvent.end) }]);
      }
      
      setShowEventModal(false);
      setNewEvent({
        title: '',
        start: new Date(),
        end: new Date(new Date().setHours(new Date().getHours() + 1)),
        description: '',
        phoneNumber: '',
        email: '',
        status: 'confirmed'
      });
      
    } catch (err) {
      console.error(`Error ${selectedEvent ? 'updating' : 'creating'} event:`, err);
      setError(`Failed to ${selectedEvent ? 'update' : 'create'} event. Please try again.`);
      
      // For demo purposes, update the UI optimistically
      const tempEvent = {
        ...eventToSave,
        id: selectedEvent ? selectedEvent.id : `temp-${Date.now()}`
      };
      
      if (selectedEvent) {
        setEvents(events.map(event => event.id === tempEvent.id ? tempEvent : event));
      } else {
        setEvents([...events, tempEvent]);
      }
      
      setShowEventModal(false);
      
    } finally {
      setIsLoading(false);
    }
  };

  // Handle deleting an event
  const handleDeleteEvent = async () => {
    if (!selectedEvent) return;
    
    try {
      setIsLoading(true);
      
      // Replace with actual API endpoint when backend is ready
      const response = await fetch(`/api/calendar/events/${selectedEvent.id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete event');
      }
      
      // Update events list
      setEvents(events.filter(event => event.id !== selectedEvent.id));
      setShowEventModal(false);
      
    } catch (err) {
      console.error('Error deleting event:', err);
      setError('Failed to delete event. Please try again.');
      
      // For demo purposes, update the UI optimistically
      setEvents(events.filter(event => event.id !== selectedEvent.id));
      setShowEventModal(false);
      
    } finally {
      setIsLoading(false);
    }
  };

  // Handle syncing with external calendars
  const handleSyncCalendar = async (provider) => {
    try {
      setIsLoading(true);
      
      // Replace with actual API endpoint when backend is ready
      const response = await fetch('/api/calendar/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ provider }),
      });
      
      if (!response.ok) {
        throw new Error(`Failed to sync with ${provider}`);
      }
      
      const data = await response.json();
      
      // Update sync status
      setSyncStatus({
        ...syncStatus,
        [provider]: true,
        lastSynced: new Date().toISOString()
      });
      
      // Refresh events
      const formattedEvents = data.events.map(event => ({
        ...event,
        start: new Date(event.start),
        end: new Date(event.end)
      }));
      
      setEvents(formattedEvents);
      
    } catch (err) {
      console.error(`Error syncing with ${provider}:`, err);
      setError(`Failed to sync with ${provider}. Please try again.`);
    } finally {
      setIsLoading(false);
    }
  };

  // Calendar event styles
  const eventStyleGetter = (event) => {
    let backgroundColor = '#6d28d9'; // Default purple
    
    switch(event.status) {
      case 'confirmed':
        backgroundColor = '#10b981'; // Green
        break;
      case 'pending':
        backgroundColor = '#f59e0b'; // Yellow
        break;
      case 'cancelled':
        backgroundColor = '#ef4444'; // Red
        break;
    }
    
    return {
      style: {
        backgroundColor,
        borderRadius: '4px',
        opacity: 0.8,
        color: 'white',
        border: '0px',
        display: 'block'
      }
    };
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-white">Booking Calendar</h1>
          <div className="flex space-x-3">
            <button
              onClick={() => handleSyncCalendar('google')}
              className={`px-4 py-2 rounded-lg flex items-center space-x-2 ${
                syncStatus.google 
                  ? 'bg-green-600 hover:bg-green-700' 
                  : 'bg-gray-700 hover:bg-gray-600'
              } text-white transition-colors`}
              disabled={isLoading}
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z"/>
              </svg>
              <span>{syncStatus.google ? 'Synced with Google' : 'Sync with Google'}</span>
            </button>
            <button
              onClick={() => handleSyncCalendar('outlook')}
              className={`px-4 py-2 rounded-lg flex items-center space-x-2 ${
                syncStatus.outlook 
                  ? 'bg-green-600 hover:bg-green-700' 
                  : 'bg-gray-700 hover:bg-gray-600'
              } text-white transition-colors`}
              disabled={isLoading}
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                <path d="M7.88,12.04c0-4.28,3.5-7.76,7.78-7.76c1.84,0,3.55,0.63,4.9,1.72L24,3.38V21H7.88V12.04z M11,12h5.33l2.67-3v6L16.33,12H11z"/>
              </svg>
              <span>{syncStatus.outlook ? 'Synced with Outlook' : 'Sync with Outlook'}</span>
            </button>
          </div>
        </div>

        {error && (
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3 text-red-200">
            {error}
          </div>
        )}

        {syncStatus.lastSynced && (
          <div className="text-sm text-gray-400">
            Last synced: {new Date(syncStatus.lastSynced).toLocaleString()}
          </div>
        )}

        <div className="bg-gray-800/50 rounded-xl border border-purple-500/20 p-6 h-[calc(100vh-250px)]">
          {isLoading && !events.length ? (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
            </div>
          ) : (
            <Calendar
              localizer={localizer}
              events={events}
              startAccessor="start"
              endAccessor="end"
              style={{ height: '100%' }}
              onSelectEvent={handleSelectEvent}
              onSelectSlot={handleSelectSlot}
              selectable
              eventPropGetter={eventStyleGetter}
              views={['month', 'week', 'day', 'agenda']}
              defaultView="week"
              className="text-white calendar-dark-theme"
            />
          )}
        </div>
      </div>

      {/* Event Modal */}
      {showEventModal && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-gray-800 rounded-xl border border-purple-500/30 p-6 max-w-md w-full mx-4"
          >
            <h2 className="text-xl font-semibold text-white mb-4">
              {selectedEvent ? 'Edit Appointment' : 'New Appointment'}
            </h2>
            <form onSubmit={handleSaveEvent}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Title
                  </label>
                  <input
                    type="text"
                    value={selectedEvent ? selectedEvent.title : newEvent.title}
                    onChange={(e) => selectedEvent 
                      ? setSelectedEvent({...selectedEvent, title: e.target.value})
                      : setNewEvent({...newEvent, title: e.target.value})
                    }
                    className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Appointment title"
                    required
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">
                      Start Date & Time
                    </label>
                    <input
                      type="datetime-local"
                      value={moment(selectedEvent ? selectedEvent.start : newEvent.start).format('YYYY-MM-DDTHH:mm')}
                      onChange={(e) => {
                        const date = new Date(e.target.value);
                        selectedEvent 
                          ? setSelectedEvent({...selectedEvent, start: date})
                          : setNewEvent({...newEvent, start: date});
                      }}
                      className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">
                      End Date & Time
                    </label>
                    <input
                      type="datetime-local"
                      value={moment(selectedEvent ? selectedEvent.end : newEvent.end).format('YYYY-MM-DDTHH:mm')}
                      onChange={(e) => {
                        const date = new Date(e.target.value);
                        selectedEvent 
                          ? setSelectedEvent({...selectedEvent, end: date})
                          : setNewEvent({...newEvent, end: date});
                      }}
                      className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Description
                  </label>
                  <textarea
                    value={selectedEvent ? selectedEvent.description : newEvent.description}
                    onChange={(e) => selectedEvent 
                      ? setSelectedEvent({...selectedEvent, description: e.target.value})
                      : setNewEvent({...newEvent, description: e.target.value})
                    }
                    className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 min-h-[80px]"
                    placeholder="Appointment details"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={selectedEvent ? selectedEvent.phoneNumber : newEvent.phoneNumber}
                    onChange={(e) => selectedEvent 
                      ? setSelectedEvent({...selectedEvent, phoneNumber: e.target.value})
                      : setNewEvent({...newEvent, phoneNumber: e.target.value})
                    }
                    className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Contact phone number"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    value={selectedEvent ? selectedEvent.email : newEvent.email}
                    onChange={(e) => selectedEvent 
                      ? setSelectedEvent({...selectedEvent, email: e.target.value})
                      : setNewEvent({...newEvent, email: e.target.value})
                    }
                    className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Contact email"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Status
                  </label>
                  <select
                    value={selectedEvent ? selectedEvent.status : newEvent.status}
                    onChange={(e) => selectedEvent 
                      ? setSelectedEvent({...selectedEvent, status: e.target.value})
                      : setNewEvent({...newEvent, status: e.target.value})
                    }
                    className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="confirmed">Confirmed</option>
                    <option value="pending">Pending</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
              </div>
              
              <div className="flex justify-between mt-6">
                <div>
                  {selectedEvent && (
                    <button
                      type="button"
                      onClick={handleDeleteEvent}
                      className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                    >
                      Delete
                    </button>
                  )}
                </div>
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowEventModal(false)}
                    className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Saving...' : 'Save'}
                  </button>
                </div>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </div>
  );
}

// Mock data for development
const mockCalendarEvents = [
  {
    id: '1',
    title: 'Initial Consultation',
    start: '2025-04-01T10:00:00',
    end: '2025-04-01T11:00:00',
    description: 'First meeting to discuss project requirements',
    phoneNumber: '+****************',
    email: '<EMAIL>',
    status: 'confirmed'
  },
  {
    id: '2',
    title: 'Follow-up Call',
    start: '2025-04-02T14:00:00',
    end: '2025-04-02T14:30:00',
    description: 'Quick check-in on project progress',
    phoneNumber: '+****************',
    email: '<EMAIL>',
    status: 'pending'
  },
  {
    id: '3',
    title: 'Product Demo',
    start: '2025-04-03T15:00:00',
    end: '2025-04-03T16:30:00',
    description: 'Demonstration of new features',
    phoneNumber: '+****************',
    email: '<EMAIL>',
    status: 'confirmed'
  },
  {
    id: '4',
    title: 'Team Meeting',
    start: '2025-04-04T09:00:00',
    end: '2025-04-04T10:00:00',
    description: 'Weekly team sync',
    phoneNumber: '',
    email: '',
    status: 'confirmed'
  },
  {
    id: '5',
    title: 'Client Presentation',
    start: '2025-04-05T13:00:00',
    end: '2025-04-05T14:30:00',
    description: 'Final presentation of project deliverables',
    phoneNumber: '+****************',
    email: '<EMAIL>',
    status: 'cancelled'
  }
];
