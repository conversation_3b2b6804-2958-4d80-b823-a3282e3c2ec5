import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request) {
  console.log('API route /api/chat/history invoked');
  try {
    const { searchParams } = new URL(request.url);
    const phoneNumber = searchParams.get('phoneNumber');

    if (!phoneNumber) {
      return NextResponse.json({ success: false, message: 'Phone number parameter is required.' }, { status: 400 });
    }

    // Initialize Supabase client to verify ownership
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      { cookies: { get: (name) => cookieStore.get(name)?.value } }
    );

    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error('Error getting session:', sessionError);
      return NextResponse.json({ success: false, message: 'Session error' }, { status: 500 });
    }
    if (!session) {
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 401 });
    }

    // Verify user owns the number (important security check!)
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true }
    });
    const numberRecord = await prisma.phoneNumber.findUnique({
      where: { number: phoneNumber },
      select: { userId: true }
    });

    if (!user || !numberRecord || numberRecord.userId !== user.id) {
        console.warn(`Unauthorized attempt to access history for ${phoneNumber} by user ${session.user.email}`);
        return NextResponse.json({ success: false, message: 'Access denied to this phone number history.' }, { status: 403 });
    }

    // Fetch chat messages from the database
    console.log(`Fetching chat history for ${phoneNumber}`);
    const messages = await prisma.chatMessage.findMany({
      where: {
        phoneNumber: phoneNumber,
        userId: user.id // Ensure we only get messages linked to the correct user
      },
      orderBy: {
        createdAt: 'asc' // Order chronologically
      },
      take: 100 // Limit history length for performance
    });

    console.log(`Found ${messages.length} messages for ${phoneNumber}`);

    // Return the messages
    return NextResponse.json({ success: true, history: messages });

  } catch (error) {
    console.error('[API /api/chat/history] Error fetching chat history:', error);
    return NextResponse.json({ success: false, message: 'Failed to fetch chat history' }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
} 