---
description:
globs:
alwaysApply: false
---
# AI Integration Layer Functional Document (`ai_integration_layer.mdc`)

## 1. Purpose and Scope

**Purpose:** Define the architecture, capabilities, and interfaces of the central AI service(s) powering intelligent features across the CallSaver platform. This layer acts as the brain, processing communication data, executing commands, generating insights, and enabling automation.

**Scope:**
- Provide core AI functionalities:
    - Natural Language Processing (NLP) for understanding calls and messages.
    - Speech-to-Text (STT) for call transcription.
    - Text-to-Speech (TTS) for AI voice responses.
    - Sentiment Analysis.
    - Call/Message Summarization.
    - Named Entity Recognition (NER) for extracting key info (names, dates, topics).
    - Intent Recognition and Command Execution based on user-defined rules or voice/SMS commands.
    - Knowledge Base Management and Retrieval for AI assistants.
    - (Future) Predictive analytics or anomaly detection.
- Interface with communication providers (Twilio/eSIM) to intercept and handle calls/SMS when configured.
- Integrate with other backend services (Database, Analytics, Notifications, User Service).
- Manage AI models (potentially multiple models for different tasks, including fine-tuned or third-party LLMs).
- Handle AI training data ingestion and potentially retraining workflows.
- Provide APIs for configuration (via Automation section) and data retrieval (for Logs, Analytics, Dashboard).

## 2. User Interactions (Indirect)

Users interact with the AI layer *through* other dashboard sections:
- **Automation:** Configuring AI behavior (rules, personality, knowledge, commands).
- **Call/Message Logs:** Viewing AI-generated transcriptions, summaries, sentiment.
- **Analytics:** Seeing AI-driven metrics and insights.
- **Dashboard:** Viewing AI summaries or alerts.
- **Help Center:** Interacting with an AI chatbot or AI-powered search.
- **Direct Interaction:** (Future) A dedicated chat interface to interact directly with the AI assistant for a specific number.
- **Voice/SMS Commands:** Interacting via phone calls or text messages using defined commands.

## 3. Backend Integrations & Services Used

- **Communication Providers (Twilio/eSIM):** Receive webhooks for incoming calls/SMS, use provider APIs to control call flow (e.g., `<Say>`, `<Gather>`, `<Record>`), send messages.
- **Database:** Store AI configurations (per number), processed data (transcripts, summaries, sentiment scores), training data, knowledge base content.
- **Speech-to-Text Service (e.g., Google Cloud Speech, AssemblyAI, Whisper):** Transcribe call audio.
- **Text-to-Speech Service (e.g., Google Cloud TTS, ElevenLabs):** Generate synthesized voice responses.
- **LLM Provider (e.g., OpenAI GPT-4/Gemini/Claude):** Used for summarization, sentiment analysis, intent recognition, command execution logic, conversational responses.
- **Vector Database (e.g., Pinecone, Supabase pgvector):** Store and query embeddings for knowledge base retrieval (RAG).
- **Analytics Service:** Push processed AI metrics (sentiment, interaction types, command usage) for aggregation.
- **Notification Service:** Trigger notifications based on AI events (e.g., urgent sentiment detected, command failed).
- **User Service:** Fetch user/organization context for personalization or permission checks.
- **Storage Service (e.g., S3):** Store raw audio recordings before/after processing.

## 4. Necessary API Endpoints (Primarily Internal/Service-to-Service)

*These might not all be user-facing REST APIs, but represent logical interfaces.*

- **Webhook Endpoints:**
    - `POST /webhooks/twilio/voice`: Receives incoming call events from Twilio.
    - `POST /webhooks/twilio/message`: Receives incoming SMS events from Twilio.
    - `POST /webhooks/esim/event`: (Generic) Receives events from eSIM providers.
- **Configuration API (Used by Automation Section):**
    - `PUT /internal/ai/config/{phoneNumberId}`: Updates the full AI configuration for a number.
    - `POST /internal/ai/knowledge/{phoneNumberId}`: Adds data to a number's knowledge base.
- **Processing Triggers:**
    - `POST /internal/ai/process/call/{callSid}`: Initiates post-call processing (transcription, summary, sentiment).
    - `POST /internal/ai/process/message/{messageSid}`: Initiates message processing (sentiment, intent recognition).
- **Data Retrieval API (Used by Logs, Analytics, Dashboard):**
    - `GET /internal/ai/data/transcription/{callSid}`
    - `GET /internal/ai/data/summary/{callSid}`
    - `GET /internal/ai/data/sentiment/{callSid | messageSid}`
    - `GET /internal/ai/data/commands/executions?phoneNumberId=<id>`
- **Direct Interaction API (Future):**
    - `POST /api/ai/chat/{phoneNumberId}`: Send a message to the AI assistant for a number.

## 5. Conceptual Backend Structure

```
/ai-service
  /webhook-handlers         # Handles incoming events from Twilio/eSIM
  /call-processor           # Manages active call logic (STT, TTS, LLM interaction)
  /message-processor        # Manages SMS logic (Intent recognition, LLM interaction)
  /post-processing          # Handles async tasks (transcription, summary, sentiment)
  /knowledge-manager        # Interfaces with Vector DB for RAG
  /command-executor         # Executes defined custom commands (may call external APIs)
  /model-integrations       # Adapters for different STT, TTS, LLM providers
  /config-manager           # Loads and manages AI configurations per number
  /api                      # Internal API endpoints for other services
```

## 6. Data Processed & Generated

- **Input:** Call audio streams, SMS content, User configuration (rules, knowledge docs, commands), User prompts (via chat/voice/SMS).
- **Intermediate:** Raw transcriptions, LLM prompts, Embeddings for knowledge data.
- **Output:** Formatted transcriptions, Call/message summaries, Sentiment scores (e.g., positive/negative/neutral, or numeric), Recognized intents, Extracted entities, Command execution results (success/failure, data payload), Synthesized audio responses, Chat messages.

## 7. State and UI Behavior (Backend Behavior)

- **Real-time Processing:** Handle active calls/SMS with low latency, managing state machines for interactions (e.g., waiting for user input via `<Gather>`).
- **Asynchronous Processing:** Offload non-real-time tasks like transcription, summarization, and analytics updates to background queues/workers.
- **Scalability:** Design services to scale horizontally to handle varying loads of calls and messages.
- **Model Management:** Implement strategies for selecting appropriate models (cost vs. performance), potentially A/B testing, and updating models.
- **Configuration Loading:** Efficiently load the correct AI configuration (rules, knowledge) for the specific phone number involved in an interaction.
- **Rate Limiting/Cost Control:** Implement safeguards against excessive API usage with third-party providers (LLMs, STT, TTS).

## 8. AI Integration (Core Functionalities Summary)

- **Call Handling:** Answering calls, understanding caller intent, providing information, executing commands, routing calls, taking messages.
- **SMS Handling:** Understanding incoming messages, executing commands, providing information, sending automated or AI-generated replies.
- **Voicemail Processing:** Transcribing voicemails, summarizing content, analyzing sentiment.
- **Data Enrichment:** Adding summaries and sentiment scores to call/message logs.
- **Analytics Insights:** Providing data points for analytics (sentiment trends, command usage).
- **Support Automation:** Powering chatbots or AI-assisted search in the Help Center.

## 9. Error Handling Rules

- **Provider Failures:** Implement retry logic and fallbacks for failures from external services (STT, TTS, LLM, Twilio). Log errors clearly.
    - *Example:* If TTS fails, play a generic pre-recorded message. If LLM fails, follow a default rule (e.g., send to voicemail).
- **Transcription Errors:** Handle low-confidence transcriptions; potentially flag them in the UI.
- **Intent Recognition Failures:** If user intent is unclear after retries, follow a default fallback path (e.g., "Sorry, I didn't understand, connecting you to...").
- **Command Execution Errors:** Report failures back to the user (if in active interaction) and log the error details. Trigger notifications if configured.
- **Configuration Issues:** Validate AI configurations upon saving; handle missing or invalid configurations gracefully during execution (use defaults or report error).
- **Rate Limiting:** Handle 429 errors from providers gracefully, potentially queuing requests or notifying admins.

## 10. Logging and Usage Tracking Expectations

- **Log:**
    - Start and end of AI processing for calls/messages (include relevant IDs).
    - External API calls made (provider, duration, success/failure, cost if available).
    - Recognized intents and executed commands (success/failure, parameters).
    - Errors encountered during any stage of AI processing (include stack traces/details).
    - Configuration loading events (which config was loaded for which number).
    - Knowledge base interactions (queries, retrieved documents).
- **Track:**
    - Usage of specific AI features (transcriptions, summaries, sentiment analyses performed).
    - Latency of real-time interactions (e.g., time to first response).
    - Accuracy metrics (e.g., command success rate, sentiment accuracy - may require manual review or user feedback).
    - Cost per interaction/feature (track external API costs).
    - Usage patterns per number/user.
    - Fallback rule triggers (indicates AI limitations or configuration issues).
