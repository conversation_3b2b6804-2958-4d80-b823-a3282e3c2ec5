'use client';

import { createBrowserClient } from '@supabase/ssr';

// Global variable to hold the singleton instance
let clientInstance = null;
// Flag to track initialization in progress
let initializationInProgress = false;
// Flag to indicate if an initialization attempt has been made
let initializationAttempted = false;

// Create a simple mock client that won't throw errors
const createMockClient = (reason) => {
  console.warn(`Creating mock Supabase client: ${reason}`);
  return {
    auth: {
      getSession: async () => ({ data: { session: null }, error: null }),
      signInWithPassword: async () => ({ data: null, error: new Error(`Supabase client unavailable: ${reason}`) }),
      signOut: async () => ({ error: null }),
      onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } })
    },
    // Add minimal implementations for other commonly used methods
    from: () => ({
      select: () => ({ data: [], error: null }),
      insert: () => ({ data: null, error: new Error(`Supabase client unavailable: ${reason}`) }),
      update: () => ({ data: null, error: new Error(`Supabase client unavailable: ${reason}`) }),
      delete: () => ({ data: null, error: new Error(`Supabase client unavailable: ${reason}`) })
    })
  };
};

// Function to create or return the singleton Supabase client instance
export const getSupabaseClient = () => {
  // Return existing instance if already created and valid
  if (clientInstance && clientInstance.auth && typeof clientInstance.auth.getSession === 'function') {
    return clientInstance;
  }

  // If initialization is already in progress, wait for it to complete
  if (initializationInProgress) {
    throw new Error('Supabase client initialization in progress. Please retry your operation.');
  }

  // Ensure this runs only on the client
  if (typeof window === 'undefined') {
    throw new Error('Supabase client can only be initialized in browser environment');
  }

  // Set flag to indicate we're attempting initialization
  initializationInProgress = true;
  initializationAttempted = true;

  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseAnonKey) {
      initializationInProgress = false;
      throw new Error('Supabase credentials are missing in environment variables');
    }

    // Create the actual client instance
    const newClient = createBrowserClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        flowType: 'pkce', // Recommended for browser clients
        detectSessionInUrl: true,
        persistSession: true,
        // Using default cookie storage is generally recommended with @supabase/ssr
        // The library handles cookie storage automatically.
      }
    });

    // Validate the created client
    if (!newClient || !newClient.auth || typeof newClient.auth.getSession !== 'function') {
      throw new Error('Created client is invalid or missing auth methods');
    }

    // Set up auth state change listener for better debugging (optional here)
    newClient.auth.onAuthStateChange((event, session) => {
      console.log('[getSupabaseClient] Auth state changed:', event, session ? 'Session exists' : 'No session');

      // Example: Update local storage flag on sign-in/sign-out
      // Ensure window check wraps localStorage access
      if (typeof window !== 'undefined') {
        if (event === 'SIGNED_IN') {
          localStorage.setItem('just_signed_in', 'true');
          localStorage.setItem('sign_in_time', Date.now().toString());
        } else if (event === 'SIGNED_OUT') {
          localStorage.removeItem('just_signed_in');
          localStorage.removeItem('sign_in_time');
          localStorage.removeItem('auth_retry_count');
        }
      }
    });

    // Store the instance globally
    clientInstance = newClient;
    initializationInProgress = false;
    console.log("Supabase client initialized successfully.");
    return clientInstance;

  } catch (error) {
    console.error('Failed to initialize Supabase client:', error);
    clientInstance = null; // Ensure instance is null on error
    initializationInProgress = false;
    throw error; // Throw the error instead of returning a mock client
  }
};

// Default export the function for easy import
// Note: Files importing this will need to change from `import supabaseClient from ...`
// to `import getSupabaseClient from ...` and call `getSupabaseClient()`
export default getSupabaseClient;
