---
description: 
globs: 
alwaysApply: false
---
# Component State Mapping Functional Document (`component_state_mapping.mdc`)

## 1. Purpose and Scope

**Purpose:** Outline the strategy for managing frontend state within the CallSaver Next.js application. This includes defining types of state, chosen libraries/patterns, how data fetched from the API is stored and accessed by components, and how UI state is handled.

**Scope:**
- Define the primary state management approach (e.g., global stores, server state caching, local component state).
- Identify key data domains and map them to the appropriate state management strategy.
- Describe the data fetching, caching, and synchronization mechanisms between the frontend and backend API.
- Detail how loading, error, and empty states derived from the application state are handled in components.
- Explain the integration of real-time updates (via WebSockets) into the state management system.

## 2. State Management Approach

- **Server State Caching:** Utilize a dedicated library like **React Query (TanStack Query)** or **RTK Query** to manage server state. This handles fetching, caching, background synchronization, invalidation, loading/error states, and optimization for data fetched from the API Gateway. This will be the primary way data like call logs, number lists, analytics, etc., are managed.
- **Global Client State:** Employ a lightweight global state manager like **Zustand** or **React Context API** for minimal, truly global client-side state that isn't directly tied to server data. Examples include:
    - User authentication status (e.g., `isAuthenticated`).
    - Basic user profile information needed globally (name, role).
    - UI state like sidebar collapsed/expanded status, notification panel open/closed.
    - Unread notification count.
- **Local Component State:** Use standard React `useState` and `useReducer` hooks for state confined to a single component or a small, closely related group of components. Examples include:
    - Form input values and validation status.
    - Modal open/closed state.
    - Toggle states within a component.
    - State specific to complex UI interactions within a component.

## 3. Data Domain State Mapping

| Data Domain                     | Primary State Management | Notes                                                                 |
| :------------------------------ | :----------------------- | :-------------------------------------------------------------------- |
| **User Auth & Profile**       | Global (Zustand/Context) | `isAuthenticated`, basic user info (name, email, role) needed globally. |
| **Detailed User Settings**    | Server Cache (React Query) | Profile details, notification prefs, API keys fetched for Settings page. |
| **Organization Users/Roles**  | Server Cache (React Query) | Fetched list for User Management section (Admin only).                |
| **Owned Numbers/eSIMs**       | Server Cache (React Query) | List fetched for Number Management, Automation selector, filters.       |
| **Available Numbers/eSIMs**   | Server Cache (React Query) | Search results fetched on demand in Number Management.                |
| **Automation Config**         | Server Cache (React Query) | Fetched when a number is selected in Automation section.                |
| **AI Assistant Config**       | Server Cache (React Query) | Fetched when managing AI for a number.                                |
| **Call Logs**                 | Server Cache (React Query) | Paginated, filterable data fetched for Call Logs section.             |
| **Call Details (Recording etc)**| Server Cache (React Query) | Fetched on demand when viewing details of a specific call.            |
| **Analytics Data**            | Server Cache (React Query) | Fetched based on filters (date range, number) for Analytics section.  |
| **Credit Balance**            | Global (Zustand/Context) / Server Cache | Balance might be global for header display, refreshed via React Query. |
| **Billing History**           | Server Cache (React Query) | Paginated transaction list fetched for Billing section.               |
| **Subscription Status**       | Server Cache (React Query) | Fetched for display in Settings/Billing.                              |
| **Notifications List**        | Server Cache (React Query) | Fetched for the in-app notification center.                           |
| **Unread Notification Count** | Global (Zustand/Context) | Updated via WebSocket push, potentially backed by React Query fetch.    |
| **Availability Rules**        | Server Cache (React Query) | Fetched for the Appointment Scheduler settings.                       |
| **Appointments**              | Server Cache (React Query) | Fetched based on date range for the calendar view.                    |
| **Help Articles/Categories**  | Server Cache (React Query) | Fetched on demand for the Help Center.                                |
| **Form State**                | Local Component State    | Input values, validation errors within forms (Settings, Automation).  |
| **UI Element State**          | Local / Global State     | Modal visibility (local), Sidebar state (global).                     |

## 4. Data Fetching, Caching, and Synchronization

- **React Query / RTK Query:**
    - Define query keys based on endpoint and parameters (e.g., `['call-logs', { page: 1, status: 'missed' }]`).
    - Use hooks like `useQuery`, `useInfiniteQuery` (for pagination), `useMutation`.
    - Leverage automatic caching, background refetching (`stale-while-revalidate`).
    - Implement optimistic updates for mutations (e.g., adding a note to a call log) for better UX.
    - Use query invalidation (`queryClient.invalidateQueries`) after mutations to ensure data consistency (e.g., invalidate `['call-logs']` after adding a note).
- **Zustand / Context:**
    - Define simple stores/providers for global state.
    - Update state via defined actions.
    - Persist state to `localStorage` if needed (e.g., sidebar preference), though auth state should rely on secure session cookies.

## 5. Loading, Error, and Empty States

- **React Query Integration:** Components using `useQuery` will have access to `isLoading`, `isError`, `error`, `data`, `isFetching` states.
    - Render skeleton loaders based on `isLoading` or `isFetching`.
    - Render specific error messages or generic error components based on `isError` and the `error` object.
    - Check if `data` is empty or null after successful fetch to render "No data" / empty state messages.
- **Global State:** Components consuming global state should handle cases where data might not be immediately available (e.g., user profile loading after login).
- **Consistency:** Implement reusable `LoadingSpinner`, `ErrorMessage`, `EmptyState` components to ensure UI consistency.

## 6. Real-time Updates (WebSockets)

- **Integration:** A WebSocket connection managed globally (e.g., in a main layout or context).
- **Event Handling:** When a WebSocket message arrives (e.g., `new_notification`, `balance_updated`, `call_status_changed`):
    - **Update Global State:** Directly update Zustand/Context state for things like unread notification count or balance.
    - **Invalidate Server Cache:** Use `queryClient.invalidateQueries` to trigger React Query to refetch relevant data (e.g., invalidate `['notifications']` on `new_notification`, invalidate `['call-logs']` on `call_status_changed`). This ensures the cached server state reflects the real-time changes upon next render or background refetch.
    - **Avoid Direct Cache Manipulation:** Prefer invalidation over directly manipulating the React Query cache via WebSockets unless absolutely necessary and carefully managed, as it can bypass fetching logic and become complex.

## 7. State Management Considerations

- **Modularity:** Keep state logic co-located with features where possible (e.g., React Query logic near the components using it, Zustand stores organized by feature).
- **Performance:** Avoid excessive re-renders. Use selectors (`useStore(state => state.value)`) with Zustand. React Query is generally optimized. Memoize components where necessary.
- **Debugging:** Utilize React DevTools, Zustand DevTools middleware, and React Query DevTools for inspecting state, actions, and cache contents during development.
- **Testing:** State management logic (Zustand actions, custom hooks using React Query) should be unit-testable. Component tests should mock providers/stores as needed.

## 8. Logging

- **State Changes (Debug):** During development, log significant global state changes or React Query cache updates.
- **Errors:** Log errors encountered during state updates or data fetching (captured by React Query/global error handlers).
- **WebSocket Events:** Log received WebSocket messages and corresponding state update/invalidation actions.
