---
title: CallSaver.app System Architecture
description: Comprehensive overview of the CallSaver.app system architecture, components, and interactions
date: 2025-04-28
status: Required
---

# CallSaver.app System Architecture

## Overview

CallSaver.app is a multi-tenant SaaS application for managing phone numbers, calls, and SMS with AI-powered features. This document provides a comprehensive overview of the system architecture, describing the main components, their interactions, data flows, and deployment topology.

## System Context Diagram

```
                                      ┌─────────────────┐
                      Webhooks        │                 │
                  ┌─────────────────▶│  Twilio API     │
                  │                   │                 │
                  │                   └─────────────────┘
                  │                            ▲
                  │                            │ API Calls
┌─────────────────┐           ┌─────────────────┐
│                 │  HTTP     │                 │          ┌─────────────────┐
│   End User      │◀─────────▶│  CallSaver.app  │◀────────▶│  OpenAI API     │
│   (Browser)     │           │                 │          │                 │
│                 │           └─────────────────┘          └─────────────────┘
└─────────────────┘                   ▲
                                      │ Payments            ┌─────────────────┐
                                      │                     │                 │
                                      └────────────────────▶│  Stripe API     │
                                                            │                 │
                                                            └─────────────────┘
```

## Core Components

CallSaver.app follows a modern microservices-inspired architecture with clear boundaries between components, while still maintaining the simplicity of a monolithic deployment for the initial launch.

### Backend Components

1. **API Gateway**
   - Routes and validates all incoming API requests
   - Handles authentication and authorization
   - Applies rate limiting and security measures
   - Manages CORS and other cross-cutting concerns

2. **Authentication Service**
   - Manages user authentication (JWT tokens)
   - Integrates with Supa<PERSON> Auth for external providers (Google, etc.)
   - Handles session management and security features

3. **User Management Service**
   - Handles user registration, profile management
   - Manages organizations and multi-tenant features
   - Controls user roles and permissions

4. **Telephony Service**
   - Integrates with Twilio for number purchase and management
   - Handles call and SMS operations
   - Manages call forwarding and routing rules
   - Processes incoming webhook events from Twilio

5. **AI Service**
   - Processes calls and messages with AI capabilities
   - Manages transcription, summarization, and sentiment analysis
   - Handles knowledge base management and retrieval (RAG)
   - Integrates with OpenAI for language model capabilities

6. **Automation Service**
   - Manages automation rules and workflows
   - Processes triggers and executes actions
   - Handles scheduled automations and recurring tasks

7. **Analytics Service**
   - Collects and processes usage metrics
   - Generates reports and insights
   - Provides dashboard data

8. **Billing Service**
   - Manages credits and usage tracking
   - Integrates with Stripe for payments
   - Handles subscriptions and billing operations

9. **Notification Service**
   - Delivers in-app, email, and SMS notifications
   - Manages notification preferences

10. **Task Queue Service**
    - Manages async job processing
    - Handles retries and dead-letter queues
    - Provides monitoring for background tasks

### Frontend Components

1. **Next.js App Router**
   - Handles client-side and server-side rendering
   - Manages routing and navigation
   - Provides responsive layouts

2. **UI Component Library**
   - Reusable UI components built with React and TailwindCSS
   - Ensures consistent design and behavior

3. **State Management**
   - Uses React Query for server state
   - Implements Zustand/Context for client state
   - Manages auth state and user context

4. **API Client**
   - Abstracts API communication
   - Handles error and loading states
   - Implements caching strategies

## Data Storage

CallSaver.app utilizes multiple data stores for different needs:

1. **PostgreSQL (Prisma)**
   - Primary relational database
   - Stores user data, phone numbers, configurations
   - Handles transactional data and relationships
   - Uses Row-Level Security (RLS) for tenant isolation

2. **MongoDB**
   - Stores flexible schema data like call logs, messages
   - Handles analytics and historical data

3. **Redis**
   - Powers the task queue system (BullMQ)
   - Provides caching for performance optimization
   - Manages rate limiting and distributed locks
   - Stores ephemeral data like session information

4. **Vector Database (Pinecone)**
   - Stores embeddings for AI knowledge retrieval
   - Enables semantic search capabilities

## Key Processing Flows

### Call Handling Flow

```
1. Incoming call received by Twilio
2. Twilio sends webhook to CallSaver API
3. CallSaver looks up number configuration
4. System applies matching automation rules
5. If AI enabled, connects to AI service
6. AI service processes call with OpenAI
7. Response sent back to Twilio (TwiML)
8. Call details recorded in database
9. Post-call processing queued in task queue
10. Workers process transcription, summary, sentiment
11. Call log updated with enhanced data
12. Notifications sent to user if configured
```

### SMS Handling Flow

```
1. Incoming SMS received by Twilio
2. Twilio sends webhook to CallSaver API
3. CallSaver looks up number configuration
4. System applies matching automation rules
5. If AI enabled, sends text to AI service
6. AI service processes text with OpenAI
7. Response generated and sent via Twilio
8. Message details recorded in database
9. Notifications sent to user if configured
```

### Number Purchase Flow

```
1. User requests available numbers
2. CallSaver queries Twilio for available numbers
3. User selects and purchases number
4. System checks credit balance
5. CallSaver purchases number from Twilio
6. Number assigned to user's account
7. Credits deducted from user's balance
8. Number configuration initialized
9. Dashboard updated with new number
```

## Multi-Tenant Architecture

CallSaver.app implements a robust multi-tenant architecture:

1. **Data Isolation**
   - Every tenant-specific table includes an `organizationId` column
   - All queries filter by organizationId
   - PostgreSQL Row-Level Security (RLS) provides an additional security layer

2. **Request Context**
   - Authentication middleware enriches requests with tenant context
   - API gateway validates tenant access
   - Background jobs maintain tenant context

3. **Resource Allocation**
   - Each tenant has separate resource quotas
   - Usage is tracked per tenant
   - Billing is isolated by tenant

## Security Architecture

1. **Authentication**
   - JWT-based authentication with refresh tokens
   - Token rotation on authentication state changes
   - MFA support for enhanced security

2. **Authorization**
   - Role-based access control (RBAC)
   - Permission verification middleware
   - API key management for programmatic access

3. **Data Protection**
   - Encryption at rest for sensitive data
   - TLS for all communications
   - Tenant isolation through RLS and application logic

4. **API Security**
   - Rate limiting to prevent abuse
   - CSRF protection for browser clients
   - Input validation and sanitization
   - Webhook signature verification

## Deployment Architecture

CallSaver.app is deployed using a hybrid approach:

1. **Backend Services (Railway)**
   - Node.js Express application
   - Horizontally scalable containers
   - Managed PostgreSQL database
   - Redis for caching and queues

2. **Frontend (Vercel)**
   - Next.js application
   - Edge-optimized CDN
   - Serverless functions for API routes

3. **Additional Services**
   - MongoDB Atlas for document storage
   - Pinecone for vector database
   - Supabase for authentication

```
┌───────────────────────────────────────────────────────────────────────┐
│                           Railway Platform                             │
├───────────────────────────────────────────────────────────────────────┤
│                                                                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐        │
│  │                 │  │                 │  │                 │        │
│  │  API Service    │  │  Worker Service │  │  Redis Service  │        │
│  │  (Node.js)      │  │  (Node.js)      │  │                 │        │
│  │                 │  │                 │  │                 │        │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘        │
│                                                                       │
│  ┌─────────────────┐                                                  │
│  │                 │                                                  │
│  │  PostgreSQL     │                                                  │
│  │  Database       │                                                  │
│  │                 │                                                  │
│  └─────────────────┘                                                  │
│                                                                       │
└───────────────────────────────────────────────────────────────────────┘

┌───────────────────────────────────────────────────────────────────────┐
│                           Vercel Platform                              │
├───────────────────────────────────────────────────────────────────────┤
│                                                                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐        │
│  │                 │  │                 │  │                 │        │
│  │  Next.js App    │  │  Edge Functions │  │  Static Assets  │        │
│  │                 │  │                 │  │                 │        │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘        │
│                                                                       │
└───────────────────────────────────────────────────────────────────────┘

┌───────────────────┐  ┌───────────────────┐  ┌───────────────────┐
│                   │  │                   │  │                   │
│  MongoDB Atlas    │  │  Pinecone Vector  │  │  Supabase Auth    │
│                   │  │  Database         │  │                   │
│                   │  │                   │  │                   │
└───────────────────┘  └───────────────────┘  └───────────────────┘
```

## Scalability Considerations

1. **Horizontal Scaling**
   - Stateless API services can scale horizontally
   - Worker processes scale independently based on queue depth
   - Database connection pooling handles concurrent connections

2. **Database Scaling**
   - Read replicas for high-read scenarios
   - Connection pooling with PgBouncer
   - Optimized indexes for common queries
   - Future sharding strategy based on tenant ID

3. **Caching Strategy**
   - Redis caches for frequent data
   - Response caching for common API responses
   - Next.js ISR for static or semi-dynamic content

## Monitoring and Observability

1. **Logging**
   - Structured JSON logging
   - Log aggregation in central system
   - Context-enriched logs with request IDs and tenant info

2. **Metrics**
   - Application performance metrics
   - Business metrics (calls, messages, etc.)
   - Infrastructure metrics (CPU, memory, etc.)

3. **Alerting**
   - Error rate thresholds
   - Performance degradation alerts
   - Business metric anomalies
   - Infrastructure health alerts

## Disaster Recovery

1. **Backup Strategy**
   - Daily PostgreSQL backups
   - Point-in-time recovery capability
   - MongoDB Atlas backups
   - Retention policies aligned with business needs

2. **Recovery Processes**
   - Documented restoration procedures
   - Regular restoration testing
   - Clear owner assignments for recovery scenarios

## Future Architecture Evolution

1. **Service Separation**
   - Move toward true microservices as scale demands
   - Extract high-load services first (AI processing, telephony)

2. **Serverless Expansion**
   - Migrate appropriate background tasks to serverless functions
   - Optimize for cost and scale

3. **Enhanced AI Capabilities**
   - Custom fine-tuned models for call/SMS handling
   - Enhanced RAG systems for domain-specific knowledge

4. **Geographic Expansion**
   - Multi-region deployment for lower latency
   - Regional data residency compliance

## Conclusion

The CallSaver.app architecture is designed for reliability, security, and scalability while maintaining operational simplicity. By leveraging modern cloud services and following best practices in multi-tenant SaaS design, the system provides a solid foundation for the current feature set while allowing for future growth and evolution.
