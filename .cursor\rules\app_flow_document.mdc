---
description: 
globs: 
alwaysApply: false
---
---
description: Comprehensive application flow and user journey documentation for CallSaver
globs: ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx"]
alwaysApply: true
version: 1.0.0
---

# CallSaver Application Flow Document

## Overview

This document defines the complete user journey and system interaction flow for the CallSaver platform, mapping out all key processes from user onboarding to call/SMS handling and analytics. It serves as the authoritative reference for understanding how data and processes flow through the system.

## 1. User Onboarding & Setup

### 1.1 Initial Registration Flow

1. **Landing Page Entry**
   - User discovers CallSaver through marketing channels
   - Frontend displays value proposition, features, and pricing tiers
   - Clear CTA guides users to sign-up or learn more

2. **Account Creation**
   - **Frontend**: Collects minimal required user data through streamlined form
   - **Backend**: 
     - Validates input through `validationMiddleware.js`
     - Creates user record in Supabase via `authService.js`
     - Generates secure JWT token with appropriate expiration
     - Creates initial user settings with sensible defaults

3. **Email Verification**
   - Automatic email dispatch through SendGrid/Mailgun
   - Tokenized verification link with secure expiration
   - Success redirect to onboarding wizard

### 1.2 Dashboard Access & Personalization

1. **Dashboard Initialization**
   - **Frontend**: 
     - Fetches personalized dashboard data via `/api/users/dashboard`
     - Loads appropriate UI components based on user subscription tier
     - Displays getting-started guidance for new users

   - **Backend**:
     - Authenticates request via `authMiddleware.js`
     - Aggregates user-specific data (numbers, usage, settings)
     - Returns structured response through consistent API format

2. **User Settings Configuration**
   - Profile settings (name, email, password, timezone)
   - Notification preferences (email, SMS, in-app)
   - Billing and subscription management options

### 1.3 Number Acquisition & Setup

1. **Number Selection Process**
   - **Legacy (Twilio)**:
     - Number search interface (country, area code, pattern)
     - Purchase confirmation with transparent pricing
     - Immediate provisioning through Twilio API
     - Direct webhook configuration

   - **eSIM Implementation**:
     - eSIM provider selection with comparison matrix
     - Region/package selection with clear pricing
     - QR code generation for profile activation
     - In-app guide for device setup process

2. **Number Configuration**
   - **Handling Rules Configuration**:
     - Forward to personal number
     - Enable AI assistant (voice parameters, prompt)
     - Enable AI SMS responder (response style, intent recognition)
     - Scheduled availability and operating hours
   
   - **Advanced Settings**:
     - Memory system configuration (Pinecone/vector DB setup)
     - Voice customization parameters (tone, accent, style)
     - Custom SMS response templates
     - Fallback handling logic

## 2. Incoming Communication Handling

### 2.1 Call Processing Workflow

1. **Call Initiation**
   - External caller dials user's assigned number
   - Initial routing through provider infrastructure:
     - Twilio: Webhook activation to CallSaver backend
     - eSIM: Data-channel routing to WebRTC endpoint

2. **Rule Application Logic**
   - Backend identifies associated user account
   - Retrieves configured handling rules from database
   - Applies rule-based decision tree:
     ```
     if (forwardingEnabled) {
       routeCallToUserPersonalNumber();
     } else if (aiAssistantEnabled) {
       initiateAIVoiceWorkflow();
     } else {
       applyDefaultHandling();
     }
     ```

3. **AI Voice Assistant Interaction**
   - **Context Retrieval**:
     - `vectorDbService.js` queries memory system using caller ID
     - Retrieves relevant conversation history and metadata
     - Assembles context package for AI model

   - **Voice Interaction**:
     - `aiService.js` sends context-enriched prompt to AI model
     - Processes response through TTS for natural voice output
     - Records caller audio for transcription and context storage
     - Manages turn-taking and conversation flow

   - **Escalation Handling**:
     - Recognizes complex queries beyond AI capability
     - Implements customized escalation paths based on user settings
     - Provides transition messaging to maintain positive experience

### 2.2 SMS Processing Workflow

1. **Incoming Message Reception**
   - SMS received through provider channels:
     - Twilio: Webhook activation with message payload
     - eSIM: Data-channel delivery to message processor

2. **Message Analysis and Response**
   - **Context Assembly**:
     - Retrieves conversation history for the sender
     - Analyzes message intent through NLP processing
     - Determines appropriate response type

   - **Response Generation**:
     - `aiService.js` generates contextually relevant response
     - Applies user-defined style and tone parameters
     - Formats message according to length constraints
     - Implements appropriate delay for natural conversation pacing

   - **Delivery Confirmation**:
     - Sends response through provider API
     - Logs communication in database with metadata
     - Updates vector store with new interaction data

### 2.3 Data Logging and Memory System

1. **Interaction Recording**
   - Logs call/SMS metadata (timestamp, duration, sender)
   - Stores full transcripts with speaker identification
   - Records outcome and action items from conversation
   - Calculates billable units for accurate accounting

2. **Vector-Based Memory System**
   - Generates semantic embeddings for key conversation elements
   - Stores vectors with relevant metadata for retrieval
   - Implements periodic consolidation of similar interactions
   - Provides relevance-based context for future interactions

## 3. User Analytics & Insights

### 3.1 Dashboard Analytics

1. **Usage Visualization**
   - Call/message volume trends with interactive charts
   - Duration and engagement metrics for voice interactions
   - Response time and quality metrics for SMS
   - Cost and resource utilization breakdowns

2. **Conversation Intelligence**
   - Topic modeling for common conversation themes
   - Sentiment analysis for caller satisfaction
   - Intent classification for understanding caller needs
   - Recommendation engine for handling rule optimization

### 3.2 Reporting System

1. **Scheduled Reports**
   - Weekly/monthly usage summaries via email
   - Anomaly detection for unusual patterns
   - Resource utilization and credit consumption reports
   - Cost optimization suggestions

2. **Export Capabilities**
   - CSV/JSON export of interaction history
   - PDF generation for detailed call logs
   - Integration options with external analytics tools
   - Compliance-ready data exports for business users

## 4. Billing & Subscription Management

### 4.1 Credit System

1. **Credit Allocation**
   - Tier-based credit allocation on subscription renewal
   - Transparent credit consumption for each interaction type
   - Low-balance alerts and notifications
   - Auto-renewal and top-up options

2. **Billing Cycle Management**
   - Prorated billing for mid-cycle changes
   - Usage-based overage charges
   - Subscription tier upgrades/downgrades
   - Corporate billing options with consolidated invoicing

## 5. System Interaction Diagrams

### 5.1 User Onboarding & Setup Flowchart

```mermaid
graph LR
    subgraph "User Onboarding & Setup"
        A[User Visits Landing Page] --> B{Sign Up / Log In};
        B -- Credentials --> C[Backend: Authenticate/Create User (PostgreSQL via Prisma)];
        C -- Session/Token --> D[User Accesses Dashboard];
        D --> E{Purchase/Assign Number?};
        E -- Yes --> F[Frontend: Number Selection/Payment (Stripe?)] ;
        F --> G[Backend: Interact w/ Provider API & Save to PostgreSQL];
        G --> H[User Configures Call Handling Rules];
        E -- No --> H;
        H --> I[Backend: Save Config to PostgreSQL (Incl. Optional Pinecone Setup)];
    end

    classDef db fill:#f9f,stroke:#333,stroke-width:2px;
    classDef api fill:#ccf,stroke:#333,stroke-width:2px;
    classDef user fill:#9cf,stroke:#333,stroke-width:2px;

    class C,G,I db;
    class F api;
    class A,B,D,E,H user;
```

### 5.2 Incoming Call/SMS Handling Flowchart

```mermaid
graph TD
    subgraph "Incoming Call/SMS Handling"
        J[External Caller Contacts Number] --> K[Provider Receives Call/SMS];
        K --> L[Provider Sends Webhook/API Call to Backend];
        L --> M[Backend: Identify User & Get Rules from PostgreSQL];
        M --> N{Handling Rule?};
        N -- Forward Call --> O[Backend: Instruct Provider (TwiML/API) to Forward];
        N -- AI Voice --> P[Backend: Query Pinecone (Optional Vector Search)];
        P --> Q[Backend: Call AI Model API (e.g., OpenAI/Gemini)];
        Q --> R[Backend: Instruct Provider (TwiML/API) w/ AI Voice Response];
        N -- AI SMS --> S[Backend: Query Pinecone (Optional Vector Search)];
        S --> T[Backend: Call AI Model API (e.g., OpenAI/Gemini)];
        T --> U[Backend: Instruct Provider API to Send AI SMS];
        O --> V[Backend: Log Forwarding Action in PostgreSQL];
        R --> W[Backend: Log Full Conversation Metadata in PostgreSQL];
        U --> W;
        W --> X{Vector Memory Enabled?};
        X -- Yes --> Y[Backend: Generate Embeddings & Upsert to Pinecone];
        X -- No --> Z[End Flow];
        V --> Z;
        Y --> Z;
    end

    classDef db fill:#f9f,stroke:#333,stroke-width:2px;
    classDef api fill:#ccf,stroke:#333,stroke-width:2px;
    classDef external fill:#ccc,stroke:#333,stroke-width:2px;
    classDef logic fill:#ff9,stroke:#333,stroke-width:1px;

    class M,O,P,Q,R,S,T,U,V,W,X,Y db;
    class K,L api;
    class J external;
    class N logic;
```

### 5.3 Log Retrieval & Context Display Flowchart

```mermaid
graph LR
    subgraph "Log Retrieval & Context Display"
        AA[User Navigates to Logs in Dashboard] --> BB[Frontend: Request Logs API];
        BB --> CC[Backend: Query PostgreSQL (via Prisma) for Logs];
        CC --> DD[Frontend: Display Logs];
        DD --> EE{View Contextual History?};
        EE -- Yes --> FF[Frontend: Request Context API];
        FF --> GG[Backend: Query Pinecone (Vector Search)];
        GG --> HH[Frontend: Display Contextual History];
        EE -- No --> II[End Flow];
        HH --> II;
    end

    classDef db fill:#f9f,stroke:#333,stroke-width:2px;
    classDef user fill:#9cf,stroke:#333,stroke-width:2px;

    class CC,GG db;
    class AA,BB,DD,EE,FF,HH user;
```

## 6. Version History

| Version | Date | Description |
|---------|------|-------------|
| 1.0.0 | 2025-04-13 | Initial comprehensive flow document |

## 7. Related Documents

- [Backend Structure Document](mdc:.cursor/rules/backend_structure_document.mdc)
- [Frontend Guidelines Document](mdc:.cursor/rules/frontend_guidelines_document.mdc)
- [Implementation Plan](mdc:.cursor/rules/implementation_plan.mdc)
