'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  PaymentElement,
  useStripe,
  useElements,
} from '@stripe/react-stripe-js';

// Initialize Stripe with the publishable key from environment variables
const stripePromise = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
  ? loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY)
  : null;

// Log error if Stripe key is missing
if (!process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
  console.error("Stripe publishable key not configured in frontend environment variables (NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY)");
}

// Main Billing Page Component
export default function BillingPage() {
  const [subscription, setSubscription] = useState(null);
  const [invoices, setInvoices] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState(null);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [clientSecret, setClientSecret] = useState('');
  const [selectedPlan, setSelectedPlan] = useState(null);

  // Fetch subscription data
  useEffect(() => {
    const fetchSubscriptionData = async () => {
      try {
        setIsLoading(true);
        // Replace with actual API endpoint when backend is ready
        const response = await fetch('/api/billing/subscription');
        
        if (!response.ok) {
          throw new Error('Failed to fetch subscription data');
        }
        
        const data = await response.json();
        setSubscription(data);
      } catch (err) {
        console.error('Error fetching subscription data:', err);
        setError('Failed to load subscription information. Please try again later.');
        // Use mock data for now
        setSubscription(mockSubscription);
      } finally {
        setIsLoading(false);
      }
    };

    // Fetch invoice history
    const fetchInvoiceHistory = async () => {
      try {
        // Replace with actual API endpoint when backend is ready
        const response = await fetch('/api/billing/invoices');
        
        if (!response.ok) {
          throw new Error('Failed to fetch invoice history');
        }
        
        const data = await response.json();
        setInvoices(data);
      } catch (err) {
        console.error('Error fetching invoice history:', err);
        // Use mock data
        setInvoices(mockInvoices);
      }
    };

    fetchSubscriptionData();
    fetchInvoiceHistory();
  }, []);

  // Handle plan selection and upgrade using Stripe Checkout
  const handleSelectPlan = async (plan) => {
    if (!plan || !plan.priceId) {
      setError('Invalid plan selection. Please try again.');
      return;
    }
    
    setSelectedPlan(plan);
    setIsProcessing(true);
    setError(null);
    
    try {
      // Create Checkout Session via API
      const response = await fetch('/api/billing/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId: plan.priceId
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create checkout session');
      }
      
      const { sessionId } = await response.json();
      
      // Redirect to Stripe Checkout
      if (!stripePromise) {
        throw new Error('Stripe has not been properly initialized');
      }
      
      const stripe = await stripePromise;
      const { error } = await stripe.redirectToCheckout({ sessionId });
      
      if (error) {
        throw new Error(error.message);
      }
    } catch (err) {
      console.error('Error redirecting to checkout:', err);
      setError(`Failed to proceed to checkout: ${err.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle cancellation
  const handleCancelSubscription = async () => {
    if (!subscription) return;
    
    if (!confirm('Are you sure you want to cancel your subscription? This will take effect at the end of your current billing period.')) {
      return;
    }
    
    try {
      setIsLoading(true);
      
      // Replace with actual API endpoint when backend is ready
      const response = await fetch('/api/billing/cancel-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId: subscription.id
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to cancel subscription');
      }
      
      const data = await response.json();
      
      // Update subscription status
      setSubscription({
        ...subscription,
        status: 'canceled',
        cancelAtPeriodEnd: true,
        currentPeriodEnd: subscription.currentPeriodEnd
      });
      
    } catch (err) {
      console.error('Error canceling subscription:', err);
      setError('Failed to cancel subscription. Please try again later.');
      
      // For demo purposes, update UI optimistically
      setSubscription({
        ...subscription,
        status: 'canceled',
        cancelAtPeriodEnd: true
      });
      
    } finally {
      setIsLoading(false);
    }
  };

  // Handle resuming a canceled subscription
  const handleResumeSubscription = async () => {
    if (!subscription) return;
    
    try {
      setIsLoading(true);
      
      // Replace with actual API endpoint when backend is ready
      const response = await fetch('/api/billing/resume-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscriptionId: subscription.id
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to resume subscription');
      }
      
      const data = await response.json();
      
      // Update subscription status
      setSubscription({
        ...subscription,
        status: 'active',
        cancelAtPeriodEnd: false
      });
      
    } catch (err) {
      console.error('Error resuming subscription:', err);
      setError('Failed to resume subscription. Please try again later.');
      
      // For demo purposes, update UI optimistically
      setSubscription({
        ...subscription,
        status: 'active',
        cancelAtPeriodEnd: false
      });
      
    } finally {
      setIsLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-white">Billing & Subscription</h1>
        </div>

        {error && (
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3 text-red-200">
            {error}
          </div>
        )}

        {/* Current Subscription */}
        <div className="bg-gray-800/50 rounded-xl border border-purple-500/20 p-6">
          <h2 className="text-lg font-semibold text-white mb-4">Current Plan</h2>
          
          {isLoading && !subscription ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
            </div>
          ) : subscription ? (
            <div>
              <div className="flex flex-col md:flex-row md:items-center justify-between">
                <div>
                  <h3 className="text-xl font-bold text-white">{subscription.plan.name}</h3>
                  <p className="text-gray-400 mt-1">{subscription.plan.description}</p>
                  
                  <div className="mt-4 space-y-2">
                    <div className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>{subscription.plan.features.calls} AI-powered calls per month</span>
                    </div>
                    <div className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>{subscription.plan.features.sms} SMS responses per month</span>
                    </div>
                    <div className="flex items-center text-gray-300">
                      <svg className="w-5 h-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>{subscription.plan.features.numbers} phone numbers included</span>
                    </div>
                    {subscription.plan.features.analytics && (
                      <div className="flex items-center text-gray-300">
                        <svg className="w-5 h-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span>Advanced analytics</span>
                      </div>
                    )}
                    {subscription.plan.features.customization && (
                      <div className="flex items-center text-gray-300">
                        <svg className="w-5 h-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span>Custom AI training</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="mt-6 md:mt-0 text-right">
                  <div className="text-3xl font-bold text-white">${subscription.plan.price}<span className="text-lg text-gray-400">/mo</span></div>
                  
                  <div className="mt-2 text-sm text-gray-400">
                    {subscription.status === 'active' ? (
                      subscription.cancelAtPeriodEnd ? (
                        <span className="text-yellow-400">
                          Cancels on {formatDate(subscription.currentPeriodEnd)}
                        </span>
                      ) : (
                        <span>
                          Next billing date: {formatDate(subscription.currentPeriodEnd)}
                        </span>
                      )
                    ) : (
                      <span className="text-red-400">
                        {subscription.status === 'canceled' ? 'Canceled' : 'Inactive'}
                      </span>
                    )}
                  </div>
                  
                  <div className="mt-4">
                    {subscription.status === 'active' ? (
                      subscription.cancelAtPeriodEnd ? (
                        <button
                          onClick={handleResumeSubscription}
                          className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                          disabled={isLoading}
                        >
                          Resume Subscription
                        </button>
                      ) : (
                        <button
                          onClick={handleCancelSubscription}
                          className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                          disabled={isLoading}
                        >
                          Cancel Subscription
                        </button>
                      )
                    ) : (
                      <button
                        onClick={() => handleSelectPlan(availablePlans[1])} // Default to Pro plan
                        className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                        disabled={isLoading}
                      >
                        Reactivate Subscription
                      </button>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="mt-6 pt-6 border-t border-gray-700">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold text-white">Usage This Month</h3>
                  <span className="text-sm text-gray-400">Billing period: {formatDate(subscription.currentPeriodStart)} - {formatDate(subscription.currentPeriodEnd)}</span>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-4">
                  <div className="bg-gray-700/30 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-300">AI Calls</span>
                      <span className="text-sm text-gray-400">{subscription.usage.calls} / {subscription.plan.features.calls}</span>
                    </div>
                    <div className="w-full bg-gray-600 rounded-full h-2.5">
                      <div 
                        className="bg-purple-600 h-2.5 rounded-full" 
                        style={{ width: `${Math.min(100, (subscription.usage.calls / subscription.plan.features.calls) * 100)}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="bg-gray-700/30 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-300">SMS Responses</span>
                      <span className="text-sm text-gray-400">{subscription.usage.sms} / {subscription.plan.features.sms}</span>
                    </div>
                    <div className="w-full bg-gray-600 rounded-full h-2.5">
                      <div 
                        className="bg-purple-600 h-2.5 rounded-full" 
                        style={{ width: `${Math.min(100, (subscription.usage.sms / subscription.plan.features.sms) * 100)}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="bg-gray-700/30 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-gray-300">Phone Numbers</span>
                      <span className="text-sm text-gray-400">{subscription.usage.numbers} / {subscription.plan.features.numbers}</span>
                    </div>
                    <div className="w-full bg-gray-600 rounded-full h-2.5">
                      <div 
                        className="bg-purple-600 h-2.5 rounded-full" 
                        style={{ width: `${Math.min(100, (subscription.usage.numbers / subscription.plan.features.numbers) * 100)}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-400 mb-4">You don't have an active subscription.</p>
              <button
                onClick={() => handleSelectPlan(availablePlans[0])}
                className="px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
              >
                Choose a Plan
              </button>
            </div>
          )}
        </div>

        {/* Available Plans */}
        <div className="bg-gray-800/50 rounded-xl border border-purple-500/20 p-6">
          <h2 className="text-lg font-semibold text-white mb-6">Available Plans</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {availablePlans.map((plan) => (
              <div 
                key={plan.id}
                className={`rounded-xl border p-6 ${
                  subscription?.plan.id === plan.id
                    ? 'bg-purple-900/30 border-purple-500'
                    : 'bg-gray-700/30 border-gray-600 hover:border-purple-500/50'
                } transition-colors`}
              >
                <h3 className="text-xl font-bold text-white">{plan.name}</h3>
                <p className="text-gray-400 mt-1 min-h-[50px]">{plan.description}</p>
                
                <div className="my-4">
                  <span className="text-3xl font-bold text-white">${plan.price}</span>
                  <span className="text-gray-400">/month</span>
                </div>
                
                <ul className="space-y-2 mb-6">
                  <li className="flex items-center text-gray-300">
                    <svg className="w-5 h-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>{plan.features.calls} AI-powered calls</span>
                  </li>
                  <li className="flex items-center text-gray-300">
                    <svg className="w-5 h-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>{plan.features.sms} SMS responses</span>
                  </li>
                  <li className="flex items-center text-gray-300">
                    <svg className="w-5 h-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>{plan.features.numbers} phone numbers</span>
                  </li>
                  <li className="flex items-center text-gray-300">
                    {plan.features.analytics ? (
                      <>
                        <svg className="w-5 h-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span>Advanced analytics</span>
                      </>
                    ) : (
                      <>
                        <svg className="w-5 h-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        <span className="text-gray-500">Advanced analytics</span>
                      </>
                    )}
                  </li>
                  <li className="flex items-center text-gray-300">
                    {plan.features.customization ? (
                      <>
                        <svg className="w-5 h-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span>Custom AI training</span>
                      </>
                    ) : (
                      <>
                        <svg className="w-5 h-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        <span className="text-gray-500">Custom AI training</span>
                      </>
                    )}
                  </li>
                </ul>
                
                <button
                  onClick={() => handleSelectPlan(plan)}
                  className={`w-full py-2 rounded-lg transition-colors ${
                    subscription?.plan.id === plan.id
                      ? 'bg-gray-600 text-gray-300 cursor-default'
                      : 'bg-purple-600 hover:bg-purple-700 text-white'
                  }`}
                  disabled={subscription?.plan.id === plan.id || isLoading}
                >
                  {subscription?.plan.id === plan.id ? 'Current Plan' : 'Select Plan'}
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Billing History */}
        <div className="bg-gray-800/50 rounded-xl border border-purple-500/20 p-6">
          <h2 className="text-lg font-semibold text-white mb-4">Billing History</h2>
          
          {invoices.length === 0 ? (
            <p className="text-gray-400 text-center py-4">No billing history available.</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left text-gray-400 border-b border-gray-700">
                    <th className="pb-2">Date</th>
                    <th className="pb-2">Description</th>
                    <th className="pb-2">Amount</th>
                    <th className="pb-2">Status</th>
                    <th className="pb-2">Invoice</th>
                  </tr>
                </thead>
                <tbody>
                  {invoices.map((invoice) => (
                    <tr key={invoice.id} className="border-b border-gray-700 text-gray-300">
                      <td className="py-3">{formatDate(invoice.date)}</td>
                      <td className="py-3">{invoice.description}</td>
                      <td className="py-3">${invoice.amount.toFixed(2)}</td>
                      <td className="py-3">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          invoice.status === 'paid' ? 'bg-green-900/30 text-green-400' :
                          invoice.status === 'pending' ? 'bg-yellow-900/30 text-yellow-400' :
                          'bg-red-900/30 text-red-400'
                        }`}>
                          {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                        </span>
                      </td>
                      <td className="py-3">
                        <a 
                          href={invoice.invoiceUrl} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-purple-400 hover:text-purple-300 transition-colors"
                        >
                          View
                        </a>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Upgrade/Payment Modal */}
      {showUpgradeModal && clientSecret && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-gray-800 rounded-xl border border-purple-500/30 p-6 max-w-md w-full mx-4"
          >
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-white">
                Upgrade to {selectedPlan?.name}
              </h2>
              <button
                onClick={() => setShowUpgradeModal(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="mb-6">
              <div className="flex justify-between text-gray-300 mb-2">
                <span>Plan:</span>
                <span>{selectedPlan?.name}</span>
              </div>
              <div className="flex justify-between text-gray-300 mb-2">
                <span>Price:</span>
                <span>${selectedPlan?.price}/month</span>
              </div>
              <div className="flex justify-between text-gray-300 mb-4">
                <span>Billing cycle:</span>
                <span>Monthly</span>
              </div>
              
              <div className="border-t border-gray-700 pt-4 mb-4">
                <div className="flex justify-between text-white font-semibold">
                  <span>Total due today:</span>
                  <span>${selectedPlan?.price}</span>
                </div>
              </div>
            </div>
            
            <Elements stripe={stripePromise} options={{ clientSecret }}>
              <CheckoutForm 
                onClose={() => setShowUpgradeModal(false)}
                onSuccess={(newSubscription) => {
                  setShowUpgradeModal(false);
                  setSubscription(newSubscription);
                }}
                planName={selectedPlan?.name}
              />
            </Elements>
          </motion.div>
        </div>
      )}
    </div>
  );
}

// Checkout Form Component (used inside Elements provider)
function CheckoutForm({ onClose, onSuccess, planName }) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!stripe || !elements) {
      // Stripe.js has not yet loaded.
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/dashboard/billing/success`,
        },
        redirect: 'if_required',
      });

      if (error) {
        setErrorMessage(error.message);
      } else if (paymentIntent && paymentIntent.status === 'succeeded') {
        // Payment succeeded, create subscription on backend
        try {
          // Replace with actual API endpoint when backend is ready
          const response = await fetch('/api/billing/create-subscription', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              paymentIntentId: paymentIntent.id,
              planName: planName
            }),
          });
          
          if (!response.ok) {
            throw new Error('Failed to create subscription');
          }
          
          const subscription = await response.json();
          onSuccess(subscription);
          
        } catch (err) {
          console.error('Error creating subscription:', err);
          setErrorMessage('Payment succeeded but we could not activate your subscription. Please contact support.');
          
          // For demo purposes, simulate success
          onSuccess({
            id: 'sub_mock',
            status: 'active',
            currentPeriodStart: new Date().toISOString(),
            currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            cancelAtPeriodEnd: false,
            plan: availablePlans.find(p => p.name === planName) || availablePlans[1],
            usage: { calls: 0, sms: 0, numbers: 0 }
          });
        }
      }
    } catch (err) {
      console.error('Error processing payment:', err);
      setErrorMessage('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <PaymentElement />
      
      {errorMessage && (
        <div className="mt-4 p-3 bg-red-900/30 border border-red-500/30 rounded-lg text-red-300 text-sm">
          {errorMessage}
        </div>
      )}
      
      <div className="flex justify-end space-x-3 mt-6">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center"
          disabled={!stripe || isLoading}
        >
          {isLoading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </>
          ) : (
            'Pay & Subscribe'
          )}
        </button>
      </div>
    </form>
  );
}

// Define available plans with Stripe Price IDs
const availablePlans = [
  {
    id: 'basic',
    name: 'Basic',
    priceId: 'price_1PlP7tE8oISCvrMNkUgKQPEN', // Stripe Price ID for Basic plan
    price: 29,
    per: 'month',
    description: 'Great for individuals and small businesses',
    features: {
      calls: 30,
      sms: 100,
      users: 1,
      storage: '10GB',
      support: 'Email'
    },
    popular: false
  },
  {
    id: 'pro',
    name: 'Professional',
    priceId: 'price_1PlP8IE8oISCvrMNxPttZMdY', // Stripe Price ID for Pro plan
    price: 79,
    per: 'month',
    description: 'Perfect for growing businesses',
    features: {
      calls: 100,
      sms: 500,
      users: 5,
      storage: '50GB',
      support: 'Priority Email'
    },
    popular: true
  },
  {
    id: 'business',
    name: 'Business',
    priceId: 'price_1PlP8rE8oISCvrMNVN6LfS85', // Stripe Price ID for Business plan
    price: 199,
    per: 'month',
    description: 'For enterprises with advanced needs',
    features: {
      calls: 'Unlimited',
      sms: 5000,
      users: 'Unlimited',
      storage: '250GB',
      support: '24/7 Phone & Email'
    },
    popular: false
  }
];

const mockSubscription = {
  id: 'sub_123456',
  status: 'active',
  currentPeriodStart: '2025-03-01T00:00:00Z',
  currentPeriodEnd: '2025-04-01T00:00:00Z',
  cancelAtPeriodEnd: false,
  plan: availablePlans[1], // Pro plan
  usage: {
    calls: 217,
    sms: 432,
    numbers: 2
  }
};

const mockInvoices = [
  {
    id: 'in_123456',
    date: '2025-03-01T00:00:00Z',
    description: 'Professional Plan - Monthly',
    amount: 79.00,
    status: 'paid',
    invoiceUrl: '#'
  },
  {
    id: 'in_123455',
    date: '2025-02-01T00:00:00Z',
    description: 'Professional Plan - Monthly',
    amount: 79.00,
    status: 'paid',
    invoiceUrl: '#'
  },
  {
    id: 'in_123454',
    date: '2025-01-01T00:00:00Z',
    description: 'Starter Plan - Monthly',
    amount: 29.00,
    status: 'paid',
    invoiceUrl: '#'
  }
];
