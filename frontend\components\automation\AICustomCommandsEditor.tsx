'use client';

import { useState } from 'react';
import { AIAssistantConfig } from './AutomationConfigPanel';
import { PlusIcon, PencilIcon, TrashIcon, CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { Switch } from '@headlessui/react';

interface AICustomCommandsEditorProps {
  commands: AIAssistantConfig['commands'];
  onUpdate: (commands: AIAssistantConfig['commands']) => void;
  disabled?: boolean;
}

export default function AICustomCommandsEditor({
  commands,
  onUpdate,
  disabled = false,
}: AICustomCommandsEditorProps) {
  // State for editing
  const [editingId, setEditingId] = useState<string | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [newCommand, setNewCommand] = useState({
    triggerPhrase: '',
    action: '',
    enabled: true,
  });
  const [editCommand, setEditCommand] = useState({
    id: '',
    triggerPhrase: '',
    action: '',
    enabled: true,
  });

  // Handle add new command
  const handleAddCommand = () => {
    if (!newCommand.triggerPhrase.trim() || !newCommand.action.trim()) return;
    
    const newCommandWithId = {
      ...newCommand,
      id: `${Date.now()}`,
    };
    
    onUpdate([...commands, newCommandWithId]);
    setNewCommand({ triggerPhrase: '', action: '', enabled: true });
    setIsAdding(false);
  };

  // Handle edit command
  const handleEditCommand = () => {
    if (!editCommand.triggerPhrase.trim() || !editCommand.action.trim()) return;
    
    const updatedCommands = commands.map(command => 
      command.id === editCommand.id ? editCommand : command
    );
    
    onUpdate(updatedCommands);
    setEditingId(null);
  };

  // Handle delete command
  const handleDeleteCommand = (id: string) => {
    const updatedCommands = commands.filter(command => command.id !== id);
    onUpdate(updatedCommands);
  };

  // Handle toggle command
  const handleToggleCommand = (id: string, enabled: boolean) => {
    const updatedCommands = commands.map(command => 
      command.id === id ? { ...command, enabled } : command
    );
    
    onUpdate(updatedCommands);
  };

  // Start editing a command
  const startEditing = (command: AIAssistantConfig['commands'][0]) => {
    setEditCommand(command);
    setEditingId(command.id);
  };

  return (
    <div className="space-y-6">
      {/* Commands List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50 flex justify-between items-center">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Custom Commands
          </h3>
          <button
            type="button"
            onClick={() => setIsAdding(true)}
            disabled={disabled || isAdding}
            className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-900/50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <PlusIcon className="h-3 w-3 mr-1" />
            Add Command
          </button>
        </div>
        
        {/* Add New Command Form */}
        {isAdding && (
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-blue-50 dark:bg-blue-900/20">
            <div className="space-y-3">
              <div>
                <label htmlFor="newTriggerPhrase" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Trigger Phrase
                </label>
                <input
                  type="text"
                  id="newTriggerPhrase"
                  value={newCommand.triggerPhrase}
                  onChange={(e) => setNewCommand({ ...newCommand, triggerPhrase: e.target.value })}
                  placeholder="e.g., schedule appointment"
                  className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  disabled={disabled}
                />
              </div>
              <div>
                <label htmlFor="newAction" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Action
                </label>
                <input
                  type="text"
                  id="newAction"
                  value={newCommand.action}
                  onChange={(e) => setNewCommand({ ...newCommand, action: e.target.value })}
                  placeholder="e.g., Create calendar event"
                  className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  disabled={disabled}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  type="button"
                  onClick={() => setIsAdding(false)}
                  className="inline-flex items-center px-3 py-1 border border-gray-300 dark:border-gray-600 text-xs font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleAddCommand}
                  disabled={!newCommand.triggerPhrase.trim() || !newCommand.action.trim() || disabled}
                  className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Add
                </button>
              </div>
            </div>
          </div>
        )}
        
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {commands.length === 0 && !isAdding ? (
            <li className="px-4 py-4 text-sm text-gray-500 dark:text-gray-400 text-center">
              No custom commands defined yet.
            </li>
          ) : (
            commands.map((command) => (
              <li key={command.id} className="px-4 py-3">
                {editingId === command.id ? (
                  // Edit Mode
                  <div className="space-y-3">
                    <div>
                      <label htmlFor={`edit-trigger-${command.id}`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Trigger Phrase
                      </label>
                      <input
                        type="text"
                        id={`edit-trigger-${command.id}`}
                        value={editCommand.triggerPhrase}
                        onChange={(e) => setEditCommand({ ...editCommand, triggerPhrase: e.target.value })}
                        className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        disabled={disabled}
                      />
                    </div>
                    <div>
                      <label htmlFor={`edit-action-${command.id}`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Action
                      </label>
                      <input
                        type="text"
                        id={`edit-action-${command.id}`}
                        value={editCommand.action}
                        onChange={(e) => setEditCommand({ ...editCommand, action: e.target.value })}
                        className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        disabled={disabled}
                      />
                    </div>
                    <div className="flex justify-end space-x-2">
                      <button
                        type="button"
                        onClick={() => setEditingId(null)}
                        className="inline-flex items-center p-1 border border-gray-300 dark:border-gray-600 text-xs font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                      <button
                        type="button"
                        onClick={handleEditCommand}
                        disabled={!editCommand.triggerPhrase.trim() || !editCommand.action.trim() || disabled}
                        className="inline-flex items-center p-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <CheckIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ) : (
                  // View Mode
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {command.triggerPhrase}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {command.action}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={command.enabled}
                        onChange={(enabled) => handleToggleCommand(command.id, enabled)}
                        disabled={disabled}
                        className={`${
                          command.enabled ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
                        } relative inline-flex h-5 w-10 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${
                          disabled ? 'opacity-50 cursor-not-allowed' : ''
                        }`}
                      >
                        <span
                          className={`${
                            command.enabled ? 'translate-x-5' : 'translate-x-1'
                          } inline-block h-3 w-3 transform rounded-full bg-white transition-transform`}
                        />
                      </Switch>
                      <button
                        type="button"
                        onClick={() => startEditing(command)}
                        disabled={disabled}
                        className="text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        type="button"
                        onClick={() => handleDeleteCommand(command.id)}
                        disabled={disabled}
                        className="text-gray-400 hover:text-red-500 dark:hover:text-red-400 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                )}
              </li>
            ))
          )}
        </ul>
      </div>
      
      <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-md">
        <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-300 mb-1">
          About Custom Commands
        </h3>
        <p className="text-sm text-yellow-700 dark:text-yellow-200">
          Custom commands allow your AI assistant to perform specific actions when it recognizes certain phrases during calls or in messages. For example, you can create a command to schedule appointments or check order status.
        </p>
      </div>
    </div>
  );
}
