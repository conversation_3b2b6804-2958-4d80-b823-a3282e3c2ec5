"use client";

import React from 'react';
import { QueryProvider } from './QueryProvider';
import { WebSocketProvider } from './WebSocketProvider';

interface AppProvidersProps {
  children: React.ReactNode;
}

/**
 * AppProviders
 * Combines all application providers in the correct order
 */
export function AppProviders({ children }: AppProvidersProps) {
  return (
    <QueryProvider>
      <WebSocketProvider>
        {children}
      </WebSocketProvider>
    </QueryProvider>
  );
}
