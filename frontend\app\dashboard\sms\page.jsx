'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

export default function SMSPage() {
  const [smsThreads, setSmsThreads] = useState([]);
  const [selectedThread, setSelectedThread] = useState(null);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [autoResponses, setAutoResponses] = useState({
    enabled: true,
    templates: [
      { id: 1, name: 'Default Response', content: 'Thank you for your message. We\'ll get back to you as soon as possible.' },
      { id: 2, name: 'Appointment Booking', content: 'Thanks for your interest in booking an appointment. Please let me know what day and time works best for you.' },
      { id: 3, name: 'Business Hours', content: 'Thank you for your message. Our business hours are Monday-Friday 9am-5pm. We\'ll respond during business hours.' }
    ],
    activeTemplateId: 1
  });

  // Fetch SMS threads from API
  useEffect(() => {
    const fetchSMSThreads = async () => {
      try {
        setIsLoading(true);
        // Replace with actual API endpoint when backend is ready
        const response = await fetch('/api/sms/threads');
        
        if (!response.ok) {
          throw new Error('Failed to fetch SMS threads');
        }
        
        const data = await response.json();
        setSmsThreads(data);
      } catch (err) {
        console.error('Error fetching SMS threads:', err);
        setError('Failed to load SMS conversations. Please try again later.');
        // Use mock data for now
        setSmsThreads(mockSMSThreads);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSMSThreads();
  }, []);

  // Handle sending a new message
  const handleSendMessage = async (e) => {
    e.preventDefault();
    
    if (!newMessage.trim() || !selectedThread) return;
    
    // Optimistically update UI
    const updatedThreads = smsThreads.map(thread => {
      if (thread.id === selectedThread.id) {
        return {
          ...thread,
          messages: [
            ...thread.messages,
            {
              id: `temp-${Date.now()}`,
              content: newMessage,
              timestamp: new Date().toISOString(),
              sender: 'business',
              status: 'sending'
            }
          ]
        };
      }
      return thread;
    });
    
    setSmsThreads(updatedThreads);
    setSelectedThread(updatedThreads.find(t => t.id === selectedThread.id));
    setNewMessage('');
    
    try {
      // Replace with actual API endpoint when backend is ready
      const response = await fetch('/api/sms/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          threadId: selectedThread.id,
          phoneNumber: selectedThread.phoneNumber,
          message: newMessage
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to send message');
      }
      
      // Update message status to sent
      const finalThreads = updatedThreads.map(thread => {
        if (thread.id === selectedThread.id) {
          return {
            ...thread,
            messages: thread.messages.map(msg => {
              if (msg.status === 'sending') {
                return { ...msg, status: 'sent' };
              }
              return msg;
            })
          };
        }
        return thread;
      });
      
      setSmsThreads(finalThreads);
      setSelectedThread(finalThreads.find(t => t.id === selectedThread.id));
      
    } catch (err) {
      console.error('Error sending message:', err);
      // Show error in UI
      const errorThreads = smsThreads.map(thread => {
        if (thread.id === selectedThread.id) {
          return {
            ...thread,
            messages: thread.messages.map(msg => {
              if (msg.status === 'sending') {
                return { ...msg, status: 'failed' };
              }
              return msg;
            })
          };
        }
        return thread;
      });
      
      setSmsThreads(errorThreads);
      setSelectedThread(errorThreads.find(t => t.id === selectedThread.id));
      setError('Failed to send message. Please try again.');
    }
  };

  // Toggle auto-response setting
  const toggleAutoResponse = () => {
    setAutoResponses({
      ...autoResponses,
      enabled: !autoResponses.enabled
    });
  };

  // Change active template
  const changeActiveTemplate = (templateId) => {
    setAutoResponses({
      ...autoResponses,
      activeTemplateId: templateId
    });
  };

  // Save template changes
  const saveTemplate = (templateId, content) => {
    setAutoResponses({
      ...autoResponses,
      templates: autoResponses.templates.map(template => 
        template.id === templateId ? { ...template, content } : template
      )
    });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-white">SMS Responder</h1>
          <div className="flex items-center space-x-2">
            <span className="text-gray-300">Auto-Response:</span>
            <button 
              onClick={toggleAutoResponse}
              className={`relative inline-flex h-6 w-11 items-center rounded-full ${autoResponses.enabled ? 'bg-purple-600' : 'bg-gray-700'}`}
            >
              <span 
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${autoResponses.enabled ? 'translate-x-6' : 'translate-x-1'}`} 
              />
            </button>
          </div>
        </div>

        {error && (
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3 text-red-200">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
          {/* SMS Threads List */}
          <div className="bg-gray-800/50 rounded-xl border border-purple-500/20 overflow-hidden">
            <div className="p-4 border-b border-gray-700">
              <h2 className="text-lg font-semibold text-white">Conversations</h2>
            </div>
            <div className="overflow-y-auto h-[calc(100%-60px)]">
              {isLoading ? (
                <div className="flex justify-center items-center h-full">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
                </div>
              ) : smsThreads.length === 0 ? (
                <div className="text-center p-6 text-gray-400">
                  No SMS conversations yet
                </div>
              ) : (
                <ul>
                  {smsThreads.map(thread => (
                    <li 
                      key={thread.id}
                      className={`p-4 border-b border-gray-700 hover:bg-gray-700/50 cursor-pointer transition-colors ${selectedThread?.id === thread.id ? 'bg-gray-700/70' : ''}`}
                      onClick={() => setSelectedThread(thread)}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium text-white">{thread.contactName || thread.phoneNumber}</h3>
                          <p className="text-sm text-gray-400 truncate">
                            {thread.messages[thread.messages.length - 1]?.content}
                          </p>
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(thread.messages[thread.messages.length - 1]?.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>

          {/* Conversation View */}
          <div className="bg-gray-800/50 rounded-xl border border-purple-500/20 overflow-hidden md:col-span-2">
            {selectedThread ? (
              <>
                <div className="p-4 border-b border-gray-700">
                  <h2 className="text-lg font-semibold text-white">
                    {selectedThread.contactName || selectedThread.phoneNumber}
                  </h2>
                </div>
                <div className="flex flex-col h-[calc(100%-120px)]">
                  <div className="flex-1 overflow-y-auto p-4 space-y-4">
                    {selectedThread.messages.map(message => (
                      <div 
                        key={message.id}
                        className={`flex ${message.sender === 'business' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div 
                          className={`max-w-[70%] rounded-lg px-4 py-2 ${
                            message.sender === 'business' 
                              ? 'bg-purple-600 text-white' 
                              : 'bg-gray-700 text-white'
                          }`}
                        >
                          <p>{message.content}</p>
                          <div className="flex justify-end items-center mt-1 space-x-1">
                            <span className="text-xs opacity-70">
                              {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </span>
                            {message.sender === 'business' && (
                              <span>
                                {message.status === 'sending' && (
                                  <svg className="w-3 h-3 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                  </svg>
                                )}
                                {message.status === 'sent' && (
                                  <svg className="w-3 h-3 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                )}
                                {message.status === 'failed' && (
                                  <svg className="w-3 h-3 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                  </svg>
                                )}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-700">
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        placeholder="Type your message..."
                        className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                      <button
                        type="submit"
                        disabled={!newMessage.trim()}
                        className="bg-purple-600 hover:bg-purple-700 text-white rounded-lg px-4 py-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        Send
                      </button>
                    </div>
                  </form>
                </div>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-gray-400 p-6">
                <svg className="w-16 h-16 mb-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                </svg>
                <p>Select a conversation to view messages</p>
              </div>
            )}
          </div>
        </div>

        {/* Auto-Response Templates */}
        {autoResponses.enabled && (
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-800/50 rounded-xl border border-purple-500/20 p-6"
          >
            <h2 className="text-lg font-semibold text-white mb-4">Auto-Response Templates</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {autoResponses.templates.map(template => (
                <div 
                  key={template.id}
                  className={`p-4 rounded-lg border ${
                    autoResponses.activeTemplateId === template.id 
                      ? 'border-purple-500 bg-purple-900/20' 
                      : 'border-gray-700 bg-gray-800/50'
                  }`}
                >
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="font-medium text-white">{template.name}</h3>
                    <button
                      onClick={() => changeActiveTemplate(template.id)}
                      className={`text-xs px-2 py-1 rounded ${
                        autoResponses.activeTemplateId === template.id
                          ? 'bg-purple-600 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      {autoResponses.activeTemplateId === template.id ? 'Active' : 'Activate'}
                    </button>
                  </div>
                  <p className="text-sm text-gray-300">{template.content}</p>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
}

// Mock data for development
const mockSMSThreads = [
  {
    id: '1',
    phoneNumber: '+****************',
    contactName: 'John Smith',
    messages: [
      {
        id: '101',
        content: 'Hello, I\'m interested in your services. Do you have availability next week?',
        timestamp: '2025-03-31T14:22:00Z',
        sender: 'customer',
        status: 'received'
      },
      {
        id: '102',
        content: 'Hi John, thanks for reaching out! We do have availability next week. What day works best for you?',
        timestamp: '2025-03-31T14:25:00Z',
        sender: 'business',
        status: 'sent'
      },
      {
        id: '103',
        content: 'Tuesday or Wednesday afternoon would work for me.',
        timestamp: '2025-03-31T14:30:00Z',
        sender: 'customer',
        status: 'received'
      }
    ]
  },
  {
    id: '2',
    phoneNumber: '+****************',
    contactName: 'Sarah Johnson',
    messages: [
      {
        id: '201',
        content: 'Do you offer weekend appointments?',
        timestamp: '2025-03-30T10:15:00Z',
        sender: 'customer',
        status: 'received'
      },
      {
        id: '202',
        content: 'Yes, we offer appointments on Saturdays from 9am to 2pm. Would you like to schedule one?',
        timestamp: '2025-03-30T10:20:00Z',
        sender: 'business',
        status: 'sent'
      }
    ]
  },
  {
    id: '3',
    phoneNumber: '+****************',
    contactName: 'Michael Brown',
    messages: [
      {
        id: '301',
        content: 'I need to reschedule my appointment for tomorrow.',
        timestamp: '2025-03-29T16:05:00Z',
        sender: 'customer',
        status: 'received'
      },
      {
        id: '302',
        content: 'No problem, Michael. What time would work better for you?',
        timestamp: '2025-03-29T16:10:00Z',
        sender: 'business',
        status: 'sent'
      },
      {
        id: '303',
        content: 'Would 3pm work instead of 1pm?',
        timestamp: '2025-03-29T16:15:00Z',
        sender: 'customer',
        status: 'received'
      },
      {
        id: '304',
        content: '3pm works perfectly. I\'ve updated your appointment. See you then!',
        timestamp: '2025-03-29T16:20:00Z',
        sender: 'business',
        status: 'sent'
      },
      {
        id: '305',
        content: 'Thank you!',
        timestamp: '2025-03-29T16:22:00Z',
        sender: 'customer',
        status: 'received'
      }
    ]
  }
];
