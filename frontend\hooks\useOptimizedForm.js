'use client';

import { useState, useCallback, useRef } from 'react';
import { markAndMeasure } from '../utils/performance/performanceMonitor';

/**
 * Custom hook for optimized form handling
 * 
 * This hook provides several optimizations for form handling:
 * - Debounced validation
 * - Throttled submissions
 * - Performance monitoring
 * - Form state management
 * - Error handling
 * 
 * @param {Object} options - Form options
 * @param {Object} options.initialValues - Initial form values
 * @param {Function} options.validate - Validation function
 * @param {Function} options.onSubmit - Submit handler
 * @param {number} options.validationDelay - Delay in ms before validation runs (default: 300)
 * @param {number} options.submitThrottle - Minimum time in ms between submissions (default: 1000)
 * @param {boolean} options.validateOnChange - Whether to validate on change (default: true)
 * @param {boolean} options.validateOnBlur - Whether to validate on blur (default: true)
 * @returns {Object} - Form state and handlers
 */
const useOptimizedForm = (options = {}) => {
  const {
    initialValues = {},
    validate = () => ({}),
    onSubmit,
    validationDelay = 300,
    submitThrottle = 1000,
    validateOnChange = true,
    validateOnBlur = true,
  } = options;

  const [values, setValues] = useState(initialValues);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitCount, setSubmitCount] = useState(0);
  
  const validationTimeoutRef = useRef(null);
  const lastSubmitTimeRef = useRef(0);
  
  // Run validation with debounce
  const runValidation = useCallback((fieldValues = values) => {
    // Clear any existing validation timeout
    if (validationTimeoutRef.current) {
      clearTimeout(validationTimeoutRef.current);
    }
    
    // Start validation timer
    markAndMeasure('validation-start', 'validation-start');
    
    // Set a new timeout for validation
    validationTimeoutRef.current = setTimeout(() => {
      const validationErrors = validate(fieldValues);
      setErrors(validationErrors);
      
      // End validation timer
      markAndMeasure('validation-end', 'validation-duration', 'validation-start');
    }, validationDelay);
  }, [validate, values, validationDelay]);
  
  // Handle field change
  const handleChange = useCallback((e) => {
    const { name, value, type, checked } = e.target;
    const fieldValue = type === 'checkbox' ? checked : value;
    
    setValues(prevValues => {
      const newValues = { ...prevValues, [name]: fieldValue };
      
      // Validate on change if enabled
      if (validateOnChange) {
        runValidation(newValues);
      }
      
      return newValues;
    });
  }, [runValidation, validateOnChange]);
  
  // Handle field blur
  const handleBlur = useCallback((e) => {
    const { name } = e.target;
    
    setTouched(prevTouched => ({
      ...prevTouched,
      [name]: true,
    }));
    
    // Validate on blur if enabled
    if (validateOnBlur) {
      runValidation();
    }
  }, [runValidation, validateOnBlur]);
  
  // Handle form submission
  const handleSubmit = useCallback(async (e) => {
    if (e) {
      e.preventDefault();
    }
    
    // Throttle submissions
    const now = Date.now();
    if (now - lastSubmitTimeRef.current < submitThrottle) {
      return;
    }
    lastSubmitTimeRef.current = now;
    
    // Validate all fields
    const validationErrors = validate(values);
    setErrors(validationErrors);
    
    // Mark all fields as touched
    const allTouched = Object.keys(values).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {});
    setTouched(allTouched);
    
    // Don't submit if there are errors
    if (Object.keys(validationErrors).length > 0) {
      return;
    }
    
    // Start submission timer
    markAndMeasure('submit-start', 'submit-start');
    
    setIsSubmitting(true);
    setSubmitCount(count => count + 1);
    
    try {
      await onSubmit(values);
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
      
      // End submission timer
      markAndMeasure('submit-end', 'submit-duration', 'submit-start');
    }
  }, [validate, values, onSubmit, submitThrottle]);
  
  // Reset the form
  const resetForm = useCallback((newValues = initialValues) => {
    setValues(newValues);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  }, [initialValues]);
  
  // Set a specific field value
  const setFieldValue = useCallback((name, value) => {
    setValues(prevValues => {
      const newValues = { ...prevValues, [name]: value };
      
      // Validate on change if enabled
      if (validateOnChange) {
        runValidation(newValues);
      }
      
      return newValues;
    });
  }, [runValidation, validateOnChange]);
  
  // Set a specific field error
  const setFieldError = useCallback((name, error) => {
    setErrors(prevErrors => ({
      ...prevErrors,
      [name]: error,
    }));
  }, []);
  
  // Set a specific field as touched
  const setFieldTouched = useCallback((name, isTouched = true) => {
    setTouched(prevTouched => ({
      ...prevTouched,
      [name]: isTouched,
    }));
    
    // Validate on blur if enabled
    if (validateOnBlur && isTouched) {
      runValidation();
    }
  }, [runValidation, validateOnBlur]);
  
  // Check if the form is valid
  const isValid = Object.keys(errors).length === 0;
  
  // Check if the form is dirty (values different from initial)
  const isDirty = JSON.stringify(values) !== JSON.stringify(initialValues);
  
  return {
    values,
    errors,
    touched,
    isSubmitting,
    submitCount,
    isValid,
    isDirty,
    handleChange,
    handleBlur,
    handleSubmit,
    resetForm,
    setFieldValue,
    setFieldError,
    setFieldTouched,
  };
};

export default useOptimizedForm;
