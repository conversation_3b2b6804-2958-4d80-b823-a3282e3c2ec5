/**
 * Airalo eSIM Provider
 * 
 * This module provides integration with Airalo Partners API for provisioning,
 * activating, and managing eSIM profiles.
 */

const axios = require('axios');
const logger = require('../../utils/logger');
const config = require('../../config');

/**
 * Create an Airalo provider instance
 * 
 * @param {Object} options - Provider configuration options
 * @returns {Object} Provider instance
 */
function createAiraloProvider(options = {}) {
  // Get configuration from options or global config
  const clientId = options.clientId || config.esim?.providers?.airalo?.clientId;
  const clientSecret = options.clientSecret || config.esim?.providers?.airalo?.clientSecret;
  const apiUrl = options.apiUrl || config.esim?.providers?.airalo?.apiUrl || 'https://api.airalo.com/v2';
  const sandbox = options.sandbox !== undefined ? options.sandbox : config.esim?.providers?.airalo?.sandbox || false;
  
  if (!clientId || !clientSecret) {
    throw new Error('Airalo client ID and client secret are required');
  }
  
  logger.info('Creating Airalo eSIM provider instance', { 
    sandbox, 
    apiUrl: apiUrl.replace(/\/v2\/?$/, '') // Log base URL without version
  });
  
  // Access token storage
  let accessToken = null;
  let tokenExpiry = null;
  
  /**
   * Get a valid access token, obtaining a new one if necessary
   * 
   * @returns {Promise<string>} Valid access token
   */
  async function getAccessToken() {
    // Check if we have a valid token
    if (accessToken && tokenExpiry && new Date() < tokenExpiry) {
      return accessToken;
    }
    
    logger.debug('Obtaining new Airalo access token');
    
    try {
      // Request a new token using client credentials grant
      const response = await axios.post(`${apiUrl}/token`, {
        client_id: clientId,
        client_secret: clientSecret,
        grant_type: 'client_credentials'
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      // Extract token and expiry
      accessToken = response.data.data.access_token;
      
      // Calculate expiry date (token is valid for 1 year, but we'll refresh slightly earlier)
      const expiresIn = response.data.data.expires_in || 31536000; // Default to 1 year in seconds
      tokenExpiry = new Date(Date.now() + (expiresIn * 1000) - (24 * 60 * 60 * 1000)); // Expires in minus 1 day
      
      logger.info('Obtained new Airalo access token', { expiresAt: tokenExpiry });
      
      return accessToken;
    } catch (error) {
      logger.error('Failed to obtain Airalo access token', { error: error.message });
      throw new Error(`Authentication failed: ${error.message}`);
    }
  }
  
  /**
   * Create an authenticated API client
   * 
   * @returns {Promise<Object>} Axios client instance with auth headers
   */
  async function createAuthenticatedClient() {
    const token = await getAccessToken();
    
    return axios.create({
      baseURL: apiUrl,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
        'X-Sandbox': sandbox ? 'true' : 'false'
      }
    });
  }
  
  /**
   * Handle API errors consistently
   * 
   * @param {Error} error - Original error object
   * @returns {Error} Enhanced error object
   */
  function handleApiError(error) {
    if (error.response) {
      const { status, data } = error.response;
      logger.error('Airalo API error', { 
        status,
        error: data.error || data.message || 'Unknown error',
        code: data.code
      });
      
      // Enhance error with API details
      error.apiError = {
        status,
        message: data.error || data.message || 'Unknown error',
        code: data.code
      };
    } else if (error.request) {
      logger.error('Airalo API request failed', { error: error.message });
    } else {
      logger.error('Airalo API client error', { error: error.message });
    }
    
    return error;
  }
  
  /**
   * Provision a new eSIM profile
   * 
   * @param {Object} options - Provisioning options
   * @returns {Object} Provisioned profile
   */
  async function provisionProfile(options = {}) {
    logger.debug('Provisioning eSIM profile with Airalo', { options });
    
    try {
      // Get authenticated client
      const client = await createAuthenticatedClient();
      
      // Map packageId to Airalo product ID (specific mapping would be implemented here)
      const productId = options.packageId;
      
      // Request body for provisioning
      const requestBody = {
        product_id: productId,
        region: options.region || 'global',
        callback_url: options.callbackUrl,
        metadata: options.metadata || {}
      };
      
      // Call Airalo API to provision eSIM
      const response = await client.post('/esims', requestBody);
      
      // Map Airalo response to standard profile format
      const profile = {
        id: response.data.id,
        iccid: response.data.iccid,
        status: mapStatus(response.data.status),
        activationCode: response.data.activation_code,
        activated: response.data.activated_at,
        expires: response.data.expires_at,
        packageDetails: {
          id: response.data.product.id,
          name: response.data.product.name,
          description: response.data.product.description,
          dataAmount: response.data.product.data_quota_mb,
          validity: response.data.product.validity_days,
          price: response.data.product.price,
          currency: response.data.product.currency,
          regions: [response.data.product.region]
        },
        dataUsed: response.data.data_usage_mb || 0,
        assignedNumber: response.data.phone_number,
        metadata: options.metadata || {}
      };
      
      logger.info('Successfully provisioned Airalo eSIM', { profileId: profile.id });
      
      return profile;
    } catch (error) {
      const enhancedError = handleApiError(error);
      logger.error('Failed to provision Airalo eSIM', { error: enhancedError.message });
      throw enhancedError;
    }
  }
  
  /**
   * Deactivate an eSIM profile
   * 
   * @param {String} profileId - Airalo eSIM ID
   * @param {Object} options - Deactivation options
   * @returns {Boolean} Success indicator
   */
  async function deactivateProfile(profileId, options = {}) {
    logger.debug('Deactivating Airalo eSIM profile', { profileId });
    
    try {
      // Get authenticated client
      const client = await createAuthenticatedClient();
      
      // Call Airalo API to deactivate eSIM
      await client.post(`/esims/${profileId}/deactivate`);
      
      logger.info('Successfully deactivated Airalo eSIM', { profileId });
      
      return true;
    } catch (error) {
      const enhancedError = handleApiError(error);
      logger.error('Failed to deactivate Airalo eSIM', { 
        profileId, 
        error: enhancedError.message 
      });
      throw enhancedError;
    }
  }
  
  /**
   * Get available eSIM packages
   * 
   * @param {Object} criteria - Search criteria
   * @param {Object} options - Search options
   * @returns {Array} Available packages
   */
  async function getAvailablePackages(criteria = {}, options = {}) {
    logger.debug('Getting available Airalo eSIM packages', { criteria });
    
    try {
      // Get authenticated client
      const client = await createAuthenticatedClient();
      
      // Build query parameters
      const queryParams = {};
      
      if (criteria.region) {
        queryParams.region = criteria.region;
      }
      
      if (criteria.minData) {
        queryParams.min_data = criteria.minData;
      }
      
      if (criteria.maxPrice) {
        queryParams.max_price = criteria.maxPrice;
      }
      
      // Call Airalo API to get available products
      const response = await client.get('/products', { params: queryParams });
      
      // Map Airalo products to standard package format
      const packages = response.data.map(product => ({
        id: product.id,
        name: product.name,
        description: product.description,
        dataAmount: product.data_quota_mb,
        validity: product.validity_days,
        price: product.price,
        currency: product.currency,
        regions: [product.region]
      }));
      
      return packages;
    } catch (error) {
      const enhancedError = handleApiError(error);
      logger.error('Failed to get Airalo eSIM packages', { error: enhancedError.message });
      throw enhancedError;
    }
  }
  
  /**
   * Get eSIM profile details
   * 
   * @param {String} profileId - Airalo eSIM ID
   * @param {Object} options - Retrieval options
   * @returns {Object} Profile details
   */
  async function getProfileDetails(profileId, options = {}) {
    logger.debug('Getting Airalo eSIM profile details', { profileId });
    
    try {
      // Get authenticated client
      const client = await createAuthenticatedClient();
      
      // Call Airalo API to get eSIM details
      const response = await client.get(`/esims/${profileId}`);
      
      // Map Airalo response to standard profile format
      const profile = {
        id: response.data.id,
        iccid: response.data.iccid,
        status: mapStatus(response.data.status),
        activationCode: response.data.activation_code,
        activated: response.data.activated_at,
        expires: response.data.expires_at,
        packageDetails: {
          id: response.data.product.id,
          name: response.data.product.name,
          description: response.data.product.description,
          dataAmount: response.data.product.data_quota_mb,
          validity: response.data.product.validity_days,
          price: response.data.product.price,
          currency: response.data.product.currency,
          regions: [response.data.product.region]
        },
        dataUsed: response.data.data_usage_mb || 0,
        assignedNumber: response.data.phone_number,
        metadata: response.data.metadata || {}
      };
      
      return profile;
    } catch (error) {
      const enhancedError = handleApiError(error);
      logger.error('Failed to get Airalo eSIM profile details', { 
        profileId, 
        error: enhancedError.message 
      });
      throw enhancedError;
    }
  }
  
  /**
   * Generate activation QR code
   * 
   * @param {String} profileId - Airalo eSIM ID
   * @param {Object} options - Generation options
   * @returns {Object} QR code data
   */
  async function generateActivationQR(profileId, options = {}) {
    logger.debug('Generating activation QR for Airalo eSIM', { profileId });
    
    try {
      // Get authenticated client
      const client = await createAuthenticatedClient();
      
      // Call Airalo API to generate QR code
      const response = await client.get(`/esims/${profileId}/qr`);
      
      return {
        qrCodeData: response.data.qr_code,
        activationCode: response.data.activation_code,
        format: 'png',
        expiresAt: response.data.expires_at
      };
    } catch (error) {
      const enhancedError = handleApiError(error);
      logger.error('Failed to generate Airalo eSIM QR code', { 
        profileId, 
        error: enhancedError.message 
      });
      throw enhancedError;
    }
  }
  
  /**
   * Check activation status
   * 
   * @param {String} profileId - Airalo eSIM ID
   * @param {Object} options - Check options
   * @returns {Object} Activation status
   */
  async function checkActivationStatus(profileId, options = {}) {
    logger.debug('Checking activation status for Airalo eSIM', { profileId });
    
    try {
      // Get eSIM details to check status
      const profile = await getProfileDetails(profileId);
      
      return {
        profileId,
        status: profile.status,
        activatedAt: profile.activated,
        isActive: profile.status === 'activated'
      };
    } catch (error) {
      // Error already handled by getProfileDetails
      logger.error('Failed to check Airalo eSIM activation status', { 
        profileId, 
        error: error.message 
      });
      throw error;
    }
  }
  
  /**
   * Get data usage
   * 
   * @param {String} profileId - Airalo eSIM ID
   * @param {Object} options - Usage options
   * @returns {Object} Data usage
   */
  async function getDataUsage(profileId, options = {}) {
    logger.debug('Getting data usage for Airalo eSIM', { profileId });
    
    try {
      // Get eSIM details to check usage
      const profile = await getProfileDetails(profileId);
      
      return {
        profileId,
        packageId: profile.packageDetails.id,
        dataTotal: profile.packageDetails.dataAmount,
        dataUsed: profile.dataUsed,
        dataRemaining: profile.packageDetails.dataAmount - profile.dataUsed,
        isUnlimited: profile.packageDetails.dataAmount === -1,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      // Error already handled by getProfileDetails
      logger.error('Failed to get Airalo eSIM data usage', { 
        profileId, 
        error: error.message 
      });
      throw error;
    }
  }
  
  /**
   * Purchase a data package
   * 
   * @param {String} profileId - Airalo eSIM ID
   * @param {Object} packageDetails - Package to purchase
   * @param {Object} options - Purchase options
   * @returns {Object} Purchase confirmation
   */
  async function purchaseDataPackage(profileId, packageDetails, options = {}) {
    logger.debug('Purchasing data package for Airalo eSIM', { 
      profileId, 
      packageId: packageDetails.packageId 
    });
    
    try {
      // Get authenticated client
      const client = await createAuthenticatedClient();
      
      // Call Airalo API to purchase data package
      const response = await client.post(`/esims/${profileId}/topups`, {
        product_id: packageDetails.packageId
      });
      
      return {
        profileId,
        packageId: packageDetails.packageId,
        purchaseId: response.data.id,
        transactionDate: response.data.created_at,
        price: response.data.price,
        currency: response.data.currency,
        status: mapPurchaseStatus(response.data.status)
      };
    } catch (error) {
      const enhancedError = handleApiError(error);
      logger.error('Failed to purchase Airalo data package', { 
        profileId, 
        packageId: packageDetails.packageId,
        error: enhancedError.message 
      });
      throw enhancedError;
    }
  }
  
  // Helper functions
  
  /**
   * Map Airalo status to standardized status
   * 
   * @param {String} airaloStatus - Airalo-specific status
   * @returns {String} Standardized status
   */
  function mapStatus(airaloStatus) {
    const statusMap = {
      'pending': 'provisioned',
      'active': 'activated',
      'inactive': 'deactivated',
      'suspended': 'suspended'
    };
    
    return statusMap[airaloStatus] || airaloStatus;
  }
  
  /**
   * Map Airalo purchase status to standardized status
   * 
   * @param {String} airaloStatus - Airalo-specific purchase status
   * @returns {String} Standardized status
   */
  function mapPurchaseStatus(airaloStatus) {
    const statusMap = {
      'pending': 'pending',
      'completed': 'completed',
      'failed': 'failed'
    };
    
    return statusMap[airaloStatus] || airaloStatus;
  }
  
  // Return provider interface
  return {
    provisionProfile,
    deactivateProfile,
    getAvailablePackages,
    getProfileDetails,
    generateActivationQR,
    checkActivationStatus,
    getDataUsage,
    purchaseDataPackage
  };
}

module.exports = createAiraloProvider;
