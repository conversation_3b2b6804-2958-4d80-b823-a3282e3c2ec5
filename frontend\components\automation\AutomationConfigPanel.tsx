'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { Tab } from '@headlessui/react';
import CallRulesConfig from './CallRulesConfig';
import SmsRulesConfig from './SmsRulesConfig';
import VoicemailConfig from './VoicemailConfig';
import AIAssistantManager from './AIAssistantManager';
import AutomationToggle from './AutomationToggle';
import LoadingSpinner from '../shared/LoadingSpinner';
import ErrorMessage from '../shared/ErrorMessage';

// Types for automation configuration
export interface AutomationConfig {
  callRules: {
    action: 'forward' | 'voicemail' | 'ai_answer';
    forwardTo?: string;
    scheduleActive: boolean;
    scheduleRules?: {
      days: string[];
      startTime: string;
      endTime: string;
    };
  };
  smsRules: {
    action: 'forward' | 'auto_reply' | 'ai_reply';
    forwardTo?: string;
    autoReplyText?: string;
  };
  voicemailConfig: {
    transcriptionEnabled: boolean;
    aiSummaryEnabled: boolean;
    notificationEnabled: boolean;
    notificationEmail?: string;
  };
}

// Types for AI assistant configuration
export interface AIAssistantConfig {
  personality: string;
  knowledgeSources: {
    id: string;
    name: string;
    type: 'text' | 'file';
    lastUpdated: string;
  }[];
  commands: {
    id: string;
    triggerPhrase: string;
    action: string;
    enabled: boolean;
  }[];
}

interface AutomationConfigPanelProps {
  selectedNumberId: string;
}

export default function AutomationConfigPanel({
  selectedNumberId,
}: AutomationConfigPanelProps) {
  // State for the selected tab
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);

  // Fetch automation configuration
  const {
    data: automationConfig,
    isLoading: isLoadingAutomation,
    error: automationError,
  } = useQuery({
    queryKey: ['automations', selectedNumberId],
    queryFn: async () => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Return mock data
        return {
          callRules: {
            action: 'ai_answer',
            forwardTo: '',
            scheduleActive: true,
            scheduleRules: {
              days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
              startTime: '09:00',
              endTime: '17:00',
            },
          },
          smsRules: {
            action: 'ai_reply',
            forwardTo: '',
            autoReplyText: 'Thank you for your message. We will get back to you shortly.',
          },
          voicemailConfig: {
            transcriptionEnabled: true,
            aiSummaryEnabled: true,
            notificationEnabled: true,
            notificationEmail: '<EMAIL>',
          },
        } as AutomationConfig;
      }
      
      // In production, fetch from API
      const { data } = await axios.get<AutomationConfig>(`/api/numbers/${selectedNumberId}/automations`);
      return data;
    },
    enabled: !!selectedNumberId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch AI assistant configuration
  const {
    data: aiConfig,
    isLoading: isLoadingAI,
    error: aiError,
  } = useQuery({
    queryKey: ['ai-assistant', selectedNumberId],
    queryFn: async () => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock data
        return {
          personality: 'professional',
          knowledgeSources: [
            {
              id: '1',
              name: 'Company FAQ',
              type: 'file',
              lastUpdated: '2023-05-15T10:30:00Z',
            },
            {
              id: '2',
              name: 'Product Information',
              type: 'text',
              lastUpdated: '2023-05-20T14:45:00Z',
            },
          ],
          commands: [
            {
              id: '1',
              triggerPhrase: 'schedule appointment',
              action: 'Create calendar event',
              enabled: true,
            },
            {
              id: '2',
              triggerPhrase: 'check order status',
              action: 'Query order database',
              enabled: true,
            },
          ],
        } as AIAssistantConfig;
      }
      
      // In production, fetch from API
      const { data } = await axios.get<AIAssistantConfig>(`/api/numbers/${selectedNumberId}/ai-assistant`);
      return data;
    },
    enabled: !!selectedNumberId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Loading state
  const isLoading = isLoadingAutomation || isLoadingAI;

  // Error state
  const error = automationError || aiError;

  // If loading, show loading spinner
  if (isLoading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <ErrorMessage
          title="Failed to load automation configuration"
          message="We couldn't load the automation configuration for this number. Please try again later."
          error={error instanceof Error ? error : new Error('Unknown error')}
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  // If no data, show message
  if (!automationConfig || !aiConfig) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <p className="text-gray-500 dark:text-gray-400 text-center py-8">
          No automation configuration found for this number.
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div className="p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white">
          Automation Configuration
        </h2>
        <AutomationToggle
          numberId={selectedNumberId}
          initialStatus={true} // This would come from the number data
        />
      </div>

      <Tab.Group selectedIndex={selectedTabIndex} onChange={setSelectedTabIndex}>
        <Tab.List className="flex border-b border-gray-200 dark:border-gray-700">
          <Tab
            className={({ selected }) =>
              `flex-1 py-3 px-4 text-sm font-medium text-center focus:outline-none ${
                selected
                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`
            }
          >
            Call Rules
          </Tab>
          <Tab
            className={({ selected }) =>
              `flex-1 py-3 px-4 text-sm font-medium text-center focus:outline-none ${
                selected
                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`
            }
          >
            SMS Rules
          </Tab>
          <Tab
            className={({ selected }) =>
              `flex-1 py-3 px-4 text-sm font-medium text-center focus:outline-none ${
                selected
                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`
            }
          >
            Voicemail
          </Tab>
          <Tab
            className={({ selected }) =>
              `flex-1 py-3 px-4 text-sm font-medium text-center focus:outline-none ${
                selected
                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`
            }
          >
            AI Assistant
          </Tab>
        </Tab.List>
        <Tab.Panels className="p-4 sm:p-6">
          <Tab.Panel>
            <CallRulesConfig
              numberId={selectedNumberId}
              initialData={automationConfig.callRules}
            />
          </Tab.Panel>
          <Tab.Panel>
            <SmsRulesConfig
              numberId={selectedNumberId}
              initialData={automationConfig.smsRules}
            />
          </Tab.Panel>
          <Tab.Panel>
            <VoicemailConfig
              numberId={selectedNumberId}
              initialData={automationConfig.voicemailConfig}
            />
          </Tab.Panel>
          <Tab.Panel>
            <AIAssistantManager
              numberId={selectedNumberId}
              initialData={aiConfig}
            />
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
    </div>
  );
}
