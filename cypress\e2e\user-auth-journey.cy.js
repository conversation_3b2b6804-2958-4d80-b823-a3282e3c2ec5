/**
 * E2E Test for User Registration/Login Journey
 * 
 * This test covers the user registration and login process,
 * ensuring that new users can sign up and existing users can log in.
 */

describe('User Authentication Journey', () => {
  beforeEach(() => {
    // Clear cookies and localStorage before each test
    cy.clearCookies();
    cy.clearLocalStorage();
    
    // Mock API responses
    cy.intercept('POST', '*api/auth/register*', {
      statusCode: 200,
      body: {
        success: true,
        user: {
          id: 'test-user-id',
          email: '<EMAIL>',
          name: 'Test User',
          organizationId: 'org-123'
        }
      }
    }).as('register');
    
    cy.intercept('POST', '*api/auth/login*', {
      statusCode: 200,
      body: {
        success: true,
        user: {
          id: 'test-user-id',
          email: '<EMAIL>',
          name: 'Test User',
          organizationId: 'org-123'
        },
        token: 'fake-jwt-token'
      }
    }).as('login');
  });
  
  it('should allow a new user to register', () => {
    // Visit the registration page
    cy.visit('/register');
    
    // Verify page content
    cy.get('h1').should('contain', 'Create an Account');
    
    // Fill out the registration form
    cy.get('input[name="name"]').type('Test User');
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('Password123!');
    cy.get('input[name="confirmPassword"]').type('Password123!');
    
    // Accept terms
    cy.get('input[type="checkbox"]').check();
    
    // Submit the form
    cy.get('button[type="submit"]').click();
    
    // Wait for the API call to complete
    cy.wait('@register');
    
    // Verify redirect to confirmation page or dashboard
    cy.url().should('include', '/dashboard');
    
    // Verify welcome message
    cy.get('[data-testid="welcome-message"]').should('be.visible');
  });
  
  it('should allow an existing user to log in', () => {
    // Visit the login page
    cy.visit('/login');
    
    // Verify page content
    cy.get('h1').should('contain', 'Log In');
    
    // Fill out the login form
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('Password123!');
    
    // Submit the form
    cy.get('button[type="submit"]').click();
    
    // Wait for the API call to complete
    cy.wait('@login');
    
    // Verify redirect to dashboard
    cy.url().should('include', '/dashboard');
    
    // Verify user-specific content is visible
    cy.get('[data-testid="user-greeting"]').should('contain', 'Test User');
  });
  
  it('should display validation errors for invalid inputs', () => {
    // Visit the registration page
    cy.visit('/register');
    
    // Submit empty form
    cy.get('button[type="submit"]').click();
    
    // Verify validation errors
    cy.get('[data-testid="error-message"]').should('exist');
    
    // Try invalid email
    cy.get('input[name="email"]').type('invalid-email');
    cy.get('button[type="submit"]').click();
    cy.get('[data-testid="email-error"]').should('be.visible');
    
    // Try weak password
    cy.get('input[name="email"]').clear().type('<EMAIL>');
    cy.get('input[name="password"]').type('weak');
    cy.get('button[type="submit"]').click();
    cy.get('[data-testid="password-error"]').should('be.visible');
    
    // Try mismatched passwords
    cy.get('input[name="password"]').clear().type('Password123!');
    cy.get('input[name="confirmPassword"]').type('DifferentPassword123!');
    cy.get('button[type="submit"]').click();
    cy.get('[data-testid="confirm-password-error"]').should('be.visible');
  });
  
  it('should allow password recovery', () => {
    // Intercept password reset request
    cy.intercept('POST', '*api/auth/forgot-password*', {
      statusCode: 200,
      body: { success: true }
    }).as('forgotPassword');
    
    // Visit the login page
    cy.visit('/login');
    
    // Click on forgot password link
    cy.get('a').contains('Forgot password?').click();
    
    // Verify url
    cy.url().should('include', '/forgot-password');
    
    // Enter email
    cy.get('input[name="email"]').type('<EMAIL>');
    
    // Submit form
    cy.get('button[type="submit"]').click();
    
    // Wait for API call to complete
    cy.wait('@forgotPassword');
    
    // Verify confirmation message
    cy.get('[data-testid="success-message"]').should('be.visible');
    cy.get('[data-testid="success-message"]').should('contain', 'password reset');
  });
});
