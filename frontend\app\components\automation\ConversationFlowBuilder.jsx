"use client";

import { useState, useRef, useCallback } from 'react';
import React<PERSON><PERSON>, {
  ReactFlowProvider,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  Panel
} from 'reactflow';
import 'reactflow/dist/style.css';
import { 
  PlusCircleIcon, 
  TrashIcon, 
  ArrowPathIcon,
  DocumentTextIcon,
  ChatBubbleLeftRightIcon,
  CalendarIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline';

// Custom node types
const nodeTypes = {
  start: StartNode,
  message: MessageNode,
  condition: ConditionNode,
  action: ActionNode,
  end: EndNode
};

// Start Node Component
function StartNode({ data }) {
  return (
    <div className="px-4 py-3 rounded-lg bg-green-600 text-white shadow-lg border border-green-500 min-w-[180px]">
      <div className="flex items-center">
        <div className="p-2 bg-green-700 rounded-full mr-2">
          <PlayIcon className="h-4 w-4 text-white" />
        </div>
        <div>
          <h4 className="font-medium">Start</h4>
          <p className="text-xs text-green-200">{data.label}</p>
        </div>
      </div>
    </div>
  );
}

// Message Node Component
function MessageNode({ data }) {
  return (
    <div className="px-4 py-3 rounded-lg bg-blue-600 text-white shadow-lg border border-blue-500 min-w-[220px]">
      <div className="flex items-center mb-2">
        <div className="p-2 bg-blue-700 rounded-full mr-2">
          <ChatBubbleLeftRightIcon className="h-4 w-4 text-white" />
        </div>
        <h4 className="font-medium">Send Message</h4>
      </div>
      <p className="text-xs text-blue-200 line-clamp-3">{data.message || "Message content..."}</p>
    </div>
  );
}

// Condition Node Component
function ConditionNode({ data }) {
  return (
    <div className="px-4 py-3 rounded-lg bg-purple-600 text-white shadow-lg border border-purple-500 min-w-[220px]">
      <div className="flex items-center mb-2">
        <div className="p-2 bg-purple-700 rounded-full mr-2">
          <QuestionMarkCircleIcon className="h-4 w-4 text-white" />
        </div>
        <h4 className="font-medium">Condition</h4>
      </div>
      <p className="text-xs text-purple-200">{data.condition || "If..."}</p>
      <div className="flex justify-between mt-2 text-xs">
        <div className="bg-green-500/30 px-2 py-1 rounded">Yes</div>
        <div className="bg-red-500/30 px-2 py-1 rounded">No</div>
      </div>
    </div>
  );
}

// Action Node Component
function ActionNode({ data }) {
  return (
    <div className="px-4 py-3 rounded-lg bg-amber-600 text-white shadow-lg border border-amber-500 min-w-[220px]">
      <div className="flex items-center mb-2">
        <div className="p-2 bg-amber-700 rounded-full mr-2">
          {data.actionType === 'calendar' ? (
            <CalendarIcon className="h-4 w-4 text-white" />
          ) : data.actionType === 'document' ? (
            <DocumentTextIcon className="h-4 w-4 text-white" />
          ) : (
            <ClockIcon className="h-4 w-4 text-white" />
          )}
        </div>
        <h4 className="font-medium">{data.actionTitle || "Perform Action"}</h4>
      </div>
      <p className="text-xs text-amber-200">{data.description || "Action description..."}</p>
    </div>
  );
}

// End Node Component
function EndNode({ data }) {
  return (
    <div className="px-4 py-3 rounded-lg bg-gray-600 text-white shadow-lg border border-gray-500 min-w-[180px]">
      <div className="flex items-center">
        <div className="p-2 bg-gray-700 rounded-full mr-2">
          <CheckCircleIcon className="h-4 w-4 text-white" />
        </div>
        <div>
          <h4 className="font-medium">End</h4>
          <p className="text-xs text-gray-300">{data.label || "Conversation End"}</p>
        </div>
      </div>
    </div>
  );
}

// Play Icon Component
function PlayIcon(props) {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      viewBox="0 0 24 24" 
      fill="currentColor" 
      {...props}
    >
      <path d="M8 5v14l11-7z" />
    </svg>
  );
}

// Main Conversation Flow Builder Component
const ConversationFlowBuilder = () => {
  const reactFlowWrapper = useRef(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [reactFlowInstance, setReactFlowInstance] = useState(null);
  const [selectedNode, setSelectedNode] = useState(null);
  const [nodeName, setNodeName] = useState('');
  const [nodeContent, setNodeContent] = useState('');
  const [showNodePanel, setShowNodePanel] = useState(false);
  
  // Initial nodes setup
  useState(() => {
    const initialNodes = [
      {
        id: '1',
        type: 'start',
        position: { x: 250, y: 50 },
        data: { label: 'Incoming Call' }
      },
      {
        id: '2',
        type: 'message',
        position: { x: 250, y: 150 },
        data: { message: 'Hi there, this is Callsaver. We noticed you called—sorry we missed you. Can you tell us the reason for your call?' }
      },
      {
        id: '3',
        type: 'condition',
        position: { x: 250, y: 300 },
        data: { condition: 'Contains "appointment" or "schedule"' }
      },
      {
        id: '4',
        type: 'action',
        position: { x: 100, y: 450 },
        data: { 
          actionType: 'calendar',
          actionTitle: 'Schedule Appointment',
          description: 'Check calendar and offer available times'
        }
      },
      {
        id: '5',
        type: 'end',
        position: { x: 400, y: 450 },
        data: { label: 'Transfer to human agent' }
      }
    ];
    
    const initialEdges = [
      { id: 'e1-2', source: '1', target: '2' },
      { id: 'e2-3', source: '2', target: '3' },
      { id: 'e3-4', source: '3', target: '4', sourceHandle: 'yes' },
      { id: 'e3-5', source: '3', target: '5', sourceHandle: 'no' }
    ];
    
    setNodes(initialNodes);
    setEdges(initialEdges);
  }, []);
  
  // Handle node click
  const onNodeClick = useCallback((event, node) => {
    setSelectedNode(node);
    setNodeName(node.data.label || node.data.actionTitle || '');
    setNodeContent(node.data.message || node.data.condition || node.data.description || '');
    setShowNodePanel(true);
  }, []);
  
  // Handle connection between nodes
  const onConnect = useCallback((params) => {
    setEdges((eds) => addEdge({ ...params, animated: true }, eds));
  }, [setEdges]);
  
  // Handle drag over for new nodes
  const onDragOver = useCallback((event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);
  
  // Handle drop for new nodes
  const onDrop = useCallback(
    (event) => {
      event.preventDefault();
      
      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');
      
      // Check if the dropped element is valid
      if (typeof type === 'undefined' || !type) {
        return;
      }
      
      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });
      
      const newNode = {
        id: `${Date.now()}`,
        type,
        position,
        data: { 
          label: `New ${type} node`,
          message: type === 'message' ? 'Enter your message here...' : undefined,
          condition: type === 'condition' ? 'Define your condition...' : undefined,
          actionType: type === 'action' ? 'default' : undefined,
          actionTitle: type === 'action' ? 'New Action' : undefined,
          description: type === 'action' ? 'Action description...' : undefined
        },
      };
      
      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes]
  );
  
  // Update node data
  const updateNodeData = () => {
    if (!selectedNode) return;
    
    setNodes((nds) =>
      nds.map((node) => {
        if (node.id === selectedNode.id) {
          if (node.type === 'message') {
            return {
              ...node,
              data: {
                ...node.data,
                message: nodeContent,
              },
            };
          } else if (node.type === 'condition') {
            return {
              ...node,
              data: {
                ...node.data,
                condition: nodeContent,
              },
            };
          } else if (node.type === 'action') {
            return {
              ...node,
              data: {
                ...node.data,
                actionTitle: nodeName,
                description: nodeContent,
              },
            };
          } else {
            return {
              ...node,
              data: {
                ...node.data,
                label: nodeName,
              },
            };
          }
        }
        return node;
      })
    );
    
    setShowNodePanel(false);
  };
  
  // Delete selected node
  const deleteNode = () => {
    if (!selectedNode) return;
    
    setNodes((nds) => nds.filter((node) => node.id !== selectedNode.id));
    setEdges((eds) => eds.filter(
      (edge) => edge.source !== selectedNode.id && edge.target !== selectedNode.id
    ));
    
    setShowNodePanel(false);
    setSelectedNode(null);
  };
  
  // Reset flow
  const resetFlow = () => {
    if (window.confirm('Are you sure you want to reset the flow? All changes will be lost.')) {
      window.location.reload();
    }
  };
  
  // Save flow
  const saveFlow = () => {
    const flow = { nodes, edges };
    localStorage.setItem('callsaverFlow', JSON.stringify(flow));
    alert('Flow saved successfully!');
  };
  
  // Node types for the sidebar
  const nodeTypesForSidebar = [
    { type: 'message', label: 'Message', icon: <ChatBubbleLeftRightIcon className="h-5 w-5" /> },
    { type: 'condition', label: 'Condition', icon: <QuestionMarkCircleIcon className="h-5 w-5" /> },
    { type: 'action', label: 'Action', icon: <ClockIcon className="h-5 w-5" /> },
    { type: 'end', label: 'End', icon: <CheckCircleIcon className="h-5 w-5" /> }
  ];
  
  // Drag start handler for sidebar items
  const onDragStart = (event, nodeType) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
  };
  
  return (
    <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg h-[800px]">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-white">AI Conversation Flow Builder</h2>
        <div className="flex space-x-2">
          <button 
            onClick={saveFlow}
            className="px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm flex items-center"
          >
            <DocumentTextIcon className="h-4 w-4 mr-1" />
            Save Flow
          </button>
          <button 
            onClick={resetFlow}
            className="px-3 py-1.5 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white rounded-lg transition-colors text-sm flex items-center"
          >
            <ArrowPathIcon className="h-4 w-4 mr-1" />
            Reset
          </button>
        </div>
      </div>
      
      <div className="flex h-[700px]">
        {/* Sidebar */}
        <div className="w-48 bg-gray-800 rounded-l-lg p-3 border-r border-gray-700">
          <h3 className="text-white font-medium mb-3">Node Types</h3>
          <div className="space-y-2">
            {nodeTypesForSidebar.map((item) => (
              <div
                key={item.type}
                className="p-2 bg-gray-700 rounded-lg text-white cursor-grab flex items-center"
                onDragStart={(event) => onDragStart(event, item.type)}
                draggable
              >
                <div className="p-1.5 bg-gray-600 rounded-md mr-2">
                  {item.icon}
                </div>
                <span className="text-sm">{item.label}</span>
              </div>
            ))}
          </div>
          
          <div className="mt-6">
            <h3 className="text-white font-medium mb-3">Instructions</h3>
            <ul className="text-xs text-gray-400 space-y-2">
              <li>• Drag nodes from the sidebar</li>
              <li>• Connect nodes by dragging from handles</li>
              <li>• Click on nodes to edit properties</li>
              <li>• Save your flow when finished</li>
            </ul>
          </div>
        </div>
        
        {/* Flow Builder */}
        <div className="flex-1 bg-gray-950 rounded-r-lg" ref={reactFlowWrapper}>
          <ReactFlowProvider>
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onNodeClick={onNodeClick}
              onDrop={onDrop}
              onDragOver={onDragOver}
              nodeTypes={nodeTypes}
              fitView
              attributionPosition="bottom-right"
              onInit={setReactFlowInstance}
            >
              <Controls />
              <Background color="#374151" gap={16} />
            </ReactFlow>
          </ReactFlowProvider>
        </div>
      </div>
      
      {/* Node Edit Panel */}
      {showNodePanel && selectedNode && (
        <div className="absolute right-8 top-24 w-80 bg-gray-800 rounded-lg p-4 shadow-xl border border-gray-700 z-50">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-white font-medium">Edit Node</h3>
            <button 
              onClick={() => setShowNodePanel(false)}
              className="text-gray-400 hover:text-white"
            >
              <XCircleIcon className="h-5 w-5" />
            </button>
          </div>
          
          {(selectedNode.type === 'start' || selectedNode.type === 'end' || selectedNode.type === 'action') && (
            <div className="mb-3">
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Node Name
              </label>
              <input
                type="text"
                value={nodeName}
                onChange={(e) => setNodeName(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500"
              />
            </div>
          )}
          
          {(selectedNode.type === 'message' || selectedNode.type === 'condition' || selectedNode.type === 'action') && (
            <div className="mb-3">
              <label className="block text-gray-300 text-sm font-medium mb-2">
                {selectedNode.type === 'message' ? 'Message Content' : 
                  selectedNode.type === 'condition' ? 'Condition' : 'Description'}
              </label>
              <textarea
                value={nodeContent}
                onChange={(e) => setNodeContent(e.target.value)}
                rows={4}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500"
              />
            </div>
          )}
          
          {selectedNode.type === 'action' && (
            <div className="mb-3">
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Action Type
              </label>
              <select
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500"
                value={selectedNode.data.actionType}
                onChange={(e) => {
                  setNodes((nds) =>
                    nds.map((node) => {
                      if (node.id === selectedNode.id) {
                        return {
                          ...node,
                          data: {
                            ...node.data,
                            actionType: e.target.value,
                          },
                        };
                      }
                      return node;
                    })
                  );
                }}
              >
                <option value="default">Default Action</option>
                <option value="calendar">Calendar Action</option>
                <option value="document">Document Action</option>
              </select>
            </div>
          )}
          
          <div className="flex justify-between">
            <button
              onClick={deleteNode}
              className="px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors text-sm flex items-center"
            >
              <TrashIcon className="h-4 w-4 mr-1" />
              Delete
            </button>
            <button
              onClick={updateNodeData}
              className="px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm"
            >
              Update
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ConversationFlowBuilder;
