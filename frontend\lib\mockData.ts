/**
 * Mock data for development environment
 * This file provides mock data for API calls when the backend is not available
 */

// Dashboard mock data
export const dashboardMockData = {
  summary: {
    totalCallsToday: 12,
    totalCallsWeek: 87,
    totalMessagesToday: 34,
    activeAutomations: 3,
    creditBalance: 250.75,
    answeredCalls: 8,
    missedCalls: 4,
    totalCalls: 87
  },
  recentActivity: [
    {
      id: '1',
      type: 'call',
      timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
      description: 'Incoming call from +1 (555) 123-4567',
      link: '/call-logs/1'
    },
    {
      id: '2',
      type: 'sms',
      timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2 hours ago
      description: 'SMS received from +1 (555) 987-6543',
      link: '/messages/2'
    },
    {
      id: '3',
      type: 'voicemail',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(), // 5 hours ago
      description: 'New voicemail from +1 (555) 456-7890',
      link: '/call-logs/3'
    },
    {
      id: '4',
      type: 'credit',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
      description: 'Credits added: 100 credits',
      link: '/billing/4'
    }
  ],
  aiInsights: [
    {
      id: '1',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
      description: 'You\'ve received 3 calls from the same number today. Consider adding it to your contacts.',
      relatedNumberId: '123'
    },
    {
      id: '2',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
      description: 'Your call volume has increased by 30% this week compared to last week.',
      relatedNumberId: null
    }
  ]
};

// Number management mock data
export const numberManagementMockData = {
  ownedNumbers: [
    {
      id: '1',
      phoneNumber: '+***********',
      friendlyName: 'Business Line',
      status: 'active',
      type: 'local',
      capabilities: ['voice', 'sms', 'mms'],
      acquiredDate: new Date(Date.now() - 1000 * 60 * 60 * 24 * 30).toISOString(), // 30 days ago
      automationLink: '/automation/1'
    },
    {
      id: '2',
      phoneNumber: '+***********',
      friendlyName: 'Toll-Free Support',
      status: 'active',
      type: 'tollfree',
      capabilities: ['voice', 'sms'],
      acquiredDate: new Date(Date.now() - 1000 * 60 * 60 * 24 * 60).toISOString(), // 60 days ago
      automationLink: '/automation/2'
    },
    {
      id: '3',
      phoneNumber: '+***********',
      friendlyName: '',
      status: 'pending',
      type: 'local',
      capabilities: ['voice', 'sms', 'mms', 'fax'],
      acquiredDate: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
      automationLink: null
    }
  ],
  availableNumbers: [
    {
      id: '101',
      phoneNumber: '+***********',
      location: 'San Francisco, CA',
      capabilities: ['voice', 'sms', 'mms'],
      type: 'local',
      cost: {
        setup: 1.00,
        monthly: 3.99
      }
    },
    {
      id: '102',
      phoneNumber: '+15553334444',
      location: 'New York, NY',
      capabilities: ['voice', 'sms'],
      type: 'local',
      cost: {
        setup: 1.00,
        monthly: 3.99
      }
    },
    {
      id: '103',
      phoneNumber: '+18887776666',
      location: 'United States',
      capabilities: ['voice', 'sms', 'mms'],
      type: 'tollfree',
      cost: {
        setup: 3.00,
        monthly: 5.99
      }
    },
    {
      id: '104',
      phoneNumber: '+14155556666',
      location: 'San Francisco, CA',
      capabilities: ['voice', 'sms', 'mms', 'fax'],
      type: 'local',
      cost: {
        setup: 1.00,
        monthly: 4.99
      }
    },
    {
      id: '105',
      phoneNumber: '+12125557777',
      location: 'New York, NY',
      capabilities: ['voice', 'sms'],
      type: 'local',
      cost: {
        setup: 1.00,
        monthly: 3.99
      }
    }
  ],
  ownedEsims: [
    {
      id: '201',
      planName: 'Global Traveler 5GB',
      status: 'active',
      activationDate: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(), // 5 days ago
      expiryDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 25).toISOString(), // 25 days from now
      dataRemaining: '3.2 GB',
      country: 'Global'
    },
    {
      id: '202',
      planName: 'US Unlimited',
      status: 'active',
      activationDate: new Date(Date.now() - 1000 * 60 * 60 * 24 * 15).toISOString(), // 15 days ago
      expiryDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 15).toISOString(), // 15 days from now
      dataRemaining: 'Unlimited',
      country: 'United States'
    }
  ],
  availableEsims: [
    {
      id: '301',
      planName: 'Europe Travel 10GB',
      data: '10 GB',
      validity: '30 days',
      country: 'EU',
      cost: 29.99
    },
    {
      id: '302',
      planName: 'US Unlimited',
      data: 'Unlimited',
      validity: '30 days',
      country: 'US',
      cost: 39.99
    },
    {
      id: '303',
      planName: 'Global Traveler 5GB',
      data: '5 GB',
      validity: '30 days',
      country: 'GLOBAL',
      cost: 49.99
    },
    {
      id: '304',
      planName: 'Asia Explorer 8GB',
      data: '8 GB',
      validity: '15 days',
      country: 'ASIA',
      cost: 24.99
    }
  ]
};
