'use client';

import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { PlusIcon, PencilIcon, TrashIcon, CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { Switch } from '@headlessui/react';
import LoadingSpinner from '../shared/LoadingSpinner';
import EmptyState from '../shared/EmptyState';

interface Command {
  id: string;
  name: string;
  triggerPhrase: string;
  action: string;
  parameters?: {
    name: string;
    type: string;
    required: boolean;
  }[];
  enabled: boolean;
}

export default function CustomCommandConfigurator() {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newCommand, setNewCommand] = useState({
    name: '',
    triggerPhrase: '',
    action: '',
    enabled: true,
  });
  const [editCommand, setEditCommand] = useState<Command | null>(null);
  const queryClient = useQueryClient();

  // Fetch commands
  const {
    data: commands,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['ai', 'commands'],
    queryFn: async () => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock data
        return [
          {
            id: '1',
            name: 'Schedule Appointment',
            triggerPhrase: 'schedule appointment',
            action: 'Create calendar event',
            parameters: [
              { name: 'date', type: 'date', required: true },
              { name: 'time', type: 'time', required: true },
              { name: 'duration', type: 'number', required: false },
            ],
            enabled: true,
          },
          {
            id: '2',
            name: 'Check Order Status',
            triggerPhrase: 'check order status',
            action: 'Query order database',
            parameters: [
              { name: 'orderId', type: 'string', required: true },
            ],
            enabled: true,
          },
          {
            id: '3',
            name: 'Send Confirmation Email',
            triggerPhrase: 'send confirmation email',
            action: 'Send email',
            parameters: [
              { name: 'email', type: 'email', required: true },
              { name: 'template', type: 'string', required: false },
            ],
            enabled: false,
          },
        ] as Command[];
      }
      
      // In production, fetch from API
      const { data } = await axios.get<Command[]>('/api/ai/commands');
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Add command mutation
  const addCommandMutation = useMutation({
    mutationFn: async (command: Omit<Command, 'id'>) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Return mock success
        return { success: true, id: `${Date.now()}` };
      }
      
      // In production, call API
      const { data } = await axios.post('/api/ai/commands', command);
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['ai', 'commands'] });
      
      // Reset form
      setNewCommand({
        name: '',
        triggerPhrase: '',
        action: '',
        enabled: true,
      });
      setShowAddForm(false);
      
      // Show success toast
      console.log('Command added successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to add command');
    },
  });

  // Update command mutation
  const updateCommandMutation = useMutation({
    mutationFn: async (command: Command) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.put(`/api/ai/commands/${command.id}`, command);
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['ai', 'commands'] });
      
      // Reset editing state
      setEditingId(null);
      setEditCommand(null);
      
      // Show success toast
      console.log('Command updated successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to update command');
    },
  });

  // Delete command mutation
  const deleteCommandMutation = useMutation({
    mutationFn: async (id: string) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.delete(`/api/ai/commands/${id}`);
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['ai', 'commands'] });
      
      // Show success toast
      console.log('Command deleted successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to delete command');
    },
  });

  // Toggle command mutation
  const toggleCommandMutation = useMutation({
    mutationFn: async ({ id, enabled }: { id: string; enabled: boolean }) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.patch(`/api/ai/commands/${id}`, { enabled });
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['ai', 'commands'] });
      
      // Show success toast
      console.log('Command status updated successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to update command status');
    },
  });

  // Handle add command form submission
  const handleAddCommand = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!newCommand.name.trim() || !newCommand.triggerPhrase.trim() || !newCommand.action.trim()) {
      return;
    }
    
    // Submit form
    addCommandMutation.mutate(newCommand);
  };

  // Handle edit command form submission
  const handleUpdateCommand = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!editCommand || !editCommand.name.trim() || !editCommand.triggerPhrase.trim() || !editCommand.action.trim()) {
      return;
    }
    
    // Submit form
    updateCommandMutation.mutate(editCommand);
  };

  // Start editing a command
  const startEditing = (command: Command) => {
    setEditCommand({ ...command });
    setEditingId(command.id);
  };

  // Handle toggle command
  const handleToggleCommand = (id: string, enabled: boolean) => {
    toggleCommandMutation.mutate({ id, enabled });
  };

  // If loading, show loading spinner
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" color="blue" />
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-md">
        <h3 className="text-sm font-medium text-red-800 dark:text-red-300">
          Failed to load commands
        </h3>
        <p className="mt-2 text-sm text-red-700 dark:text-red-200">
          We couldn't load your custom commands. Please try again later.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Custom Commands
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Create custom commands for your AI assistant to perform specific actions.
          </p>
        </div>
        <button
          type="button"
          onClick={() => setShowAddForm(!showAddForm)}
          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
        >
          <PlusIcon className="h-4 w-4 mr-1" />
          Add Command
        </button>
      </div>

      {/* Add Command Form */}
      {showAddForm && (
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-3">
            Add Custom Command
          </h3>
          <form onSubmit={handleAddCommand} className="space-y-4">
            <div>
              <label htmlFor="commandName" className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">
                Command Name
              </label>
              <input
                type="text"
                id="commandName"
                value={newCommand.name}
                onChange={(e) => setNewCommand({ ...newCommand, name: e.target.value })}
                placeholder="e.g., Schedule Appointment"
                className="block w-full border-blue-300 dark:border-blue-700 dark:bg-blue-900/30 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                disabled={addCommandMutation.isPending}
                required
              />
            </div>
            <div>
              <label htmlFor="triggerPhrase" className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">
                Trigger Phrase
              </label>
              <input
                type="text"
                id="triggerPhrase"
                value={newCommand.triggerPhrase}
                onChange={(e) => setNewCommand({ ...newCommand, triggerPhrase: e.target.value })}
                placeholder="e.g., schedule appointment"
                className="block w-full border-blue-300 dark:border-blue-700 dark:bg-blue-900/30 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                disabled={addCommandMutation.isPending}
                required
              />
              <p className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                This is what the customer will say to trigger the command.
              </p>
            </div>
            <div>
              <label htmlFor="action" className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">
                Action
              </label>
              <input
                type="text"
                id="action"
                value={newCommand.action}
                onChange={(e) => setNewCommand({ ...newCommand, action: e.target.value })}
                placeholder="e.g., Create calendar event"
                className="block w-full border-blue-300 dark:border-blue-700 dark:bg-blue-900/30 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                disabled={addCommandMutation.isPending}
                required
              />
              <p className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                Describe what this command will do.
              </p>
            </div>
            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="inline-flex items-center px-3 py-2 border border-blue-300 dark:border-blue-700 shadow-sm text-sm leading-4 font-medium rounded-md text-blue-700 dark:text-blue-400 bg-white dark:bg-gray-800 hover:bg-blue-50 dark:hover:bg-blue-900/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                disabled={addCommandMutation.isPending}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={addCommandMutation.isPending || !newCommand.name.trim() || !newCommand.triggerPhrase.trim() || !newCommand.action.trim()}
              >
                {addCommandMutation.isPending ? (
                  <>
                    <LoadingSpinner size="small" color="white" />
                    <span className="ml-2">Adding...</span>
                  </>
                ) : (
                  'Add Command'
                )}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Commands List */}
      {commands && commands.length > 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <ul className="divide-y divide-gray-200 dark:divide-gray-700">
            {commands.map((command) => (
              <li key={command.id} className="p-4">
                {editingId === command.id && editCommand ? (
                  // Edit Mode
                  <form onSubmit={handleUpdateCommand} className="space-y-4">
                    <div>
                      <label htmlFor={`edit-name-${command.id}`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Command Name
                      </label>
                      <input
                        type="text"
                        id={`edit-name-${command.id}`}
                        value={editCommand.name}
                        onChange={(e) => setEditCommand({ ...editCommand, name: e.target.value })}
                        className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        disabled={updateCommandMutation.isPending}
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor={`edit-trigger-${command.id}`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Trigger Phrase
                      </label>
                      <input
                        type="text"
                        id={`edit-trigger-${command.id}`}
                        value={editCommand.triggerPhrase}
                        onChange={(e) => setEditCommand({ ...editCommand, triggerPhrase: e.target.value })}
                        className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        disabled={updateCommandMutation.isPending}
                        required
                      />
                    </div>
                    <div>
                      <label htmlFor={`edit-action-${command.id}`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Action
                      </label>
                      <input
                        type="text"
                        id={`edit-action-${command.id}`}
                        value={editCommand.action}
                        onChange={(e) => setEditCommand({ ...editCommand, action: e.target.value })}
                        className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        disabled={updateCommandMutation.isPending}
                        required
                      />
                    </div>
                    <div className="flex justify-end space-x-2">
                      <button
                        type="button"
                        onClick={() => {
                          setEditingId(null);
                          setEditCommand(null);
                        }}
                        className="inline-flex items-center p-1 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                        disabled={updateCommandMutation.isPending}
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                      <button
                        type="submit"
                        className="inline-flex items-center p-1 border border-transparent text-sm font-medium rounded text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={updateCommandMutation.isPending || !editCommand.name.trim() || !editCommand.triggerPhrase.trim() || !editCommand.action.trim()}
                      >
                        <CheckIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </form>
                ) : (
                  // View Mode
                  <div>
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                          {command.name}
                        </h3>
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          Trigger: "{command.triggerPhrase}"
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={command.enabled}
                          onChange={(enabled) => handleToggleCommand(command.id, enabled)}
                          disabled={toggleCommandMutation.isPending}
                          className={`${
                            command.enabled ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
                          } relative inline-flex h-5 w-10 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 ${
                            toggleCommandMutation.isPending ? 'opacity-50 cursor-not-allowed' : ''
                          }`}
                        >
                          <span
                            className={`${
                              command.enabled ? 'translate-x-5' : 'translate-x-1'
                            } inline-block h-3 w-3 transform rounded-full bg-white transition-transform`}
                          />
                        </Switch>
                        <button
                          type="button"
                          onClick={() => startEditing(command)}
                          disabled={updateCommandMutation.isPending}
                          className="text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          type="button"
                          onClick={() => deleteCommandMutation.mutate(command.id)}
                          disabled={deleteCommandMutation.isPending}
                          className="text-gray-400 hover:text-red-500 dark:hover:text-red-400 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                    <div className="mt-2">
                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        <span className="font-medium">Action:</span> {command.action}
                      </div>
                      {command.parameters && command.parameters.length > 0 && (
                        <div className="mt-2">
                          <span className="text-xs font-medium text-gray-500 dark:text-gray-400">
                            Parameters:
                          </span>
                          <ul className="mt-1 text-xs text-gray-500 dark:text-gray-400 space-y-1">
                            {command.parameters.map((param, index) => (
                              <li key={index}>
                                {param.name} ({param.type}){param.required ? ' (required)' : ''}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </li>
            ))}
          </ul>
        </div>
      ) : (
        <EmptyState
          title="No custom commands yet"
          description="Create custom commands to enable your AI assistant to perform specific actions when triggered by certain phrases."
          icon={<PlusIcon className="h-12 w-12 text-gray-400" />}
          actionText="Add Command"
          onAction={() => setShowAddForm(true)}
        />
      )}

      <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-md p-4 text-sm">
        <h3 className="font-medium text-yellow-800 dark:text-yellow-300 mb-1">
          About Custom Commands
        </h3>
        <p className="text-yellow-700 dark:text-yellow-200">
          Custom commands allow your AI assistant to perform specific actions when it recognizes certain phrases during calls or in messages. For example, you can create commands to schedule appointments, check order status, or send confirmation emails.
        </p>
      </div>
    </div>
  );
}
