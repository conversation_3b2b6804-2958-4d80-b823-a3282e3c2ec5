/**
 * eSIM Service
 * 
 * This service manages eSIM operations across different providers.
 * It provides a unified interface for provisioning, managing, and monitoring eSIM profiles.
 */

const logger = require('../utils/logger');
const config = require('../config');
const { createProvider } = require('./providers');
const { validateProfile, mapProviderProfile } = require('./models/esimProfile');
const crypto = require('crypto');

/**
 * Initialize the eSIM service
 * 
 * @param {Object} options - Configuration options
 * @returns {Object} eSIM service interface
 */
function initializeEsimService(options = {}) {
  // Check if eSIM is enabled
  const enabled = options.enabled !== undefined ? options.enabled : config.esim?.enabled || false;
  const defaultProvider = options.defaultProvider || config.esim?.defaultProvider || 'mock';
  
  logger.info('Initializing eSIM service', { enabled, defaultProvider });
  
  // In-memory storage for provider instances
  const providers = new Map();
  
  /**
   * Get or create a provider instance
   * 
   * @param {string} providerName - Provider name
   * @returns {Object} Provider instance
   */
  function getProvider(providerName) {
    const provider = providerName || defaultProvider;
    
    if (!providers.has(provider)) {
      // Get provider configuration
      const providerConfig = config.esim?.providers?.[provider] || {};
      
      // Create provider instance
      const instance = createProvider(provider, providerConfig);
      providers.set(provider, instance);
    }
    
    return providers.get(provider);
  }
  
  /**
   * Check if eSIM is enabled for a specific user
   * 
   * @param {string} userId - User ID
   * @param {string} phoneNumber - Phone number
   * @returns {boolean} Whether eSIM is enabled
   */
  function isEnabledForUser(userId, phoneNumber) {
    if (!enabled) return false;
    
    // Get enabled users and numbers
    const enabledUsers = config.esim?.esimEnabledUsers || [];
    const enabledNumbers = config.esim?.esimEnabledNumbers || [];
    
    // Check if user or number is explicitly enabled
    if (enabledUsers.length > 0 && userId) {
      return enabledUsers.includes(userId.toString());
    }
    
    if (enabledNumbers.length > 0 && phoneNumber) {
      return enabledNumbers.includes(phoneNumber);
    }
    
    // If no specific users or numbers are configured, eSIM is enabled for all
    return true;
  }
  
  /**
   * Get available eSIM packages/numbers
   * 
   * @param {Object} criteria - Search criteria
   * @param {Object} options - Search options
   * @returns {Promise<Array>} Available packages
   */
  async function getAvailableNumbers(criteria = {}, options = {}) {
    if (!enabled) {
      logger.warn('eSIM service is disabled');
      return [];
    }
    
    const provider = getProvider(options.provider);
    
    try {
      return await provider.getAvailablePackages(criteria, options);
    } catch (error) {
      logger.error('Error getting available eSIM packages', { error, criteria });
      throw error;
    }
  }
  
  /**
   * Provision a new eSIM number
   * 
   * @param {Object} details - Provisioning details
   * @param {Object} options - Provisioning options
   * @returns {Promise<Object>} Provisioned profile
   */
  async function provisionNumber(details, options = {}) {
    if (!enabled) {
      logger.warn('eSIM service is disabled');
      throw new Error('eSIM service is disabled');
    }
    
    const provider = getProvider(options.provider);
    const profileId = crypto.randomUUID();
    
    try {
      // Add metadata for tracking
      const metadata = {
        ...(details.metadata || {}),
        createdAt: new Date().toISOString(),
        userId: options.userId || details.userId,
        source: options.source || 'api'
      };
      
      // Provision with provider
      const providerProfile = await provider.provisionProfile({
        ...details,
        metadata
      });
      
      // Map to standard profile format
      const profile = mapProviderProfile(providerProfile, {
        id: profileId,
        userId: options.userId || details.userId,
        provider: options.provider || defaultProvider,
        metadata
      });
      
      // Validate profile
      return validateProfile(profile);
    } catch (error) {
      logger.error('Error provisioning eSIM number', { error, details });
      throw error;
    }
  }
  
  /**
   * Generate activation QR code
   * 
   * @param {string} profileId - Profile ID
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} QR code data
   */
  async function generateActivationQR(profileId, options = {}) {
    if (!enabled) {
      logger.warn('eSIM service is disabled');
      throw new Error('eSIM service is disabled');
    }
    
    const provider = getProvider(options.provider);
    
    try {
      return await provider.generateActivationQR(profileId, options);
    } catch (error) {
      logger.error('Error generating eSIM activation QR', { error, profileId });
      throw error;
    }
  }
  
  /**
   * Check activation status
   * 
   * @param {string} profileId - Profile ID
   * @param {Object} options - Check options
   * @returns {Promise<Object>} Activation status
   */
  async function checkActivationStatus(profileId, options = {}) {
    if (!enabled) {
      logger.warn('eSIM service is disabled');
      throw new Error('eSIM service is disabled');
    }
    
    const provider = getProvider(options.provider);
    
    try {
      return await provider.checkActivationStatus(profileId, options);
    } catch (error) {
      logger.error('Error checking eSIM activation status', { error, profileId });
      throw error;
    }
  }
  
  /**
   * Get data usage
   * 
   * @param {string} profileId - Profile ID
   * @param {Object} options - Usage options
   * @returns {Promise<Object>} Data usage
   */
  async function getDataUsage(profileId, options = {}) {
    if (!enabled) {
      logger.warn('eSIM service is disabled');
      throw new Error('eSIM service is disabled');
    }
    
    const provider = getProvider(options.provider);
    
    try {
      return await provider.getDataUsage(profileId, options);
    } catch (error) {
      logger.error('Error getting eSIM data usage', { error, profileId });
      throw error;
    }
  }
  
  /**
   * Purchase a data package
   * 
   * @param {string} profileId - Profile ID
   * @param {Object} packageDetails - Package to purchase
   * @param {Object} options - Purchase options
   * @returns {Promise<Object>} Purchase confirmation
   */
  async function purchaseDataPackage(profileId, packageDetails, options = {}) {
    if (!enabled) {
      logger.warn('eSIM service is disabled');
      throw new Error('eSIM service is disabled');
    }
    
    const provider = getProvider(options.provider);
    
    try {
      return await provider.purchaseDataPackage(profileId, packageDetails, options);
    } catch (error) {
      logger.error('Error purchasing eSIM data package', { error, profileId, packageDetails });
      throw error;
    }
  }
  
  /**
   * Deactivate an eSIM profile
   * 
   * @param {string} profileId - Profile ID
   * @param {Object} options - Deactivation options
   * @returns {Promise<boolean>} Success indicator
   */
  async function deactivateProfile(profileId, options = {}) {
    if (!enabled) {
      logger.warn('eSIM service is disabled');
      throw new Error('eSIM service is disabled');
    }
    
    const provider = getProvider(options.provider);
    
    try {
      return await provider.deactivateProfile(profileId, options);
    } catch (error) {
      logger.error('Error deactivating eSIM profile', { error, profileId });
      throw error;
    }
  }
  
  // Return eSIM service interface
  return {
    isEnabled: () => enabled,
    isEnabledForUser,
    getAvailableNumbers,
    provisionNumber,
    generateActivationQR,
    checkActivationStatus,
    getDataUsage,
    purchaseDataPackage,
    deactivateProfile
  };
}

// Export a singleton instance with default options
module.exports = initializeEsimService();
