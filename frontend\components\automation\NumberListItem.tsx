import { PhoneNumber } from './AutomationLayout';
import { PhoneIcon } from '@heroicons/react/24/outline';

interface NumberListItemProps {
  number: PhoneNumber;
  isSelected: boolean;
  onSelect: () => void;
}

export default function NumberListItem({
  number,
  isSelected,
  onSelect,
}: NumberListItemProps) {
  return (
    <li
      className={`relative cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${
        isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''
      }`}
      onClick={onSelect}
    >
      <div className="px-4 py-4 sm:px-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <PhoneIcon className="h-6 w-6 text-gray-400" />
            </div>
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {number.friendlyName || 'Unnamed Number'}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {number.phoneNumber}
              </div>
            </div>
          </div>
          <div className="ml-2 flex-shrink-0">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                number.automationStatus === 'active'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
              }`}
            >
              {number.automationStatus === 'active' ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>
      </div>
      {isSelected && (
        <div className="absolute inset-y-0 left-0 w-1 bg-blue-600 dark:bg-blue-500"></div>
      )}
    </li>
  );
}
