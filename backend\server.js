const express = require('express');
const cors = require('cors');
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3006;
const prisma = new PrismaClient();

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());

// Health check route
app.get('/', (req, res) => {
  res.send('Callsaver V2 Backend API Running');
});

// API Routes
app.get('/api', (req, res) => {
  res.json({
    message: 'Callsaver V2 API',
    version: '2.0.0',
    endpoints: [
      '/api/calls',
      '/api/numbers',
      '/api/messages',
      '/api/automation',
      '/api/users'
    ]
  });
});

// Import the calls router
const callsRouter = require('./routes/calls');
app.use('/api/calls', callsRouter);

// Import the numbers router
const numbersRouter = require('./routes/numbers');
app.use('/api/numbers', numbersRouter);

// Import the messages router
const messagesRouter = require('./routes/messages');
app.use('/api/messages', messagesRouter);

// Import the automation router
const automationRouter = require('./routes/automation');
app.use('/api/automation', automationRouter);

// Create a users router for user-specific endpoints
const express_router = express.Router();

// Get user's phone numbers
express_router.get('/numbers', async (req, res) => {
  try {
    // In a real implementation, you would extract the user ID from the JWT token
    // For now, we'll return a mock response
    res.json({
      success: true,
      count: 2,
      data: [
        {
          id: "1",
          number: "+14155552671",
          friendlyName: "(*************",
          countryCode: "US",
          region: "California",
          locality: "San Francisco",
          isActive: true,
          isPrimary: true,
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
          capabilities: {
            voice: true,
            sms: true,
            fax: false
          }
        },
        {
          id: "2",
          number: "+14255551234",
          friendlyName: "(*************",
          countryCode: "US",
          region: "Washington",
          locality: "Seattle",
          isActive: true,
          isPrimary: false,
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
          capabilities: {
            voice: true,
            sms: true,
            fax: false
          }
        }
      ]
    });
  } catch (error) {
    console.error('Error fetching user numbers:', error);
    res.status(500).json({ success: false, message: 'Internal server error' });
  }
});

// Mount the users router
app.use('/api/users', express_router);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});