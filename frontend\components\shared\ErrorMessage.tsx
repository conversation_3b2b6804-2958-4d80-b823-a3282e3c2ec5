'use client';

import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';

interface ErrorMessageProps {
  title?: string;
  message: string;
  error?: Error | null;
  onRetry?: () => void;
}

export default function ErrorMessage({ title = 'Error', message, error, onRetry }: ErrorMessageProps) {
  const isDevelopment = process.env.NODE_ENV === 'development';

  return (
    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 sm:p-4 text-center">
      <ExclamationTriangleIcon className="h-8 w-8 sm:h-10 sm:w-10 text-red-500 dark:text-red-400 mx-auto mb-2 sm:mb-3" />
      <h3 className="text-base sm:text-lg font-medium text-red-800 dark:text-red-300 mb-1.5 sm:mb-2">{title}</h3>
      <p className="text-xs sm:text-sm text-red-600 dark:text-red-400 mb-3 sm:mb-4">{message}</p>

      {/* Show error details in development */}
      {isDevelopment && error && (
        <div className="mt-1.5 sm:mt-2 p-1.5 sm:p-2 bg-red-100 dark:bg-red-900/30 rounded text-left overflow-auto max-h-24 sm:max-h-32 text-xs text-red-800 dark:text-red-300 font-mono">
          <p className="font-semibold">Error: {error.message}</p>
          {error.stack && (
            <pre className="mt-1 whitespace-pre-wrap">{error.stack}</pre>
          )}
        </div>
      )}

      {onRetry && (
        <button
          type="button"
          onClick={onRetry}
          className="mt-2 sm:mt-3 px-3 sm:px-4 py-1.5 sm:py-2 bg-red-600 hover:bg-red-700 text-white text-xs sm:text-sm rounded-lg transition-colors duration-150"
        >
          Try Again
        </button>
      )}
    </div>
  );
}
