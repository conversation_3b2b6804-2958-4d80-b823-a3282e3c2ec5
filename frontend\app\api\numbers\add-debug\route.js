'use server';

import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// Create a new Prisma client
const prisma = new PrismaClient();

// This route is for DEBUG/DEVELOPMENT purposes ONLY!
// It allows adding a test phone number directly to the database
export async function POST(request) {
  console.log('[API Debug] Add test phone number request received');
  
  // Only allow in development mode
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json({
      success: false,
      message: 'This endpoint is only available in development mode'
    }, { status: 403 });
  }
  
  try {
    // Get authentication info from Supabase
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      { 
        cookies: { 
          get: (name) => cookieStore.get(name)?.value
        } 
      }
    );
    
    // Get user session for authentication
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      console.log('[API Debug] No valid session found');
      return NextResponse.json({
        success: false,
        message: 'Authentication required'
      }, { status: 401 });
    }
    
    // Parse request body
    const requestData = await request.json();
    
    // Generate a random phone number if not provided
    const phoneNumber = requestData.phoneNumber || `+1${Math.floor(Math.random() * 9000000000) + 1000000000}`;
    const countryCode = requestData.countryCode || 'US';
    
    // Get user from database
    const user = await prisma.user.findUnique({
      where: { 
        email: session.user.email 
      }
    });
    
    if (!user) {
      return NextResponse.json({
        success: false,
        message: 'User not found in database'
      }, { status: 404 });
    }
    
    // Check if phone number already exists
    const existingNumber = await prisma.phoneNumber.findUnique({
      where: {
        number: phoneNumber
      }
    });
    
    if (existingNumber) {
      return NextResponse.json({
        success: false,
        message: 'Phone number already exists',
        phoneNumber: existingNumber
      }, { status: 409 });
    }
    
    // Add the phone number to the database
    const newPhoneNumber = await prisma.phoneNumber.create({
      data: {
        number: phoneNumber,
        countryCode: countryCode,
        providerId: 'debug-provider',
        userId: user.id,
        isActive: true
      }
    });
    
    console.log(`[API Debug] Added test phone number ${phoneNumber} for user ${user.id}`);
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Test phone number added successfully',
      phoneNumber: {
        ...newPhoneNumber,
        createdAt: newPhoneNumber.createdAt?.toISOString(),
        updatedAt: newPhoneNumber.updatedAt?.toISOString()
      }
    });
  } catch (error) {
    console.error('[API Debug] Error adding test phone number:', error);
    return NextResponse.json({ 
      success: false, 
      message: `Error adding test phone number: ${error.message}`
    }, { status: 500 });
  } finally {
    // Disconnect Prisma
    await prisma.$disconnect();
  }
} 