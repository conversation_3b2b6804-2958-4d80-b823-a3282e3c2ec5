"use client";

import { useState } from 'react';
import { 
  PhoneArrowUpRightIcon, 
  PhoneArrowDownLeftIcon,
  PhoneXMarkIcon,
  ChatBubbleLeftEllipsisIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

export default function RecentCallsList({ calls = [] }) {
  const [expandedCall, setExpandedCall] = useState(null);

  // Helper function to format phone numbers nicely
  const formatPhoneNumber = (phoneNumber) => {
    if (!phoneNumber) return 'Unknown';
    
    // If it already includes formatting, return as is
    if (phoneNumber.includes(' ') || phoneNumber.includes('-')) {
      return phoneNumber;
    }
    
    // Basic formatting for US numbers
    if (phoneNumber.length === 10) {
      return `(${phoneNumber.substring(0, 3)}) ${phoneNumber.substring(3, 6)}-${phoneNumber.substring(6)}`;
    }
    
    // For international format with country code
    if (phoneNumber.startsWith('+')) {
      // Keep country code, then format rest if it's a US number (+1)
      if (phoneNumber.startsWith('+1') && phoneNumber.length === 12) {
        return `+1 (${phoneNumber.substring(2, 5)}) ${phoneNumber.substring(5, 8)}-${phoneNumber.substring(8)}`;
      }
      return phoneNumber; 
    }
    
    // Default case
    return phoneNumber;
  };
  
  // Format duration in minutes and seconds
  const formatDuration = (seconds) => {
    if (!seconds && seconds !== 0) return 'N/A';
    
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Format timestamp nicely
  const formatTime = (timestamp) => {
    if (!timestamp) return 'Unknown time';
    
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffMs = now - date;
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      
      // Today
      if (diffDays === 0) {
        return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      }
      
      // Yesterday
      if (diffDays === 1) {
        return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      }
      
      // This week (less than 7 days ago)
      if (diffDays < 7) {
        return `${date.toLocaleDateString([], { weekday: 'long' })} at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
      }
      
      // Older
      return date.toLocaleDateString([], { year: 'numeric', month: 'short', day: 'numeric' });
    } catch (e) {
      console.error('Error formatting date:', e);
      return timestamp;
    }
  };
  
  // Get icon for call type
  const getCallIcon = (call) => {
    // First check the status
    if (call.status === 'missed') {
      return <PhoneXMarkIcon className="h-4 w-4 text-red-400" />;
    }
    
    if (call.status === 'voicemail') {
      return <ChatBubbleLeftEllipsisIcon className="h-4 w-4 text-blue-400" />;
    }
    
    // Then check direction
    if (call.type === 'outbound') {
      return <PhoneArrowUpRightIcon className="h-4 w-4 text-purple-400" />;
    } 
    
    // Default to inbound
    return <PhoneArrowDownLeftIcon className="h-4 w-4 text-green-400" />;
  };
  
  if (!calls || calls.length === 0) {
    return (
      <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg h-full">
        <h3 className="text-lg font-medium text-white flex items-center">
          <ClockIcon className="h-5 w-5 mr-2 text-purple-400" />
          Recent Calls
        </h3>
        <div className="mt-6 flex flex-col items-center justify-center text-center h-40">
          <p className="text-gray-400">No call history available yet</p>
          <p className="text-gray-500 text-sm mt-2">Your recent calls will appear here</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
      <h3 className="text-lg font-medium text-white flex items-center">
        <ClockIcon className="h-5 w-5 mr-2 text-purple-400" />
        Recent Calls
      </h3>
      
      <div className="mt-4 divide-y divide-gray-800/60">
        {calls.map((call) => (
          <div key={call.id} className="py-3 group">
            <div 
              className="cursor-pointer"
              onClick={() => setExpandedCall(expandedCall === call.id ? null : call.id)}
            >
              <div className="flex justify-between items-start">
                <div className="flex items-start">
                  <div className="mr-3 mt-1">
                    {getCallIcon(call)}
                  </div>
                  <div>
                    <p className="text-white font-medium">{formatPhoneNumber(call.from)}</p>
                    <p className="text-gray-400 text-xs flex items-center">
                      {call.type === 'inbound' ? 'Incoming' : 'Outgoing'} · {formatTime(call.timestamp)}
                    </p>
                  </div>
                </div>
                <div>
                  {call.duration > 0 && (
                    <span className="text-gray-500 text-xs">{formatDuration(call.duration)}</span>
                  )}
                </div>
              </div>
            </div>
            
            {expandedCall === call.id && (
              <div className="mt-3 p-3 bg-gray-800/30 rounded-lg border border-gray-700/50 text-sm">
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <p className="text-gray-500 text-xs">From</p>
                    <p className="text-gray-200">{formatPhoneNumber(call.from)}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 text-xs">To</p>
                    <p className="text-gray-200">{formatPhoneNumber(call.to)}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 text-xs">Time</p>
                    <p className="text-gray-200">{new Date(call.timestamp).toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-gray-500 text-xs">Duration</p>
                    <p className="text-gray-200">
                      {call.duration > 0 ? formatDuration(call.duration) : 'N/A'}
                    </p>
                  </div>
                </div>
                
                {call.status === 'voicemail' && (
                  <div className="mt-3 border-t border-gray-700/50 pt-3">
                    <p className="text-gray-500 text-xs mb-1">Voicemail</p>
                    <div className="flex space-x-3">
                      <button className="flex items-center justify-center py-1 px-3 bg-blue-600/50 hover:bg-blue-600/70 text-white rounded text-xs font-medium transition-colors">
                        <span>Play</span>
                      </button>
                      <button className="flex items-center justify-center py-1 px-3 bg-purple-600/50 hover:bg-purple-600/70 text-white rounded text-xs font-medium transition-colors">
                        <span>View Transcript</span>
                      </button>
                    </div>
                  </div>
                )}
                
                <div className="flex space-x-2 mt-3">
                  <button className="flex items-center justify-center py-1 px-3 bg-green-600/50 hover:bg-green-600/70 text-white rounded text-xs font-medium transition-colors">
                    <PhoneArrowUpRightIcon className="h-3 w-3 mr-1" />
                    <span>Call Back</span>
                  </button>
                  <button className="flex items-center justify-center py-1 px-3 bg-gray-700/50 hover:bg-gray-700/70 text-white rounded text-xs font-medium transition-colors">
                    <span>Add To Contacts</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
      
      {calls.length > 5 && (
        <div className="mt-4 text-center">
          <button className="text-sm text-purple-400 hover:text-purple-300 transition-colors">
            View All Call History
          </button>
        </div>
      )}
    </div>
  );
} 