# Input Validation and Sanitization

This document outlines the approach to input validation and sanitization in the CallSaver.app platform.

## Overview

Input validation and sanitization are critical security measures that help protect the application from various attacks, including:

- Cross-Site Scripting (XSS)
- SQL Injection
- Command Injection
- Path Traversal
- Denial of Service (DoS)

Our approach implements defense in depth by validating and sanitizing inputs at multiple levels:

1. **Middleware Level**: Global sanitization of all request inputs
2. **Schema Validation Level**: Structured validation using Joi schemas
3. **Controller Level**: Context-specific validation and sanitization

## Implementation Details

### 1. Global Input Sanitization Middleware

The `inputValidator.js` middleware provides global sanitization for all incoming requests:

- Automatically sanitizes `req.body`, `req.query`, and `req.params`
- Detects potential attack patterns using pattern matching
- Blocks high-severity attacks and logs suspicious activity
- Preserves raw input for specific routes that require it (e.g., webhooks)

```javascript
// Apply the middleware in server.js
app.use(sanitizeInputs);
```

### 2. Schema-Based Validation

The `schemaValidation.js` middleware provides structured validation using Joi schemas:

- Defines reusable schema components for common data types
- Validates request bodies against predefined schemas
- Strips unknown fields to prevent parameter pollution
- Returns consistent error messages for validation failures

```javascript
// Example usage in a route
router.post('/users', validateRequest('user', 'create'), userController.createUser);
```

### 3. Enhanced Validation with Sanitization

The `inputValidator.js` middleware also provides enhanced validation that combines validation and sanitization:

- Sanitizes inputs before validation
- Validates using Joi schemas
- Provides consistent error handling
- Supports different validation contexts

```javascript
// Example usage in a route
router.post('/users', validateAndSanitize('user', 'create'), userController.createUser);
```

### 4. Controller-Level Validation

For dynamic validation requirements, controllers can use helper functions:

- `validateRequestData`: Validates and sanitizes request data
- `validateField`: Validates and sanitizes individual fields
- `sanitizeObject`: Sanitizes object properties recursively
- `sanitizeString`: Sanitizes string inputs to prevent XSS

```javascript
// Example usage in a controller
const { success, data, errors } = validateRequestData(req, schema);
if (!success) {
  return res.status(400).json({ success: false, errors });
}
```

## Sanitization Techniques

The platform uses the following sanitization techniques:

1. **HTML Entity Encoding**: Converts special characters to their HTML entity equivalents
2. **Input Filtering**: Removes or escapes potentially dangerous characters
3. **Type Checking**: Ensures inputs are of the expected type
4. **Length Limiting**: Prevents buffer overflow attacks
5. **Pattern Matching**: Detects known attack patterns

## Attack Pattern Detection

The platform includes pattern detection for common attack vectors:

- SQL Injection patterns
- XSS attack patterns
- Path Traversal attempts
- Command Injection attempts
- NoSQL Injection patterns

## Best Practices

When implementing new features, follow these best practices:

1. **Validate All Inputs**: Every input from external sources must be validated
2. **Use Schema Validation**: Define Joi schemas for structured validation
3. **Sanitize Before Use**: Always sanitize inputs before using them in sensitive operations
4. **Validate Context**: Consider the context when validating (e.g., creation vs. update)
5. **Fail Securely**: Reject invalid inputs with appropriate error messages
6. **Log Validation Failures**: Track validation failures to detect potential attacks

## Example: Complete Validation Flow

```javascript
// 1. Define a schema in schemaValidation.js
const userSchema = Joi.object({
  name: Joi.string().min(2).max(50).required(),
  email: schemas.email,
  role: Joi.string().valid('user', 'admin').default('user')
});

// 2. Apply validation middleware in routes
router.post('/users', validateAndSanitize('user', 'create'), userController.createUser);

// 3. Additional validation in controller if needed
const createUser = async (req, res) => {
  // Input is already validated and sanitized by middleware
  const { name, email, role } = req.body;
  
  // Perform additional context-specific validation if needed
  if (role === 'admin' && !req.user.isAdmin) {
    return res.status(403).json({
      success: false,
      message: 'Unauthorized to create admin users'
    });
  }
  
  // Proceed with creating the user
  // ...
};
```

## Testing Validation and Sanitization

The platform includes comprehensive tests for validation and sanitization:

- Unit tests for sanitization functions
- Integration tests for validation middleware
- Security tests for attack pattern detection
- Regression tests for known vulnerabilities

Run the tests using:

```bash
npm test
```

## Conclusion

Input validation and sanitization are critical security measures that help protect the application from various attacks. By implementing a multi-layered approach, we ensure that all inputs are properly validated and sanitized before being processed by the application.
