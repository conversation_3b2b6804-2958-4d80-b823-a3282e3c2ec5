import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import PermissionTooltip from '../../components/ui/PermissionTooltip';

// Mock the useRef implementation
jest.mock('react', () => {
  const originalReact = jest.requireActual('react');
  return {
    ...originalReact,
    useRef: jest.fn().mockImplementation(() => ({
      current: {
        getBoundingClientRect: () => ({
          width: 100,
          height: 50
        }),
        style: {}
      }
    }))
  };
});

describe('PermissionTooltip', () => {
  test('renders children correctly', () => {
    render(
      <PermissionTooltip message="You don't have permission">
        <button>Click me</button>
      </PermissionTooltip>
    );
    
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  test('shows tooltip on mouse enter', () => {
    render(
      <PermissionTooltip message="You don't have permission">
        <button>Click me</button>
      </PermissionTooltip>
    );
    
    // Tooltip should not be visible initially
    expect(screen.queryByText("You don't have permission")).not.toBeInTheDocument();
    
    // Trigger mouse enter
    fireEvent.mouseEnter(screen.getByText('Click me'));
    
    // Tooltip should be visible
    expect(screen.getByText("You don't have permission")).toBeInTheDocument();
  });

  test('hides tooltip on mouse leave', () => {
    render(
      <PermissionTooltip message="You don't have permission">
        <button>Click me</button>
      </PermissionTooltip>
    );
    
    // Trigger mouse enter to show tooltip
    fireEvent.mouseEnter(screen.getByText('Click me'));
    
    // Tooltip should be visible
    expect(screen.getByText("You don't have permission")).toBeInTheDocument();
    
    // Trigger mouse leave
    fireEvent.mouseLeave(screen.getByText('Click me'));
    
    // Tooltip should not be visible
    expect(screen.queryByText("You don't have permission")).not.toBeInTheDocument();
  });

  test('renders with lock icon by default', () => {
    render(
      <PermissionTooltip message="You don't have permission">
        <button>Click me</button>
      </PermissionTooltip>
    );
    
    // Trigger mouse enter to show tooltip
    fireEvent.mouseEnter(screen.getByText('Click me'));
    
    // There should be an SVG icon (the lock)
    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });

  test('can hide lock icon when specified', () => {
    render(
      <PermissionTooltip message="You don't have permission" showIcon={false}>
        <button>Click me</button>
      </PermissionTooltip>
    );
    
    // Trigger mouse enter to show tooltip
    fireEvent.mouseEnter(screen.getByText('Click me'));
    
    // There should not be an SVG icon
    const svg = document.querySelector('svg');
    expect(svg).not.toBeInTheDocument();
  });

  test('applies custom class names', () => {
    render(
      <PermissionTooltip 
        message="You don't have permission" 
        className="custom-wrapper"
        tooltipClassName="custom-tooltip"
      >
        <button>Click me</button>
      </PermissionTooltip>
    );
    
    // Check wrapper class
    expect(screen.getByText('Click me').parentElement).toHaveClass('custom-wrapper');
    
    // Trigger mouse enter to show tooltip
    fireEvent.mouseEnter(screen.getByText('Click me'));
    
    // Check tooltip class
    const tooltip = screen.getByText("You don't have permission").parentElement?.parentElement;
    expect(tooltip).toHaveClass('custom-tooltip');
  });

  test('positions tooltip based on position prop', () => {
    // Test top position (default)
    render(
      <PermissionTooltip message="Top tooltip">
        <button>Top</button>
      </PermissionTooltip>
    );
    
    fireEvent.mouseEnter(screen.getByText('Top'));
    let tooltip = screen.getByText('Top tooltip').parentElement?.parentElement;
    expect(tooltip).toHaveClass('bottom-full');
    
    // Cleanup
    fireEvent.mouseLeave(screen.getByText('Top'));
    
    // Test bottom position
    render(
      <PermissionTooltip message="Bottom tooltip" position="bottom">
        <button>Bottom</button>
      </PermissionTooltip>
    );
    
    fireEvent.mouseEnter(screen.getByText('Bottom'));
    tooltip = screen.getByText('Bottom tooltip').parentElement?.parentElement;
    expect(tooltip).toHaveClass('top-full');
    
    // Cleanup
    fireEvent.mouseLeave(screen.getByText('Bottom'));
    
    // Test left position
    render(
      <PermissionTooltip message="Left tooltip" position="left">
        <button>Left</button>
      </PermissionTooltip>
    );
    
    fireEvent.mouseEnter(screen.getByText('Left'));
    tooltip = screen.getByText('Left tooltip').parentElement?.parentElement;
    expect(tooltip).toHaveClass('right-full');
    
    // Cleanup
    fireEvent.mouseLeave(screen.getByText('Left'));
    
    // Test right position
    render(
      <PermissionTooltip message="Right tooltip" position="right">
        <button>Right</button>
      </PermissionTooltip>
    );
    
    fireEvent.mouseEnter(screen.getByText('Right'));
    tooltip = screen.getByText('Right tooltip').parentElement?.parentElement;
    expect(tooltip).toHaveClass('left-full');
  });
});
