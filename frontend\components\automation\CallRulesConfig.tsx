'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { AutomationConfig } from './AutomationConfigPanel';
import LoadingSpinner from '../shared/LoadingSpinner';

interface CallRulesConfigProps {
  numberId: string;
  initialData: AutomationConfig['callRules'];
}

export default function CallRulesConfig({
  numberId,
  initialData,
}: CallRulesConfigProps) {
  // State for form data
  const [formData, setFormData] = useState(initialData);
  
  // Get the query client
  const queryClient = useQueryClient();

  // Mutation for updating call rules
  const updateMutation = useMutation({
    mutationFn: async (data: AutomationConfig['callRules']) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data: response } = await axios.put(`/api/numbers/${numberId}/automations`, {
        callRules: data,
      });
      return response;
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['automations', numberId] });
      
      // Show success toast (you can use a toast library like react-hot-toast)
      console.log('Call rules updated successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to update call rules');
    },
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateMutation.mutate(formData);
  };

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    // Handle checkbox inputs
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({
        ...prev,
        [name]: checked,
      }));
      return;
    }
    
    // Handle action selection
    if (name === 'action') {
      setFormData(prev => ({
        ...prev,
        action: value as 'forward' | 'voicemail' | 'ai_answer',
      }));
      return;
    }
    
    // Handle schedule rules
    if (name.startsWith('scheduleRules.')) {
      const field = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        scheduleRules: {
          ...prev.scheduleRules,
          [field]: value,
        },
      }));
      return;
    }
    
    // Handle other inputs
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle day selection for schedule
  const handleDayToggle = (day: string) => {
    const currentDays = formData.scheduleRules?.days || [];
    const newDays = currentDays.includes(day)
      ? currentDays.filter(d => d !== day)
      : [...currentDays, day];
    
    setFormData(prev => ({
      ...prev,
      scheduleRules: {
        ...prev.scheduleRules,
        days: newDays,
      },
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Call Handling
        </label>
        <select
          name="action"
          value={formData.action}
          onChange={handleChange}
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-700 dark:bg-gray-800 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          disabled={updateMutation.isPending}
        >
          <option value="forward">Forward to another number</option>
          <option value="voicemail">Send to voicemail</option>
          <option value="ai_answer">Let AI assistant answer</option>
        </select>
      </div>

      {formData.action === 'forward' && (
        <div>
          <label htmlFor="forwardTo" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Forward To
          </label>
          <input
            type="tel"
            name="forwardTo"
            id="forwardTo"
            value={formData.forwardTo || ''}
            onChange={handleChange}
            placeholder="+****************"
            className="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            disabled={updateMutation.isPending}
          />
        </div>
      )}

      <div className="flex items-center">
        <input
          type="checkbox"
          name="scheduleActive"
          id="scheduleActive"
          checked={formData.scheduleActive}
          onChange={handleChange}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-700 rounded"
          disabled={updateMutation.isPending}
        />
        <label htmlFor="scheduleActive" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
          Only apply during specific hours
        </label>
      </div>

      {formData.scheduleActive && (
        <div className="pl-6 space-y-4 border-l-2 border-gray-200 dark:border-gray-700">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Days
            </label>
            <div className="flex flex-wrap gap-2">
              {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => (
                <button
                  key={day}
                  type="button"
                  onClick={() => handleDayToggle(day)}
                  className={`px-3 py-1 text-sm rounded-full ${
                    formData.scheduleRules?.days.includes(day)
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                  }`}
                  disabled={updateMutation.isPending}
                >
                  {day.charAt(0).toUpperCase() + day.slice(1, 3)}
                </button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="startTime" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Start Time
              </label>
              <input
                type="time"
                name="scheduleRules.startTime"
                id="startTime"
                value={formData.scheduleRules?.startTime || ''}
                onChange={handleChange}
                className="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                disabled={updateMutation.isPending}
              />
            </div>
            <div>
              <label htmlFor="endTime" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                End Time
              </label>
              <input
                type="time"
                name="scheduleRules.endTime"
                id="endTime"
                value={formData.scheduleRules?.endTime || ''}
                onChange={handleChange}
                className="mt-1 block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                disabled={updateMutation.isPending}
              />
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-end">
        <button
          type="submit"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={updateMutation.isPending}
        >
          {updateMutation.isPending ? (
            <>
              <LoadingSpinner size="small" color="white" />
              <span className="ml-2">Saving...</span>
            </>
          ) : (
            'Save Changes'
          )}
        </button>
      </div>
    </form>
  );
}
