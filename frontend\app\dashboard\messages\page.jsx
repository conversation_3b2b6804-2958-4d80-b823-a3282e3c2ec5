'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card';

export default function MessagesPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [messages, setMessages] = useState([]);
  const [conversations, setConversations] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState('all');
  const [messageType, setMessageType] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [newMessage, setNewMessage] = useState('');
  const [isSending, setIsSending] = useState(false);

  // Mock conversation data
  const mockConversations = [
    { id: 1, contact: '+1 (555) 123-4567', contact_name: '<PERSON>', last_message: 'Can you provide more information about your services?', last_message_time: '2023-05-15T10:30:00Z', unread_count: 0 },
    { id: 2, contact: '+1 (555) 234-5678', contact_name: 'Jane Doe', last_message: 'Thanks for your help!', last_message_time: '2023-05-14T14:45:00Z', unread_count: 2 },
    { id: 3, contact: '+1 (555) 345-6789', contact_name: 'Bob Johnson', last_message: 'I need to reschedule my appointment.', last_message_time: '2023-05-13T11:20:00Z', unread_count: 0 },
    { id: 4, contact: '+1 (555) 456-7890', contact_name: 'Alice Williams', last_message: 'Is there a discount for returning customers?', last_message_time: '2023-05-12T16:15:00Z', unread_count: 1 },
    { id: 5, contact: '+1 (555) 567-8901', contact_name: 'Charlie Brown', last_message: 'Please call me back when you get a chance.', last_message_time: '2023-05-11T09:10:00Z', unread_count: 0 },
  ];

  // Mock message data for a conversation
  const mockMessages = {
    1: [
      { id: 101, conversation_id: 1, from: '+1 (555) 123-4567', to: '+1 (555) 987-6543', body: 'Hello, I\'m interested in your services.', direction: 'inbound', status: 'delivered', timestamp: '2023-05-15T09:30:00Z', media_url: null },
      { id: 102, conversation_id: 1, from: '+1 (555) 987-6543', to: '+1 (555) 123-4567', body: 'Thank you for your interest! How can I help you today?', direction: 'outbound', status: 'delivered', timestamp: '2023-05-15T09:35:00Z', media_url: null },
      { id: 103, conversation_id: 1, from: '+1 (555) 123-4567', to: '+1 (555) 987-6543', body: 'Can you provide more information about your services?', direction: 'inbound', status: 'delivered', timestamp: '2023-05-15T10:30:00Z', media_url: null },
    ],
    2: [
      { id: 201, conversation_id: 2, from: '+1 (555) 234-5678', to: '+1 (555) 987-6543', body: 'Hi, I have a question about my recent order.', direction: 'inbound', status: 'delivered', timestamp: '2023-05-14T13:30:00Z', media_url: null },
      { id: 202, conversation_id: 2, from: '+1 (555) 987-6543', to: '+1 (555) 234-5678', body: 'Of course, I\'d be happy to help. What\'s your order number?', direction: 'outbound', status: 'delivered', timestamp: '2023-05-14T13:35:00Z', media_url: null },
      { id: 203, conversation_id: 2, from: '+1 (555) 234-5678', to: '+1 (555) 987-6543', body: 'It\'s #12345. I haven\'t received my confirmation email yet.', direction: 'inbound', status: 'delivered', timestamp: '2023-05-14T13:40:00Z', media_url: null },
      { id: 204, conversation_id: 2, from: '+1 (555) 987-6543', to: '+1 (555) 234-5678', body: 'I\'ve resent the confirmation email. You should receive it shortly.', direction: 'outbound', status: 'delivered', timestamp: '2023-05-14T13:45:00Z', media_url: null },
      { id: 205, conversation_id: 2, from: '+1 (555) 234-5678', to: '+1 (555) 987-6543', body: 'Got it! Thanks for your help!', direction: 'inbound', status: 'delivered', timestamp: '2023-05-14T14:45:00Z', media_url: null },
    ],
    3: [
      { id: 301, conversation_id: 3, from: '+1 (555) 345-6789', to: '+1 (555) 987-6543', body: 'I need to reschedule my appointment for tomorrow.', direction: 'inbound', status: 'delivered', timestamp: '2023-05-13T10:30:00Z', media_url: null },
      { id: 302, conversation_id: 3, from: '+1 (555) 987-6543', to: '+1 (555) 345-6789', body: 'I\'d be happy to help you reschedule. What time works better for you?', direction: 'outbound', status: 'delivered', timestamp: '2023-05-13T10:35:00Z', media_url: null },
      { id: 303, conversation_id: 3, from: '+1 (555) 345-6789', to: '+1 (555) 987-6543', body: 'Can we do next Monday at 2 PM instead?', direction: 'inbound', status: 'delivered', timestamp: '2023-05-13T10:40:00Z', media_url: null },
      { id: 304, conversation_id: 3, from: '+1 (555) 987-6543', to: '+1 (555) 345-6789', body: 'Monday at 2 PM works great. I\'ve updated your appointment.', direction: 'outbound', status: 'delivered', timestamp: '2023-05-13T10:45:00Z', media_url: null },
      { id: 305, conversation_id: 3, from: '+1 (555) 345-6789', to: '+1 (555) 987-6543', body: 'I need to reschedule my appointment.', direction: 'inbound', status: 'delivered', timestamp: '2023-05-13T11:20:00Z', media_url: null },
    ],
    4: [
      { id: 401, conversation_id: 4, from: '+1 (555) 456-7890', to: '+1 (555) 987-6543', body: 'Do you offer any discounts?', direction: 'inbound', status: 'delivered', timestamp: '2023-05-12T15:30:00Z', media_url: null },
      { id: 402, conversation_id: 4, from: '+1 (555) 987-6543', to: '+1 (555) 456-7890', body: 'We offer a 10% discount for first-time customers and a loyalty program for returning customers.', direction: 'outbound', status: 'delivered', timestamp: '2023-05-12T15:35:00Z', media_url: null },
      { id: 403, conversation_id: 4, from: '+1 (555) 456-7890', to: '+1 (555) 987-6543', body: 'Is there a discount for returning customers?', direction: 'inbound', status: 'delivered', timestamp: '2023-05-12T16:15:00Z', media_url: null },
    ],
    5: [
      { id: 501, conversation_id: 5, from: '+1 (555) 567-8901', to: '+1 (555) 987-6543', body: 'Hello, I left a voicemail earlier today.', direction: 'inbound', status: 'delivered', timestamp: '2023-05-11T08:30:00Z', media_url: null },
      { id: 502, conversation_id: 5, from: '+1 (555) 987-6543', to: '+1 (555) 567-8901', body: 'I apologize for missing your call. How can I assist you?', direction: 'outbound', status: 'delivered', timestamp: '2023-05-11T08:45:00Z', media_url: null },
      { id: 503, conversation_id: 5, from: '+1 (555) 567-8901', to: '+1 (555) 987-6543', body: 'Please call me back when you get a chance.', direction: 'inbound', status: 'delivered', timestamp: '2023-05-11T09:10:00Z', media_url: null },
    ],
  };

  useEffect(() => {
    const fetchConversations = async () => {
      try {
        setIsLoading(true);
        // In a real implementation, this would be an API call to get conversations
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Filter conversations based on search term
        let filteredConversations = [...mockConversations];
        
        if (searchTerm) {
          filteredConversations = filteredConversations.filter(conversation => 
            conversation.contact.includes(searchTerm) || 
            conversation.contact_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            conversation.last_message.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        
        // Sort conversations by last message time (newest first)
        filteredConversations.sort((a, b) => new Date(b.last_message_time) - new Date(a.last_message_time));
        
        setConversations(filteredConversations);
        
        // If a conversation is selected, load its messages
        if (selectedConversation) {
          loadConversationMessages(selectedConversation.id);
        }
      } catch (err) {
        console.error('Error fetching conversations:', err);
        setError('Failed to load conversations. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchConversations();
  }, [searchTerm, selectedConversation]);

  const loadConversationMessages = async (conversationId) => {
    try {
      setIsLoading(true);
      // In a real implementation, this would be an API call to get messages for a conversation
      // For now, we'll simulate a delay and return mock data
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Get messages for the selected conversation
      const conversationMessages = mockMessages[conversationId] || [];
      
      // Filter messages based on date range
      let filteredMessages = [...conversationMessages];
      
      if (dateRange !== 'all') {
        const now = new Date();
        let startDate;
        
        switch (dateRange) {
          case 'today':
            startDate = new Date(now.setHours(0, 0, 0, 0));
            break;
          case 'yesterday':
            startDate = new Date(now.setDate(now.getDate() - 1));
            startDate.setHours(0, 0, 0, 0);
            break;
          case 'week':
            startDate = new Date(now.setDate(now.getDate() - 7));
            break;
          case 'month':
            startDate = new Date(now.setMonth(now.getMonth() - 1));
            break;
          default:
            startDate = null;
        }
        
        if (startDate) {
          filteredMessages = filteredMessages.filter(message => new Date(message.timestamp) >= startDate);
        }
      }
      
      // Filter messages based on message type
      if (messageType !== 'all') {
        filteredMessages = filteredMessages.filter(message => {
          if (messageType === 'inbound') return message.direction === 'inbound';
          if (messageType === 'outbound') return message.direction === 'outbound';
          return true;
        });
      }
      
      // Sort messages by timestamp (oldest first)
      filteredMessages.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
      
      setMessages(filteredMessages);
    } catch (err) {
      console.error('Error fetching messages:', err);
      setError('Failed to load messages. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectConversation = (conversation) => {
    setSelectedConversation(conversation);
    loadConversationMessages(conversation.id);
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation) return;
    
    try {
      setIsSending(true);
      // In a real implementation, this would be an API call to send a message
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Create a new message
      const newMessageObj = {
        id: Math.max(...messages.map(m => m.id)) + 1,
        conversation_id: selectedConversation.id,
        from: '+1 (555) 987-6543', // Your number
        to: selectedConversation.contact,
        body: newMessage,
        direction: 'outbound',
        status: 'sent',
        timestamp: new Date().toISOString(),
        media_url: null
      };
      
      // Add the new message to the messages list
      setMessages([...messages, newMessageObj]);
      
      // Update the conversation's last message
      const updatedConversations = conversations.map(conv => {
        if (conv.id === selectedConversation.id) {
          return {
            ...conv,
            last_message: newMessage,
            last_message_time: new Date().toISOString()
          };
        }
        return conv;
      });
      
      // Sort conversations by last message time (newest first)
      updatedConversations.sort((a, b) => new Date(b.last_message_time) - new Date(a.last_message_time));
      
      setConversations(updatedConversations);
      
      // Clear the new message input
      setNewMessage('');
    } catch (err) {
      console.error('Error sending message:', err);
      setError('Failed to send message. Please try again later.');
    } finally {
      setIsSending(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    
    // If the date is today, show only the time
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
    }
    
    // If the date is yesterday, show "Yesterday" and the time
    if (date.toDateString() === yesterday.toDateString()) {
      return `Yesterday, ${date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })}`;
    }
    
    // Otherwise, show the full date
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold text-white mb-6">Messages</h1>
      
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
          <p className="text-red-200">{error}</p>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Conversations List */}
        <div className="md:col-span-1">
          <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 h-[calc(100vh-200px)]">
            <CardHeader className="pb-2">
              <CardTitle className="text-white">Conversations</CardTitle>
              <div className="mt-2">
                <input 
                  type="text" 
                  placeholder="Search conversations..." 
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white text-sm"
                />
              </div>
            </CardHeader>
            <CardContent className="overflow-y-auto h-[calc(100%-80px)]">
              {isLoading && !selectedConversation ? (
                <div className="flex justify-center items-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
                </div>
              ) : conversations.length === 0 ? (
                <div className="text-center p-6 text-gray-400">
                  No conversations found
                </div>
              ) : (
                <ul className="space-y-2">
                  {conversations.map(conversation => (
                    <li 
                      key={conversation.id}
                      onClick={() => handleSelectConversation(conversation)}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        selectedConversation?.id === conversation.id 
                          ? 'bg-purple-600/20 border border-purple-500/30' 
                          : 'hover:bg-gray-700/50'
                      }`}
                    >
                      <div className="flex justify-between items-start">
                        <div className="font-medium text-white">{conversation.contact_name}</div>
                        <div className="text-xs text-gray-400">{formatDate(conversation.last_message_time)}</div>
                      </div>
                      <div className="text-sm text-gray-300 mt-1">{conversation.contact}</div>
                      <div className="text-sm text-gray-400 mt-1 truncate">{conversation.last_message}</div>
                      {conversation.unread_count > 0 && (
                        <div className="mt-2 flex justify-end">
                          <span className="bg-purple-600 text-white text-xs px-2 py-1 rounded-full">
                            {conversation.unread_count} new
                          </span>
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Messages */}
        <div className="md:col-span-2">
          <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 h-[calc(100vh-200px)]">
            {selectedConversation ? (
              <>
                <CardHeader className="pb-2 border-b border-gray-700">
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle className="text-white">{selectedConversation.contact_name}</CardTitle>
                      <CardDescription className="text-gray-400">{selectedConversation.contact}</CardDescription>
                    </div>
                    <div className="flex space-x-2">
                      <select 
                        value={dateRange} 
                        onChange={(e) => setDateRange(e.target.value)}
                        className="bg-gray-700 border border-gray-600 rounded-lg px-2 py-1 text-white text-sm"
                      >
                        <option value="all">All Time</option>
                        <option value="today">Today</option>
                        <option value="yesterday">Yesterday</option>
                        <option value="week">Last 7 Days</option>
                        <option value="month">Last 30 Days</option>
                      </select>
                      <select 
                        value={messageType} 
                        onChange={(e) => setMessageType(e.target.value)}
                        className="bg-gray-700 border border-gray-600 rounded-lg px-2 py-1 text-white text-sm"
                      >
                        <option value="all">All Messages</option>
                        <option value="inbound">Inbound</option>
                        <option value="outbound">Outbound</option>
                      </select>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="flex flex-col h-[calc(100%-160px)]">
                  <div className="flex-1 overflow-y-auto py-4">
                    {isLoading ? (
                      <div className="flex justify-center items-center h-32">
                        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
                      </div>
                    ) : messages.length === 0 ? (
                      <div className="text-center p-6 text-gray-400">
                        No messages found
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {messages.map(message => (
                          <div 
                            key={message.id}
                            className={`flex ${message.direction === 'outbound' ? 'justify-end' : 'justify-start'}`}
                          >
                            <div 
                              className={`max-w-[70%] rounded-lg px-4 py-2 ${
                                message.direction === 'outbound' 
                                  ? 'bg-purple-600 text-white' 
                                  : 'bg-gray-700 text-gray-200'
                              }`}
                            >
                              <div className="text-sm">{message.body}</div>
                              <div className="text-xs mt-1 opacity-70">{formatDate(message.timestamp)}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="border-t border-gray-700 pt-4">
                    <div className="flex space-x-2">
                      <input 
                        type="text" 
                        placeholder="Type a message..." 
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSendMessage();
                          }
                        }}
                        className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                      />
                      <button 
                        onClick={handleSendMessage}
                        disabled={!newMessage.trim() || isSending}
                        className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
                      >
                        {isSending ? 'Sending...' : 'Send'}
                      </button>
                    </div>
                  </div>
                </CardContent>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                </svg>
                <p className="text-lg">Select a conversation to view messages</p>
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}
