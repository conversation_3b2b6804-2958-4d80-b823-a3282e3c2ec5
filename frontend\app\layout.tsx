import './globals.css';
import { LanguageProvider } from './i18n/LanguageContext';

import { Inter } from 'next/font/google';
import { SessionProvider } from './providers/SessionProvider';
import { AppProviders } from '../providers/AppProviders';

// Import dynamically loaded components from the client component
import { ConditionalNavbar } from './components/DynamicImports';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'CallSaver - Never Miss A Customer Call Again',
  description: 'AI-Powered Call Management Platform',
  // Add additional metadata
  openGraph: {
    title: 'CallSaver - Never Miss A Customer Call Again',
    description: 'AI-Powered Call Management Platform',
    type: 'website',
    images: ['/og-image.jpg'],
  },
};

// Define viewport metadata separately (Next.js 14+ requirement)
export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#0d0d17',
};

// Define caching headers for better performance
export const revalidate = 3600; // Revalidate at most once per hour

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <style dangerouslySetInnerHTML={{
          __html: `
            :root {
              --main-padding-top: 0;
            }
          `
        }} />
        <script dangerouslySetInnerHTML={{
          __html: `
            document.addEventListener('DOMContentLoaded', function() {
              setTimeout(() => {
                document.querySelectorAll('.scroll-reveal').forEach(el => {
                  el.style.opacity = '1';
                  el.style.transform = 'translateY(0)';
                });
              }, 100);
            });
          `
        }} />
      </head>
      <body className={`${inter.className} bg-[#0d0d17] min-h-screen overflow-x-hidden`}>
        <SessionProvider>
          {/* Add our custom AppProviders here, wrapping LanguageProvider */}
          <AppProviders>
            <LanguageProvider>
              <div className="min-h-screen flex flex-col relative z-10">
                <ConditionalNavbar />
                <main className="flex-grow relative" style={{ paddingTop: 'var(--main-padding-top)' }}>
                  {children}
                </main>
              </div>
            </LanguageProvider>
          </AppProviders>
        </SessionProvider>
      </body>
    </html>
  );
}
