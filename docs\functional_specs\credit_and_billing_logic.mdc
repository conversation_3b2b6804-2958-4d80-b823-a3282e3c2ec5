---
description: 
globs: 
alwaysApply: false
---
# Credit and Billing Logic Functional Document (`credit_and_billing_logic.mdc`)

## 1. Purpose and Scope

**Purpose:** Define the system for managing user credits, processing payments for subscriptions and credit top-ups, tracking usage costs against credits, and providing users with visibility into their billing history and subscription status.

**Scope:**
- Implement a credit-based system where users purchase credits to pay for usage (calls, messages, AI processing, number rentals, eSIM purchases).
- Define pricing models for various actions (e.g., cost per minute, per message, per AI interaction, per number per month, per eSIM plan).
- Integrate with a payment provider (e.g., Stripe) to handle:
    - Subscription plan payments (recurring).
    - One-time credit pack purchases.
    - Securely storing payment methods (delegated to the provider).
- Manage user credit balances, deducting credits as usage occurs.
- Provide users with a transaction history (credit additions and deductions).
- Display current subscription plan details and status.
- Handle billing events like payment failures, subscription cancellations, and upgrades/downgrades.
- Trigger low credit notifications.

## 2. User Interactions

- **View Credit Balance:** See the current available credit balance prominently displayed (e.g., in the header or dashboard).
- **Add Credits:** Initiate a purchase flow to buy credit packs (e.g., $10 for 1000 credits).
- **Manage Subscription:** View current plan, potentially upgrade/downgrade, or cancel (often by linking to the payment provider's customer portal).
- **View Billing History:** Access a list or table showing all credit transactions (purchases, usage deductions, refunds) with dates, descriptions, and amounts.
- **Manage Payment Methods:** Add, update, or remove payment methods (typically via the payment provider's portal).
- **View Invoices:** Access past invoices (usually via the payment provider's portal).

## 3. Backend Integrations & Services Used

- **Billing Service:** The central service orchestrating billing logic. It interfaces with:
    - **Payment Provider (e.g., Stripe):** For creating checkout sessions, processing payments, managing subscriptions, handling webhooks (payment success, failure, subscription changes), generating portal links.
    - **Database:** Stores credit balance per user, transaction history, subscription status, pricing rules, credit pack definitions.
    - **User Service:** Associates billing information and credits with users/organizations.
    - **Usage Tracking Services:** Receives usage events from Call Logging, AI Service, Number Management, eSIM Service to calculate credit deductions.
- **Notification Service:** Sends billing-related notifications (low credits, payment success/failure, upcoming renewal).

## 4. Necessary API Endpoints

- `GET /api/billing/balance`: Fetches the current credit balance for the user.
- `GET /api/billing/transactions?page=1&limit=25`: Fetches the user's credit transaction history.
- `GET /api/billing/subscription`: Fetches the user's current subscription plan details and status.
- `GET /api/billing/credit-packs`: Fetches available credit packs for purchase.
- `POST /api/billing/checkout-session`: Creates a payment provider (Stripe) checkout session for purchasing credits or subscribing to a plan. Returns a URL for redirection.
- `POST /webhooks/stripe`: Public endpoint to receive webhooks from Stripe (requires signature verification). Handles events like `checkout.session.completed`, `invoice.payment_succeeded`, `invoice.payment_failed`, `customer.subscription.updated`, etc.
- `GET /api/billing/portal-url`: (Already defined in Settings) Generates a link to the Stripe customer portal.
- **Internal Endpoints:**
    - `POST /internal/billing/deduct-credits`: Endpoint for usage tracking services to report usage and trigger credit deduction (payload: userId, amount, description, usageEventId).

## 5. Expected Frontend Component Structure

```
/components
  /billing
    CreditBalanceDisplay.tsx      # Shows current credits (potentially in header)
    AddCreditsButton.tsx          # Button to trigger purchase flow
    CreditPackSelectorModal.tsx   # Modal to choose a credit pack to buy
    BillingHistoryTable.tsx       # Table displaying transaction history
    SubscriptionInfoPanel.tsx     # Displays current plan and status
    ManageBillingButton.tsx       # Button linking to Stripe portal
    BillingSkeleton.tsx           # Loading state placeholder
```
*Note: These components would likely be integrated into the Dashboard and Settings sections.*

## 6. Data Displayed

- **Credit Balance:** Current number of credits.
- **Transaction History:** Date, Type (Purchase, Usage - Call, Usage - AI, Refund, Subscription), Description (e.g., "Call to +123...", "AI Summary - Call SID: ...", "1000 Credit Pack Purchase"), Amount (Credits Added/Deducted), Resulting Balance.
- **Subscription Info:** Plan Name (e.g., "Pro", "Free Trial"), Status (Active, Canceled, Past Due), Renewal Date (if applicable), Price.
- **Credit Packs:** Pack Name (e.g., "1000 Credits"), Price (Currency), Purchase Button.

## 7. State and UI Behavior

- **Loading States:** Show skeletons while fetching balance, history, or subscription status.
- **Add Credits Flow:**
    - User clicks "Add Credits".
    - Modal shows available packs.
    - User selects a pack.
    - Frontend calls `POST /api/billing/checkout-session`.
    - Backend creates Stripe session and returns URL.
    - Frontend redirects user to Stripe Checkout page.
    - User completes payment on Stripe.
    - Stripe redirects back to a success/cancel URL on CallSaver.
    - Stripe sends webhook `checkout.session.completed` to backend.
    - Backend verifies webhook, updates credit balance, logs transaction.
    - Frontend UI (e.g., balance display) updates (ideally via WebSocket push or polling after redirect).
- **Manage Billing:** Clicking the button redirects to the Stripe customer portal in a new tab.
- **Balance Updates:** Credit balance should ideally update near real-time as usage occurs or purchases complete. This might involve WebSockets pushed from the backend upon balance changes.

## 8. AI Integration

- **Usage Reporting:** AI assistant can be asked about current credit balance or recent usage costs (requires AI service to query Billing Service).
- **Low Credit Alerts:** AI can potentially provide more contextual low credit warnings via notifications (e.g., "Warning: Your credits are low and you have high AI usage configured").
- **Cost Estimation:** (Future) AI could estimate the credit cost of certain actions before the user confirms them (e.g., "Purchasing this number will cost approximately X credits per month").

## 9. Error Handling Rules

- **Payment Failures:**
    - Stripe Checkout handles card declines directly.
    - If a recurring subscription payment fails, Stripe sends a webhook (`invoice.payment_failed`). The backend updates subscription status (e.g., "Past Due") and triggers notifications. Access might be restricted after a grace period.
- **Insufficient Credits:** Backend prevents actions costing more credits than available (e.g., number purchase, potentially placing calls if balance is zero). Provide clear errors to the user.
- **Webhook Processing Errors:** Implement robust error handling and logging for webhook processing. Use idempotency keys (like Stripe's) to prevent duplicate processing. Have alerts for webhook failures.
- **API Errors:** Handle errors when fetching balance, history, or creating checkout sessions gracefully in the UI.

## 10. Logging and Usage Tracking Expectations

- **Log:**
    - All credit transactions (purchases, deductions, refunds) with details (user, amount, type, related event ID, timestamp).
    - Attempts to create checkout sessions (success/failure).
    - All received payment provider webhooks (event type, payload, processing status - success/failure/skipped).
    - Subscription status changes.
    - Errors during payment processing, credit deduction, or webhook handling.
- **Track:**
    - Credit purchase frequency and amounts.
    - Average credit balance per user segment.
    - Usage breakdown by cost category (calls, SMS, AI, numbers).
    - Subscription plan distribution and churn rate.
    - Revenue metrics (handled primarily via Stripe reporting).
    - Clicks on "Add Credits" and "Manage Billing".
