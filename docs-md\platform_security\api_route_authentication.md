# API Route Authentication Requirements

This document outlines the authentication requirements for all API routes in the CallSaver.app platform.

## Authentication Mechanisms

The platform uses the following authentication mechanisms:

1. **Session-based Authentication**: Used for web UI access, implemented via Supabase JWT tokens.
2. **API Key Authentication**: Used for programmatic access via the Developer SDK.
3. **CSRF Protection**: Applied to routes that handle sensitive operations to prevent cross-site request forgery attacks.

## Route Authentication Requirements

| Route Prefix | Authentication | CSRF Protection | Role Requirements | Notes |
|--------------|----------------|-----------------|-------------------|-------|
| `/api/auth` | Varies | No | None | Authentication endpoints, most are public |
| `/api/users` | Required | Required | None | User profile management |
| `/api/organization` | Required | Required | Admin | Organization management |
| `/api/api-keys` | Required | Required | None | API key management |
| `/api/dashboard` | Required | Required | None | Dashboard data |
| `/api/numbers` | Required | Required | None | Phone number management |
| `/api/esims` | Required | Required | None | eSIM management |
| `/api/call-logs` | Required | Required | None | Call logging and recordings |
| `/api/transcriptions` | Required | Required | None | Voice transcription management |
| `/api/messages` | Required | Required | None | SMS messaging |
| `/api/automations` | Required | Required | None | Call automation rules |
| `/api/scheduled-automations` | Required | Required | None | Scheduled automation tasks |
| `/api/blocklist` | Required | Required | None | Blocked number management |
| `/api/ip-blocklist` | Required | Required | Admin | IP address blocklist management |
| `/api/analytics` | Required | Required | None | Usage analytics |
| `/api/billing` | Required | Required | None | Billing and subscription |
| `/api/credits` | Required | Required | None | Credit management |
| `/api/notifications` | Required | Required | None | User notifications |
| `/api/appointments` | Required | Required | None | Appointment scheduling |
| `/api/availability` | Required | Required | None | Availability management |
| `/api/help/articles` | Not Required | Not Required | None | Public help center articles |
| `/api/help/categories` | Not Required | Not Required | None | Public help center categories |
| `/api/support/tickets` | Required | Required | None | Support ticket management |
| `/api/support/chat/config` | Required | Required | None | Chat configuration |
| `/api/queues` | Required | Required | Admin | Task queue monitoring |
| `/api/developer/register` | Not Required | Not Required | None | Developer registration |
| `/api/developer/verify` | Not Required | Not Required | None | Developer email verification |
| `/api/developer/*` | Required | Required | Developer | Developer portal features |
| `/api/security` | Required | Required | None | Security settings, MFA, audit logs |
| `/api/integrations` | Required | Required | None | External service integrations |
| `/api/webhooks/*` | Not Required | Not Required | None | External service webhooks |

## Special Cases

### Public Routes

The following routes are intentionally public (no authentication required):

1. **Authentication Routes**:
   - `/api/auth/login`
   - `/api/auth/register`
   - `/api/auth/forgot-password`
   - `/api/auth/reset-password`

2. **Help Center**:
   - `/api/help/articles`
   - `/api/help/articles/:articleSlug`
   - `/api/help/categories`

3. **Developer Portal**:
   - `/api/developer/register`
   - `/api/developer/verify/:token`

4. **Webhooks**:
   - `/api/webhooks/*` - These routes use signature validation instead of session authentication

### Routes with Role Requirements

Some routes require specific user roles:

1. **Admin-only Routes**:
   - `/api/organization/*` - Organization management
   - `/api/ip-blocklist/*` - IP address blocklist management
   - `/api/queues/*` - Task queue monitoring
   - `/api/analytics/retention-policies/*` - Analytics retention policy management

2. **Developer-only Routes**:
   - `/api/developer/*` (except registration and verification) - Developer portal features

## Implementation Details

Authentication is enforced at two levels:

1. **API Gateway Level**: In `apiGatewayRoutes.js`, where middleware is applied to route groups.
2. **Individual Route Level**: In specific route files, where middleware is applied to individual routes or subrouters.

All routes should have appropriate authentication middleware unless they are explicitly intended to be public.
