import { NextResponse } from 'next/server';
import OpenAI from 'openai';
import { parseCommand } from '../../../utils/commandParser';

// Initialize OpenAI with API key from environment variables
let openai;
try {
  // Ensure we have the API key
  if (!process.env.OPENAI_API_KEY) {
    console.error('OPENAI_API_KEY is missing in environment variables');
  } else {
    openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }
} catch (error) {
  console.error('Error initializing OpenAI:', error);
}

export async function POST(request) {
  try {
    // Check if OpenAI is properly initialized
    if (!openai) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'OpenAI API is not configured on the server. Please contact the administrator.' 
        },
        { status: 500 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { messages } = body;
    
    // Validate required parameters
    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { success: false, message: 'Missing or invalid messages parameter' },
        { status: 400 }
      );
    }
    
    // Format messages for OpenAI API
    const formattedMessages = messages.map(msg => ({
      role: msg.sender === 'user' ? 'user' : 'assistant',
      content: msg.content
    }));
    
    // Check if the last user message looks like a command without the slash prefix
    const lastUserMessage = formattedMessages.length > 0 && formattedMessages[formattedMessages.length - 1].role === 'user' 
      ? formattedMessages[formattedMessages.length - 1].content 
      : '';
    
    // Detect natural language commands like "call +1234567890 and say hello"
    const callPattern = /^(?:please\s+)?(?:make\s+a\s+)?call\s+(\+\d+)(?:\s+and\s+say|\s+saying|\s+say)(?:\s*\:)?\s+(.+)$/i;
    const callMatch = lastUserMessage.match(callPattern);
    
    const smsPattern = /^(?:please\s+)?(?:send\s+)?(?:a\s+)?(?:text|sms)(?:\s+message)?\s+to\s+(\+\d+)(?:\s+saying|\s+with)(?:\s*\:)?\s+(.+)$/i;
    const smsMatch = lastUserMessage.match(smsPattern);
    
    // If natural language command is detected, treat it like a slash command
    if (callMatch || smsMatch) {
      return NextResponse.json({
        success: true,
        message: "I've detected a command in your message. Please use the appropriate slash command format:",
        detectedCommand: callMatch 
          ? `/call ${callMatch[1]} Say: ${callMatch[2]}`
          : `/sms ${smsMatch[1]} ${smsMatch[2]}`,
        commandType: callMatch ? 'call' : 'sms'
      });
    }
    
    // Add system message at the beginning
    formattedMessages.unshift({
      role: 'system',
      content: `You are CallSaver AI, a helpful assistant that can answer questions about call handling, telecommunications, and general topics. You can provide support for using the CallSaver application.

When users want to make calls or send SMS, guide them to use the following formats:
- For calls: /call +[number] Say: [message]  (e.g., /call +16205268448 Say: Hello there)
- For SMS: /sms +[number] [message]  (e.g., /sms +31970102886 Your appointment is confirmed)

Also recognize when users type natural language commands like "call +1234567890 and say hello" or "send a text to +1234567890 saying hello", and suggest the proper command format.`
    });
    
    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: "gpt-4-turbo", // Use the latest model
      messages: formattedMessages,
      max_tokens: 2048,
      temperature: 0.7,
    });
    
    const responseMessage = completion.choices[0].message.content;
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: responseMessage,
      usage: completion.usage,
    });
  } catch (error) {
    console.error('Error calling OpenAI API:', error);
    return NextResponse.json(
      { success: false, message: `Failed to get AI response: ${error.message}` },
      { status: 500 }
    );
  }
}
