'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { useDashboard } from '../../hooks/useDashboard';
import { useAuthStore } from '../../stores/authStore';
import { isAdmin } from '../../app/utils/roleUtils';
import MetricCard from './MetricCard';
import RecentActivityFeed from './RecentActivityFeed';
import QuickLinks from './QuickLinks';
import CreditBalanceWidget from './CreditBalanceWidget';
import AISummaryWidget from './AISummaryWidget';
import DashboardSkeleton from './DashboardSkeleton';
import ErrorMessage from '../shared/ErrorMessage';
import { PhoneIcon, ChatBubbleLeftRightIcon, BoltIcon, CreditCardIcon } from '@heroicons/react/24/outline';

export default function DashboardLayout() {
  const {
    summary,
    recentActivity,
    aiInsights,
    isLoading,
    refreshDashboard
  } = useDashboard();
  
  // Get user and check if admin
  const user = useAuthStore(state => state.user);
  const userIsAdmin = isAdmin(user);

  // Refresh dashboard data on initial load
  useEffect(() => {
    refreshDashboard();
  }, [refreshDashboard]);

  // Handle loading state
  if (isLoading) {
    return <DashboardSkeleton />;
  }

  // Handle error state for summary data
  if (summary.error) {
    return (
      <div className="p-6">
        <ErrorMessage
          title="Could not load dashboard"
          message="We couldn't load your dashboard data. Please try again later."
          error={summary.error}
          onRetry={refreshDashboard}
        />
      </div>
    );
  }

  // Quick links configuration
  const quickLinks = [
    { title: 'Add Phone Number', href: '/numbers/new', icon: PhoneIcon },
    { title: 'Setup Automation', href: '/automation', icon: BoltIcon },
    { title: 'View Call Logs', href: '/call-logs', icon: ChatBubbleLeftRightIcon },
    { title: 'Manage Billing', href: '/billing', icon: CreditCardIcon },
  ];

  return (
    <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6 md:py-8">
      <div className="mb-8 flex justify-between items-start">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-1 sm:mb-2">Dashboard</h1>
          <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">Welcome back! Here's an overview of your CallSaver account.</p>
        </div>
        {userIsAdmin && (
          <Link 
            href="/dashboard/admin" 
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm font-medium transition-colors duration-150 flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 11-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 004.486-6.336l-3.276 3.277a3.004 3.004 0 01-2.25-2.25l3.276-3.276a4.5 4.5 0 00-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437l1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008z" />
            </svg>
            Admin Dashboard
          </Link>
        )}
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
        <MetricCard
          title="Total Calls Today"
          value={summary.data?.totalCallsToday || 0}
          icon={PhoneIcon}
          link="/call-logs"
        />
        <MetricCard
          title="Total Calls This Week"
          value={summary.data?.totalCallsWeek || 0}
          icon={PhoneIcon}
          link="/call-logs"
        />
        <MetricCard
          title="Total Messages Today"
          value={summary.data?.totalMessagesToday || 0}
          icon={ChatBubbleLeftRightIcon}
          link="/messages"
        />
        <MetricCard
          title="Active Automations"
          value={summary.data?.activeAutomations || 0}
          icon={BoltIcon}
          link="/automation"
        />
      </div>

      {/* Two-column layout for wider screens */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 sm:gap-8">
        {/* Left column (2/3 width on large screens) */}
        <div className="lg:col-span-2 space-y-8">
          {/* Recent Activity Feed */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sm:p-6">
            <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4 text-gray-900 dark:text-white">Recent Activity</h2>
            <RecentActivityFeed
              data={(recentActivity.data || []).map(item => ({
                ...item,
                type: item.type as "call" | "sms" | "voicemail" | "alert"
              }))}
              isLoading={recentActivity.isLoading}
              error={recentActivity.error}
              onRetry={() => refreshDashboard()}
            />
          </div>

          {/* AI Summary Widget (if available) */}
          {aiInsights.data && aiInsights.data.length > 0 && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sm:p-6">
              <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4 text-gray-900 dark:text-white">AI Insights</h2>
              <AISummaryWidget
                insights={aiInsights.data}
                isLoading={aiInsights.isLoading}
                error={aiInsights.error}
                onRetry={() => refreshDashboard()}
              />
            </div>
          )}
        </div>

        {/* Right column (1/3 width on large screens) */}
        <div className="space-y-8">
          {/* Credit Balance Widget */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sm:p-6">
            <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4 text-gray-900 dark:text-white">Credit Balance</h2>
            <CreditBalanceWidget
              balance={summary.data?.creditBalance || 0}
              isLoading={summary.isLoading}
              linkToBilling="/billing"
            />
          </div>

          {/* Quick Links */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sm:p-6">
            <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4 text-gray-900 dark:text-white">Quick Links</h2>
            <QuickLinks links={quickLinks} />
          </div>
        </div>
      </div>
    </div>
  );
}
