'use client';

/**
 * Utility functions for handling user roles and permissions
 */

/**
 * Check if a user has admin role
 * @param {Object} user - User object from auth context
 * @returns {boolean} - Whether the user is an admin
 */
export const isAdmin = (user) => {
  if (!user) return false;
  
  console.log('Checking admin role for user:', user);
  
  // Check user.role directly if available
  if (user.role) {
    console.log('User has role property:', user.role);
    return user.role === 'admin';
  }
  
  // Check in user_metadata if role is stored there (Supabase structure)
  if (user.user_metadata && user.user_metadata.role) {
    console.log('User has user_metadata.role:', user.user_metadata.role);
    return user.user_metadata.role === 'admin';
  }
  
  // Check <NAME_EMAIL> as a fallback
  if (user.email) {
    console.log('Checking email:', user.email);
    if (user.email === '<EMAIL>') {
      console.log('User is admin based on email');
      return true;
    }
  }
  
  console.log('User is not admin');
  return false;
};

/**
 * Check if a user has a specific role
 * @param {Object} user - User object from auth context
 * @param {string|string[]} roles - Role or array of roles to check
 * @returns {boolean} - Whether the user has one of the specified roles
 */
export const hasRole = (user, roles) => {
  if (!user) return false;
  
  // Convert single role to array
  const rolesToCheck = Array.isArray(roles) ? roles : [roles];
  
  // Get user's role
  let userRole = null;
  
  // Check user.role directly if available
  if (user.role) {
    userRole = user.role;
  }
  // Check in user_metadata if role is stored there (Supabase structure)
  else if (user.user_metadata && user.user_metadata.role) {
    userRole = user.user_metadata.role;
  }
  // Special <NAME_EMAIL>
  else if (user.email === '<EMAIL>') {
    userRole = 'admin';
  }
  
  // Check if user's role is in the list of roles to check
  return rolesToCheck.includes(userRole);
};

/**
 * Get navigation items based on user role
 * @param {Object} user - User object from auth context
 * @returns {Array} - Array of navigation items the user has access to
 */
export const getNavigationItems = (user) => {
  // Base navigation items for all authenticated users
  const baseNavItems = [
    { 
      name: 'Dashboard', 
      path: '/dashboard', 
      icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6' 
    },
    { 
      name: 'Automation', 
      path: '/dashboard/automation', 
      icon: 'M9.75 3.104v5.714a2.25 2.25 0 01-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 014.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0112 15a9.065 9.065 0 00-6.23-.693L5 14.5m14.8.8l1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0112 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5' 
    },
    { 
      name: 'Phone Numbers', 
      path: '/dashboard/phone-numbers', 
      icon: 'M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 002.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 01-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 00-1.091-.852H4.5A2.25 2.25 0 002.25 4.5v2.25z' 
    },
    { 
      name: 'Calls', 
      path: '/dashboard/calls', 
      icon: 'M14.25 9.75v-4.5m0 4.5h4.5m-4.5 0l6-6m-3 18c-8.284 0-15-6.716-15-15V4.5A2.25 2.25 0 014.5 2.25h1.372c.516 0 .966.351 1.091.852l1.106 4.423c.11.44-.054.902-.417 1.173l-1.293.97a1.062 1.062 0 00-.38 1.21 12.035 12.035 0 007.143 7.143c.441.162.928-.004 1.21-.38l.97-1.293c.271-.363.734-.527 1.173-.417l4.423 1.106c.5.125.852.575.852 1.091V19.5a2.25 2.25 0 01-2.25 2.25h-2.25z' 
    },
    { 
      name: 'Messages', 
      path: '/dashboard/messages', 
      icon: 'M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 01.865-.501 48.172 48.172 0 003.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z' 
    },
    { 
      name: 'Settings', 
      path: '/dashboard/settings', 
      icon: 'M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z' 
    }
  ];
  
  // Admin-specific navigation items
  const adminNavItems = [
    { 
      name: 'Admin Dashboard', 
      path: '/dashboard/admin', 
      icon: 'M11.42 15.17L17.25 21A2.652 2.652 0 0021 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 11-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 004.486-6.336l-3.276 3.277a3.004 3.004 0 01-2.25-2.25l3.276-3.276a4.5 4.5 0 00-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437l1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008z' 
    },
    { 
      name: 'User Management', 
      path: '/dashboard/admin/users', 
      icon: 'M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z' 
    },
    { 
      name: 'Security Settings', 
      path: '/dashboard/admin/security', 
      icon: 'M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z' 
    },
    { 
      name: 'Tenant Management', 
      path: '/dashboard/admin/tenants', 
      icon: 'M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008zm0 3h.008v.008h-.008v-.008zm0 3h.008v.008h-.008v-.008z' 
    },
    { 
      name: 'Developer Portal', 
      path: '/dashboard/developer', 
      icon: 'M17.25 6.75L22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3l-4.5 16.5' 
    },
    { 
      name: 'API Keys', 
      path: '/dashboard/developer/api-keys', 
      icon: 'M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z' 
    }
  ];
  
  // Return combined navigation items for admin users
  if (isAdmin(user)) {
    return [...baseNavItems, ...adminNavItems];
  }
  
  // Return only base navigation items for regular users
  return baseNavItems;
};
