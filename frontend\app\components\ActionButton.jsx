"use client";

import { motion } from 'framer-motion';

export default function ActionButton({ icon, label, primary = false }) {
  return (
    <motion.button
      className={`flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all ${
        primary 
          ? 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white' 
          : 'bg-[#2c2c44] text-white hover:bg-[#33334d]'
      }`}
      whileHover={{ 
        scale: 1.05,
        boxShadow: primary 
          ? '0 0 15px rgba(139, 92, 246, 0.4)' 
          : '0 0 10px rgba(255, 255, 255, 0.1)'
      }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        type: "spring",
        stiffness: 500,
        damping: 30
      }}
    >
      {icon && (
        <span className="mr-2">
          {icon}
        </span>
      )}
      {label}
    </motion.button>
  );
} 