'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '../../../../components/ui/tabs';
import AdminPageLayout from '../../../../components/admin/AdminPageLayout';

export default function AdvancedAnalytics() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timeRange, setTimeRange] = useState('7d');
  const [analyticsData, setAnalyticsData] = useState({
    userMetrics: {
      totalUsers: 0,
      activeUsers: 0,
      newUsers: 0,
      retentionRate: 0
    },
    callMetrics: {
      totalCalls: 0,
      avgCallDuration: 0,
      callSuccessRate: 0,
      peakCallHours: []
    },
    messageMetrics: {
      totalMessages: 0,
      avgResponseTime: 0,
      deliveryRate: 0
    },
    systemMetrics: {
      apiLatency: 0,
      errorRate: 0,
      serverLoad: 0,
      storageUsed: 0
    }
  });

  useEffect(() => {
    const fetchAnalyticsData = async () => {
      try {
        setIsLoading(true);
        // In a real implementation, this would be an API call to get analytics data
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data - would vary based on timeRange in a real implementation
        setAnalyticsData({
          userMetrics: {
            totalUsers: 1250,
            activeUsers: 876,
            newUsers: timeRange === '7d' ? 45 : timeRange === '30d' ? 187 : 543,
            retentionRate: 78.5
          },
          callMetrics: {
            totalCalls: timeRange === '7d' ? 3456 : timeRange === '30d' ? 15678 : 45678,
            avgCallDuration: 142, // seconds
            callSuccessRate: 94.7,
            peakCallHours: [9, 14, 16] // 9am, 2pm, 4pm
          },
          messageMetrics: {
            totalMessages: timeRange === '7d' ? 12345 : timeRange === '30d' ? 45678 : 123456,
            avgResponseTime: 8.3, // seconds
            deliveryRate: 99.2
          },
          systemMetrics: {
            apiLatency: 124, // ms
            errorRate: 0.8, // percent
            serverLoad: 42, // percent
            storageUsed: 1.7 // TB
          }
        });
      } catch (err) {
        console.error('Error fetching analytics data:', err);
        setError('Failed to load analytics data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalyticsData();
  }, [timeRange]);

  const formatNumber = (num) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <AdminPageLayout
      title="Advanced Analytics"
      description="Detailed platform analytics and performance metrics."
    >
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
          <p className="text-red-200">{error}</p>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <div className="flex space-x-2">
          <button 
            onClick={() => setTimeRange('7d')}
            className={`px-3 py-1 rounded-lg ${
              timeRange === '7d' 
                ? 'bg-purple-600 text-white' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            Last 7 Days
          </button>
          <button 
            onClick={() => setTimeRange('30d')}
            className={`px-3 py-1 rounded-lg ${
              timeRange === '30d' 
                ? 'bg-purple-600 text-white' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            Last 30 Days
          </button>
          <button 
            onClick={() => setTimeRange('90d')}
            className={`px-3 py-1 rounded-lg ${
              timeRange === '90d' 
                ? 'bg-purple-600 text-white' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            Last 90 Days
          </button>
        </div>
        
        <button className="bg-purple-600 hover:bg-purple-700 text-white py-1 px-3 rounded-lg text-sm transition-colors">
          Export Data
        </button>
      </div>

      <Tabs defaultValue="users" className="w-full">
        <TabsList className="bg-gray-800/70 border border-purple-500/20 mb-6">
          <TabsTrigger value="users">User Analytics</TabsTrigger>
          <TabsTrigger value="calls">Call Analytics</TabsTrigger>
          <TabsTrigger value="messages">Message Analytics</TabsTrigger>
          <TabsTrigger value="system">System Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">User Analytics</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Total Users</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : formatNumber(analyticsData.userMetrics.totalUsers)}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Active Users</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : formatNumber(analyticsData.userMetrics.activeUsers)}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">New Users</CardTitle>
                <CardDescription className="text-gray-400">In selected period</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : formatNumber(analyticsData.userMetrics.newUsers)}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Retention Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : `${analyticsData.userMetrics.retentionRate}%`}</p>
              </CardContent>
            </Card>
          </div>
          
          <div className="bg-gray-700/30 border border-gray-600/30 rounded-lg p-4 text-center">
            <p className="text-gray-400">User growth visualization would be displayed here</p>
            <div className="h-64 flex items-center justify-center">
              <p className="text-gray-500">Chart Placeholder</p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="calls" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">Call Analytics</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Total Calls</CardTitle>
                <CardDescription className="text-gray-400">In selected period</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : formatNumber(analyticsData.callMetrics.totalCalls)}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Avg Call Duration</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : formatTime(analyticsData.callMetrics.avgCallDuration)}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Success Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : `${analyticsData.callMetrics.callSuccessRate}%`}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Peak Hours</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">
                  {isLoading ? '...' : analyticsData.callMetrics.peakCallHours.map(hour => 
                    `${hour > 12 ? hour - 12 : hour}${hour >= 12 ? 'pm' : 'am'}`
                  ).join(', ')}
                </p>
              </CardContent>
            </Card>
          </div>
          
          <div className="bg-gray-700/30 border border-gray-600/30 rounded-lg p-4 text-center">
            <p className="text-gray-400">Call volume visualization would be displayed here</p>
            <div className="h-64 flex items-center justify-center">
              <p className="text-gray-500">Chart Placeholder</p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="messages" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">Message Analytics</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Total Messages</CardTitle>
                <CardDescription className="text-gray-400">In selected period</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : formatNumber(analyticsData.messageMetrics.totalMessages)}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Avg Response Time</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : `${analyticsData.messageMetrics.avgResponseTime}s`}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Delivery Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : `${analyticsData.messageMetrics.deliveryRate}%`}</p>
              </CardContent>
            </Card>
          </div>
          
          <div className="bg-gray-700/30 border border-gray-600/30 rounded-lg p-4 text-center">
            <p className="text-gray-400">Message volume visualization would be displayed here</p>
            <div className="h-64 flex items-center justify-center">
              <p className="text-gray-500">Chart Placeholder</p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="system" className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <h2 className="text-xl font-bold text-white mb-4">System Performance</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">API Latency</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : `${analyticsData.systemMetrics.apiLatency}ms`}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Error Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : `${analyticsData.systemMetrics.errorRate}%`}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Server Load</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : `${analyticsData.systemMetrics.serverLoad}%`}</p>
              </CardContent>
            </Card>

            <Card className="bg-gray-700/50 border border-gray-600/50">
              <CardHeader>
                <CardTitle className="text-white">Storage Used</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold text-white">{isLoading ? '...' : `${analyticsData.systemMetrics.storageUsed} TB`}</p>
              </CardContent>
            </Card>
          </div>
          
          <div className="bg-gray-700/30 border border-gray-600/30 rounded-lg p-4 text-center">
            <p className="text-gray-400">System performance visualization would be displayed here</p>
            <div className="h-64 flex items-center justify-center">
              <p className="text-gray-500">Chart Placeholder</p>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </AdminPageLayout>
  );
}
