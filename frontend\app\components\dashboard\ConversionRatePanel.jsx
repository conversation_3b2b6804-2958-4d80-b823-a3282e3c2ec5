"use client";

import React, { useState } from 'react';
import { 
  ArrowUpIcon, 
  ArrowDownIcon, 
  ChevronUpIcon, 
  ChevronDownIcon 
} from '@heroicons/react/24/solid';
import { PresentationChartLineIcon } from '@heroicons/react/24/outline';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '../../components/ui/select';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine } from 'recharts';

export function ConversionRatePanel() {
  const [showDetails, setShowDetails] = useState(false);
  const [timeRange, setTimeRange] = useState('weekly');
  
  // Sample data
  const conversionData = {
    daily: {
      value: 24.5,
      change: 3.2,
      chartData: [
        { name: 'Mon', value: 22.4 },
        { name: '<PERSON><PERSON>', value: 21.8 },
        { name: 'Wed', value: 23.2 },
        { name: 'Thu', value: 24.5 },
        { name: 'Fri', value: 25.3 },
        { name: 'Sat', value: 24.9 },
        { name: 'Sun', value: 24.5 },
      ],
    },
    weekly: {
      value: 27.8,
      change: -1.5,
      chartData: [
        { name: 'Week 1', value: 26.3 },
        { name: 'Week 2', value: 28.1 },
        { name: 'Week 3', value: 29.4 },
        { name: 'Week 4', value: 27.8 },
      ],
    },
    monthly: {
      value: 29.4,
      change: 2.1,
      chartData: [
        { name: 'Jan', value: 24.2 },
        { name: 'Feb', value: 25.7 },
        { name: 'Mar', value: 27.3 },
        { name: 'Apr', value: 26.8 },
        { name: 'May', value: 28.4 },
        { name: 'Jun', value: 29.4 },
      ],
    }
  };

  const bestPeriod = {
    value: 32.7,
    period: 'Last Month'
  };

  const improvementNeeded = 7.3;

  // List of common successful resolutions
  const successfulResolutions = [
    { name: 'Automated Troubleshooting', percentage: 42 },
    { name: 'Quick Reference Guide', percentage: 28 },
    { name: 'Knowledge Base Search', percentage: 18 },
    { name: 'Agent Handoff', percentage: 12 }
  ];

  return (
    <div className="dashboard-card-indigo border-glow h-full w-full min-h-[400px] flex flex-col">
      <div className="p-5 sm:p-6 relative z-10 flex-grow flex flex-col">
        <header className="dashboard-card-header mb-5 flex-col sm:flex-row">
          <div className="flex items-center mb-3 sm:mb-0">
            <div className="title-icon-indigo flex-shrink-0">
              <PresentationChartLineIcon className="h-5 w-5" />
            </div>
            <div>
              <h3 className="font-bold text-lg text-indigo-400">Conversion Rate</h3>
              <p className="text-gray-400 text-sm">Percentage of successful call resolutions</p>
            </div>
          </div>

          <div className="flex-shrink-0">
            <Select
              value={timeRange}
              onValueChange={setTimeRange}
              className="w-auto"
            >
              <SelectTrigger className="bg-indigo-950/50 border-indigo-800/50 text-sm py-1 h-8">
                <SelectValue placeholder="Time Range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </header>

        <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 mb-6 flex-grow">
          {/* Main conversion rate display */}
          <div className="bg-gradient-to-br from-indigo-900/50 to-purple-900/30 rounded-xl p-6 w-full flex-1 border border-indigo-600/30 backdrop-blur-xs shadow-lg shadow-indigo-900/20 hover:border-indigo-500/40 transition-all duration-300 transform hover:scale-[1.01] animate-pulse-subtle animate-slideIn">
            <div className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-indigo-300 to-purple-200 bg-clip-text text-transparent mb-3">{conversionData[timeRange].value}%</div>
            <div className="flex items-center mt-3">
              <div className={`conversion-change-indicator ${conversionData[timeRange].change >= 0 ? 'positive' : 'negative'} animate-slideIn`} style={{ animationDelay: '0.1s' }}>
                {conversionData[timeRange].change >= 0 ? (
                  <ArrowUpIcon className="h-4 w-4 mr-1.5 flex-shrink-0" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 mr-1.5 flex-shrink-0" />
                )}
                <span className="font-medium">{Math.abs(conversionData[timeRange].change)}%</span>
              </div>
              <span className="text-gray-400 text-sm ml-3 animate-slideIn" style={{ animationDelay: '0.2s' }}>vs previous period</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 mb-6">
          {/* Best Period Card */}
          <div className="bg-gradient-to-br from-indigo-900/20 to-green-900/10 rounded-xl p-4 border border-green-700/30 animate-slideIn hover:border-green-500/50 transition-all duration-300 transform hover:scale-[1.02] shadow-md shadow-green-900/10" style={{ animationDelay: '0.3s' }}>
            <div className="text-xs uppercase tracking-wider text-gray-400 font-medium mb-1">Best Period</div>
            <div className="text-xl font-semibold bg-gradient-to-r from-green-400 to-emerald-300 bg-clip-text text-transparent">{bestPeriod.value}%</div>
            <div className="text-sm text-gray-400 mt-1">{bestPeriod.period}</div>
          </div>

          {/* Improvement Card */}
          <div className="bg-gradient-to-br from-indigo-900/20 to-yellow-900/10 rounded-xl p-4 border border-yellow-700/30 animate-slideIn hover:border-yellow-500/50 transition-all duration-300 transform hover:scale-[1.02] shadow-md shadow-yellow-900/10" style={{ animationDelay: '0.4s' }}>
            <div className="text-xs uppercase tracking-wider text-gray-400 font-medium mb-1">Improvement Needed</div>
            <div className="text-xl font-semibold bg-gradient-to-r from-amber-400 to-yellow-300 bg-clip-text text-transparent">{improvementNeeded}%</div>
            <div className="text-sm text-gray-400 mt-1">To reach target</div>
          </div>
        </div>

        {/* Conversion Rate Chart */}
        <div className="mt-2 h-[180px] w-full bg-indigo-950/20 rounded-xl p-4 border border-indigo-800/30 animate-slideIn chart-container" style={{ animationDelay: '0.5s' }}>
          <div className="flex justify-between items-center mb-2">
            <div className="text-xs text-gray-400">Performance Trend</div>
            <div className="flex items-center text-xs">
              <div className="w-3 h-0.5 bg-green-400 mr-1.5"></div>
              <span className="text-gray-400">Target: {bestPeriod.value}%</span>
            </div>
          </div>
          <ResponsiveContainer width="100%" height="85%">
            <AreaChart
              data={conversionData[timeRange].chartData}
              margin={{ top: 5, right: 5, left: 0, bottom: 5 }}
            >
              <defs>
                <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#818cf8" stopOpacity={0.9} />
                  <stop offset="50%" stopColor="#6366f1" stopOpacity={0.6} />
                  <stop offset="95%" stopColor="#4f46e5" stopOpacity={0.1} />
                </linearGradient>
              </defs>
              <XAxis dataKey="name" tick={{ fill: '#9ca3af', fontSize: 10 }} axisLine={false} tickLine={false} />
              <YAxis hide={true} />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'rgba(15, 23, 42, 0.9)', 
                  border: '1px solid rgba(99, 102, 241, 0.3)',
                  borderRadius: '0.375rem',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                }}
                labelStyle={{ color: '#e5e7eb' }}
                itemStyle={{ color: '#c7d2fe' }}
                formatter={(value) => [`${value}%`, 'Conversion Rate']}
                labelFormatter={(label) => `${label}`}
              />
              <ReferenceLine 
                y={bestPeriod.value} 
                stroke="rgba(74, 222, 128, 0.6)" 
                strokeDasharray="3 3" 
                strokeWidth={1.5}
              />
              <Area 
                type="monotone" 
                dataKey="value" 
                stroke="#a5b4fc" 
                strokeWidth={2}
                fillOpacity={1} 
                fill="url(#colorValue)" 
                animationDuration={1500}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        <div className="mt-auto">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center text-indigo-400 hover:text-indigo-300 transition-colors duration-200 mb-2"
          >
            <span className="font-medium">Successful Resolution Types</span>
            {showDetails ? (
              <ChevronUpIcon className="h-4 w-4 ml-1" />
            ) : (
              <ChevronDownIcon className="h-4 w-4 ml-1" />
            )}
          </button>

          {showDetails && (
            <div className="animate-slideIn mt-3 space-y-3">
              {successfulResolutions.map((resolution, index) => (
                <div key={index} className="bg-gray-800/40 rounded-lg p-3 hover:bg-gray-800/60 transition-colors duration-200 border border-gray-700/30">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-white">{resolution.name}</span>
                    <span className="text-indigo-400 font-medium">{resolution.percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-indigo-600 to-purple-500 h-2 rounded-full"
                      style={{ width: `${resolution.percentage}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      <div className="card-glow"></div>
    </div>
  );
} 