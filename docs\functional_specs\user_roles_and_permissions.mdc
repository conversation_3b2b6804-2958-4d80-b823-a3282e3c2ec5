---
description: 
globs: 
alwaysApply: false
---
# User Roles and Permissions Functional Document (`user_roles_and_permissions.mdc`)

## 1. Purpose and Scope

**Purpose:** Define and manage user roles within an organization's CallSaver account, controlling access to different sections, features, and data based on assigned roles. Ensure secure and appropriate access levels for various team members.

**Scope:**
- Define default roles (e.g., <PERSON><PERSON>, Member, Viewer).
- Allow Admins to invite new users to the organization's account.
- Allow Admins to assign roles to users.
- Allow Admins to remove users from the account.
- Enforce permissions based on roles across all dashboard sections (viewing data, making configuration changes, accessing billing, managing numbers, etc.).
- (Future) Allow creation of custom roles with granular permission settings.
- Display a list of current users, their roles, and statuses (e.g., Active, Pending Invitation).

## 2. User Interactions (Primarily for Admins)

- **View Users:** See a list of all users associated with the account, including their email, assigned role, and status.
- **Invite User:** Enter an email address and select an initial role to send an invitation.
- **Change Role:** Select a user and choose a different role from a dropdown menu.
- **Remove User:** Select a user and click a "Remove" button, followed by confirmation.
- **Resend Invitation:** Option to resend the invitation email to users with "Pending Invitation" status.
- **Filter/Search Users:** (Optional) Filter the user list by role or search by email/name.

**Interactions for Non-Admins:**
- View their own role (usually displayed in profile/settings).
- Experience restricted access to sections/features based on their role.

## 3. Backend Integrations & Services Used

- **User Service / Authentication Service:** Manages user accounts, roles, invitations, and enforces permissions at the API level.
- **Database:** Stores user information, organization membership, assigned roles, and potentially permission definitions.
- **Email Service:** Sends invitation emails to new users.

## 4. Necessary API Endpoints

- `GET /api/organization/users?page=1&limit=25&role=<role_filter>&search=<query>`: Fetches the list of users in the organization (Admin only).
- `POST /api/organization/invitations`: Sends an invitation to a new user email with a specified role (Admin only).
- `DELETE /api/organization/invitations/{invitationId}`: Cancels a pending invitation (Admin only).
- `POST /api/organization/invitations/{invitationId}/resend`: Resends an invitation email (Admin only).
- `PUT /api/organization/users/{userId}/role`: Changes the role of an existing user (Admin only).
- `DELETE /api/organization/users/{userId}`: Removes a user from the organization (Admin only).
- `GET /api/users/me`: (Existing endpoint) Should include the user's own role information.
- `GET /api/roles`: (Optional) Fetches the list of available roles and their permissions (useful for future custom roles).

*Note: All API endpoints interacting with dashboard sections (e.g., `/api/call-logs`, `/api/automations`) must internally check the requesting user's role and permissions before returning data or allowing modifications.*

## 5. Expected Frontend Component Structure

```
/components
  /users
    UserManagementLayout.tsx      # Main layout for user management (likely within Settings or a dedicated section)
    UserListTable.tsx             # Displays the table of users
      UserListRow.tsx             # Represents a single user row with actions (Change Role, Remove)
    InviteUserForm.tsx            # Form/Modal to invite a new user (email input, role selector)
    RoleSelectorDropdown.tsx      # Reusable dropdown for selecting roles
    RemoveUserConfirmationDialog.tsx # Confirmation dialog for removing users
    UserManagementSkeleton.tsx    # Loading state placeholder
```

## 6. Data Displayed

- **User List:** User's Name (if available), Email Address, Assigned Role, Status (Active, Pending Invitation), Date Added/Invited, Action Buttons (Change Role, Remove, Resend Invite).
- **Invite Form:** Email input field, Role selection dropdown.

## 7. State and UI Behavior

- **Role-Based UI:** The entire dashboard UI should dynamically enable/disable or hide elements based on the logged-in user's role. This check should happen both on the frontend (for immediate visual feedback) and backend (for security).
- **Loading State:** Show skeletons while loading the user list.
- **Admin Actions:** Buttons for Invite, Change Role, Remove should only be visible/enabled for Admins.
- **Feedback:** Provide clear success or error messages (toast notifications) for inviting, changing roles, or removing users.
- **Invitation Status:** Clearly indicate users who haven't accepted their invitation yet. Provide an option to resend the invite.
- **Self-Management:** Users should not be able to change their own role (unless they are the sole Admin) or remove themselves (unless specific logic allows it).

## 8. AI Integration

- **No Direct Integration:** User roles and permissions are primarily an administrative and security function.
- **Indirect Impact:** AI features and data visibility within other sections might be restricted based on user roles (e.g., only Admins can see detailed AI cost analysis).

## 9. Error Handling Rules

- **API Errors:** Display errors if fetching the user list fails. Provide specific error messages for failed invitations (e.g., "Invalid email address", "User already exists"), role changes, or removals.
- **Permission Denied:** If a non-Admin attempts an admin action (either via UI manipulation or direct API call), the backend must return a clear "Forbidden" (403) error. The frontend should handle this gracefully, perhaps showing a disabled state or a generic access denied message.
- **Invitation Errors:** Handle cases where the email service fails to send the invitation.
- **Removing Last Admin:** Prevent the removal of the last user with an Admin role to avoid locking everyone out of account management. The backend must enforce this rule.

## 10. Logging and Usage Tracking Expectations

- **Log:**
    - All user invitations sent (inviter, invitee email, assigned role).
    - Invitation acceptance events.
    - User role changes (admin performing change, user affected, old role, new role).
    - User removals (admin performing removal, user removed).
    - Failed attempts at admin actions by non-admins (permission denied errors).
    - Errors during invitation sending, role changes, or removals.
- **Track:**
    - Views of the user management section.
    - Number of invitations sent over time.
    - Frequency of role changes.
    - Number of users removed.
    - Use of filtering/searching within the user list.
