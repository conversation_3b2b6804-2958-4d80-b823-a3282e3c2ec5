import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import AIAssistantManager from '../../components/automation/AIAssistantManager';
import axios from 'axios';

// Mock dependencies
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock the child components to simplify testing
jest.mock('../../components/automation/AIPersonalitySelector', () => ({
  __esModule: true,
  default: ({ selectedPersonality, onChange, disabled }) => (
    <div data-testid="personality-selector">
      <select 
        data-testid="personality-select" 
        value={selectedPersonality} 
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
      >
        <option value="professional">Professional</option>
        <option value="friendly">Friendly</option>
        <option value="technical">Technical</option>
      </select>
    </div>
  )
}));

jest.mock('../../components/automation/AIKnowledgeUploader', () => ({
  __esModule: true,
  default: ({ knowledgeSources, onUpdate, disabled }) => (
    <div data-testid="knowledge-uploader">
      <button 
        data-testid="add-knowledge" 
        onClick={() => onUpdate([...knowledgeSources, { id: 'new-id', name: 'New Source', type: 'document' }])}
        disabled={disabled}
      >
        Add Knowledge
      </button>
    </div>
  )
}));

jest.mock('../../components/automation/AICustomCommandsEditor', () => ({
  __esModule: true,
  default: ({ commands, onUpdate, disabled }) => (
    <div data-testid="commands-editor">
      <button 
        data-testid="add-command" 
        onClick={() => onUpdate([...commands, { id: 'new-id', trigger: 'New Trigger', response: 'New Response' }])}
        disabled={disabled}
      >
        Add Command
      </button>
    </div>
  )
}));

jest.mock('../../components/automation/AIConversationTester', () => ({
  __esModule: true,
  default: ({ numberId, mode }) => (
    <div data-testid={`conversation-tester-${mode}`}>
      AI Conversation Tester for {mode}
    </div>
  )
}));

describe('AIAssistantManager', () => {
  let queryClient: QueryClient;
  
  const initialData = {
    personality: 'professional',
    knowledgeSources: [
      { id: '1', name: 'Company FAQ', type: 'document' }
    ],
    commands: [
      { id: '1', trigger: 'hours', response: 'We are open from 9am to 5pm.' }
    ],
    organizationId: 'org_123'
  };
  
  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock successful API response
    mockedAxios.put.mockResolvedValue({
      data: { success: true }
    });
  });
  
  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <AIAssistantManager numberId="123" initialData={initialData} />
      </QueryClientProvider>
    );
  };
  
  test('should render configuration tab with initial data', () => {
    renderComponent();
    
    expect(screen.getByText('AI Assistant Configuration')).toBeInTheDocument();
    expect(screen.getByText('Assistant Personality')).toBeInTheDocument();
    expect(screen.getByText('Knowledge Base')).toBeInTheDocument();
    expect(screen.getByText('Custom Commands')).toBeInTheDocument();
    
    expect(screen.getByTestId('personality-selector')).toBeInTheDocument();
    expect(screen.getByTestId('knowledge-uploader')).toBeInTheDocument();
    expect(screen.getByTestId('commands-editor')).toBeInTheDocument();
    
    expect(screen.getByText('Save Changes')).toBeInTheDocument();
  });
  
  test('should switch between tabs', () => {
    renderComponent();
    
    // Initial tab should be Configuration
    expect(screen.getByText('AI Assistant Configuration')).toBeInTheDocument();
    
    // Switch to Call Test tab
    fireEvent.click(screen.getByText('Test Call'));
    expect(screen.getByTestId('conversation-tester-call')).toBeInTheDocument();
    expect(screen.getByText('Test Call AI Response')).toBeInTheDocument();
    
    // Switch to SMS Test tab
    fireEvent.click(screen.getByText('Test SMS'));
    expect(screen.getByTestId('conversation-tester-sms')).toBeInTheDocument();
    expect(screen.getByText('Test SMS AI Response')).toBeInTheDocument();
    
    // Switch back to Configuration tab
    fireEvent.click(screen.getByText('Configuration'));
    expect(screen.getByText('AI Assistant Configuration')).toBeInTheDocument();
  });
  
  test('should update personality when changed', () => {
    renderComponent();
    
    const personalitySelect = screen.getByTestId('personality-select');
    fireEvent.change(personalitySelect, { target: { value: 'friendly' } });
    
    // Check save button and click it
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);
    
    // Check that the API was called with updated data
    expect(mockedAxios.put).toHaveBeenCalledWith('/api/numbers/123/ai-assistant', {
      ...initialData,
      personality: 'friendly'
    });
  });
  
  test('should update knowledge sources when changed', () => {
    renderComponent();
    
    const addKnowledgeButton = screen.getByTestId('add-knowledge');
    fireEvent.click(addKnowledgeButton);
    
    // Check save button and click it
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);
    
    // Check that the API was called with updated data
    expect(mockedAxios.put).toHaveBeenCalledWith('/api/numbers/123/ai-assistant', {
      ...initialData,
      knowledgeSources: [
        ...initialData.knowledgeSources,
        { id: 'new-id', name: 'New Source', type: 'document' }
      ]
    });
  });
  
  test('should update commands when changed', () => {
    renderComponent();
    
    const addCommandButton = screen.getByTestId('add-command');
    fireEvent.click(addCommandButton);
    
    // Check save button and click it
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);
    
    // Check that the API was called with updated data
    expect(mockedAxios.put).toHaveBeenCalledWith('/api/numbers/123/ai-assistant', {
      ...initialData,
      commands: [
        ...initialData.commands,
        { id: 'new-id', trigger: 'New Trigger', response: 'New Response' }
      ]
    });
  });
  
  test('should show loading state when saving', async () => {
    // Make API call take some time
    mockedAxios.put.mockImplementationOnce(() => 
      new Promise(resolve => setTimeout(() => resolve({ data: { success: true } }), 100))
    );
    
    renderComponent();
    
    // Click save button
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);
    
    // Check for loading state
    expect(screen.getByText('Saving...')).toBeInTheDocument();
    
    // Wait for save to complete
    await waitFor(() => {
      expect(screen.getByText('Save Changes')).toBeInTheDocument();
    });
  });
  
  test('should handle API errors gracefully', async () => {
    // Mock console.error to prevent error logs in test output
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    // Make API call fail
    mockedAxios.put.mockRejectedValueOnce(new Error('API Error'));
    
    renderComponent();
    
    // Click save button
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);
    
    // Wait for error handling
    await waitFor(() => {
      expect(console.error).toHaveBeenCalledWith('Failed to update AI assistant configuration');
    });
    
    // Check that we're back to normal state
    expect(screen.getByText('Save Changes')).toBeInTheDocument();
  });
});
