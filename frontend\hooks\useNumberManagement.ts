import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { numberManagementMockData } from '../lib/mockData';

// Create an axios instance for API calls
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Flag to determine if we should use mock data
const USE_MOCK_DATA = process.env.NODE_ENV === 'development';

// Types for number management
export interface AvailableNumber {
  id: string;
  phoneNumber: string;
  location: string;
  capabilities: string[];
  type: string;
  cost: {
    setup: number;
    monthly: number;
  };
}

export interface OwnedNumber {
  id: string;
  phoneNumber: string;
  friendlyName: string;
  status: string;
  type: string;
  capabilities: string[];
  acquiredDate: string;
  automationLink?: string;
}

export interface SearchParams {
  country?: string;
  areaCode?: string;
  capabilities?: string[];
  type?: string;
}

export interface NumberUpdateParams {
  friendlyName?: string;
  // Add other updatable fields as needed
}

/**
 * Hook for managing phone numbers
 */
export function useNumberManagement() {
  const queryClient = useQueryClient();

  // Fetch owned numbers
  const ownedNumbersQuery = useQuery({
    queryKey: ['numbers', 'owned'],
    queryFn: async () => {
      if (USE_MOCK_DATA) {
        // Return mock data
        return numberManagementMockData.ownedNumbers;
      }
      const { data } = await api.get<OwnedNumber[]>('/numbers/owned');
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Search for available numbers
  const searchAvailableNumbers = (searchParams: SearchParams) => {
    return useQuery({
      queryKey: ['numbers', 'available', searchParams],
      queryFn: async () => {
        if (USE_MOCK_DATA) {
          // Return mock data with a slight delay to simulate API call
          await new Promise(resolve => setTimeout(resolve, 500));
          return numberManagementMockData.availableNumbers;
        }
        const { data } = await api.get<AvailableNumber[]>('/numbers/available', {
          params: searchParams,
        });
        return data;
      },
      enabled: false, // Don't run automatically, only when triggered
      staleTime: 60 * 1000, // 1 minute
    });
  };

  // Purchase a number
  const purchaseNumberMutation = useMutation({
    mutationFn: async (numberIdentifier: string) => {
      if (USE_MOCK_DATA) {
        // Simulate API call with a delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { data: { success: true } };
      }
      return api.post('/numbers/purchase', { numberIdentifier });
    },
    onSuccess: () => {
      // Invalidate owned numbers query to refresh the list
      queryClient.invalidateQueries({ queryKey: ['numbers', 'owned'] });
      // Invalidate user balance
      queryClient.invalidateQueries({ queryKey: ['user', 'balance'] });
    },
  });

  // Update number settings
  const updateNumberMutation = useMutation({
    mutationFn: async ({ numberId, updateData }: { numberId: string; updateData: NumberUpdateParams }) => {
      if (USE_MOCK_DATA) {
        // Simulate API call with a delay
        await new Promise(resolve => setTimeout(resolve, 800));
        return { data: { success: true } };
      }
      return api.put(`/numbers/${numberId}`, updateData);
    },
    onSuccess: () => {
      // Invalidate owned numbers query to refresh the list
      queryClient.invalidateQueries({ queryKey: ['numbers', 'owned'] });
    },
  });

  // Release a number
  const releaseNumberMutation = useMutation({
    mutationFn: async (numberId: string) => {
      if (USE_MOCK_DATA) {
        // Simulate API call with a delay
        await new Promise(resolve => setTimeout(resolve, 1200));
        return { data: { success: true } };
      }
      return api.delete(`/numbers/${numberId}`);
    },
    onSuccess: () => {
      // Invalidate owned numbers query to refresh the list
      queryClient.invalidateQueries({ queryKey: ['numbers', 'owned'] });
    },
  });

  return {
    // Queries
    ownedNumbers: {
      data: ownedNumbersQuery.data || [],
      isLoading: ownedNumbersQuery.isLoading,
      error: ownedNumbersQuery.error,
      refetch: ownedNumbersQuery.refetch,
    },
    searchAvailableNumbers,

    // Mutations
    purchaseNumber: purchaseNumberMutation.mutate,
    isPurchasing: purchaseNumberMutation.isPending,
    purchaseError: purchaseNumberMutation.error,

    updateNumber: updateNumberMutation.mutate,
    isUpdating: updateNumberMutation.isPending,
    updateError: updateNumberMutation.error,

    releaseNumber: releaseNumberMutation.mutate,
    isReleasing: releaseNumberMutation.isPending,
    releaseError: releaseNumberMutation.error,
  };
}
