import { NextResponse } from 'next/server';

export async function GET(request) {
  try {
    // Get search parameters from the request URL
    const { searchParams } = new URL(request.url);
    
    // Return a debug response with the params
    return NextResponse.json({
      message: 'Debug API is working',
      params: Object.fromEntries(searchParams.entries()),
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error in debug API:', error);
    return NextResponse.json(
      { error: 'Debug API error', message: error.message }, 
      { status: 500 }
    );
  }
} 