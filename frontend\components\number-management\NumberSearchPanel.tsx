'use client';

import { useState } from 'react';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { useNumberManagement, SearchParams } from '../../hooks/useNumberManagement';
import NumberSearchForm from './NumberSearchForm';
import AvailableNumbersList from './AvailableNumbersList';
import LoadingSpinner from '../shared/LoadingSpinner';

interface NumberSearchPanelProps {
  onPurchaseClick: (number: any) => void;
}

export default function NumberSearchPanel({ onPurchaseClick }: NumberSearchPanelProps) {
  const [searchParams, setSearchParams] = useState<SearchParams>({
    country: 'US',
    areaCode: '',
    capabilities: [],
    type: 'local'
  });

  const { searchAvailableNumbers } = useNumberManagement();
  const {
    data: availableNumbers,
    isLoading,
    error,
    refetch
  } = searchAvailableNumbers(searchParams);

  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = (params: SearchParams) => {
    setSearchParams(params);
    setHasSearched(true);
    refetch();
  };

  return (
    <div>
      <div className="mb-4 sm:mb-6 md:mb-8">
        <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-3 sm:mb-4">
          Search for Available Numbers
        </h2>
        <NumberSearchForm onSubmit={handleSearch} isSearching={isLoading} />
      </div>

      {isLoading && (
        <div className="flex justify-center items-center py-6 sm:py-8 md:py-12">
          <LoadingSpinner size="large" />
        </div>
      )}

      {!isLoading && hasSearched && (
        <div>
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-3 sm:mb-4">
            Search Results
          </h2>
          <AvailableNumbersList
            numbers={availableNumbers || []}
            isLoading={isLoading}
            error={error as Error}
            onPurchaseClick={onPurchaseClick}
          />
        </div>
      )}

      {!isLoading && !hasSearched && (
        <div className="text-center py-6 sm:py-8 md:py-12">
          <MagnifyingGlassIcon className="h-10 w-10 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-3 sm:mb-4" />
          <h3 className="text-base sm:text-lg font-medium text-gray-900 dark:text-white">
            Search for a phone number
          </h3>
          <p className="text-sm sm:text-base text-gray-500 dark:text-gray-400 mt-2 max-w-md mx-auto">
            Use the search form above to find available phone numbers. You can filter by country, area code, and capabilities.
          </p>
        </div>
      )}
    </div>
  );
}
