'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../lib/apiClient';
import toast from 'react-hot-toast';

// Types for scheduled automations
export interface ScheduledAutomation {
  id: string;
  name: string;
  description?: string;
  type: 'call' | 'sms';
  status: 'enabled' | 'disabled';
  scheduleSummary: string;
  schedule: AutomationSchedule;
  conditions: AutomationCondition[];
  templateId: string;
  createdAt: string;
  lastRun?: string;
  nextRun?: string;
}

export interface AutomationSchedule {
  type: 'once' | 'recurring';
  dateTime?: string; // ISO string for 'once' type
  cronExpression?: string; // For 'recurring' type
  timezone: string;
  endDate?: string; // Optional end date for recurring schedules
}

export interface AutomationCondition {
  id: string;
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than';
  value: string | number | boolean;
  type: 'contact' | 'event' | 'custom';
}

export interface AutomationTemplate {
  id: string;
  name: string;
  type: 'call' | 'sms';
  preview: string;
}

export interface AutomationHistoryItem {
  id: string;
  automationId: string;
  timestamp: string;
  status: 'success' | 'failed' | 'pending';
  target: string; // Phone number or contact name
  result?: string;
  error?: string;
}

export interface AutomationStats {
  successRate: number;
  totalExecutions: number;
  errorCount: number;
  chartData: {
    date: string;
    successCount: number;
    failureCount: number;
  }[];
}

export interface CreateAutomationParams {
  name: string;
  description?: string;
  type: 'call' | 'sms';
  schedule: AutomationSchedule;
  conditions: AutomationCondition[];
  templateId: string;
  isEnabled?: boolean;
}

export interface UpdateAutomationParams {
  id: string;
  name?: string;
  description?: string;
  type?: 'call' | 'sms';
  schedule?: AutomationSchedule;
  conditions?: AutomationCondition[];
  templateId?: string;
  isEnabled?: boolean;
}

export interface ToggleAutomationParams {
  id: string;
  isEnabled: boolean;
}

export interface HistoryQueryParams {
  page?: number;
  limit?: number;
  automationId?: string;
  status?: 'success' | 'failed' | 'pending';
}

export interface StatsQueryParams {
  timeRange?: 'day' | 'week' | 'month';
  automationId?: string;
}

/**
 * Hook for managing scheduled automations
 */
export function useScheduledAutomation() {
  const queryClient = useQueryClient();

  // Fetch all automations
  const automationsQuery = useQuery({
    queryKey: ['automations'],
    queryFn: () => api.get<ScheduledAutomation[]>('/api/automations'),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch a specific automation
  const getAutomation = (automationId: string) => {
    return useQuery({
      queryKey: ['automations', automationId],
      queryFn: () => api.get<ScheduledAutomation>(`/api/automations/${automationId}`),
      staleTime: 5 * 60 * 1000, // 5 minutes
      enabled: !!automationId,
    });
  };

  // Fetch automation history
  const getAutomationHistory = (params: HistoryQueryParams = {}) => {
    return useQuery({
      queryKey: ['automations', 'history', params],
      queryFn: () => api.get<{ data: AutomationHistoryItem[], totalPages: number }>('/api/automations/history', { params }),
      staleTime: 60 * 1000, // 1 minute
    });
  };

  // Fetch automation stats
  const getAutomationStats = (params: StatsQueryParams = {}) => {
    return useQuery({
      queryKey: ['automations', 'stats', params],
      queryFn: () => api.get<AutomationStats>('/api/automations/stats', { params }),
      staleTime: 5 * 60 * 1000, // 5 minutes
    });
  };

  // Fetch templates
  const getTemplates = (type?: 'call' | 'sms') => {
    return useQuery({
      queryKey: ['templates', type],
      queryFn: () => api.get<AutomationTemplate[]>('/api/templates', { params: { type } }),
      staleTime: 10 * 60 * 1000, // 10 minutes
    });
  };

  // Create automation
  const createAutomationMutation = useMutation({
    mutationFn: (params: CreateAutomationParams) => {
      return api.post<{ id: string }>('/api/automations', params);
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['automations'] });
      
      // Show success toast
      toast.success('Automation created successfully');
    },
    onError: (error: any) => {
      // Show error toast
      toast.error(error.message || 'Failed to create automation');
    },
  });

  // Update automation
  const updateAutomationMutation = useMutation({
    mutationFn: (params: UpdateAutomationParams) => {
      return api.put<{ success: boolean }>(`/api/automations/${params.id}`, params);
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['automations'] });
      queryClient.invalidateQueries({ queryKey: ['automations', variables.id] });
      
      // Show success toast
      toast.success('Automation updated successfully');
    },
    onError: (error: any) => {
      // Show error toast
      toast.error(error.message || 'Failed to update automation');
    },
  });

  // Toggle automation status
  const toggleAutomationMutation = useMutation({
    mutationFn: (params: ToggleAutomationParams) => {
      return api.put<{ success: boolean }>(`/api/automations/${params.id}/status`, { isEnabled: params.isEnabled });
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['automations'] });
      queryClient.invalidateQueries({ queryKey: ['automations', variables.id] });
      
      // Show success toast
      toast.success(`Automation ${variables.isEnabled ? 'enabled' : 'disabled'}`);
    },
    onError: (error: any) => {
      // Show error toast
      toast.error(error.message || 'Failed to update automation status');
    },
  });

  // Delete automation
  const deleteAutomationMutation = useMutation({
    mutationFn: (automationId: string) => {
      return api.delete<{ success: boolean }>(`/api/automations/${automationId}`);
    },
    onSuccess: (_, automationId) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['automations'] });
      
      // Show success toast
      toast.success('Automation deleted');
    },
    onError: (error: any) => {
      // Show error toast
      toast.error(error.message || 'Failed to delete automation');
    },
  });

  return {
    // Queries
    automationsQuery,
    getAutomation,
    getAutomationHistory,
    getAutomationStats,
    getTemplates,
    
    // Mutations
    createAutomation: createAutomationMutation.mutate,
    updateAutomation: updateAutomationMutation.mutate,
    toggleAutomation: toggleAutomationMutation.mutate,
    deleteAutomation: deleteAutomationMutation.mutate,
    
    // Loading states
    isCreating: createAutomationMutation.isPending,
    isUpdating: updateAutomationMutation.isPending,
    isTogglingStatus: toggleAutomationMutation.isPending,
    isDeleting: deleteAutomationMutation.isPending,
  };
}
