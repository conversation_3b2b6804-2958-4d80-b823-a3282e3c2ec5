"use client";

import React, { useState } from 'react';
import { 
  ArrowPathIcon, 
  BeakerIcon, 
  PresentationChartLineIcon,
  LightBulbIcon,
  ArrowTrendingUpIcon,
  ArrowUpRightIcon
} from '@heroicons/react/24/outline';

export function AIResponseAnalyzer() {
  const [isLoading, setIsLoading] = useState(false);
  const [responseText, setResponseText] = useState('');
  const [analysisResult, setAnalysisResult] = useState(null);
  
  // Simulate submitting for analysis
  const handleSubmitForAnalysis = () => {
    if (!responseText.trim()) return;
    
    setIsLoading(true);
    
    // Simulate API call with timeout
    setTimeout(() => {
      // Mock analysis result
      const result = {
        score: 87,
        sentiment: 'Positive',
        responseTime: '12 seconds',
        wordCount: responseText.split(/\s+/).filter(Boolean).length,
        keyMetrics: {
          clarity: 92,
          empathy: 85,
          professionalism: 88,
          accuracy: 84
        },
        improvements: [
          'Consider acknowledging the customer\'s frustration more explicitly',
          'Provide more specific next steps for troubleshooting',
          'Reduce technical jargon for better accessibility'
        ],
        strengths: [
          'Clear explanation of the problem',
          'Friendly and approachable tone',
          'Good use of follow-up questions to confirm understanding'
        ]
      };
      
      setAnalysisResult(result);
      setIsLoading(false);
    }, 2000);
  };
  
  // Sample responses for quick testing
  const sampleResponses = [
    "I understand you're experiencing issues with your internet connection. Let me help troubleshoot this. First, could you tell me if your router's lights are on and if you've tried restarting it in the last 24 hours?",
    "Thank you for reaching out about your billing concern. I can see there was an extra charge of $24.99 on your latest statement. This appears to be for the premium support package that was added on March 15th. Would you like me to remove this from your account and process a refund?",
    "I'm sorry to hear about the problems with your recent order. I can see that your package shows as delivered yesterday at 2:15 PM, but you haven't received it. Let me help by filing a missing package claim right away and expediting a replacement item to you. Would that work for your situation?"
  ];
  
  return (
    <div className="h-full flex flex-col overflow-hidden bg-gray-900">
      <div className="p-5 sm:p-6 border-b border-gray-800">
        <h3 className="text-xl font-bold text-white flex items-center">
          <BeakerIcon className="h-5 w-5 mr-2 text-indigo-400" />
          AI Response Analyzer
        </h3>
        <p className="text-gray-400 text-sm mt-1">
          Analyze and improve your AI-powered customer communications
        </p>
      </div>
      
      <div className="p-5 sm:p-6 flex-grow flex flex-col">
        <div className="mb-5">
          <label htmlFor="response-text" className="block text-sm font-medium text-gray-300 mb-2">
            Enter AI response for analysis
          </label>
          <textarea
            id="response-text"
            rows={6}
            value={responseText}
            onChange={(e) => setResponseText(e.target.value)}
            placeholder="Paste or type the AI response you want to analyze..."
            className="w-full p-3 bg-gray-900 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-white"
          />
          
          {/* Quick samples */}
          <div className="mt-3">
            <p className="text-sm text-gray-400 mb-2">Or try a sample response:</p>
            <div className="flex flex-wrap gap-2">
              {sampleResponses.map((sample, index) => (
                <button
                  key={index}
                  onClick={() => setResponseText(sample)}
                  className="px-3 py-1.5 bg-gray-800 hover:bg-gray-700 text-sm text-gray-300 rounded-md transition-colors"
                >
                  Sample {index + 1}
                </button>
              ))}
            </div>
          </div>
        </div>
        
        <button
          onClick={handleSubmitForAnalysis}
          disabled={isLoading || !responseText.trim()}
          className={`w-full py-3 rounded-lg font-medium flex items-center justify-center transition-colors ${
            isLoading || !responseText.trim()
              ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
              : 'bg-indigo-600 hover:bg-indigo-700 text-white'
          }`}
        >
          {isLoading ? (
            <>
              <ArrowPathIcon className="h-5 w-5 mr-2 animate-spin" />
              Analyzing Response...
            </>
          ) : (
            <>
              <PresentationChartLineIcon className="h-5 w-5 mr-2" />
              Analyze Response
            </>
          )}
        </button>
        
        {/* Analysis Results */}
        {analysisResult && (
          <div className="mt-8 animate-slideIn">
            <h4 className="text-lg font-medium text-white mb-4 flex items-center">
              <ArrowTrendingUpIcon className="h-5 w-5 mr-2 text-indigo-400" />
              Analysis Results
            </h4>
            
            {/* Score Overview */}
            <div className="bg-gray-800/50 rounded-lg p-5 mb-5 border border-gray-700/50">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-400">Overall Quality Score</p>
                  <div className="text-3xl font-bold text-white mt-1">{analysisResult.score}/100</div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 mt-4 sm:mt-0">
                  <div>
                    <p className="text-xs text-gray-500">Sentiment</p>
                    <p className="text-sm font-medium text-green-400">{analysisResult.sentiment}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Response Time</p>
                    <p className="text-sm font-medium text-white">{analysisResult.responseTime}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Word Count</p>
                    <p className="text-sm font-medium text-white">{analysisResult.wordCount} words</p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Key Metrics */}
            <div className="mb-6">
              <h5 className="text-sm font-medium text-gray-300 mb-3">Key Metrics</h5>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(analysisResult.keyMetrics).map(([key, value]) => (
                  <div key={key} className="bg-gray-800/30 rounded-lg p-3 border border-gray-800">
                    <p className="text-xs text-gray-400 capitalize">{key}</p>
                    <div className="mt-2 flex items-center justify-between">
                      <div className="w-full h-1.5 bg-gray-700 rounded-full overflow-hidden mr-2">
                        <div 
                          className="h-full bg-indigo-500 rounded-full" 
                          style={{ width: `${value}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-white">{value}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Improvement Suggestions */}
            <div className="mb-6">
              <h5 className="text-sm font-medium text-gray-300 mb-3 flex items-center">
                <LightBulbIcon className="h-4 w-4 mr-1.5 text-yellow-400" />
                Suggested Improvements
              </h5>
              <ul className="space-y-2">
                {analysisResult.improvements.map((improvement, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-yellow-400 mr-2">•</span>
                    <span className="text-sm text-gray-300">{improvement}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            {/* Strengths */}
            <div>
              <h5 className="text-sm font-medium text-gray-300 mb-3 flex items-center">
                <ArrowUpRightIcon className="h-4 w-4 mr-1.5 text-green-400" />
                Response Strengths
              </h5>
              <ul className="space-y-2">
                {analysisResult.strengths.map((strength, index) => (
                  <li key={index} className="flex items-start">
                    <span className="text-green-400 mr-2">•</span>
                    <span className="text-sm text-gray-300">{strength}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 