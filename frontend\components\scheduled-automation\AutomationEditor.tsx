'use client';

import { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { 
  ScheduledAutomation, 
  CreateAutomationParams,
  UpdateAutomationParams,
  useScheduledAutomation 
} from '../../hooks/useScheduledAutomation';
import BasicInfoForm from './editor/BasicInfoForm';
import ScheduleConfigurator from './editor/ScheduleConfigurator';
import ConditionBuilder from './editor/ConditionBuilder';
import TemplateSelector from './editor/TemplateSelector';
import ActionPreview from './editor/ActionPreview';
import LoadingSpinner from '../shared/LoadingSpinner';

interface AutomationEditorProps {
  automation: ScheduledAutomation | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

// Steps in the editor
type EditorStep = 'basic' | 'schedule' | 'conditions' | 'template' | 'preview';

export default function AutomationEditor({
  automation,
  isOpen,
  onClose,
  onSave,
}: AutomationEditorProps) {
  // Current step in the editor
  const [currentStep, setCurrentStep] = useState<EditorStep>('basic');
  
  // Form data
  const [formData, setFormData] = useState<CreateAutomationParams>({
    name: '',
    description: '',
    type: 'call',
    schedule: {
      type: 'once',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    },
    conditions: [],
    templateId: '',
    isEnabled: true,
  });
  
  const { createAutomation, updateAutomation, isCreating, isUpdating } = useScheduledAutomation();
  
  // Reset form when opening the editor
  useEffect(() => {
    if (isOpen) {
      // If editing an existing automation, populate the form
      if (automation) {
        setFormData({
          name: automation.name,
          description: automation.description || '',
          type: automation.type,
          schedule: automation.schedule,
          conditions: automation.conditions,
          templateId: automation.templateId,
          isEnabled: automation.status === 'enabled',
        });
      } else {
        // Reset form for new automation
        setFormData({
          name: '',
          description: '',
          type: 'call',
          schedule: {
            type: 'once',
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          },
          conditions: [],
          templateId: '',
          isEnabled: true,
        });
      }
      
      // Always start at the first step
      setCurrentStep('basic');
    }
  }, [isOpen, automation]);
  
  // Handle form field changes
  const handleChange = (field: keyof CreateAutomationParams, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };
  
  // Handle saving the automation
  const handleSave = () => {
    if (automation) {
      // Update existing automation
      const updateParams: UpdateAutomationParams = {
        id: automation.id,
        ...formData,
      };
      updateAutomation(updateParams, {
        onSuccess: () => {
          onSave();
        },
      });
    } else {
      // Create new automation
      createAutomation(formData, {
        onSuccess: () => {
          onSave();
        },
      });
    }
  };
  
  // Navigate to the next step
  const goToNextStep = () => {
    switch (currentStep) {
      case 'basic':
        setCurrentStep('schedule');
        break;
      case 'schedule':
        setCurrentStep('conditions');
        break;
      case 'conditions':
        setCurrentStep('template');
        break;
      case 'template':
        setCurrentStep('preview');
        break;
      case 'preview':
        handleSave();
        break;
    }
  };
  
  // Navigate to the previous step
  const goToPreviousStep = () => {
    switch (currentStep) {
      case 'schedule':
        setCurrentStep('basic');
        break;
      case 'conditions':
        setCurrentStep('schedule');
        break;
      case 'template':
        setCurrentStep('conditions');
        break;
      case 'preview':
        setCurrentStep('template');
        break;
    }
  };
  
  // Check if the current step is valid and can proceed
  const canProceed = () => {
    switch (currentStep) {
      case 'basic':
        return formData.name.trim() !== '' && formData.type !== undefined;
      case 'schedule':
        return (
          formData.schedule.type === 'once' && formData.schedule.dateTime ||
          formData.schedule.type === 'recurring' && formData.schedule.cronExpression
        );
      case 'conditions':
        return true; // Conditions are optional
      case 'template':
        return formData.templateId !== '';
      case 'preview':
        return true;
      default:
        return false;
    }
  };
  
  // Render the current step content
  const renderStepContent = () => {
    switch (currentStep) {
      case 'basic':
        return (
          <BasicInfoForm
            name={formData.name}
            description={formData.description}
            type={formData.type}
            onChange={handleChange}
          />
        );
      case 'schedule':
        return (
          <ScheduleConfigurator
            schedule={formData.schedule}
            onChange={(schedule) => handleChange('schedule', schedule)}
          />
        );
      case 'conditions':
        return (
          <ConditionBuilder
            conditions={formData.conditions}
            onChange={(conditions) => handleChange('conditions', conditions)}
          />
        );
      case 'template':
        return (
          <TemplateSelector
            templateType={formData.type}
            selectedTemplateId={formData.templateId}
            onChange={(templateId) => handleChange('templateId', templateId)}
          />
        );
      case 'preview':
        return (
          <ActionPreview
            automationConfig={formData}
            isEnabled={formData.isEnabled || false}
            onToggleEnabled={(isEnabled) => handleChange('isEnabled', isEnabled)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex justify-between items-center mb-4">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-900 dark:text-white"
                  >
                    {automation ? 'Edit Automation' : 'Create New Automation'}
                  </Dialog.Title>
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-500 focus:outline-none"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                {/* Step indicator */}
                <div className="mb-6">
                  <div className="flex items-center justify-between">
                    {['basic', 'schedule', 'conditions', 'template', 'preview'].map((step, index) => (
                      <div key={step} className="flex flex-col items-center">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            currentStep === step
                              ? 'bg-blue-600 text-white'
                              : index < ['basic', 'schedule', 'conditions', 'template', 'preview'].indexOf(currentStep)
                              ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                              : 'bg-gray-200 text-gray-500 dark:bg-gray-700 dark:text-gray-400'
                          }`}
                        >
                          {index + 1}
                        </div>
                        <span className="text-xs mt-1 text-gray-500 dark:text-gray-400">
                          {step.charAt(0).toUpperCase() + step.slice(1)}
                        </span>
                      </div>
                    ))}
                  </div>
                  <div className="relative mt-2">
                    <div className="absolute top-0 left-0 right-0 h-1 bg-gray-200 dark:bg-gray-700"></div>
                    <div
                      className="absolute top-0 left-0 h-1 bg-blue-600 transition-all"
                      style={{
                        width: `${
                          ((['basic', 'schedule', 'conditions', 'template', 'preview'].indexOf(currentStep) + 1) /
                            5) *
                          100
                        }%`,
                      }}
                    ></div>
                  </div>
                </div>

                {/* Step content */}
                <div className="mb-6 min-h-[300px]">
                  {renderStepContent()}
                </div>

                {/* Navigation buttons */}
                <div className="flex justify-between mt-6">
                  <button
                    type="button"
                    onClick={goToPreviousStep}
                    disabled={currentStep === 'basic'}
                    className={`px-4 py-2 rounded-md ${
                      currentStep === 'basic'
                        ? 'bg-gray-200 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    Back
                  </button>
                  
                  <button
                    type="button"
                    onClick={goToNextStep}
                    disabled={!canProceed() || isCreating || isUpdating}
                    className={`px-4 py-2 rounded-md ${
                      !canProceed() || isCreating || isUpdating
                        ? 'bg-blue-300 text-white dark:bg-blue-800 dark:text-gray-300 cursor-not-allowed'
                        : 'bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700'
                    }`}
                  >
                    {isCreating || isUpdating ? (
                      <div className="flex items-center">
                        <LoadingSpinner size="sm" color="white" />
                        <span className="ml-2">
                          {currentStep === 'preview' ? 'Saving...' : 'Next...'}
                        </span>
                      </div>
                    ) : (
                      currentStep === 'preview' ? 'Save Automation' : 'Next'
                    )}
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
