"use client";

import React, { useState } from 'react';
import {
  UserCircleIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  CalendarIcon,
  TagIcon,
  PencilIcon,
  ArrowPathIcon,
  ArrowTrendingUpIcon,
  ShieldCheckIcon,
  ChatBubbleLeftRightIcon,
  EllipsisHorizontalIcon,
  HeartIcon,
  HandThumbUpIcon,
  LinkIcon,
  IdentificationIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/outline';

export function CustomerProfile({ customerId }) {
  // In a real app, fetch customer data based on the customerId
  const [isEditing, setIsEditing] = useState(false);
  
  // Mock customer data
  const [customer, setCustomer] = useState({
    id: 'cust-001',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Main Street, Anytown, CA 90210',
    joinDate: 'Jan 15, 2023',
    lastContact: 'Mar 16, 2023',
    loyaltyTier: 'Gold',
    lifetimeValue: '$1,845.75',
    interactionCount: 23,
    preferredContact: 'Phone',
    notes: '<PERSON> prefers to be contacted in the evenings after 6 PM. He has mentioned interest in our premium plans during previous conversations.',
    tags: ['Premium Customer', 'High Priority', 'Renewal Due'],
    profileCompletion: 85
  });
  
  // Mock interaction history
  const interactionHistory = [
    {
      id: 'int-001',
      type: 'Call',
      date: 'Mar 14, 2023',
      duration: '8:32',
      outcome: 'Resolved',
      agent: 'AI Assistant',
      summary: 'Customer called about upgrading his subscription. Details provided about premium features and pricing.'
    },
    {
      id: 'int-002',
      type: 'SMS',
      date: 'Mar 12, 2023',
      outcome: 'Resolved',
      agent: 'AI Assistant',
      summary: 'Customer inquired about billing cycle. Clarified that billing occurs on the 15th of each month.'
    },
    {
      id: 'int-003',
      type: 'Email',
      date: 'Feb 28, 2023',
      outcome: 'Resolved',
      agent: 'Sarah Chen',
      summary: 'Sent welcome email with account setup instructions and onboarding materials.'
    },
    {
      id: 'int-004',
      type: 'Call',
      date: 'Feb 26, 2023',
      duration: '4:15',
      outcome: 'Resolved',
      agent: 'James Wilson',
      summary: 'Initial sales call. Customer expressed interest in our services and requested additional information.'
    }
  ];
  
  // Mock purchase history
  const purchaseHistory = [
    {
      id: 'pur-001',
      date: 'Mar 15, 2023',
      product: 'Premium Plan - Annual',
      amount: '$899.99',
      status: 'Completed'
    },
    {
      id: 'pur-002',
      date: 'Feb 27, 2023',
      product: 'Basic Plan - Monthly',
      amount: '$89.99',
      status: 'Completed'
    },
    {
      id: 'pur-003',
      date: 'Feb 27, 2023',
      product: 'Setup Assistance',
      amount: '$49.99',
      status: 'Completed'
    }
  ];
  
  // Mock recommendations
  const recommendations = [
    {
      id: 'rec-001',
      title: 'Premium Support Add-on',
      description: 'Based on recent support interactions, customer may benefit from our premium support package.',
      confidence: 'High'
    },
    {
      id: 'rec-002',
      title: 'Data Backup Service',
      description: 'Customer has significant usage and would benefit from our automated backup service.',
      confidence: 'Medium'
    },
    {
      id: 'rec-003',
      title: 'Schedule Quarterly Review',
      description: 'Customer is a high-value client who would benefit from regular account reviews.',
      confidence: 'High'
    }
  ];
  
  // Handle editing customer info
  const handleEdit = () => {
    setIsEditing(!isEditing);
    if (isEditing) {
      // In a real app, save changes to the backend
      // For this mock, we just toggle the edit state
    }
  };
  
  return (
    <div className="bg-gray-900 text-white">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left column - Customer Info */}
        <div className="lg:col-span-1">
          <div className="border border-gray-800 bg-gray-900 rounded-xl flex flex-col h-full">
            {/* Profile header */}
            <div className="p-6 border-b border-gray-800 flex flex-col items-center">
              <div className="relative">
                <div className="h-24 w-24 bg-indigo-900/50 rounded-full flex items-center justify-center mb-4">
                  <UserCircleIcon className="h-16 w-16 text-indigo-400" />
                </div>
                
                {/* Loyalty badge */}
                <div className="absolute -top-2 -right-2 bg-yellow-600/80 text-yellow-200 text-xs font-medium px-2 py-1 rounded-full border border-yellow-500">
                  {customer.loyaltyTier}
                </div>
              </div>
              
              <h2 className="text-xl font-bold text-white">{customer.name}</h2>
              <p className="text-gray-400 text-sm mt-1">Customer ID: {customer.id}</p>
              
              {/* Profile completion */}
              <div className="w-full mt-4">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs text-gray-400">Profile Completion</span>
                  <span className="text-xs text-indigo-400">{customer.profileCompletion}%</span>
                </div>
                <div className="w-full bg-gray-800 rounded-full h-2">
                  <div 
                    className="bg-indigo-500 h-2 rounded-full" 
                    style={{ width: `${customer.profileCompletion}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="flex space-x-2 mt-4">
                <button 
                  onClick={handleEdit}
                  className={`px-4 py-2 rounded-lg text-sm font-medium flex items-center ${
                    isEditing 
                      ? 'bg-green-600 hover:bg-green-700 text-white' 
                      : 'bg-gray-800 hover:bg-gray-700 text-white'
                  }`}
                >
                  <PencilIcon className="h-4 w-4 mr-1.5" />
                  {isEditing ? 'Save Changes' : 'Edit Profile'}
                </button>
                <button className="p-2 rounded-lg bg-gray-800 hover:bg-gray-700 text-white">
                  <EllipsisHorizontalIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
            
            {/* Contact information */}
            <div className="p-6 border-b border-gray-800">
              <h3 className="text-md font-semibold text-white mb-4">Contact Information</h3>
              
              <div className="space-y-4">
                <div className="flex items-start">
                  <PhoneIcon className="h-5 w-5 text-indigo-400 mr-3 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-400">Phone</p>
                    {isEditing ? (
                      <input 
                        type="text" 
                        value={customer.phone}
                        onChange={(e) => setCustomer({...customer, phone: e.target.value})}
                        className="bg-gray-800 border border-gray-700 rounded-md px-3 py-1.5 mt-1 text-white w-full text-sm"
                      />
                    ) : (
                      <p className="text-white">{customer.phone}</p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-start">
                  <EnvelopeIcon className="h-5 w-5 text-indigo-400 mr-3 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-400">Email</p>
                    {isEditing ? (
                      <input 
                        type="email" 
                        value={customer.email}
                        onChange={(e) => setCustomer({...customer, email: e.target.value})}
                        className="bg-gray-800 border border-gray-700 rounded-md px-3 py-1.5 mt-1 text-white w-full text-sm"
                      />
                    ) : (
                      <p className="text-white">{customer.email}</p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-start">
                  <MapPinIcon className="h-5 w-5 text-indigo-400 mr-3 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-400">Address</p>
                    {isEditing ? (
                      <textarea
                        value={customer.address}
                        onChange={(e) => setCustomer({...customer, address: e.target.value})}
                        className="bg-gray-800 border border-gray-700 rounded-md px-3 py-1.5 mt-1 text-white w-full text-sm"
                        rows="2"
                      />
                    ) : (
                      <p className="text-white">{customer.address}</p>
                    )}
                  </div>
                </div>
                
                <div className="flex items-start">
                  <CalendarIcon className="h-5 w-5 text-indigo-400 mr-3 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-400">Customer Since</p>
                    <p className="text-white">{customer.joinDate}</p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Tags */}
            <div className="p-6 border-b border-gray-800">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-md font-semibold text-white">Tags</h3>
                {isEditing && (
                  <button className="text-indigo-400 text-sm hover:text-indigo-300">
                    + Add Tag
                  </button>
                )}
              </div>
              
              <div className="flex flex-wrap gap-2">
                {customer.tags.map((tag, index) => (
                  <div 
                    key={index} 
                    className="bg-gray-800 text-gray-300 px-3 py-1 rounded-full text-xs flex items-center"
                  >
                    <TagIcon className="h-3 w-3 mr-1 text-indigo-400" />
                    {tag}
                    {isEditing && (
                      <button className="ml-1.5 text-gray-500 hover:text-gray-300">
                        ×
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
            
            {/* Notes */}
            <div className="p-6 flex-grow">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-md font-semibold text-white">Notes</h3>
                {!isEditing && (
                  <button className="text-indigo-400 text-sm hover:text-indigo-300">
                    + Add Note
                  </button>
                )}
              </div>
              
              {isEditing ? (
                <textarea
                  value={customer.notes}
                  onChange={(e) => setCustomer({...customer, notes: e.target.value})}
                  className="bg-gray-800 border border-gray-700 rounded-md px-4 py-3 text-white w-full h-32 text-sm"
                  placeholder="Add notes about this customer..."
                />
              ) : (
                <div className="bg-gray-800/50 rounded-lg p-4 text-gray-300 text-sm border border-gray-800">
                  {customer.notes}
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Center and Right Columns */}
        <div className="lg:col-span-2">
          {/* Customer Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="border border-gray-800 bg-gray-900 p-4 rounded-xl">
              <div className="flex items-center">
                <div className="bg-indigo-900/30 p-3 rounded-lg mr-4">
                  <ArrowTrendingUpIcon className="h-5 w-5 text-indigo-400" />
                </div>
                <div>
                  <p className="text-gray-400 text-sm">Lifetime Value</p>
                  <h3 className="text-xl font-bold text-white">{customer.lifetimeValue}</h3>
                </div>
              </div>
            </div>
            
            <div className="border border-gray-800 bg-gray-900 p-4 rounded-xl">
              <div className="flex items-center">
                <div className="bg-purple-900/30 p-3 rounded-lg mr-4">
                  <ChatBubbleLeftRightIcon className="h-5 w-5 text-purple-400" />
                </div>
                <div>
                  <p className="text-gray-400 text-sm">Interactions</p>
                  <h3 className="text-xl font-bold text-white">{customer.interactionCount}</h3>
                </div>
              </div>
            </div>
            
            <div className="border border-gray-800 bg-gray-900 p-4 rounded-xl">
              <div className="flex items-center">
                <div className="bg-blue-900/30 p-3 rounded-lg mr-4">
                  <HeartIcon className="h-5 w-5 text-blue-400" />
                </div>
                <div>
                  <p className="text-gray-400 text-sm">Satisfaction</p>
                  <div className="flex items-center text-white">
                    <HandThumbUpIcon className="h-4 w-4 text-green-400 mr-1" />
                    <span className="text-xl font-bold">95%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Interaction History */}
          <div className="border border-gray-800 bg-gray-900 rounded-xl mb-6">
            <div className="p-5 border-b border-gray-800 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <LinkIcon className="h-5 w-5 mr-2 text-indigo-400" />
                Interaction History
              </h3>
              <button className="text-indigo-400 text-sm hover:text-indigo-300 flex items-center">
                <ArrowPathIcon className="h-4 w-4 mr-1" />
                Refresh
              </button>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left bg-gray-800/50">
                    <th className="p-4 text-gray-400 text-sm font-medium">Type</th>
                    <th className="p-4 text-gray-400 text-sm font-medium">Date</th>
                    <th className="p-4 text-gray-400 text-sm font-medium">Agent</th>
                    <th className="p-4 text-gray-400 text-sm font-medium">Outcome</th>
                    <th className="p-4 text-gray-400 text-sm font-medium">Summary</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-800">
                  {interactionHistory.map((interaction) => (
                    <tr key={interaction.id} className="hover:bg-gray-800/30">
                      <td className="p-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          interaction.type === 'Call' ? 'bg-green-900/30 text-green-400 border border-green-800/30' :
                          interaction.type === 'SMS' ? 'bg-purple-900/30 text-purple-400 border border-purple-800/30' :
                          'bg-blue-900/30 text-blue-400 border border-blue-800/30'
                        }`}>
                          {interaction.type}
                        </span>
                      </td>
                      <td className="p-4 text-white text-sm">{interaction.date}</td>
                      <td className="p-4 text-white text-sm">{interaction.agent}</td>
                      <td className="p-4">
                        <span className="bg-green-900/30 text-green-400 border border-green-800/30 text-xs px-2.5 py-0.5 rounded-full">
                          {interaction.outcome}
                        </span>
                      </td>
                      <td className="p-4 text-gray-300 text-sm">{interaction.summary}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Purchase History */}
          <div className="border border-gray-800 bg-gray-900 rounded-xl mb-6">
            <div className="p-5 border-b border-gray-800 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <IdentificationIcon className="h-5 w-5 mr-2 text-indigo-400" />
                Purchase History
              </h3>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left bg-gray-800/50">
                    <th className="p-4 text-gray-400 text-sm font-medium">Date</th>
                    <th className="p-4 text-gray-400 text-sm font-medium">Product</th>
                    <th className="p-4 text-gray-400 text-sm font-medium">Amount</th>
                    <th className="p-4 text-gray-400 text-sm font-medium">Status</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-800">
                  {purchaseHistory.map((purchase) => (
                    <tr key={purchase.id} className="hover:bg-gray-800/30">
                      <td className="p-4 text-white text-sm">{purchase.date}</td>
                      <td className="p-4 text-white text-sm">{purchase.product}</td>
                      <td className="p-4 text-white font-medium">{purchase.amount}</td>
                      <td className="p-4">
                        <span className="bg-green-900/30 text-green-400 border border-green-800/30 text-xs px-2.5 py-0.5 rounded-full">
                          {purchase.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* AI Recommendations */}
          <div className="border border-gray-800 bg-gray-900 rounded-xl">
            <div className="p-5 border-b border-gray-800">
              <h3 className="text-lg font-semibold text-white flex items-center">
                <RocketLaunchIcon className="h-5 w-5 mr-2 text-indigo-400" />
                AI-Powered Recommendations
              </h3>
            </div>
            
            <div className="p-5 space-y-4">
              {recommendations.map((rec) => (
                <div key={rec.id} className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
                  <div className="flex justify-between items-start">
                    <h4 className="font-medium text-white">{rec.title}</h4>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${
                      rec.confidence === 'High' ? 'bg-green-900/30 text-green-400 border border-green-800/30' :
                      rec.confidence === 'Medium' ? 'bg-yellow-900/30 text-yellow-400 border border-yellow-800/30' :
                      'bg-red-900/30 text-red-400 border border-red-800/30'
                    }`}>
                      {rec.confidence} Confidence
                    </span>
                  </div>
                  <p className="text-gray-400 text-sm mt-2">{rec.description}</p>
                  <div className="mt-3 flex justify-end space-x-2">
                    <button className="text-gray-400 text-sm hover:text-gray-300">
                      Dismiss
                    </button>
                    <button className="text-indigo-400 text-sm hover:text-indigo-300">
                      Apply
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 