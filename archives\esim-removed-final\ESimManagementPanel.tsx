'use client';

import { useState } from 'react';
import { Tab } from '@headlessui/react';
import { useEsimManagement, EsimSearchParams } from '../../hooks/useEsimManagement';
import LoadingSpinner from '../shared/LoadingSpinner';
import ErrorMessage from '../shared/ErrorMessage';
import EmptyState from '../shared/EmptyState';
import { DevicePhoneMobileIcon, GlobeAltIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function ESimManagementPanel() {
  const [searchParams, setSearchParams] = useState<EsimSearchParams>({
    country: '',
    region: ''
  });

  const { 
    ownedEsims, 
    searchAvailableEsims, 
    purchaseEsim, 
    isPurchasing 
  } = useEsimManagement();

  const { 
    data: availableEsims, 
    isLoading: isSearching, 
    error: searchError, 
    refetch 
  } = searchAvailableEsims(searchParams);

  const [hasSearched, setHasSearched] = useState(false);
  const [selectedEsimId, setSelectedEsimId] = useState<string | null>(null);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setHasSearched(true);
    refetch();
  };

  const handlePurchase = (planId: string) => {
    purchaseEsim(planId);
  };

  return (
    <div>
      <Tab.Group>
        <Tab.List className="flex space-x-1 rounded-xl bg-blue-900/20 p-1 mb-8">
          <Tab
            className={({ selected }) =>
              classNames(
                'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                selected
                  ? 'bg-white dark:bg-gray-800 text-blue-700 dark:text-blue-400 shadow'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-white/[0.12] hover:text-blue-600 dark:hover:text-blue-300'
              )
            }
          >
            My eSIMs
          </Tab>
          <Tab
            className={({ selected }) =>
              classNames(
                'w-full rounded-lg py-2.5 text-sm font-medium leading-5',
                'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                selected
                  ? 'bg-white dark:bg-gray-800 text-blue-700 dark:text-blue-400 shadow'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-white/[0.12] hover:text-blue-600 dark:hover:text-blue-300'
              )
            }
          >
            Get New eSIM
          </Tab>
        </Tab.List>
        <Tab.Panels>
          {/* My eSIMs Panel */}
          <Tab.Panel>
            {ownedEsims.isLoading ? (
              <div className="flex justify-center items-center py-12">
                <LoadingSpinner size="large" />
              </div>
            ) : ownedEsims.error ? (
              <ErrorMessage 
                title="Could not load your eSIMs" 
                message="We couldn't load your eSIMs. Please try again later."
                error={ownedEsims.error as Error}
                onRetry={() => ownedEsims.refetch()}
              />
            ) : ownedEsims.data.length === 0 ? (
              <EmptyState 
                title="No eSIMs yet" 
                message="You don't have any eSIMs yet. Get started by purchasing your first eSIM."
                icon={DevicePhoneMobileIcon}
                action={{
                  label: "Get an eSIM",
                  onClick: () => {
                    // This would typically change the tab to the search tab
                    // For now, we'll just log it
                    console.log("Navigate to eSIM search tab");
                  }
                }}
              />
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {ownedEsims.data.map((esim) => (
                  <div key={esim.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                    {/* eSIM Card Header */}
                    <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                      <div className="flex items-center">
                        <div className="p-2 bg-green-100 dark:bg-green-900 rounded-full mr-3">
                          <DevicePhoneMobileIcon className="h-5 w-5 text-green-600 dark:text-green-400" />
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                            {esim.planName}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {esim.country}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* eSIM Card Body */}
                    <div className="p-4">
                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div>
                          <p className="text-xs text-gray-500 dark:text-gray-400">Status</p>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {esim.status}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 dark:text-gray-400">Activated</p>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {new Date(esim.activationDate).toLocaleDateString()}
                          </p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div>
                          <p className="text-xs text-gray-500 dark:text-gray-400">Expires</p>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {new Date(esim.expiryDate).toLocaleDateString()}
                          </p>
                        </div>
                        {esim.dataRemaining && (
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">Data Remaining</p>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {esim.dataRemaining}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* eSIM Card Footer */}
                    <div className="p-4 bg-gray-50 dark:bg-gray-750 border-t border-gray-200 dark:border-gray-700">
                      <button
                        onClick={() => setSelectedEsimId(esim.id)}
                        className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                      >
                        View Details
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Tab.Panel>

          {/* Get New eSIM Panel */}
          <Tab.Panel>
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                Search for Available eSIMs
              </h2>
              
              {/* eSIM Search Form */}
              <form onSubmit={handleSearch} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  {/* Country Selection */}
                  <div>
                    <label htmlFor="esim-country" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Country
                    </label>
                    <select
                      id="esim-country"
                      name="country"
                      className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 dark:border-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md bg-white dark:bg-gray-800"
                      value={searchParams.country}
                      onChange={(e) => setSearchParams({...searchParams, country: e.target.value})}
                    >
                      <option value="">Select a country</option>
                      <option value="US">United States</option>
                      <option value="EU">Europe</option>
                      <option value="GLOBAL">Global</option>
                      {/* Add more countries as needed */}
                    </select>
                  </div>

                  {/* Region Selection (if applicable) */}
                  <div>
                    <label htmlFor="esim-region" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Region (Optional)
                    </label>
                    <input
                      type="text"
                      name="region"
                      id="esim-region"
                      placeholder="e.g. North America"
                      className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-800"
                      value={searchParams.region}
                      onChange={(e) => setSearchParams({...searchParams, region: e.target.value})}
                    />
                  </div>
                </div>

                {/* Search Button */}
                <button
                  type="submit"
                  disabled={isSearching}
                  className="flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSearching ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Searching...
                    </>
                  ) : (
                    <>
                      <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                      Search eSIMs
                    </>
                  )}
                </button>
              </form>
            </div>

            {/* Search Results */}
            {isSearching && (
              <div className="flex justify-center items-center py-12">
                <LoadingSpinner size="large" />
              </div>
            )}

            {!isSearching && hasSearched && searchError && (
              <ErrorMessage 
                title="Could not load available eSIMs" 
                message="We couldn't load available eSIMs. Please try again with different search criteria."
                error={searchError as Error}
              />
            )}

            {!isSearching && hasSearched && !searchError && availableEsims && availableEsims.length === 0 && (
              <EmptyState 
                title="No eSIMs found" 
                message="We couldn't find any eSIMs matching your search criteria. Try adjusting your search parameters."
                icon={GlobeAltIcon}
              />
            )}

            {!isSearching && hasSearched && !searchError && availableEsims && availableEsims.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                  Available eSIMs
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {availableEsims.map((esim) => (
                    <div key={esim.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                      {/* eSIM Card Header */}
                      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                        <div className="flex items-center">
                          <div className="p-2 bg-green-100 dark:bg-green-900 rounded-full mr-3">
                            <GlobeAltIcon className="h-5 w-5 text-green-600 dark:text-green-400" />
                          </div>
                          <div>
                            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                              {esim.planName}
                            </h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              {esim.country}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* eSIM Card Body */}
                      <div className="p-4">
                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">Data</p>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {esim.data}
                            </p>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 dark:text-gray-400">Validity</p>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {esim.validity}
                            </p>
                          </div>
                        </div>

                        <div className="mb-4">
                          <p className="text-xs text-gray-500 dark:text-gray-400">Cost</p>
                          <p className="text-lg font-semibold text-gray-900 dark:text-white">
                            ${esim.cost.toFixed(2)}
                          </p>
                        </div>
                      </div>

                      {/* eSIM Card Footer */}
                      <div className="p-4 bg-gray-50 dark:bg-gray-750 border-t border-gray-200 dark:border-gray-700">
                        <button
                          onClick={() => handlePurchase(esim.id)}
                          disabled={isPurchasing}
                          className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isPurchasing ? 'Purchasing...' : 'Purchase eSIM'}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {!isSearching && !hasSearched && (
              <div className="text-center py-12">
                <GlobeAltIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Search for an eSIM
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mt-2 max-w-md mx-auto">
                  Use the search form above to find available eSIMs. You can filter by country and region.
                </p>
              </div>
            )}
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>

      {/* eSIM Details Modal would go here */}
    </div>
  );
}
