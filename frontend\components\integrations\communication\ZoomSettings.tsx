'use client';

import { IntegrationSettings } from '../../../hooks/useIntegrations';

interface ZoomSettingsProps {
  settings: IntegrationSettings;
  onChange: (settings: IntegrationSettings) => void;
}

export default function ZoomSettings({
  settings,
  onChange,
}: ZoomSettingsProps) {
  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    onChange({
      ...settings,
      [name]: checked,
    });
  };

  // Handle select change
  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    onChange({
      ...settings,
      [name]: value,
    });
  };

  return (
    <div className="space-y-4">
      <div>
        <label
          htmlFor="meetingType"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Default Meeting Type
        </label>
        <select
          id="meetingType"
          name="meetingType"
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          value={settings.meetingType || 'instant'}
          onChange={handleSelectChange}
        >
          <option value="instant">Instant Meeting</option>
          <option value="scheduled">Scheduled Meeting</option>
          <option value="recurring">Recurring Meeting</option>
        </select>
      </div>

      <div>
        <label
          htmlFor="defaultDuration"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Default Meeting Duration
        </label>
        <select
          id="defaultDuration"
          name="defaultDuration"
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          value={settings.defaultDuration || '30'}
          onChange={handleSelectChange}
        >
          <option value="15">15 minutes</option>
          <option value="30">30 minutes</option>
          <option value="45">45 minutes</option>
          <option value="60">60 minutes</option>
        </select>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="autoGenerateMeetings"
            name="autoGenerateMeetings"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.autoGenerateMeetings || false}
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="autoGenerateMeetings"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Auto-generate Zoom Meetings
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Automatically create Zoom meetings for scheduled appointments.
          </p>
        </div>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="includeMeetingLink"
            name="includeMeetingLink"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.includeMeetingLink !== false} // Default to true
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="includeMeetingLink"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Include Meeting Link in SMS
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Include Zoom meeting link in appointment confirmation SMS.
          </p>
        </div>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="enableWaitingRoom"
            name="enableWaitingRoom"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.enableWaitingRoom || false}
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="enableWaitingRoom"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Enable Waiting Room
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Enable waiting room for generated Zoom meetings.
          </p>
        </div>
      </div>
    </div>
  );
}
