# Supabase Connection Guide

This guide explains the different ways to connect to your Supabase database in the CallSaver application. Each connection type has specific use cases and advantages.

## 1. Direct PostgreSQL Connection

**Use case:** Server-side operations requiring persistent connections (API routes, background jobs)

**Configuration:**
```env
DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.fayjmfdjudtldfpwxlpu.supabase.co:5432/postgres"
```

**Usage example:**
```javascript
import sql from '../utils/db';

// Query example
const users = await sql`SELECT * FROM users WHERE id = ${userId}`;

// Insert example
const newUser = await sql`
  INSERT INTO users (name, email) 
  VALUES (${name}, ${email}) 
  RETURNING *
`;

// Transaction example
await sql.begin(async (sql) => {
  const { id } = await sql`INSERT INTO accounts (name) VALUES (${name}) RETURNING id`;
  await sql`INSERT INTO users (account_id, name) VALUES (${id}, ${userName})`;
});
```

## 2. Supabase Client (App Framework)

**Use case:** Client-side features like authentication, storage, and realtime subscriptions

**Configuration:**
```env
NEXT_PUBLIC_SUPABASE_URL=https://fayjmfdjudtldfpwxlpu.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Usage example:**
```javascript
import supabaseClient from '../utils/supabaseClient';

// Auth example
const { data, error } = await supabaseClient.auth.signUp({
  email: '<EMAIL>',
  password: 'example-password',
});

// Query example with RLS policies
const { data, error } = await supabaseClient
  .from('profiles')
  .select('*')
  .eq('id', userId);

// Storage example
const { data, error } = await supabaseClient
  .storage
  .from('avatars')
  .upload(`public/${fileName}`, file);

// Realtime subscription
const subscription = supabaseClient
  .channel('table-db-changes')
  .on('postgres_changes', { 
    event: 'INSERT', 
    schema: 'public', 
    table: 'messages' 
  }, (payload) => {
    console.log('New message received!', payload);
  })
  .subscribe();
```

## 3. Server-Side Supabase Client

**Use case:** Server components and API routes requiring admin access

**Configuration:**
```env
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
```

**Usage example:**
```javascript
import supabaseServer from '../utils/supabaseServer';

// Admin operations that bypass RLS
const { data, error } = await supabaseServer
  .from('users')
  .select('*')
  .eq('subscription_status', 'active');

// Service role operations
const { data, error } = await supabaseServer.auth.admin.listUsers();
```

## 4. Mobile (Expo) Supabase Client

**Use case:** Expo mobile applications

**Configuration:**
```env
EXPO_PUBLIC_SUPABASE_URL=https://fayjmfdjudtldfpwxlpu.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Usage example:**
```javascript
import supabaseMobile from '../utils/supabaseMobile';

// Works the same as the standard client but optimized for Expo
const { data, error } = await supabaseMobile.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'example-password'
});
```

## 5. Prisma ORM Configuration

**Use case:** Type-safe database operations with connection pooling

**Configuration:**
```env
DATABASE_URL="postgresql://postgres.fayjmfdjudtldfpwxlpu:[YOUR-PASSWORD]@aws-0-eu-central-1.pooler.supabase.co:6543/postgres?pgbouncer=true"
DIRECT_URL="postgresql://postgres.fayjmfdjudtldfpwxlpu:[YOUR-PASSWORD]@aws-0-eu-central-1.pooler.supabase.co:5432/postgres"
```

**Prisma Schema:**
```prisma
datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}
```

**Usage example:**
```javascript
import { PrismaClient } from '@prisma/client'
const prisma = new PrismaClient()

// Query example
const user = await prisma.user.findUnique({
  where: { id: userId },
  include: { profile: true }
});

// Create example
const newUser = await prisma.user.create({
  data: {
    email: '<EMAIL>',
    name: 'John Doe',
    profile: {
      create: {
        bio: 'New user',
        avatarUrl: 'https://example.com/avatar.png'
      }
    }
  }
});

// Transaction example
await prisma.$transaction([
  prisma.account.create({ data: { name: 'Business Account' } }),
  prisma.user.update({ 
    where: { id: userId },
    data: { role: 'ADMIN' }
  })
]);
```

## Connection Pattern Selection

Choose the appropriate connection method based on your specific use case:

1. **Direct PostgreSQL**: Use for backend operations requiring persistent connections
2. **Supabase Client**: Use for frontend/client-side operations involving auth, storage, or realtime
3. **Server Supabase Client**: Use for server components and admin operations
4. **Mobile Client**: Use for Expo mobile applications
5. **Prisma ORM**: Use for type-safe database access with the benefits of an ORM

You can use multiple connection types within the same application for different parts of your codebase. 