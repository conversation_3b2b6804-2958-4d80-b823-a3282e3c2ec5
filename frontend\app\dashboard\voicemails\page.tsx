'use client';

import { useState, useEffect } from 'react';
import useS<PERSON> from 'swr';
import supabaseClient from '../../utils/supabaseClient'; // Import shared client
import { useRouter } from 'next/navigation';

// Define TypeScript interface for call log data structure
interface CallLog {
  id: string;
  from: string;
  to: string;
  timestamp: string;
  status: string;
  duration?: number;
  recordingUrl?: string;
  transcriptionText?: string;
  callType: 'incoming' | 'outgoing' | 'missed';
}

// Define the API response type
interface CallLogResponse {
  success: boolean;
  count: number;
  data: CallLog[];
}

export default function VoicemailsPage() {
  const router = useRouter();
  const [isClientReady, setIsClientReady] = useState(false);

  // Set client-side rendering flag after mount
  useEffect(() => {
    setIsClientReady(true);
  }, []);

  // Fetcher function for SWR that handles authentication and API calls
  const fetcher = async (url: string) => {
    try {
      // Get Supabase token from the current session using shared client
      const { data: { session }, error: sessionError } = await supabaseClient.auth.getSession();
      const token = session?.access_token;

      if (sessionError || !token) {
        console.error('User not authenticated', sessionError);
        router.push('/signin');
        throw new Error('User not authenticated');
      }

      // Make authenticated request to the backend
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API error:', response.status, errorText);
        throw new Error(`API error: ${response.status} - ${errorText || response.statusText}`);
      }

      const result: CallLogResponse = await response.json();
      
      if (!result.success) {
        throw new Error('API returned unsuccessful response');
      }

      return result.data;
    } catch (error) {
      console.error('Fetcher error:', error);
      throw error;
    }
  };

  // Use SWR hook to fetch call logs with automatic revalidation
  const { data: callLogs, error, isLoading } = useSWR<CallLog[]>(
    isClientReady ? 'http://localhost:3006/api/call-logs' : null,
    fetcher
  );

  // Format timestamp to readable date/time
  const formatDateTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  // Get status badge color based on call status
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-500';
      case 'missed':
        return 'bg-red-500';
      case 'voicemail':
        return 'bg-blue-500';
      case 'in-progress':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="min-h-screen bg-gray-950 text-white pb-12">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">Voicemails & Call Logs</h1>
        
        {/* Loading state */}
        {isLoading && (
          <div className="flex items-center justify-center py-20">
            <div className="animate-spin h-8 w-8 border-4 border-purple-500 border-t-transparent rounded-full mr-3"></div>
            <p className="text-gray-400">Loading voicemails...</p>
          </div>
        )}
        
        {/* Error state */}
        {error && (
          <div className="bg-red-900/30 border border-red-500/30 rounded-lg p-4 mb-6">
            <p className="text-red-200">Error loading voicemails. Please try again later.</p>
            <p className="text-red-300 text-sm mt-1">
              {error instanceof Error ? error.message : 'Unknown error'}
            </p>
          </div>
        )}
        
        {/* Empty state */}
        {callLogs && callLogs.length === 0 && (
          <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-8 text-center">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-12 w-12 mx-auto text-gray-500 mb-3" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={1.5} 
                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" 
              />
            </svg>
            <p className="text-gray-400">No voicemails or call logs found.</p>
            <p className="mt-2 text-gray-500 text-sm">
              Any incoming calls and voicemails will appear here.
            </p>
          </div>
        )}
        
        {/* Call logs list */}
        {callLogs && callLogs.length > 0 && (
          <div className="space-y-4">
            {callLogs.map((callLog) => (
              <div 
                key={callLog.id} 
                className="bg-gray-800/70 rounded-lg border border-gray-700 p-5 hover:border-purple-500/30 transition-colors"
              >
                <div className="flex flex-col md:flex-row md:justify-between md:items-start">
                  {/* Call details */}
                  <div className="mb-4 md:mb-0">
                    <div className="flex items-center mb-2">
                      <span className="text-lg font-medium text-white">{callLog.from}</span>
                      <span className={`ml-3 px-2 py-1 text-xs rounded-full ${getStatusColor(callLog.status)}`}>
                        {callLog.status}
                      </span>
                    </div>
                    <p className="text-gray-400 text-sm">
                      <span className="inline-block mr-4">
                        <span className="text-gray-500 mr-1">To:</span> {callLog.to}
                      </span>
                      <span className="inline-block">
                        <span className="text-gray-500 mr-1">Time:</span> {formatDateTime(callLog.timestamp)}
                      </span>
                    </p>
                    
                    {callLog.duration && (
                      <p className="text-gray-400 text-sm mt-1">
                        <span className="text-gray-500 mr-1">Duration:</span> 
                        {Math.floor(callLog.duration / 60)}:{(callLog.duration % 60).toString().padStart(2, '0')}
                      </p>
                    )}
                  </div>
                  
                  {/* Transcript */}
                  {callLog.transcriptionText && (
                    <div className="bg-gray-900/60 p-3 rounded border border-gray-700 mt-4 w-full md:w-1/2">
                      <p className="text-gray-400 text-xs mb-1">Transcription:</p>
                      <p className="text-white text-sm">{callLog.transcriptionText}</p>
                    </div>
                  )}
                </div>
                
                {/* Audio player (if recording exists) */}
                {callLog.recordingUrl && (
                  <div className="mt-5 pt-4 border-t border-gray-700">
                    <p className="text-gray-400 text-xs mb-2">Recording:</p>
                    <audio className="w-full" controls>
                      <source src={callLog.recordingUrl} type="audio/mpeg" />
                      Your browser does not support the audio element.
                    </audio>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 