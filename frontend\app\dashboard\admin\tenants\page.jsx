'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../components/ui/card';
import AdminPageLayout from '../../../../components/admin/AdminPageLayout';

export default function TenantManagement() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tenants, setTenants] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedTenant, setSelectedTenant] = useState(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Mock tenant data
  const mockTenants = [
    { id: 1, name: 'Acme Corporation', domain: 'acme.com', plan: 'Enterprise', status: 'Active', usersCount: 125, createdAt: '2023-01-10T08:15:00Z' },
    { id: 2, name: 'Globex Industries', domain: 'globex.com', plan: 'Pro', status: 'Active', usersCount: 47, createdAt: '2023-01-15T09:20:00Z' },
    { id: 3, name: 'Initech LLC', domain: 'initech.com', plan: 'Basic', status: 'Inactive', usersCount: 12, createdAt: '2023-02-05T11:10:00Z' },
    { id: 4, name: 'Umbrella Corp', domain: 'umbrella.com', plan: 'Enterprise', status: 'Active', usersCount: 89, createdAt: '2023-02-10T13:45:00Z' },
    { id: 5, name: 'Stark Industries', domain: 'stark.com', plan: 'Pro', status: 'Active', usersCount: 56, createdAt: '2023-02-15T10:30:00Z' },
    { id: 6, name: 'Wayne Enterprises', domain: 'wayne.com', plan: 'Enterprise', status: 'Active', usersCount: 78, createdAt: '2023-03-01T14:20:00Z' },
    { id: 7, name: 'Cyberdyne Systems', domain: 'cyberdyne.com', plan: 'Basic', status: 'Suspended', usersCount: 8, createdAt: '2023-03-10T09:15:00Z' },
    { id: 8, name: 'Oscorp Industries', domain: 'oscorp.com', plan: 'Pro', status: 'Active', usersCount: 34, createdAt: '2023-03-15T11:30:00Z' },
  ];

  useEffect(() => {
    const fetchTenants = async () => {
      try {
        setIsLoading(true);
        // In a real implementation, this would be an API call to get tenants
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Filter tenants based on search term
        const filteredTenants = mockTenants.filter(tenant =>
          tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tenant.domain.toLowerCase().includes(searchTerm.toLowerCase()) ||
          tenant.plan.toLowerCase().includes(searchTerm.toLowerCase())
        );

        // Pagination
        const itemsPerPage = 10;
        const totalPages = Math.ceil(filteredTenants.length / itemsPerPage);
        setTotalPages(totalPages);

        // Get current page items
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedTenants = filteredTenants.slice(startIndex, endIndex);

        setTenants(paginatedTenants);
      } catch (err) {
        console.error('Error fetching tenants:', err);
        setError('Failed to load tenants. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTenants();
  }, [searchTerm, currentPage]);

  const handleEditTenant = (tenant) => {
    setSelectedTenant(tenant);
    setIsEditModalOpen(true);
  };

  const handleDeleteTenant = (tenantId) => {
    if (window.confirm('Are you sure you want to delete this tenant? This action cannot be undone and will remove all associated users and data.')) {
      // In a real implementation, this would be an API call to delete the tenant
      console.log(`Deleting tenant with ID: ${tenantId}`);
      // Then refetch the tenants
      setTenants(tenants.filter(tenant => tenant.id !== tenantId));
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };

  return (
    <AdminPageLayout
      title="Tenant Management"
      description="Manage multi-tenant organizations and their settings.">


      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
          <p className="text-red-200">{error}</p>
        </div>
      )}

      <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 mb-6">
        <CardHeader>
          <CardTitle className="text-white">Tenants</CardTitle>
          <CardDescription className="text-gray-400">Manage multi-tenant organizations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between mb-4 gap-4">
            <div className="w-full md:w-1/3">
              <input
                type="text"
                placeholder="Search tenants..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
              />
            </div>
            <button
              className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
              onClick={() => {
                setSelectedTenant({
                  id: null,
                  name: '',
                  domain: '',
                  plan: 'Basic',
                  status: 'Active',
                  usersCount: 0
                });
                setIsEditModalOpen(true);
              }}
            >
              Add New Tenant
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead className="bg-gray-700/50 text-gray-300">
                <tr>
                  <th className="px-4 py-2 rounded-tl-lg">Name</th>
                  <th className="px-4 py-2">Domain</th>
                  <th className="px-4 py-2">Plan</th>
                  <th className="px-4 py-2">Status</th>
                  <th className="px-4 py-2">Users</th>
                  <th className="px-4 py-2">Created</th>
                  <th className="px-4 py-2 rounded-tr-lg">Actions</th>
                </tr>
              </thead>
              <tbody className="text-gray-300">
                {isLoading ? (
                  <tr>
                    <td colSpan="7" className="px-4 py-2 text-center">Loading tenants...</td>
                  </tr>
                ) : tenants.length === 0 ? (
                  <tr>
                    <td colSpan="7" className="px-4 py-2 text-center">No tenants found</td>
                  </tr>
                ) : (
                  tenants.map((tenant, index) => (
                    <tr key={tenant.id} className={index % 2 === 0 ? 'bg-gray-700/30' : 'bg-gray-700/10'}>
                      <td className="px-4 py-2 font-medium">{tenant.name}</td>
                      <td className="px-4 py-2">{tenant.domain}</td>
                      <td className="px-4 py-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          tenant.plan === 'Enterprise' ? 'bg-purple-500/20 text-purple-300' :
                          tenant.plan === 'Pro' ? 'bg-blue-500/20 text-blue-300' :
                          'bg-green-500/20 text-green-300'
                        }`}>
                          {tenant.plan}
                        </span>
                      </td>
                      <td className="px-4 py-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          tenant.status === 'Active' ? 'bg-green-500/20 text-green-300' :
                          tenant.status === 'Inactive' ? 'bg-gray-500/20 text-gray-300' :
                          'bg-red-500/20 text-red-300'
                        }`}>
                          {tenant.status}
                        </span>
                      </td>
                      <td className="px-4 py-2">{tenant.usersCount}</td>
                      <td className="px-4 py-2">{formatDate(tenant.createdAt)}</td>
                      <td className="px-4 py-2">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEditTenant(tenant)}
                            className="text-blue-400 hover:text-blue-300"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteTenant(tenant.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-between items-center mt-4">
              <div className="text-gray-400">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="bg-gray-700 text-white px-3 py-1 rounded-lg disabled:opacity-50"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="bg-gray-700 text-white px-3 py-1 rounded-lg disabled:opacity-50"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tenant Edit Modal */}
      {isEditModalOpen && selectedTenant && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold text-white mb-4">
              {selectedTenant.id ? 'Edit Tenant' : 'Add New Tenant'}
            </h2>

            <div className="space-y-4">
              <div>
                <label className="text-gray-300 block mb-1">Organization Name</label>
                <input
                  type="text"
                  value={selectedTenant.name}
                  onChange={(e) => setSelectedTenant({...selectedTenant, name: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                />
              </div>

              <div>
                <label className="text-gray-300 block mb-1">Domain</label>
                <input
                  type="text"
                  value={selectedTenant.domain}
                  onChange={(e) => setSelectedTenant({...selectedTenant, domain: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                />
              </div>

              <div>
                <label className="text-gray-300 block mb-1">Subscription Plan</label>
                <select
                  value={selectedTenant.plan}
                  onChange={(e) => setSelectedTenant({...selectedTenant, plan: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                >
                  <option value="Basic">Basic</option>
                  <option value="Pro">Pro</option>
                  <option value="Enterprise">Enterprise</option>
                </select>
              </div>

              <div>
                <label className="text-gray-300 block mb-1">Status</label>
                <select
                  value={selectedTenant.status}
                  onChange={(e) => setSelectedTenant({...selectedTenant, status: e.target.value})}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                >
                  <option value="Active">Active</option>
                  <option value="Inactive">Inactive</option>
                  <option value="Suspended">Suspended</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end mt-6 space-x-2">
              <button
                onClick={() => setIsEditModalOpen(false)}
                className="bg-gray-700 text-white px-4 py-2 rounded-lg"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  // In a real implementation, this would be an API call to save the tenant
                  console.log('Saving tenant:', selectedTenant);
                  setIsEditModalOpen(false);

                  // Update the tenant list
                  if (selectedTenant.id) {
                    setTenants(tenants.map(tenant => tenant.id === selectedTenant.id ? selectedTenant : tenant));
                  } else {
                    // Add new tenant with a mock ID
                    const newTenant = {
                      ...selectedTenant,
                      id: Math.max(...tenants.map(t => t.id), 0) + 1,
                      createdAt: new Date().toISOString()
                    };
                    setTenants([...tenants, newTenant]);
                  }
                }}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </AdminPageLayout>
  );
}
