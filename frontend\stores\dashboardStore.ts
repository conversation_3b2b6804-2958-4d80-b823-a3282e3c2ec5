import { create } from 'zustand';

// Define types for dashboard data
export interface DashboardSummary {
  activeNumbers: number;
  totalCalls: number;
  missedCalls: number;
  answeredCalls: number;
  voicemails: number;
  callDuration: number;
  creditBalance: number;
  lastUpdated: string;
}

export interface RecentActivity {
  id: string;
  type: 'call' | 'voicemail' | 'sms' | 'credit' | 'automation';
  timestamp: string;
  description: string;
  status?: string;
  phoneNumber?: string;
  duration?: number;
}

export interface AIInsight {
  id: string;
  type: 'recommendation' | 'alert' | 'trend';
  title: string;
  description: string;
  severity?: 'low' | 'medium' | 'high';
  timestamp: string;
  relatedNumberId?: string;
}

// Define the dashboard state interface
interface DashboardState {
  summary: DashboardSummary | null;
  recentActivity: RecentActivity[];
  aiInsights: AIInsight[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setSummary: (data: DashboardSummary) => void;
  setRecentActivity: (data: RecentActivity[]) => void;
  setAIInsights: (data: AIInsight[]) => void;
  addRecentActivity: (activity: RecentActivity) => void;
  addAIInsight: (insight: AIInsight) => void;
  setLoading: (status: boolean) => void;
  setError: (error: string | null) => void;
  clearDashboardData: () => void;
}

// Create the dashboard store
export const useDashboardStore = create<DashboardState>()((set, get) => ({
  summary: null,
  recentActivity: [],
  aiInsights: [],
  isLoading: false,
  error: null,
  
  setSummary: (data) => {
    set({ summary: data });
  },
  
  setRecentActivity: (data) => {
    set({ recentActivity: data });
  },
  
  setAIInsights: (data) => {
    set({ aiInsights: data });
  },
  
  addRecentActivity: (activity) => {
    set((state) => ({
      recentActivity: [activity, ...state.recentActivity].slice(0, 20) // Keep only the latest 20 activities
    }));
  },
  
  addAIInsight: (insight) => {
    set((state) => ({
      aiInsights: [insight, ...state.aiInsights].slice(0, 10) // Keep only the latest 10 insights
    }));
  },
  
  setLoading: (status) => {
    set({ isLoading: status });
  },
  
  setError: (error) => {
    set({ error });
  },
  
  clearDashboardData: () => {
    set({
      summary: null,
      recentActivity: [],
      aiInsights: [],
      error: null
    });
  },
}));

// Export selectors for efficiency
export const useDashboardSummary = () => useDashboardStore((state) => state.summary);
export const useRecentActivity = () => useDashboardStore((state) => state.recentActivity);
export const useAIInsights = () => useDashboardStore((state) => state.aiInsights);
export const useDashboardLoading = () => useDashboardStore((state) => state.isLoading);
export const useDashboardError = () => useDashboardStore((state) => state.error);
