--[[
  Functions to remove jobs by max count.
]]

-- Includes
--- @include "removeJob"

local function removeJobsByMaxCount(maxCount, targetSet, prefix)
  local start = maxCount
  local jobIds = rcall("ZREVRANGE", targetSet, start, -1)
  for i, jobId in ipairs(jobIds) do
    removeJob(jobId, false, prefix, false --[[remove debounce key]])
  end
  rcall("ZRE<PERSON>ANGEBYRANK", targetSet, 0, -(maxCount + 1))
end
