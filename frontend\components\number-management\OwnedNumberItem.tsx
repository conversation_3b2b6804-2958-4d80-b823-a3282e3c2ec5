'use client';

import Link from 'next/link';
import {
  PhoneIcon,
  Cog6ToothIcon,
  TrashIcon,
  BoltIcon,
  CheckCircleIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';
import { OwnedNumber } from '../../hooks/useNumberManagement';

interface OwnedNumberItemProps {
  numberData: OwnedNumber;
  onConfigClick: () => void;
  onReleaseClick: () => void;
}

export default function OwnedNumberItem({
  numberData,
  onConfigClick,
  onReleaseClick
}: OwnedNumberItemProps) {
  const {
    id,
    phoneNumber,
    friendlyName,
    status,
    type,
    capabilities,
    acquiredDate,
    automationLink
  } = numberData;

  // Format phone number for display
  const formatPhoneNumber = (phone: string) => {
    // This is a simple formatter, you might want to use a library like libphonenumber-js
    // for more sophisticated formatting based on country codes
    if (phone.startsWith('+1')) {
      // US format: +1 (XXX) XXX-XXXX
      const cleaned = phone.replace(/\D/g, '').substring(1); // Remove non-digits and the +1
      if (cleaned.length === 10) {
        return `+1 (${cleaned.substring(0, 3)}) ${cleaned.substring(3, 6)}-${cleaned.substring(6, 10)}`;
      }
    }
    return phone; // Return as-is if not US or not 10 digits
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status indicator color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'text-green-500 dark:text-green-400';
      case 'pending':
        return 'text-yellow-500 dark:text-yellow-400';
      case 'inactive':
        return 'text-red-500 dark:text-red-400';
      default:
        return 'text-gray-500 dark:text-gray-400';
    }
  };

  // Get status icon
  const StatusIcon = status.toLowerCase() === 'active' ? CheckCircleIcon : ExclamationCircleIcon;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
      {/* Header */}
      <div className="p-3 sm:p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-full mr-3">
              <PhoneIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="text-base sm:text-lg font-medium text-gray-900 dark:text-white">
                {friendlyName || 'Unnamed Number'}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {formatPhoneNumber(phoneNumber)}
              </p>
            </div>
          </div>
          <div className="flex items-center">
            <StatusIcon className={`h-5 w-5 ${getStatusColor(status)} mr-1`} />
            <span className={`text-sm font-medium ${getStatusColor(status)}`}>
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </span>
          </div>
        </div>
      </div>

      {/* Body */}
      <div className="p-3 sm:p-4">
        <div className="grid grid-cols-2 gap-2 sm:gap-4 mb-3 sm:mb-4">
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Type</p>
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </p>
          </div>
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Acquired</p>
            <p className="text-sm font-medium text-gray-900 dark:text-white">
              {formatDate(acquiredDate)}
            </p>
          </div>
        </div>

        {/* Capabilities */}
        <div className="mb-3 sm:mb-4">
          <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">Capabilities</p>
          <div className="flex flex-wrap gap-2">
            {capabilities.map((capability, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-300"
              >
                {capability}
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* Footer with actions */}
      <div className="p-3 sm:p-4 bg-gray-50 dark:bg-gray-750 border-t border-gray-200 dark:border-gray-700">
        <div className="flex flex-wrap gap-2 sm:flex-nowrap sm:justify-between">
          <button
            type="button"
            onClick={onConfigClick}
            className="inline-flex items-center px-2 sm:px-3 py-1.5 sm:py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-650 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Cog6ToothIcon className="h-4 w-4 mr-2" />
            Configure
          </button>

          {automationLink && (
            <Link
              href={automationLink}
              className="inline-flex items-center px-2 sm:px-3 py-1.5 sm:py-2 border border-transparent shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <BoltIcon className="h-4 w-4 mr-2" />
              Automation
            </Link>
          )}

          <button
            type="button"
            onClick={onReleaseClick}
            className="inline-flex items-center px-2 sm:px-3 py-1.5 sm:py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-xs sm:text-sm leading-4 font-medium rounded-md text-red-700 dark:text-red-400 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-650 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <TrashIcon className="h-4 w-4 mr-2" />
            Release
          </button>
        </div>
      </div>
    </div>
  );
}
