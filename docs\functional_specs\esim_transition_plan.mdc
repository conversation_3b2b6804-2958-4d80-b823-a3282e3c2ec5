---
description: 
globs: 
alwaysApply: false
---
# eSIM Transition Plan Functional Document (`esim_transition_plan.mdc`)

## 1. Purpose and Scope

**Purpose:** Outline the strategy, technical requirements, and phased approach for integrating eSIM (Embedded SIM) capabilities into the CallSaver platform, complementing or potentially offering an alternative to traditional phone number provisioning (e.g., via Twilio). This aims to provide users with global connectivity options, data plans, and a modern alternative for mobile identity.

**Scope:**
- **Provider Integration:** Define strategy for integrating with one or more eSIM providers (e.g., Airalo, Celitech, internal solution if developed).
- **Feature Parity Assessment:** Determine which CallSaver features (AI automation, call/SMS logging, analytics) can be supported with eSIMs, considering provider limitations (e.g., some eSIMs are data-only).
- **Discovery & Purchase:** Implement UI/API flows for users to search, select, and purchase eSIM plans based on geography, data allowance, and validity.
- **Activation & Management:** Provide mechanisms for users to install and activate eSIM profiles (QR code, in-app activation if supported by the provider/OS). Manage eSIM lifecycle (view status, top-up if applicable).
- **Usage Tracking:** Integrate eSIM data usage reporting into the platform's analytics and billing.
- **AI Integration:** Define how the AI assistant interacts with eSIM capabilities (e.g., monitoring data usage, suggesting plans, potentially handling eSIM-based communication if supported).
- **Billing Integration:** Adapt the credit and billing system to handle eSIM plan costs (one-time purchases, potential top-ups).
- **Phased Rollout Plan:** Define stages for development, testing, and release.

## 2. User Interactions

- **Discover eSIM Plans:** Search/browse available eSIMs by country or region within the "Number Management" or a dedicated "eSIM" section.
- **Purchase eSIM:** Select a plan, review details (data, validity, cost), and confirm purchase using account credits or saved payment methods.
- **Activate eSIM:** Receive activation instructions, typically involving scanning a QR code or following an in-app prompt (requires OS integration or provider app deeplinking).
- **View Owned eSIMs:** See a list of purchased eSIMs, their status (Installed, Active, Expired), data remaining, and validity period.
- **Manage eSIMs:** (Limited by provider capabilities) Potentially top-up data, view usage history, or remove an eSIM profile.
- **Link eSIM to AI (If Applicable):** Associate an eSIM (especially if it supports voice/SMS) with an AI assistant profile in the Automation section.

## 3. Backend Integrations & Services Used

- **eSIM Provider APIs:** Integrate with selected provider(s) for:
    - Searching available plans (`GET /provider/esims/plans?country=...`)
    - Purchasing plans (`POST /provider/esims/purchase`)
    - Retrieving purchased eSIM details and activation codes (`GET /provider/esims/{purchaseId}`)
    - Fetching usage data (`GET /provider/esims/{esimInstanceId}/usage`)
    - Managing lifecycle (activation status, top-up) - *highly provider-dependent*
- **Number Provisioning Service:** Adapt or extend this service to handle eSIM provider interactions alongside Twilio.
- **Billing Service:** Process payments/credit deductions for eSIM purchases. Track eSIM costs.
- **Database:** Store eSIM plan details, purchase records, activation status, user associations, usage data snapshots.
- **User Service:** Link eSIM profiles to user accounts.
- **AI Service:** Access eSIM status and usage data; potentially interact with eSIM communication channels if available.
- **Notification Service:** Send alerts related to eSIM activation, low data, expiry.

## 4. Necessary API Endpoints (Additions/Modifications)

- **Number Management API:**
    - `GET /api/esims/available?country=<iso>&region=<region>`: Search for eSIM plans.
    - `POST /api/esims/purchase`: Purchase an eSIM plan.
    - `GET /api/esims/owned`: List owned eSIMs with status, data, validity.
    - `GET /api/esims/{esimPurchaseId}/activation`: Retrieve activation details (QR code data, manual codes).
    - `GET /api/esims/{esimInstanceId}/usage`: Get latest usage data (proxied from provider).
- **Internal APIs:**
    - Endpoints within the Number Provisioning Service to abstract provider-specific calls.
    - Potential webhooks from eSIM providers for status updates (e.g., activation complete, data running low).

## 5. Expected Frontend Component Structure (Additions/Modifications)

- **Number Management Section:**
    - `ESimSearchForm.tsx`
    - `AvailableESimList.tsx` / `AvailableESimItem.tsx`
    - `OwnedESimList.tsx` / `OwnedESimItem.tsx`
    - `ESimActivationModal.tsx` (Displaying QR code, instructions)
    - `ESimDetailView.tsx` (Showing usage, validity)
- **Billing/Usage Components:** Adapt to show eSIM costs and data usage alongside traditional number usage.
- **Automation Section:** UI elements to link AI profiles to eSIMs (if applicable).

## 6. Data Displayed

- **Available eSIMs:** Plan Name, Country/Region Coverage, Data Allowance (GB), Validity (Days), Price (Credits/Currency).
- **Owned eSIMs:** Plan Name, Status (Pending Activation, Active, Installed, Expired), Data Remaining / Total Data, Validity Period (Start/End Dates), Activation QR Code/Details (accessible until activated), Usage History (link/summary).

## 7. State and UI Behavior

- **Purchase Flow:** Similar to number purchase, check credits, confirm, show feedback.
- **Activation Flow:**
    - Display QR code clearly with instructions for iOS/Android.
    - Provide manual activation codes as fallback.
    - Update eSIM status in the UI once activation is confirmed (may require polling or webhook).
- **Usage Display:** Show data usage visually (e.g., progress bar). Update periodically.
- **Error Handling:** Specific errors for eSIM purchase failures (provider errors, payment issues) and activation problems.

## 8. AI Integration Strategy

- **Data Monitoring:** AI can monitor eSIM data usage and trigger notifications (via Notification Service) for low data or upcoming expiry based on user preferences.
- **Plan Recommendations:** AI could analyze travel plans (if user provides them) or past usage to recommend suitable eSIM plans.
- **Troubleshooting:** AI chatbot in Help Center can assist with common eSIM activation issues.
- **Direct Communication (If Supported):** If an eSIM provider offers APIs for voice/SMS or data-based communication that CallSaver can integrate with, the AI Assistant could leverage these channels similarly to how it uses Twilio numbers. *This is a significant dependency on provider capabilities.*

## 9. Transition Strategy & Phased Rollout

- **Phase 1: Research & Provider Selection:** Finalize choice of initial eSIM provider(s). Analyze API capabilities, pricing, and coverage. Build proof-of-concept integration.
- **Phase 2: Core Integration (Backend):**
    - Integrate provider APIs for search, purchase, activation code retrieval, and basic status updates into the Number Provisioning Service.
    - Adapt Database schema.
    - Integrate with Billing Service for purchase flow.
- **Phase 3: Core Integration (Frontend):**
    - Build UI components for eSIM discovery, purchase, and activation display within the Number Management section.
    - Display owned eSIMs and their status.
- **Phase 4: Usage Tracking & Basic Management:**
    - Integrate provider APIs for fetching data usage.
    - Display usage information in the UI.
    - Implement basic lifecycle management (viewing expiry).
- **Phase 5: Feature Integration & AI:**
    - Integrate eSIM usage into Analytics.
    - Implement AI monitoring/notifications for data/expiry.
    - Link eSIMs in Automation section (if applicable based on provider capabilities).
- **Phase 6: Beta Testing:** Roll out to a limited group of users. Gather feedback and identify issues.
- **Phase 7: General Availability:** Launch to all users. Market the new capability.
- **Ongoing:** Evaluate adding more providers, advanced management features (top-ups), and deeper AI integration based on user feedback and provider API evolution.

**Coexistence with Twilio:** Initially, eSIMs will be offered *alongside* Twilio numbers. Users can choose based on their needs. The UI will clearly differentiate between traditional numbers and eSIM plans.

## 10. Error Handling Rules

- **Provider API Errors:** Handle specific errors from the eSIM provider API during search, purchase, activation retrieval, and usage fetching. Display user-friendly messages (e.g., "eSIM provider unavailable", "Purchase failed at provider").
- **Activation Errors:** Guide users through common activation issues. Provide links to provider support or CallSaver help articles. Log activation failures.
- **Insufficient Credits:** Prevent purchase if user lacks sufficient credits.
- **Unsupported Features:** Clearly indicate if certain CallSaver features (e.g., AI call handling) are not available for specific data-only eSIMs.

## 11. Logging and Usage Tracking Expectations

- **Log:**
    - eSIM search attempts (criteria).
    - eSIM purchase attempts (plan details, success/failure, provider errors).
    - Activation code retrieval events.
    - eSIM status changes (e.g., Activated, Expired).
    - Usage data fetch attempts/failures.
    - Errors during any eSIM operation.
- **Track:**
    - Adoption rate of eSIM feature.
    - Most popular eSIM plans/regions.
    - Activation success/failure rates.
    - Usage patterns of purchased eSIMs.
    - Revenue generated from eSIMs.
    - User navigation flow within eSIM management.
