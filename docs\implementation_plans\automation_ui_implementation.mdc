---
description: 
globs: 
alwaysApply: false
---
# Automation UI Implementation Plan (`automation_ui_implementation.mdc`)

## 1. Overview

This document details the frontend implementation plan for the CallSaver Automation section. It outlines components, data fetching, state management, user interactions, and error handling based on `automation_section_document.mdc` and `component_state_mapping.mdc`. This section allows users to configure AI-powered automations for their phone numbers.

## 2. Component Hierarchy

The automation UI will be built using React components within the Next.js app router structure (e.g., `app/automations/page.tsx`). Components will reside in `front/mainpage/components/automation/`.

```
/components
  /automation
    AutomationLayout.tsx          # Main layout, fetches the list of numbers. Manages selected number state.
    NumberList.tsx                # Displays the list of numbers for selection. Props: numbers[], selectedNumberId, onSelectNumber, isLoading.
      NumberListItem.tsx          # Represents a single number. Props: numberData, isSelected, onSelect.
    AutomationConfigPanel.tsx     # Container displaying config for the selected number. Props: selectedNumberId. Fetches/mutates config.
      CallRulesConfig.tsx         # Component for setting call handling rules. Props: numberId, initialData, isLoading, isSaving. Uses useMutation.
      SmsRulesConfig.tsx          # Component for setting SMS handling rules. Props: numberId, initialData, isLoading, isSaving. Uses useMutation.
      VoicemailConfig.tsx         # Component for setting voicemail preferences. Props: numberId, initialData, isLoading, isSaving. Uses useMutation.
      AIAssistantManager.tsx      # Interface for managing the AI assistant. Props: numberId, initialData, isLoading, isSaving. Uses useMutation.
        AIPersonalitySelector.tsx # Dropdown/Selector for AI personality.
        AIKnowledgeUploader.tsx   # File upload/text input for knowledge base. Handles POST /train.
        AICustomCommandsEditor.tsx# UI for defining custom AI commands (potentially modal/sub-view).
      AutomationToggle.tsx        # Enable/disable switch for the number's automation. Props: numberId, initialStatus. Uses useMutation.
    AutomationLogsViewer.tsx      # (Potentially separate page/modal) Displays automation logs. Props: numberId. Fetches logs.
    AutomationSkeleton.tsx      # Loading state placeholder for number list or config panel.
  /shared                       # Shared components
    LoadingSpinner.tsx
    ErrorMessage.tsx
    EmptyState.tsx
    Modal.tsx
    TextInput.tsx
    Select.tsx
    Button.tsx
    ToggleSwitch.tsx
    ToastNotification.tsx (e.g., from react-hot-toast)
```

## 3. Data Sources & Backend Integration

React Query (`@tanstack/react-query`) will manage server state.

| Component / Feature         | API Endpoint                                                     | Method | React Query Hook        | Query Key                                             | Data Shape (Expected)                                                                                                                               | Notes                                                                                                                                                              |
| :-------------------------- | :--------------------------------------------------------------- | :----- | :---------------------- | :---------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :----------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **List Owned Numbers**      | `GET /api/numbers`                                               | GET    | `useQuery`              | `['numbers', 'owned']`                                | `[{ id: string, phoneNumber: string, friendlyName: string, automationStatus: 'active'\|'inactive' }, ...]`                                                       | Fetched in `AutomationLayout`.                                                                                                                                     |
| **Get Automation Config**   | `GET /api/numbers/{numberId}/automations`                        | GET    | `useQuery`              | `['automations', numberId]`                           | `{ callRules: {...}, smsRules: {...}, voicemailConfig: {...} }`                                                                                                    | Fetched in `AutomationConfigPanel` when `numberId` is selected. Enabled only when `numberId` is truthy.                                                           |
| **Update Automation Config**| `PUT /api/numbers/{numberId}/automations`                        | PUT    | `useMutation`           | -                                                     | Input: `{ callRules?, smsRules?, voicemailConfig? }`. Output: Success/Error.                                                                                      | Used in `CallRulesConfig`, `SmsRulesConfig`, `VoicemailConfig`. Invalidates `['automations', numberId]` on success.                                                |
| **Get AI Assistant Config** | `GET /api/numbers/{numberId}/ai-assistant`                       | GET    | `useQuery`              | `['ai-assistant', numberId]`                          | `{ personality: string, knowledgeSources: [...], commands: [...] }`                                                                                               | Fetched in `AIAssistantManager`. Enabled only when `numberId` is truthy.                                                                                           |
| **Update AI Assistant**     | `PUT /api/numbers/{numberId}/ai-assistant`                       | PUT    | `useMutation`           | -                                                     | Input: `{ personality?, knowledgeSources?, commands? }`. Output: Success/Error.                                                                                   | Used in `AIAssistantManager`. Invalidates `['ai-assistant', numberId]` on success.                                                                                 |
| **Train AI Assistant**      | `POST /api/numbers/{numberId}/ai-assistant/train`                | POST   | `useMutation`           | -                                                     | Input: `{ type: 'text'\|'file', content: string \| FormData }`. Output: Success/Error.                                                                             | Used in `AIKnowledgeUploader`. May trigger refetch/invalidation if needed.                                                                                         |
| **Toggle Automation**       | `POST /api/numbers/{numberId}/automations/toggle`                | POST   | `useMutation`           | -                                                     | Input: `{ enabled: boolean }`. Output: Success/Error.                                                                                                             | Used in `AutomationToggle`. Invalidates `['numbers', 'owned']` and potentially `['automations', numberId]` on success.                                            |
| **Get Automation Logs**     | `GET /api/numbers/{numberId}/automation-logs?type=...&limit=...`| GET    | `useQuery` / `useInfiniteQuery` | `['automation-logs', numberId, { type, filters }]` | `[{ id: string, timestamp: string, eventType: string, details: string, status: 'success'\|'failure' }, ...]` (Paginated)                                       | Used in `AutomationLogsViewer`.                                                                                                                                    |

**Backend Integration Notes:**
- Mutations (`useMutation`) will handle `PUT`/`POST`/`DELETE` requests.
- Use `onSuccess` callbacks in mutations to invalidate relevant queries (`queryClient.invalidateQueries`) ensuring data consistency.
- Implement optimistic updates where appropriate (e.g., toggling automation status) for better UX.
- Handle authentication via HttpOnly cookies.

## 4. UI State Management

- **Server State (React Query):**
    - Manages all data related to numbers, automation configurations, AI settings, and logs using `useQuery` and `useMutation`.
    - Query keys structured as above, enabling caching and targeted invalidation.
    - `isLoading`, `isError`, `error`, `data`, `isFetching`, `isPending` (from `useMutation`) states drive UI feedback (skeletons, spinners, error messages, disabling forms).
- **Global Client State (Zustand/Context):**
    - Minimal use; potentially for global flags like "is AI service available" if needed across sections.
- **Local Component State (`useState`):**
    - `AutomationLayout`: Stores the `selectedNumberId`.
    - Forms (`CallRulesConfig`, `SmsRulesConfig`, etc.): Manage input values, potentially using libraries like `react-hook-form` for complex forms and validation.
    - State for modal visibility (`AICustomCommandsEditor` modal).
    - State within `AIKnowledgeUploader` to manage file selection/preview.

## 5. UI/UX Behavior

- **Initial Load:** `AutomationLayout` fetches the number list (`GET /api/numbers`). `NumberList` displays numbers or a skeleton/loading state. `AutomationConfigPanel` is initially empty or shows a "Select a number" prompt.
- **Number Selection:** Clicking a number in `NumberList` updates `selectedNumberId` state in `AutomationLayout`. This triggers `AutomationConfigPanel` to fetch the config (`GET /api/numbers/{numberId}/automations` and `GET /api/numbers/{numberId}/ai-assistant`) for the selected number. Skeletons are shown within the panel during loading.
- **Configuration:** Users interact with forms/controls within the `AutomationConfigPanel` sub-components (`CallRulesConfig`, etc.). Local state tracks form inputs.
- **Saving Changes:** Clicking "Save" in a config component triggers the corresponding `useMutation` hook (`PUT` requests).
    - Disable the form and show a loading indicator (`isPending` from `useMutation`).
    - On success: Show a success toast notification. React Query invalidation refetches data, updating the UI.
    - On error: Show an error toast notification with details. Keep form enabled for correction.
- **AI Training:** `AIKnowledgeUploader` uses `useMutation` for the `POST /train` endpoint. Provide feedback on upload progress and success/failure.
- **Toggling Automation:** `AutomationToggle` uses `useMutation` for `POST /toggle`. Optimistically update the toggle state and revert on error. Invalidate `['numbers', 'owned']` to update the status indicator in `NumberList`.
- **Form Validation:** Implement client-side validation (e.g., required fields, correct formats) using standard HTML5 validation or libraries like `react-hook-form` or `zod`. Backend validation errors should also be displayed.
- **Responsiveness:** Use TailwindCSS for a responsive layout adapting from mobile to desktop. The `NumberList` might become a dropdown or sidebar on smaller screens, while the `AutomationConfigPanel` takes the main view.

## 6. Error Handling

- **API Fetch Errors (`useQuery`):**
    - If `GET /api/numbers` fails: Display an error message in the `NumberList` area.
    - If `GET /automations` or `GET /ai-assistant` fails for a selected number: Display an error message within the `AutomationConfigPanel`.
- **API Mutation Errors (`useMutation`):**
    - Display specific error messages via toast notifications (e.g., "Failed to save call rules: Invalid input.", "AI training failed."). Log detailed errors.
    - If backend validation fails, highlight the specific fields in the form with error messages.
- **Configuration Conflicts:** Rely on backend validation. Display clear error messages returned from the API if conflicting rules are submitted.
- **AI Service Unavailability:** If fetching AI config or training fails due to service issues, disable relevant UI sections (`AIAssistantManager`) and show an informative error message.
- **Authentication Errors:** Global handler redirects to login.

## 7. AI-Enhanced UI

- **Configuration Focus:** The UI provides forms and controls (`AIPersonalitySelector`, `AIKnowledgeUploader`, `AICustomCommandsEditor`) specifically designed for configuring the AI's behavior.
- **User-Friendly Abstractions:** Aim to abstract complex AI concepts into understandable settings (e.g., selecting a "Friendly" personality instead of raw model parameters).
- **Feedback:** Provide clear feedback during AI training processes (uploading, processing).
- **Logs:** `AutomationLogsViewer` allows users to see the results of AI actions (call answered, SMS sent, command executed).

## 8. Implementation Notes

- Follow TailwindCSS guidelines from `frontend_guidelines_document.mdc`.
- Adhere to naming and architecture rules from `cursor_project_rules.mdc`.
- Use shared components (`Button`, `Modal`, `TextInput`, etc.) for consistency.
- Implement accessibility standards.
- Add detailed logging for configuration changes, errors, and AI interactions as per `automation_section_document.mdc`.
- Consider using a form management library (`react-hook-form`) for complex configuration sections to simplify state management and validation.
