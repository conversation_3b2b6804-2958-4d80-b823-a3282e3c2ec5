"use client";

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>lock, FiPhone, FiMessageSquare } from 'react-icons/fi';
import CallHistory from './CallHistory';

export default function ActivityHistory({ phoneNumber }) {
  const [activeTab, setActiveTab] = useState('calls');
  const [callHistory, setCallHistory] = useState([]);
  const [messageHistory, setMessageHistory] = useState([]);

  // Load call history from API - similar to what was in AutomationChat
  useEffect(() => {
    const loadHistory = async () => {
      try {
        // Call the API to get call history for the selected phone number
        const response = await fetch(`/api/call-logs?phoneNumber=${phoneNumber}`);
        if (response.ok) {
          const data = await response.json();
          setCallHistory(data.calls || []);
          setMessageHistory(data.messages || []);
        }
      } catch (error) {
        console.error('Failed to load call history:', error);
        window.toast?.error('Failed to load call history');
      }
    };
    
    if (phoneNumber) {
      loadHistory();
    }
  }, [phoneNumber]);

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 shadow-lg overflow-hidden">
      <div className="border-b border-gray-700 p-4">
        <h2 className="text-lg font-semibold">Activity History</h2>
        <p className="text-gray-400 text-sm">
          View your recent calls and messages for this phone number.
        </p>
      </div>
      
      <div className="border-b border-gray-700 bg-gray-900/50">
        <div className="flex px-4">
          <button
            className={`py-3 px-4 font-medium relative ${
              activeTab === 'calls' 
                ? 'text-blue-400 border-b-2 border-blue-400' 
                : 'text-gray-400 hover:text-white'
            }`}
            onClick={() => setActiveTab('calls')}
          >
            <div className="flex items-center">
              <FiPhone className="mr-2" />
              <span>Calls</span>
              {callHistory.length > 0 && (
                <span className="ml-2 bg-gray-700 text-xs px-2 py-0.5 rounded-full">
                  {callHistory.length}
                </span>
              )}
            </div>
          </button>
          
          <button
            className={`py-3 px-4 font-medium relative ${
              activeTab === 'messages' 
                ? 'text-blue-400 border-b-2 border-blue-400' 
                : 'text-gray-400 hover:text-white'
            }`}
            onClick={() => setActiveTab('messages')}
          >
            <div className="flex items-center">
              <FiMessageSquare className="mr-2" />
              <span>Messages</span>
              {messageHistory.length > 0 && (
                <span className="ml-2 bg-gray-700 text-xs px-2 py-0.5 rounded-full">
                  {messageHistory.length}
                </span>
              )}
            </div>
          </button>
        </div>
      </div>
      
      <div className="p-4">
        <CallHistory 
          calls={activeTab === 'calls' ? callHistory : []}
          messages={activeTab === 'messages' ? messageHistory : []}
        />
      </div>
    </div>
  );
}
