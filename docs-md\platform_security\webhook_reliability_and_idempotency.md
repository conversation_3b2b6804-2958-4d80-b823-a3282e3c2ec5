---
description: Defines strategies for ensuring the secure, reliable, and idempotent processing of incoming webhooks.
---
# Webhook Reliability and Idempotency Strategy (`webhook_reliability_and_idempotency.mdc`)

## 1. Purpose and Scope

**Purpose:** To establish standardized procedures for securely receiving, validating, and processing incoming webhooks from external services (e.g., Stripe, Twilio, eSIM providers) in a reliable and idempotent manner. This prevents duplicate processing and ensures data integrity.

**Scope:**
- Security validation (signature verification).
- Idempotency key handling and event tracking.
- Acknowledgment responses to webhook providers.
- Asynchronous processing of webhook payloads.
- Error handling and retry logic for processing failures.
- Dead-letter queue (DLQ) for unprocessable events.
- Monitoring and alerting for webhook endpoints.

## 2. Security: Signature Validation

- **Mandate:** ALL incoming webhooks MUST be validated to ensure they originate from the expected provider and have not been tampered with.
- **Mechanism:** Utilize the signature verification methods provided by each service:
    - **Stripe:** Verify the `Stripe-Signature` header using the appropriate webhook signing secret. Requires access to the raw request body.
    - **Twilio:** Verify the `X-Twilio-Signature` header using the Twilio Auth Token. Requires constructing the expected signature based on the request URL and parameters.
    - **Other Providers:** Follow the specific signature validation mechanism documented by the provider.
- **Implementation:** Implement validation logic in dedicated middleware applied to webhook routes. Reject requests with invalid signatures immediately with an appropriate HTTP status code (e.g., 401 Unauthorized or 403 Forbidden).
- **Secret Management:** Securely store and manage webhook signing secrets (e.g., via environment variables or a secrets management system). Refer to `env_configuration_rules.mdc`.

## 3. Idempotency Control

- **Goal:** Ensure that the side effects of processing a specific webhook event occur exactly once, even if the provider sends the same event multiple times (e.g., due to network issues or retries).
- **Mechanism:**
    1.  **Extract Idempotency Key:** Identify a unique identifier for each event provided by the webhook source (e.g., Stripe `Event.id`, Twilio `CallSid` combined with `SequenceNumber` or timestamp for status callbacks, a unique ID from other providers).
    2.  **Track Processed Events:** Before processing the core logic, check if an event with this idempotency key has already been successfully processed or is currently being processed.
        - **Storage:** Use a persistent store (e.g., dedicated `WebhookEventLog` database table, Redis cache with appropriate TTL) to track the status of processed idempotency keys.
        - **Status Tracking:** Store the key along with its processing status (e.g., `PROCESSING`, `COMPLETED`, `FAILED`).
    3.  **Processing Logic:**
        - If the key is already marked `COMPLETED`, acknowledge the webhook immediately (e.g., HTTP 200 OK) without reprocessing.
        - If the key is marked `PROCESSING`, potentially return a conflict status (e.g., HTTP 409 Conflict) or wait briefly if concurrent processing is possible but undesirable.
        - If the key is new or marked `FAILED` (and eligible for retry), mark it as `PROCESSING`, execute the core business logic, and update the status to `COMPLETED` upon success or `FAILED` upon failure. Use database transactions to link the status update with the business logic execution where possible.
- **Implementation:** Implement idempotency checks within the webhook controller logic or dedicated utility functions.

## 4. Reliability and Processing

- **Immediate Acknowledgment:** Acknowledge receipt of the webhook to the provider as quickly as possible (e.g., HTTP 200 OK) *after* security validation and potentially recording the event for idempotency tracking, but *before* executing lengthy business logic.
- **Asynchronous Processing:** For any non-trivial business logic triggered by a webhook (e.g., updating database records, calling other services, triggering AI tasks), enqueue a job to be processed asynchronously by background workers.
    - **Mechanism:** Use the task queue system (ref `task_queue.mdc`). The webhook handler's primary responsibility becomes validation, idempotency check, and enqueuing the job.
    - **Payload:** Pass the validated webhook payload and relevant metadata (like the idempotency key) to the background job.
- **Error Handling (Job Processing):** Implement robust error handling within the background job processors. Distinguish between transient and permanent failures.
- **Retries (Job Processing):** Utilize the task queue's retry mechanisms for transient processing failures (ref `ai_task_processing_and_escalation.mdc` principles apply here too).
- **Dead-Letter Queue (DLQ):** Move webhook processing jobs that fail permanently (after retries) to a DLQ for investigation.

## 5. Monitoring and Alerting

- **Endpoint Monitoring:** Monitor the availability, latency, and error rates (4xx, 5xx) of all webhook endpoints.
- **Validation Failures:** Alert on significant numbers of signature validation failures (potential security issue).
- **Processing Failures:** Monitor the rate of webhook processing jobs failing and entering the DLQ. Alert on anomalies.
- **Idempotency Conflicts:** Monitor the rate of duplicate events being received (could indicate provider issues or configuration problems).
- **Logging:** Log key details of webhook reception, validation results, idempotency checks, job enqueuing, and final processing status.

## 6. Provider-Specific Considerations

- **Stripe:** Requires raw request body for signature validation. Events have unique IDs suitable for idempotency. Provides retry mechanisms.
- **Twilio:** Signature validation requires request URL and parameters. Event uniqueness for idempotency might require combining SIDs with timestamps or sequence numbers depending on the webhook type. Twilio also retries failed requests.

## 7. Related Documents

- `docs/functional_specs/api_gateway_routes.mdc` (Webhook endpoint definitions)
- `docs/functional_specs/task_queue.mdc` (Asynchronous processing queue)
- `docs/functional_specs/ai_task_processing_and_escalation.mdc` (Error handling/retry patterns)
- `docs/functional_specs/env_configuration_rules.mdc` (Secret management)
- `docs/functional_specs/notifications_and_alerts_document.mdc` (Alerting strategy)
- `docs/security_audit.md` (if exists)
- Specific webhook controller implementations (e.g., `stripeWebhookController.js`, `voiceWebhookController.js`)
