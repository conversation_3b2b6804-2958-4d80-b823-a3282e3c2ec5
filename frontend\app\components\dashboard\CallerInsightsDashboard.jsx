"use client";

import { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>hart, 
  Line, 
  <PERSON>Chart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { 
  ArrowUpIcon, 
  ArrowDownIcon,
  PhoneIcon,
  ClockIcon,
  ChatBubbleLeftRightIcon,
  UserGroupIcon,
  FaceSmileIcon,
  FaceFrownIcon,
  DocumentTextIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';

// Sample data for the dashboard
const generateCallVolumeData = () => {
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  return days.map(day => ({
    name: day,
    calls: Math.floor(Math.random() * 30) + 10,
    messages: Math.floor(Math.random() * 20) + 5,
    appointments: Math.floor(Math.random() * 10) + 2
  }));
};

const generateSentimentData = () => {
  return [
    { name: 'Positive', value: 65, color: '#4ade80' },
    { name: 'Neutral', value: 25, color: '#60a5fa' },
    { name: 'Negative', value: 10, color: '#f87171' }
  ];
};

const generateTopQuestionsData = () => {
  return [
    { question: 'What are your business hours?', count: 24 },
    { question: 'Do you offer free consultations?', count: 18 },
    { question: 'How much do your services cost?', count: 15 },
    { question: 'Can I schedule an appointment?', count: 12 },
    { question: 'What documents do I need to bring?', count: 9 }
  ];
};

const generateConversionData = () => {
  return [
    { name: 'Jan', rate: 25 },
    { name: 'Feb', rate: 30 },
    { name: 'Mar', rate: 28 },
    { name: 'Apr', rate: 32 },
    { name: 'May', rate: 35 },
    { name: 'Jun', rate: 40 },
    { name: 'Jul', rate: 38 }
  ];
};

const CallerInsightsDashboard = () => {
  const [callVolumeData, setCallVolumeData] = useState([]);
  const [sentimentData, setSentimentData] = useState([]);
  const [topQuestionsData, setTopQuestionsData] = useState([]);
  const [conversionData, setConversionData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('week');
  
  // Metrics
  const [metrics, setMetrics] = useState({
    totalCalls: 0,
    avgCallDuration: '0:00',
    responseRate: 0,
    conversionRate: 0,
    callsVsPrevious: 0,
    conversionVsPrevious: 0
  });
  
  // Load data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // In a real app, this would be an API call to fetch data
      const callVolume = generateCallVolumeData();
      const sentiment = generateSentimentData();
      const topQuestions = generateTopQuestionsData();
      const conversion = generateConversionData();
      
      // Calculate metrics
      const totalCalls = callVolume.reduce((sum, day) => sum + day.calls, 0);
      const avgCallDuration = '3:24'; // In a real app, this would be calculated
      const responseRate = 92; // Percentage
      const conversionRate = 34; // Percentage
      
      // Calculate changes vs previous period
      const callsVsPrevious = 12.5; // Percentage increase
      const conversionVsPrevious = 8.2; // Percentage increase
      
      setCallVolumeData(callVolume);
      setSentimentData(sentiment);
      setTopQuestionsData(topQuestions);
      setConversionData(conversion);
      
      setMetrics({
        totalCalls,
        avgCallDuration,
        responseRate,
        conversionRate,
        callsVsPrevious,
        conversionVsPrevious
      });
      
      setIsLoading(false);
    };
    
    fetchData();
  }, [timeRange]);
  
  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-gray-900 p-3 rounded-lg border border-gray-800 shadow-lg">
          <p className="text-gray-300 font-medium">{label}</p>
          {payload.map((entry, index) => (
            <p key={`item-${index}`} style={{ color: entry.color || entry.fill }} className="text-sm">
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };
  
  return (
    <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
      <div className="flex flex-col space-y-6">
        {/* Header with title and time range selector */}
        <div className="flex flex-wrap items-center justify-between gap-4">
          <h2 className="text-xl font-semibold text-white flex items-center">
            <PhoneIcon className="h-6 w-6 mr-2 text-purple-400" />
            Caller Insights Dashboard
          </h2>
          
          <div className="flex bg-gray-800/50 rounded-lg p-1">
            <button
              onClick={() => setTimeRange('week')}
              className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                timeRange === 'week' 
                  ? 'bg-purple-600 text-white' 
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Week
            </button>
            <button
              onClick={() => setTimeRange('month')}
              className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                timeRange === 'month' 
                  ? 'bg-purple-600 text-white' 
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Month
            </button>
            <button
              onClick={() => setTimeRange('quarter')}
              className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                timeRange === 'quarter' 
                  ? 'bg-purple-600 text-white' 
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Quarter
            </button>
          </div>
        </div>
        
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Total Calls */}
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-gray-400 text-xs">Total Calls</p>
                <h4 className="text-2xl font-semibold text-white mt-1">{metrics.totalCalls}</h4>
              </div>
              <div className={`flex items-center ${metrics.callsVsPrevious > 0 ? 'text-green-400' : 'text-red-400'} text-sm`}>
                {metrics.callsVsPrevious > 0 ? (
                  <ArrowUpIcon className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 mr-1" />
                )}
                {Math.abs(metrics.callsVsPrevious).toFixed(1)}%
              </div>
            </div>
            <p className="text-gray-500 text-xs mt-2">vs previous period</p>
          </div>
          
          {/* Average Call Duration */}
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
            <div className="flex justify-between">
              <div>
                <p className="text-gray-400 text-xs">Avg. Call Duration</p>
                <h4 className="text-2xl font-semibold text-white mt-1">{metrics.avgCallDuration}</h4>
              </div>
              <ClockIcon className="h-6 w-6 text-purple-400" />
            </div>
            <p className="text-gray-500 text-xs mt-2">minutes:seconds</p>
          </div>
          
          {/* Response Rate */}
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
            <div className="flex justify-between">
              <div>
                <p className="text-gray-400 text-xs">Response Rate</p>
                <h4 className="text-2xl font-semibold text-white mt-1">{metrics.responseRate}%</h4>
              </div>
              <ChatBubbleLeftRightIcon className="h-6 w-6 text-purple-400" />
            </div>
            <p className="text-gray-500 text-xs mt-2">of calls received a response</p>
          </div>
          
          {/* Conversion Rate */}
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-gray-400 text-xs">Conversion Rate</p>
                <h4 className="text-2xl font-semibold text-white mt-1">{metrics.conversionRate}%</h4>
              </div>
              <div className={`flex items-center ${metrics.conversionVsPrevious > 0 ? 'text-green-400' : 'text-red-400'} text-sm`}>
                {metrics.conversionVsPrevious > 0 ? (
                  <ArrowUpIcon className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 mr-1" />
                )}
                {Math.abs(metrics.conversionVsPrevious).toFixed(1)}%
              </div>
            </div>
            <p className="text-gray-500 text-xs mt-2">calls resulting in appointments</p>
          </div>
        </div>
        
        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Call Volume Chart */}
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
            <h3 className="text-white font-medium mb-4 flex items-center">
              <PhoneIcon className="h-5 w-5 mr-2 text-purple-400" />
              Call Volume
            </h3>
            <div className="h-64">
              {isLoading ? (
                <div className="h-full flex items-center justify-center">
                  <div className="loading-pulse"></div>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={callVolumeData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" vertical={false} />
                    <XAxis 
                      dataKey="name" 
                      tick={{ fill: '#9CA3AF' }} 
                      axisLine={{ stroke: '#4B5563' }}
                      tickLine={{ stroke: '#4B5563' }}
                    />
                    <YAxis 
                      tick={{ fill: '#9CA3AF' }} 
                      axisLine={{ stroke: '#4B5563' }}
                      tickLine={{ stroke: '#4B5563' }}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Bar dataKey="calls" name="Calls" fill="#8b5cf6" radius={[4, 4, 0, 0]} />
                    <Bar dataKey="messages" name="SMS" fill="#60a5fa" radius={[4, 4, 0, 0]} />
                    <Bar dataKey="appointments" name="Appointments" fill="#4ade80" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </div>
          </div>
          
          {/* Conversion Rate Trend */}
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
            <h3 className="text-white font-medium mb-4 flex items-center">
              <CalendarIcon className="h-5 w-5 mr-2 text-purple-400" />
              Conversion Rate Trend
            </h3>
            <div className="h-64">
              {isLoading ? (
                <div className="h-full flex items-center justify-center">
                  <div className="loading-pulse"></div>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={conversionData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" vertical={false} />
                    <XAxis 
                      dataKey="name" 
                      tick={{ fill: '#9CA3AF' }} 
                      axisLine={{ stroke: '#4B5563' }}
                      tickLine={{ stroke: '#4B5563' }}
                    />
                    <YAxis 
                      tick={{ fill: '#9CA3AF' }} 
                      axisLine={{ stroke: '#4B5563' }}
                      tickLine={{ stroke: '#4B5563' }}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Line 
                      type="monotone" 
                      dataKey="rate" 
                      name="Conversion Rate (%)" 
                      stroke="#4ade80" 
                      strokeWidth={2}
                      dot={{ r: 4, fill: '#4ade80', stroke: '#4ade80' }}
                      activeDot={{ r: 6, fill: '#4ade80', stroke: '#fff' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </div>
          </div>
        </div>
        
        {/* Additional Insights Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Sentiment Analysis */}
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
            <h3 className="text-white font-medium mb-4 flex items-center">
              <FaceSmileIcon className="h-5 w-5 mr-2 text-purple-400" />
              Caller Sentiment
            </h3>
            <div className="h-64">
              {isLoading ? (
                <div className="h-full flex items-center justify-center">
                  <div className="loading-pulse"></div>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={sentimentData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      labelLine={false}
                    >
                      {sentimentData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </div>
          </div>
          
          {/* Top Questions */}
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30 col-span-1 lg:col-span-2">
            <h3 className="text-white font-medium mb-4 flex items-center">
              <DocumentTextIcon className="h-5 w-5 mr-2 text-purple-400" />
              Top Questions Asked
            </h3>
            {isLoading ? (
              <div className="h-64 flex items-center justify-center">
                <div className="loading-pulse"></div>
              </div>
            ) : (
              <div className="space-y-3">
                {topQuestionsData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
                    <div className="flex items-start">
                      <span className="bg-purple-500/20 text-purple-400 rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 flex-shrink-0">
                        {index + 1}
                      </span>
                      <p className="text-white text-sm">{item.question}</p>
                    </div>
                    <span className="bg-gray-600 px-2 py-1 rounded-full text-gray-300 text-xs">
                      {item.count} times
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {/* AI Insights Section */}
        <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
          <h3 className="text-white font-medium mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            AI-Generated Insights
          </h3>
          
          <div className="space-y-4">
            <div className="p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
              <p className="text-white text-sm">
                <span className="font-medium">Peak Call Times:</span> Most calls are received between 10:00 AM and 2:00 PM on weekdays. Consider increasing AI assistant availability during these hours.
              </p>
            </div>
            
            <div className="p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
              <p className="text-white text-sm">
                <span className="font-medium">Common Patterns:</span> 68% of callers ask about pricing within the first minute of conversation. Consider adding pricing information to your initial AI greeting.
              </p>
            </div>
            
            <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
              <p className="text-white text-sm">
                <span className="font-medium">Conversion Opportunity:</span> Callers who mention &quot;urgent&quot; or &quot;emergency&quot; convert at a 3x higher rate. Consider prioritizing these calls for immediate human follow-up.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallerInsightsDashboard;
