# CallSaver - AI Call Management Solution

CallSaver is a modern, AI-powered call management platform that helps businesses never miss another important call. Our system engages customers 24/7, handles inquiries, schedules appointments, and qualifies leads even when you can't answer.

## Features

- AI-powered SMS responses for missed calls
- Multi-language support (English, German, Arabic)
- Affiliate program system
- Advanced analytics dashboard
- GeoIP-based localization
- User management system
- Responsive design for all devices

## Technology Stack

- **Frontend**: Next.js, React, TailwindCSS, Framer Motion
- **Backend**: Next.js API Routes
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **Internationalization**: next-intl
- **Payments**: Stripe
- **Containerization**: Docker
- **Deployment**: Railway

## Getting Started

### Prerequisites

- Node.js 18 or later
- npm or yarn
- Docker and Docker Compose (for containerized development)
- PostgreSQL (local or containerized)
- MongoDB (optional, for AI-generated content storage)

### Environment Setup

1. Clone the repository
   ```bash
   git clone https://github.com/your-username/callsaver.git
   cd callsaver
   ```

2. Create `.env` file (copy from example)
   ```bash
   cp .env.example .env
   ```

3. Update the environment variables in `.env` with your own values

### Installation

#### Option 1: Local Development

1. Install dependencies
   ```bash
   npm install
   ```

2. Generate Prisma client
   ```bash
   npm run prisma:generate
   ```

3. Run database migrations
   ```bash
   npm run prisma:migrate
   ```

4. Start the development server
   ```bash
   npm run dev
   ```

#### Option 2: Docker Development

1. Build and start the containers
   ```bash
   docker-compose up --build
   ```

2. Run database migrations inside the container
   ```bash
   docker-compose exec app npx prisma migrate dev
   ```

### Accessing the App

- Frontend: [http://localhost:3000](http://localhost:3000)
- Prisma Studio: [http://localhost:5555](http://localhost:5555) (run `npm run prisma:studio` first)

## Project Structure

- `/app` - Next.js app directory
  - `/api` - API routes for backend functionality
  - `/components` - Reusable React components
  - `/dashboard` - Dashboard pages and components
  - `/hooks` - Custom React hooks
  - `/i18n` - Internationalization setup
  - `/data` - Static data and mock content
- `/prisma` - Prisma schema and migrations
- `/public` - Static assets
- `/.github/workflows` - CI/CD pipelines

## Deployment

### Railway Deployment

1. Install Railway CLI
   ```bash
   npm install -g @railway/cli
   ```

2. Login to Railway
   ```bash
   railway login
   ```

3. Link to your Railway project
   ```bash
   railway link
   ```

4. Deploy the application
   ```bash
   railway up
   ```

### Docker Deployment

1. Build the production Docker image
   ```bash
   docker build -t callsaver:latest .
   ```

2. Run the container
   ```bash
   docker run -p 3000:3000 --env-file .env.production callsaver:latest
   ```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## CRM System Features

The CallSaver CRM system includes the following components:

1. **Call List Management**
   - Advanced filtering, sorting, and search functionality
   - Detailed call information including AI responses, customer responses, and outcomes
   - Priority indicators and status tracking

2. **Customer Journey Visualization**
   - Timeline view of all customer interactions
   - Color-coded event types (calls, SMS, website visits, purchases)
   - Quick filtering and customer search

3. **AI Response Analyzer**
   - Analyze AI responses for quality and effectiveness
   - Get improvement suggestions and key metrics
   - Track response sentiment and customer satisfaction

4. **Customer Profiles**
   - Comprehensive customer information and contact details
   - Purchase history and lifetime value metrics
   - Interaction history with detailed logs
   - AI-powered recommendations for next actions
   - Profile editing and note management

## Getting Started

```bash
# Install dependencies
npm install

# Run the development server
npm run dev

# Build for production
npm run build

# Start the production server
npm start
```

## Tech Stack

- Next.js
- React
- Tailwind CSS
- Heroicons
- Recharts (for data visualization)

## Supabase Integration

This application uses Supabase for database, authentication, and storage needs. Multiple connection options are available:

1. **Direct PostgreSQL Connection**: For server-side operations requiring persistent connections
2. **Supabase Client (App Framework)**: For client-side features like auth, storage, and realtime
3. **Server-Side Supabase Client**: For server components requiring admin access
4. **Mobile (Expo) Client**: For Expo mobile applications
5. **Prisma ORM Configuration**: For type-safe database operations with connection pooling

To test the connections, visit the `/supabase-test` route after setting up your environment variables.

For detailed documentation on using each connection type, see [docs/supabase-connection-guide.md](docs/supabase-connection-guide.md).

## Environment Variables

The following environment variables are required:

### Supabase Configuration
```
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### Database Connection (Choose based on your needs)
```
DATABASE_URL=your-connection-pooling-url
DIRECT_URL=your-direct-connection-url
```

## Features

- Multi-connection database architecture
- Authentication with Supabase
- Real-time updates with Supabase Realtime
- File storage with Supabase Storage
- Mobile support with optimized Expo integration 