'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { Tab } from '@headlessui/react';
import { Settings, Phone, MessageSquare } from 'lucide-react';
import { AIAssistantConfig } from './AutomationConfigPanel';
import LoadingSpinner from '../shared/LoadingSpinner';
import AIPersonalitySelector from './AIPersonalitySelector';
import AIKnowledgeUploader from './AIKnowledgeUploader';
import AICustomCommandsEditor from './AICustomCommandsEditor';
import AIConversationTester from './AIConversationTester';

interface AIAssistantManagerProps {
  numberId: string;
  initialData: AIAssistantConfig;
}

export default function AIAssistantManager({
  numberId,
  initialData,
}: AIAssistantManagerProps) {
  // State for form data and active tab
  const [formData, setFormData] = useState(initialData);
  const [activeTab, setActiveTab] = useState(0);
  
  // Get the query client
  const queryClient = useQueryClient();

  // Mutation for updating AI assistant config
  const updateMutation = useMutation({
    mutationFn: async (data: AIAssistantConfig) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data: response } = await axios.put(`/api/numbers/${numberId}/ai-assistant`, data);
      return response;
    },
    onSuccess: () => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['ai-assistant', numberId] });
      
      // Show success toast (you can use a toast library like react-hot-toast)
      console.log('AI assistant configuration updated successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to update AI assistant configuration');
    },
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateMutation.mutate(formData);
  };

  // Handle personality change
  const handlePersonalityChange = (personality: string) => {
    setFormData(prev => ({
      ...prev,
      personality,
    }));
  };

  // Handle knowledge sources update
  const handleKnowledgeUpdate = (knowledgeSources: AIAssistantConfig['knowledgeSources']) => {
    setFormData(prev => ({
      ...prev,
      knowledgeSources,
    }));
  };

  // Handle commands update
  const handleCommandsUpdate = (commands: AIAssistantConfig['commands']) => {
    setFormData(prev => ({
      ...prev,
      commands,
    }));
  };

  return (
    <div className="space-y-6">
      <Tab.Group selectedIndex={activeTab} onChange={setActiveTab}>
        <Tab.List className="flex space-x-1 rounded-lg bg-gray-100 dark:bg-gray-700 p-1">
          <Tab
            className={({ selected }) =>
              `flex items-center space-x-2 w-full rounded-md py-2.5 px-3 text-sm font-medium leading-5 text-gray-900 dark:text-white
               ${selected ? 'bg-white dark:bg-gray-800 shadow' : 'text-gray-500 dark:text-gray-400 hover:bg-white/[0.12] hover:text-gray-900 dark:hover:text-white'}`
            }
          >
            <Settings size={16} />
            <span>Configuration</span>
          </Tab>
          <Tab
            className={({ selected }) =>
              `flex items-center space-x-2 w-full rounded-md py-2.5 px-3 text-sm font-medium leading-5 text-gray-900 dark:text-white
               ${selected ? 'bg-white dark:bg-gray-800 shadow' : 'text-gray-500 dark:text-gray-400 hover:bg-white/[0.12] hover:text-gray-900 dark:hover:text-white'}`
            }
          >
            <Phone size={16} />
            <span>Test Call</span>
          </Tab>
          <Tab
            className={({ selected }) =>
              `flex items-center space-x-2 w-full rounded-md py-2.5 px-3 text-sm font-medium leading-5 text-gray-900 dark:text-white
               ${selected ? 'bg-white dark:bg-gray-800 shadow' : 'text-gray-500 dark:text-gray-400 hover:bg-white/[0.12] hover:text-gray-900 dark:hover:text-white'}`
            }
          >
            <MessageSquare size={16} />
            <span>Test SMS</span>
          </Tab>
        </Tab.List>
        <Tab.Panels>
          {/* Configuration Panel */}
          <Tab.Panel>
            <form onSubmit={handleSubmit} className="space-y-8">
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  AI Assistant Configuration
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                  Configure how your AI assistant behaves when handling calls and messages.
                </p>

                {/* Personality Selector */}
                <div className="mb-8">
                  <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                    Assistant Personality
                  </h4>
                  <AIPersonalitySelector
                    selectedPersonality={formData.personality}
                    onChange={handlePersonalityChange}
                    disabled={updateMutation.isPending}
                  />
                </div>

                {/* Knowledge Base */}
                <div className="mb-8">
                  <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                    Knowledge Base
                  </h4>
                  <AIKnowledgeUploader
                    numberId={numberId}
                    knowledgeSources={formData.knowledgeSources}
                    onUpdate={handleKnowledgeUpdate}
                    disabled={updateMutation.isPending}
                  />
                </div>

                {/* Custom Commands */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
                    Custom Commands
                  </h4>
                  <AICustomCommandsEditor
                    commands={formData.commands}
                    onUpdate={handleCommandsUpdate}
                    disabled={updateMutation.isPending}
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={updateMutation.isPending}
                >
                  {updateMutation.isPending ? (
                    <>
                      <LoadingSpinner size="small" color="white" />
                      <span className="ml-2">Saving...</span>
                    </>
                  ) : (
                    'Save Changes'
                  )}
                </button>
              </div>
            </form>
          </Tab.Panel>
          
          {/* Call Test Panel */}
          <Tab.Panel>
            <div className="mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Test Call AI Response
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Simulate a phone call to test how your AI assistant responds. Changes made in the Configuration 
                tab will be reflected in the AI's responses once saved.
              </p>
            </div>
            <AIConversationTester 
              numberId={numberId} 
              mode="call" 
            />
          </Tab.Panel>
          
          {/* SMS Test Panel */}
          <Tab.Panel>
            <div className="mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Test SMS AI Response
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Simulate an SMS conversation to test how your AI assistant responds. Changes made in the Configuration 
                tab will be reflected in the AI's responses once saved.
              </p>
            </div>
            <AIConversationTester 
              numberId={numberId} 
              mode="sms" 
            />
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>
    </div>
  );
}
