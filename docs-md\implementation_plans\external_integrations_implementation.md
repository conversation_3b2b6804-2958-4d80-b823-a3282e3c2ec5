# External Integrations Implementation Plan

## Overview
This document outlines the implementation plan for the External Integrations UI within the CallSaver.app dashboard. This section allows users to connect and manage integrations with third-party services like Google Calendar, Outlook, Slack, Salesforce, Zapier, HubSpot, Zoom, and Shopify.

## Component Hierarchy
```mermaid
graph TD
    A[ExternalIntegrationsPage] --> B(IntegrationGrid);
    A --> C(IntegrationDetailModal{props: integration, isOpen, onClose});

    B --> B1(IntegrationCard{props: integration, onConnect, onManage, onDisconnect});

    C --> C1(OAuthConnectButton{props: authUrl, onConnected});
    C --> C2(WebhookConfig{props: webhookUrl, onSave});
    C --> C3(SyncSettingsForm{props: settings, onSave});
    C --> C4(ConnectionStatus{props: status});
    C --> C5(DisconnectButton{props: onDisconnect});

    subgraph Google Calendar / Outlook
        C3_GC[SyncSettingsForm_GC]
    end
    subgraph Slack
        C2_SL[WebhookConfig_SL]
        C3_SL[SyncSettingsForm_SL]
    end
    subgraph Salesforce
        C3_SF[SyncSettingsForm_SF]
    end
    subgraph Zapier
        C2_ZP[WebhookConfig_ZP]
    end
    subgraph HubSpot
        C3_HS[SyncSettingsForm_HS]
    end
    subgraph Zoom
        C3_ZM[SyncSettingsForm_ZM]
    end
    subgraph Shopify
        C2_SH[WebhookConfig_SH]
        C3_SH[SyncSettingsForm_SH]
    end

    C -- Specific Config --> C1
    C -- Specific Config --> C2
    C -- Specific Config --> C3
    C -- Status --> C4
    C -- Actions --> C5
```

*   **ExternalIntegrationsPage**: Main container for the integrations section.
    *   State: `integrationsList`, `selectedIntegration`, `isModalOpen`, `isLoading`
*   **IntegrationGrid**: Displays available integrations as cards.
    *   **IntegrationCard**: Represents a single integration. Props: `integration` (name, icon, status), `onConnect(id)`, `onManage(id)`, `onDisconnect(id)`.
*   **IntegrationDetailModal**: Modal for configuring a specific integration. Props: `integration`, `isOpen`, `onClose`.
    *   State: `currentSettings`, `connectionStatus`, `isLoading`
    *   **OAuthConnectButton**: Handles OAuth flow initiation. Props: `authUrl`, `onConnected()`.
    *   **WebhookConfig**: Displays and allows configuration of webhooks. Props: `webhookUrl` (read-only for Zapier, configurable for others?), `onSave(config)`.
    *   **SyncSettingsForm**: Form for integration-specific settings (e.g., sync direction, data mapping). Props: `settings`, `onSave(settings)`. Specific forms per integration (e.g., `SyncSettingsForm_GC` for Google Calendar).
    *   **ConnectionStatus**: Displays the current connection status (Connected, Disconnected, Error). Props: `status`.
    *   **DisconnectButton**: Button to disconnect the integration. Props: `onDisconnect()`.

## Data Flow
1.  **Load Integrations**: `ExternalIntegrationsPage` fetches the list of available integrations and their statuses (`/api/integrations`) using TanStack Query.
2.  **Connect (OAuth)**: User clicks "Connect" on an `IntegrationCard`. `ExternalIntegrationsPage` opens `IntegrationDetailModal`. User clicks `OAuthConnectButton`. Redirects to provider's OAuth page. After authorization, user is redirected back to a callback URL (`/api/auth/callback/{provider}`). Backend handles token exchange, saves credentials securely, updates integration status. Frontend polls (`/api/integrations/{id}/status`) or uses WebSocket to update UI.
3.  **Connect (Webhook/API Key)**: User clicks "Connect". Modal opens. User inputs API key or configures webhooks via `WebhookConfig` or `SyncSettingsForm`. `onSave` calls backend (`/api/integrations/{id}/connect`) with credentials/config. Backend validates and saves.
4.  **Manage Settings**: User clicks "Manage". Modal opens. `IntegrationDetailModal` fetches current settings (`/api/integrations/{id}/settings`). User modifies settings in `SyncSettingsForm`, clicks save. `onSave` calls backend (`PUT /api/integrations/{id}/settings`).
5.  **Disconnect**: User clicks "Disconnect" on card or in modal. Confirmation prompt shown. `onDisconnect` calls backend (`DELETE /api/integrations/{id}`). Frontend updates UI.

## API Integration
*   `GET /api/integrations`: Fetch list of all available integrations and their connection status. Returns: `[{ id, name, description, icon, status: 'connected'|'disconnected'|'error' }]`.
*   `GET /api/integrations/{id}`: Fetch details for a specific integration (used if needed before opening modal). Returns: `{ id, name, ..., authType: 'oauth'|'apikey'|'webhook', authUrl?, webhookUrl? }`.
*   `GET /api/integrations/{id}/status`: Fetch connection status for a specific integration. Returns: `{ status: 'connected'|'disconnected'|'error', details? }`.
*   `POST /api/integrations/{id}/connect`: Initiate connection (for non-OAuth) or potentially finalize connection after callback. Body: `{ apiKey?, webhookConfig?, settings? }`. Returns: `{ success: true }`.
*   `GET /api/integrations/{id}/settings`: Fetch current configuration settings for the integration. Returns: `{ setting1: value, ... }`.
*   `PUT /api/integrations/{id}/settings`: Update configuration settings. Body: `{ setting1: value, ... }`. Returns: `{ success: true }`.
*   `DELETE /api/integrations/{id}`: Disconnect the integration. Returns: `{ success: true }`.
*   `GET /api/auth/callback/{provider}`: Backend endpoint for OAuth callbacks. Handles code exchange, token storage. Redirects user back to the frontend integrations page.

**Integration-Specific Endpoints (Examples - handled by backend service layer):**
*   Google Calendar: Sync events, check availability.
*   Slack: Send notifications, receive slash commands via webhook.
*   Salesforce: CRUD operations on Contacts, Log Calls.
*   Zapier: Outbound webhooks triggered by CallSaver events.
*   HubSpot: Sync Contacts, Log Activities.
*   Zoom: Schedule meetings.
*   Shopify: Listen for order webhooks, potentially trigger calls/SMS.

## State Management
*   **TanStack Query**:
    *   Fetching integration list (`/api/integrations`).
    *   Fetching specific integration details/status (`/api/integrations/{id}`, `/api/integrations/{id}/status`).
    *   Fetching integration settings (`/api/integrations/{id}/settings`).
    *   Mutations for connect, update settings, disconnect.
*   **Zustand**:
    *   Managing modal visibility (`isModalOpen`).
    *   Storing the currently selected integration for the modal.
    *   Local form state within `IntegrationDetailModal` before saving.
    *   Temporary loading/connecting states during OAuth redirects or connection attempts.

## User Interactions
*   **Viewing Integrations**: User navigates to the Integrations page. Grid displays cards.
*   **Connecting**: User clicks "Connect". Modal appears. Follows OAuth flow or enters required info. Status updates on card/modal.
*   **Managing**: User clicks "Manage". Modal appears with current settings. User modifies and saves.
*   **Disconnecting**: User clicks "Disconnect". Confirms action. Integration status updates to "Disconnected".

## Error Handling
*   **API Errors**: Use TanStack Query error states for fetching data. Display user-friendly messages. Use toasts for mutation errors (connect, save settings, disconnect).
*   **OAuth Errors**: Handle errors during the OAuth flow (e.g., user denies access, invalid scope, provider error). Redirect back with error query parameters, display message in UI.
*   **Connection Errors**: If backend fails to validate credentials or connect to the third-party API, display specific error messages in the modal or via toast.
*   **Sync Errors**: Backend should log sync errors. UI could potentially display a summary of recent sync issues fetched from an API endpoint (`/api/integrations/{id}/sync-status`).
*   **Rate Limiting**: Backend needs to handle rate limits from third-party APIs. UI might show a temporary warning if rate limits are frequently hit.

## Implementation Notes
*   **OAuth Library**: Use a library like `next-auth` extensively if possible, as it handles many OAuth providers and complexities. Backend needs robust token management (encryption, refresh tokens).
*   **Security**: Store API keys and OAuth tokens securely (e.g., encrypted in the database, using a secrets manager). Never expose sensitive credentials to the frontend.
*   **Webhooks**: Ensure webhook endpoints are secured (e.g., using signature verification). Provide clear instructions for users setting up webhooks (especially for Zapier).
*   **Data Mapping**: For sync integrations (Salesforce, HubSpot), provide flexible data mapping options in the UI (`SyncSettingsForm`).
*   **UI Consistency**: Maintain a consistent look and feel across different integration configuration modals.
*   **Background Sync**: Sync logic will run on the backend, likely triggered by cron jobs or webhooks. The UI primarily manages the configuration and displays status.
*   **Provider SDKs/APIs**: Backend services will encapsulate the logic for interacting with each third-party API/SDK.

## Related Components
*   **DashboardLayout**: Provides overall structure.
*   **NotificationSystem**: For toasts.
*   **AuthenticationContext**: For user context.
*   Backend Services for each integration (e.g., `GoogleCalendarService`, `SlackService`).

## Link to prompt_to_mdc_router.mdc
*   **Primary Purpose**: Defines the implementation plan for the External Integrations management UI.
*   **Frontend Components**: `ExternalIntegrationsPage`, `IntegrationGrid`, `IntegrationCard`, `IntegrationDetailModal`, and specific configuration components.
*   **API Endpoints**: `/api/integrations`, `/api/integrations/{id}`, `/api/integrations/{id}/status`, `/api/integrations/{id}/connect`, `/api/integrations/{id}/settings`, `/api/auth/callback/{provider}`.
*   **Dependencies**: Assumes backend API endpoints and corresponding service logic for each integration are implemented. May depend on functional specs in `docs/integrations/`.
