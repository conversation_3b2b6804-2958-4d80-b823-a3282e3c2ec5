import { create } from 'zustand';

// Define types for automation data
export interface AutomationRule {
  id: string;
  phoneNumberId: string;
  name: string;
  type: 'voicemail' | 'forwarding' | 'menu' | 'timeOfDay' | 'callScreening' | 'custom';
  isActive: boolean;
  priority: number;
  conditions: {
    timeOfDay?: {
      start?: string;
      end?: string;
      days?: string[];
    };
    callerType?: string[];
    inputDigit?: string;
    menuOption?: string;
  };
  actions: {
    forward?: {
      to: string;
      screening?: boolean;
    };
    voicemail?: {
      greeting?: string;
      transcribe?: boolean;
      emailNotification?: boolean;
    };
    playMessage?: {
      text: string;
      voice?: string;
    };
    menu?: {
      greeting: string;
      options: {
        digit: string;
        action: string;
      }[];
    };
    sms?: {
      message: string;
      to?: string;
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface AIAssistantConfig {
  id: string;
  phoneNumberId: string;
  isEnabled: boolean;
  mode: 'screening' | 'assistant' | 'voicemail' | 'custom';
  prompt: string;
  voice?: string;
  useTranscription: boolean;
  memoryEnabled: boolean;
  personalityTags: string[];
  customInstructions?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AutomationLog {
  id: string;
  phoneNumberId: string;
  callSid?: string;
  ruleId?: string;
  assistantId?: string;
  type: 'rule' | 'assistant' | 'system';
  event: string;
  details: any;
  timestamp: string;
}

// Define the automation state interface
interface AutomationState {
  rules: Record<string, AutomationRule[]>;  // phoneNumberId -> rules
  aiAssistants: Record<string, AIAssistantConfig>;  // phoneNumberId -> config
  logs: Record<string, AutomationLog[]>;  // phoneNumberId -> logs
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setRules: (phoneNumberId: string, rules: AutomationRule[]) => void;
  addRule: (rule: AutomationRule) => void;
  updateRule: (rule: AutomationRule) => void;
  removeRule: (ruleId: string, phoneNumberId: string) => void;
  toggleRuleActive: (ruleId: string, phoneNumberId: string, isActive: boolean) => void;
  
  setAIAssistant: (phoneNumberId: string, config: AIAssistantConfig) => void;
  updateAIAssistant: (config: AIAssistantConfig) => void;
  
  setLogs: (phoneNumberId: string, logs: AutomationLog[]) => void;
  addLog: (log: AutomationLog) => void;
  
  setLoading: (status: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

// Create the automation store
export const useAutomationStore = create<AutomationState>()((set, get) => ({
  rules: {},
  aiAssistants: {},
  logs: {},
  isLoading: false,
  error: null,
  
  setRules: (phoneNumberId, rules) => {
    set((state) => ({
      rules: { ...state.rules, [phoneNumberId]: rules }
    }));
  },
  
  addRule: (rule) => {
    set((state) => {
      const numberRules = state.rules[rule.phoneNumberId] || [];
      return {
        rules: {
          ...state.rules,
          [rule.phoneNumberId]: [...numberRules, rule]
        }
      };
    });
  },
  
  updateRule: (rule) => {
    set((state) => {
      const numberRules = state.rules[rule.phoneNumberId] || [];
      const updatedRules = numberRules.map(r => r.id === rule.id ? rule : r);
      return {
        rules: {
          ...state.rules,
          [rule.phoneNumberId]: updatedRules
        }
      };
    });
  },
  
  removeRule: (ruleId, phoneNumberId) => {
    set((state) => {
      const numberRules = state.rules[phoneNumberId] || [];
      const filteredRules = numberRules.filter(r => r.id !== ruleId);
      return {
        rules: {
          ...state.rules,
          [phoneNumberId]: filteredRules
        }
      };
    });
  },
  
  toggleRuleActive: (ruleId, phoneNumberId, isActive) => {
    set((state) => {
      const numberRules = state.rules[phoneNumberId] || [];
      const updatedRules = numberRules.map(r => 
        r.id === ruleId ? { ...r, isActive } : r
      );
      return {
        rules: {
          ...state.rules,
          [phoneNumberId]: updatedRules
        }
      };
    });
  },
  
  setAIAssistant: (phoneNumberId, config) => {
    set((state) => ({
      aiAssistants: { ...state.aiAssistants, [phoneNumberId]: config }
    }));
  },
  
  updateAIAssistant: (config) => {
    set((state) => ({
      aiAssistants: { ...state.aiAssistants, [config.phoneNumberId]: config }
    }));
  },
  
  setLogs: (phoneNumberId, logs) => {
    set((state) => ({
      logs: { ...state.logs, [phoneNumberId]: logs }
    }));
  },
  
  addLog: (log) => {
    set((state) => {
      const numberLogs = state.logs[log.phoneNumberId] || [];
      return {
        logs: {
          ...state.logs,
          [log.phoneNumberId]: [log, ...numberLogs]
        }
      };
    });
  },
  
  setLoading: (status) => {
    set({ isLoading: status });
  },
  
  setError: (error) => {
    set({ error });
  },
  
  clearError: () => {
    set({ error: null });
  },
}));

// Export selectors for efficiency
export const useAutomationRules = (phoneNumberId: string) => 
  useAutomationStore((state) => state.rules[phoneNumberId] || []);

export const useAIAssistant = (phoneNumberId: string) => 
  useAutomationStore((state) => state.aiAssistants[phoneNumberId]);

export const useAutomationLogs = (phoneNumberId: string) => 
  useAutomationStore((state) => state.logs[phoneNumberId] || []);

export const useAutomationLoading = () => 
  useAutomationStore((state) => state.isLoading);

export const useAutomationError = () => 
  useAutomationStore((state) => state.error);
