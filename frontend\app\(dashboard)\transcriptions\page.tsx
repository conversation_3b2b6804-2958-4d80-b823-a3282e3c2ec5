'use client';

import { useState } from 'react';
import { useTranscriptions, TranscriptionFilters } from '../../../hooks/useTranscriptions';
import TranscriptionList from '../../../components/transcriptions/TranscriptionList';
import TranscriptionDetailView from '../../../components/transcriptions/TranscriptionDetailView';
import TranscriptionSearchBar from '../../../components/transcriptions/TranscriptionSearchBar';
import LoadingSpinner from '../../../components/shared/LoadingSpinner';
import ErrorMessage from '../../../components/shared/ErrorMessage';
import { 
  MagnifyingGlassIcon, 
  ChartBarIcon, 
  DocumentTextIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

export default function TranscriptionDashboardPage() {
  // State
  const [selectedTranscriptionId, setSelectedTranscriptionId] = useState<string | null>(null);
  const [isDetailViewOpen, setIsDetailViewOpen] = useState(false);
  const [filters, setFilters] = useState<TranscriptionFilters>({
    page: 1,
    limit: 20
  });

  // Fetch transcriptions
  const { 
    data, 
    isLoading, 
    isError, 
    error, 
    refetch 
  } = useTranscriptions(filters);

  // Handlers
  const handleSelectTranscription = (id: string) => {
    setSelectedTranscriptionId(id);
    setIsDetailViewOpen(true);
  };

  const handleCloseDetailView = () => {
    setIsDetailViewOpen(false);
  };

  const handleSearch = (searchFilters: TranscriptionFilters) => {
    setFilters(prev => ({ ...prev, ...searchFilters, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Voice Transcriptions</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            View, search, and analyze your call transcriptions
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <button
            onClick={() => refetch()}
            className="flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
          >
            <ArrowPathIcon className="h-5 w-5 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-6">
        <TranscriptionSearchBar onSearch={handleSearch} />
      </div>

      <div className="grid grid-cols-1 gap-6">
        {isLoading ? (
          <div className="flex justify-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : isError ? (
          <ErrorMessage 
            message={`Error loading transcriptions: ${error instanceof Error ? error.message : 'Unknown error'}`} 
          />
        ) : data?.results?.length === 0 ? (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 text-center">
            <DocumentTextIcon className="h-16 w-16 mx-auto text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">No transcriptions found</h3>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              {filters.search 
                ? 'Try adjusting your search filters' 
                : 'Your transcriptions will appear here once you receive or make calls'}
            </p>
          </div>
        ) : (
          <>
            <TranscriptionList 
              transcriptions={data.results} 
              onSelect={handleSelectTranscription} 
            />
            
            {data.totalCount > filters.limit && (
              <div className="flex justify-center mt-6">
                <nav className="flex items-center">
                  <button
                    onClick={() => handlePageChange(filters.page! - 1)}
                    disabled={filters.page === 1}
                    className="px-3 py-1 rounded-md mr-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <span className="text-gray-700 dark:text-gray-300">
                    Page {filters.page} of {Math.ceil(data.totalCount / filters.limit!)}
                  </span>
                  <button
                    onClick={() => handlePageChange(filters.page! + 1)}
                    disabled={filters.page! >= Math.ceil(data.totalCount / filters.limit!)}
                    className="px-3 py-1 rounded-md ml-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 disabled:opacity-50"
                  >
                    Next
                  </button>
                </nav>
              </div>
            )}
          </>
        )}
      </div>

      {selectedTranscriptionId && (
        <TranscriptionDetailView
          transcriptionId={selectedTranscriptionId}
          isOpen={isDetailViewOpen}
          onClose={handleCloseDetailView}
        />
      )}
    </div>
  );
}
