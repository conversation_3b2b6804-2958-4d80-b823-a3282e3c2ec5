---
description: Defines the overall testing strategy for the CallSaver platform, covering different test levels, tools, and environments.
---
# Testing Strategy (`testing_strategy.mdc`)

## 1. Purpose and Scope

**Purpose:** To establish a comprehensive and consistent testing strategy for the CallSaver platform, ensuring code quality, application reliability, and confidence in deployments.

**Scope:**
- Testing principles and goals.
- Levels of testing (Unit, Integration, End-to-End, Contract).
- Testing frameworks and tools for backend and frontend.
- Test environments (local, CI, staging).
- Test data management.
- Code coverage targets.
- Testing in CI/CD pipelines.
- Specialized testing areas (performance, security, AI model evaluation).
- Roles and responsibilities.

## 2. Principles and Goals

- **Quality Assurance:** Ensure features meet functional requirements and quality standards.
- **Regression Prevention:** Prevent previously fixed bugs from recurring.
- **Confidence in Deployment:** Enable safe and frequent deployments to production.
- **Early Feedback:** Catch bugs early in the development cycle when they are cheaper to fix.
- **Maintainability:** Tests should be clear, maintainable, and reliable.
- **Automation:** Automate tests at all levels wherever practical.

## 3. Levels of Testing

### 3.1. Unit Testing

- **Goal:** Verify the correctness of individual functions, methods, or components in isolation.
- **Scope:** Test specific logic units, mocking external dependencies (database, external APIs, other services).
- **Backend:**
    - **Framework:** Jest.
    - **Focus:** Test controllers, services, utilities, helper functions. Mock Prisma Client, external API clients, queue interactions.
- **Frontend:**
    - **Framework:** Jest with React Testing Library.
    - **Focus:** Test individual React components, hooks, utility functions, store logic (Zustand selectors/actions). Mock API client, browser APIs.
- **Responsibility:** Developers writing the code.

### 3.2. Integration Testing

- **Goal:** Verify the interaction and communication between different components or services within the application.
- **Scope:** Test how units work together. May involve interacting with real (but controlled) dependencies like a test database or mock external services.
- **Backend:**
    - **Framework:** Jest (potentially with Supertest for API endpoint testing).
    - **Focus:** Test API endpoints, ensuring correct request handling, middleware execution, service interaction, database interaction (against a test database), and response formatting. Mock external service APIs (Twilio, Stripe, AI providers) using tools like `nock` or dedicated mock servers. Test task queue producers and consumers.
- **Frontend:**
    - **Framework:** Jest with React Testing Library.
    - **Focus:** Test interactions between multiple components, routing, state management integration across features. Mock API responses using tools like Mock Service Worker (`msw`).
- **Responsibility:** Developers writing the feature.

### 3.3. End-to-End (E2E) Testing

- **Goal:** Verify complete user flows through the application, simulating real user interactions from the UI to the backend and back.
- **Scope:** Test critical user journeys across the entire stack.
- **Framework:** Cypress or Playwright.
- **Focus:** Simulate user actions (clicking buttons, filling forms, navigating pages) in a real browser interacting with a deployed version of the application (ideally in a staging environment). Verify UI changes, data persistence, and overall flow correctness.
- **Responsibility:** Dedicated QA team or developers (depending on team structure). Keep E2E tests focused on critical paths due to their higher maintenance cost and potential flakiness.

### 3.4. Contract Testing (Optional/Future)

- **Goal:** Ensure that independently developed services (e.g., frontend and backend, or different microservices) adhere to agreed-upon API contracts.
- **Framework:** Pact or similar contract testing tools.
- **Scope:** Define contracts (expected requests/responses) and verify that providers (e.g., backend API) fulfill the expectations of consumers (e.g., frontend). Useful for preventing breaking changes between services.

## 4. Tooling

- **Backend:** Jest, Supertest, `nock` (or similar HTTP mocking), Prisma testing utilities.
- **Frontend:** Jest, React Testing Library, Mock Service Worker (`msw`), Cypress/Playwright.
- **Code Coverage:** Jest's built-in coverage reporting (or tools like Istanbul).
- **CI/CD Platform:** GitHub Actions, GitLab CI, Jenkins, etc.

## 5. Test Environments

- **Local:** Developers run unit and integration tests locally during development.
- **CI (Continuous Integration):** All unit and integration tests run automatically on every commit/PR. E2E tests might run on merges to main or nightly.
- **Staging:** A dedicated environment mirroring production where E2E tests and manual QA are performed before deploying to production.

## 6. Test Data Management

- **Unit Tests:** Use mocks and stubs.
- **Integration Tests:** Use fixtures or factories to generate consistent test data in a dedicated test database. Reset the database state between test runs.
- **E2E Tests:** Requires careful setup in the staging environment. May involve creating dedicated test accounts or using data seeding scripts. Avoid polluting the staging environment with excessive test data.

## 7. Code Coverage

- **Target:** Aim for a meaningful code coverage target (e.g., >80%) for unit and integration tests, but focus on testing critical logic and edge cases rather than solely optimizing the percentage.
- **Monitoring:** Track coverage trends over time using CI tools.

## 8. CI/CD Integration

- Integrate automated tests (unit, integration) into the CI/CD pipeline.
- Fail builds if tests fail.
- Optionally run E2E tests as part of the pipeline (e.g., after deployment to staging).
- Report test results and coverage in the CI/CD interface.

## 9. Specialized Testing

- **Performance Testing:** Use tools like k6, JMeter, or cloud provider load testing services to test application performance and scalability under load (typically against staging). Refer to `docs/platform_ux/performance_budget.mdc`.
- **Security Testing:** Conduct regular security scans (SAST, DAST), dependency vulnerability checks, and potentially penetration testing. Refer to `docs/security_audit.md` (if exists).
- **AI Model Evaluation:** Specific strategies for evaluating AI prompt/model performance (ref `ai_prompt_training_strategy.mdc`).

## 10. Roles and Responsibilities

- **Developers:** Responsible for writing unit and integration tests for their code. Fixing failing tests. Maintaining test suites.
- **QA Team (if applicable):** Responsible for E2E testing, exploratory testing, defining test plans for major features.
- **DevOps/Platform Team:** Responsible for maintaining test environments and CI/CD pipelines.

## 11. Related Documents

- `back/backend/jest.config.js`
- `front/mainpage/jest.config.js` (or equivalent)
- `docs/dev_guides/internal_cli_tooling_strategy.mdc` (Testing CLI tools)
- `docs/architecture/service_failover_and_redundancy.mdc` (Testing failover)
- `docs/platform_ux/performance_budget.mdc`
- `docs/ai/ai_prompt_training_strategy.mdc`
- `docs/security_audit.md` (if exists)
