"use client";

import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { FiX, FiAlertTriangle, FiCheckCircle, FiInfo } from 'react-icons/fi';

export default function Toast({ 
  message, 
  type = 'info', 
  duration = 5000, 
  onClose 
}) {
  const [isVisible, setIsVisible] = useState(true);
  const [portalRoot, setPortalRoot] = useState(null);
  
  useEffect(() => {
    setPortalRoot(document.body);
    
    // Auto-dismiss after duration
    if (duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(() => onClose?.(), 300); // Wait for animation
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [duration, onClose]);
  
  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => onClose?.(), 300);
  };
  
  const getIcon = () => {
    switch (type) {
      case 'success': return <FiCheckCircle className="text-green-400" />;
      case 'error': return <FiAlertTriangle className="text-red-400" />;
      case 'warning': return <FiAlertTriangle className="text-yellow-400" />;
      default: return <FiInfo className="text-blue-400" />;
    }
  };
  
  const getColor = () => {
    switch (type) {
      case 'success': return 'border-green-400 bg-green-400/10';
      case 'error': return 'border-red-400 bg-red-400/10';
      case 'warning': return 'border-yellow-400 bg-yellow-400/10';
      default: return 'border-blue-400 bg-blue-400/10';
    }
  };
  
  if (!portalRoot) return null;
  
  return createPortal(
    <div 
      className={`fixed bottom-4 right-4 max-w-md p-4 rounded-md border ${getColor()} shadow-lg transition-all duration-300 ${
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}
    >
      <div className="flex items-start">
        <div className="mr-3 text-xl mt-0.5">
          {getIcon()}
        </div>
        
        <div className="flex-1">
          <div className="font-medium text-white">{message}</div>
        </div>
        
        <button 
          onClick={handleClose}
          className="ml-3 text-gray-400 hover:text-white"
        >
          <FiX />
        </button>
      </div>
    </div>,
    portalRoot
  );
}

// Toast Container for managing multiple toasts
export function ToastContainer() {
  const [toasts, setToasts] = useState([]);
  
  const addToast = (message, type = 'info', duration = 5000) => {
    const id = Date.now();
    setToasts(prev => [...prev, { id, message, type, duration }]);
    return id;
  };
  
  const removeToast = (id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };
  
  // Expose methods globally
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.toast = {
        show: addToast,
        success: (message, duration) => addToast(message, 'success', duration),
        error: (message, duration) => addToast(message, 'error', duration),
        warning: (message, duration) => addToast(message, 'warning', duration),
        info: (message, duration) => addToast(message, 'info', duration)
      };
    }
    
    return () => {
      if (typeof window !== 'undefined') {
        delete window.toast;
      }
    };
  }, []);
  
  return (
    <>
      {toasts.map(toast => (
        <Toast
          key={toast.id}
          message={toast.message}
          type={toast.type}
          duration={toast.duration}
          onClose={() => removeToast(toast.id)}
        />
      ))}
    </>
  );
} 