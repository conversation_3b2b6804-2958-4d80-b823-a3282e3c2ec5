'use client';

import { useState, useEffect, useCallback } from 'react';
import supabaseClient from '../utils/supabaseClient';
import { useRouter } from 'next/navigation';
import { useSession } from '../providers/SessionProvider';

export default function AuthStatus() {
  const { user, loading, session } = useSession();
  const router = useRouter();
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);

  // Debounced navigation function
  const navigateTo = useCallback((path) => {
    if (isNavigating) return;
    setIsNavigating(true);
    router.push(path);
    // Reset navigation state after a delay
    setTimeout(() => setIsNavigating(false), 1000);
  }, [router, isNavigating]);

  const handleSignOut = async () => {
    if (isSigningOut || isNavigating) return;
    
    try {
      setIsSigningOut(true);
      await supabaseClient.auth.signOut();
      // Clear any stored demo user
      localStorage.removeItem('callsaver_demo_user');
      // Small delay to ensure state updates properly
      await new Promise(resolve => setTimeout(resolve, 100));
      navigateTo('/');
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setIsSigningOut(false);
    }
  };

  const handleSignIn = () => {
    if (isNavigating) return;
    navigateTo('/signin');
  };
  
  const showAccessToken = async () => {
    if (isSigningOut) return;
    
    const currentSession = session || (await supabaseClient.auth.getSession())?.data?.session;
    
    if (currentSession && currentSession.access_token) {
      console.log('==== CURRENT ACCESS TOKEN (Client Attempt) ====');
      console.log(currentSession.access_token);
      console.log('================================================');
      
      try {
        await navigator.clipboard.writeText(currentSession.access_token);
        alert('Access token copied to clipboard (if available client-side)!');
      } catch (err) {
        console.error('Failed to copy token:', err);
      }
    } else {
      console.log('No active session or access token available client-side.');
      alert('No active session or access token available client-side. Token is primarily managed server-side.');
    }
  };

  if (loading) {
    return <div className="p-4 rounded-lg bg-gray-800 shadow-md">Loading authentication status...</div>;
  }

  return (
    <div className="p-4 rounded-lg bg-gray-800 shadow-md">
      {user ? (
        <div>
          <p className="mb-2">Signed in as: <span className="font-medium text-purple-400">{user.email}</span></p>
          <p className="mb-4 text-sm text-gray-400">User ID: {user.id}</p>
          <div className="flex flex-wrap gap-2">
            <button 
              onClick={handleSignOut}
              disabled={isSigningOut || isNavigating}
              className={`px-4 py-2 ${isSigningOut || isNavigating ? 'bg-gray-600 cursor-not-allowed' : 'bg-red-600 hover:bg-red-700'} rounded-lg text-white transition-colors`}
            >
              {isSigningOut ? 'Signing Out...' : 'Sign Out'}
            </button>
            <button 
              onClick={showAccessToken}
              disabled={isSigningOut || isNavigating}
              className={`px-4 py-2 ${isSigningOut || isNavigating ? 'bg-gray-600 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'} rounded-lg text-white transition-colors`}
            >
              Show Access Token
            </button>
          </div>
        </div>
      ) : (
        <div>
          <p className="mb-4">You are not signed in.</p>
          <button 
            onClick={handleSignIn}
            disabled={isNavigating}
            className={`px-4 py-2 ${isNavigating ? 'bg-gray-600 cursor-not-allowed' : 'bg-purple-600 hover:bg-purple-700'} rounded-lg text-white transition-colors`}
          >
            Sign In
          </button>
        </div>
      )}
    </div>
  );
} 