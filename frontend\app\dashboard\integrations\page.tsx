'use client';

import { useState } from 'react';
import IntegrationsLayout from '../../../components/integrations/IntegrationsLayout';
import IntegrationsSkeleton from '../../../components/integrations/IntegrationsSkeleton';
import { Suspense } from 'react';

export default function IntegrationsPage() {
  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
        Integrations
      </h1>
      <Suspense fallback={<IntegrationsSkeleton />}>
        <IntegrationsLayout />
      </Suspense>
    </div>
  );
}
