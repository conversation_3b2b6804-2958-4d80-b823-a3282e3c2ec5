'use client';

import { useState } from 'react';
import { useNotificationPreferences, useUpdateNotificationPreferences } from '../../../hooks/useSettings';
import NotificationChannelsForm from './NotificationChannelsForm';
import NotificationTypesForm from './NotificationTypesForm';
import LoadingSpinner from '../shared/LoadingSpinner';
import ErrorMessage from '../shared/ErrorMessage';

export default function NotificationPreferencesPanel() {
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  
  // Fetch notification preferences
  const { 
    data: preferences, 
    isLoading, 
    isError, 
    error 
  } = useNotificationPreferences();
  
  // Update notification preferences mutation
  const updatePreferences = useUpdateNotificationPreferences();
  
  // Handle success message
  const handleSuccess = () => {
    setSuccessMessage('Notification preferences updated successfully');
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };
  
  // Handle channel update
  const handleChannelUpdate = async (channels: any) => {
    try {
      await updatePreferences.mutateAsync({ channels });
      handleSuccess();
    } catch (err) {
      // Error is handled by the mutation
      console.error('Error updating notification channels:', err);
    }
  };
  
  // Handle notification types update
  const handleTypesUpdate = async (types: any) => {
    try {
      await updatePreferences.mutateAsync({ types });
      handleSuccess();
    } catch (err) {
      // Error is handled by the mutation
      console.error('Error updating notification types:', err);
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }
  
  if (isError) {
    return (
      <ErrorMessage 
        title="Failed to load notification preferences" 
        message="We couldn't load your notification preferences. Please try again later."
        error={error instanceof Error ? error : undefined}
        onRetry={() => window.location.reload()}
      />
    );
  }
  
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Notification Preferences</h2>
      
      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-800 dark:text-green-400">
          {successMessage}
        </div>
      )}
      
      {updatePreferences.isError && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-800 dark:text-red-400">
          {updatePreferences.error instanceof Error 
            ? updatePreferences.error.message 
            : 'An error occurred while updating your notification preferences'}
        </div>
      )}
      
      <div className="space-y-8">
        {/* Notification Channels */}
        <div className="pb-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Notification Channels</h3>
          
          <NotificationChannelsForm 
            channels={preferences?.channels || []} 
            isLoading={updatePreferences.isPending}
            onUpdate={handleChannelUpdate}
          />
        </div>
        
        {/* Notification Types */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Notification Types</h3>
          
          <NotificationTypesForm 
            notificationTypes={preferences?.types || []} 
            isLoading={updatePreferences.isPending}
            onUpdate={handleTypesUpdate}
          />
        </div>
      </div>
    </div>
  );
}
