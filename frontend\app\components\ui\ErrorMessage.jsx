"use client";

import React from 'react';
import {
  ExclamationTriangleIcon,
  ExclamationCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

export default function ErrorMessage({ 
  title,
  message,
  severity = 'error', // 'error', 'warning', 'info'
  action,
  actionText = 'Try Again',
  onActionClick,
  className = '',
  dismissible = false,
  onDismiss
}) {
  // Configure colors based on severity
  const severityConfig = {
    error: {
      bg: 'bg-red-950/50',
      border: 'border-red-800/50',
      text: 'text-red-200',
      icon: <ExclamationCircleIcon className="h-5 w-5 text-red-400" />,
      actionBg: 'bg-red-700/60 hover:bg-red-700/80'
    },
    warning: {
      bg: 'bg-yellow-950/50',
      border: 'border-yellow-800/50',
      text: 'text-yellow-200',
      icon: <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />,
      actionBg: 'bg-yellow-700/60 hover:bg-yellow-700/80'
    },
    info: {
      bg: 'bg-blue-950/50',
      border: 'border-blue-800/50',
      text: 'text-blue-200',
      icon: <InformationCircleIcon className="h-5 w-5 text-blue-400" />,
      actionBg: 'bg-blue-700/60 hover:bg-blue-700/80'
    }
  };
  
  const config = severityConfig[severity] || severityConfig.error;
  
  return (
    <div className={`rounded-lg ${config.bg} border ${config.border} p-4 ${config.text} ${className}`}>
      <div className="flex">
        <div className="flex-shrink-0">
          {config.icon}
        </div>
        <div className="ml-3 flex-1">
          {title && (
            <h3 className="text-sm font-medium">{title}</h3>
          )}
          <div className={`${title ? 'mt-2' : ''} text-sm`}>
            <p>{message}</p>
          </div>
          
          {action && (
            <div className="mt-4">
              <div className="flex space-x-3">
                <button
                  type="button"
                  className={`rounded-md px-3 py-2 text-sm font-medium ${config.actionBg} transition-colors`}
                  onClick={onActionClick}
                >
                  {actionText}
                </button>
              </div>
            </div>
          )}
        </div>
        
        {dismissible && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                type="button"
                onClick={onDismiss}
                className={`inline-flex rounded-md p-1.5 ${config.text} hover:bg-gray-800 transition-colors`}
              >
                <span className="sr-only">Dismiss</span>
                <svg
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 