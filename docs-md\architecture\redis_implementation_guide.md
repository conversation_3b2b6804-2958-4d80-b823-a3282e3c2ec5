# Redis Implementation Guide

## Overview

This document outlines the Redis implementation in the CallSaver application, including best practices, usage patterns, and configuration guidelines.

## Redis Client Architecture

The application uses a centralized Redis client implementation located at `back/backend/lib/redis.js`. This client provides:

1. **Consistent Interface**: A unified API for all Redis operations across the application
2. **Error Handling**: Robust error handling with graceful fallbacks
3. **Reconnection Logic**: Automatic reconnection with backoff strategy
4. **Connection State Management**: Tracking of connection state to avoid operations when disconnected
5. **Logging**: Comprehensive logging of Redis operations and errors

## Usage Guidelines

### Importing the Redis Client

Always use the centralized Redis client:

```javascript
const redis = require('../lib/redis');
```

### Basic Operations

The Redis client provides wrapper methods for common Redis operations:

```javascript
// Setting a value with expiration
await redis.set('key', 'value', 'EX', 3600); // Expires in 1 hour

// Getting a value
const value = await redis.get('key');

// Deleting a key
await redis.del('key');

// Checking if a key exists
const exists = await redis.exists('key');

// Incrementing a counter
const newValue = await redis.incr('counter');

// Setting expiration on a key
await redis.expire('key', 3600); // 1 hour
```

### Connection State Awareness

Always check the connection state before performing critical operations:

```javascript
if (redis.isConnected()) {
  // Perform Redis operations
} else {
  // Use fallback mechanism
}
```

### Error Handling

The Redis client handles errors internally, but you should still handle potential null returns:

```javascript
const value = await redis.get('key');
if (value === null) {
  // Key doesn't exist or Redis is disconnected
  // Use fallback or default value
}
```

## Common Usage Patterns

### Rate Limiting

Rate limiting is implemented using the `rate-limiter-flexible` package with Redis:

```javascript
const { RateLimiterRedis } = require('rate-limiter-flexible');
const redis = require('../lib/redis');

const limiter = new RateLimiterRedis({
  storeClient: redis.getClient(),
  keyPrefix: 'rl:',
  points: 10, // 10 requests
  duration: 1, // per 1 second
});

// In your route handler
try {
  await limiter.consume(userKey);
  // Process the request
} catch (error) {
  // Rate limit exceeded
}
```

### Caching

For caching data:

```javascript
async function getCachedData(key, fetchFunction, expireSeconds = 3600) {
  // Try to get from cache first
  const cachedData = await redis.get(key);
  
  if (cachedData) {
    return JSON.parse(cachedData);
  }
  
  // If not in cache, fetch the data
  const data = await fetchFunction();
  
  // Cache the data
  await redis.set(key, JSON.stringify(data), 'EX', expireSeconds);
  
  return data;
}
```

### Distributed Locks

For implementing distributed locks:

```javascript
async function acquireLock(lockName, ttlSeconds = 30) {
  const lockKey = `lock:${lockName}`;
  const lockValue = Date.now().toString();
  
  // Try to acquire the lock
  const acquired = await redis.set(lockKey, lockValue, 'EX', ttlSeconds, 'NX');
  
  if (!acquired) {
    return null; // Lock not acquired
  }
  
  return lockValue; // Return lock value for release verification
}

async function releaseLock(lockName, lockValue) {
  const lockKey = `lock:${lockName}`;
  
  // Get the current lock value
  const currentValue = await redis.get(lockKey);
  
  // Only release if we own the lock
  if (currentValue === lockValue) {
    await redis.del(lockKey);
    return true;
  }
  
  return false;
}
```

### Pub/Sub

For implementing publish/subscribe patterns:

```javascript
// Publisher
async function publishEvent(channel, message) {
  await redis.getClient().publish(channel, JSON.stringify(message));
}

// Subscriber
function subscribeToEvents(channel, callback) {
  const subscriber = redis.getClient().duplicate();
  
  subscriber.on('message', (channel, message) => {
    callback(JSON.parse(message));
  });
  
  subscriber.subscribe(channel);
  
  return subscriber; // Return for later unsubscribe
}
```

## Security Considerations

### Redis Security Best Practices

1. **Authentication**: Always use Redis authentication in production
2. **Network Security**: Restrict Redis access to only the application servers
3. **No Internet Exposure**: Never expose Redis directly to the internet
4. **Data Sensitivity**: Don't store sensitive data in Redis without encryption
5. **Resource Limits**: Configure memory limits to prevent resource exhaustion

### Secure Configuration

In production, ensure Redis is configured with:

```
requirepass <strong_password>
bind 127.0.0.1 # Or internal network IPs
protected-mode yes
maxmemory 1gb # Adjust based on needs
maxmemory-policy allkeys-lru
```

## Monitoring and Maintenance

### Health Checks

Implement regular health checks:

```javascript
async function checkRedisHealth() {
  try {
    const result = await redis.ping();
    return result === 'PONG';
  } catch (error) {
    return false;
  }
}
```

### Key Management

Implement key expiration and namespace conventions:

1. **Always set TTL**: Set expiration on all keys to prevent memory leaks
2. **Use Namespaces**: Prefix keys with application-specific namespaces
3. **Avoid Long Keys**: Keep key names reasonably short to save memory

## Redis Configuration

### Development Environment

For local development:

```
port 6379
bind 127.0.0.1
protected-mode yes
maxmemory 256mb
maxmemory-policy allkeys-lru
```

### Production Environment

For production:

```
port 6379
bind 127.0.0.1 # Or internal network IPs
requirepass <strong_password>
protected-mode yes
maxmemory 2gb # Adjust based on needs
maxmemory-policy allkeys-lru
appendonly yes
appendfsync everysec
```

## Troubleshooting

### Common Issues

1. **Connection Refused**: Check if Redis server is running and accessible
2. **Authentication Failed**: Verify Redis password is correct
3. **Out of Memory**: Check Redis memory usage and limits
4. **Slow Performance**: Look for expensive operations or large keys

### Debugging

Enable verbose logging for debugging:

```javascript
// In your application code
process.env.REDIS_DEBUG = 'true';
```

## Future Improvements

1. **Redis Cluster**: Implement Redis Cluster for high availability
2. **Redis Sentinel**: Add Redis Sentinel for automatic failover
3. **Circuit Breaker**: Implement circuit breaker pattern for Redis operations
4. **Metrics Collection**: Add detailed metrics for Redis operations
5. **Advanced Caching**: Implement more sophisticated caching strategies

## References

- [Redis Documentation](https://redis.io/documentation)
- [Redis Best Practices](https://redis.io/topics/optimization)
- [Redis Security](https://redis.io/topics/security)
