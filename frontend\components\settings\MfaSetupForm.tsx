'use client';

import { useState } from 'react';
import Image from 'next/image';
import { useForm } from 'react-hook-form';
import { useMfaSetup, useEnableMfa, useDisableMfa } from '../../../hooks/useSettings';

interface MfaSetupFormProps {
  isMfaEnabled: boolean;
  onSuccess: (enabled: boolean) => void;
}

export default function MfaSetupForm({ isMfaEnabled, onSuccess }: MfaSetupFormProps) {
  const [showSetup, setShowSetup] = useState(false);
  const [showDisable, setShowDisable] = useState(false);
  
  // MFA setup query
  const mfaSetup = useMfaSetup();
  
  // Enable MFA mutation
  const enableMfa = useEnableMfa();
  
  // Disable MFA mutation
  const disableMfa = useDisableMfa();
  
  // Form for verification code
  const { 
    register: registerEnable, 
    handleSubmit: handleEnableSubmit, 
    formState: { errors: enableErrors },
    reset: resetEnableForm
  } = useForm({
    defaultValues: {
      verificationCode: ''
    }
  });
  
  // Form for disabling MFA
  const { 
    register: registerDisable, 
    handleSubmit: handleDisableSubmit, 
    formState: { errors: disableErrors },
    reset: resetDisableForm
  } = useForm({
    defaultValues: {
      password: '',
      verificationCode: ''
    }
  });
  
  // Start MFA setup
  const handleStartSetup = () => {
    setShowSetup(true);
    mfaSetup.refetch();
  };
  
  // Cancel MFA setup
  const handleCancelSetup = () => {
    setShowSetup(false);
    resetEnableForm();
  };
  
  // Submit verification code to enable MFA
  const handleEnableMfa = async (data: any) => {
    try {
      await enableMfa.mutateAsync(data.verificationCode);
      setShowSetup(false);
      resetEnableForm();
      onSuccess(true);
    } catch (err) {
      // Error is handled by the mutation
      console.error('Error enabling MFA:', err);
    }
  };
  
  // Start MFA disable process
  const handleStartDisable = () => {
    setShowDisable(true);
  };
  
  // Cancel MFA disable
  const handleCancelDisable = () => {
    setShowDisable(false);
    resetDisableForm();
  };
  
  // Submit form to disable MFA
  const handleDisableMfa = async (data: any) => {
    try {
      await disableMfa.mutateAsync({
        password: data.password,
        verificationCode: data.verificationCode
      });
      setShowDisable(false);
      resetDisableForm();
      onSuccess(false);
    } catch (err) {
      // Error is handled by the mutation
      console.error('Error disabling MFA:', err);
    }
  };
  
  return (
    <div>
      {/* Current MFA Status */}
      <div className="flex items-center justify-between mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div>
          <p className="text-sm font-medium text-gray-900 dark:text-white">
            Two-factor authentication is {isMfaEnabled ? 'enabled' : 'disabled'}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            {isMfaEnabled 
              ? 'Your account is protected with an additional layer of security.' 
              : 'Add an extra layer of security to your account by requiring a verification code.'}
          </p>
        </div>
        
        {isMfaEnabled ? (
          <button
            type="button"
            onClick={handleStartDisable}
            className="px-4 py-2 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Disable
          </button>
        ) : (
          <button
            type="button"
            onClick={handleStartSetup}
            className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Enable
          </button>
        )}
      </div>
      
      {/* MFA Setup Form */}
      {showSetup && (
        <div className="mt-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Set up two-factor authentication
          </h4>
          
          {mfaSetup.isLoading ? (
            <div className="flex justify-center py-6">
              <div className="animate-spin w-8 h-8 border-4 border-indigo-500 border-t-transparent rounded-full"></div>
            </div>
          ) : mfaSetup.isError ? (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-800 dark:text-red-400 text-sm mb-4">
              {mfaSetup.error instanceof Error 
                ? mfaSetup.error.message 
                : 'Failed to set up two-factor authentication. Please try again.'}
            </div>
          ) : mfaSetup.data ? (
            <div>
              <div className="mb-6">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  1. Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)
                </p>
                
                <div className="flex justify-center mb-4">
                  <div className="bg-white p-2 rounded-lg">
                    <Image 
                      src={mfaSetup.data.qrCodeUrl} 
                      alt="QR Code" 
                      width={200} 
                      height={200} 
                    />
                  </div>
                </div>
                
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  2. If you can't scan the QR code, enter this code manually:
                </p>
                
                <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded font-mono text-sm mb-4 text-center">
                  {mfaSetup.data.secret}
                </div>
                
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  3. Enter the verification code from your authenticator app
                </p>
                
                {enableMfa.isError && (
                  <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-800 dark:text-red-400 text-sm mb-4">
                    {enableMfa.error instanceof Error 
                      ? enableMfa.error.message 
                      : 'Invalid verification code. Please try again.'}
                  </div>
                )}
                
                <form onSubmit={handleEnableSubmit(handleEnableMfa)}>
                  <div className="mb-4">
                    <label htmlFor="verificationCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Verification Code
                    </label>
                    <input
                      id="verificationCode"
                      type="text"
                      inputMode="numeric"
                      autoComplete="one-time-code"
                      {...registerEnable('verificationCode', { 
                        required: 'Verification code is required',
                        pattern: {
                          value: /^\d{6}$/,
                          message: 'Code must be 6 digits'
                        }
                      })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="123456"
                    />
                    {enableErrors.verificationCode && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">{enableErrors.verificationCode.message}</p>
                    )}
                  </div>
                  
                  <div className="flex justify-end space-x-3">
                    <button
                      type="button"
                      onClick={handleCancelSetup}
                      className="px-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={enableMfa.isPending}
                      className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {enableMfa.isPending ? 'Verifying...' : 'Verify and Enable'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          ) : null}
        </div>
      )}
      
      {/* MFA Disable Form */}
      {showDisable && (
        <div className="mt-6 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Disable two-factor authentication
          </h4>
          
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            To disable two-factor authentication, please enter your password and a verification code from your authenticator app.
          </p>
          
          {disableMfa.isError && (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-800 dark:text-red-400 text-sm mb-4">
              {disableMfa.error instanceof Error 
                ? disableMfa.error.message 
                : 'Failed to disable two-factor authentication. Please try again.'}
            </div>
          )}
          
          <form onSubmit={handleDisableSubmit(handleDisableMfa)}>
            <div className="mb-4">
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Password
              </label>
              <input
                id="password"
                type="password"
                {...registerDisable('password', { 
                  required: 'Password is required'
                })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
              {disableErrors.password && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{disableErrors.password.message}</p>
              )}
            </div>
            
            <div className="mb-4">
              <label htmlFor="disableVerificationCode" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Verification Code
              </label>
              <input
                id="disableVerificationCode"
                type="text"
                inputMode="numeric"
                autoComplete="one-time-code"
                {...registerDisable('verificationCode', { 
                  required: 'Verification code is required',
                  pattern: {
                    value: /^\d{6}$/,
                    message: 'Code must be 6 digits'
                  }
                })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                placeholder="123456"
              />
              {disableErrors.verificationCode && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{disableErrors.verificationCode.message}</p>
              )}
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleCancelDisable}
                className="px-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={disableMfa.isPending}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {disableMfa.isPending ? 'Disabling...' : 'Disable Two-Factor Authentication'}
              </button>
            </div>
          </form>
        </div>
      )}
      
      {/* MFA Information */}
      {!showSetup && !showDisable && (
        <div className="text-sm text-gray-600 dark:text-gray-400">
          <p className="mb-2">Two-factor authentication adds an extra layer of security to your account by requiring:</p>
          <ul className="list-disc pl-5 space-y-1 mb-4">
            <li>Your password</li>
            <li>A verification code from your mobile device</li>
          </ul>
          <p>
            Once enabled, you'll need to enter a verification code from your authenticator app whenever you sign in.
          </p>
        </div>
      )}
    </div>
  );
}
