---
title: CallSaver.app Deployment Blueprint
description: Comprehensive deployment plan for Railway (backend) and Vercel (frontend)
date: 2025-04-28
status: Required
---

# CallSaver.app Deployment Blueprint

## Overview

This document outlines the deployment strategy for the CallSaver.app application. The backend will be deployed on Railway, and the frontend will be deployed on Vercel. This blueprint provides step-by-step instructions for setting up CI/CD pipelines, configuring environments, and monitoring the deployed application.

## Architecture Overview

```
┌─────────────────┐       ┌─────────────────┐       ┌─────────────────┐
│   Next.js       │       │   Express API   │       │   PostgreSQL    │
│   Frontend      │───────│   Backend       │───────│   Database      │
│   (Vercel)      │       │   (Railway)     │       │   (Railway)     │
└─────────────────┘       └─────────────────┘       └─────────────────┘
                                  │
                                  │
                          ┌───────┴───────┐
                          │   MongoDB     │
                          │   (Atlas)     │
                          └───────────────┘
                                  │
                                  │
                          ┌───────┴───────┐
                          │   Redis       │
                          │   (Upstash)   │
                          └───────────────┘
```

## 1. Backend Deployment (Railway)

### 1.1 Prerequisites

- Railway account with billing set up
- GitHub repository connected to Railway
- PostgreSQL database add-on
- Redis add-on (via Upstash integration)

### 1.2 Railway Project Setup

1. Create a new project in Railway
2. Connect the GitHub repository
3. Add a PostgreSQL service
4. Add a Redis service via Upstash integration
5. Set up the Node.js service for the backend:
   - Root directory: `/back/backend`
   - Start command: `node server.js`
   - Health check path: `/api/health`

### 1.3 Environment Configuration

Configure the following environment variables in the Railway project settings:

```
# Database Configuration
DATABASE_URL=postgresql://${PGUSER}:${PGPASSWORD}@${PGHOST}:${PGPORT}/${PGDATABASE}
DIRECT_URL=postgresql://${PGUSER}:${PGPASSWORD}@${PGHOST}:${PGPORT}/${PGDATABASE}

# Redis Configuration
REDIS_URL=${REDIS_URL}

# Application Settings
NODE_ENV=production
PORT=3000
API_URL=https://api.callsaver.app
FRONTEND_URL=https://callsaver.app
JWT_SECRET=${JWT_SECRET}
JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
JWT_EXPIRY=24h
JWT_REFRESH_EXPIRY=7d

# Twilio Configuration
TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
TWILIO_PHONE_NUMBER=${TWILIO_PHONE_NUMBER}
TWILIO_WEBHOOK_BASE_URL=https://api.callsaver.app/webhooks/twilio

# OpenAI Configuration
OPENAI_API_KEY=${OPENAI_API_KEY}
OPENAI_ORGANIZATION=${OPENAI_ORGANIZATION}

# Stripe Configuration
STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}

# Pinecone Configuration
PINECONE_API_KEY=${PINECONE_API_KEY}
PINECONE_ENVIRONMENT=${PINECONE_ENVIRONMENT}
PINECONE_INDEX=${PINECONE_INDEX}

# Supabase Configuration
SUPABASE_URL=${SUPABASE_URL}
SUPABASE_KEY=${SUPABASE_KEY}
SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
```

### 1.4 Database Migrations

Set up a deploy hook to run Prisma migrations during the deployment process:

1. Add the following to the Railway project settings:
   - Build command: `npx prisma migrate deploy`
   - Deploy command: `node server.js`

2. Configure a deployment pipeline in your CI/CD workflow:

```yml
steps:
  - name: Install dependencies
    command: npm ci
  
  - name: Run database migrations
    command: npx prisma migrate deploy
  
  - name: Start application
    command: node server.js
```

### 1.5 Custom Domain Configuration

1. Set up a custom domain for the API (e.g., `api.callsaver.app`)
2. Configure SSL certificate using Railway's automatic SSL provisioning
3. Update DNS records:
   - Type: CNAME
   - Name: api
   - Value: [Railway-provided domain]
   - TTL: 3600

## 2. Frontend Deployment (Vercel)

### 2.1 Prerequisites

- Vercel account with billing set up
- GitHub repository connected to Vercel

### 2.2 Vercel Project Setup

1. Import the GitHub repository into Vercel
2. Configure the project settings:
   - Framework preset: Next.js
   - Root directory: `/front/mainpage`
   - Build command: `npm run build`
   - Output directory: `.next`
   - Development command: `npm run dev`

### 2.3 Environment Configuration

Configure the following environment variables in the Vercel project settings:

```
# API Configuration
NEXT_PUBLIC_API_URL=https://api.callsaver.app
NEXT_PUBLIC_WEBSOCKET_URL=wss://api.callsaver.app

# Authentication
NEXT_PUBLIC_SUPABASE_URL=${SUPABASE_URL}
NEXT_PUBLIC_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
NEXT_AUTH_SECRET=${NEXT_AUTH_SECRET}

# Analytics
NEXT_PUBLIC_ANALYTICS_ID=${ANALYTICS_ID}

# Feature Flags
NEXT_PUBLIC_FEATURE_AI_ASSISTANT=true
NEXT_PUBLIC_FEATURE_ANALYTICS=true
NEXT_PUBLIC_FEATURE_ADVANCED_AUTOMATION=false

# External Services
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}
```

### 2.4 Custom Domain Configuration

1. Set up a custom domain for the frontend (e.g., `callsaver.app`)
2. Configure SSL certificate using Vercel's automatic SSL provisioning
3. Update DNS records:
   - Type: A
   - Name: @
   - Value: [Vercel-provided IP]
   - TTL: 3600
   - Type: CNAME
   - Name: www
   - Value: cname.vercel-dns.com
   - TTL: 3600

## 3. CI/CD Pipeline

### 3.1 GitHub Actions Workflow

Create a GitHub Actions workflow file at `.github/workflows/main.yml`:

```yml
name: CallSaver.app CI/CD

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test-backend:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: testdb
        ports:
          - 5432:5432
        options: --health-cmd pg_isready --health-interval 10s --health-timeout 5s --health-retries 5
      
      redis:
        image: redis:alpine
        ports:
          - 6379:6379
        options: --health-cmd "redis-cli ping" --health-interval 10s --health-timeout 5s --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'back/backend/package-lock.json'
      
      - name: Install dependencies
        run: npm ci
        working-directory: ./back/backend
      
      - name: Run Prisma migrations
        run: npx prisma migrate dev --name init
        working-directory: ./back/backend
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/testdb
      
      - name: Run tests
        run: npm test
        working-directory: ./back/backend
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/testdb
          REDIS_URL: redis://localhost:6379
          JWT_SECRET: test-secret
          JWT_REFRESH_SECRET: test-refresh-secret
          TWILIO_ACCOUNT_SID: ${{ secrets.TEST_TWILIO_ACCOUNT_SID }}
          TWILIO_AUTH_TOKEN: ${{ secrets.TEST_TWILIO_AUTH_TOKEN }}
  
  test-frontend:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'front/mainpage/package-lock.json'
      
      - name: Install dependencies
        run: npm ci
        working-directory: ./front/mainpage
      
      - name: Run linting
        run: npm run lint
        working-directory: ./front/mainpage
      
      - name: Run tests
        run: npm test
        working-directory: ./front/mainpage
  
  deploy:
    needs: [test-backend, test-frontend]
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Install Railway CLI
        run: npm i -g @railway/cli
      
      - name: Deploy Backend to Railway
        run: railway up
        working-directory: ./back/backend
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
      
      # Vercel deployment happens automatically through the Vercel GitHub integration
```

### 3.2 Branch Protection Rules

Configure GitHub branch protection rules for the `main` branch:

1. Require pull request reviews before merging
2. Require status checks to pass before merging
3. Require branches to be up to date before merging
4. Include administrators in these restrictions

## 4. Monitoring and Error Tracking

### 4.1 Application Monitoring

Configure monitoring using Datadog or New Relic:

1. Add the monitoring agent to the backend service:
   ```js
   // Add at the top of server.js
   if (process.env.NODE_ENV === 'production') {
     require('dd-trace').init(); // For Datadog
   }
   ```

2. Set up frontend error monitoring (e.g., Sentry):
   ```js
   // In front/mainpage/app/providers.js
   import * as Sentry from '@sentry/nextjs';
   
   if (process.env.NODE_ENV === 'production') {
     Sentry.init({
       dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
       tracesSampleRate: 0.2,
     });
   }
   ```

### 4.2 Log Management

1. Configure structured logging in the backend:
   ```js
   // In back/backend/lib/logger.js
   const winston = require('winston');
   
   const logger = winston.createLogger({
     level: process.env.LOG_LEVEL || 'info',
     format: winston.format.combine(
       winston.format.timestamp(),
       winston.format.json()
     ),
     transports: [
       new winston.transports.Console(),
       // Add additional transports for production (e.g., Papertrail, Logtail)
     ]
   });
   
   module.exports = logger;
   ```

2. Set up log draining in Railway to forward logs to a central log management system

### 4.3 Alerting

Set up alerting via Datadog, PagerDuty, or similar services for:

1. Error rate spikes
2. API latency beyond thresholds
3. Database connection issues
4. High memory/CPU usage
5. Failed deploys
6. Failed payments or critical business functions

## 5. Backup and Disaster Recovery

### 5.1 Database Backups

1. Configure automated PostgreSQL backups:
   - Daily full backups
   - Point-in-time recovery
   - Backup retention: 30 days

2. Enable MongoDB Atlas backups:
   - Daily snapshots
   - Retention: 7 days

### 5.2 Disaster Recovery Plan

1. Document recovery procedures for:
   - Database corruption
   - Application failure
   - Infrastructure outage
   - Data breach

2. Test recovery procedures quarterly

### 5.3 Data Retention and Compliance

1. Implement data retention policies:
   - Call recordings: 90 days
   - Call logs: 1 year
   - User data: Duration of account + 30 days
   - System logs: 90 days

## 6. Rollback Procedures

### 6.1 Backend Rollback

1. Railway rollback command:
   ```bash
   railway rollback --service backend
   ```

2. Database rollback with Prisma:
   ```bash
   npx prisma migrate resolve --rolled-back [migration_name]
   ```

### 6.2 Frontend Rollback

1. Vercel rollback via dashboard or CLI:
   ```bash
   vercel rollback [deployment_id]
   ```

## 7. Security Considerations

### 7.1 Security Headers

Configure security headers in:

1. Frontend (Next.js config):
   ```js
   // In front/mainpage/next.config.js
   const securityHeaders = [
     {
       key: 'X-XSS-Protection',
       value: '1; mode=block',
     },
     {
       key: 'X-Content-Type-Options',
       value: 'nosniff',
     },
     {
       key: 'Content-Security-Policy',
       value: "default-src 'self'; script-src 'self' 'unsafe-inline' https://js.stripe.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https://*.stripe.com",
     },
     // ...more headers
   ];
   
   module.exports = {
     // ...other config
     async headers() {
       return [
         {
           source: '/(.*)',
           headers: securityHeaders,
         },
       ];
     },
   };
   ```

2. Backend (Express.js middleware):
   ```js
   const helmet = require('helmet');
   app.use(helmet());
   ```

### 7.2 Environment Variable Protection

Ensure that all sensitive environment variables are:
1. Encrypted at rest in Railway and Vercel
2. Not exposed to frontend code unless necessary
3. Rotated regularly for production environments

## 8. Post-Deployment Verification

### 8.1 Deployment Checklist

Run through the following checklist after each deployment:

1. Health endpoint returns 200 OK
2. Authentication flows work properly
3. Critical paths function (number purchase, call management, SMS)
4. Database migrations completed successfully
5. Monitoring systems are reporting data
6. No unexpected errors in logs
7. Frontend loads correctly across devices

### 8.2 Smoke Test Script

Create an automated smoke test script to verify critical paths after deployment:

```js
// In back/backend/scripts/smoke-test.js
const axios = require('axios');
const assert = require('assert');

async function runSmokeTests() {
  const baseUrl = process.env.API_URL || 'https://api.callsaver.app';
  
  // Test API health
  const healthResponse = await axios.get(`${baseUrl}/api/health`);
  assert.strictEqual(healthResponse.status, 200);
  
  // More critical path tests...
  
  console.log('✅ All smoke tests passed!');
}

runSmokeTests().catch(console.error);
```

## 9. Scaling Considerations

### 9.1 Railway Scaling

Configure auto-scaling in Railway:
1. CPU-based scaling: Scale when CPU > 70% for 5 minutes
2. Memory-based scaling: Scale when memory > 80% for 5 minutes
3. Initial instances: 2 (for high availability)
4. Maximum instances: 10

### 9.2 Database Scaling

1. PostgreSQL:
   - Start with Railway's Standard tier (4GB RAM, 2 CPU)
   - Monitor connection count and query performance
   - Configure connection pooling with PgBouncer

2. MongoDB Atlas:
   - Start with M10 cluster (2GB RAM, 2 vCPU)
   - Enable auto-scaling
   - Configure sharding when data exceeds 100GB

### 9.3 Redis Scaling

1. Upstash Redis:
   - Start with Pro tier (1GB)
   - Monitor memory usage and hit rate
   - Enable persistence and replication

## Conclusion

This deployment blueprint provides a comprehensive guide for deploying the CallSaver.app application to Railway (backend) and Vercel (frontend). Follow these instructions to ensure a smooth, secure, and scalable deployment process with proper monitoring, disaster recovery, and rollback procedures in place.
