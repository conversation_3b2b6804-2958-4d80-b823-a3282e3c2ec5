import { PhoneNumber } from './AutomationLayout';
import NumberListItem from './NumberListItem';

interface NumberListProps {
  numbers: PhoneNumber[];
  selectedNumberId: string | null;
  onSelectNumber: (numberId: string) => void;
}

export default function NumberList({
  numbers,
  selectedNumberId,
  onSelectNumber,
}: NumberListProps) {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white">
          Your Phone Numbers
        </h2>
        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
          Select a number to configure
        </p>
      </div>
      <ul className="divide-y divide-gray-200 dark:divide-gray-700">
        {numbers.map((number) => (
          <NumberListItem
            key={number.id}
            number={number}
            isSelected={number.id === selectedNumberId}
            onSelect={() => onSelectNumber(number.id)}
          />
        ))}
      </ul>
    </div>
  );
}
