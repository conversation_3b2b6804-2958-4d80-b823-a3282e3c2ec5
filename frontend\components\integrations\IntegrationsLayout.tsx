'use client';

import { useState } from 'react';
import IntegrationCard from './IntegrationCard';
import IntegrationDetailModal from './IntegrationDetailModal';
import IntegrationsSkeleton from './IntegrationsSkeleton';
import ErrorMessage from '../shared/ErrorMessage';
import { useIntegrations, Integration } from '../../hooks/useIntegrations';
import {
  CalendarIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  BuildingOfficeIcon,
  CubeIcon,
  UserGroupIcon,
  VideoCameraIcon,
  ShoppingBagIcon
} from '@heroicons/react/24/outline';

// Add icons to the integrations
const addIconsToIntegrations = (integrations: Integration[]): Integration[] => {
  return integrations.map(integration => {
    let icon;
    switch (integration.provider.toLowerCase()) {
      case 'google':
      case 'microsoft':
        icon = <CalendarIcon className="h-6 w-6" />;
        break;
      case 'slack':
        icon = <ChatBubbleLeftRightIcon className="h-6 w-6" />;
        break;
      case 'salesforce':
        icon = <BuildingOfficeIcon className="h-6 w-6" />;
        break;
      case 'zapier':
        icon = <CubeIcon className="h-6 w-6" />;
        break;
      case 'hubspot':
        icon = <UserGroupIcon className="h-6 w-6" />;
        break;
      case 'zoom':
        icon = <VideoCameraIcon className="h-6 w-6" />;
        break;
      case 'shopify':
        icon = <ShoppingBagIcon className="h-6 w-6" />;
        break;
      default:
        icon = <CubeIcon className="h-6 w-6" />;
    }
    return { ...integration, icon };
  });
};

export default function IntegrationsLayout() {
  const [selectedIntegrationId, setSelectedIntegrationId] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Use our custom hook to fetch integrations
  const { integrationsQuery } = useIntegrations();
  const { data: rawIntegrations, isLoading, error } = integrationsQuery;

  // Add icons to the integrations
  const integrations = rawIntegrations ? addIconsToIntegrations(rawIntegrations) : undefined;

  // Handle integration selection
  const handleSelectIntegration = (integrationId: string) => {
    setSelectedIntegrationId(integrationId);
    setIsModalOpen(true);
  };

  // Get selected integration
  const selectedIntegration = integrations?.find(
    (integration) => integration.id === selectedIntegrationId
  );

  // If loading, show skeleton
  if (isLoading) {
    return <IntegrationsSkeleton />;
  }

  // If error, show error message
  if (error) {
    return (
      <ErrorMessage
        title="Failed to load integrations"
        message="We couldn't load your integrations. Please try again later."
        actionText="Try Again"
        onAction={() => window.location.reload()}
      />
    );
  }

  // Group integrations by category
  const groupedIntegrations = integrations?.reduce(
    (acc, integration) => {
      if (!acc[integration.category]) {
        acc[integration.category] = [];
      }
      acc[integration.category].push(integration);
      return acc;
    },
    {} as Record<string, Integration[]>
  );

  // Category titles
  const categoryTitles: Record<string, string> = {
    calendar: 'Calendar',
    communication: 'Communication',
    crm: 'CRM & Sales',
    automation: 'Automation',
    other: 'Other',
  };

  return (
    <div className="flex flex-col lg:flex-row h-full w-full gap-6">
      {/* Integrations grid */}
      <div className="w-full lg:w-2/3">
        <div className="space-y-8">
          {Object.entries(groupedIntegrations || {}).map(([category, integrations]) => (
            <div key={category}>
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {categoryTitles[category] || category}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {integrations.map((integration) => (
                  <IntegrationCard
                    key={integration.id}
                    integration={integration}
                    isSelected={integration.id === selectedIntegrationId}
                    onSelect={() => handleSelectIntegration(integration.id)}
                  />
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Modal for integration details */}
      {selectedIntegration && (
        <IntegrationDetailModal
          integration={selectedIntegration}
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />
      )}
    </div>
  );
}
