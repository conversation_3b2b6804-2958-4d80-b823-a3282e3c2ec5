'use client';

import { useState, useEffect } from 'react';
import { NumbersAPI, BillingAPI } from '../utils/api';

// List of countries for the dropdown
const COUNTRIES = [
  { code: 'US', name: 'United States' },
  { code: 'CA', name: 'Canada' },
  { code: 'DE', name: 'Germany' },
  { code: 'GB', name: 'United Kingdom' },
  { code: 'AU', name: 'Australia' },
  { code: 'FR', name: 'France' },
  { code: 'ES', name: 'Spain' },
  { code: 'IT', name: 'Italy' },
  { code: 'BR', name: 'Brazil' },
  { code: 'MX', name: 'Mexico' },
];

export default function BuyNumberSection() {
  // State for form inputs
  const [countryCode, setCountryCode] = useState('US');
  const [areaCode, setAreaCode] = useState('');
  const [capabilities, setCapabilities] = useState({
    voice: true,
    sms: true,
    fax: false,
  });

  // State for API data and UI state
  const [phoneNumbers, setPhoneNumbers] = useState([]);
  const [selectedNumber, setSelectedNumber] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [purchaseSuccess, setPurchaseSuccess] = useState(false);
  const [purchasedNumberDetails, setPurchasedNumberDetails] = useState(null);
  const [userTokens, setUserTokens] = useState(null);
  const [debugInfo, setDebugInfo] = useState(null);

  // Fetch the user's available tokens/credits on component mount
  useEffect(() => {
    async function fetchUserTokens() {
      try {
        // Check if demo user
        const demoUser = localStorage.getItem('callsaver_demo_user');
        if (demoUser) {
          // Demo users get 5 tokens
          setUserTokens(5);
          return;
        }

        // Real users: fetch from API
        const creditsData = await BillingAPI.getCredits();
        console.log('Credits data from API:', creditsData);

        // Fix: correctly access the credits from the API response structure
        // The API returns { success: true, data: { credits: N } }
        const credits = creditsData.data?.credits || creditsData.credits || 0;
        setUserTokens(credits);
      } catch (error) {
        console.error('Error fetching user tokens:', error);
        // Set to 0 as fallback
        setUserTokens(0);
      }
    }

    fetchUserTokens();
  }, []);

  // Handle capability checkbox changes
  const handleCapabilityChange = (capability) => {
    setCapabilities(prev => ({
      ...prev,
      [capability]: !prev[capability]
    }));
  };

  // Handle country selection
  const handleCountryChange = (e) => {
    setCountryCode(e.target.value);
  };

  // Handle area code input
  const handleAreaCodeChange = (e) => {
    setAreaCode(e.target.value.replace(/[^0-9]/g, ''));
  };

  // Fetch available phone numbers from the API
  const fetchPhoneNumbers = async () => {
    setIsLoading(true);
    setError('');
    setPhoneNumbers([]);
    setSelectedNumber(null);
    setDebugInfo(null);

    try {
      // Prepare search parameters
      const searchParams = {
        countryCode,
        areaCode: areaCode || undefined,
        voiceEnabled: capabilities.voice,
        smsEnabled: capabilities.sms,
        limit: 20
      };

      // Log search parameters
      console.log('Searching for numbers with params:', searchParams);
      setDebugInfo({ requestParams: searchParams });

      // Call our API utility
      const response = await NumbersAPI.searchAvailableNumbers(searchParams);
      console.log('Raw API response in Component:', response);

      // Update debug info
      setDebugInfo(prev => ({
        ...prev,
        response: response
      }));

      // Extract numbers from the response - handle all possible response formats
      let numbersData = [];
      if (response.success && response.data && Array.isArray(response.data.numbers)) {
        // New format: { success: true, data: { numbers: [...] } }
        numbersData = response.data.numbers;
      } else if (response.success && Array.isArray(response.data)) {
        // Alternative format: { success: true, data: [...] }
        numbersData = response.data;
      } else if (Array.isArray(response)) {
        // Direct array format (old API): [...]
        numbersData = response;
      } else if (response.data && Array.isArray(response.data)) {
        // Another format: { data: [...] }
        numbersData = response.data;
      }

      // Handle empty results
      if (numbersData.length === 0) {
        const countryName = COUNTRIES.find(c => c.code === countryCode)?.name || countryCode;

        let emptyCriteria = [];
        if (capabilities.voice) emptyCriteria.push('voice');
        if (capabilities.sms) emptyCriteria.push('SMS');
        if (capabilities.fax) emptyCriteria.push('fax');

        const capabilitiesText = emptyCriteria.length > 0
          ? `with ${emptyCriteria.join(' and ')} capabilities`
          : '';

        const areaCodeText = areaCode
          ? `in area code ${areaCode}`
          : '';

        setError(`No phone numbers available in ${countryName} ${areaCodeText} ${capabilitiesText}. Try different search criteria.`);
        setIsLoading(false);
        return;
      }

      // Transform the data for our UI
      const formattedNumbers = numbersData.map((number) => ({
        id: number.sid || Math.random().toString(36).substr(2, 9),
        phoneNumber: number.phoneNumber,
        formattedNumber: number.friendlyName,
        location: `${number.locality || ''} ${number.region || ''}`.trim() || number.isoCountry,
        country: number.isoCountry,
        type: number.numberType || 'local',
        capabilities: {
          voice: number.capabilities?.voice || false,
          sms: number.capabilities?.sms || false,
          fax: number.capabilities?.fax || false,
        },
        price: '1 Token',
      }));

      console.log(`Successfully loaded ${formattedNumbers.length} phone numbers`);
      setPhoneNumbers(formattedNumbers);
    } catch (err) {
      console.error('Error fetching phone numbers:', err);

      setError(err.message || 'Failed to fetch phone numbers. Please try again.');

      // Update debug info with caught error
      setDebugInfo(prev => ({
        ...prev,
        error: err.message
      }));
    } finally {
      setIsLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    fetchPhoneNumbers();
  };

  // Handle phone number selection
  const handleSelectNumber = (number) => {
    setSelectedNumber(number);
  };

  // Purchase a phone number
  const handlePurchaseNumber = async () => {
    if (!selectedNumber) return;

    console.log('Buy button clicked. Attempting purchase for:', selectedNumber);

    // Reset states
    setPurchasedNumberDetails(null);
    setPurchaseSuccess(false);
    setError('');
    setIsLoading(true);

    try {
      // Check if user has enough tokens
      if ((userTokens || 0) < 1) {
        setError('You don\'t have enough tokens to purchase this number. Please add tokens to your account.');
        setIsLoading(false);
        return;
      }

      // Demo user check
      const demoUser = localStorage.getItem('callsaver_demo_user');
      if (demoUser) {
        // Simulate purchase for demo users
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Update tokens (simulate deduction)
        setUserTokens(prev => Math.max(0, (prev || 0) - 1));

        // Set success state with demo data
        const demoNumberDetails = {
          id: `pn_demo_${Math.random().toString(36).substr(2, 8)}`,
          phoneNumber: selectedNumber.phoneNumber,
          number: selectedNumber.phoneNumber,
          formattedNumber: selectedNumber.formattedNumber,
          friendlyName: selectedNumber.formattedNumber,
          sid: `PN${Math.random().toString(36).substr(2, 8)}`,
          dateCreated: new Date().toISOString(),
          createdAt: new Date().toISOString(),
          status: 'active',
          isActive: true,
          // Ensure capabilities is properly defined
          capabilities: {
            voice: selectedNumber.capabilities?.voice || false,
            sms: selectedNumber.capabilities?.sms || false,
            fax: selectedNumber.capabilities?.fax || false
          },
          countryCode: selectedNumber.country || 'US',
          region: selectedNumber.location || 'Unknown',
          locality: ''
        };

        console.log('Demo purchase data:', demoNumberDetails);

        // Store in localStorage for persistence
        const existingNumbers = JSON.parse(localStorage.getItem('callsaver_purchased_numbers') || '[]');
        existingNumbers.push(demoNumberDetails);
        localStorage.setItem('callsaver_purchased_numbers', JSON.stringify(existingNumbers));

        setPurchaseSuccess(true);
        setPurchasedNumberDetails(demoNumberDetails);

        setIsLoading(false);
        return;
      }

      // For real users, call the API
      const purchaseData = await NumbersAPI.purchaseNumber({
        phoneNumber: selectedNumber.phoneNumber,
        capabilities: selectedNumber.capabilities,
        countryCode: selectedNumber.country,
        region: selectedNumber.location.split(' ')[0] || '',
        locality: selectedNumber.location.split(' ')[1] || ''
      });

      console.log('Purchase API response:', purchaseData);

      // Update tokens
      setUserTokens(prev => Math.max(0, (prev || 0) - 1));

      // Process the response data
      let processedData = {};
      
      if (purchaseData.success && purchaseData.data) {
        processedData = purchaseData.data;
      } else if (typeof purchaseData === 'object') {
        processedData = purchaseData;
      }

      console.log('Raw API response:', processedData);

      // Create a properly formatted object for display
      const formattedData = {
        id: processedData.id || `pn_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
        // Handle phoneNumber which might be a string or an object
        phoneNumber: typeof processedData.phoneNumber === 'string' ?
          processedData.phoneNumber :
          (processedData.phoneNumber?.number || processedData.number || selectedNumber.phoneNumber),

        number: processedData.number || processedData.phoneNumber || selectedNumber.phoneNumber,

        // Handle country/location information
        countryCode: processedData.countryCode ||
                    processedData.phoneNumber?.countryCode ||
                    selectedNumber.country || 'US',
                    
        region: processedData.region || selectedNumber.location || '',
        
        // Format the friendly name if not provided
        friendlyName: processedData.friendlyName || selectedNumber.formattedNumber,

        // Ensure the capabilities property exists
        capabilities: processedData.capabilities ||
                     processedData.twilioDetails?.capabilities ||
                     {
                       voice: selectedNumber.capabilities?.voice || false,
                       sms: selectedNumber.capabilities?.sms || false,
                       fax: selectedNumber.capabilities?.fax || false
                     },
                     
        // Ensure other important fields exist
        dateCreated: processedData.dateCreated || processedData.createdAt || new Date().toISOString(),
        createdAt: processedData.createdAt || processedData.dateCreated || new Date().toISOString(),
        status: processedData.status || 'active',
        isActive: processedData.isActive !== undefined ? processedData.isActive : true
      };

      console.log('Formatted data for display:', formattedData);

      // Store in localStorage for persistence
      const existingNumbers = JSON.parse(localStorage.getItem('callsaver_purchased_numbers') || '[]');
      existingNumbers.push(formattedData);
      localStorage.setItem('callsaver_purchased_numbers', JSON.stringify(existingNumbers));

      // Set success state
      setPurchaseSuccess(true);
      setPurchasedNumberDetails(formattedData);

    } catch (err) {
      console.error('Error purchasing phone number:', err);
      setError(err.message || 'Failed to purchase the phone number. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Reset UI after purchase
  const handleReset = () => {
    setSelectedNumber(null);
    setPurchaseSuccess(false);
    setPurchasedNumberDetails(null);
    setPhoneNumbers([]);
  };

  return (
    <div className="bg-gray-900/70 backdrop-blur-lg rounded-2xl border border-purple-500/20 shadow-xl p-6 md:p-8 mb-8">
      {/* Section header with token display */}
      <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-white">Buy a Phone Number</h2>
          <p className="text-gray-300 mt-2">
            Search and purchase a phone number for your Callsaver account.
          </p>
        </div>
        <div className="bg-gray-800 rounded-full px-4 py-2 flex items-center self-start">
          <span className="text-yellow-400 mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092c-.464.058-.896.14-1.275.287-.373.146-.737.345-1.052.567-.314.222-.558.474-.734.79-.174.314-.292.682-.292 1.064 0 .84.479 1.598 1.221 2.096.742.498 1.72.798 2.632.798.353 0 .704-.052 1.04-.152.336-.1.663-.246.964-.419.302-.173.575-.376.813-.617.239-.241.431-.518.568-.82.138-.302.208-.66.208-1.021 0-.382-.118-.75-.293-1.064a2.76 2.76 0 00-.735-.79 3.75 3.75 0 00-1.051-.567 4.659 4.659 0 00-1.275-.287V5zM11 18.834A8.001 8.001 0 0110 1.166v17.668z" clipRule="evenodd" />
            </svg>
          </span>
          <span className="text-white font-medium">
            {userTokens === null ? 'Fetching tokens...' : `${userTokens} Tokens`}
          </span>
        </div>
      </div>

      {/* Search form */}
      <div className="bg-gray-800/50 rounded-lg p-5 border border-gray-700 mb-8">
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Country selection */}
          <div>
            <label htmlFor="country" className="block text-sm font-medium text-gray-400 mb-1">
              Country
            </label>
            <select
              id="country"
              value={countryCode}
              onChange={handleCountryChange}
              className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 shadow-sm appearance-none cursor-pointer hover:border-gray-500 transition-colors duration-200"
            >
              {COUNTRIES.map((country) => (
                <option key={country.code} value={country.code}>
                  {country.name} ({country.code})
                </option>
              ))}
            </select>
          </div>

          {/* Area code input - only show for US and CA */}
          {(countryCode === 'US' || countryCode === 'CA') && (
            <div>
              <label htmlFor="areaCode" className="block text-sm font-medium text-gray-400 mb-1">
                Area Code (Optional)
              </label>
              <input
                type="text"
                id="areaCode"
                value={areaCode}
                onChange={handleAreaCodeChange}
                maxLength={3}
                placeholder="e.g. 415"
                className="w-full px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          )}

          {/* Capabilities selection */}
          <div>
            <p className="block text-sm font-medium text-gray-400 mb-2">
              Capabilities
            </p>
            <div className="flex flex-wrap gap-4">
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  checked={capabilities.voice}
                  onChange={() => handleCapabilityChange('voice')}
                  className="form-checkbox h-5 w-5 text-purple-600 rounded focus:ring-2 focus:ring-purple-500"
                />
                <span className="ml-2 text-gray-300">Voice</span>
              </label>
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  checked={capabilities.sms}
                  onChange={() => handleCapabilityChange('sms')}
                  className="form-checkbox h-5 w-5 text-purple-600 rounded focus:ring-2 focus:ring-purple-500"
                />
                <span className="ml-2 text-gray-300">SMS</span>
              </label>
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  checked={capabilities.fax}
                  onChange={() => handleCapabilityChange('fax')}
                  className="form-checkbox h-5 w-5 text-purple-600 rounded focus:ring-2 focus:ring-purple-500"
                />
                <span className="ml-2 text-gray-300">Fax</span>
              </label>
            </div>
          </div>

          {/* Search button */}
          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="w-full md:w-auto px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition duration-200 flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Searching...
                </>
              ) : (
                'Search Numbers'
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Results section */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-white mb-4">Available Numbers</h3>

        {/* Loading state */}
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin h-8 w-8 border-4 border-purple-500 border-t-transparent rounded-full"></div>
            <p className="ml-3 text-gray-400">Searching for available numbers...</p>
          </div>
        )}

        {/* Error state */}
        {error && !isLoading && (
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 text-center flex flex-col items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-red-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-red-200 mb-3">{error}</p>
            <button
              onClick={() => setError('')}
              className="px-4 py-2 bg-red-800/30 hover:bg-red-800/50 text-white text-sm rounded-md transition-colors duration-200"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Empty state - when no search has been performed yet */}
        {!isLoading && !error && phoneNumbers.length === 0 && (
          <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-8 text-center">
            <div className="flex justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            </div>
            <h4 className="text-lg font-medium text-white mb-2">No Phone Numbers Yet</h4>
            <p className="text-gray-400 mb-4">
              Search for available phone numbers using the form above.
            </p>
            <div className="text-gray-500 text-sm max-w-md mx-auto">
              <p className="mb-2">Tips:</p>
              <ul className="list-disc text-left pl-8">
                <li>Choose your country from the dropdown</li>
                <li>For US/Canada numbers, you can specify an area code</li>
                <li>Select capabilities (Voice, SMS, Fax) as needed</li>
                <li>Click &ldquo;Search Numbers&rdquo; to see what&apos;s available</li>
              </ul>
            </div>
          </div>
        )}

        {/* Phone number grid */}
        {!isLoading && !error && phoneNumbers.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {phoneNumbers.map((number) => (
              <div
                key={number.id}
                className={`
                  bg-gray-800/70 rounded-lg p-4 border transition-all duration-200
                  ${selectedNumber?.id === number.id
                    ? 'border-purple-500 ring-2 ring-purple-500/50'
                    : 'border-gray-700 hover:border-purple-400/30 hover:bg-gray-800/90'}
                `}
              >
                <div className="text-lg font-medium text-white mb-1">{number.formattedNumber}</div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">{number.location || number.country}</span>
                  <span className="text-gray-400">{number.type}</span>
                </div>
                <div className="flex flex-wrap gap-1 mt-2">
                  {number.capabilities.voice && (
                    <span className="px-2 py-0.5 bg-blue-500/20 text-blue-300 text-xs rounded">Voice</span>
                  )}
                  {number.capabilities.sms && (
                    <span className="px-2 py-0.5 bg-green-500/20 text-green-300 text-xs rounded">SMS</span>
                  )}
                  {number.capabilities.fax && (
                    <span className="px-2 py-0.5 bg-yellow-500/20 text-yellow-300 text-xs rounded">Fax</span>
                  )}
                </div>
                <div className="mt-3 flex justify-between items-center">
                  <span className="text-yellow-300 font-medium">{number.price}</span>
                  <button
                    className="px-3 py-1 bg-purple-600 hover:bg-purple-700 text-white rounded text-xs font-medium shadow-sm hover:shadow-md transition-all duration-200"
                    onClick={() => handleSelectNumber(number)}
                  >
                    Buy (1 Token)
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Confirmation area - this would be shown when a number is selected */}
      {selectedNumber && !purchaseSuccess && (
        <div className="bg-gray-800/80 rounded-lg p-5 border border-purple-500/30">
          <h3 className="text-lg font-semibold text-white mb-3">Confirm Your Purchase</h3>

          <div className="flex flex-col md:flex-row justify-between mb-4">
            <div>
              <p className="text-gray-400 text-sm">Selected Number:</p>
              <p className="text-white text-xl font-medium">{selectedNumber.formattedNumber}</p>
              <p className="text-gray-400 text-sm mt-1">{selectedNumber.location || selectedNumber.country} • {selectedNumber.type}</p>
              <div className="flex flex-wrap gap-1 mt-2">
                {selectedNumber.capabilities.voice && (
                  <span className="px-2 py-0.5 bg-blue-500/20 text-blue-300 text-xs rounded">Voice</span>
                )}
                {selectedNumber.capabilities.sms && (
                  <span className="px-2 py-0.5 bg-green-500/20 text-green-300 text-xs rounded">SMS</span>
                )}
                {selectedNumber.capabilities.fax && (
                  <span className="px-2 py-0.5 bg-yellow-500/20 text-yellow-300 text-xs rounded">Fax</span>
                )}
              </div>
            </div>

            <div className="mt-4 md:mt-0">
              <p className="text-gray-400 text-sm">Cost:</p>
              <p className="text-yellow-300 text-xl font-medium">1 Token</p>
              <p className="text-gray-400 text-sm mt-1">Your balance: <span className={parseInt(userTokens) < 1 ? "text-red-300" : "text-gray-300"}>{userTokens || '0'} tokens</span></p>
              {parseInt(userTokens) < 1 && (
                <p className="text-red-300 text-xs mt-2">Insufficient tokens. Please add more tokens to purchase.</p>
              )}
            </div>
          </div>

          <div className="flex justify-between mt-4">
            <button
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded-lg transition duration-200"
              onClick={() => setSelectedNumber(null)}
            >
              Cancel
            </button>

            <button
              className={`px-6 py-3 ${parseInt(userTokens) >= 1 ? 'bg-purple-600 hover:bg-purple-700' : 'bg-purple-600/50 cursor-not-allowed'} text-white font-medium rounded-lg shadow-lg transition duration-200 flex items-center`}
              onClick={handlePurchaseNumber}
              disabled={isLoading || parseInt(userTokens) < 1}
            >
              {isLoading ? (
                <>
                  <div className="animate-spin h-5 w-5 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
                  Processing...
                </>
              ) : (
                <>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                  Confirm Purchase (1 Token)
                </>
              )}
            </button>
          </div>
        </div>
      )}

      {/* Success message */}
      {purchaseSuccess && purchasedNumberDetails && (
        <div className="bg-green-900/30 rounded-lg p-6 border border-green-500/30">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-green-500/20 rounded-full p-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>

          <h3 className="text-xl font-bold text-white text-center mb-2">Phone Number Purchased Successfully!</h3>
          <p className="text-gray-300 text-center mb-6">Your new phone number is ready to use with CallSaver.</p>

          <div className="bg-gray-800/50 rounded-lg p-5 border border-gray-700 mb-6">
            <div className="flex flex-col md:flex-row justify-between">
              <div>
                <p className="text-gray-400 text-sm">Your New Number:</p>
                <p className="text-white text-xl font-medium">
                  {purchasedNumberDetails.friendlyName ||
                   (typeof purchasedNumberDetails.phoneNumber === 'string' ?
                    purchasedNumberDetails.phoneNumber :
                    (purchasedNumberDetails.phoneNumber?.number ||
                     (purchasedNumberDetails.number || 'Phone number purchased')))}
                </p>
                <p className="text-gray-400 text-sm mt-1">
                  {purchasedNumberDetails.location || purchasedNumberDetails.country || purchasedNumberDetails.countryCode || 'US'} •
                  Purchased on {new Date().toLocaleDateString()}
                </p>

                <div className="flex flex-wrap gap-1 mt-3">
                  {purchasedNumberDetails.capabilities?.voice && (
                    <span className="px-2 py-0.5 bg-blue-500/20 text-blue-300 text-xs rounded">Voice</span>
                  )}
                  {purchasedNumberDetails.capabilities?.sms && (
                    <span className="px-2 py-0.5 bg-green-500/20 text-green-300 text-xs rounded">SMS</span>
                  )}
                  {purchasedNumberDetails.capabilities?.fax && (
                    <span className="px-2 py-0.5 bg-yellow-500/20 text-yellow-300 text-xs rounded">Fax</span>
                  )}
                </div>
              </div>

              <div className="mt-4 md:mt-0 md:text-right">
                <p className="text-gray-400 text-sm">Number Type:</p>
                <p className="text-white text-sm">{purchasedNumberDetails.type || "Local Number"}</p>
                <p className="text-gray-400 text-sm mt-2">Cost:</p>
                <p className="text-yellow-300 text-sm font-semibold">1 Token</p>
                <p className="text-gray-400 text-sm mt-2">Remaining Balance:</p>
                <p className="text-yellow-300 text-sm font-semibold">{userTokens} Tokens</p>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <button
              onClick={handleReset}
              className="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded-lg shadow-lg transition duration-200 text-center"
            >
              Buy Another Number
            </button>
            <a
              href="/dashboard/settings"
              className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg shadow-lg transition duration-200 text-center"
            >
              Configure Number Settings
            </a>
          </div>
        </div>
      )}

      {/* Debug information - only in development and when not in purchase success state */}
      {process.env.NODE_ENV !== 'production' && debugInfo && !purchaseSuccess && (
        <div className="mt-8 border border-blue-500/30 rounded-lg p-4 bg-blue-500/10">
          <h3 className="text-lg font-semibold text-white mb-2">Debug Information</h3>
          <pre className="text-xs text-gray-300 overflow-auto max-h-80">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}