'use server';

import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// Create a new Prisma client - will be used for direct database access
const prisma = new PrismaClient();

// This route provides fallback data when the Twilio API is unavailable
export async function GET(request) {
  console.log('[API Fallback] Attempting to provide fallback phone number data');
  
  try {
    // Get authentication info from Supabase
    const cookieStore = cookies();
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      { 
        cookies: { 
          get: (name) => cookieStore.get(name)?.value
        } 
      }
    );
    
    // Get user session for authentication
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      console.log('[API Fallback] No valid session found, returning empty numbers array');
      return NextResponse.json({
        success: true,
        message: 'Fallback without authentication',
        numbers: [] // Empty array for unauthenticated users
      });
    }
    
    // Try to get user by email from the database
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });
    
    if (!user) {
      console.log('[API Fallback] User not found in database');
      return NextResponse.json({
        success: true,
        message: 'User not found in database',
        numbers: []
      });
    }
    
    // Directly query the database for this user's phone numbers
    console.log(`[API Fallback] Querying phone numbers for user ID: ${user.id}`);
    const phoneNumbers = await prisma.phoneNumber.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: 'asc' }
    });
    
    console.log(`[API Fallback] Found ${phoneNumbers.length} phone numbers for user`);
    
    // Return the phone numbers
    return NextResponse.json({
      success: true,
      message: 'Fallback data provided from database',
      numbers: phoneNumbers
    });
  } catch (error) {
    console.error('[API Fallback] Error in fallback handler:', error);
    // Return empty array on error
    return NextResponse.json({
      success: true,
      message: 'Error in fallback data provider',
      numbers: [] // Empty array instead of error
    });
  } finally {
    // Disconnect Prisma
    await prisma.$disconnect();
  }
}
