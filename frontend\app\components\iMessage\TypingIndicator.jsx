"use client";

import { motion } from 'framer-motion';

export default function TypingIndicator() {
  return (
    <div className="flex justify-start mb-3">
      <div className="bg-[#E9E9EB] rounded-[16px] rounded-bl-[4px] px-4 py-2.5 shadow-sm">
        <div className="flex space-x-1.5">
          <motion.div 
            className="h-2 w-2 rounded-full bg-gray-400" 
            animate={{ 
              scale: [0.8, 1.2, 0.8],
              opacity: [0.4, 0.7, 0.4]
            }}
            transition={{ 
              repeat: Infinity, 
              duration: 0.8,
              ease: "easeInOut",
              delay: 0
            }}
          />
          <motion.div 
            className="h-2 w-2 rounded-full bg-gray-400" 
            animate={{ 
              scale: [0.8, 1.2, 0.8],
              opacity: [0.4, 0.7, 0.4]
            }}
            transition={{ 
              repeat: Infinity, 
              duration: 0.8,
              ease: "easeInOut",
              delay: 0.2
            }}
          />
          <motion.div 
            className="h-2 w-2 rounded-full bg-gray-400" 
            animate={{ 
              scale: [0.8, 1.2, 0.8],
              opacity: [0.4, 0.7, 0.4]
            }}
            transition={{ 
              repeat: Infinity, 
              duration: 0.8,
              ease: "easeInOut",
              delay: 0.4
            }}
          />
        </div>
      </div>
    </div>
  );
} 