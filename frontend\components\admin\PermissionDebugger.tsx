'use client';

import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { api } from '../../lib/apiClient';
import { usePermissions } from '../../hooks/usePermissions';
import { LockClosedIcon, LockOpenIcon, ExclamationCircleIcon, InformationCircleIcon } from '@heroicons/react/24/solid';

interface PermissionDebuggerProps {
  userId?: string; // Optional user ID to debug permissions for a specific user
  showAllPermissions?: boolean; // Whether to show all available permissions
  className?: string;
}

interface PermissionDetails {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
  scope: string;
  groupId: string;
  groupName: string;
}

interface RoleDetails {
  id: string;
  name: string;
  description: string;
  isSystem: boolean;
  isDefault: boolean;
  parentId: string | null;
  parentName: string | null;
  permissions: string[];
}

/**
 * A component for debugging permissions
 * Shows detailed information about a user's permissions, roles, and inheritance
 */
const PermissionDebugger: React.FC<PermissionDebuggerProps> = ({
  userId,
  showAllPermissions = false,
  className = '',
}) => {
  const [selectedPermission, setSelectedPermission] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'granted' | 'denied'>('all');
  const { userPermissions, isLoading: isPermissionsLoading } = usePermissions();

  // Fetch detailed permission information
  const { data: permissionDetails, isLoading: isDetailsLoading } = useQuery({
    queryKey: ['permission-details', userId],
    queryFn: async () => {
      const response = await api.get<{
        permissions: PermissionDetails[];
        roles: RoleDetails[];
        userPermissions: string[];
        allPermissions: string[];
      }>(`/api/admin/permissions/debug${userId ? `?userId=${userId}` : ''}`);
      return response;
    },
    enabled: showAllPermissions || !!userId,
  });

  // Filter permissions based on search term and filter type
  const filteredPermissions = React.useMemo(() => {
    if (!permissionDetails) return [];

    let permissions = showAllPermissions 
      ? permissionDetails.allPermissions 
      : permissionDetails?.userPermissions || userPermissions || [];

    // Apply search filter
    if (searchTerm) {
      permissions = permissions.filter(p => 
        p.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply type filter
    if (filterType === 'granted') {
      permissions = permissions.filter(p => 
        (permissionDetails?.userPermissions || userPermissions || []).includes(p)
      );
    } else if (filterType === 'denied') {
      permissions = permissions.filter(p => 
        !(permissionDetails?.userPermissions || userPermissions || []).includes(p)
      );
    }

    return permissions;
  }, [permissionDetails, userPermissions, searchTerm, filterType, showAllPermissions]);

  // Get details for a specific permission
  const getPermissionDetails = (permissionString: string) => {
    if (!permissionDetails?.permissions) return null;

    const [resource, action, scope = 'any'] = permissionString.split(':');
    
    // Handle wildcard permissions
    if (permissionString === '**') {
      return {
        id: '**',
        name: 'Super Admin',
        description: 'Grants all permissions in the system',
        resource: '*',
        action: '*',
        scope: '*',
        groupId: 'system',
        groupName: 'System',
      };
    }

    // Handle resource wildcards
    if (action === '*') {
      return {
        id: `${resource}:*:${scope}`,
        name: `All ${resource} actions`,
        description: `Grants all actions on ${resource} resources`,
        resource,
        action: '*',
        scope,
        groupId: resource,
        groupName: resource.charAt(0).toUpperCase() + resource.slice(1),
      };
    }

    // Find exact permission
    return permissionDetails.permissions.find(p => 
      p.resource === resource && 
      p.action === action && 
      (p.scope === scope || p.scope === '*' || scope === '*')
    );
  };

  // Get roles that grant a specific permission
  const getRolesForPermission = (permissionString: string) => {
    if (!permissionDetails?.roles) return [];

    return permissionDetails.roles.filter(role => 
      role.permissions.includes(permissionString) ||
      role.permissions.includes('**') ||
      role.permissions.includes(`${permissionString.split(':')[0]}:*:${permissionString.split(':')[2] || 'any'}`)
    );
  };

  // Check if a permission is granted
  const isPermissionGranted = (permissionString: string) => {
    return (permissionDetails?.userPermissions || userPermissions || []).includes(permissionString);
  };

  // Loading state
  if (isPermissionsLoading || isDetailsLoading) {
    return (
      <div className={`p-4 bg-gray-800 rounded-lg ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-700 rounded w-3/4"></div>
          <div className="h-4 bg-gray-700 rounded w-1/2"></div>
          <div className="h-4 bg-gray-700 rounded w-5/6"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-gray-800 rounded-lg shadow-lg overflow-hidden ${className}`}>
      <div className="p-4 bg-gray-900 border-b border-gray-700">
        <h2 className="text-xl font-semibold text-white">Permission Debugger</h2>
        <p className="text-gray-400 text-sm mt-1">
          {userId 
            ? `Debugging permissions for user ${userId}` 
            : 'Debugging permissions for current user'}
        </p>
      </div>

      {/* Search and filters */}
      <div className="p-4 border-b border-gray-700 bg-gray-850 flex flex-wrap gap-4">
        <div className="flex-1 min-w-[200px]">
          <input
            type="text"
            placeholder="Search permissions..."
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex space-x-2">
          <button
            className={`px-3 py-2 rounded-md text-sm font-medium ${
              filterType === 'all' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
            onClick={() => setFilterType('all')}
          >
            All
          </button>
          <button
            className={`px-3 py-2 rounded-md text-sm font-medium ${
              filterType === 'granted' 
                ? 'bg-green-600 text-white' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
            onClick={() => setFilterType('granted')}
          >
            Granted
          </button>
          <button
            className={`px-3 py-2 rounded-md text-sm font-medium ${
              filterType === 'denied' 
                ? 'bg-red-600 text-white' 
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
            onClick={() => setFilterType('denied')}
          >
            Denied
          </button>
        </div>
      </div>

      {/* Permission list and details */}
      <div className="flex flex-col md:flex-row">
        {/* Permission list */}
        <div className="md:w-1/2 border-r border-gray-700 overflow-y-auto max-h-[500px]">
          <div className="p-2">
            <h3 className="text-lg font-medium text-white px-2 py-1">
              Permissions ({filteredPermissions.length})
            </h3>
            <div className="space-y-1 mt-2">
              {filteredPermissions.length === 0 ? (
                <div className="text-gray-400 text-sm p-2">
                  No permissions found matching your criteria.
                </div>
              ) : (
                filteredPermissions.map((permission) => (
                  <div
                    key={permission}
                    className={`px-3 py-2 rounded-md cursor-pointer flex items-center ${
                      selectedPermission === permission
                        ? 'bg-gray-700'
                        : 'hover:bg-gray-700/50'
                    }`}
                    onClick={() => setSelectedPermission(permission)}
                  >
                    {isPermissionGranted(permission) ? (
                      <LockOpenIcon className="w-4 h-4 text-green-400 mr-2" />
                    ) : (
                      <LockClosedIcon className="w-4 h-4 text-red-400 mr-2" />
                    )}
                    <span className={`text-sm ${isPermissionGranted(permission) ? 'text-white' : 'text-gray-400'}`}>
                      {permission}
                    </span>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>

        {/* Permission details */}
        <div className="md:w-1/2 p-4 bg-gray-800">
          {selectedPermission ? (
            <div>
              <h3 className="text-lg font-medium text-white">Permission Details</h3>
              <div className="mt-4 space-y-4">
                <div className="bg-gray-700 rounded-lg p-4">
                  <div className="flex items-center">
                    {isPermissionGranted(selectedPermission) ? (
                      <LockOpenIcon className="w-5 h-5 text-green-400 mr-2" />
                    ) : (
                      <LockClosedIcon className="w-5 h-5 text-red-400 mr-2" />
                    )}
                    <h4 className="text-md font-medium text-white">{selectedPermission}</h4>
                  </div>
                  
                  <div className="mt-2 text-sm text-gray-300">
                    {getPermissionDetails(selectedPermission) ? (
                      <>
                        <p className="mt-1">
                          <span className="text-gray-400">Name:</span>{' '}
                          {getPermissionDetails(selectedPermission)?.name}
                        </p>
                        <p className="mt-1">
                          <span className="text-gray-400">Description:</span>{' '}
                          {getPermissionDetails(selectedPermission)?.description}
                        </p>
                        <p className="mt-1">
                          <span className="text-gray-400">Group:</span>{' '}
                          {getPermissionDetails(selectedPermission)?.groupName}
                        </p>
                      </>
                    ) : (
                      <p className="text-yellow-400 flex items-center">
                        <ExclamationCircleIcon className="w-4 h-4 mr-1" />
                        No detailed information available for this permission.
                      </p>
                    )}
                  </div>
                </div>

                <div className="bg-gray-700 rounded-lg p-4">
                  <h4 className="text-md font-medium text-white">Granted by Roles</h4>
                  <div className="mt-2">
                    {getRolesForPermission(selectedPermission).length > 0 ? (
                      <ul className="space-y-2">
                        {getRolesForPermission(selectedPermission).map((role) => (
                          <li key={role.id} className="text-sm">
                            <span className="text-blue-400">{role.name}</span>
                            {role.parentName && (
                              <span className="text-gray-400"> (inherits from {role.parentName})</span>
                            )}
                            <p className="text-gray-400 text-xs mt-1">{role.description}</p>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-sm text-gray-400">
                        This permission is not granted by any role.
                      </p>
                    )}
                  </div>
                </div>

                <div className="bg-gray-700 rounded-lg p-4">
                  <h4 className="text-md font-medium text-white">Permission Components</h4>
                  <div className="mt-2 text-sm">
                    <p>
                      <span className="text-gray-400">Resource:</span>{' '}
                      <code className="bg-gray-800 px-1 py-0.5 rounded text-blue-300">
                        {selectedPermission.split(':')[0]}
                      </code>
                    </p>
                    <p className="mt-1">
                      <span className="text-gray-400">Action:</span>{' '}
                      <code className="bg-gray-800 px-1 py-0.5 rounded text-green-300">
                        {selectedPermission.split(':')[1]}
                      </code>
                    </p>
                    <p className="mt-1">
                      <span className="text-gray-400">Scope:</span>{' '}
                      <code className="bg-gray-800 px-1 py-0.5 rounded text-purple-300">
                        {selectedPermission.split(':')[2] || 'any'}
                      </code>
                    </p>
                  </div>
                </div>

                <div className="bg-gray-700 rounded-lg p-4">
                  <h4 className="text-md font-medium text-white">Usage Examples</h4>
                  <div className="mt-2 text-sm">
                    <p className="text-gray-300">In React components:</p>
                    <pre className="bg-gray-800 p-2 rounded mt-1 text-xs overflow-x-auto">
                      <code className="text-purple-300">
                        {`<PermissionGate permission="${selectedPermission}">\n  {/* Protected content */}\n</PermissionGate>`}
                      </code>
                    </pre>
                    <p className="text-gray-300 mt-3">With hooks:</p>
                    <pre className="bg-gray-800 p-2 rounded mt-1 text-xs overflow-x-auto">
                      <code className="text-purple-300">
                        {`const { hasPermission } = usePermissions();\nconst canAccess = hasPermission('${selectedPermission}');`}
                      </code>
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-gray-400 p-8">
              <InformationCircleIcon className="w-12 h-12 mb-4" />
              <p className="text-center">
                Select a permission from the list to view its details.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PermissionDebugger;
