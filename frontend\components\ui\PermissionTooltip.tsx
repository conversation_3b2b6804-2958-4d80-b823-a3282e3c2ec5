'use client';

import React, { useState, useRef, useEffect } from 'react';
import { LockClosedIcon } from '@heroicons/react/24/solid';

interface PermissionTooltipProps {
  message: string;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  showIcon?: boolean;
  className?: string;
  tooltipClassName?: string;
}

/**
 * A tooltip component for permission-related messages
 * 
 * @param message - The message to display in the tooltip
 * @param children - The element to attach the tooltip to
 * @param position - The position of the tooltip (default: 'top')
 * @param showIcon - Whether to show a lock icon (default: true)
 * @param className - Additional CSS classes for the wrapper
 * @param tooltipClassName - Additional CSS classes for the tooltip
 */
const PermissionTooltip: React.FC<PermissionTooltipProps> = ({
  message,
  children,
  position = 'top',
  showIcon = true,
  className = '',
  tooltipClassName = '',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);

  // Position the tooltip based on the wrapper element
  useEffect(() => {
    if (isVisible && tooltipRef.current && wrapperRef.current) {
      const wrapperRect = wrapperRef.current.getBoundingClientRect();
      const tooltipRect = tooltipRef.current.getBoundingClientRect();

      let top = 0;
      let left = 0;

      switch (position) {
        case 'top':
          top = -tooltipRect.height - 8;
          left = (wrapperRect.width - tooltipRect.width) / 2;
          break;
        case 'bottom':
          top = wrapperRect.height + 8;
          left = (wrapperRect.width - tooltipRect.width) / 2;
          break;
        case 'left':
          top = (wrapperRect.height - tooltipRect.height) / 2;
          left = -tooltipRect.width - 8;
          break;
        case 'right':
          top = (wrapperRect.height - tooltipRect.height) / 2;
          left = wrapperRect.width + 8;
          break;
      }

      tooltipRef.current.style.top = `${top}px`;
      tooltipRef.current.style.left = `${left}px`;
    }
  }, [isVisible, position]);

  // Get position-specific classes
  const getPositionClasses = () => {
    switch (position) {
      case 'top':
        return 'bottom-full mb-2';
      case 'bottom':
        return 'top-full mt-2';
      case 'left':
        return 'right-full mr-2';
      case 'right':
        return 'left-full ml-2';
      default:
        return 'bottom-full mb-2';
    }
  };

  return (
    <div
      ref={wrapperRef}
      className={`relative inline-block ${className}`}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}

      {isVisible && (
        <div
          ref={tooltipRef}
          className={`absolute z-50 px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-md shadow-lg max-w-xs transition-opacity duration-300 ${
            isVisible ? 'opacity-100' : 'opacity-0'
          } ${getPositionClasses()} ${tooltipClassName}`}
        >
          <div className="flex items-center">
            {showIcon && (
              <LockClosedIcon className="w-4 h-4 mr-2 text-red-400" />
            )}
            <span>{message}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default PermissionTooltip;
