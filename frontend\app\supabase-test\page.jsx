import SupabaseConnections from '../components/SupabaseConnections';

export default function SupabaseTestPage() {
  return (
    <div className="container mx-auto py-12 px-4">
      <h1 className="text-2xl md:text-3xl font-semibold text-white/90 mb-8 text-center">
        Supabase Integration Test
      </h1>
      
      <SupabaseConnections />
      
      <div className="max-w-3xl mx-auto mt-12 bg-gray-900/50 p-6 rounded-xl border border-gray-800">
        <h2 className="text-xl text-white/80 mb-4">Available Connection Options</h2>
        <ul className="space-y-3 text-gray-300">
          <li className="flex items-start">
            <span className="inline-block w-6 text-center mr-2">•</span>
            <span><strong>Direct PostgreSQL:</strong> Best for server-side operations with persistent connections</span>
          </li>
          <li className="flex items-start">
            <span className="inline-block w-6 text-center mr-2">•</span>
            <span><strong>Supabase Client:</strong> Best for client-side auth, storage and realtime features</span>
          </li>
          <li className="flex items-start">
            <span className="inline-block w-6 text-center mr-2">•</span>
            <span><strong>Prisma ORM:</strong> Best for type-safe database operations with pooled connections</span>
          </li>
        </ul>
      </div>
    </div>
  );
} 