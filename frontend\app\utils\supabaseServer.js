import { createServerClient, createClient } from '@supabase/ssr';
import { cookies } from 'next/headers';

// This function creates a Supabase client for server components
export function createServerSupabase() {
  const cookieStore = cookies();
  
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        get: (name) => cookieStore.get(name)?.value,
        set: () => {
          // Server components can't set cookies directly
          return;
        },
        remove: () => {
          // Server components can't remove cookies directly
          return;
        },
      },
    }
  );
}

// Service role client for more privileged operations (use carefully!)
export function createServiceClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
  
  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase service role key');
  }
  
  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
}

// Use this in server components or API routes
const supabaseServer = createServerSupabase();

export default supabaseServer; 