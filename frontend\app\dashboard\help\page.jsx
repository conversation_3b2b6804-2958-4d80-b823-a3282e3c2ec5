'use client';

export default function HelpPage() {
  const faqItems = [
    {
      question: 'How do I purchase a new phone number?',
      answer: 'Navigate to the "Buy Number" section in the sidebar. From there, you can search for available numbers, filter by capabilities, and purchase a number that suits your needs.'
    },
    {
      question: 'How do I view my call logs?',
      answer: 'Your call logs and voicemails can be found in the "Call Logs" section in the sidebar. This shows all incoming and outgoing calls, along with voicemails and transcriptions.'
    },
    {
      question: 'How do I set up call forwarding?',
      answer: 'You can configure call forwarding in the Settings section under the "Phone Numbers" tab. Select the number you wish to configure and set up forwarding rules.'
    },
    {
      question: 'Can I export my call data?',
      answer: 'Yes, you can export your call data from the Analytics section. Look for the export button at the top right of the call data table.'
    },
    {
      question: 'How do I cancel my subscription?',
      answer: 'You can manage your subscription in the Settings section under the "Billing & Subscription" tab. From there, you can update your plan or cancel your subscription.'
    }
  ];

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white">Help Center</h2>
        <p className="text-gray-400 mt-1">
          Find answers to common questions and get support for CallSaver.
        </p>
      </div>
      
      {/* Search section */}
      <div className="mb-10">
        <div className="relative">
          <input 
            type="text" 
            placeholder="Search for help..." 
            className="w-full pl-12 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
          />
          <div className="absolute left-4 top-3.5">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>
      
      {/* Contact options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
        <div className="bg-gray-900/70 backdrop-blur-lg rounded-xl border border-gray-800 p-6">
          <div className="flex items-center mb-4">
            <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="ml-3 text-lg font-semibold text-white">Email Support</h3>
          </div>
          <p className="text-gray-400 mb-4">
            Get help from our support team via email. We typically respond within 24 hours.
          </p>
          <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm">
            Contact Support
          </button>
        </div>
        
        <div className="bg-gray-900/70 backdrop-blur-lg rounded-xl border border-gray-800 p-6">
          <div className="flex items-center mb-4">
            <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className="ml-3 text-lg font-semibold text-white">Live Chat</h3>
          </div>
          <p className="text-gray-400 mb-4">
            Chat with our support team in real-time during business hours (9am-5pm EST).
          </p>
          <button className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors text-sm">
            Start Chat
          </button>
        </div>
      </div>
      
      {/* FAQ section */}
      <div className="bg-gray-900/70 backdrop-blur-lg rounded-xl border border-gray-800 p-6">
        <h3 className="text-xl font-semibold text-white mb-6">Frequently Asked Questions</h3>
        
        <div className="space-y-4">
          {faqItems.map((item, index) => (
            <div key={index} className="border-b border-gray-800 pb-4 last:border-b-0">
              <h4 className="text-white font-medium mb-2">{item.question}</h4>
              <p className="text-gray-400 text-sm">{item.answer}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 