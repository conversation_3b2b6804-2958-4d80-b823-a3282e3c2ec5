'use client';

import React from 'react';

interface Tab {
  id: string;
  label: string;
}

interface SettingsLayoutProps {
  children: React.ReactNode;
  activeTab: string;
  tabs: Tab[];
  onTabChange: (tabId: string) => void;
}

export default function SettingsLayout({
  children,
  activeTab,
  tabs,
  onTabChange
}: SettingsLayoutProps) {
  return (
    <div className="flex flex-col md:flex-row gap-6">
      {/* Sidebar navigation */}
      <div className="w-full md:w-64 flex-shrink-0">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
          <nav className="flex flex-col">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                className={`px-4 py-3 text-left transition-colors ${
                  activeTab === tab.id
                    ? 'bg-indigo-50 dark:bg-indigo-900/20 border-l-4 border-indigo-600 dark:border-indigo-500 text-indigo-700 dark:text-indigo-400 font-medium'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50 border-l-4 border-transparent'
                }`}
                onClick={() => onTabChange(tab.id)}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content area */}
      <div className="flex-1">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          {children}
        </div>
      </div>
    </div>
  );
}
