'use client';

import { useState, useEffect } from 'react';
import { IntegrationSettings } from '../../../hooks/useIntegrations';

interface GoogleCalendarSettingsProps {
  settings: IntegrationSettings;
  onChange: (settings: IntegrationSettings) => void;
}

export default function GoogleCalendarSettings({
  settings,
  onChange,
}: GoogleCalendarSettingsProps) {
  const [selectedCalendars, setSelectedCalendars] = useState<string[]>(
    settings.selectedCalendars || []
  );
  const [availableCalendars, setAvailableCalendars] = useState<
    { id: string; name: string }[]
  >(settings.availableCalendars || []);

  // Update settings when selectedCalendars changes
  useEffect(() => {
    onChange({
      ...settings,
      selectedCalendars,
    });
  }, [selectedCalendars]);

  // Handle checkbox change for a calendar
  const handleCalendarToggle = (calendarId: string) => {
    if (selectedCalendars.includes(calendarId)) {
      setSelectedCalendars(selectedCalendars.filter((id) => id !== calendarId));
    } else {
      setSelectedCalendars([...selectedCalendars, calendarId]);
    }
  };

  // Handle sync direction change
  const handleSyncDirectionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange({
      ...settings,
      syncDirection: e.target.value,
    });
  };

  // Handle checkbox change for general settings
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    onChange({
      ...settings,
      [name]: checked,
    });
  };

  return (
    <div className="space-y-6">
      {/* Sync Direction */}
      <div>
        <label
          htmlFor="syncDirection"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Sync Direction
        </label>
        <select
          id="syncDirection"
          name="syncDirection"
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
          value={settings.syncDirection || 'both'}
          onChange={handleSyncDirectionChange}
        >
          <option value="both">Two-way (CallSaver ↔ Google)</option>
          <option value="to_google">One-way (CallSaver → Google)</option>
          <option value="from_google">One-way (Google → CallSaver)</option>
        </select>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Determine how calendar events are synchronized.
        </p>
      </div>

      {/* Calendar Selection */}
      <div>
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Calendars to Sync
        </h4>
        {availableCalendars.length > 0 ? (
          <div className="space-y-2 max-h-60 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-md p-2">
            {availableCalendars.map((calendar) => (
              <div key={calendar.id} className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id={`calendar-${calendar.id}`}
                    type="checkbox"
                    className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
                    checked={selectedCalendars.includes(calendar.id)}
                    onChange={() => handleCalendarToggle(calendar.id)}
                  />
                </div>
                <div className="ml-3 text-sm">
                  <label
                    htmlFor={`calendar-${calendar.id}`}
                    className="font-medium text-gray-700 dark:text-gray-300"
                  >
                    {calendar.name}
                  </label>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-sm text-gray-500 dark:text-gray-400">
            No calendars available. Please reconnect your Google Calendar account.
          </p>
        )}
      </div>

      {/* Additional Settings */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Additional Settings
        </h4>

        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="syncBusySlots"
              name="syncBusySlots"
              type="checkbox"
              className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
              checked={settings.syncBusySlots !== false} // Default to true
              onChange={handleCheckboxChange}
            />
          </div>
          <div className="ml-3 text-sm">
            <label
              htmlFor="syncBusySlots"
              className="font-medium text-gray-700 dark:text-gray-300"
            >
              Sync Busy Slots
            </label>
            <p className="text-gray-500 dark:text-gray-400">
              Block CallSaver availability when you have events in Google Calendar.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="includeAttendeeInfo"
              name="includeAttendeeInfo"
              type="checkbox"
              className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
              checked={settings.includeAttendeeInfo || false}
              onChange={handleCheckboxChange}
            />
          </div>
          <div className="ml-3 text-sm">
            <label
              htmlFor="includeAttendeeInfo"
              className="font-medium text-gray-700 dark:text-gray-300"
            >
              Include Attendee Information
            </label>
            <p className="text-gray-500 dark:text-gray-400">
              Add caller/contact information to Google Calendar events.
            </p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="addCallLink"
              name="addCallLink"
              type="checkbox"
              className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
              checked={settings.addCallLink !== false} // Default to true
              onChange={handleCheckboxChange}
            />
          </div>
          <div className="ml-3 text-sm">
            <label
              htmlFor="addCallLink"
              className="font-medium text-gray-700 dark:text-gray-300"
            >
              Add CallSaver Link
            </label>
            <p className="text-gray-500 dark:text-gray-400">
              Include a link to the CallSaver appointment in Google Calendar events.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
