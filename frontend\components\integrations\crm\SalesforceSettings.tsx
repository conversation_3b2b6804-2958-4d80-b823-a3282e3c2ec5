'use client';

import { IntegrationSettings } from '../../../hooks/useIntegrations';

interface SalesforceSettingsProps {
  settings: IntegrationSettings;
  onChange: (settings: IntegrationSettings) => void;
}

export default function SalesforceSettings({
  settings,
  onChange,
}: SalesforceSettingsProps) {
  // Handle checkbox change
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    onChange({
      ...settings,
      [name]: checked,
    });
  };

  return (
    <div className="space-y-4">
      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
        Data Synchronization
      </h4>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="syncContacts"
            name="syncContacts"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.syncContacts !== false} // Default to true
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="syncContacts"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Sync Contacts
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Synchronize contacts between CallSaver and Salesforce.
          </p>
        </div>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="logCalls"
            name="logCalls"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.logCalls !== false} // Default to true
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="logCalls"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Log Calls
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Create call activities in Salesforce for calls handled by CallSaver.
          </p>
        </div>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="createTasks"
            name="createTasks"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.createTasks || false}
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="createTasks"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Create Tasks
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Create tasks in Salesforce for missed calls and voicemails.
          </p>
        </div>
      </div>

      <div className="flex items-start">
        <div className="flex items-center h-5">
          <input
            id="syncOpportunities"
            name="syncOpportunities"
            type="checkbox"
            className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded"
            checked={settings.syncOpportunities || false}
            onChange={handleCheckboxChange}
          />
        </div>
        <div className="ml-3 text-sm">
          <label
            htmlFor="syncOpportunities"
            className="font-medium text-gray-700 dark:text-gray-300"
          >
            Sync Opportunities
          </label>
          <p className="text-gray-500 dark:text-gray-400">
            Link calls and messages to Salesforce opportunities.
          </p>
        </div>
      </div>
    </div>
  );
}
