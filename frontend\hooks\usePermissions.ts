import { useAuthStore } from '../stores/authStore';
import { useQuery } from '@tanstack/react-query';
import { api } from '../lib/apiClient';

// Types for permissions
export interface Permission {
  resource: string;
  action: string;
  scope?: string;
}

// Interface for the hook return value
interface UsePermissionsReturn {
  // Check if user has a specific permission
  hasPermission: (permission: string) => boolean;

  // Check if user has any of the specified permissions
  hasAnyPermission: (permissions: string[]) => boolean;

  // Check if user has all of the specified permissions
  hasAllPermissions: (permissions: string[]) => boolean;

  // Get all permissions for the current user
  userPermissions: string[] | undefined;

  // Loading state
  isLoading: boolean;

  // Error state
  error: Error | null;

  // Enhanced permission checking functions
  can: (permission: string) => boolean;
  canAny: (permissions: string[]) => boolean;
  canAll: (permissions: string[]) => boolean;
  canAccess: (resource: string, action?: string, scope?: string) => boolean;

  // Role checking functions
  isAdmin: () => boolean;
  isDeveloper: () => boolean;
}

/**
 * Hook for checking user permissions
 * @returns {UsePermissionsReturn} Permission checking functions and state
 */
export function usePermissions(): UsePermissionsReturn {
  const { user } = useAuthStore();

  // Fetch permissions for the current user
  const { data: userPermissions, isLoading, error } = useQuery({
    queryKey: ['permissions', user?.id],
    queryFn: async () => {
      if (!user) return [];
      const response = await api.get<string[]>('/users/permissions');
      return response;
    },
    enabled: !!user, // Only run if user is authenticated
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  /**
   * Check if the user has a specific permission
   * @param {string} permission - Permission to check in format 'resource:action[:scope]'
   * @returns {boolean} Whether the user has the permission
   */
  const hasPermission = (permission: string): boolean => {
    if (!userPermissions || !user) return false;

    // Super admin has all permissions
    if (userPermissions.includes('**')) return true;

    // Parse the permission string
    const [resource, action, scope = 'any'] = permission.split(':');

    // Check for exact permission match
    if (userPermissions.includes(permission)) return true;

    // Check for wildcard permissions
    if (userPermissions.includes(`${resource}:*`)) return true;
    if (userPermissions.includes(`${resource}:*:${scope}`)) return true;
    if (userPermissions.includes(`*:${action}`)) return true;
    if (userPermissions.includes(`*:${action}:${scope}`)) return true;
    if (userPermissions.includes(`${resource}:${action}:*`)) return true;
    if (userPermissions.includes(`${resource}:${action}`)) return true;

    return false;
  };

  /**
   * Check if the user has any of the specified permissions
   * @param {string[]} permissions - Array of permissions to check
   * @returns {boolean} Whether the user has any of the permissions
   */
  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => hasPermission(permission));
  };

  /**
   * Check if the user has all of the specified permissions
   * @param {string[]} permissions - Array of permissions to check
   * @returns {boolean} Whether the user has all of the permissions
   */
  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => hasPermission(permission));
  };

  /**
   * Enhanced permission checking functions with more intuitive naming
   */
  const can = hasPermission;
  const canAny = hasAnyPermission;
  const canAll = hasAllPermissions;

  /**
   * Check if the user has access to a specific resource
   * @param {string} resource - The resource to check
   * @param {string} action - The action to check (optional, defaults to 'read')
   * @param {string} scope - The scope to check (optional, defaults to 'any')
   * @returns {boolean} Whether the user has access to the resource
   */
  const canAccess = (
    resource: string,
    action: string = 'read',
    scope: string = 'any'
  ): boolean => {
    return hasPermission(`${resource}:${action}:${scope}`);
  };

  /**
   * Check if the user has admin access
   * @returns {boolean} Whether the user has admin access
   */
  const isAdmin = (): boolean => {
    return hasPermission('**') || hasPermission('admin:access:any');
  };

  /**
   * Check if the user has developer access
   * @returns {boolean} Whether the user has developer access
   */
  const isDeveloper = (): boolean => {
    return hasPermission('developer:access:self');
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    userPermissions,
    isLoading,
    error: error as Error | null,
    // Enhanced functions
    can,
    canAny,
    canAll,
    canAccess,
    isAdmin,
    isDeveloper,
  };
}

// Export a convenience hook for checking a specific permission
export function useHasPermission(permission: string): boolean {
  const { hasPermission } = usePermissions();
  return hasPermission(permission);
}

// Export a convenience hook for checking multiple permissions (any)
export function useHasAnyPermission(permissions: string[]): boolean {
  const { hasAnyPermission } = usePermissions();
  return hasAnyPermission(permissions);
}

// Export a convenience hook for checking multiple permissions (all)
export function useHasAllPermissions(permissions: string[]): boolean {
  const { hasAllPermissions } = usePermissions();
  return hasAllPermissions(permissions);
}

// Export a convenience hook for checking if user has admin access
export function useIsAdmin(): boolean {
  const { isAdmin } = usePermissions();
  return isAdmin();
}

// Export a convenience hook for checking if user has developer access
export function useIsDeveloper(): boolean {
  const { isDeveloper } = usePermissions();
  return isDeveloper();
}

// Export a convenience hook for checking resource access
export function useCanAccess(resource: string, action?: string, scope?: string): boolean {
  const { canAccess } = usePermissions();
  return canAccess(resource, action, scope);
}
