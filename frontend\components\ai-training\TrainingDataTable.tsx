'use client';

import { useState } from 'react';
import { TrainingData } from './TrainingDataManager';
import { DocumentTextIcon, ChatBubbleLeftIcon, TrashIcon, EyeIcon } from '@heroicons/react/24/outline';
import { format } from 'date-fns';
import EmptyState from '../shared/EmptyState';

interface TrainingDataTableProps {
  data: TrainingData[];
  onDelete: (id: string) => void;
  isDeleting: boolean;
}

export default function TrainingDataTable({
  data,
  onDelete,
  isDeleting,
}: TrainingDataTableProps) {
  const [previewId, setPreviewId] = useState<string | null>(null);

  // Handle preview toggle
  const togglePreview = (id: string) => {
    setPreviewId(previewId === id ? null : id);
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  // Format file size
  const formatFileSize = (bytes?: number) => {
    if (bytes === undefined) return '';
    
    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    }
  };

  // If no data, show empty state
  if (data.length === 0) {
    return (
      <EmptyState
        title="No training data yet"
        description="Add training data to help your AI assistant understand your business and respond more effectively to customer inquiries."
        icon={<DocumentTextIcon className="h-12 w-12 text-gray-400" />}
        actionText="Add Training Data"
        onAction={() => window.location.href = '/dashboard/ai-training'}
      />
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-900/50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Type
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Content
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Date Added
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Size
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {data.map((item) => (
              <tr key={item.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {item.type === 'file' ? (
                      <DocumentTextIcon className="h-5 w-5 text-gray-400" />
                    ) : (
                      <ChatBubbleLeftIcon className="h-5 w-5 text-gray-400" />
                    )}
                    <span className="ml-2 text-sm text-gray-900 dark:text-white">
                      {item.type === 'file' ? 'File' : 'Text'}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-900 dark:text-white">
                    {item.type === 'file' && item.name ? (
                      <span className="font-medium">{item.name}</span>
                    ) : (
                      <span className="font-medium">Text Input</span>
                    )}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                    {item.preview.length > 50 && previewId !== item.id
                      ? `${item.preview.substring(0, 50)}...`
                      : item.preview}
                  </div>
                  {previewId === item.id && (
                    <div className="mt-2 text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 p-3 rounded-md">
                      {item.preview}
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {formatDate(item.createdAt)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {formatFileSize(item.size)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end space-x-2">
                    <button
                      type="button"
                      onClick={() => togglePreview(item.id)}
                      className="text-gray-400 hover:text-blue-500 dark:hover:text-blue-400"
                    >
                      <EyeIcon className="h-5 w-5" />
                    </button>
                    <button
                      type="button"
                      onClick={() => onDelete(item.id)}
                      disabled={isDeleting}
                      className="text-gray-400 hover:text-red-500 dark:hover:text-red-400 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
