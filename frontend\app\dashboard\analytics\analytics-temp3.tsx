  return (
    <motion.div 
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="container max-w-7xl mx-auto py-6 px-4"
    >
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor your call and message performance with key metrics
          </p>
        </div>
        
        <div className="flex items-center space-x-2 mt-4 md:mt-0">
          <Select
            value={timeRange}
            onValueChange={(value) => setTimeRange(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={() => fetchData()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>
      
      {/* Stats Overview - First Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <StatCard
          title="Total Calls"
          value={data?.totalCalls || 0}
          icon={<Phone className="h-4 w-4 text-primary" />}
          description={`${data?.callsByDirection.inbound || 0} inbound, ${data?.callsByDirection.outbound || 0} outbound`}
          trend={trends?.calls}
        />
        
        <StatCard
          title="Total Messages"
          value={data?.totalMessages || 0}
          icon={<MessageSquare className="h-4 w-4 text-primary" />}
          description={`${data?.messagesByDirection.inbound || 0} inbound, ${data?.messagesByDirection.outbound || 0} outbound`}
          trend={trends?.messages}
        />
        
        <StatCard
          title="Total Call Duration"
          value={data ? formatDuration(data.totalDuration) : '0h 0m 0s'}
          icon={<Clock className="h-4 w-4 text-primary" />}
          description="Average: 5m 23s per call"
          trend={trends?.duration}
        />
      </div>
      
      {/* Stats Overview - Second Row with Circular Progress */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        {/* Missed Call Rate */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Missed Call Rate</CardTitle>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="flex items-center justify-between">
              <div className="w-20 h-20">
                <CircularProgressbar 
                  value={data?.missedCallRate || 0} 
                  text={`${data?.missedCallRate || 0}%`}
                  styles={buildStyles({
                    textSize: '24px',
                    pathColor: data?.missedCallRate && data.missedCallRate > 20 ? COLORS.failed : COLORS.noAnswer,
                    textColor: data?.missedCallRate && data.missedCallRate > 20 ? COLORS.failed : COLORS.noAnswer,
                    trailColor: '#e9ecef'
                  })}
                />
              </div>
              <div className="ml-4">
                <div className="flex items-center">
                  <PhoneOff className="h-4 w-4 mr-1 text-red-500" />
                  <p className="text-sm font-medium">{data?.missedCalls || 0} missed calls</p>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {data?.missedCallRate || 0}% of total call volume
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* AI Handoff Rate */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">AI Handoff Rate</CardTitle>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="flex items-center justify-between">
              <div className="w-20 h-20">
                <CircularProgressbar 
                  value={data?.aiHandoffRate || 0} 
                  text={`${data?.aiHandoffRate || 0}%`}
                  styles={buildStyles({
                    textSize: '24px',
                    pathColor: COLORS.progress,
                    textColor: COLORS.progress,
                    trailColor: '#e9ecef'
                  })}
                />
              </div>
              <div className="ml-4">
                <div className="flex items-center">
                  <Bot className="h-4 w-4 mr-1 text-indigo-500" />
                  <p className="text-sm font-medium">{data?.aiHandledCalls || 0} calls handled by AI</p>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {data?.aiHandoffRate || 0}% of inbound calls
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* SMS Response Rate */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">SMS Response Rate</CardTitle>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="flex items-center justify-between">
              <div className="w-20 h-20">
                <CircularProgressbar 
                  value={data?.smsResponseRate || 0} 
                  text={`${data?.smsResponseRate || 0}%`}
                  styles={buildStyles({
                    textSize: '24px',
                    pathColor: COLORS.outbound,
                    textColor: COLORS.outbound,
                    trailColor: '#e9ecef'
                  })}
                />
              </div>
              <div className="ml-4">
                <div className="flex items-center">
                  <CheckCircle2 className="h-4 w-4 mr-1 text-green-500" />
                  <p className="text-sm font-medium">{data?.messagesByDirection.outbound || 0} responses</p>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  From {data?.messagesByDirection.inbound || 0} inbound messages
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Average Response Time */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Avg. SMS Response Time</CardTitle>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="flex items-center justify-between">
              <div className="w-20 h-20">
                <CircularProgressbar 
                  value={data?.smsResponseTime || 0} 
                  maxValue={60}
                  text={`${data?.smsResponseTime || 0}m`}
                  styles={buildStyles({
                    textSize: '24px',
                    pathColor: COLORS.inbound,
                    textColor: COLORS.inbound,
                    trailColor: '#e9ecef'
                  })}
                />
              </div>
              <div className="ml-4">
                <div className="flex items-center">
                  <Timer className="h-4 w-4 mr-1 text-blue-500" />
                  <p className="text-sm font-medium">{data?.smsResponseTime || 0} minutes</p>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Average time to first response
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Charts */}
      <Tabs defaultValue="calls" className="mb-6">
        <TabsList className="mb-4">
          <TabsTrigger value="calls">Call Analytics</TabsTrigger>
          <TabsTrigger value="messages">Message Analytics</TabsTrigger>
          <TabsTrigger value="ai">AI Performance</TabsTrigger>
        </TabsList>
        
        <TabsContent value="calls" className="space-y-6">
          {/* Call Trend Chart */}
          <ChartCard 
            title="Call Volume Trend" 
            description={`Call activity over the last ${timeRange === "7d" ? "7 days" : timeRange === "30d" ? "30 days" : "90 days"}`}
          >
            <ResponsiveContainer width="100%" height={300}>
              <LineChart
                data={data?.callTrend.map(day => ({
                  ...day,
                  date: format(new Date(day.date), "MMM dd")
                }))}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="inbound" stroke={COLORS.inbound} name="Inbound Calls" />
                <Line type="monotone" dataKey="outbound" stroke={COLORS.outbound} name="Outbound Calls" />
              </LineChart>
            </ResponsiveContainer>
          </ChartCard>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Call Status Distribution */}
            <ChartCard title="Call Status Distribution">
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={data ? [
                      { name: "Completed", value: data.callsByStatus.completed },
                      { name: "No Answer", value: data.callsByStatus.noAnswer },
                      { name: "Busy", value: data.callsByStatus.busy },
                      { name: "Failed", value: data.callsByStatus.failed },
                      { name: "Canceled", value: data.callsByStatus.canceled }
                    ] : []}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {data && [
                      <Cell key="completed" fill={COLORS.completed} />,
                      <Cell key="noAnswer" fill={COLORS.noAnswer} />,
                      <Cell key="busy" fill={COLORS.busy} />,
                      <Cell key="failed" fill={COLORS.failed} />,
                      <Cell key="canceled" fill={COLORS.canceled} />
                    ]}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </ChartCard>
            
            {/* Peak Call Hours */}
            <ChartCard title="Peak Call Hours">
              <ResponsiveContainer width="100%" height={300}>
                <BarChart
                  data={data?.peakCallHours.map(item => ({
                    hour: `${item.hour}:00`,
                    count: item.count
                  }))}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" name="Call Count" fill={COLORS.inbound} />
                </BarChart>
              </ResponsiveContainer>
            </ChartCard>
          </div>
        </TabsContent>
        
        <TabsContent value="messages" className="space-y-6">
          {/* Message Trend Chart */}
          <ChartCard 
            title="Message Volume Trend" 
            description={`Message activity over the last ${timeRange === "7d" ? "7 days" : timeRange === "30d" ? "30 days" : "90 days"}`}
          >
            <ResponsiveContainer width="100%" height={300}>
              <LineChart
                data={data?.messageTrend.map(day => ({
                  ...day,
                  date: format(new Date(day.date), "MMM dd")
                }))}
                margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="inbound" stroke={COLORS.inbound} name="Inbound Messages" />
                <Line type="monotone" dataKey="outbound" stroke={COLORS.outbound} name="Outbound Messages" />
              </LineChart>
            </ResponsiveContainer>
          </ChartCard>
          
          <ChartCard title="Message Direction Distribution">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={data ? [
                    { name: "Inbound", value: data.messagesByDirection.inbound },
                    { name: "Outbound", value: data.messagesByDirection.outbound }
                  ] : []}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {data && [
                    <Cell key="inbound" fill={COLORS.inbound} />,
                    <Cell key="outbound" fill={COLORS.outbound} />
                  ]}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </ChartCard>
        </TabsContent>

        <TabsContent value="ai" className="space-y-6">
          {/* AI Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ChartCard title="AI Assistant Handling">
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={data ? [
                      { name: "AI Handled Calls", value: data.aiHandledCalls },
                      { name: "Human Handled Calls", value: data.callsByDirection.inbound - data.aiHandledCalls }
                    ] : []}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {data && [
                      <Cell key="ai" fill={COLORS.progress} />,
                      <Cell key="human" fill="#A855F7" />
                    ]}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </ChartCard>
            
            <ChartCard title="AI SMS Response">
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={data ? [
                      { name: "AI Handled Messages", value: data.aiHandledMessages },
                      { name: "Human Handled Messages", value: data.messagesByDirection.inbound - data.aiHandledMessages }
                    ] : []}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {data && [
                      <Cell key="ai" fill={COLORS.progress} />,
                      <Cell key="human" fill="#A855F7" />
                    ]}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </ChartCard>
          </div>
          
          <ChartCard 
            title="AI Performance Metrics" 
            description="Key metrics for AI assistant performance"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 py-4">
              <div className="flex flex-col items-center">
                <div className="w-32 h-32 mb-4">
                  <CircularProgressbar 
                    value={data?.aiHandoffRate || 0} 
                    text={`${data?.aiHandoffRate || 0}%`}
                    styles={buildStyles({
                      textSize: '20px',
                      pathColor: COLORS.progress,
                      textColor: COLORS.progress,
                      trailColor: '#e9ecef'
                    })}
                  />
                </div>
                <h3 className="text-base font-medium">AI Call Handling Rate</h3>
                <p className="text-sm text-muted-foreground text-center">
                  Percentage of inbound calls handled by AI
                </p>
              </div>
              
              <div className="flex flex-col items-center">
                <div className="w-32 h-32 mb-4">
                  <CircularProgressbar 
                    value={data ? (data.aiHandledMessages / data.messagesByDirection.inbound) * 100 : 0} 
                    text={`${data ? ((data.aiHandledMessages / data.messagesByDirection.inbound) * 100).toFixed(1) : 0}%`}
                    styles={buildStyles({
                      textSize: '20px',
                      pathColor: "#8B5CF6",
                      textColor: "#8B5CF6",
                      trailColor: '#e9ecef'
                    })}
                  />
                </div>
                <h3 className="text-base font-medium">AI Message Handling Rate</h3>
                <p className="text-sm text-muted-foreground text-center">
                  Percentage of inbound messages handled by AI
                </p>
              </div>
              
              <div className="flex flex-col items-center">
                <div className="w-32 h-32 mb-4">
                  <CircularProgressbar 
                    value={80} // Placeholder for AI satisfaction score
                    text="80%"
                    styles={buildStyles({
                      textSize: '20px',
                      pathColor: "#10B981",
                      textColor: "#10B981",
                      trailColor: '#e9ecef'
                    })}
                  />
                </div>
                <h3 className="text-base font-medium">AI Satisfaction Score</h3>
                <p className="text-sm text-muted-foreground text-center">
                  Customer satisfaction with AI responses
                </p>
              </div>
            </div>
          </ChartCard>
        </TabsContent>
      </Tabs>
      
      {/* Top Callers */}
      <Card>
        <CardHeader>
          <CardTitle>Top Callers</CardTitle>
          <CardDescription>Most frequent callers by volume</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data?.topCallers.map((caller, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                    <Users className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">{caller.phoneNumber}</p>
                    <p className="text-xs text-gray-500">Caller ID: Unknown</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <p className="font-medium">{caller.callCount} calls</p>
                  <Button variant="ghost" size="sm" className="ml-2">
                    <Phone className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
