'use client';

import { useState } from 'react';
import { useScheduledAutomation, ScheduledAutomation } from '../../../../hooks/useScheduledAutomation';
import AutomationList from '../../../../components/scheduled-automation/AutomationList';
import AutomationEditor from '../../../../components/scheduled-automation/AutomationEditor';
import AutomationAnalytics from '../../../../components/scheduled-automation/AutomationAnalytics';
import LoadingSpinner from '../../../../components/shared/LoadingSpinner';
import ErrorMessage from '../../../../components/shared/ErrorMessage';
import { PlusIcon, ChartBarIcon, ListBulletIcon } from '@heroicons/react/24/outline';

export default function ScheduledAutomationPage() {
  const [selectedAutomation, setSelectedAutomation] = useState<ScheduledAutomation | null>(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'list' | 'analytics'>('list');
  
  const { automationsQuery } = useScheduledAutomation();
  const { data: automations, isLoading, error } = automationsQuery;

  // Handle opening the editor for creating a new automation
  const handleCreateNew = () => {
    setSelectedAutomation(null);
    setIsEditorOpen(true);
  };

  // Handle opening the editor for editing an existing automation
  const handleEdit = (automation: ScheduledAutomation) => {
    setSelectedAutomation(automation);
    setIsEditorOpen(true);
  };

  // Handle closing the editor
  const handleCloseEditor = () => {
    setIsEditorOpen(false);
    setSelectedAutomation(null);
  };

  // Handle saving an automation (create or update)
  const handleSave = () => {
    setIsEditorOpen(false);
    setSelectedAutomation(null);
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="p-4">
        <ErrorMessage message="Failed to load automations. Please try again later." />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Scheduled Automations</h1>
        
        <div className="flex space-x-4">
          {/* View mode toggle */}
          <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-1 rounded-md ${
                viewMode === 'list'
                  ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow'
                  : 'text-gray-500 dark:text-gray-400'
              }`}
            >
              <ListBulletIcon className="h-5 w-5 inline mr-1" />
              List
            </button>
            <button
              onClick={() => setViewMode('analytics')}
              className={`px-3 py-1 rounded-md ${
                viewMode === 'analytics'
                  ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow'
                  : 'text-gray-500 dark:text-gray-400'
              }`}
            >
              <ChartBarIcon className="h-5 w-5 inline mr-1" />
              Analytics
            </button>
          </div>
          
          {/* Create new button */}
          <button
            onClick={handleCreateNew}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <PlusIcon className="h-5 w-5 mr-1" />
            Create New
          </button>
        </div>
      </div>

      {/* Main content based on view mode */}
      {viewMode === 'list' ? (
        <AutomationList
          automations={automations || []}
          onEdit={handleEdit}
        />
      ) : (
        <AutomationAnalytics />
      )}

      {/* Automation editor modal */}
      <AutomationEditor
        automation={selectedAutomation}
        isOpen={isEditorOpen}
        onClose={handleCloseEditor}
        onSave={handleSave}
      />
    </div>
  );
}
