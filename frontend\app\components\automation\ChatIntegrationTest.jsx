"use client";

import { useState } from 'react';

export default function ChatIntegrationTest() {
  const [apiResponse, setApiResponse] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const testOpenAIIntegration = async () => {
    setIsLoading(true);
    setError(null);
    setApiResponse(null);
    
    try {
      // Test the OpenAI integration
      const response = await fetch('/api/automation/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messages: [
            { sender: 'user', content: 'Hello, are you connected to GPT-4-turbo?' }
          ]
        })
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to connect to OpenAI API');
      }
      
      setApiResponse(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 bg-gray-900 rounded-lg border border-gray-700">
      <h2 className="text-xl font-bold mb-4">OpenAI Integration Test</h2>
      
      <button 
        onClick={testOpenAIIntegration}
        disabled={isLoading}
        className={`px-4 py-2 rounded ${
          isLoading 
            ? 'bg-gray-600 cursor-not-allowed' 
            : 'bg-blue-600 hover:bg-blue-700'
        } text-white transition-colors mb-4`}
      >
        {isLoading ? 'Testing...' : 'Test OpenAI Connection'}
      </button>
      
      {error && (
        <div className="p-4 bg-red-900/30 border border-red-700 rounded-md mb-4 text-red-200">
          <p className="font-bold">Error:</p>
          <p>{error}</p>
          <p className="mt-2 text-sm">
            Make sure your OPENAI_API_KEY is set correctly in your environment variables.
          </p>
        </div>
      )}
      
      {apiResponse && (
        <div className="p-4 bg-green-900/30 border border-green-700 rounded-md mb-4">
          <p className="font-bold text-green-200">Success!</p>
          <div className="mt-2 p-3 bg-gray-800 rounded border border-gray-700">
            <p className="font-bold">OpenAI Response:</p>
            <p className="mt-1 whitespace-pre-wrap">{apiResponse.message}</p>
          </div>
          <div className="mt-4 text-sm text-green-200">
            <p>Usage Statistics:</p>
            <ul className="list-disc list-inside mt-1">
              <li>Total Tokens: {apiResponse.usage?.total_tokens || 'N/A'}</li>
              <li>Prompt Tokens: {apiResponse.usage?.prompt_tokens || 'N/A'}</li>
              <li>Completion Tokens: {apiResponse.usage?.completion_tokens || 'N/A'}</li>
              <li>Model: gpt-4-turbo</li>
            </ul>
          </div>
        </div>
      )}
      
      <div className="mt-4 text-sm text-gray-400">
        <p>This test verifies that your OpenAI API integration is working correctly with gpt-4-turbo.</p>
        <p className="mt-1">If the test fails, check the following:</p>
        <ul className="list-disc list-inside mt-1">
          <li>Your OpenAI API key is properly set in the .env file</li>
          <li>Your API key has access to the gpt-4-turbo model</li>
          <li>Your API key has sufficient credits</li>
          <li>The OpenAI API service is currently available</li>
        </ul>
      </div>
    </div>
  );
}
