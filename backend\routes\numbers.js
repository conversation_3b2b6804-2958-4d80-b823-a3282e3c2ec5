const express = require('express');
const router = express.Router();
const numberController = require('../controllers/numberController');

// Define routes for numbers
router.get('/', numberController.getNumbers);
router.post('/', numberController.createNumber);
router.get('/:id', numberController.getNumberById);
router.put('/:id', numberController.updateNumber);
router.delete('/:id', numberController.deleteNumber);

module.exports = router;