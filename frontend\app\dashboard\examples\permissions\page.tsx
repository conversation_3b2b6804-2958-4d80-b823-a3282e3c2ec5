'use client';

import React, { useState } from 'react';
import { 
  PermissionGate, 
  PermissionAwareButton, 
  PermissionAwareLink, 
  PermissionAwareFeature,
  PermissionTooltip,
  useFeatureFlag
} from '../../../../components/ui/permission';

export default function PermissionExamplesPage() {
  const [count, setCount] = useState(0);
  
  // Example of using the useFeatureFlag hook directly
  const isAdvancedFeatureEnabled = useFeatureFlag({
    name: 'advanced-analytics',
    permission: 'analytics:advanced:any',
    description: 'Advanced analytics features'
  });

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold text-white mb-6">Permission Components Examples</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Permission Gate Examples */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">PermissionGate</h2>
          
          <div className="space-y-4">
            <div className="p-4 border border-gray-700 rounded-lg">
              <h3 className="text-lg font-medium text-white mb-2">Basic Usage</h3>
              <PermissionGate permission="dashboard:read:any">
                <div className="p-3 bg-green-900/30 border border-green-700 rounded">
                  This content is only visible if you have the 'dashboard:read:any' permission.
                </div>
              </PermissionGate>
              
              <PermissionGate 
                permission="dashboard:admin:any" 
                fallback={
                  <div className="p-3 mt-3 bg-red-900/30 border border-red-700 rounded">
                    You don't have admin permissions to view this content.
                  </div>
                }
              >
                <div className="p-3 mt-3 bg-green-900/30 border border-green-700 rounded">
                  This content is only visible to admins.
                </div>
              </PermissionGate>
            </div>
            
            <div className="p-4 border border-gray-700 rounded-lg">
              <h3 className="text-lg font-medium text-white mb-2">Multiple Permissions</h3>
              <PermissionGate anyPermission={['reports:read:any', 'reports:create:any']}>
                <div className="p-3 bg-green-900/30 border border-green-700 rounded">
                  This content is visible if you have ANY of the specified permissions.
                </div>
              </PermissionGate>
              
              <PermissionGate 
                allPermissions={['reports:read:any', 'reports:export:any']} 
                fallback={
                  <div className="p-3 mt-3 bg-yellow-900/30 border border-yellow-700 rounded">
                    You need both read and export permissions to view this content.
                  </div>
                }
              >
                <div className="p-3 mt-3 bg-green-900/30 border border-green-700 rounded">
                  This content is visible if you have ALL of the specified permissions.
                </div>
              </PermissionGate>
            </div>
          </div>
        </div>
        
        {/* Permission Aware Button Examples */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">PermissionAwareButton</h2>
          
          <div className="space-y-4">
            <div className="p-4 border border-gray-700 rounded-lg">
              <h3 className="text-lg font-medium text-white mb-2">Basic Usage</h3>
              <div className="flex flex-wrap gap-3">
                <PermissionAwareButton
                  permission="calls:create:any"
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded"
                  onClick={() => alert('Button clicked!')}
                >
                  Make Call
                </PermissionAwareButton>
                
                <PermissionAwareButton
                  permission="sms:send:any"
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded"
                  onClick={() => alert('Button clicked!')}
                >
                  Send SMS
                </PermissionAwareButton>
              </div>
            </div>
            
            <div className="p-4 border border-gray-700 rounded-lg">
              <h3 className="text-lg font-medium text-white mb-2">With Tooltips</h3>
              <div className="flex flex-wrap gap-3">
                <PermissionAwareButton
                  permission="calls:record:any"
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded"
                  onClick={() => alert('Button clicked!')}
                  renderWhenNoAccess={true}
                  showTooltipWhenDisabled={true}
                  tooltipMessage="You need the 'calls:record:any' permission to record calls"
                >
                  Record Call
                </PermissionAwareButton>
                
                <PermissionAwareButton
                  permission="calls:transfer:any"
                  className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded"
                  onClick={() => alert('Button clicked!')}
                  renderWhenNoAccess={true}
                  showTooltipWhenDisabled={true}
                  tooltipPosition="bottom"
                >
                  Transfer Call
                </PermissionAwareButton>
              </div>
            </div>
            
            <div className="p-4 border border-gray-700 rounded-lg">
              <h3 className="text-lg font-medium text-white mb-2">Resource-based Permissions</h3>
              <div className="flex flex-wrap gap-3">
                <PermissionAwareButton
                  resource="phoneNumbers"
                  action="purchase"
                  scope="any"
                  className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded"
                  onClick={() => alert('Button clicked!')}
                  renderWhenNoAccess={true}
                  showTooltipWhenDisabled={true}
                >
                  Purchase Number
                </PermissionAwareButton>
              </div>
            </div>
          </div>
        </div>
        
        {/* Permission Aware Link Examples */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">PermissionAwareLink</h2>
          
          <div className="space-y-4">
            <div className="p-4 border border-gray-700 rounded-lg">
              <h3 className="text-lg font-medium text-white mb-2">Basic Usage</h3>
              <div className="flex flex-wrap gap-3">
                <PermissionAwareLink
                  href="/dashboard/reports"
                  permission="reports:read:any"
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded inline-block"
                >
                  View Reports
                </PermissionAwareLink>
                
                <PermissionAwareLink
                  href="/dashboard/admin"
                  permission="admin:access:any"
                  className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded inline-block"
                >
                  Admin Dashboard
                </PermissionAwareLink>
              </div>
            </div>
            
            <div className="p-4 border border-gray-700 rounded-lg">
              <h3 className="text-lg font-medium text-white mb-2">With Tooltips</h3>
              <div className="flex flex-wrap gap-3">
                <PermissionAwareLink
                  href="/dashboard/analytics"
                  permission="analytics:advanced:any"
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded inline-block"
                  renderDisabledWhenNoAccess={true}
                  showTooltipWhenNoAccess={true}
                  tooltipMessage="You need the 'analytics:advanced:any' permission to access advanced analytics"
                >
                  Advanced Analytics
                </PermissionAwareLink>
                
                <PermissionAwareLink
                  href="/dashboard/billing"
                  permission="billing:manage:any"
                  className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded inline-block"
                  renderDisabledWhenNoAccess={true}
                  showTooltipWhenNoAccess={true}
                  tooltipPosition="bottom"
                >
                  Billing Settings
                </PermissionAwareLink>
              </div>
            </div>
          </div>
        </div>
        
        {/* Feature Flag Examples */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Feature Flags</h2>
          
          <div className="space-y-4">
            <div className="p-4 border border-gray-700 rounded-lg">
              <h3 className="text-lg font-medium text-white mb-2">PermissionAwareFeature</h3>
              
              <PermissionAwareFeature
                name="counter-feature"
                permission="examples:counter:any"
                fallback={
                  <div className="p-3 bg-red-900/30 border border-red-700 rounded">
                    You don't have permission to use the counter feature.
                  </div>
                }
              >
                <div className="p-4 bg-gray-700 rounded-lg">
                  <p className="text-white mb-2">Counter: {count}</p>
                  <div className="flex gap-2">
                    <button 
                      className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded"
                      onClick={() => setCount(count + 1)}
                    >
                      Increment
                    </button>
                    <button 
                      className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded"
                      onClick={() => setCount(0)}
                    >
                      Reset
                    </button>
                  </div>
                </div>
              </PermissionAwareFeature>
              
              <div className="mt-4">
                <PermissionAwareFeature
                  name="disabled-feature"
                  permission="examples:premium:any"
                  renderDisabled={true}
                >
                  <div className="p-4 bg-gray-700 rounded-lg">
                    <p className="text-white mb-2">Premium Feature</p>
                    <p className="text-gray-400 text-sm">This feature is only available to premium users.</p>
                  </div>
                </PermissionAwareFeature>
              </div>
            </div>
            
            <div className="p-4 border border-gray-700 rounded-lg">
              <h3 className="text-lg font-medium text-white mb-2">useFeatureFlag Hook</h3>
              
              <div className="p-4 bg-gray-700 rounded-lg">
                <p className="text-white mb-2">
                  Advanced Analytics Feature: {' '}
                  <span className={isAdvancedFeatureEnabled ? 'text-green-400' : 'text-red-400'}>
                    {isAdvancedFeatureEnabled ? 'Enabled' : 'Disabled'}
                  </span>
                </p>
                
                {isAdvancedFeatureEnabled ? (
                  <div className="mt-2 p-3 bg-green-900/30 border border-green-700 rounded">
                    Advanced analytics features are available to you.
                  </div>
                ) : (
                  <div className="mt-2 p-3 bg-red-900/30 border border-red-700 rounded">
                    You don't have access to advanced analytics features.
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* Custom Tooltip Examples */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Custom Tooltips</h2>
          
          <div className="space-y-4">
            <div className="p-4 border border-gray-700 rounded-lg">
              <h3 className="text-lg font-medium text-white mb-2">PermissionTooltip</h3>
              
              <div className="flex flex-wrap gap-4">
                <PermissionTooltip message="This is a top tooltip">
                  <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded">
                    Top Tooltip
                  </button>
                </PermissionTooltip>
                
                <PermissionTooltip message="This is a bottom tooltip" position="bottom">
                  <button className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded">
                    Bottom Tooltip
                  </button>
                </PermissionTooltip>
                
                <PermissionTooltip message="This is a left tooltip" position="left">
                  <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded">
                    Left Tooltip
                  </button>
                </PermissionTooltip>
                
                <PermissionTooltip message="This is a right tooltip" position="right">
                  <button className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded">
                    Right Tooltip
                  </button>
                </PermissionTooltip>
              </div>
            </div>
            
            <div className="p-4 border border-gray-700 rounded-lg">
              <h3 className="text-lg font-medium text-white mb-2">Custom Styling</h3>
              
              <div className="flex flex-wrap gap-4">
                <PermissionTooltip 
                  message="This tooltip has custom styling" 
                  tooltipClassName="bg-purple-900 border-purple-500"
                >
                  <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded">
                    Custom Style
                  </button>
                </PermissionTooltip>
                
                <PermissionTooltip 
                  message="This tooltip has no icon" 
                  showIcon={false}
                >
                  <button className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded">
                    No Icon
                  </button>
                </PermissionTooltip>
              </div>
            </div>
          </div>
        </div>
        
        {/* Permission Debugger Link */}
        <div className="bg-gray-800 rounded-lg p-6 col-span-1 md:col-span-2">
          <h2 className="text-xl font-semibold text-white mb-4">Permission Debugger</h2>
          
          <div className="p-4 border border-gray-700 rounded-lg">
            <p className="text-gray-300 mb-4">
              The Permission Debugger allows administrators to inspect and debug permissions for any user in the system.
              You can see which permissions are granted, which roles grant those permissions, and detailed information about each permission.
            </p>
            
            <PermissionAwareLink
              href="/dashboard/admin/permissions/debug"
              permission="permissions:read:any"
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded inline-block"
              renderDisabledWhenNoAccess={true}
              showTooltipWhenNoAccess={true}
              tooltipMessage="You need administrator permissions to access the Permission Debugger"
            >
              Open Permission Debugger
            </PermissionAwareLink>
          </div>
        </div>
      </div>
    </div>
  );
}
