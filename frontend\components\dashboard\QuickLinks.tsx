'use client';

import Link from 'next/link';
import { ComponentType } from 'react';

interface QuickLink {
  title: string;
  href: string;
  icon: ComponentType<{ className?: string }>;
}

interface QuickLinksProps {
  links: QuickLink[];
}

export default function QuickLinks({ links }: QuickLinksProps) {
  if (!links || links.length === 0) {
    return null;
  }

  return (
    <div className="grid grid-cols-1 gap-3">
      {links.map((link, index) => {
        const Icon = link.icon;
        
        return (
          <Link 
            key={index} 
            href={link.href}
            className="flex items-center p-3 rounded-lg border border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors duration-150"
          >
            <div className="p-2 bg-indigo-100 dark:bg-indigo-900 rounded-full mr-3">
              <Icon className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
            </div>
            <span className="text-sm font-medium text-gray-900 dark:text-white">{link.title}</span>
          </Link>
        );
      })}
    </div>
  );
}
