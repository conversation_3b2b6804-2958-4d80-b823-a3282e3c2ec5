"use client";

import { useState, useEffect } from 'react';
import { 
  AreaChart, 
  Area, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  Legend,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  ArrowUpIcon, 
  ArrowDownIcon,
  ClockIcon,
  PhoneIcon,
  UserGroupIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import { CallLogsAPI } from '../../utils/api';

// Fallback data generator functions in case API calls fail
const generateSampleData = () => {
  const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  const data = [];
  
  for (let i = 0; i < 7; i++) {
    const answered = Math.floor(Math.random() * 15) + 5;
    const missed = Math.floor(Math.random() * 10);
    const voicemail = Math.floor(Math.random() * 8);
    
    data.push({
      name: days[i],
      answered,
      missed,
      voicemail,
      total: answered + missed + voicemail
    });
  }
  
  return data;
};

const generateHourlyData = () => {
  const data = [];
  
  for (let i = 0; i < 24; i++) {
    const hour = i < 10 ? `0${i}:00` : `${i}:00`;
    const value = Math.floor(Math.random() * 8) + (i > 8 && i < 18 ? 5 : 1); // More calls during business hours
    
    data.push({
      hour,
      calls: value
    });
  }
  
  return data;
};

const generateCallTypeData = () => {
  return [
    { name: 'Answered', value: 65, color: '#4ade80' },
    { name: 'Missed', value: 20, color: '#f87171' },
    { name: 'Voicemail', value: 15, color: '#60a5fa' }
  ];
};

const CallHistoryVisualization = () => {
  const [activeTab, setActiveTab] = useState('weekly');
  const [weeklyData, setWeeklyData] = useState([]);
  const [hourlyData, setHourlyData] = useState([]);
  const [callTypeData, setCallTypeData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Load call data from API
  useEffect(() => {
    const loadCallData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Check if demo user
        const demoUser = localStorage.getItem('callsaver_demo_user');
        if (demoUser) {
          // Demo users get empty data now, not samples
          await new Promise(resolve => setTimeout(resolve, 800)); // Simulate API delay
          setWeeklyData([]);
          setHourlyData([]);
          setCallTypeData([]);
          setError('Displaying empty state for demo user.'); // Optional: Informative message
          setIsLoading(false);
          return;
        }
        
        // For real users: fetch from the API
        try {
          // Get weekly call stats
          const weeklyStats = await CallLogsAPI.getCallStats('weekly');
          if (Array.isArray(weeklyStats) && weeklyStats.length > 0) {
            setWeeklyData(weeklyStats);
          } else {
            // If no data or invalid format, use empty array
            setWeeklyData([]);
          }
          
          // Get hourly call stats 
          const hourlyStats = await CallLogsAPI.getCallStats('hourly');
          if (Array.isArray(hourlyStats) && hourlyStats.length > 0) {
            setHourlyData(hourlyStats);
          } else {
            // If no data or invalid format, use empty array
            setHourlyData([]);
          }
          
          // Calculate call types from weekly data
          const types = {
            answered: 0,
            missed: 0,
            voicemail: 0
          };
          
          // Sum up values from weekly data
          weeklyStats.forEach(day => {
            types.answered += day.answered || 0;
            types.missed += day.missed || 0;
            types.voicemail += day.voicemail || 0;
          });
          
          // Format for pie chart
          const typesData = [
            { name: 'Answered', value: types.answered, color: '#4ade80' },
            { name: 'Missed', value: types.missed, color: '#f87171' },
            { name: 'Voicemail', value: types.voicemail, color: '#60a5fa' }
          ];
          
          // If all values are 0, use empty array or calculated data
          if (types.answered === 0 && types.missed === 0 && types.voicemail === 0) {
            // Ensure it's empty if API returned nothing, otherwise use calculated zeros
            if (weeklyStats.length === 0) {
              setCallTypeData([]);
            } else {
              setCallTypeData(typesData); // Keep the zero values if calculated from real data
            }
          } else {
            setCallTypeData(typesData);
          }
        } catch (apiError) {
          console.error('Error fetching call stats:', apiError);
          // Use empty data if API call fails
          setWeeklyData([]);
          setHourlyData([]);
          setCallTypeData([]);
          
          setError('Could not load call data from the server.'); // Updated error message
        }
      } catch (e) {
        console.error('Error in loadCallData:', e);
        // Use empty data on other errors
        setWeeklyData([]);
        setHourlyData([]);
        setCallTypeData([]);
        setError('An unexpected error occurred while loading call data.');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadCallData();
  }, []);
  
  // Calculate total calls and change percentage (only if data exists)
  const hasData = weeklyData.length > 0;
  const totalCalls = hasData ? weeklyData.reduce((sum, day) => sum + (day.total || day.answered + day.missed + day.voicemail || 0), 0) : 0;
  // TODO: Replace simulated previous period with actual data when available
  const previousTotal = hasData ? totalCalls * 0.9 : 0; // Simulate previous period (10% less)
  const changePercentage = hasData && previousTotal !== 0 ? ((totalCalls - previousTotal) / previousTotal) * 100 : 0;
  const isPositiveChange = changePercentage > 0;
  
  // TODO: Calculate actual Avg. Call Duration and Unique Callers when API provides data
  const avgCallDuration = hasData ? "3:24" : "0:00"; // Placeholder, replace with real data or '-'
  const uniqueCallers = hasData ? 42 : 0; // Placeholder, replace with real data or '-'

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-gray-900 p-3 rounded-lg border border-gray-800 shadow-lg">
          <p className="text-gray-300 font-medium">{label}</p>
          {payload.map((entry, index) => (
            <p key={`item-${index}`} style={{ color: entry.color }} className="text-sm">
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };
  
  return (
    <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
      <div className="flex flex-col space-y-4">
        <div className="flex flex-wrap items-center justify-between gap-2">
          <h3 className="text-lg font-medium text-white flex items-center">
            <PhoneIcon className="h-5 w-5 mr-2 text-purple-400" />
            Call History
          </h3>
          
          {/* Tab selector */}
          <div className="flex bg-gray-800/50 rounded-lg p-1">
            <button
              onClick={() => setActiveTab('weekly')}
              className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                activeTab === 'weekly' 
                  ? 'bg-purple-600 text-white' 
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Weekly
            </button>
            <button
              onClick={() => setActiveTab('hourly')}
              className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                activeTab === 'hourly' 
                  ? 'bg-purple-600 text-white' 
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Hourly
            </button>
            <button
              onClick={() => setActiveTab('types')}
              className={`px-3 py-1.5 text-xs font-medium rounded-md ${
                activeTab === 'types' 
                  ? 'bg-purple-600 text-white' 
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Call Types
            </button>
          </div>
        </div>
        
        {/* Error message if applicable */}
        {error && (
          <div className="bg-red-500/10 border border-red-500/20 text-red-400 p-3 mb-4 rounded-lg text-sm">
            {error}
          </div>
        )}
        
        {/* Stats summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-gray-400 text-xs">Total Calls</p>
                <h4 className="text-2xl font-semibold text-white mt-1">{totalCalls}</h4>
              </div>
              {hasData && changePercentage !== 0 && (
                <div className={`flex items-center ${isPositiveChange ? 'text-green-400' : 'text-red-400'} text-sm`}>
                  {isPositiveChange ? (
                    <ArrowUpIcon className="h-4 w-4 mr-1" />
                  ) : (
                    <ArrowDownIcon className="h-4 w-4 mr-1" />
                  )}
                  {Math.abs(changePercentage).toFixed(1)}%
                </div>
              )}
            </div>
            <p className="text-gray-500 text-xs mt-2">{hasData ? 'vs previous period' : 'No comparison data'}</p>
          </div>
          
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
            <div className="flex justify-between">
              <div>
                <p className="text-gray-400 text-xs">Avg. Call Duration</p>
                {/* Display calculated or placeholder value */}
                <h4 className="text-2xl font-semibold text-white mt-1">{avgCallDuration}</h4>
              </div>
              <ClockIcon className="h-6 w-6 text-purple-400" />
            </div>
            <p className="text-gray-500 text-xs mt-2">{hasData ? 'minutes:seconds' : 'No data available'}</p>
          </div>
          
          <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
            <div className="flex justify-between">
              <div>
                <p className="text-gray-400 text-xs">Unique Callers</p>
                 {/* Display calculated or placeholder value */}
                <h4 className="text-2xl font-semibold text-white mt-1">{uniqueCallers}</h4>
              </div>
              <UserGroupIcon className="h-6 w-6 text-purple-400" />
            </div>
            <p className="text-gray-500 text-xs mt-2">{hasData ? 'this week' : 'No data available'}</p>
          </div>
        </div>
        
        {/* Chart area */}
        <div className="h-80 mt-2">
          {isLoading ? (
            <div className="h-full flex items-center justify-center">
              <div className="loading-pulse"></div>
            </div>
          ) : !hasData ? (
            <div className="h-full flex items-center justify-center text-gray-500">
              No call history data available for this period.
            </div>
          ) : (
            <>
              {activeTab === 'weekly' && weeklyData.length > 0 && (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={weeklyData}
                    margin={{ top: 10, right: 10, left: -20, bottom: 0 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" vertical={false} />
                    <XAxis 
                      dataKey="name" 
                      tick={{ fill: '#9CA3AF' }} 
                      axisLine={{ stroke: '#4B5563' }}
                      tickLine={{ stroke: '#4B5563' }}
                    />
                    <YAxis 
                      tick={{ fill: '#9CA3AF' }} 
                      axisLine={{ stroke: '#4B5563' }}
                      tickLine={{ stroke: '#4B5563' }}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Bar dataKey="answered" name="Answered" fill="#4ade80" radius={[4, 4, 0, 0]} />
                    <Bar dataKey="missed" name="Missed" fill="#f87171" radius={[4, 4, 0, 0]} />
                    <Bar dataKey="voicemail" name="Voicemail" fill="#60a5fa" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              )}
              
              {activeTab === 'hourly' && hourlyData.length > 0 && (
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={hourlyData}
                    margin={{ top: 10, right: 10, left: -20, bottom: 0 }}
                  >
                    <defs>
                      <linearGradient id="colorCalls" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0.1}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" vertical={false} />
                    <XAxis 
                      dataKey="hour" 
                      tick={{ fill: '#9CA3AF' }} 
                      axisLine={{ stroke: '#4B5563' }}
                      tickLine={{ stroke: '#4B5563' }}
                      interval={3}
                    />
                    <YAxis 
                      tick={{ fill: '#9CA3AF' }} 
                      axisLine={{ stroke: '#4B5563' }}
                      tickLine={{ stroke: '#4B5563' }}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Area 
                      type="monotone" 
                      dataKey="calls" 
                      stroke="#8b5cf6" 
                      fillOpacity={1} 
                      fill="url(#colorCalls)" 
                      name="Calls"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              )}
              
              {activeTab === 'types' && callTypeData.length > 0 && callTypeData.some(d => d.value > 0) && (
                <div className="h-full flex items-center justify-center">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={callTypeData}
                        cx="50%"
                        cy="50%"
                        innerRadius={80}
                        outerRadius={120}
                        paddingAngle={5}
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        labelLine={false}
                      >
                        {callTypeData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip content={<CustomTooltip />} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              )}
            </>
          )}
        </div>
        
        {/* Quick insights - Only show if there is data */}
        {hasData && (
          <div className="mt-4 bg-gray-800/30 rounded-lg p-4 border border-gray-700/20">
            <h4 className="text-sm font-medium text-white flex items-center mb-2">
              <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2 text-purple-400" />
            Quick Insights
          </h4>
          <ul className="text-sm text-gray-300 space-y-2">
            <li className="flex items-start">
              <span className="h-5 w-5 rounded-full bg-purple-500/20 flex items-center justify-center text-purple-400 mr-2 flex-shrink-0 mt-0.5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </span>
              <span>Most calls occur between <span className="text-purple-400 font-medium">10:00 AM and 2:00 PM</span></span>
            </li>
            <li className="flex items-start">
              <span className="h-5 w-5 rounded-full bg-purple-500/20 flex items-center justify-center text-purple-400 mr-2 flex-shrink-0 mt-0.5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </span>
              <span><span className="text-purple-400 font-medium">Wednesday</span> has the highest call volume this week</span>
            </li>
            <li className="flex items-start">
              <span className="h-5 w-5 rounded-full bg-purple-500/20 flex items-center justify-center text-purple-400 mr-2 flex-shrink-0 mt-0.5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </span>
              <span>Your call answer rate is <span className="text-green-400 font-medium">65%</span>, which is <span className="text-green-400">5%</span> higher than last week</span>
            </li>
          </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default CallHistoryVisualization;
