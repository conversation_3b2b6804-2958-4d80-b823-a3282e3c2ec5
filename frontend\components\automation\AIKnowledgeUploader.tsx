'use client';

import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { AIAssistantConfig } from './AutomationConfigPanel';
import LoadingSpinner from '../shared/LoadingSpinner';
import { DocumentTextIcon, TrashIcon, PlusIcon } from '@heroicons/react/24/outline';

interface AIKnowledgeUploaderProps {
  numberId: string;
  knowledgeSources: AIAssistantConfig['knowledgeSources'];
  onUpdate: (knowledgeSources: AIAssistantConfig['knowledgeSources']) => void;
  disabled?: boolean;
}

export default function AIKnowledgeUploader({
  numberId,
  knowledgeSources,
  onUpdate,
  disabled = false,
}: AIKnowledgeUploaderProps) {
  // State for file upload
  const [file, setFile] = useState<File | null>(null);
  const [textInput, setTextInput] = useState('');
  const [showTextInput, setShowTextInput] = useState(false);
  
  // Get the query client
  const queryClient = useQueryClient();

  // Mutation for uploading knowledge
  const uploadMutation = useMutation({
    mutationFn: async (data: { type: 'text' | 'file'; content: string | FormData }) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Generate a mock response
        const newSource = {
          id: `${Date.now()}`,
          name: data.type === 'file' 
            ? (file?.name || 'Uploaded File') 
            : `Text Input ${new Date().toLocaleDateString()}`,
          type: data.type,
          lastUpdated: new Date().toISOString(),
        };
        
        // Return mock success
        return { success: true, source: newSource };
      }
      
      // In production, call API
      const { data: response } = await axios.post(
        `/api/numbers/${numberId}/ai-assistant/train`,
        data.type === 'file' ? data.content : { type: data.type, content: data.content },
        data.type === 'file' ? { headers: { 'Content-Type': 'multipart/form-data' } } : {}
      );
      return response;
    },
    onSuccess: (data) => {
      // Update knowledge sources
      if (data.source) {
        const newSources = [...knowledgeSources, data.source];
        onUpdate(newSources);
      }
      
      // Reset form
      setFile(null);
      setTextInput('');
      setShowTextInput(false);
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['ai-assistant', numberId] });
      
      // Show success toast (you can use a toast library like react-hot-toast)
      console.log('Knowledge uploaded successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to upload knowledge');
    },
  });

  // Mutation for deleting knowledge
  const deleteMutation = useMutation({
    mutationFn: async (sourceId: string) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.delete(`/api/numbers/${numberId}/ai-assistant/knowledge/${sourceId}`);
      return data;
    },
    onSuccess: (_, sourceId) => {
      // Update knowledge sources
      const newSources = knowledgeSources.filter(source => source.id !== sourceId);
      onUpdate(newSources);
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['ai-assistant', numberId] });
      
      // Show success toast (you can use a toast library like react-hot-toast)
      console.log('Knowledge source deleted successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to delete knowledge source');
    },
  });

  // Handle file change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  // Handle file upload
  const handleFileUpload = () => {
    if (!file) return;
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'file');
    
    uploadMutation.mutate({ type: 'file', content: formData });
  };

  // Handle text upload
  const handleTextUpload = () => {
    if (!textInput.trim()) return;
    
    uploadMutation.mutate({ type: 'text', content: textInput });
  };

  // Handle delete
  const handleDelete = (sourceId: string) => {
    deleteMutation.mutate(sourceId);
  };

  return (
    <div className="space-y-6">
      {/* Knowledge Sources List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Knowledge Sources
          </h3>
        </div>
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {knowledgeSources.length === 0 ? (
            <li className="px-4 py-4 text-sm text-gray-500 dark:text-gray-400 text-center">
              No knowledge sources added yet.
            </li>
          ) : (
            knowledgeSources.map((source) => (
              <li key={source.id} className="px-4 py-3 flex items-center justify-between">
                <div className="flex items-center">
                  <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {source.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(source.lastUpdated).toLocaleDateString()} • {source.type}
                    </p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => handleDelete(source.id)}
                  disabled={disabled || deleteMutation.isPending}
                  className="text-gray-400 hover:text-red-500 dark:hover:text-red-400 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <TrashIcon className="h-5 w-5" />
                </button>
              </li>
            ))
          )}
        </ul>
      </div>

      {/* Upload Controls */}
      <div className="space-y-4">
        <div className="flex space-x-2">
          <button
            type="button"
            onClick={() => setShowTextInput(!showTextInput)}
            disabled={disabled || uploadMutation.isPending}
            className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Text
          </button>
          
          <label
            className={`inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 ${
              disabled || uploadMutation.isPending ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
            }`}
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Upload File
            <input
              type="file"
              className="hidden"
              onChange={handleFileChange}
              disabled={disabled || uploadMutation.isPending}
              accept=".txt,.pdf,.doc,.docx,.csv"
            />
          </label>
        </div>

        {/* Text Input */}
        {showTextInput && (
          <div className="space-y-3">
            <textarea
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              disabled={disabled || uploadMutation.isPending}
              placeholder="Enter knowledge base text here..."
              className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              rows={5}
            />
            <div className="flex justify-end">
              <button
                type="button"
                onClick={handleTextUpload}
                disabled={disabled || uploadMutation.isPending || !textInput.trim()}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {uploadMutation.isPending && uploadMutation.variables?.type === 'text' ? (
                  <>
                    <LoadingSpinner size="small" color="white" />
                    <span className="ml-2">Uploading...</span>
                  </>
                ) : (
                  'Upload Text'
                )}
              </button>
            </div>
          </div>
        )}

        {/* File Upload */}
        {file && (
          <div className="space-y-3">
            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md flex items-center">
              <DocumentTextIcon className="h-5 w-5 text-blue-500 dark:text-blue-400 mr-2" />
              <span className="text-sm text-blue-700 dark:text-blue-300 truncate">
                {file.name}
              </span>
            </div>
            <div className="flex justify-end">
              <button
                type="button"
                onClick={handleFileUpload}
                disabled={disabled || uploadMutation.isPending}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {uploadMutation.isPending && uploadMutation.variables?.type === 'file' ? (
                  <>
                    <LoadingSpinner size="small" color="white" />
                    <span className="ml-2">Uploading...</span>
                  </>
                ) : (
                  'Upload File'
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
