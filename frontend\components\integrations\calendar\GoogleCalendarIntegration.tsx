'use client';

import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { CalendarIcon, ArrowPathIcon, Cog6ToothIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from '../../shared/LoadingSpinner';

interface CalendarEvent {
  id: string;
  title: string;
  start: string;
  end: string;
  location?: string;
  attendees?: { name: string; email: string }[];
}

interface GoogleCalendarIntegrationProps {
  isConnected: boolean;
  accountInfo?: {
    name?: string;
    email?: string;
  };
}

export default function GoogleCalendarIntegration({
  isConnected,
  accountInfo,
}: GoogleCalendarIntegrationProps) {
  const [showSettings, setShowSettings] = useState(false);
  const queryClient = useQueryClient();

  // Fetch upcoming events
  const {
    data: events,
    isLoading: isLoadingEvents,
    error: eventsError,
  } = useQuery({
    queryKey: ['integrations', 'google-calendar', 'events'],
    queryFn: async () => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Return mock data
        return [
          {
            id: '1',
            title: 'Team Meeting',
            start: new Date(Date.now() + 1000 * 60 * 60 * 2).toISOString(), // 2 hours from now
            end: new Date(Date.now() + 1000 * 60 * 60 * 3).toISOString(), // 3 hours from now
            location: 'Conference Room A',
            attendees: [
              { name: 'John Doe', email: '<EMAIL>' },
              { name: 'Jane Smith', email: '<EMAIL>' },
            ],
          },
          {
            id: '2',
            title: 'Client Call',
            start: new Date(Date.now() + 1000 * 60 * 60 * 24).toISOString(), // 1 day from now
            end: new Date(Date.now() + 1000 * 60 * 60 * 24 + 1000 * 60 * 30).toISOString(), // 1 day and 30 minutes from now
            attendees: [
              { name: 'Client A', email: '<EMAIL>' },
            ],
          },
          {
            id: '3',
            title: 'Project Review',
            start: new Date(Date.now() + 1000 * 60 * 60 * 48).toISOString(), // 2 days from now
            end: new Date(Date.now() + 1000 * 60 * 60 * 48 + 1000 * 60 * 60).toISOString(), // 2 days and 1 hour from now
            location: 'Virtual',
          },
        ] as CalendarEvent[];
      }
      
      // In production, fetch from API
      const { data } = await axios.get<CalendarEvent[]>('/api/integrations/google-calendar/events');
      return data;
    },
    enabled: isConnected,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Connect to Google Calendar mutation
  const connectMutation = useMutation({
    mutationFn: async () => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // In a real app, this would redirect to Google OAuth
        // For now, we'll just simulate a successful connection
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.post('/api/integrations/google-calendar/connect');
      return data;
    },
    onSuccess: (data) => {
      // In a real app, this would handle the OAuth redirect
      // For now, we'll just invalidate the queries
      queryClient.invalidateQueries({ queryKey: ['integrations'] });
      
      // Show success toast
      console.log('Connected to Google Calendar successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to connect to Google Calendar');
    },
  });

  // Sync calendar mutation
  const syncMutation = useMutation({
    mutationFn: async () => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.post('/api/integrations/google-calendar/sync');
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['integrations', 'google-calendar', 'events'] });
      
      // Show success toast
      console.log('Synced Google Calendar successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to sync Google Calendar');
    },
  });

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString(undefined, {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
    });
  };

  // Handle connect button click
  const handleConnect = () => {
    connectMutation.mutate();
  };

  // Handle sync button click
  const handleSync = () => {
    syncMutation.mutate();
  };

  // If not connected, show connect button
  if (!isConnected) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center mb-4">
          <CalendarIcon className="h-8 w-8 text-blue-500 dark:text-blue-400" />
          <h2 className="ml-3 text-lg font-medium text-gray-900 dark:text-white">
            Google Calendar
          </h2>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Connect your Google Calendar to sync appointments and events with CallSaver.
        </p>
        <button
          type="button"
          onClick={handleConnect}
          disabled={connectMutation.isPending}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {connectMutation.isPending ? (
            <>
              <LoadingSpinner size="small" color="white" />
              <span className="ml-2">Connecting...</span>
            </>
          ) : (
            'Connect to Google Calendar'
          )}
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div className="p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CalendarIcon className="h-8 w-8 text-blue-500 dark:text-blue-400" />
            <div className="ml-3">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                Google Calendar
              </h2>
              {accountInfo?.email && (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {accountInfo.email}
                </p>
              )}
            </div>
          </div>
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={() => setShowSettings(!showSettings)}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
            >
              <Cog6ToothIcon className="h-4 w-4 mr-1" />
              Settings
            </button>
            <button
              type="button"
              onClick={handleSync}
              disabled={syncMutation.isPending}
              className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {syncMutation.isPending ? (
                <>
                  <LoadingSpinner size="small" color="gray" />
                  <span className="ml-2">Syncing...</span>
                </>
              ) : (
                <>
                  <ArrowPathIcon className="h-4 w-4 mr-1" />
                  Sync Now
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {showSettings ? (
        <div className="p-4 sm:p-6">
          <GoogleCalendarSettings />
        </div>
      ) : (
        <div className="p-4 sm:p-6">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
            Upcoming Events
          </h3>

          {isLoadingEvents ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="lg" color="blue" />
            </div>
          ) : eventsError ? (
            <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-md">
              <p className="text-sm text-red-700 dark:text-red-200">
                Failed to load events. Please try again later.
              </p>
            </div>
          ) : events && events.length > 0 ? (
            <div className="overflow-hidden rounded-md border border-gray-200 dark:border-gray-700">
              <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                {events.map((event) => (
                  <li key={event.id} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700">
                    <div className="flex justify-between">
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                          {event.title}
                        </h4>
                        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                          {formatDate(event.start)} - {formatDate(event.end)}
                        </p>
                        {event.location && (
                          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            Location: {event.location}
                          </p>
                        )}
                      </div>
                      {event.attendees && event.attendees.length > 0 && (
                        <div className="text-right">
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {event.attendees.length} attendee{event.attendees.length !== 1 ? 's' : ''}
                          </p>
                        </div>
                      )}
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                No upcoming events found.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

function GoogleCalendarSettings() {
  const [syncFrequency, setSyncFrequency] = useState('hourly');
  const [calendarIds, setCalendarIds] = useState(['primary']);
  const [lookAheadDays, setLookAheadDays] = useState('30');
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  // Mock calendars
  const availableCalendars = [
    { id: 'primary', name: 'Primary Calendar' },
    { id: 'work', name: 'Work Calendar' },
    { id: 'personal', name: 'Personal Calendar' },
    { id: 'family', name: 'Family Calendar' },
  ];

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would save the settings to the server
    console.log('Settings saved:', {
      syncFrequency,
      calendarIds,
      lookAheadDays,
      notificationsEnabled,
    });
  };

  // Handle calendar selection
  const handleCalendarToggle = (calendarId: string) => {
    if (calendarIds.includes(calendarId)) {
      setCalendarIds(calendarIds.filter(id => id !== calendarId));
    } else {
      setCalendarIds([...calendarIds, calendarId]);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Sync Settings
        </h3>
        <div className="space-y-4">
          <div>
            <label htmlFor="syncFrequency" className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
              Sync Frequency
            </label>
            <select
              id="syncFrequency"
              value={syncFrequency}
              onChange={(e) => setSyncFrequency(e.target.value)}
              className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="realtime">Real-time</option>
              <option value="hourly">Hourly</option>
              <option value="daily">Daily</option>
              <option value="manual">Manual only</option>
            </select>
          </div>

          <div>
            <label htmlFor="lookAheadDays" className="block text-sm text-gray-600 dark:text-gray-400 mb-1">
              Look Ahead Days
            </label>
            <select
              id="lookAheadDays"
              value={lookAheadDays}
              onChange={(e) => setLookAheadDays(e.target.value)}
              className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="7">7 days</option>
              <option value="14">14 days</option>
              <option value="30">30 days</option>
              <option value="60">60 days</option>
              <option value="90">90 days</option>
            </select>
          </div>

          <div className="flex items-center">
            <input
              id="notificationsEnabled"
              type="checkbox"
              checked={notificationsEnabled}
              onChange={(e) => setNotificationsEnabled(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-700 rounded"
            />
            <label htmlFor="notificationsEnabled" className="ml-2 block text-sm text-gray-600 dark:text-gray-400">
              Enable notifications for calendar events
            </label>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Calendars to Sync
        </h3>
        <div className="space-y-2">
          {availableCalendars.map((calendar) => (
            <div key={calendar.id} className="flex items-center">
              <input
                id={`calendar-${calendar.id}`}
                type="checkbox"
                checked={calendarIds.includes(calendar.id)}
                onChange={() => handleCalendarToggle(calendar.id)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-700 rounded"
              />
              <label htmlFor={`calendar-${calendar.id}`} className="ml-2 block text-sm text-gray-600 dark:text-gray-400">
                {calendar.name}
              </label>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-end">
        <button
          type="submit"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
        >
          Save Settings
        </button>
      </div>
    </form>
  );
}
