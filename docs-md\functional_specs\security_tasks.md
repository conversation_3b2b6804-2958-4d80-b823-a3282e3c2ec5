---
description:
globs:
alwaysApply: false
---
# Security Tasks (`security_tasks.mdc`)

## Task Queue

| Task Description                                                                 | Priority | Target File/Component                | Dependencies | Status    |
|----------------------------------------------------------------------------------|----------|--------------------------------------|--------------|-----------|
| Enforce HMAC validation, replay protection, IP allowlisting on all webhooks      | High     | back/backend/middleware/webhookSecurity.js | None         | COMPLETED |
| Require auth middleware on every route + mapping in API gateway                  | High     | back/backend/routes/*, api_gateway_routes.mdc | None         | COMPLETED |
| Validate/sanitize all user input at middleware and controller levels             | High     | back/backend/middleware/inputValidator.js | None         | COMPLETED |
| Patch privilege escalation by mapping roles to actions explicitly                | High     | access_control/role_map.js           | None         | TODO      |

## Related Documents
- [api_gateway_routes.mdc](mdc:api_gateway_routes.mdc)
- [idempotency_tasks.mdc](mdc:idempotency_tasks.mdc)
- [cleanup_tasks.mdc](mdc:cleanup_tasks.mdc)