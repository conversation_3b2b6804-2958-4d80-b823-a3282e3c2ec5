'use client'; // Mark this as a Client Component

import dynamic from 'next/dynamic';

// Dynamically import components with no SSR and export them
export const ErrorBoundary = dynamic(() => import('./ErrorBoundary'), { ssr: false });
export const PerformanceOptimizer = dynamic(() => import('./PerformanceOptimizer'), { ssr: false });
export const ConditionalNavbar = dynamic(() => import('./ConditionalNavbar'), { ssr: false });
export const WebVitalsInit = dynamic(() => import('./WebVitalsInit'), { ssr: false });

// This component doesn't need to render anything itself.
// We just use it as a place to perform the dynamic imports within a client context.
export default function DynamicImportsPlaceholder() {
  return null; // Or <></>
}
