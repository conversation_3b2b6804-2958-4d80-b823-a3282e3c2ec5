# Permission System Testing Guide

This guide provides instructions for manually testing the permission-based access control system using a local development server.

## Prerequisites

- Node.js and npm installed
- CallSaver backend repository cloned
- Dependencies installed (`npm install`)
- Database configured and running

## Running Automated Tests

Before manual testing, run the automated tests to ensure the permission system is working correctly:

```bash
node scripts/test-permissions.js
```

This script will run the following tests:
- Unit tests for the access control service
- Unit tests for the permission middleware
- Integration tests for permission-based access control
- Integration tests for the updated routes

## Setting Up for Manual Testing

1. Start the local development server:

```bash
npm run dev
```

2. Create test users with different roles:

```bash
# Create an admin user
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Password123!","name":"Admin User","role":"admin"}'

# Create a member user
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Password123!","name":"Member User","role":"member"}'

# Create a developer user
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Password123!","name":"Developer User","role":"developer"}'

# Create a viewer user
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Password123!","name":"Viewer User","role":"viewer"}'
```

3. Log in with each user to get authentication tokens:

```bash
# Log in as admin
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Password123!"}' \
  -o admin-token.json

# Log in as member
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Password123!"}' \
  -o member-token.json

# Log in as developer
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Password123!"}' \
  -o developer-token.json

# Log in as viewer
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Password123!"}' \
  -o viewer-token.json
```

4. Extract the tokens from the JSON responses:

```bash
ADMIN_TOKEN=$(cat admin-token.json | jq -r '.token')
MEMBER_TOKEN=$(cat member-token.json | jq -r '.token')
DEVELOPER_TOKEN=$(cat developer-token.json | jq -r '.token')
VIEWER_TOKEN=$(cat viewer-token.json | jq -r '.token')
```

## Test Cases

### 1. Security Routes

#### Audit Logs (Admin Only)

```bash
# Admin should have access
curl -X GET http://localhost:3000/api/security/audit-logs \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# Member should not have access
curl -X GET http://localhost:3000/api/security/audit-logs \
  -H "Authorization: Bearer $MEMBER_TOKEN"

# Developer should not have access
curl -X GET http://localhost:3000/api/security/audit-logs \
  -H "Authorization: Bearer $DEVELOPER_TOKEN"

# Viewer should not have access
curl -X GET http://localhost:3000/api/security/audit-logs \
  -H "Authorization: Bearer $VIEWER_TOKEN"
```

#### Rate Limit Bypass Rules (Admin Only)

```bash
# Admin should have access
curl -X GET http://localhost:3000/api/security/rate-limit-bypass \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# Member should not have access
curl -X GET http://localhost:3000/api/security/rate-limit-bypass \
  -H "Authorization: Bearer $MEMBER_TOKEN"
```

### 2. Tenant Routes

#### Create Tenant (Admin Only)

```bash
# Admin should be able to create a tenant
curl -X POST http://localhost:3000/api/tenants \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Tenant"}'

# Member should not be able to create a tenant
curl -X POST http://localhost:3000/api/tenants \
  -H "Authorization: Bearer $MEMBER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Tenant"}'
```

#### Update Tenant (Admin Only)

```bash
# Admin should be able to update a tenant
curl -X PUT http://localhost:3000/api/tenants/tenant1 \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"Updated Tenant"}'

# Member should not be able to update a tenant
curl -X PUT http://localhost:3000/api/tenants/tenant1 \
  -H "Authorization: Bearer $MEMBER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"Updated Tenant"}'
```

### 3. Blocked IP Routes

#### Get Blocked IPs (Admin Only)

```bash
# Admin should have access
curl -X GET http://localhost:3000/api/ip-blocklist \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# Member should not have access
curl -X GET http://localhost:3000/api/ip-blocklist \
  -H "Authorization: Bearer $MEMBER_TOKEN"
```

#### Add Blocked IP (Admin Only)

```bash
# Admin should be able to add a blocked IP
curl -X POST http://localhost:3000/api/ip-blocklist \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"ipAddress":"***********"}'

# Member should not be able to add a blocked IP
curl -X POST http://localhost:3000/api/ip-blocklist \
  -H "Authorization: Bearer $MEMBER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"ipAddress":"***********"}'
```

### 4. Queue Routes

#### Get Queues (Admin Only)

```bash
# Admin should have access
curl -X GET http://localhost:3000/api/queues \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# Member should not have access
curl -X GET http://localhost:3000/api/queues \
  -H "Authorization: Bearer $MEMBER_TOKEN"
```

### 5. Developer Routes

#### Developer Profile (Developer Only)

```bash
# Developer should have access
curl -X GET http://localhost:3000/api/developer/profile \
  -H "Authorization: Bearer $DEVELOPER_TOKEN"

# Member should not have access
curl -X GET http://localhost:3000/api/developer/profile \
  -H "Authorization: Bearer $MEMBER_TOKEN"

# Admin should have access (admin has all permissions)
curl -X GET http://localhost:3000/api/developer/profile \
  -H "Authorization: Bearer $ADMIN_TOKEN"
```

## Testing Edge Cases

### 1. Missing Authentication

```bash
# Should return 401 Unauthorized
curl -X GET http://localhost:3000/api/security/audit-logs
```

### 2. Invalid Token

```bash
# Should return 401 Unauthorized
curl -X GET http://localhost:3000/api/security/audit-logs \
  -H "Authorization: Bearer invalid-token"
```

### 3. Malformed Token

```bash
# Should return 401 Unauthorized
curl -X GET http://localhost:3000/api/security/audit-logs \
  -H "Authorization: malformed-token"
```

## Verifying Results

For each test case, verify that:

1. Admin users can access all routes
2. Non-admin users can only access routes they have permission for
3. Unauthorized access attempts return a 403 Forbidden status code
4. Unauthenticated access attempts return a 401 Unauthorized status code

## Troubleshooting

If you encounter issues during testing:

1. Check the server logs for error messages
2. Verify that the tokens are valid and not expired
3. Check that the user roles are correctly set in the database
4. Verify that the permission mappings in `config/rolePermissions.js` are correct
5. Check that the middleware is correctly applied to the routes

## Conclusion

This testing guide provides a comprehensive approach to manually testing the permission-based access control system. By following these steps, you can verify that the system correctly restricts access to routes based on user roles and permissions.
