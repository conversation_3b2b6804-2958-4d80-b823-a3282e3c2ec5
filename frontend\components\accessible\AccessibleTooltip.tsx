import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { twMerge } from 'tailwind-merge';

export interface AccessibleTooltipProps {
  /**
   * The content of the tooltip
   */
  content: React.ReactNode;
  
  /**
   * The children to attach the tooltip to
   */
  children: React.ReactElement;
  
  /**
   * The position of the tooltip
   */
  position?: 'top' | 'right' | 'bottom' | 'left';
  
  /**
   * The delay before showing the tooltip (in ms)
   */
  delay?: number;
  
  /**
   * Whether the tooltip is disabled
   */
  disabled?: boolean;
  
  /**
   * Additional CSS classes to apply to the tooltip
   */
  className?: string;
  
  /**
   * Whether to render the tooltip in a portal
   */
  usePortal?: boolean;
  
  /**
   * The ID of the element to render the portal in
   */
  portalId?: string;
}

/**
 * An accessible tooltip component with proper ARIA attributes.
 */
export const AccessibleTooltip: React.FC<AccessibleTooltipProps> = ({
  content,
  children,
  position = 'top',
  delay = 300,
  disabled = false,
  className,
  usePortal = true,
  portalId = 'tooltip-root',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const triggerRef = useRef<HTMLElement | null>(null);
  const tooltipRef = useRef<HTMLDivElement | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Generate a unique ID for the tooltip
  const tooltipId = useRef(`tooltip-${Math.random().toString(36).substr(2, 9)}`);
  
  // Position the tooltip
  const updateTooltipPosition = () => {
    if (!triggerRef.current || !tooltipRef.current) return;
    
    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    
    let top = 0;
    let left = 0;
    
    switch (position) {
      case 'top':
        top = triggerRect.top - tooltipRect.height - 8;
        left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;
        break;
      case 'right':
        top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;
        left = triggerRect.right + 8;
        break;
      case 'bottom':
        top = triggerRect.bottom + 8;
        left = triggerRect.left + triggerRect.width / 2 - tooltipRect.width / 2;
        break;
      case 'left':
        top = triggerRect.top + triggerRect.height / 2 - tooltipRect.height / 2;
        left = triggerRect.left - tooltipRect.width - 8;
        break;
    }
    
    // Adjust for scroll position
    top += window.scrollY;
    left += window.scrollX;
    
    // Keep the tooltip within the viewport
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    if (left < 8) {
      left = 8;
    } else if (left + tooltipRect.width > viewportWidth - 8) {
      left = viewportWidth - tooltipRect.width - 8;
    }
    
    if (top < 8) {
      top = 8;
    } else if (top + tooltipRect.height > viewportHeight - 8) {
      top = viewportHeight - tooltipRect.height - 8;
    }
    
    setTooltipPosition({ top, left });
  };
  
  // Show the tooltip
  const showTooltip = () => {
    if (disabled) return;
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      // Update position after the tooltip is visible
      setTimeout(updateTooltipPosition, 0);
    }, delay);
  };
  
  // Hide the tooltip
  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    
    setIsVisible(false);
  };
  
  // Update position when window is resized
  useEffect(() => {
    if (!isVisible) return;
    
    const handleResize = () => {
      updateTooltipPosition();
    };
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleResize);
    };
  }, [isVisible]);
  
  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  // Clone the child element to add event handlers and aria attributes
  const triggerElement = React.cloneElement(children, {
    ref: (node: HTMLElement | null) => {
      triggerRef.current = node;
      
      // Call the original ref if it exists
      const { ref } = children as any;
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    },
    onMouseEnter: (e: React.MouseEvent) => {
      showTooltip();
      
      // Call the original onMouseEnter if it exists
      if (children.props.onMouseEnter) {
        children.props.onMouseEnter(e);
      }
    },
    onMouseLeave: (e: React.MouseEvent) => {
      hideTooltip();
      
      // Call the original onMouseLeave if it exists
      if (children.props.onMouseLeave) {
        children.props.onMouseLeave(e);
      }
    },
    onFocus: (e: React.FocusEvent) => {
      showTooltip();
      
      // Call the original onFocus if it exists
      if (children.props.onFocus) {
        children.props.onFocus(e);
      }
    },
    onBlur: (e: React.FocusEvent) => {
      hideTooltip();
      
      // Call the original onBlur if it exists
      if (children.props.onBlur) {
        children.props.onBlur(e);
      }
    },
    'aria-describedby': isVisible ? tooltipId.current : undefined,
  });
  
  // Create the tooltip content
  const tooltipContent = isVisible ? (
    <div
      ref={tooltipRef}
      id={tooltipId.current}
      role="tooltip"
      className={twMerge(
        'absolute z-50 px-2 py-1 text-sm text-white bg-gray-900 rounded shadow-lg',
        className
      )}
      style={{
        top: `${tooltipPosition.top}px`,
        left: `${tooltipPosition.left}px`,
      }}
    >
      {content}
      <div
        className={twMerge(
          'absolute w-2 h-2 bg-gray-900 transform rotate-45',
          position === 'top' && 'bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2',
          position === 'right' && 'left-0 top-1/2 -translate-y-1/2 -translate-x-1/2',
          position === 'bottom' && 'top-0 left-1/2 -translate-x-1/2 -translate-y-1/2',
          position === 'left' && 'right-0 top-1/2 -translate-y-1/2 translate-x-1/2'
        )}
      />
    </div>
  ) : null;
  
  // Render the tooltip
  return (
    <>
      {triggerElement}
      {isVisible &&
        (usePortal ? (
          createPortal(
            tooltipContent,
            document.getElementById(portalId) || document.body
          )
        ) : (
          tooltipContent
        ))}
    </>
  );
};

export default AccessibleTooltip;
