'use client';

import { useState, useEffect, useRef } from 'react';
import getSupabaseClient from '../../utils/supabaseClient'; // Import the function
// NOTE: Assuming useMcpTool hook is available globally or via context.
// If not, direct XML usage is needed in the function.
// import { useMcpTool } from '@modelcontextprotocol/react'; // Hypothetical import

// Placeholder icons (replace with actual icons later)
const PaperAirplaneIcon = () => <span className="mr-2">✈️</span>;
const PaperClipIcon = () => <span className="mr-2">📎</span>;
const CameraIcon = () => <span className="mr-2">📷</span>;
const ArrowDownTrayIcon = () => <span className="mr-2">📥</span>;
const KeyIcon = () => <span className="mr-2">🔑</span>;

export default function AIInteractionBox({ selectedPhoneNumber }) {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const messagesEndRef = useRef(null);
  const [session, setSession] = useState(null);
  const [isUploadingFile, setIsUploadingFile] = useState(false);
  const [uploadError, setUploadError] = useState('');
  const fileInputRef = useRef(null);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const imageInputRef = useRef(null);
  
  // User identification for data isolation
  const [userId, setUserId] = useState('');

  // Scroll to bottom whenever messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Generate or retrieve user ID on mount
  useEffect(() => {
    // Check if we have a user ID in localStorage
    let storedUserId = localStorage.getItem('callsaver_user_id');
    
    // If not, generate a new one
    if (!storedUserId) {
      storedUserId = `user_${Math.random().toString(36).substring(2, 15)}`;
      localStorage.setItem('callsaver_user_id', storedUserId);
    }
    
    setUserId(storedUserId);
    console.log(`[AIInteractionBox] User ID: ${storedUserId}`);
  }, []);

  // Get session on mount
  useEffect(() => {
    const supabase = getSupabaseClient(); // Get client instance
    if (!supabase) {
      console.error("AIInteractionBox: Failed to get Supabase client.");
      return; // Exit if client is not available
    }

    const getSupabaseSession = async () => {
      const { data } = await supabase.auth.getSession();
      setSession(data.session);
    };
    getSupabaseSession();

    // Listen for auth changes
    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
      setSession(session);
    });

    return () => {
      authListener?.subscription?.unsubscribe();
    };
  }, []);

  // Load chat history when selectedPhoneNumber changes
  useEffect(() => {
    const loadHistory = async () => {
      if (!selectedPhoneNumber) {
        setMessages([]);
        setIsLoadingHistory(false);
        return;
      }

      setIsLoadingHistory(true);
      console.log(`[AIInteractionBox] Fetching history for ${selectedPhoneNumber}...`);
      try {
        // First check if we have a localStorage history for this user and phone
        const localKey = `chat_history_${userId}_${selectedPhoneNumber}`;
        const localHistory = localStorage.getItem(localKey);
        
        if (localHistory) {
          try {
            const parsedHistory = JSON.parse(localHistory);
            if (Array.isArray(parsedHistory) && parsedHistory.length > 0) {
              setMessages(parsedHistory);
              setIsLoadingHistory(false);
              console.log(`[AIInteractionBox] Loaded ${parsedHistory.length} messages from localStorage for ${selectedPhoneNumber}`);
              return;
            }
          } catch (err) {
            console.warn('Failed to parse local history:', err);
            // Continue with empty history
          }
        }
      
        // Initialize with a welcome message
        setMessages([{
          id: 'welcome',
          sender: 'ai',
          text: `Hello! I'm your CallSaver AI assistant for phone number ${selectedPhoneNumber}. How can I help you today?`
        }]);
        
      } catch (error) {
        console.error('[AIInteractionBox] Error loading history:', error);
        setMessages([
          { id: 'err-hist', sender: 'ai', text: `Error loading history: ${error.message}`, isError: true }
        ]);
      } finally {
        setIsLoadingHistory(false);
      }
    };

    loadHistory();

  }, [selectedPhoneNumber, userId]);

  // Handle sending a message OR slash command
  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isSending || !selectedPhoneNumber) return;

    const currentInput = inputMessage.trim();
    const userMessage = { id: Date.now(), sender: 'user', text: currentInput };

    setInputMessage(''); // Clear input immediately
    setMessages(prev => [...prev, userMessage]); // Add user message to UI
    setIsSending(true); // Disable input

    // Check for /call command with proper format
    const callCommandMatch = currentInput.match(/^\/call\s+(\+?[\d\s\-()]+)\s+Say:\s+(.+)/i);
    
    // Also check for improper format to provide feedback
    const invalidCallCommand = currentInput.startsWith('/call') && !callCommandMatch;

    if (callCommandMatch) {
      const targetNumber = callCommandMatch[1].trim();
      const messageToSay = callCommandMatch[2].trim();
      
      // Don't add user message again, already added above
      await handleInitiateCall(targetNumber, messageToSay); // handleInitiateCall will set isSending false
    } 
    else if (invalidCallCommand) {
      // Provide helpful feedback for incorrect call command format
      setMessages(prev => [...prev, {
        id: Date.now() + 1,
        sender: 'system',
        text: `Invalid call command format. Please use: /call +********** Say: Your message`,
        isError: true
      }]);
      setIsSending(false);
    }
    else {
      // Regular chat message - use the shared OpenAI API key
      console.log(`[AIInteractionBox] Sending message to AI for ${selectedPhoneNumber} with prompt: ${currentInput.substring(0, 50)}...`);

      try {
        // Get previous messages, limited to last 10 for context
        const messageHistory = messages.slice(-10);
        
        // Call our API with the userId for data isolation
        const response = await fetch('/api/automation/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            phoneNumber: selectedPhoneNumber,
            message: currentInput,
            messageHistory,
            userId // Include user ID for data isolation
          }),
        });

        const data = await response.json();

        if (!response.ok || !data.success) {
          throw new Error(data.message || 'Failed to get AI response');
        }

        // Add AI response to chat
        const aiResponse = {
          id: Date.now() + 1,
          sender: 'ai',
          text: data.response
        };
        setMessages(prev => [...prev, aiResponse]);

        // Save chat history to localStorage with user-specific key
        const updatedMessages = [...messages, userMessage, aiResponse];
        const localKey = `chat_history_${userId}_${selectedPhoneNumber}`;
        localStorage.setItem(localKey, JSON.stringify(updatedMessages));
        
      } catch (error) {
        console.error('[AIInteractionBox] Error getting AI response:', error);
        const errorResponse = {
          id: Date.now() + 1,
          sender: 'ai',
          text: `Error: ${error.message}`,
          isError: true
        };
        setMessages(prev => [...prev, errorResponse]);
      } finally {
        setIsSending(false);
      }
    }
  };

  // Handle initiating a call via backend
  const handleInitiateCall = async (targetNumber, messageToSay) => {
    // isSending is already true from handleSendMessage
    console.log(`[AIInteractionBox] Initiating call from ${selectedPhoneNumber} to ${targetNumber} saying: "${messageToSay}"`);
    
    try {
      // Add system message indicating call is being initiated
      setMessages(prev => [...prev, {
        id: Date.now(),
        sender: 'system',
        text: `Initiating call from ${selectedPhoneNumber} to ${targetNumber}...`
      }]);
      
      // Make the actual API call to the backend
      const response = await fetch('/api/automation/initiate-call', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': session?.access_token ? `Bearer ${session.access_token}` : '',
        },
        body: JSON.stringify({
          fromNumber: selectedPhoneNumber,
          toNumber: targetNumber,
          message: messageToSay,
        }),
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || 'Failed to initiate call');
      }
      
      const callSid = data.callSid || 'unknown';
      const status = data.status || 'unknown';
      
      // Add system message with call details
      setMessages(prev => [...prev, {
        id: Date.now() + 1,
        sender: 'system',
        text: `Call to ${targetNumber} has been initiated. Call ID: ${callSid}, Status: ${status}`
      }]);
      
      // Add AI response about the call after a short delay
      setTimeout(() => {
        const statusMessage = status === 'queued' ? 
          'The call has been queued and will connect shortly.' : 
          status === 'ringing' ? 
          'The phone is now ringing.' : 
          `The call status is "${status}".`;
          
        setMessages(prev => [...prev, {
          id: Date.now() + 3,
          sender: 'ai',
          text: `I've placed a call to ${targetNumber} with your message: "${messageToSay}". ${statusMessage} Would you like to do anything else?`
        }]);
      }, 1000);
      
      console.log(`[AIInteractionBox] Call initiation API response:`, data);

    } catch (error) {
      console.error('[AIInteractionBox] Error initiating call:', error);
      
      // Check for Twilio-specific errors
      const isTwilioError = error.message?.includes('Twilio');
      
      // Add system error message
      setMessages(prev => [...prev, {
        id: Date.now() + 1,
        sender: 'system',
        text: `Error initiating call to ${targetNumber}: ${error.message}`,
        isError: true
      }]);
      
      // Add helpful AI message to guide the user
      setTimeout(() => {
        setMessages(prev => [...prev, {
          id: Date.now() + 2,
          sender: 'ai',
          text: isTwilioError ?
            `I encountered an issue with the Twilio service. This might be due to account limits, an invalid phone number format, or another Twilio-specific issue. Please check the error message and try again.` :
            `I encountered an issue while trying to make the call. Please check the number format and try again. Remember to use the format: /call +********** Say: Your message`
        }]);
      }, 1000);
    } finally {
      setIsSending(false); // Re-enable input
    }
  };

  // Handle file input change
  const handleFileSelected = async (event) => {
    const file = event.target.files[0];
    // Reset file input to allow uploading the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    if (!file || !selectedPhoneNumber) return;

    setIsUploadingFile(true);
    setUploadError('');
    console.log(`[AIInteractionBox] Uploading file for ${selectedPhoneNumber}: ${file.name}`);

    // Add a system message to chat indicating upload started
    setMessages(prev => [...prev, {
      id: Date.now(),
      sender: 'system',
      text: `Uploading ${file.name} for training...`,
    }]);

    const formData = new FormData();
    formData.append('file', file);
    formData.append('phoneNumber', selectedPhoneNumber); // Send phone number for backend association

    try {
      const response = await fetch('/api/automation/upload-doc', {
        method: 'POST',
        headers: {
          // No 'Content-Type', let browser set it for FormData
          'Authorization': `Bearer ${session?.access_token}`,
        },
        body: formData,
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || 'File upload failed');
      }

      // Add a system message to chat indicating success
      setMessages(prev => [...prev, {
        id: Date.now(),
        sender: 'system',
        text: `Successfully uploaded: ${file.name}. It will be processed for training ${selectedPhoneNumber}.`,
      }]);

      // Add AI response suggesting next steps
      setMessages(prev => [...prev, {
        id: Date.now() + 1,
        sender: 'ai',
        text: `I've received your document "${file.name}". This will help me learn how to respond to inquiries about this content. You can upload more documents if needed, or click the "Retrain Now" button above to start the training process.`,
      }]);

      console.log(`[AIInteractionBox] File upload successful: ${file.name} for ${selectedPhoneNumber}`);

    } catch (error) {
      console.error('[AIInteractionBox] File upload error:', error);
      setUploadError(`Upload failed: ${error.message}`);
      // Add error message to chat
      setMessages(prev => [...prev, {
        id: Date.now(),
        sender: 'system',
        text: `Error uploading ${file.name}: ${error.message}`,
        isError: true,
      }]);
    } finally {
      setIsUploadingFile(false);
    }
  };

  // Trigger file input click
  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  // Handle image input change
  const handleImageSelected = async (event) => {
    const file = event.target.files[0];
    // Reset file input
    if (imageInputRef.current) {
      imageInputRef.current.value = '';
    }

    if (!file || !selectedPhoneNumber) return;

    // Basic type check (can be more robust)
    if (!file.type.startsWith('image/')) {
        setUploadError('Invalid file type. Please select an image.');
        return;
    }

    setIsUploadingImage(true);
    setUploadError('');
    console.log(`[AIInteractionBox] Selected image for ${selectedPhoneNumber}: ${file.name}`);

    // Placeholder: Show message in chat
    setMessages(prev => [...prev, {
      id: Date.now(),
      sender: 'system',
      text: `Image selected: ${file.name}. Processing coming soon.`,
    }]);

    // TODO: Implement actual image upload logic to backend
    // const formData = new FormData();
    // formData.append('image', file);
    // formData.append('phoneNumber', selectedPhoneNumber);
    // ... fetch('/api/automation/upload-image', ...) ...

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 500));

    setIsUploadingImage(false);
  };

  // Trigger image input click
  const triggerImageInput = () => {
    imageInputRef.current?.click();
  };

  return (
    <div className="flex flex-col h-[600px] bg-gray-900/50 border border-gray-800 rounded-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gray-900 p-4 flex justify-between items-center border-b border-gray-800">
        <div>
          <h3 className="text-white font-medium">Chat Assistant</h3>
          {selectedPhoneNumber ? (
            <p className="text-gray-400 text-sm">{selectedPhoneNumber}</p>
          ) : (
            <p className="text-red-400 text-sm">No phone number selected</p>
          )}
        </div>
        
        {/* Connected status and user ID badge */}
        <div className="flex items-center">
          <span className="text-green-400 text-xs flex items-center mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            CallSaver AI Connected
          </span>
          
          <div className="bg-gray-800 text-gray-300 text-xs py-1 px-2 rounded-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
            </svg>
            User ID: {userId.substring(0, 8)}...
          </div>
        </div>
      </div>
      
      {/* Message Display Area */}
      <div className="flex-grow overflow-y-auto mb-4 border border-gray-700 rounded-lg p-4 bg-gray-800/50 custom-scrollbar">
        {/* Real calls notification */}
        <div className="mb-4 p-2 bg-green-900/30 border border-green-700 rounded-lg text-green-100 text-xs">
          <p className="font-semibold">Twilio calling enabled</p>
          <p>Calls will be placed using your Twilio account. Standard Twilio charges may apply.</p>
        </div>
        
        {isLoadingHistory ? (
          <div className="flex justify-center items-center h-full">
            <p className="text-gray-400 italic">Loading chat history...</p>
          </div>
        ) : messages.length === 0 ? (
           <div className="flex justify-center items-center h-full">
             <p className="text-gray-400 italic">No messages yet. Start the conversation!</p>
           </div>
        ) : (
          <div className="space-y-4">
            {messages.map((msg) => (
              <div key={msg.id} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div
                  className={`max-w-[75%] px-4 py-2 rounded-lg ${
                    msg.isError ? 'bg-red-700/80 text-red-100' :
                    msg.isWarning ? 'bg-yellow-700/80 text-yellow-100' :
                    msg.sender === 'user' ? 'bg-purple-600 text-white' : 
                    msg.sender === 'system' ? 'bg-blue-700/80 text-blue-100' : 'bg-gray-700 text-gray-200'
                  }`}
                >
                  {/* Basic rendering of text - could be enhanced for markdown/code */}
                  {typeof msg.text === 'string' ? msg.text.split('\n').map((line, index) => (
                    <span key={index}>{line}<br/></span>
                  )) : JSON.stringify(msg.text)}
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} /> {/* Anchor for scrolling */}
          </div>
        )}
      </div>

      {/* Upload Error Display */}
      {uploadError && (
        <div className="my-2 p-2 bg-red-900/40 border border-red-700 rounded-lg text-red-200 text-sm">
          {uploadError}
        </div>
      )}

      {/* Input Area */}
      <div className="p-4 border-t border-gray-800 bg-gray-900/80">
        {/* Command helper above input */}
        <div className="mb-2 px-2 py-1 bg-gray-800/50 rounded-lg border border-gray-700 text-xs text-gray-400">
          <p className="font-medium text-purple-400 mb-1">Available Commands:</p>
          <p><span className="bg-gray-700 px-1 py-0.5 rounded text-white font-mono">/call +********** Say: Your message</span> - Make a call to a number</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
            placeholder={selectedPhoneNumber ? "Type a message or use /call command..." : "Please select a phone number first"}
            disabled={!selectedPhoneNumber || isSending || isLoadingHistory}
            className="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
          />
          
          {/* Quick Call Button */}
          <button
            onClick={() => {
              setInputMessage(prev => prev.startsWith('/call') ? prev : '/call +1 Say: ');
            }}
            disabled={!selectedPhoneNumber || isSending || isLoadingHistory}
            title="Quick call command"
            className="p-2 bg-blue-600 rounded-lg text-white hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:hover:bg-blue-600"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
            </svg>
          </button>
          
          <button
            onClick={handleSendMessage}
            disabled={!selectedPhoneNumber || isSending || !inputMessage.trim() || isLoadingHistory}
            className="p-2 bg-purple-600 rounded-lg text-white hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:hover:bg-purple-600"
          >
            {isSending ? (
              <div className="h-5 w-5 border-t-2 border-white rounded-full animate-spin"></div>
            ) : (
              <PaperAirplaneIcon />
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
