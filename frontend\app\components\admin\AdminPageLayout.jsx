'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { isAdmin } from '../../utils/roleUtils';
import getSupabaseClient from '../../utils/supabaseClient';

/**
 * A layout component for admin pages that checks for admin privileges
 * and redirects non-admin users to the main dashboard.
 * 
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render if user is admin
 * @param {string} props.title - Page title
 * @param {string} props.description - Optional page description
 */
export default function AdminPageLayout({ children, title, description }) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [user, setUser] = useState(null);

  useEffect(() => {
    // Check if user is admin
    const checkAdminAccess = async () => {
      try {
        setIsLoading(true);

        // Check for demo user in localStorage first
        const demoUser = localStorage.getItem('callsaver_demo_user');
        if (demoUser) {
          // Use demo user data
          const parsedUser = JSON.parse(demoUser);
          const demoUserObj = {
            id: parsedUser.id,
            email: parsedUser.email,
            user_metadata: {
              name: parsedUser.name,
              role: parsedUser.role
            }
          };
          
          setUser(demoUserObj);
          
          // Check if demo user is admin
          if (!isAdmin(demoUserObj)) {
            console.error('Access denied: Demo user is not an admin');
            router.push('/dashboard');
            return;
          }
          
          setIsLoading(false);
          return;
        }

        // Get the client instance
        const supabase = getSupabaseClient();
        if (!supabase) {
          console.error('AdminPageLayout: Failed to get Supabase client.');
          router.push('/dashboard');
          return;
        }

        // Check Supabase session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError || !session) {
          console.error('Session error:', sessionError?.message || 'No session found');
          router.push('/dashboard');
          return;
        }

        // User is authenticated, now check if they're an admin
        setUser(session.user);
        
        if (!isAdmin(session.user)) {
          // Redirect non-admin users to the main dashboard
          console.error('Access denied: User is not an admin');
          router.push('/dashboard');
          return;
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Error checking admin access:', err);
        setError('Failed to verify admin access. Please try again later.');
        setIsLoading(false);
      }
    };

    checkAdminAccess();
  }, [router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-950">
        <div className="animate-spin w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full"></div>
        <p className="mt-4 text-gray-400">Verifying admin access...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4">
          <p className="text-red-200">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-white">{title}</h1>
        {description && <p className="text-gray-400 mt-1">{description}</p>}
      </div>
      {children}
    </div>
  );
}
