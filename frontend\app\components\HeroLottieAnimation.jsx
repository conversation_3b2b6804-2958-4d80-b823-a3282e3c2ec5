"use client";

import { useEffect, useRef } from 'react';
import Lottie from 'lottie-react';
import heroAnimation from '../../public/lottie/hero-animation.json';

export default function HeroLottieAnimation() {
  const lottieRef = useRef(null);
  
  useEffect(() => {
    // Make sure the animation is loaded and playing
    if (lottieRef.current) {
      // You can control the animation here if needed
      lottieRef.current.setSpeed(0.8); // Slightly slower for better visibility
    }
  }, []);

  return (
    <div className="hero-lottie-container relative w-full aspect-square max-w-md mx-auto">
      {/* Glowing background effect */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-64 h-64 rounded-full bg-gradient-to-r from-purple-500/20 to-pink-500/20 blur-xl animate-pulse-slow"></div>
      </div>
      
      {/* Tech Circle */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-72 h-72 border-2 border-purple-500/30 rounded-full tech-circle"></div>
      </div>
      
      {/* Lottie Animation */}
      <div className="absolute inset-0 flex items-center justify-center z-10">
        <Lottie
          lottieRef={lottieRef}
          animationData={heroAnimation}
          loop={true}
          autoplay={true}
          className="w-full h-full"
          style={{ 
            filter: 'drop-shadow(0 0 10px rgba(139, 92, 246, 0.5))',
            transform: 'scale(1.1)'
          }}
          rendererSettings={{
            preserveAspectRatio: 'xMidYMid slice',
            progressiveLoad: true,
            hideOnTransparent: false
          }}
        />
      </div>
      
      {/* Orbiting Feature Icons */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Phone Icon */}
        <div className="absolute orbit-icon" style={{ '--orbit-delay': '0s', '--orbit-duration': '12s' }}>
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-600/80 to-pink-500/80 flex items-center justify-center shadow-glow">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
            </svg>
          </div>
        </div>
        
        {/* SMS Bubble Icon */}
        <div className="absolute orbit-icon" style={{ '--orbit-delay': '2s', '--orbit-duration': '15s' }}>
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-600/80 to-indigo-500/80 flex items-center justify-center shadow-glow">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
              <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z" />
            </svg>
          </div>
        </div>
        
        {/* AI Brain Icon */}
        <div className="absolute orbit-icon" style={{ '--orbit-delay': '4s', '--orbit-duration': '18s' }}>
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-600/80 to-blue-500/80 flex items-center justify-center shadow-glow">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h3.5a1 1 0 011 1v3.5a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm1 7a1 1 0 00-1 1v3.5a1 1 0 001 1H7.5a1 1 0 001-1V12a1 1 0 00-1-1H4zm6-1a1 1 0 011-1H15a1 1 0 011 1v3.5a1 1 0 01-1 1h-3.5a1 1 0 01-1-1V10zm-6-6a1 1 0 00-1 1v3.5a1 1 0 001 1H7.5a1 1 0 001-1V5a1 1 0 00-1-1H4zm7.5 0a1 1 0 00-1 1v3.5a1 1 0 001 1H15a1 1 0 001-1V5a1 1 0 00-1-1h-3.5z" clipRule="evenodd" />
            </svg>
          </div>
        </div>
      </div>
      
      {/* Sound Visualization - Left */}
      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 z-20">
        <div className="sound-waves flex items-center h-40 space-x-1">
          {[...Array(5)].map((_, i) => (
            <div 
              key={`wave-out-left-${i}`} 
              className="sound-bar h-8 w-1 rounded-full"
              style={{
                background: `linear-gradient(to top, #9333ea, #ec4899)`,
                animation: `soundWave 1.5s ease-in-out ${i * 0.1}s infinite alternate`,
                height: `${20 + i * 5}px`,
                opacity: 0.8 - (i * 0.1)
              }}
            ></div>
          ))}
        </div>
      </div>
      
      {/* Sound Visualization - Right */}
      <div className="absolute right-0 top-1/2 transform -translate-y-1/2 z-20">
        <div className="sound-waves flex items-center h-40 space-x-1">
          {[...Array(5)].map((_, i) => (
            <div 
              key={`wave-out-right-${i}`} 
              className="sound-bar h-8 w-1 rounded-full"
              style={{
                background: `linear-gradient(to top, #6366f1, #3b82f6)`,
                animation: `soundWave 1.2s ease-in-out ${i * 0.15}s infinite alternate-reverse`,
                height: `${20 + i * 5}px`,
                opacity: 0.8 - (i * 0.1)
              }}
            ></div>
          ))}
        </div>
      </div>
      
      {/* AI Data Particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(15)].map((_, i) => (
          <div 
            key={`particle-${i}`}
            className="data-particle absolute rounded-full"
            style={{
              width: `${2 + Math.random() * 4}px`,
              height: `${2 + Math.random() * 4}px`,
              background: i % 2 === 0 
                ? 'linear-gradient(to right, #9333ea, #ec4899)' 
                : 'linear-gradient(to right, #6366f1, #3b82f6)',
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              opacity: 0.6 + (Math.random() * 0.4),
              boxShadow: i % 2 === 0 
                ? '0 0 10px rgba(236, 72, 153, 0.6)' 
                : '0 0 10px rgba(59, 130, 246, 0.6)',
              animation: `dataFloat ${3 + Math.random() * 6}s ease-in-out ${Math.random() * 2}s infinite alternate`
            }}
          ></div>
        ))}
      </div>
    </div>
  );
}
