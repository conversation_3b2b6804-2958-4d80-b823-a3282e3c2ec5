'use client';

import { useState } from 'react';
import supabaseClient from '../utils/supabaseClient';
import sql from '../utils/db';

export default function SupabaseConnections() {
  const [activeTab, setActiveTab] = useState('direct');
  const [status, setStatus] = useState({
    direct: { checked: false, success: false, message: 'Not checked' },
    supabase: { checked: false, success: false, message: 'Not checked' },
    prisma: { checked: false, success: false, message: 'Not checked' },
  });

  const checkDirectConnection = async () => {
    setStatus(prev => ({
      ...prev,
      direct: { ...prev.direct, checked: true, message: 'Checking...' }
    }));

    try {
      const result = await sql`SELECT NOW()`;
      if (result && result.length > 0) {
        setStatus(prev => ({
          ...prev,
          direct: { 
            checked: true, 
            success: true, 
            message: `Connected! Server time: ${new Date(result[0].now).toLocaleString()}` 
          }
        }));
      } else {
        setStatus(prev => ({
          ...prev,
          direct: { checked: true, success: false, message: 'No result returned' }
        }));
      }
    } catch (error) {
      console.error('Direct connection error:', error);
      setStatus(prev => ({
        ...prev,
        direct: { checked: true, success: false, message: `Error: ${error.message}` }
      }));
    }
  };
  
  const checkSupabaseConnection = async () => {
    setStatus(prev => ({
      ...prev,
      supabase: { ...prev.supabase, checked: true, message: 'Checking...' }
    }));
    
    try {
      const { data, error } = await supabaseClient.from('_prisma_migrations').select('count(*)', { count: 'exact', head: true });
      
      if (error) {
        throw error;
      }
      
      setStatus(prev => ({
        ...prev,
        supabase: { 
          checked: true, 
          success: true, 
          message: 'Supabase client connected successfully!' 
        }
      }));
    } catch (error) {
      console.error('Supabase client error:', error);
      setStatus(prev => ({
        ...prev,
        supabase: { 
          checked: true, 
          success: false, 
          message: `Error: ${error.message || 'Unknown error'}` 
        }
      }));
    }
  };
  
  // For demonstration purposes only - in real app would use PrismaClient
  const checkPrismaConnection = () => {
    setStatus(prev => ({
      ...prev,
      prisma: { 
        checked: true, 
        success: true, 
        message: 'Prisma is configured with pooled connection and direct URL' 
      }
    }));
  };

  return (
    <div className="bg-gray-900/70 backdrop-blur-lg rounded-2xl border border-purple-500/20 shadow-xl p-6 md:p-8 w-full max-w-3xl mx-auto my-8">
      <h2 className="text-xl text-white/80 mb-6 text-center">Supabase Connection Options</h2>
      
      <div className="flex border-b border-gray-700 mb-6">
        <button
          className={`py-2 px-4 font-medium text-sm ${activeTab === 'direct' ? 'text-purple-400 border-b-2 border-purple-400' : 'text-gray-400 hover:text-white'}`}
          onClick={() => setActiveTab('direct')}
        >
          Direct Connection
        </button>
        <button
          className={`py-2 px-4 font-medium text-sm ${activeTab === 'supabase' ? 'text-purple-400 border-b-2 border-purple-400' : 'text-gray-400 hover:text-white'}`}
          onClick={() => setActiveTab('supabase')}
        >
          App Framework
        </button>
        <button
          className={`py-2 px-4 font-medium text-sm ${activeTab === 'prisma' ? 'text-purple-400 border-b-2 border-purple-400' : 'text-gray-400 hover:text-white'}`}
          onClick={() => setActiveTab('prisma')}
        >
          Prisma ORM
        </button>
      </div>
      
      <div className="space-y-6">
        {activeTab === 'direct' && (
          <div className="space-y-4">
            <p className="text-gray-300">Direct SQL Connection using postgres client</p>
            <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
              <code className="text-xs text-green-400 block mb-2">
                DATABASE_URL=&quot;postgresql://postgres:[YOUR-PASSWORD]@db.fayjmfdjudtldfpwxlpu.supabase.co:5432/postgres&quot;
              </code>
              <p className="text-sm text-gray-300 mb-4">
                Ideal for applications with persistent, long-lived connections like web servers
              </p>
              
              <div className="flex items-center mt-2">
                <div className={`h-3 w-3 rounded-full mr-2 ${
                  !status.direct.checked ? 'bg-gray-500' :
                  status.direct.success ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <p className="text-sm text-gray-300">{status.direct.message}</p>
              </div>
              
              <button 
                onClick={checkDirectConnection}
                className="mt-4 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded text-sm"
              >
                Test Connection
              </button>
            </div>
          </div>
        )}
        
        {activeTab === 'supabase' && (
          <div className="space-y-4">
            <p className="text-gray-300">Supabase App Framework (client-side)</p>
            <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
              <code className="text-xs text-green-400 block mb-2">
                NEXT_PUBLIC_SUPABASE_URL=https://fayjmfdjudtldfpwxlpu.supabase.co<br />
                NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGc...
              </code>
              <p className="text-sm text-gray-300 mb-4">
                Best for client-side features like auth, storage, and realtime subscriptions
              </p>
              
              <div className="flex items-center mt-2">
                <div className={`h-3 w-3 rounded-full mr-2 ${
                  !status.supabase.checked ? 'bg-gray-500' :
                  status.supabase.success ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <p className="text-sm text-gray-300">{status.supabase.message}</p>
              </div>
              
              <button 
                onClick={checkSupabaseConnection}
                className="mt-4 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded text-sm"
              >
                Test Connection
              </button>
            </div>
          </div>
        )}
        
        {activeTab === 'prisma' && (
          <div className="space-y-4">
            <p className="text-gray-300">Prisma ORM Configuration</p>
            <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
              <code className="text-xs text-green-400 block mb-2">
                DATABASE_URL=&quot;postgresql://postgres.fayjmfdjudtldfpwxlpu:[YOUR-PASSWORD]@aws-0-eu-central-1.pooler.supabase.co:6543/postgres?pgbouncer=true&quot;<br />
                DIRECT_URL=&quot;postgresql://postgres.fayjmfdjudtldfpwxlpu:[YOUR-PASSWORD]@aws-0-eu-central-1.pooler.supabase.co:5432/postgres&quot;
              </code>
              <p className="text-sm text-gray-300 mb-4">
                Uses connection pooling for queries and direct connection for migrations
              </p>
              
              <div className="flex items-center mt-2">
                <div className={`h-3 w-3 rounded-full mr-2 ${
                  !status.prisma.checked ? 'bg-gray-500' :
                  status.prisma.success ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <p className="text-sm text-gray-300">{status.prisma.message}</p>
              </div>
              
              <button 
                onClick={checkPrismaConnection}
                className="mt-4 bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded text-sm"
              >
                Verify Configuration
              </button>
            </div>
          </div>
        )}
      </div>
      
      <div className="mt-8 border-t border-gray-700 pt-4">
        <p className="text-xs text-gray-400">
          Note: Replace [YOUR-PASSWORD] with your actual database password in all connection strings.
          Each connection type is optimized for different use cases within your application.
        </p>
      </div>
    </div>
  );
} 