"use client";

import { motion } from 'framer-motion';

export default function ChatBubble({ message, isLast }) {
  const isAi = message.sender === 'ai';
  const bubbleVariants = {
    hidden: { 
      opacity: 0,
      y: 20,
      scale: 0.9
    },
    visible: { 
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        type: "spring",
        damping: 25,
        stiffness: 500
      }
    }
  };

  return (
    <motion.div
      className={`flex mb-4 ${isAi ? 'justify-start' : 'justify-end'}`}
      initial="hidden"
      animate="visible"
      variants={bubbleVariants}
    >
      {isAi && (
        <div className="mr-2 flex-shrink-0">
          <div className="w-8 h-8 rounded-full bg-[#3b3363] flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
        </div>
      )}
      
      <div 
        className={`max-w-[70%] relative ${
          isAi 
            ? 'bg-[#28293d] text-white rounded-r-lg rounded-bl-lg' 
            : 'bg-[#6946db] text-white rounded-l-lg rounded-br-lg'
        }`}
      >
        {/* Glow effect for AI messages */}
        {isAi && (
          <div 
            className="absolute inset-0 rounded-r-lg rounded-bl-lg"
            style={{
              boxShadow: '0 0 8px rgba(139, 92, 246, 0.15), inset 0 0 4px rgba(139, 92, 246, 0.15)',
              pointerEvents: 'none'
            }}
          />
        )}
        
        <div className="p-3">
          {message.content}
        </div>
        
        {/* Status indicator for messages */}
        {message.status && (
          <div className="text-right pr-2 pb-1">
            <span className="text-xs text-gray-400">{message.status}</span>
          </div>
        )}
      </div>
      
      {!isAi && (
        <div className="ml-2 flex-shrink-0">
          <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
        </div>
      )}
    </motion.div>
  );
} 