'use client';

import { useState, useEffect } from 'react';
import AdminPageLayout from '../../../components/admin/AdminPageLayout';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../../../components/ui/tabs';

export default function SystemLogsPage() {
  const [logs, setLogs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  
  useEffect(() => {
    // Simulate fetching logs
    const fetchLogs = async () => {
      try {
        setIsLoading(true);
        
        // In a real implementation, this would be an API call to get logs
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Mock log data
        const mockLogs = [
          { id: 1, type: 'error', message: 'Failed to process payment for user #1234', timestamp: '2023-06-15T14:23:45Z', service: 'billing' },
          { id: 2, type: 'info', message: 'User #5678 signed up', timestamp: '2023-06-15T13:45:12Z', service: 'auth' },
          { id: 3, type: 'warning', message: 'High CPU usage detected on server node-3', timestamp: '2023-06-15T12:30:22Z', service: 'monitoring' },
          { id: 4, type: 'error', message: 'Database connection timeout', timestamp: '2023-06-15T11:15:33Z', service: 'database' },
          { id: 5, type: 'info', message: 'Scheduled backup completed successfully', timestamp: '2023-06-15T10:00:00Z', service: 'backup' },
          { id: 6, type: 'warning', message: 'Rate limit exceeded for API key #ABC123', timestamp: '2023-06-15T09:45:18Z', service: 'api' },
          { id: 7, type: 'info', message: 'System update deployed successfully', timestamp: '2023-06-15T08:30:00Z', service: 'deployment' },
          { id: 8, type: 'error', message: 'Failed to send notification to user #9876', timestamp: '2023-06-15T07:22:45Z', service: 'notifications' },
        ];
        
        setLogs(mockLogs);
      } catch (error) {
        console.error('Error fetching logs:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchLogs();
  }, []);
  
  // Filter logs based on active tab
  const filteredLogs = logs.filter(log => {
    if (activeTab === 'all') return true;
    return log.type === activeTab;
  });
  
  // Get log type badge color
  const getLogTypeBadge = (type) => {
    switch (type) {
      case 'error':
        return 'bg-red-500/20 text-red-300 border-red-500/30';
      case 'warning':
        return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
      case 'info':
        return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    }
  };
  
  return (
    <AdminPageLayout 
      title="System Logs" 
      description="View and analyze system logs for troubleshooting and auditing."
    >
      <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="bg-gray-800/70 border border-purple-500/20 mb-6">
          <TabsTrigger value="all">All Logs</TabsTrigger>
          <TabsTrigger value="error">Errors</TabsTrigger>
          <TabsTrigger value="warning">Warnings</TabsTrigger>
          <TabsTrigger value="info">Info</TabsTrigger>
        </TabsList>
        
        <TabsContent value={activeTab} className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-white">
              {activeTab === 'all' ? 'All Logs' : 
               activeTab === 'error' ? 'Error Logs' : 
               activeTab === 'warning' ? 'Warning Logs' : 'Info Logs'}
            </h2>
            
            <div className="flex space-x-2">
              <button className="bg-purple-600 hover:bg-purple-700 text-white py-1 px-3 rounded-md text-sm transition-colors">
                Export
              </button>
              <button className="bg-purple-600 hover:bg-purple-700 text-white py-1 px-3 rounded-md text-sm transition-colors">
                Refresh
              </button>
            </div>
          </div>
          
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin w-8 h-8 border-3 border-purple-500 border-t-transparent rounded-full"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="py-2 px-4 text-left text-gray-400 font-medium">Type</th>
                    <th className="py-2 px-4 text-left text-gray-400 font-medium">Timestamp</th>
                    <th className="py-2 px-4 text-left text-gray-400 font-medium">Service</th>
                    <th className="py-2 px-4 text-left text-gray-400 font-medium">Message</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredLogs.length === 0 ? (
                    <tr>
                      <td colSpan="4" className="py-4 text-center text-gray-400">
                        No logs found
                      </td>
                    </tr>
                  ) : (
                    filteredLogs.map(log => (
                      <tr key={log.id} className="border-b border-gray-700/50 hover:bg-gray-700/30">
                        <td className="py-3 px-4">
                          <span className={`inline-block px-2 py-1 rounded-md text-xs font-medium border ${getLogTypeBadge(log.type)}`}>
                            {log.type.toUpperCase()}
                          </span>
                        </td>
                        <td className="py-3 px-4 text-gray-300">
                          {new Date(log.timestamp).toLocaleString()}
                        </td>
                        <td className="py-3 px-4 text-gray-300">
                          {log.service}
                        </td>
                        <td className="py-3 px-4 text-gray-300">
                          {log.message}
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </AdminPageLayout>
  );
}
