"use client";

import { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { 
  PlusCircleIcon, 
  CogIcon,
  ChartBarIcon,
  Cog6ToothIcon,
  BellAlertIcon
} from '@heroicons/react/24/outline';
import SimpleNav from '../components/SimpleNav';
import CallHistoryVisualization from '../components/dashboard/CallHistoryVisualization';
import VoicemailList from '../components/dashboard/VoicemailList';
import RecentCallsList from '../components/dashboard/RecentCallsList';
import RecentSmsList from '../components/dashboard/RecentSmsList';
import NumbersList from '../components/dashboard/NumbersList';
import { UserAPI, CallLogsAPI, MessagesAPI, BillingAPI, NumbersAPI } from '../utils/api';
import CallInterface from '../components/dashboard/CallInterface';
import AISettingsPanel from '../components/dashboard/AISettingsPanel';
import ForwardingSettings from '../components/dashboard/ForwardingSettings';
import SmartRoutingPanel from '../components/dashboard/SmartRoutingPanel';
import AppointmentsCalendar from '../components/dashboard/AppointmentsCalendar';

export default function Dashboard() {
  const [isLoading, setIsLoading] = useState(true);
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [userNumbers, setUserNumbers] = useState([]);
  const [recentCalls, setRecentCalls] = useState([]);
  const [recentMessages, setRecentMessages] = useState([]);
  const [voicemails, setVoicemails] = useState([]);
  const [error, setError] = useState(null);
  const [userCredits, setUserCredits] = useState(0);
  const [purchaseStatus, setPurchaseStatus] = useState(null);
  const [usageStats, setUsageStats] = useState({
    callMinutes: '0:00',
    smsCount: 0,
    aiResponses: 0,
    creditsUsed: 0
  });
  const [activeSection, setActiveSection] = useState('dashboard');

  const searchParams = useSearchParams();

  // Function to refresh dashboard data
  const refreshDashboardData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Fetching dashboard data...');
      const [userData, numbersData, callsData, messagesData, voicemailsData, creditsData, usageData] = await Promise.all([
        UserAPI.getUserProfile(),
        NumbersAPI.getUserNumbers(),
        CallLogsAPI.getRecentCalls(),
        MessagesAPI.getRecentMessages(),
        CallLogsAPI.getVoicemails(),
        BillingAPI.getCredits(),
        CallLogsAPI.getCallStats('weekly') // Get usage statistics
      ]);

      // Extract data safely with better error handling
      if (userData && userData.data) {
        setUserName(userData.data.name || 'User');
        setUserEmail(userData.data.email || 'N/A');
      }

      // Process phone numbers data
      let processedNumbers = [];
      if (numbersData) {
        if (numbersData.data?.phoneNumbers) {
          processedNumbers = numbersData.data.phoneNumbers;
        } else if (numbersData.data && Array.isArray(numbersData.data)) {
          processedNumbers = numbersData.data;
        } else if (Array.isArray(numbersData)) {
          processedNumbers = numbersData;
        } else if (numbersData.numbers) {
          processedNumbers = numbersData.numbers;
        }
      }
      setUserNumbers(processedNumbers);

      // Process other data
      setRecentCalls(callsData?.data || []);
      setRecentMessages(messagesData?.data || []);
      setVoicemails(voicemailsData?.data || []);
      setUserCredits(creditsData?.data?.credits ?? 0);

      // Calculate usage statistics from call data
      if (usageData && Array.isArray(usageData)) {
        const totalCalls = usageData.reduce((sum, day) => 
          sum + (day.total || (day.answered + day.missed + day.voicemail) || 0), 0);
        
        // Calculate total minutes (assuming average call duration of 3 minutes)
        const totalMinutes = Math.floor(totalCalls * 3);
        const minutes = Math.floor(totalMinutes);
        const seconds = Math.floor((totalMinutes - minutes) * 60);
        
        setUsageStats({
          callMinutes: `${minutes}:${seconds.toString().padStart(2, '0')}`,
          smsCount: Math.floor(totalCalls * 1.5), // Estimate SMS count
          aiResponses: Math.floor(totalCalls * 0.7), // Estimate AI responses
          creditsUsed: Math.floor(totalCalls * 0.5) // Estimate credits used
        });
      }

      console.log('Dashboard data loaded successfully.');
    } catch (apiError) {
      console.error('Error loading dashboard data:', apiError);
      setError('Failed to load some dashboard data. Please try refreshing the page.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    const purchaseResult = searchParams.get('purchase');
    if (purchaseResult === 'success') {
      setPurchaseStatus({ 
        type: 'success', 
        message: 'Credits purchased successfully! Your balance should update shortly.' 
      });
    } else if (purchaseResult === 'cancelled') {
      setPurchaseStatus({ 
        type: 'cancelled', 
        message: 'Credit purchase cancelled.' 
      });
    }

    refreshDashboardData();

    // Set up periodic refresh (every 30 seconds)
    const refreshInterval = setInterval(() => {
      refreshDashboardData();
    }, 30000);

    return () => clearInterval(refreshInterval);
  }, [searchParams, refreshDashboardData]);

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-950">
        <div className="flex flex-col items-center">
          <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-t-2 border-purple-500"></div>
          <p className="mt-4 text-lg text-gray-400">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-950 text-white">
      <div className="container mx-auto max-w-7xl px-4 py-6">
        <SimpleNav />

        {/* Buy Number Button */}
        <div className="mb-6 flex justify-center">
          <a 
            href="/dashboard/buy-number" 
            className="px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg shadow-lg transition duration-200 flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
            </svg>
            Buy a Number
          </a>
        </div>

        {/* Purchase Status Notification */}
        {purchaseStatus && (
          <div className={`mb-6 p-4 rounded-lg ${
            purchaseStatus.type === 'success' 
              ? 'bg-green-500/20 border border-green-500/30 text-green-300' 
              : 'bg-yellow-500/20 border border-yellow-500/30 text-yellow-300'
          }`}>
            <p className="text-center">{purchaseStatus.message}</p>
          </div>
        )}

        {/* Error Notification */}
        {error && (
          <div className="mb-6 p-4 rounded-lg bg-red-500/20 border border-red-500/30 text-red-300">
            <p className="text-center">{error}</p>
            <div className="flex justify-center mt-2">
              <button 
                onClick={refreshDashboardData}
                className="px-3 py-1 bg-red-500/30 hover:bg-red-500/50 rounded-md text-sm transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        )}

        {/* Dashboard Navigation */}
        <div className="mb-6 flex justify-center">
          <div className="bg-gray-800/70 backdrop-blur-lg rounded-xl border border-purple-500/20 p-1 flex">
              <button 
                onClick={() => setActiveSection('dashboard')}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeSection === 'dashboard' 
                    ? 'bg-purple-600 text-white' 
                    : 'text-gray-300 hover:bg-gray-700/50'
                }`}
              >
                Dashboard
              </button>
              <button 
                onClick={() => setActiveSection('numbers')}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeSection === 'numbers' 
                    ? 'bg-purple-600 text-white' 
                    : 'text-gray-300 hover:bg-gray-700/50'
                }`}
              >
                Numbers
              </button>
              <button 
                onClick={() => setActiveSection('calls')}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeSection === 'calls' 
                    ? 'bg-purple-600 text-white' 
                    : 'text-gray-300 hover:bg-gray-700/50'
                }`}
              >
                Calls
              </button>
              <button 
                onClick={() => setActiveSection('messages')}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeSection === 'messages' 
                    ? 'bg-purple-600 text-white' 
                    : 'text-gray-300 hover:bg-gray-700/50'
                }`}
              >
                Messages
              </button>
              <button 
                onClick={() => setActiveSection('settings')}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeSection === 'settings' 
                    ? 'bg-purple-600 text-white' 
                    : 'text-gray-300 hover:bg-gray-700/50'
                }`}
              >
                Settings
              </button>
          </div>
        </div>

        {/* Dashboard Value Information */}
        <div className="mb-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-gray-800/70 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6 flex flex-col items-center">
            <div className="bg-purple-500/20 rounded-full p-3 mb-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-white mb-1">Account Status</h3>
            <p className="text-green-400 font-medium">Active</p>
            <p className="text-gray-400 text-sm text-center mt-2">Your account is in good standing and all services are active.</p>
          </div>
          
          <div className="bg-gray-800/70 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6 flex flex-col items-center">
            <div className="bg-blue-500/20 rounded-full p-3 mb-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-white mb-1">Phone Numbers</h3>
            <p className="text-blue-400 font-medium">{userNumbers.length} Active</p>
            <div className="text-gray-400 text-sm text-center mt-2">
              <p className="mb-1">Your numbers are ready to receive calls and messages.</p>
              <div className="flex flex-col gap-1 mt-2">
                {userNumbers.length > 0 ? (
                  userNumbers.slice(0, 2).map((number, index) => (
                    <span key={index} className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">
                      {number.number || number.phoneNumber} ({number.countryCode})
                    </span>
                  ))
                ) : (
                  <span className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">
                    No numbers yet
                  </span>
                )}
                {userNumbers.length > 2 && (
                  <span className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">
                    +{userNumbers.length - 2} more
                  </span>
                )}
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800/70 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6 flex flex-col items-center">
            <div className="bg-yellow-500/20 rounded-full p-3 mb-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-white mb-1">Credits Balance</h3>
            <p className="text-yellow-400 font-medium">{userCredits} Credits</p>
            <div className="text-gray-400 text-sm text-center mt-2">
              <p>
                {userCredits > 0 
                  ? "Use credits to purchase numbers and services." 
                  : "Add credits to your account to purchase numbers."}
              </p>
              <button 
                className="mt-2 px-3 py-1 bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-300 rounded-md text-xs transition-colors"
                onClick={() => window.location.href = '/dashboard/buy-credits'}
              >
                Buy Credits
              </button>
            </div>
          </div>
        </div>

        {/* Additional Dashboard Information */}
        <div className="mb-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gray-800/70 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6">
            <div className="flex items-center mb-4">
              <div className="bg-pink-500/20 rounded-full p-3 mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-pink-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">Recent Activity</h3>
                <p className="text-gray-400 text-sm">Last 7 days</p>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center border-b border-gray-700/50 pb-2">
                <span className="text-gray-300">Incoming Calls</span>
                <span className="text-white font-medium">{recentCalls.length || 0}</span>
              </div>
              <div className="flex justify-between items-center border-b border-gray-700/50 pb-2">
                <span className="text-gray-300">Messages</span>
                <span className="text-white font-medium">{recentMessages.length || 0}</span>
              </div>
              <div className="flex justify-between items-center border-b border-gray-700/50 pb-2">
                <span className="text-gray-300">Voicemails</span>
                <span className="text-white font-medium">{voicemails.length || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-300">Total Interactions</span>
                <span className="text-white font-medium">{(recentCalls.length || 0) + (recentMessages.length || 0) + (voicemails.length || 0)}</span>
              </div>
            </div>
          </div>
          
          <div className="bg-gray-800/70 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6">
            <div className="flex items-center mb-4">
              <div className="bg-green-500/20 rounded-full p-3 mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold text-white">Usage Statistics</h3>
                <p className="text-gray-400 text-sm">Current billing period</p>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center border-b border-gray-700/50 pb-2">
                <span className="text-gray-300">Call Minutes Used</span>
                <span className="text-white font-medium">{usageStats.callMinutes}</span>
              </div>
              <div className="flex justify-between items-center border-b border-gray-700/50 pb-2">
                <span className="text-gray-300">SMS Messages Sent</span>
                <span className="text-white font-medium">{usageStats.smsCount}</span>
              </div>
              <div className="flex justify-between items-center border-b border-gray-700/50 pb-2">
                <span className="text-gray-300">AI Responses</span>
                <span className="text-white font-medium">{usageStats.aiResponses}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-300">Credits Used</span>
                <span className="text-white font-medium">{usageStats.creditsUsed}</span>
              </div>
            </div>
          </div>
        </div>
        
        {/* Quick Actions */}
        <div className="mb-10 bg-gray-800/70 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6">
          <h3 className="text-xl font-bold text-white mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a 
              href="/dashboard/buy-number" 
              className="bg-gray-700/50 hover:bg-gray-700 rounded-lg p-4 flex flex-col items-center text-center transition-colors duration-200"
            >
              <div className="bg-purple-500/20 rounded-full p-3 mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <span className="text-white font-medium">Buy Number</span>
            </a>
            
            <a 
              href="/dashboard/settings" 
              className="bg-gray-700/50 hover:bg-gray-700 rounded-lg p-4 flex flex-col items-center text-center transition-colors duration-200"
            >
              <div className="bg-blue-500/20 rounded-full p-3 mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <span className="text-white font-medium">Settings</span>
            </a>
            
            <a 
              href="/dashboard/automation" 
              className="bg-gray-700/50 hover:bg-gray-700 rounded-lg p-4 flex flex-col items-center text-center transition-colors duration-200"
            >
              <div className="bg-green-500/20 rounded-full p-3 mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <span className="text-white font-medium">Automation</span>
            </a>
            
            <a 
              href="/dashboard/analytics" 
              className="bg-gray-700/50 hover:bg-gray-700 rounded-lg p-4 flex flex-col items-center text-center transition-colors duration-200"
            >
              <div className="bg-yellow-500/20 rounded-full p-3 mb-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <span className="text-white font-medium">Analytics</span>
            </a>
          </div>
        </div>

        {/* Main Dashboard Content */}
        {activeSection === 'dashboard' && (
          <>
            {/* Appointments Calendar - Moved to top */}
            <div className="mb-6">
              <AppointmentsCalendar />
            </div>
            
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
              <div className="lg:col-span-2">
                <CallHistoryVisualization />
              </div>

              <div className="space-y-6">
                <NumbersList numbers={userNumbers} />
                <CallInterface phoneNumbers={userNumbers} />
              </div>
            </div>

            {/* Calls, SMS, Voicemails Grid */}
            <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              <div className="md:col-span-1 lg:col-span-1">
                <RecentCallsList calls={recentCalls} />
              </div>
              <div className="md:col-span-1 lg:col-span-1">
                <RecentSmsList messages={recentMessages} />
              </div>
              <div className="md:col-span-2 lg:col-span-1">
                <VoicemailList voicemails={voicemails} />
              </div>
            </div>
          </>
        )}

        {/* Numbers Section */}
        {activeSection === 'numbers' && (
          <div className="space-y-6">
            <NumbersList numbers={userNumbers} />
            <div className="bg-gray-800/70 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6">
              <h3 className="text-xl font-bold text-white mb-4">Number Management</h3>
              <p className="text-gray-400 mb-4">Manage your phone numbers, configure routing, and set up automated responses.</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <a 
                  href="/dashboard/buy-number" 
                  className="bg-purple-600 hover:bg-purple-700 text-white rounded-lg p-4 flex items-center transition-colors"
                >
                  <PlusCircleIcon className="h-6 w-6 mr-3" />
                  <div>
                    <h4 className="font-medium">Buy a New Number</h4>
                    <p className="text-sm text-purple-200">Get local or toll-free numbers</p>
                  </div>
                </a>
                
                <a 
                  href="/dashboard/number-settings" 
                  className="bg-gray-700/50 hover:bg-gray-700 text-white rounded-lg p-4 flex items-center transition-colors"
                >
                  <CogIcon className="h-6 w-6 mr-3" />
                  <div>
                    <h4 className="font-medium">Number Settings</h4>
                    <p className="text-sm text-gray-300">Configure call routing and SMS</p>
                  </div>
                </a>
              </div>
            </div>
          </div>
        )}

        {/* Calls Section */}
        {activeSection === 'calls' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <RecentCallsList calls={recentCalls} />
              </div>
              <div>
                <CallInterface phoneNumbers={userNumbers} />
              </div>
            </div>
            
            <div className="bg-gray-800/70 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6">
              <h3 className="text-xl font-bold text-white mb-4">Call Analytics</h3>
              <CallHistoryVisualization />
            </div>
            
            <div className="bg-gray-800/70 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6">
              <h3 className="text-xl font-bold text-white mb-4">Voicemails</h3>
              <VoicemailList voicemails={voicemails} />
            </div>
          </div>
        )}

        {/* Messages Section */}
        {activeSection === 'messages' && (
          <div className="space-y-6">
            <div className="bg-gray-800/70 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6">
              <h3 className="text-xl font-bold text-white mb-4">SMS Messages</h3>
              <RecentSmsList messages={recentMessages} />
            </div>
            
            <div className="bg-gray-800/70 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6">
              <h3 className="text-xl font-bold text-white mb-4">Send SMS</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Select a Phone Number to Send From
                  </label>
                  <select className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white">
                    {userNumbers.map(number => (
                      <option key={number.id} value={number.number}>
                        {number.friendlyName || number.number}
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Recipient
                  </label>
                  <input
                    type="tel"
                    placeholder="Enter phone number"
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Message
                  </label>
                  <textarea
                    rows={4}
                    placeholder="Type your message here..."
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white resize-none"
                  ></textarea>
                </div>
                
                <button className="w-full py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors flex items-center justify-center">
                  Send Message
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Settings Section */}
        {activeSection === 'settings' && (
          <div className="space-y-6">
            <div className="bg-gray-800/70 backdrop-blur-lg rounded-xl border border-purple-500/20 p-6 mb-6">
              <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                <Cog6ToothIcon className="h-6 w-6 mr-2 text-purple-400" />
                Settings & Configuration
              </h3>
              <p className="text-gray-400 mb-4">
                Configure your CallSaver account settings, notifications, and automation preferences.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <AISettingsPanel />
              <SmartRoutingPanel />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ForwardingSettings />
              
              <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-white flex items-center">
                    <BellAlertIcon className="h-5 w-5 mr-2 text-purple-400" />
                    Notifications
                  </h3>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-400">
                        Email Notifications
                      </label>
                      <p className="text-xs text-gray-500 mt-1">
                        Receive email notifications for important events
                      </p>
                    </div>
                    <button 
                      className="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 bg-purple-600"
                    >
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                    </button>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-400">
                        SMS Notifications
                      </label>
                      <p className="text-xs text-gray-500 mt-1">
                        Receive SMS notifications for important events
                      </p>
                    </div>
                    <button 
                      className="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 bg-purple-600"
                    >
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                    </button>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-400">
                        Low Credit Alerts
                      </label>
                      <p className="text-xs text-gray-500 mt-1">
                        Receive alerts when your credit balance is low
                      </p>
                    </div>
                    <button 
                      className="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 bg-purple-600"
                    >
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-6" />
                    </button>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-400">
                        Marketing Updates
                      </label>
                      <p className="text-xs text-gray-500 mt-1">
                        Receive updates about new features and promotions
                      </p>
                    </div>
                    <button 
                      className="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 bg-gray-700"
                    >
                      <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1" />
                    </button>
                  </div>
                  
                  <button className="w-full py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors flex items-center justify-center mt-4">
                    Save Notification Settings
                  </button>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 p-5 shadow-lg">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-white flex items-center">
                  <CogIcon className="h-5 w-5 mr-2 text-purple-400" />
                  Account Settings
                </h3>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={userEmail}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    value={userName}
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Password
                  </label>
                  <input
                    type="password"
                    value="••••••••••••"
                    className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Leave blank to keep your current password
                  </p>
                </div>
                
                <button className="w-full py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors flex items-center justify-center mt-4">
                  Save Account Settings
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
