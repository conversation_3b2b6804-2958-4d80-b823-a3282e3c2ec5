import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../lib/apiClient';
import toast from 'react-hot-toast';

// Types
export interface Role {
  id: string;
  name: string;
  description: string;
  isSystem: boolean;
  isDefault: boolean;
  parentId: string | null;
  parentName?: string;
  children: { id: string; name: string }[];
  permissions: string[];
}

export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
  scope: string;
  displayName?: string;
  groupId?: string;
}

export interface PermissionGroup {
  id: string;
  name: string;
  description: string;
  displayOrder: number;
  permissions: Permission[];
}

export interface CreateRoleParams {
  name: string;
  description?: string;
  parentId?: string;
  permissions?: string[];
}

export interface UpdateRoleParams {
  name?: string;
  description?: string;
  parentId?: string | null;
  isDefault?: boolean;
}

export interface CreatePermissionGroupParams {
  name: string;
  description?: string;
  displayOrder: number;
  permissionIds: string[];
}

export interface UpdatePermissionGroupParams {
  name?: string;
  description?: string;
  displayOrder?: number;
  permissionIds?: string[];
}

/**
 * Hook for managing roles and permissions
 */
export function useRoleManagement() {
  const queryClient = useQueryClient();

  // Fetch roles
  const rolesQuery = useQuery({
    queryKey: ['roles'],
    queryFn: async () => {
      const response = await api.get<Role[]>('/admin/roles');
      return response;
    },
  });

  // Fetch permissions
  const permissionsQuery = useQuery({
    queryKey: ['permissions'],
    queryFn: async () => {
      const response = await api.get<{ groups: PermissionGroup[], ungrouped: Permission[] }>('/admin/permissions');
      return response;
    },
  });

  // Create role mutation
  const createRoleMutation = useMutation({
    mutationFn: async (data: CreateRoleParams) => {
      const response = await api.post('/admin/roles', data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast.success('Role created successfully');
    },
    onError: (error) => {
      toast.error('Failed to create role');
      console.error('Error creating role:', error);
    },
  });

  // Update role mutation
  const updateRoleMutation = useMutation({
    mutationFn: async ({ roleId, data }: { roleId: string; data: UpdateRoleParams }) => {
      const response = await api.put(`/admin/roles/${roleId}`, data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast.success('Role updated successfully');
    },
    onError: (error) => {
      toast.error('Failed to update role');
      console.error('Error updating role:', error);
    },
  });

  // Update role permissions mutation
  const updateRolePermissionsMutation = useMutation({
    mutationFn: async ({ roleId, permissions }: { roleId: string; permissions: string[] }) => {
      const response = await api.put(`/admin/roles/${roleId}/permissions`, { permissions });
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast.success('Role permissions updated successfully');
    },
    onError: (error) => {
      toast.error('Failed to update role permissions');
      console.error('Error updating role permissions:', error);
    },
  });

  // Delete role mutation
  const deleteRoleMutation = useMutation({
    mutationFn: async (roleId: string) => {
      const response = await api.delete(`/admin/roles/${roleId}`);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast.success('Role deleted successfully');
    },
    onError: (error) => {
      toast.error('Failed to delete role');
      console.error('Error deleting role:', error);
    },
  });

  // Clone role function
  const cloneRole = async (roleId: string, newName: string) => {
    try {
      // Get the role to clone
      const roleToClone = rolesQuery.data?.find(role => role.id === roleId);
      if (!roleToClone) {
        throw new Error('Role not found');
      }

      // Create a new role with the same properties
      const newRole = await createRoleMutation.mutateAsync({
        name: newName,
        description: `Clone of ${roleToClone.name}: ${roleToClone.description || ''}`,
        parentId: roleToClone.parentId || undefined,
        permissions: roleToClone.permissions,
      });

      return newRole;
    } catch (error) {
      console.error('Error cloning role:', error);
      throw error;
    }
  };

  // Export roles to JSON
  const exportRoles = (roleIds: string[]) => {
    if (!rolesQuery.data) return null;

    const rolesToExport = rolesQuery.data.filter(role => roleIds.includes(role.id));
    return JSON.stringify(rolesToExport, null, 2);
  };

  // Import roles from JSON
  const importRoles = async (rolesJson: string) => {
    try {
      const roles = JSON.parse(rolesJson) as Role[];

      // Create each role
      for (const role of roles) {
        await createRoleMutation.mutateAsync({
          name: role.name,
          description: role.description,
          parentId: role.parentId || undefined,
          permissions: role.permissions,
        });
      }

      return true;
    } catch (error) {
      console.error('Error importing roles:', error);
      toast.error('Failed to import roles. Please check the JSON format.');
      return false;
    }
  };

  // Create permission group mutation
  const createPermissionGroupMutation = useMutation({
    mutationFn: async (data: CreatePermissionGroupParams) => {
      const response = await api.post('/admin/permission-groups', data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permissions'] });
      toast.success('Permission group created successfully');
    },
    onError: (error) => {
      toast.error('Failed to create permission group');
      console.error('Error creating permission group:', error);
    },
  });

  // Update permission group mutation
  const updatePermissionGroupMutation = useMutation({
    mutationFn: async ({ groupId, data }: { groupId: string; data: UpdatePermissionGroupParams }) => {
      const response = await api.put(`/admin/permission-groups/${groupId}`, data);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permissions'] });
      toast.success('Permission group updated successfully');
    },
    onError: (error) => {
      toast.error('Failed to update permission group');
      console.error('Error updating permission group:', error);
    },
  });

  // Delete permission group mutation
  const deletePermissionGroupMutation = useMutation({
    mutationFn: async (groupId: string) => {
      const response = await api.delete(`/admin/permission-groups/${groupId}`);
      return response;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permissions'] });
      toast.success('Permission group deleted successfully');
    },
    onError: (error) => {
      toast.error('Failed to delete permission group');
      console.error('Error deleting permission group:', error);
    },
  });

  return {
    // Queries
    roles: {
      data: rolesQuery.data,
      isLoading: rolesQuery.isLoading,
      error: rolesQuery.error,
    },
    permissions: {
      data: permissionsQuery.data,
      isLoading: permissionsQuery.isLoading,
      error: permissionsQuery.error,
    },

    // Mutations
    createRole: createRoleMutation.mutate,
    isCreatingRole: createRoleMutation.isPending,
    createRoleError: createRoleMutation.error,

    updateRole: updateRoleMutation.mutate,
    isUpdatingRole: updateRoleMutation.isPending,
    updateRoleError: updateRoleMutation.error,

    updateRolePermissions: updateRolePermissionsMutation.mutate,
    isUpdatingRolePermissions: updateRolePermissionsMutation.isPending,
    updateRolePermissionsError: updateRolePermissionsMutation.error,

    deleteRole: deleteRoleMutation.mutate,
    isDeletingRole: deleteRoleMutation.isPending,
    deleteRoleError: deleteRoleMutation.error,

    // Additional functions
    cloneRole,
    exportRoles,
    importRoles,

    // Permission Group Mutations
    createPermissionGroup: createPermissionGroupMutation.mutate,
    isCreatingPermissionGroup: createPermissionGroupMutation.isPending,
    createPermissionGroupError: createPermissionGroupMutation.error,

    updatePermissionGroup: updatePermissionGroupMutation.mutate,
    isUpdatingPermissionGroup: updatePermissionGroupMutation.isPending,
    updatePermissionGroupError: updatePermissionGroupMutation.error,

    deletePermissionGroup: deletePermissionGroupMutation.mutate,
    isDeletingPermissionGroup: deletePermissionGroupMutation.isPending,
    deletePermissionGroupError: deletePermissionGroupMutation.error,

    // Refresh
    refreshRoles: () => queryClient.invalidateQueries({ queryKey: ['roles'] }),
    refreshPermissions: () => queryClient.invalidateQueries({ queryKey: ['permissions'] }),
  };
}
