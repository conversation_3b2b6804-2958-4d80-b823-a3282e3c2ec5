# Example Environment File for Airalo eSIM Integration
# Copy these variables to your .env file and fill in the values

# Airalo eSIM Provider Configuration
AIRALO_CLIENT_ID=your_client_id_here
AIRALO_CLIENT_SECRET=your_client_secret_here
AIRALO_API_URL=https://api.airalo.com/v2
AIRALO_SANDBOX=true  # Set to false for production

# eSIM General Configuration
ESIM_ENABLED=true
ESIM_ENABLED_USERS=  # Comma-separated list of user IDs for gradual rollout
ESIM_ENABLED_NUMBERS=  # Comma-separated list of phone numbers for gradual rollout

# Telephony Provider Configuration
TELEPHONY_DEFAULT_PROVIDER=airalo  # Set to 'airalo' to use it as default
TELEPHONY_PARALLEL_OPERATION=true  # Enable fallback to Twilio if eSIM fails

# WebRTC Configuration for Voice over eSIM
WEBRTC_ENABLED=true
WEBRTC_STUN_SERVERS=stun:stun.l.google.com:19302,stun:stun1.l.google.com:19302
WEBRTC_TURN_SERVER_URL=turn:turn.callsaver.app:3478
WEBRTC_TURN_SERVER_USERNAME=webrtc
WEBRTC_TURN_SERVER_CREDENTIAL=your_credential_here
WEBRTC_ICE_TRANSPORT_POLICY=all
WEBRTC_SIGNALING_PORT=3001
WEBRTC_SIGNALING_SECURE=true
