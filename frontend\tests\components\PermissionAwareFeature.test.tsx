import React from 'react';
import { render, screen } from '@testing-library/react';
import PermissionAwareFeature from '../../components/ui/PermissionAwareFeature';
import { useFeatureFlag } from '../../hooks/useFeatureFlag';

// Mock the useFeatureFlag hook
jest.mock('../../hooks/useFeatureFlag', () => ({
  useFeatureFlag: jest.fn()
}));

describe('PermissionAwareFeature', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders children when feature is enabled', () => {
    // Mock the hook to return true (feature enabled)
    (useFeatureFlag as jest.Mock).mockReturnValue(true);

    render(
      <PermissionAwareFeature name="test-feature" permission="feature:use:any">
        <div data-testid="feature-content">Feature Content</div>
      </PermissionAwareFeature>
    );

    expect(screen.getByTestId('feature-content')).toBeInTheDocument();
    expect(screen.getByText('Feature Content')).toBeInTheDocument();
  });

  test('renders fallback when feature is disabled', () => {
    // Mock the hook to return false (feature disabled)
    (useFeatureFlag as jest.Mock).mockReturnValue(false);

    render(
      <PermissionAwareFeature 
        name="test-feature" 
        permission="feature:use:any"
        fallback={<div data-testid="fallback-content">Fallback Content</div>}
      >
        <div data-testid="feature-content">Feature Content</div>
      </PermissionAwareFeature>
    );

    expect(screen.queryByTestId('feature-content')).not.toBeInTheDocument();
    expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
    expect(screen.getByText('Fallback Content')).toBeInTheDocument();
  });

  test('renders disabled content when renderDisabled is true', () => {
    // Mock the hook to return false (feature disabled)
    (useFeatureFlag as jest.Mock).mockReturnValue(false);

    render(
      <PermissionAwareFeature 
        name="test-feature" 
        permission="feature:use:any"
        renderDisabled={true}
        disabledClassName="disabled-feature"
      >
        <div data-testid="feature-content">Feature Content</div>
      </PermissionAwareFeature>
    );

    const disabledContainer = screen.getByTestId('feature-content').parentElement;
    expect(disabledContainer).toHaveClass('disabled-feature');
    expect(screen.getByText('Feature Content')).toBeInTheDocument();
  });

  test('passes correct parameters to useFeatureFlag', () => {
    // Mock the hook to return true
    (useFeatureFlag as jest.Mock).mockReturnValue(true);

    render(
      <PermissionAwareFeature 
        name="test-feature" 
        permission="feature:use:any"
        description="Test feature description"
      >
        <div>Feature Content</div>
      </PermissionAwareFeature>
    );

    // Check that useFeatureFlag was called with the correct parameters
    expect(useFeatureFlag).toHaveBeenCalledWith({
      name: 'test-feature',
      permission: 'feature:use:any',
      anyPermission: undefined,
      allPermissions: undefined,
      resource: undefined,
      action: 'use',
      scope: 'any',
      description: 'Test feature description',
      defaultEnabled: false,
    });
  });

  test('passes resource parameters to useFeatureFlag', () => {
    // Mock the hook to return true
    (useFeatureFlag as jest.Mock).mockReturnValue(true);

    render(
      <PermissionAwareFeature 
        name="test-feature" 
        resource="feature"
        action="read"
        scope="self"
      >
        <div>Feature Content</div>
      </PermissionAwareFeature>
    );

    // Check that useFeatureFlag was called with the correct parameters
    expect(useFeatureFlag).toHaveBeenCalledWith({
      name: 'test-feature',
      permission: undefined,
      anyPermission: undefined,
      allPermissions: undefined,
      resource: 'feature',
      action: 'read',
      scope: 'self',
      description: undefined,
      defaultEnabled: false,
    });
  });

  test('passes permission arrays to useFeatureFlag', () => {
    // Mock the hook to return true
    (useFeatureFlag as jest.Mock).mockReturnValue(true);

    render(
      <PermissionAwareFeature 
        name="test-feature" 
        anyPermission={['feature:use:any', 'feature:read:any']}
      >
        <div>Feature Content</div>
      </PermissionAwareFeature>
    );

    // Check that useFeatureFlag was called with the correct parameters
    expect(useFeatureFlag).toHaveBeenCalledWith({
      name: 'test-feature',
      permission: undefined,
      anyPermission: ['feature:use:any', 'feature:read:any'],
      allPermissions: undefined,
      resource: undefined,
      action: 'use',
      scope: 'any',
      description: undefined,
      defaultEnabled: false,
    });
  });
});
