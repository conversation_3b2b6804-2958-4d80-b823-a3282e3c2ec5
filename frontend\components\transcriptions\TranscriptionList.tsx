'use client';

import { format } from 'date-fns';
import { 
  PhoneIcon, 
  ClockIcon, 
  FaceSmileIcon, 
  FaceFrownIcon, 
  MinusCircleIcon 
} from '@heroicons/react/24/outline';

interface Transcription {
  id: string;
  callSid: string;
  from: string;
  to: string;
  duration: number;
  timestamp: string;
  transcriptionText: string;
  sentimentData?: {
    overall: string;
    overallScore: number;
  };
  keywords?: Array<{ text: string; relevance: number }>;
}

interface TranscriptionListProps {
  transcriptions: Transcription[];
  onSelect: (id: string) => void;
}

export default function TranscriptionList({ transcriptions, onSelect }: TranscriptionListProps) {
  // Helper function to format duration
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Helper function to get sentiment icon
  const getSentimentIcon = (sentiment?: string) => {
    if (!sentiment) return <MinusCircleIcon className="h-5 w-5 text-gray-400" />;
    
    switch (sentiment.toLowerCase()) {
      case 'positive':
        return <FaceSmileIcon className="h-5 w-5 text-green-500" />;
      case 'negative':
        return <FaceFrownIcon className="h-5 w-5 text-red-500" />;
      default:
        return <MinusCircleIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  // Helper function to truncate text
  const truncateText = (text: string, maxLength: number) => {
    if (!text) return 'No transcription available';
    return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
      <ul className="divide-y divide-gray-200 dark:divide-gray-700">
        {transcriptions.map((transcription) => (
          <li 
            key={transcription.id}
            className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer"
            onClick={() => onSelect(transcription.id)}
          >
            <div className="px-4 py-4 sm:px-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <PhoneIcon className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {transcription.from} → {transcription.to}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {format(new Date(transcription.timestamp), 'MMM d, yyyy h:mm a')}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <ClockIcon className="h-5 w-5 mr-1" />
                    {formatDuration(transcription.duration)}
                  </div>
                  <div className="flex items-center">
                    {getSentimentIcon(transcription.sentimentData?.overall)}
                  </div>
                </div>
              </div>
              <div className="mt-2">
                <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                  {truncateText(transcription.transcriptionText, 150)}
                </p>
              </div>
              {transcription.keywords && transcription.keywords.length > 0 && (
                <div className="mt-2 flex flex-wrap gap-1">
                  {transcription.keywords
                    .sort((a, b) => b.relevance - a.relevance)
                    .slice(0, 5)
                    .map((keyword, index) => (
                      <span 
                        key={index}
                        className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200"
                      >
                        {keyword.text}
                      </span>
                    ))}
                </div>
              )}
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
}
