---
description: Defines the strategy for caching data within the backend systems.
---
# Backend Caching Strategy (`caching_strategy.mdc`)

## 1. Purpose and Scope

**Purpose:** To define a consistent strategy for implementing caching within the CallSaver backend services to improve API response times, reduce database load, and minimize calls to external services where appropriate.

**Scope:**
- Caching goals (performance improvement, load reduction).
- Types of caches (in-memory, distributed).
- Recommended caching technology (e.g., NodeCache, Redis, Memcached).
- Cache key naming conventions.
- Cache invalidation strategies (TTL-based, event-based).
- Data candidates for caching.
- Considerations for multi-tenancy and data consistency.
- Monitoring cache performance.

## 2. Goals

- **Reduce Latency:** Serve frequently accessed, slowly changing data faster by avoiding database or external API calls.
- **Reduce Database Load:** Decrease the number of read queries hitting the primary database.
- **Reduce External API Calls:** Minimize calls to rate-limited or costly external services for data that doesn't change often.
- **Improve Scalability:** Reduce bottlenecks on downstream systems.

## 3. Caching Technologies

- **In-Memory Cache (NodeCache):**
    - **Pros:** Very fast access, simple to implement within a single service instance.
    - **Cons:** Cache is local to each service instance (not shared), cache is lost on restart, limited by instance memory.
    - **Use Cases:** Caching data frequently accessed *within* a single request lifecycle or by a single instance for short periods (e.g., user permissions during a request, temporary computation results). Suitable for basic caching needs identified in early implementation steps.
    - **Implementation:** `back/backend/lib/cacheService.js` (already created with basic functions).
- **Distributed Cache (Recommended for Scalability - e.g., Redis, Memcached):**
    - **Pros:** Shared cache accessible by multiple service instances, data persists across restarts (if configured), supports more advanced data structures and eviction policies.
    - **Cons:** Adds an external dependency, slightly higher latency than in-memory, requires network communication.
    - **Use Cases:** Caching frequently accessed data needed across multiple API requests or service instances (e.g., user sessions, frequently read database entities, results from external API calls). Essential for horizontally scaled backend services.
    - **Technology Choice:** [Specify Technology - e.g., Redis] - Rationale: Widely used, performant, supports various data structures, good Node.js clients.

## 4. Cache Key Naming Conventions

- Use a consistent, hierarchical naming convention to avoid collisions and facilitate pattern-based operations (like invalidation).
- Include object type, tenant identifier (CRITICAL), and specific object ID.
- **Format:** `cache:<tenantId>:<objectType>:<objectId>[:<specificAttribute>]`
- **Examples:**
    - `cache:org123:user:usr456` (User object)
    - `cache:org123:number:num789:config` (Specific config for a number)
    - `cache:global:esim:plans:us` (Non-tenant specific data, use 'global' or similar namespace)

## 5. Cache Invalidation Strategies

- **Time-To-Live (TTL):** Set an expiration time for cached items. Simple and effective for data that can tolerate some staleness. Choose TTLs appropriate for the data's volatility (e.g., minutes for user sessions, hours/days for stable config).
- **Explicit Invalidation (Event-Based/Write-Through):** When data is updated or deleted in the primary data store (database), explicitly remove the corresponding item(s) from the cache.
    - **Implementation:** Trigger invalidation logic within the service methods that perform writes (updates/deletes).
    - **Example:** When `updateUserProfile` is called, delete `cache:org123:user:usr456` from the cache.
- **Pattern Invalidation (Use with Caution):** Invalidate multiple cache keys based on a pattern (e.g., invalidate all cached items for a specific user). Supported by Redis (`KEYS` + `DEL` - potentially slow, or `SCAN` + `DEL`). Use judiciously as it can be resource-intensive.
- **Write-Through Caching:** Update the cache immediately after successfully writing to the database. Ensures cache consistency but adds latency to write operations.

## 6. Data Candidates for Caching

Prioritize caching data that is:
- **Frequently Read:** Accessed often by API requests.
- **Slowly Changing:** Data that doesn't update frequently.
- **Expensive to Compute/Fetch:** Data retrieved from complex database queries or slow external API calls.

**Examples:**
- User profile and permissions (short TTL or explicit invalidation on update).
- Organization settings (medium TTL or explicit invalidation).
- Phone number configurations (medium TTL or explicit invalidation).
- Results from external validation services (e.g., phone number lookup - medium/long TTL).
- Available eSIM plans (if fetched from external provider - medium TTL).
- Help center articles/categories (long TTL or explicit invalidation on update).
- Feature flag states (managed by feature flag SDK caching, typically short TTL with real-time updates).

**Avoid Caching:**
- Highly volatile data (e.g., real-time call status).
- Sensitive data where staleness poses a security risk.
- Large objects that consume excessive cache memory without significant benefit.

## 7. Multi-Tenancy Considerations

- As per the naming convention, **ALWAYS** include `tenantId` (e.g., `organizationId`) in cache keys for tenant-specific data to ensure strict data isolation. Failure to do so can lead to severe security vulnerabilities.

## 8. Cache Interaction Pattern (Cache-Aside)

The most common pattern:
1. Application requests data.
2. Application checks the cache first using the appropriate key.
3. **Cache Hit:** If data is found in the cache, return it.
4. **Cache Miss:** If data is not found:
    a. Fetch the data from the primary source (database, external API).
    b. Store the fetched data in the cache with an appropriate TTL.
    c. Return the data to the application.

## 9. Monitoring

- **Cache Hit Rate:** Monitor the ratio of cache hits to misses. A low hit rate might indicate ineffective caching (wrong data, bad keys, TTL too short).
- **Cache Latency:** Monitor the time taken for cache operations (GET, SET, DEL).
- **Cache Memory Usage:** Monitor memory consumption of the cache (especially important for distributed caches like Redis).
- **Evictions/Expirations:** Monitor the rate of items being evicted or expiring.

## 10. Related Documents

- `docs/architecture/multi_tenant_data_isolation.mdc`
- `docs/architecture/service_failover_and_redundancy.mdc` (Cache service resilience)
- `back/backend/lib/cacheService.js` (Initial implementation)
- Specific service implementations interacting with the cache.
