---
description: 
globs: 
alwaysApply: false
---
---
description: Defines the architectural strategy for ensuring strict data isolation between tenants (organizations).
---
# Multi-Tenant Data Isolation Strategy (`multi_tenant_data_isolation.mdc`)

## 1. Purpose and Scope

**Purpose:** To define the architectural principles and implementation mechanisms ensuring that data belonging to one tenant (Organization) is strictly isolated and inaccessible to other tenants within the shared CallSaver infrastructure.

**Scope:**
- Core isolation principle (Organization ID scoping).
- Database schema design for isolation.
- API layer enforcement.
- Backend service logic requirements.
- Considerations for shared resources (e.g., task queues, caches).
- Testing strategies for isolation.
- Potential future scaling models (e.g., sharding).

## 2. Core Principle: Organization ID Scoping

- **Primary Key:** The `organizationId` will serve as the primary key for data isolation across the platform.
- **Mandate:** Every piece of data that belongs to a specific tenant MUST be associated with that tenant's unique `organizationId`.
- **Universality:** This principle applies to database records, cached data, queued tasks, logs (where applicable), and any other tenant-specific resource.

## 3. Implementation Mechanisms

### 3.1. Database Schema (Prisma/Postgres)

- **Mandatory `organizationId` Column:** Every table containing tenant-specific data MUST include a non-nullable `organizationId` column, referencing the `Organization` table (likely via a foreign key constraint).
    - *Example Tables:* `User`, `PhoneNumber`, `CallLog`, `Message`, `AutomationRule`, `CreditTransaction`, `ApiKey`, `EsimPurchase`, `Appointment`, `BlockedNumber`, etc.
- **Composite Keys:** Consider using composite primary or unique keys including `organizationId` where appropriate to enforce uniqueness within a tenant's scope (e.g., `UNIQUE(organizationId, phoneNumber)`).
- **Row-Level Security (RLS):**
    - **Strong Recommendation:** Implement Postgres Row-Level Security (RLS) policies on all tenant-specific tables.
    - **Policy Logic:** RLS policies should restrict data access based on the `organizationId` associated with the current authenticated user's session. This provides a strong database-level guarantee against accidental cross-tenant data access, even if application code has bugs.
    - **Implementation:** Define RLS policies in migrations and ensure the application sets the correct `request.organization_id` (or similar session variable) for the database session during request handling (e.g., via middleware).
- **Avoid Shared Tables for Tenant Data:** Do not mix data from multiple tenants within the same row or use generic tables without a clear `organizationId` discriminator for tenant-specific information.

### 3.2. API Layer (Gateway / Middleware)

- **Authentication Context:** Authentication middleware MUST reliably determine the `organizationId` associated with the authenticated user (from session token, API key lookup) and attach it to the request context.
- **Authorization Checks:** While RLS provides database-level safety, API-level checks should still ensure users only attempt to access resources associated with their own `organizationId`.
- **Input Validation:** Validate that any `organizationId` provided in request paths or bodies matches the authenticated user's `organizationId`.

### 3.3. Backend Service Logic

- **Universal Filtering:** ALL database queries (reads, updates, deletes) performed by backend services on tenant-specific data MUST include a `WHERE` clause filtering by the correct `organizationId` obtained from the request context.
    - **Prisma Example:** `prisma.callLog.findMany({ where: { organizationId: context.organizationId, ...otherFilters } })`
- **ORM/Query Builder Configuration:** Configure ORMs or query builders to potentially apply the `organizationId` filter automatically where feasible (e.g., through base repositories or query scopes), reducing the risk of omission.
- **Data Creation:** When creating new tenant-specific data, the correct `organizationId` MUST be explicitly set.

### 3.4. Shared Resources

- **Task Queues:** Ensure task payloads include the relevant `organizationId` so that worker services process tasks within the correct tenant context. Workers must apply the same `organizationId` scoping to database operations as API services.
- **Caching:** Cache keys for tenant-specific data MUST incorporate the `organizationId` to prevent cross-tenant cache poisoning (e.g., `cache:org:<orgId>:user:<userId>`).
- **Logging:** While logs might be aggregated centrally, avoid logging sensitive cross-tenant data in a way that violates isolation. Use correlation IDs and filter logs by `organizationId` during analysis where appropriate.

## 4. Testing Strategies

- **Unit Tests:** Verify that repository/service methods correctly apply `organizationId` filters.
- **Integration Tests:**
    - Create test data for multiple tenants.
    - Authenticate as a user from Tenant A.
    - Attempt to access/modify data belonging to Tenant B via API calls. Assert that these attempts fail with appropriate errors (e.g., 403 Forbidden, 404 Not Found).
    - Verify that API calls only return data belonging to the authenticated user's tenant (Tenant A).
- **RLS Testing:** Design specific tests to verify that database-level RLS policies correctly prevent cross-tenant access, even if application-level filters were bypassed (requires careful test setup).

## 5. Future Considerations

- **Database Sharding:** If the platform scales significantly, consider sharding the database based on `organizationId` (either physically separate databases or logical sharding within a larger cluster) for improved performance and scalability isolation. This adds complexity and should only be pursued when necessary.

## 6. Related Documents

- `back/backend/prisma/schema.prisma`
- `docs/functional_specs/api_gateway_routes.mdc`
- `docs/functional_specs/user_roles_and_permissions.mdc`
- `docs/functional_specs/session_management_strategy.mdc`
- `docs/dev_guides/testing_strategy.mdc`
- `docs/security_audit.md` (if exists)
