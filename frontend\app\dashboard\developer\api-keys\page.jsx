'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../components/ui/card';

export default function ApiKeysPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [apiKeys, setApiKeys] = useState([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [newKeyName, setNewKeyName] = useState('');
  const [newKeyPermissions, setNewKeyPermissions] = useState({
    read: true,
    write: false,
    delete: false
  });
  const [newKeyCreated, setNewKeyCreated] = useState(null);

  // Mock API key data
  const mockApiKeys = [
    { id: 'key_1a2b3c4d5e6f', name: 'Production API Key', prefix: 'cs_live_', lastUsed: '2023-05-15T10:30:00Z', created: '2023-01-10T08:15:00Z', permissions: { read: true, write: true, delete: true } },
    { id: 'key_7g8h9i0j1k2l', name: 'Development API Key', prefix: 'cs_test_', lastUsed: '2023-05-14T14:45:00Z', created: '2023-02-15T09:20:00Z', permissions: { read: true, write: true, delete: false } },
    { id: 'key_3m4n5o6p7q8r', name: 'Read-Only API Key', prefix: 'cs_test_', lastUsed: '2023-04-30T11:20:00Z', created: '2023-03-05T11:10:00Z', permissions: { read: true, write: false, delete: false } },
  ];

  useEffect(() => {
    const fetchApiKeys = async () => {
      try {
        setIsLoading(true);
        // In a real implementation, this would be an API call to get API keys
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setApiKeys(mockApiKeys);
      } catch (err) {
        console.error('Error fetching API keys:', err);
        setError('Failed to load API keys. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchApiKeys();
  }, []);

  const handleCreateKey = async () => {
    try {
      setIsLoading(true);
      // In a real implementation, this would be an API call to create a new API key
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Generate a mock API key
      const newKey = {
        id: `key_${Math.random().toString(36).substring(2, 15)}`,
        name: newKeyName,
        prefix: 'cs_test_',
        lastUsed: null,
        created: new Date().toISOString(),
        permissions: newKeyPermissions
      };
      
      // In a real implementation, the API would return the full key only once
      const fullKey = `${newKey.prefix}${Math.random().toString(36).substring(2, 30)}`;
      
      setApiKeys([newKey, ...apiKeys]);
      setNewKeyCreated({
        ...newKey,
        fullKey
      });
      setIsCreateModalOpen(false);
      setNewKeyName('');
      setNewKeyPermissions({
        read: true,
        write: false,
        delete: false
      });
    } catch (err) {
      console.error('Error creating API key:', err);
      setError('Failed to create API key. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRevokeKey = (keyId) => {
    if (window.confirm('Are you sure you want to revoke this API key? This action cannot be undone and any applications using this key will no longer be able to access the API.')) {
      // In a real implementation, this would be an API call to revoke the API key
      console.log(`Revoking API key with ID: ${keyId}`);
      // Then refetch the API keys
      setApiKeys(apiKeys.filter(key => key.id !== keyId));
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-white">API Keys</h1>
        <a href="/dashboard/developer" className="text-purple-400 hover:text-purple-300">
          Back to Developer Dashboard
        </a>
      </div>
      
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
          <p className="text-red-200">{error}</p>
        </div>
      )}
      
      {/* Newly created API key alert */}
      {newKeyCreated && (
        <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4 mb-6">
          <h3 className="text-green-200 font-semibold mb-2">API Key Created Successfully</h3>
          <p className="text-green-200 mb-2">Make sure to copy your API key now. You won't be able to see it again!</p>
          <div className="bg-gray-800 p-3 rounded-lg font-mono text-sm text-white mb-2 break-all">
            {newKeyCreated.fullKey}
          </div>
          <div className="flex justify-end">
            <button 
              onClick={() => {
                navigator.clipboard.writeText(newKeyCreated.fullKey);
                alert('API key copied to clipboard!');
              }}
              className="bg-green-600 hover:bg-green-700 text-white py-1 px-3 rounded-lg text-sm transition-colors"
            >
              Copy to Clipboard
            </button>
          </div>
        </div>
      )}
      
      <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20 mb-6">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="text-white">API Keys</CardTitle>
            <CardDescription className="text-gray-400">Manage your API keys for accessing the CallSaver API</CardDescription>
          </div>
          <button 
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg transition-colors"
          >
            Create New API Key
          </button>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead className="bg-gray-700/50 text-gray-300">
                <tr>
                  <th className="px-4 py-2 rounded-tl-lg">Name</th>
                  <th className="px-4 py-2">Key</th>
                  <th className="px-4 py-2">Permissions</th>
                  <th className="px-4 py-2">Created</th>
                  <th className="px-4 py-2">Last Used</th>
                  <th className="px-4 py-2 rounded-tr-lg">Actions</th>
                </tr>
              </thead>
              <tbody className="text-gray-300">
                {isLoading ? (
                  <tr>
                    <td colSpan="6" className="px-4 py-2 text-center">Loading API keys...</td>
                  </tr>
                ) : apiKeys.length === 0 ? (
                  <tr>
                    <td colSpan="6" className="px-4 py-2 text-center">No API keys found</td>
                  </tr>
                ) : (
                  apiKeys.map((key, index) => (
                    <tr key={key.id} className={index % 2 === 0 ? 'bg-gray-700/30' : 'bg-gray-700/10'}>
                      <td className="px-4 py-2 font-medium">{key.name}</td>
                      <td className="px-4 py-2 font-mono text-sm">{key.prefix}•••••••••••••••</td>
                      <td className="px-4 py-2">
                        <div className="flex space-x-2">
                          {key.permissions.read && (
                            <span className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded-full text-xs">Read</span>
                          )}
                          {key.permissions.write && (
                            <span className="px-2 py-1 bg-green-500/20 text-green-300 rounded-full text-xs">Write</span>
                          )}
                          {key.permissions.delete && (
                            <span className="px-2 py-1 bg-red-500/20 text-red-300 rounded-full text-xs">Delete</span>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-2">{formatDate(key.created)}</td>
                      <td className="px-4 py-2">{formatDate(key.lastUsed)}</td>
                      <td className="px-4 py-2">
                        <button 
                          onClick={() => handleRevokeKey(key.id)}
                          className="text-red-400 hover:text-red-300"
                        >
                          Revoke
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
      
      <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
        <CardHeader>
          <CardTitle className="text-white">API Key Security</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 text-gray-300">
            <p>Your API keys carry many privileges, so be sure to keep them secure! Do not share your API keys in publicly accessible areas such as GitHub, client-side code, etc.</p>
            
            <h3 className="text-lg font-semibold text-white">Authentication</h3>
            <p>Authentication to the API is performed via HTTP Bearer Authentication. Provide your API key as the bearer token value in the Authorization header.</p>
            
            <div className="bg-gray-700 p-3 rounded-lg font-mono text-sm">
              Authorization: Bearer cs_test_your_api_key
            </div>
            
            <h3 className="text-lg font-semibold text-white">Rate Limiting</h3>
            <p>The API is rate limited to prevent abuse. If you exceed the rate limit, you will receive a 429 Too Many Requests response. Please contact support if you need higher rate limits.</p>
          </div>
        </CardContent>
      </Card>
      
      {/* Create API Key Modal */}
      {isCreateModalOpen && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-bold text-white mb-4">Create New API Key</h2>
            
            <div className="space-y-4">
              <div>
                <label className="text-gray-300 block mb-1">Key Name</label>
                <input 
                  type="text" 
                  value={newKeyName} 
                  onChange={(e) => setNewKeyName(e.target.value)}
                  placeholder="e.g., Production API Key"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                />
              </div>
              
              <div>
                <label className="text-gray-300 block mb-2">Permissions</label>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <input 
                      type="checkbox" 
                      id="perm-read"
                      checked={newKeyPermissions.read} 
                      onChange={(e) => setNewKeyPermissions({...newKeyPermissions, read: e.target.checked})}
                      className="mr-2 h-4 w-4"
                    />
                    <label htmlFor="perm-read" className="text-gray-300">Read (GET requests)</label>
                  </div>
                  <div className="flex items-center">
                    <input 
                      type="checkbox" 
                      id="perm-write"
                      checked={newKeyPermissions.write} 
                      onChange={(e) => setNewKeyPermissions({...newKeyPermissions, write: e.target.checked})}
                      className="mr-2 h-4 w-4"
                    />
                    <label htmlFor="perm-write" className="text-gray-300">Write (POST, PUT, PATCH requests)</label>
                  </div>
                  <div className="flex items-center">
                    <input 
                      type="checkbox" 
                      id="perm-delete"
                      checked={newKeyPermissions.delete} 
                      onChange={(e) => setNewKeyPermissions({...newKeyPermissions, delete: e.target.checked})}
                      className="mr-2 h-4 w-4"
                    />
                    <label htmlFor="perm-delete" className="text-gray-300">Delete (DELETE requests)</label>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end mt-6 space-x-2">
              <button 
                onClick={() => setIsCreateModalOpen(false)}
                className="bg-gray-700 text-white px-4 py-2 rounded-lg"
              >
                Cancel
              </button>
              <button 
                onClick={handleCreateKey}
                disabled={!newKeyName.trim() || isLoading}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Creating...' : 'Create API Key'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
