"use client";

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { conversationData } from '../data/conversationData';

export default function StaticConversationCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [randomPhone, setRandomPhone] = useState("(*************"); // Default placeholder
  const containerRef = useRef(null);
  
  // Handle pagination
  const paginate = (newIndex) => {
    if (newIndex === currentIndex) return;
    
    setDirection(newIndex > currentIndex ? 1 : -1);
    setCurrentIndex(newIndex);
  };
  
  // Simplified slide variants for basic transitions
  const slideVariants = {
    enter: (direction) => ({
      x: direction > 0 ? '100%' : '-100%',
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction) => ({
      x: direction < 0 ? '100%' : '-100%',
      opacity: 0,
    }),
  };
  
  const data = conversationData[currentIndex];
  
  // Function to generate random US phone number
  const generateRandomPhone = () => {
    const area = Math.floor(Math.random() * 900) + 100; // Random area code (100-999)
    const prefix = Math.floor(Math.random() * 900) + 100; // Random prefix (100-999)
    const lineNum = Math.floor(Math.random() * 9000) + 1000; // Random line number (1000-9999)
    return `(${area}) ${prefix}-${lineNum}`;
  };
  
  // Generate random phone number on client-side only using useEffect
  useEffect(() => {
    setRandomPhone(generateRandomPhone());
  }, [currentIndex]); // Regenerate when currentIndex changes
  
  return (
    <div className="w-full">
      {/* Title section moved to the top */}
      <div className="text-center mb-8">
        <h2 className="heading-lg laser-gradient-text" data-text="AI-Powered Solutions">
          AI-Powered Solutions
        </h2>
        <p className="subheading-text max-w-3xl mx-auto">
          Experience intelligent call handling that adapts to your business needs
        </p>
      </div>
      
      <div className="relative w-full max-w-4xl mx-auto">
        <div className="relative w-full max-w-4xl mx-auto rounded-2xl overflow-hidden shadow-xl bg-gradient-to-b from-gray-900/90 to-purple-900/60 backdrop-blur-sm border border-purple-500/20 text-white h-[600px]">
          <AnimatePresence initial={false} custom={direction} mode="wait">
            <motion.div
              key={currentIndex}
              custom={direction}
              variants={slideVariants}
              initial="enter"
              animate="center"
              exit="exit"
              transition={{
                x: { type: "spring", stiffness: 300, damping: 30 },
                opacity: { duration: 0.2 }
              }}
              className="absolute w-full"
            >
              {/* Main Conversation Card */}
              <div className="w-full max-w-md mx-auto bg-[#111125] rounded-2xl overflow-hidden shadow-2xl border border-purple-900/20">
                {/* Enhanced branded header */}
                <div className="bg-gradient-to-r from-[#1a1a35] to-[#221c38] p-5">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <div className="bg-gradient-to-br from-purple-600 to-indigo-700 rounded-full w-12 h-12 flex items-center justify-center mr-3 shadow-lg shadow-purple-900/30">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-white font-bold text-xl">CallSaver Assistant</h3>
                        <p className="text-purple-300 text-sm">Always active</p>
                      </div>
                    </div>
                    <div className="flex items-center bg-purple-900/30 px-2 py-1 rounded">
                      <span className="relative flex h-3 w-3 mr-2">
                        <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                        <span className="relative inline-flex rounded-full h-3 w-3 bg-green-500"></span>
                      </span>
                      <span className="text-xs text-green-300 font-medium">Online</span>
                    </div>
                  </div>
                  
                  {/* Enhanced Missed call alert - Moved to top */}
                  <div className="bg-gradient-to-r from-[#331418] to-[#3a1418] p-4 border-l-4 border-red-500 flex items-start mb-2">
                    <div className="bg-red-500/30 rounded-full p-2.5 mr-3 shadow-md shadow-red-900/20">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-white font-bold text-base">•Missed Call - potential customer</h4>
                      <p className="text-red-300 text-sm font-medium">{randomPhone} - 2 minutes ago</p>
                    </div>
                  </div>
                  
                  {/* Callsaver Ecosystem Label - Moved below missed call */}
                  <div className="bg-purple-900/30 rounded-lg px-4 py-2.5 border border-purple-800/30 shadow-inner">
                    <p className="text-purple-200 text-sm font-medium flex items-center">
                      <span className="inline-block w-2 h-2 bg-purple-400 rounded-full mr-2"></span>
                      <span className="font-semibold text-white">Call Saver AI</span>
                      <span className="mx-2 text-purple-400">•</span>
                      <span>Callsaver Ecosystem Triggered via Call-Forwarding</span>
                    </p>
                  </div>
                </div>
                
                {/* Enhanced Category tags */}
                {data.categories && data.categories.length > 0 && (
                  <div className="px-5 pt-5 pb-3 flex flex-wrap gap-2">
                    {data.categories.map((category, index) => (
                      <span
                        key={index}
                        className="bg-gradient-to-r from-purple-700/30 to-purple-600/30 text-purple-200 text-xs px-3 py-1.5 rounded-full flex items-center shadow-sm"
                      >
                        {category.icon && (
                          <span className="mr-1.5">{category.icon}</span>
                        )}
                        <span className="font-medium">{category.label}</span>
                      </span>
                    ))}
                  </div>
                )}
                
                {/* Enhanced Chat messages container */}
                <div className="px-5 py-5 h-72 overflow-y-auto hide-scrollbar bg-[#13132a]/30" style={{ scrollbarWidth: 'none' }}>
                  {data.context && (
                    <div className="text-center my-3 px-4">
                      <p className="text-xs text-gray-400 bg-gray-800/30 py-1.5 px-3 rounded-full inline-block">{data.context}</p>
                    </div>
                  )}
                  
                  {/* Enhanced message styling */}
                  {data.messages.map((message, index) => (
                    <div 
                      key={index}
                      className={`flex mb-5 ${message.sender === 'ai' ? 'justify-start' : 'justify-end'}`}
                    >
                      {message.sender === 'ai' && (
                        <div className="mr-2.5 flex-shrink-0">
                          <div className="w-9 h-9 rounded-full bg-gradient-to-br from-[#3b3363] to-[#2c2452] flex items-center justify-center shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                          </div>
                        </div>
                      )}
                      
                      <div 
                        className={`max-w-[75%] relative ${
                          message.sender === 'ai' 
                            ? 'bg-gradient-to-br from-[#28293d] to-[#252638] text-white rounded-r-xl rounded-bl-xl shadow-md shadow-purple-900/10' 
                            : 'bg-gradient-to-br from-[#6946db] to-[#5935bf] text-white rounded-l-xl rounded-br-xl shadow-md shadow-purple-900/20'
                        }`}
                      >
                        {/* Enhanced glow effect for AI messages */}
                        {message.sender === 'ai' && (
                          <div 
                            className="absolute inset-0 rounded-r-xl rounded-bl-xl"
                            style={{
                              boxShadow: '0 0 12px rgba(139, 92, 246, 0.15), inset 0 0 6px rgba(139, 92, 246, 0.15)',
                              pointerEvents: 'none'
                            }}
                          />
                        )}
                        
                        <div className="p-3.5">
                          {message.content}
                        </div>
                        
                        {/* Status indicator for messages */}
                        {message.status && (
                          <div className="text-right pr-2.5 pb-1.5">
                            <span className="text-xs text-gray-400">{message.status}</span>
                          </div>
                        )}
                      </div>
                      
                      {message.sender === 'user' && (
                        <div className="ml-2.5 flex-shrink-0">
                          <div className="w-9 h-9 rounded-full bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
                
                {/* Enhanced Action buttons */}
                {data.actionButtons && data.actionButtons.length > 0 && (
                  <div className="p-4 bg-gradient-to-b from-[#1a1a35]/60 to-[#1a1a35]/90 flex justify-center gap-3.5 flex-wrap border-t border-purple-900/20">
                    {data.actionButtons.map((button, index) => (
                      <button
                        key={index}
                        className={`flex items-center px-5 py-2.5 rounded-full text-sm font-medium transition-all ${
                          button.primary 
                            ? 'bg-gradient-to-r from-purple-600 to-indigo-600 text-white shadow-md shadow-purple-900/30 hover:shadow-lg hover:shadow-purple-700/40 hover:translate-y-[-2px]' 
                            : 'bg-gradient-to-r from-[#2c2c44] to-[#252538] text-white hover:bg-[#33334d] hover:shadow-md hover:shadow-black/20 hover:translate-y-[-2px]'
                        }`}
                      >
                        {button.icon && (
                          <span className="mr-2">
                            {button.icon}
                          </span>
                        )}
                        {button.label}
                      </button>
                    ))}
                  </div>
                )}
                
                {/* Enhanced footer with Callsaver Ecosystem mention */}
                <div className="bg-gradient-to-r from-[#1a1a35] to-[#221c38] border-t border-gray-800/50">
                  {/* AI assistant status */}
                  <div className="px-5 py-3.5 flex items-center justify-between text-xs border-b border-gray-800/30">
                    <div className="flex items-center">
                      <span className="relative flex h-2 w-2 mr-2">
                        <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                        <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
                      </span>
                      <span className="text-gray-300">AI assistant is online</span>
                    </div>
                    
                    <div className="bg-green-900/20 px-2 py-0.5 rounded text-green-400 font-medium">100% client satisfaction rating</div>
                  </div>
                  
                  {/* Callsaver Ecosystem footer */}
                  <div className="p-4">
                    <div className="text-center">
                      <span className="bg-purple-900/30 px-4 py-1.5 rounded-full text-sm text-purple-200 font-medium inline-block">
                        <span className="font-semibold bg-clip-text text-transparent bg-gradient-to-r from-purple-300 to-purple-100">Powered by Callsaver Ecosystem</span> · Never lose a lead again!
                      </span>
                    </div>
                    <div className="mt-2 text-center text-xs text-gray-500">
                      Visit <a href="https://callsaver.app" className="text-purple-400 hover:text-purple-300 transition-colors">Callsaver.app</a> to learn more
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
          
          {/* Enhanced Navigation arrows */}
          <button 
            onClick={() => currentIndex > 0 && paginate(currentIndex - 1)}
            className={`absolute left-2 top-1/2 -translate-y-1/2 z-10 p-3 rounded-full bg-gray-800/70 backdrop-blur-sm text-white shadow-lg hover:bg-gray-700/80 ${currentIndex === 0 ? 'opacity-30 cursor-not-allowed' : 'opacity-80 hover:opacity-100'}`}
            disabled={currentIndex === 0}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          
          <button 
            onClick={() => currentIndex < conversationData.length - 1 && paginate(currentIndex + 1)}
            className={`absolute right-2 top-1/2 -translate-y-1/2 z-10 p-3 rounded-full bg-gray-800/70 backdrop-blur-sm text-white shadow-lg hover:bg-gray-700/80 ${currentIndex === conversationData.length - 1 ? 'opacity-30 cursor-not-allowed' : 'opacity-80 hover:opacity-100'}`}
            disabled={currentIndex === conversationData.length - 1}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
        
        {/* Enhanced Pagination dots with more visible icons */}
        <div className="absolute bottom-4 w-full flex justify-center items-center space-x-3">
          {/* Left arrow */}
          <button
            onClick={() => currentIndex > 0 && paginate(currentIndex - 1)}
            className="bg-white bg-opacity-20 backdrop-blur-sm text-white rounded-full p-1.5 border border-white/10 shadow-lg hover:bg-white/30 transition-all duration-300"
            aria-label="Previous conversation"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M15 19l-7-7 7-7"
              ></path>
            </svg>
          </button>

          {/* Pagination dots and icons - restructured to prevent overlapping */}
          <div className="flex flex-col items-center gap-3">
            {/* Pagination dots */}
            <div className="flex space-x-2">
              {conversationData.map((_, index) => (
                <button
                  key={index}
                  onClick={() => paginate(index)}
                  className={`h-2.5 rounded-full transition-all duration-300 ${
                    index === currentIndex
                      ? "w-8 bg-white"
                      : "w-2.5 bg-white/40"
                  }`}
                  aria-label={`Go to conversation ${index + 1}`}
                />
              ))}
            </div>
            
            {/* Scenario Icons - fixed positioning and spacing */}
            <div className="flex space-x-2 mt-1">
              {conversationData.map((conversation, index) => {
                // Determine icon based on conversation id
                let icon = '💬';
                let label = conversation.title || 'Chat';
                
                // Map specific icons based on conversation id - simplified minimal style
                switch(conversation.id) {
                  case 'tech-support':
                    icon = '⚡';
                    break;
                  case 'restaurant-booking':
                    icon = '🍽️';
                    break;
                  case 'auto-service':
                    icon = '🚗';
                    break;
                  case 'real-estate':
                    icon = '🏠';
                    break;
                  case 'property-maintenance':
                    icon = '🔧';
                    break;
                  case 'healthcare':
                    icon = '🩺';
                    break;
                  case 'ecommerce':
                    icon = '📦';
                    break;
                }
                
                return (
                  <div 
                    key={index}
                    className={`w-7 h-7 flex items-center justify-center text-xs rounded-full transition-all duration-300 cursor-pointer ${
                      index === currentIndex
                        ? "bg-white/40 backdrop-blur-sm border border-white/30 shadow-lg"
                        : "bg-white/10 hover:bg-white/20"
                    }`}
                    title={label}
                    onClick={() => paginate(index)}
                  >
                    {icon}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Right arrow */}
          <button
            onClick={() => currentIndex < conversationData.length - 1 && paginate(currentIndex + 1)}
            className="bg-white bg-opacity-20 backdrop-blur-sm text-white rounded-full p-1.5 border border-white/10 shadow-lg hover:bg-white/30 transition-all duration-300"
            aria-label="Next conversation"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 5l7 7-7 7"
              ></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
} 