---
description:
globs:
alwaysApply: false
---
---
description: Defines the strategy, structure, and best practices for internal CLI tools.
---
# Internal CLI Tooling Strategy (`internal_cli_tooling_strategy.mdc`)

## 1. Purpose and Scope

**Purpose:** To establish a consistent framework and best practices for developing, deploying, and using internal Command-Line Interface (CLI) tools within the CallSaver platform. This ensures efficiency, maintainability, security, and standardization for operational and development tasks.

**Scope:**
- Recommended CLI framework/library (e.g., `commander`, `yargs` for Node.js).
- Standard command structure and naming conventions.
- Argument parsing and validation.
- Environment targeting (development, staging, production).
- Authentication and authorization for CLI commands.
- Output formatting (human-readable, JSON).
- Logging integration.
- Common tool categories and examples (DB management, user admin, cache control, service interaction).
- Testing strategy for CLI tools.
- Location and deployment of CLI scripts.

## 2. Framework and Structure

- **Recommended Framework:** Utilize a standard library like `commander.js` or `yargs` for Node.js-based backend tools to handle command parsing, options, help messages, etc.
- **Entry Point:** A single entry point script (e.g., `back/backend/cli.js`) that acts as a dispatcher for various commands and subcommands.
- **Command Structure:** Follow a `noun-verb` or `namespace:verb` structure (e.g., `node cli.js user create --email <email>`, `node cli.js cache flush --store redis`).
- **Modularity:** Organize command logic into separate modules imported by the main entry point.

## 3. Core Features

- **Environment Targeting:**
    - CLI tools MUST explicitly target an environment (dev, staging, prod) via an argument (e.g., `--env production`) or environment variable (`NODE_ENV=production`).
    - Implement safeguards (e.g., confirmation prompts for production actions) to prevent accidental operations on the wrong environment.
    - Load environment-specific configurations accordingly (refer to `env_configuration_rules.mdc`).
- **Authentication/Authorization:**
    - Commands performing sensitive operations MUST require authentication. This could involve:
        - Reading credentials from a secure local file or environment variable.
        - Prompting for credentials (less secure, use cautiously).
        - Integrating with an internal auth mechanism (e.g., requiring an admin user's API key or temporary token).
    - Authorization checks based on user roles (refer to `user_roles_and_permissions.mdc`) should be applied where necessary.
- **Argument Parsing & Validation:**
    - Use the chosen framework's features for defining required/optional arguments and types.
    - Perform validation on inputs (e.g., email format, UUID format).
- **Output Formatting:**
    - Support both human-readable (default) and machine-readable (e.g., `--json`) output formats.
    - Use standard exit codes (0 for success, non-zero for errors).
- **Logging:**
    - Integrate with the standard backend logging library.
    - Log command execution, key parameters (excluding secrets), and outcomes.

## 4. Common Tool Categories & Examples

- **Database Management:**
    - Running migrations (`db migrate`)
    - Seeding data (`db seed --users 10`)
    - Running specific data cleanup scripts (`db cleanup-orphans`)
- **User/Organization Administration:**
    - Creating users/orgs (`user create`, `org create`)
    - Changing user roles (`user set-role --userId <id> --role admin`)
    - Listing users/orgs (`user list`, `org list --json`)
    - Managing API keys (`apikey grant --userId <id>`, `apikey revoke --keyId <id>`)
- **Cache Management:**
    - Flushing specific caches (`cache flush --store user-sessions`)
    - Inspecting cache keys (`cache list --pattern 'user:*'`)
- **Service Interaction:**
    - Triggering specific background jobs (`job trigger --name process-recordings`)
    - Checking service health (`service status --name billing`)
- **Debugging/Diagnostics:**
    - Fetching specific logs (`logs tail --service ai-worker`)
    - Simulating events (`event simulate --type stripe.charge.succeeded`)

## 5. Testing

- Implement unit tests for individual command logic modules.
- Implement integration tests that execute the CLI entry point with various arguments and assert on output, exit codes, and side effects (e.g., database changes). Mock external dependencies.

## 6. Deployment and Location

- CLI scripts should reside within the relevant backend service directory (e.g., `back/backend/`).
- Ensure CLI tools are included in the deployment package if needed on servers.
- Document available commands and their usage clearly (e.g., via `--help` flags and potentially in `docs/dev_guides/`).

## 7. Related Documents

- `docs/functional_specs/user_roles_and_permissions.mdc`
- `docs/functional_specs/env_configuration_rules.mdc`
- `docs/dev_guides/testing_strategy.mdc`
- `back/backend/lib/logger.js` (or equivalent logging utility)
