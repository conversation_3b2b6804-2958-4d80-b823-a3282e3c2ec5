"use client";

import { useState, useEffect } from 'react';
import { FiPhone } from 'react-icons/fi';

export default function PhoneSelector({ onPhoneSelected }) {
  const [phoneNumbers, setPhoneNumbers] = useState([]);
  const [selectedPhoneNumber, setSelectedPhoneNumber] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load phone numbers from API
  useEffect(() => {
    const loadPhoneNumbers = async () => {
      try {
        setIsLoading(true);
        // Call the API to get user's phone numbers
        const response = await fetch('/api/phone-numbers');
        if (response.ok) {
          const data = await response.json();
          
          // Filter out the personal number
          const filteredNumbers = (data.phoneNumbers || []).filter(
            phone => phone.number !== '+491723773552'
          );
          
          setPhoneNumbers(filteredNumbers);
          
          // Select the first number by default
          if (filteredNumbers.length > 0) {
            const defaultNumber = filteredNumbers[0].number;
            setSelectedPhoneNumber(defaultNumber);
            
            // Notify parent component
            if (onPhoneSelected) {
              onPhoneSelected(defaultNumber);
            }
          }
        }
      } catch (error) {
        console.error('Failed to load phone numbers:', error);
        // In a real app, show an error notification
      } finally {
        setIsLoading(false);
      }
    };
    
    loadPhoneNumbers();
  }, [onPhoneSelected]);
  
  const handlePhoneChange = (e) => {
    const number = e.target.value;
    setSelectedPhoneNumber(number);
    
    // Notify parent component
    if (onPhoneSelected) {
      onPhoneSelected(number);
    }
  };

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 shadow-lg p-6">
      <h2 className="text-lg font-semibold mb-2">Select Phone Number to Chat With</h2>
      <p className="text-gray-400 mb-4">
        Select a phone number to interact with your CallSaver AI assistant. You can ask questions, set up automations, or configure call handling.
      </p>
      
      <div className="relative">
        {isLoading ? (
          <div className="bg-gray-700 h-10 rounded-md animate-pulse"></div>
        ) : phoneNumbers.length > 0 ? (
          <select
            value={selectedPhoneNumber || ''}
            onChange={handlePhoneChange}
            className="w-full bg-gray-700 border border-gray-600 rounded-md py-2 pl-10 pr-4 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {phoneNumbers.map(phone => (
              <option key={phone.id} value={phone.number}>
                {phone.number} ({phone.countryCode})
              </option>
            ))}
          </select>
        ) : (
          <div className="p-3 bg-gray-700 border border-red-500 rounded-md text-center">
            <p className="text-gray-300">No phone numbers available. Please purchase a number first.</p>
          </div>
        )}
        
        {phoneNumbers.length > 0 && (
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <FiPhone className="text-gray-400" />
          </div>
        )}
      </div>
      
      <p className="text-xs text-gray-500 mt-2">
        Selected phone number will be used for AI training and automation.
      </p>
    </div>
  );
}
