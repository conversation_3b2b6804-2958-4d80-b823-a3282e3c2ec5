"use client";

import { useState } from 'react';
import Link from 'next/link';
import { 
  PhoneIcon, 
  ChatBubbleLeftRightIcon, 
  CogIcon, 
  SparklesIcon, 
  CreditCardIcon,
  TrashIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

const NotificationItem = ({ notification, onMarkAsRead, onDelete, onClose }) => {
  const [isHovered, setIsHovered] = useState(false);
  
  // Get the appropriate icon based on notification type
  const getIcon = () => {
    switch (notification.icon) {
      case 'phone':
        return <PhoneIcon className="h-5 w-5" />;
      case 'voicemail':
        return <ChatBubbleLeftRightIcon className="h-5 w-5" />;
      case 'system':
        return <CogIcon className="h-5 w-5" />;
      case 'ai':
        return <SparklesIcon className="h-5 w-5" />;
      case 'billing':
        return <CreditCardIcon className="h-5 w-5" />;
      default:
        return <ChatBubbleLeftRightIcon className="h-5 w-5" />;
    }
  };
  
  // Get background color based on notification type
  const getIconBgColor = () => {
    switch (notification.type) {
      case 'missed_call':
        return 'bg-red-500/20 text-red-400';
      case 'voicemail':
        return 'bg-blue-500/20 text-blue-400';
      case 'system':
        return 'bg-gray-500/20 text-gray-400';
      case 'ai_insight':
        return 'bg-purple-500/20 text-purple-400';
      case 'billing':
        return 'bg-green-500/20 text-green-400';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };
  
  // Handle click on notification
  const handleClick = () => {
    if (!notification.isRead) {
      onMarkAsRead(notification.id);
    }
    onClose();
  };
  
  return (
    <div 
      className={`relative px-4 py-3 border-b border-gray-800 hover:bg-gray-800/50 transition-colors ${!notification.isRead ? 'bg-gray-800/30' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Link href={notification.actionUrl} onClick={handleClick} className="block">
        <div className="flex items-start">
          {/* Icon */}
          <div className={`p-2 rounded-full mr-3 flex-shrink-0 ${getIconBgColor()}`}>
            {getIcon()}
          </div>
          
          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <p className={`text-sm font-medium ${!notification.isRead ? 'text-white' : 'text-gray-300'}`}>
                {notification.title}
              </p>
              <span className="text-xs text-gray-500 ml-2 whitespace-nowrap">
                {notification.time}
              </span>
            </div>
            <p className="text-sm text-gray-400 mt-1 line-clamp-2">
              {notification.message}
            </p>
          </div>
        </div>
      </Link>
      
      {/* Action buttons that appear on hover */}
      {isHovered && (
        <div className="absolute top-2 right-2 flex space-x-1">
          {!notification.isRead && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onMarkAsRead(notification.id);
              }}
              className="p-1 text-gray-400 hover:text-white rounded-full hover:bg-gray-700 transition-colors"
              title="Mark as read"
            >
              <CheckIcon className="h-4 w-4" />
            </button>
          )}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onDelete(notification.id);
            }}
            className="p-1 text-gray-400 hover:text-red-400 rounded-full hover:bg-gray-700 transition-colors"
            title="Delete notification"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      )}
      
      {/* Unread indicator */}
      {!notification.isRead && (
        <div className="absolute left-0 top-0 bottom-0 w-1 bg-purple-500"></div>
      )}
    </div>
  );
};

export default NotificationItem;
