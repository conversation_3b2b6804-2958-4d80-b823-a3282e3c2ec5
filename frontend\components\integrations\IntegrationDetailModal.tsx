'use client';

import { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { CheckCircleIcon, XCircleIcon, ClockIcon } from '@heroicons/react/24/solid';
import LoadingSpinner from '../shared/LoadingSpinner';
import { Integration, IntegrationSettings, useIntegrations } from '../../hooks/useIntegrations';
import OAuthConnectButton from './OAuthConnectButton';
import WebhookConfig from './WebhookConfig';
import SyncSettingsForm from './SyncSettingsForm';

interface IntegrationDetailModalProps {
  integration: Integration | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function IntegrationDetailModal({
  integration,
  isOpen,
  onClose,
}: IntegrationDetailModalProps) {
  const [currentTab, setCurrentTab] = useState<'connect' | 'settings' | 'status'>('connect');
  const [apiKey, setApiKey] = useState('');
  const [webhookConfig, setWebhookConfig] = useState<any>({});
  const [settings, setSettings] = useState<IntegrationSettings>({});

  const {
    getIntegrationStatus,
    getIntegrationSettings,
    connectIntegration,
    updateSettings,
    disconnectIntegration,
    syncIntegration,
    isConnecting,
    isUpdatingSettings,
    isDisconnecting,
    isSyncing,
  } = useIntegrations();

  // Reset state when integration changes
  useEffect(() => {
    if (integration) {
      setApiKey('');
      setWebhookConfig({});
      setSettings({});
      
      // Set initial tab based on connection status
      if (integration.status === 'connected') {
        setCurrentTab('settings');
      } else {
        setCurrentTab('connect');
      }
    }
  }, [integration]);

  // Fetch integration status
  const { 
    data: statusData,
    isLoading: isLoadingStatus,
  } = getIntegrationStatus(integration?.id || '');

  // Fetch integration settings if connected
  const {
    data: settingsData,
    isLoading: isLoadingSettings,
  } = getIntegrationSettings(integration?.id || '');

  // Update local settings when data is loaded
  useEffect(() => {
    if (settingsData) {
      setSettings(settingsData);
    }
  }, [settingsData]);

  // Handle connect button click
  const handleConnect = () => {
    if (!integration) return;
    
    if (integration.authType === 'oauth') {
      // OAuth is handled by OAuthConnectButton component
      return;
    }
    
    connectIntegration({
      integrationId: integration.id,
      apiKey: integration.authType === 'apikey' ? apiKey : undefined,
      webhookConfig: integration.authType === 'webhook' ? webhookConfig : undefined,
      settings: settings,
    });
  };

  // Handle save settings button click
  const handleSaveSettings = () => {
    if (!integration) return;
    
    updateSettings({
      integrationId: integration.id,
      settings: settings,
    });
  };

  // Handle disconnect button click
  const handleDisconnect = () => {
    if (!integration) return;
    
    if (confirm(`Are you sure you want to disconnect ${integration.name}?`)) {
      disconnectIntegration(integration.id);
      onClose();
    }
  };

  // Handle sync button click
  const handleSync = () => {
    if (!integration) return;
    
    syncIntegration(integration.id);
  };

  // Status indicator component
  const StatusIndicator = () => {
    const status = statusData?.status || integration?.status || 'disconnected';
    
    switch (status) {
      case 'connected':
        return (
          <div className="flex items-center text-green-600 dark:text-green-400">
            <CheckCircleIcon className="h-5 w-5 mr-1" />
            <span>Connected</span>
          </div>
        );
      case 'disconnected':
        return (
          <div className="flex items-center text-gray-500 dark:text-gray-400">
            <XCircleIcon className="h-5 w-5 mr-1" />
            <span>Disconnected</span>
          </div>
        );
      case 'pending':
        return (
          <div className="flex items-center text-yellow-600 dark:text-yellow-400">
            <ClockIcon className="h-5 w-5 mr-1" />
            <span>Pending</span>
          </div>
        );
      case 'error':
        return (
          <div className="flex items-center text-red-600 dark:text-red-400">
            <XCircleIcon className="h-5 w-5 mr-1" />
            <span>Error</span>
            {statusData?.details && (
              <span className="ml-2 text-sm text-red-500">{statusData.details}</span>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  if (!integration) return null;

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-10" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white dark:bg-gray-800 p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex justify-between items-center mb-4">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-900 dark:text-white"
                  >
                    {integration.name} Integration
                  </Dialog.Title>
                  <button
                    type="button"
                    className="text-gray-400 hover:text-gray-500 focus:outline-none"
                    onClick={onClose}
                  >
                    <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                  </button>
                </div>

                <div className="mt-2">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {integration.description}
                  </p>
                </div>

                <div className="mt-4">
                  <StatusIndicator />
                </div>

                {/* Tabs */}
                <div className="mt-4 border-b border-gray-200 dark:border-gray-700">
                  <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                    <button
                      onClick={() => setCurrentTab('connect')}
                      className={`${
                        currentTab === 'connect'
                          ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                      } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                    >
                      Connect
                    </button>
                    <button
                      onClick={() => setCurrentTab('settings')}
                      className={`${
                        currentTab === 'settings'
                          ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                      } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                      disabled={integration.status !== 'connected'}
                    >
                      Settings
                    </button>
                    <button
                      onClick={() => setCurrentTab('status')}
                      className={`${
                        currentTab === 'status'
                          ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                      } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
                    >
                      Status
                    </button>
                  </nav>
                </div>

                {/* Tab content */}
                <div className="mt-4">
                  {/* Connect tab */}
                  {currentTab === 'connect' && (
                    <div>
                      {integration.authType === 'oauth' && (
                        <OAuthConnectButton
                          integration={integration}
                          onConnected={() => {
                            // Refresh data after connection
                            setTimeout(() => {
                              setCurrentTab('settings');
                            }, 1000);
                          }}
                        />
                      )}

                      {integration.authType === 'apikey' && (
                        <div>
                          <label
                            htmlFor="apiKey"
                            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                          >
                            API Key
                          </label>
                          <div className="mt-1">
                            <input
                              type="text"
                              name="apiKey"
                              id="apiKey"
                              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
                              placeholder="Enter API key"
                              value={apiKey}
                              onChange={(e) => setApiKey(e.target.value)}
                            />
                          </div>
                          <div className="mt-4">
                            <button
                              type="button"
                              className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                              onClick={handleConnect}
                              disabled={isConnecting || !apiKey}
                            >
                              {isConnecting ? (
                                <>
                                  <LoadingSpinner size="sm" />
                                  <span className="ml-2">Connecting...</span>
                                </>
                              ) : (
                                'Connect'
                              )}
                            </button>
                          </div>
                        </div>
                      )}

                      {integration.authType === 'webhook' && (
                        <WebhookConfig
                          webhookUrl={integration.webhookUrl}
                          config={webhookConfig}
                          onChange={setWebhookConfig}
                          onSave={handleConnect}
                          isLoading={isConnecting}
                        />
                      )}
                    </div>
                  )}

                  {/* Settings tab */}
                  {currentTab === 'settings' && (
                    <div>
                      {isLoadingSettings ? (
                        <div className="flex justify-center py-4">
                          <LoadingSpinner />
                        </div>
                      ) : (
                        <>
                          <SyncSettingsForm
                            integration={integration}
                            settings={settings}
                            onChange={setSettings}
                          />
                          <div className="mt-4 flex justify-between">
                            <button
                              type="button"
                              className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                              onClick={handleSaveSettings}
                              disabled={isUpdatingSettings}
                            >
                              {isUpdatingSettings ? (
                                <>
                                  <LoadingSpinner size="sm" />
                                  <span className="ml-2">Saving...</span>
                                </>
                              ) : (
                                'Save Settings'
                              )}
                            </button>
                            <button
                              type="button"
                              className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                              onClick={handleSync}
                              disabled={isSyncing}
                            >
                              {isSyncing ? (
                                <>
                                  <LoadingSpinner size="sm" />
                                  <span className="ml-2">Syncing...</span>
                                </>
                              ) : (
                                'Sync Now'
                              )}
                            </button>
                          </div>
                        </>
                      )}
                    </div>
                  )}

                  {/* Status tab */}
                  {currentTab === 'status' && (
                    <div>
                      {isLoadingStatus ? (
                        <div className="flex justify-center py-4">
                          <LoadingSpinner />
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              Connection Status
                            </h4>
                            <StatusIndicator />
                          </div>
                          
                          {integration.status === 'connected' && (
                            <>
                              {integration.lastSynced && (
                                <div>
                                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Last Synced
                                  </h4>
                                  <p className="text-sm text-gray-500 dark:text-gray-400">
                                    {new Date(integration.lastSynced).toLocaleString()}
                                  </p>
                                </div>
                              )}
                              
                              {integration.accountInfo && (
                                <div>
                                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Connected Account
                                  </h4>
                                  {integration.accountInfo.name && (
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                      Name: {integration.accountInfo.name}
                                    </p>
                                  )}
                                  {integration.accountInfo.email && (
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                      Email: {integration.accountInfo.email}
                                    </p>
                                  )}
                                  {integration.accountInfo.plan && (
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                      Plan: {integration.accountInfo.plan}
                                    </p>
                                  )}
                                </div>
                              )}
                            </>
                          )}
                          
                          <div className="pt-4">
                            <button
                              type="button"
                              className="inline-flex justify-center rounded-md border border-red-300 bg-white px-4 py-2 text-sm font-medium text-red-700 shadow-sm hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                              onClick={handleDisconnect}
                              disabled={isDisconnecting || integration.status !== 'connected'}
                            >
                              {isDisconnecting ? (
                                <>
                                  <LoadingSpinner size="sm" />
                                  <span className="ml-2">Disconnecting...</span>
                                </>
                              ) : (
                                'Disconnect'
                              )}
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
