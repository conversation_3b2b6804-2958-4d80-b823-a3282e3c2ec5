---
description: Defines the strategy for user session management, access tokens, and refresh tokens.
---
# Session Management Strategy (`session_management_strategy.mdc`)

## 1. Purpose and Scope

**Purpose:** To define a secure and robust strategy for managing user authentication sessions, including the lifecycle of access tokens and refresh tokens. This ensures consistent handling across frontend and backend components.

**Scope:**
- Token types and formats (Access Tokens, Refresh Tokens).
- Token lifetimes and expiration policies.
- Storage mechanisms (secure HTTP-only cookies, browser storage).
- Refresh token rotation and reuse detection.
- Session invalidation mechanisms (logout, password change, security events).
- Integration with frontend state management (`authStore.ts`).
- Backend middleware validation logic.

## 2. Token Strategy

### 2.1. Access Tokens (JWT)

- **Format:** JSON Web Tokens (JWT), signed using a secure algorithm (e.g., HS256 or RS256) with a secret/key known only to the backend authentication service.
- **Payload:** Contains essential, non-sensitive user identifiers (e.g., `userId`, `organizationId`, `role`) and expiration (`exp`). Avoid including sensitive PII.
- **Lifetime:** Short-lived (e.g., 15-30 minutes) to minimize the impact of token compromise.
- **Transmission:** Sent via the `Authorization: Bearer <token>` header for API calls initiated by the frontend after initial authentication.
- **Storage (Frontend):** Can be stored in memory (e.g., Zustand `authStore`) but should not be persisted in `localStorage` or `sessionStorage` due to XSS risks.

### 2.2. Refresh Tokens

- **Format:** Opaque, cryptographically secure random strings stored in the database, linked to the user and potentially the specific session/device. NOT JWTs.
- **Lifetime:** Longer-lived (e.g., 7-30 days) to allow users to stay logged in across sessions. Lifetime should be configurable via environment variables.
- **Transmission:** Sent only to the dedicated token refresh endpoint (`/api/auth/refresh-token`).
- **Storage (Frontend):** Stored securely in an HTTP-only cookie with `Secure`, `SameSite=Strict` (or `Lax` if cross-domain scenarios are needed), and `Path=/api/auth` attributes to prevent XSS access and CSRF issues.
- **Rotation:** Implement refresh token rotation. Upon successful use, the backend invalidates the used refresh token and issues a new one alongside the new access token. This helps detect token theft if an old token is reused.
- **Reuse Detection:** If a previously used (invalidated) refresh token is presented, invalidate the entire family of refresh tokens for that user session/device, forcing a full re-login. Log this as a potential security event.

## 3. Session Lifecycle

### 3.1. Login

- User authenticates (e.g., password, OAuth).
- Backend generates a new Access Token (JWT) and a new Refresh Token (opaque string).
- Backend stores the hashed Refresh Token in the database, linked to the user ID and potentially user-agent/IP for security.
- Backend sends the Access Token in the response body.
- Backend sends the Refresh Token via a secure HTTP-only cookie.
- Frontend stores the Access Token in memory (`authStore`) and user details.

### 3.2. Authenticated Requests

- Frontend includes the Access Token in the `Authorization: Bearer <token>` header.
- Backend middleware verifies the Access Token's signature and expiration.
- If valid, extracts user info from the token and proceeds.
- If expired, returns HTTP 401.

### 3.3. Token Refresh

- Frontend receives HTTP 401 due to expired Access Token.
- Frontend automatically calls the `/api/auth/refresh-token` endpoint *without* the expired Access Token but *with* the Refresh Token cookie (sent automatically by the browser).
- Backend receives the Refresh Token from the cookie.
- Backend validates the Refresh Token against the database (checking if valid, not expired, and matches stored hash).
- If valid:
    - Invalidates the used Refresh Token in the database.
    - Generates a new Access Token and a new Refresh Token.
    - Stores the new hashed Refresh Token in the database.
    - Sends the new Access Token in the response body.
    - Sends the new Refresh Token via a new HTTP-only cookie.
    - Frontend updates the Access Token in memory (`authStore`).
    - Frontend retries the original failed request with the new Access Token.
- If invalid (not found, expired, already used):
    - Returns HTTP 401.
    - Frontend clears authentication state (`authStore`) and redirects to login.

### 3.4. Logout

- Frontend calls `/api/auth/logout`.
- Backend invalidates the Refresh Token associated with the session in the database.
- Backend clears the Refresh Token cookie.
- Frontend clears authentication state (`authStore`).

### 3.5. Security Events

- On password change or detected security anomalies (e.g., refresh token reuse), the backend should invalidate *all* active refresh tokens for the user, forcing re-login on all devices.

## 4. Implementation Considerations

- **CSRF Protection:** Ensure standard CSRF protection mechanisms are in place for any state-changing requests authenticated via cookies (though primary API calls use Bearer tokens).
- **Clock Skew:** Allow for minor clock skew (e.g., +/- 60 seconds) when validating token expiration.
- **Token Revocation List:** While short access token lifetimes reduce the need, consider a revocation list (e.g., using Redis) for immediate revocation if necessary, although refresh token invalidation is the primary mechanism.
- **Configuration:** Token lifetimes and secrets must be configurable via environment variables (`ACCESS_TOKEN_LIFETIME`, `REFRESH_TOKEN_LIFETIME`, `JWT_SECRET`, etc.). Refer to `env_configuration_rules.mdc`.

## 5. Related Documents

- `docs/functional_specs/api_gateway_routes.mdc`
- `docs/functional_specs/user_roles_and_permissions.mdc`
- `docs/functional_specs/env_configuration_rules.mdc`
- `front/mainpage/stores/authStore.ts`
