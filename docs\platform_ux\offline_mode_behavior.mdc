---
description: 
globs: 
alwaysApply: false
---
---
description: Defines frontend behavior during network interruptions and offline scenarios.
---
# Offline Mode Behavior Strategy (`offline_mode_behavior.mdc`)

## 1. Purpose and Scope

**Purpose:** To define how the CallSaver frontend application (web, potentially mobile) should behave when network connectivity is lost or unreliable. This aims to provide a graceful degradation of service and a clear user experience during offline periods.

**Scope:**
- Detection of online/offline status.
- Caching strategies for essential data (user profile, numbers, basic settings).
- Queueing of user actions performed while offline (e.g., updating settings, adding notes).
- Synchronization mechanisms upon regaining connectivity.
- UI indicators and user feedback for offline status and queued actions.
- Handling of features that inherently require connectivity (e.g., making calls, real-time updates).
- Limitations and edge cases.

## 2. Offline Detection

- Utilize browser APIs (`navigator.onLine`) and potentially periodic "heartbeat" checks to the backend API (`apiClient.ts`) to reliably detect network status changes.
- Maintain an application-wide state variable indicating connectivity status (e.g., in `uiStore.ts`).

## 3. Data Caching

- **Strategy:** Employ local storage mechanisms (e.g., IndexedDB, Zustand persistence middleware with appropriate storage adapters) to cache essential read-only or slowly changing data.
- **Cacheable Data Examples:**
    - User profile information (name, role).
    - List of owned phone numbers and their basic configuration.
    - Locally configured automation rules (if feasible).
    - UI settings/preferences.
    - Help article index/content (optional).
- **Staleness:** Cached data should be considered potentially stale. Implement mechanisms to refresh data upon regaining connectivity or based on TTLs. Clearly indicate potentially stale data in the UI if necessary.
- **Limitations:** Avoid caching highly sensitive or rapidly changing data locally (e.g., real-time call status, credit balance).

## 4. Action Queueing

- **Mechanism:** When offline, certain user actions that modify data should be queued locally instead of being sent immediately.
- **Queueable Actions Examples:**
    - Updating number settings (e.g., friendly name, forwarding).
    - Modifying automation rules.
    - Adding call notes or tags.
    - Changing notification preferences.
- **Queue Structure:** Store queued actions with necessary parameters (action type, payload, timestamp) in local storage (e.g., IndexedDB).
- **Non-Queueable Actions:** Actions requiring immediate server interaction (e.g., purchasing numbers, initiating calls, fetching real-time data) should be disabled or clearly indicated as unavailable offline.

## 5. Synchronization

- **Trigger:** Upon detecting a transition back to online status.
- **Process:**
    1. Attempt to synchronize queued actions with the backend API in the order they were performed.
    2. Handle potential conflicts (e.g., if data was modified on the server while offline). Define conflict resolution strategies (e.g., last-write-wins, user notification for manual resolution).
    3. Upon successful synchronization, remove the action from the local queue.
    4. Handle API errors during sync (retry logic, potentially marking actions as failed in the UI).
    5. Refresh relevant cached data from the server after successful synchronization.

## 6. UI Feedback

- **Offline Indicator:** Display a clear, non-intrusive indicator when the application detects it is offline (e.g., banner, status icon).
- **Disabled Features:** Visually disable or provide informative tooltips for UI elements corresponding to actions that cannot be performed offline.
- **Queued Actions:** Provide feedback that an action has been queued for later synchronization (e.g., subtle UI change, status message).
- **Sync Status:** Indicate when synchronization is in progress, successful, or has encountered errors.
- **Stale Data:** If displaying potentially stale cached data, consider a subtle visual cue.

## 7. Implementation Considerations

- **Storage Limits:** Be mindful of browser local storage limits.
- **Security:** Do not store sensitive credentials or unencrypted sensitive data in local storage.
- **Complexity:** Implementing robust offline support can be complex. Prioritize essential features for offline caching and action queueing.
- **Testing:** Thoroughly test transitions between online/offline states and the synchronization process, including conflict scenarios.

## 8. Related Documents

- `docs/functional_specs/component_state_mapping.mdc`
- `front/mainpage/stores/uiStore.ts` (for connectivity state)
- `front/mainpage/lib/apiClient.ts` (for detection)
- Relevant Zustand stores using persistence middleware.
- `docs/platform_ux/performance_budget.mdc` (offline impacts performance/UX)
