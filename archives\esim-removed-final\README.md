# eSIM Implementation Guide

This directory contains the core components of the eSIM implementation for transitioning CallSaver from Twilio phone numbers to an eSIM-based solution. This guide explains the architecture, integration steps, and development workflow.

## Architecture Overview

The eSIM implementation follows a layered approach:

```
┌─────────────────────────────────────────────────┐
│              Telephony Service Layer            │
│      (Abstraction for Twilio and eSIM)          │
└───────────────┬─────────────────────────────────┘
                │
                ▼
┌─────────────────────────────────────────────────┐
│              eSIM Service Layer                 │
│     (Core eSIM functionality and WebRTC)        │
└───────────────┬─────────────────────────────────┘
                │
                ▼
┌─────────────────────────────────────────────────┐
│             Provider Adapters                   │
│ (Implementation for specific eSIM providers)    │
└───────────────┬─────────────────────────────────┘
                │
                ▼
┌─────────────────────────────────────────────────┐
│             External APIs                       │
│     (Airalo, Truphone, GigSky, etc.)            │
└─────────────────────────────────────────────────┘
```

The key components are:

1. **Telephony Service Layer**: Provides a unified interface for both Twilio and eSIM, allowing for gradual migration.
2. **eSIM Service Layer**: Implements the core eSIM functionality and WebRTC voice integration.
3. **Provider Adapters**: Adapts specific eSIM provider APIs to a common interface.
4. **WebRTC Service**: Handles real-time voice communication over eSIM data.
5. **Database Models**: Stores eSIM profile and session data.

## Integration Steps

### 1. Update Database Schema

Add the eSIM profile model to the Prisma schema:

```bash
# Copy the schema update to the main schema file
cat prisma/schema-updates.prisma >> prisma/schema.prisma

# Generate Prisma client
npx prisma generate

# Create migration
npx prisma migrate dev --name add_esim_profiles
```

### 2. Configure Environment Variables

Add the following environment variables to your `.env` file:

```
# eSIM Configuration
ESIM_ENABLED=false
ESIM_ENABLED_USERS=
ESIM_ENABLED_NUMBERS=

# Provider-specific configurations (examples)
AIRALO_API_KEY=
AIRALO_API_SECRET=
AIRALO_API_URL=
AIRALO_SANDBOX=true

# WebRTC Configuration
WEBRTC_ENABLED=false
WEBRTC_STUN_SERVERS=stun:stun.l.google.com:19302,stun:stun1.l.google.com:19302
WEBRTC_TURN_SERVER_URL=turn:turn.callsaver.app:3478
WEBRTC_TURN_SERVER_USERNAME=webrtc
WEBRTC_TURN_SERVER_CREDENTIAL=your-secret-here
WEBRTC_ICE_TRANSPORT_POLICY=all
WEBRTC_SIGNALING_PORT=3001
WEBRTC_SIGNALING_SECURE=true
WEBRTC_MAX_CALL_DURATION=3600
WEBRTC_RECORDING_ENABLED=false
WEBRTC_RECORDING_STORAGE=./recordings

# Telephony Configuration
TELEPHONY_DEFAULT_PROVIDER=twilio
TELEPHONY_PARALLEL_OPERATION=false
```

### 3. Update Config Module

Integrate the configuration updates into the main config module:

```javascript
// In config/index.js
const baseConfig = require('./base');
const envConfig = require('./env');
const esimConfig = require('./updates');

// Merge configurations
module.exports = {
  ...baseConfig,
  ...envConfig,
  ...esimConfig
};
```

### 4. Integrate with Server

Add the eSIM and WebRTC services to the Express server:

```javascript
// In server.js

// Import services
const telephonyService = require('./services/telephonyService');
const esimService = require('./esim/esimService');
const webrtcService = require('./esim/webrtcService')();

// Initialize WebRTC signaling server if enabled
if (config.webrtc.enabled) {
  const io = webrtcService.initializeSignalingServer(app, server);
  // Store in app for route handlers
  app.set('io', io);
}

// Add routes for eSIM management
app.use('/api/esim', require('./routes/esimRoutes'));
```

### 5. Create API Routes

Implement the routes for eSIM management:

```javascript
// Create routes/esimRoutes.js
const express = require('express');
const router = express.Router();
const esimController = require('../controllers/esimController');
const authMiddleware = require('../middleware/authMiddleware');

// Protected routes
router.use(authMiddleware);

// eSIM profile management routes
router.get('/profiles', esimController.getProfiles);
router.post('/profiles', esimController.createProfile);
router.get('/profiles/:id', esimController.getProfile);
router.delete('/profiles/:id', esimController.deactivateProfile);

// Package management routes
router.get('/packages', esimController.getPackages);
router.post('/profiles/:id/packages', esimController.purchasePackage);

// QR code generation
router.get('/profiles/:id/qr', esimController.generateQR);

// Usage tracking
router.get('/profiles/:id/usage', esimController.getUsage);

module.exports = router;
```

### 6. Implement Controllers

Create the controller for handling eSIM-related requests:

```javascript
// Create controllers/esimController.js with the required methods
// (see implementation in the project files)
```

## Development Workflow

### Using the Mock Provider

During development, the mock eSIM provider can be used to simulate eSIM functionality without actual API integration:

1. Set `ESIM_ENABLED=true` in your `.env` file
2. Set `defaultProvider: 'mock'` in the eSIM configuration
3. Run the server and use the API endpoints to test eSIM functionality

### Adding a Real Provider

To implement a real eSIM provider:

1. Create a new provider adapter in `esim/providers/`
2. Register the provider in `esim/providers/index.js`
3. Add the required configuration to `config/updates.js`
4. Update environment variables for the provider

### Testing WebRTC Functionality

To test the WebRTC voice functionality:

1. Set up a development TURN server or use a public one
2. Configure the WebRTC environment variables
3. Implement a simple WebRTC client (HTML/JS) for testing
4. Use the API to create a call session and connect to it

## Deployment Considerations

### TURN/STUN Servers

For production use, dedicated TURN/STUN servers are required for reliable WebRTC communication:

- Consider using a managed service like Twilio's Network Traversal Service
- Alternatively, set up dedicated servers using coturn
- Ensure proper security configuration for the TURN server

### Provider Selection

When selecting an eSIM provider for production:

1. Evaluate global coverage requirements
2. Compare pricing models and data packages
3. Test activation process and QR code delivery
4. Verify API reliability and support quality
5. Consider redundancy with multiple providers

### Gradual Migration

The implementation supports gradual migration from Twilio to eSIM:

1. Enable parallel operation mode
2. Assign specific users or numbers to use eSIM
3. Monitor performance and user feedback
4. Gradually increase the eSIM user base
5. Eventually, move all users to eSIM and retire Twilio

## Monitoring and Troubleshooting

### Logging

The eSIM components use the standard logging system. To enable debug logging:

```
LOG_LEVEL=debug
```

### Common Issues

- **QR Code Activation**: Issues with QR code scanning can be related to formatting or expiration
- **WebRTC Connectivity**: NAT traversal problems often require proper TURN server configuration
- **Provider API**: Rate limiting or authentication issues with provider APIs

## Next Steps

- **Provider Implementations**: Complete the implementation of real eSIM provider adapters
- **WebRTC Client**: Develop the client-side WebRTC implementation for web and mobile
- **Admin UI**: Create an admin interface for eSIM management
- **User Self-Service**: Implement user-facing controls for eSIM activation and management
