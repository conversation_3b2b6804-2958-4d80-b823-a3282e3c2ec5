'use client';

import { useState, useEffect } from 'react';
import { useOrganization, useTeamMembers, useUpdateOrganization } from '../../../hooks/useSettings';
import OrganizationProfileForm from './OrganizationProfileForm';
import TeamMembersManager from './TeamMembersManager';
import LoadingSpinner from '../shared/LoadingSpinner';
import ErrorMessage from '../shared/ErrorMessage';

export default function OrganizationSettingsPanel() {
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  
  // Fetch organization data
  const { 
    data: organization, 
    isLoading: isLoadingOrg, 
    isError: isErrorOrg, 
    error: orgError,
    refetch: refetchOrg
  } = useOrganization();
  
  // Fetch team members
  const { 
    data: teamMembers, 
    isLoading: isLoadingTeam, 
    isError: isErrorTeam, 
    error: teamError,
    refetch: refetchTeam
  } = useTeamMembers();
  
  // Update organization mutation
  const updateOrganization = useUpdateOrganization();
  
  // Enable queries when component mounts
  useEffect(() => {
    refetchOrg();
    refetchTeam();
  }, [refetchOrg, refetchTeam]);
  
  // Handle success message
  const handleSuccess = (message: string) => {
    setSuccessMessage(message);
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };
  
  // Handle organization update
  const handleOrganizationUpdate = async (data: any) => {
    try {
      await updateOrganization.mutateAsync(data);
      handleSuccess('Organization profile updated successfully');
    } catch (err) {
      // Error is handled by the mutation
      console.error('Error updating organization:', err);
    }
  };
  
  // Check if any data is loading
  const isLoading = isLoadingOrg || isLoadingTeam;
  
  // Check if there are any errors
  const isError = isErrorOrg || isErrorTeam;
  
  // Get the first error
  const error = orgError || teamError;
  
  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }
  
  if (isError) {
    return (
      <ErrorMessage 
        title="Failed to load organization settings" 
        message="We couldn't load your organization information. Please try again later."
        error={error instanceof Error ? error : undefined}
        onRetry={() => {
          refetchOrg();
          refetchTeam();
        }}
      />
    );
  }
  
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Organization Settings</h2>
      
      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-800 dark:text-green-400">
          {successMessage}
        </div>
      )}
      
      {updateOrganization.isError && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg text-red-800 dark:text-red-400">
          {updateOrganization.error instanceof Error 
            ? updateOrganization.error.message 
            : 'An error occurred while updating your organization profile'}
        </div>
      )}
      
      <div className="space-y-8">
        {/* Organization Profile */}
        <div className="pb-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Organization Profile</h3>
          
          <OrganizationProfileForm 
            organization={organization} 
            isLoading={updateOrganization.isPending}
            onSubmit={handleOrganizationUpdate}
          />
        </div>
        
        {/* Team Members */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Team Members</h3>
          
          <TeamMembersManager 
            teamMembers={teamMembers || []} 
            onSuccess={handleSuccess}
          />
        </div>
      </div>
    </div>
  );
}
