"use client";

import { useState } from 'react';
import AutomationChat from './AutomationChat';
import { FiActivity, FiCode, FiMessageSquare, FiTerminal } from 'react-icons/fi';

export default function AIChatAssistant({ phoneNumber }) {
  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700 shadow-lg overflow-hidden">
      <div className="border-b border-gray-700 p-4">
        <div className="flex items-center">
          <h2 className="text-lg font-semibold flex items-center">
            <FiActivity className="mr-2 text-blue-400" />
            CallSaver AI Assistant
          </h2>
        </div>
        <p className="text-gray-400 text-sm mt-2">
          Powered by GPT-4-turbo, your AI assistant can handle both slash commands and natural language conversations.
        </p>
        <div className="mt-3 flex flex-wrap gap-2">
          <span className="inline-flex items-center text-xs bg-blue-900/50 text-blue-300 rounded-full px-2 py-1">
            <FiTerminal className="mr-1" /> Slash Commands
          </span>
          <span className="inline-flex items-center text-xs bg-purple-900/50 text-purple-300 rounded-full px-2 py-1">
            <FiMessageSquare className="mr-1" /> Natural Language
          </span>
          <span className="inline-flex items-center text-xs bg-green-900/50 text-green-300 rounded-full px-2 py-1">
            <FiCode className="mr-1" /> Markdown Support
          </span>
        </div>
      </div>
      
      {phoneNumber ? (
        <div className="p-4">
          <AutomationChat phoneNumber={phoneNumber} />
        </div>
      ) : (
        <div className="p-8 text-center text-gray-400">
          <p>Please select a phone number to start chatting with your AI assistant.</p>
        </div>
      )}
    </div>
  );
}
