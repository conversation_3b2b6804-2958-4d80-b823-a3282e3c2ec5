'use client';

import { useState } from 'react';
import { Tab } from '@headlessui/react';
import { useNumberManagement } from '../../hooks/useNumberManagement';
import { useEsimManagement } from '../../hooks/useEsimManagement';
import OwnedNumbersList from './OwnedNumbersList';
import NumberSearchPanel from './NumberSearchPanel';
import ESimManagementPanel from './ESimManagementPanel';
import NumberManagementSkeleton from './NumberManagementSkeleton';
import ErrorMessage from '../shared/ErrorMessage';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

export default function NumberManagementLayout() {
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);
  const [configModalOpen, setConfigModalOpen] = useState(false);
  const [selectedNumberId, setSelectedNumberId] = useState<string | null>(null);
  const [purchaseModalOpen, setPurchaseModalOpen] = useState(false);
  const [selectedAvailableNumber, setSelectedAvailableNumber] = useState<any | null>(null);
  const [releaseModalOpen, setReleaseModalOpen] = useState(false);

  const {
    ownedNumbers,
    purchaseNumber,
    isPurchasing,
    updateNumber,
    isUpdating,
    releaseNumber,
    isReleasing
  } = useNumberManagement();

  const { ownedEsims } = useEsimManagement();

  // Handle loading state
  if (ownedNumbers.isLoading) {
    return <NumberManagementSkeleton />;
  }

  // Handle error state
  if (ownedNumbers.error) {
    return (
      <div className="p-6">
        <ErrorMessage
          title="Could not load phone numbers"
          message="We couldn't load your phone numbers. Please try again later."
          error={ownedNumbers.error as Error}
          onRetry={() => ownedNumbers.refetch()}
        />
      </div>
    );
  }

  // Handle configuration modal
  const handleConfigClick = (numberId: string) => {
    setSelectedNumberId(numberId);
    setConfigModalOpen(true);
  };

  // Handle purchase modal
  const handlePurchaseClick = (number: any) => {
    setSelectedAvailableNumber(number);
    setPurchaseModalOpen(true);
  };

  // Handle release modal
  const handleReleaseClick = (numberId: string) => {
    setSelectedNumberId(numberId);
    setReleaseModalOpen(true);
  };

  // Handle purchase confirmation
  const handlePurchaseConfirm = () => {
    if (selectedAvailableNumber) {
      purchaseNumber(selectedAvailableNumber.id);
      setPurchaseModalOpen(false);
    }
  };

  // Handle release confirmation
  const handleReleaseConfirm = () => {
    if (selectedNumberId) {
      releaseNumber(selectedNumberId);
      setReleaseModalOpen(false);
    }
  };

  // Handle config save
  const handleConfigSave = (updateData: any) => {
    if (selectedNumberId) {
      updateNumber({ numberId: selectedNumberId, updateData });
      setConfigModalOpen(false);
    }
  };

  return (
    <div className="container mx-auto px-3 sm:px-4 py-4 sm:py-6 md:py-8">
      <div className="mb-8">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-1 sm:mb-2">Number Management</h1>
        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300">
          Manage your phone numbers and eSIMs. Search, purchase, and configure your communication channels.
        </p>
      </div>

      <Tab.Group selectedIndex={selectedTabIndex} onChange={setSelectedTabIndex}>
        <Tab.List className="flex space-x-1 rounded-xl bg-blue-900/20 p-1 mb-4 sm:mb-6 md:mb-8 overflow-x-auto">
          <Tab
            className={({ selected }) =>
              classNames(
                'w-full rounded-lg py-2 sm:py-2.5 text-xs sm:text-sm font-medium leading-5 whitespace-nowrap',
                'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                selected
                  ? 'bg-white dark:bg-gray-800 text-blue-700 dark:text-blue-400 shadow'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-white/[0.12] hover:text-blue-600 dark:hover:text-blue-300'
              )
            }
          >
            My Numbers
          </Tab>
          <Tab
            className={({ selected }) =>
              classNames(
                'w-full rounded-lg py-2 sm:py-2.5 text-xs sm:text-sm font-medium leading-5 whitespace-nowrap',
                'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                selected
                  ? 'bg-white dark:bg-gray-800 text-blue-700 dark:text-blue-400 shadow'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-white/[0.12] hover:text-blue-600 dark:hover:text-blue-300'
              )
            }
          >
            Get New Number
          </Tab>
          <Tab
            className={({ selected }) =>
              classNames(
                'w-full rounded-lg py-2 sm:py-2.5 text-xs sm:text-sm font-medium leading-5 whitespace-nowrap',
                'ring-white ring-opacity-60 ring-offset-2 ring-offset-blue-400 focus:outline-none focus:ring-2',
                selected
                  ? 'bg-white dark:bg-gray-800 text-blue-700 dark:text-blue-400 shadow'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-white/[0.12] hover:text-blue-600 dark:hover:text-blue-300'
              )
            }
          >
            eSIM Management
          </Tab>
        </Tab.List>
        <Tab.Panels>
          <Tab.Panel>
            <OwnedNumbersList
              numbers={ownedNumbers.data}
              onConfigClick={handleConfigClick}
              onReleaseClick={handleReleaseClick}
            />
          </Tab.Panel>
          <Tab.Panel>
            <NumberSearchPanel onPurchaseClick={handlePurchaseClick} />
          </Tab.Panel>
          <Tab.Panel>
            <ESimManagementPanel />
          </Tab.Panel>
        </Tab.Panels>
      </Tab.Group>

      {/* Modals will be implemented separately */}
      {/* NumberConfigModal */}
      {/* PurchaseConfirmationDialog */}
      {/* ReleaseConfirmationDialog */}
    </div>
  );
}
