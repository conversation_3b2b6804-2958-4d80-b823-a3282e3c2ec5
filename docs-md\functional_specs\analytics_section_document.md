---
description:
globs:
alwaysApply: false
---
# Analytics Section Functional Document (`analytics_section_document.mdc`)

## 1. Purpose and Scope

**Purpose:** Provide users with detailed insights and visualizations regarding their communication patterns, AI assistant performance, usage metrics, and overall account activity. Enable data-driven decision-making for optimizing communication strategies.

**Scope:**
- Visualize call volume, duration, and trends over time.
- Analyze message volume and patterns.
- Report on voicemail metrics (count, average duration, transcription accuracy if measurable).
- Display AI interaction metrics (calls handled, SMS replied, commands executed, sentiment analysis).
- Track usage costs and credit consumption patterns.
- Analyze call outcomes (answered, missed, voicemail, blocked).
- Provide filtering options (by number, date range, time period).
- Offer data export capabilities (e.g., CSV).

## 2. User Interactions

- **View Charts & Graphs:** Interact with various visualizations (line charts, bar charts, pie charts) displaying key metrics.
- **Select Date Range:** Choose predefined ranges (today, week, month, year) or custom date ranges to filter data.
- **Filter by Number:** Select specific phone numbers to view their individual analytics.
- **Hover for Details:** Hover over chart elements to see specific data points.
- **View Data Tables:** See underlying data in tabular format, potentially sortable and searchable.
- **Export Data:** Download analytics reports in formats like CSV.
- **Switch Views:** Navigate between different analytic categories (Calls, Messages, AI Performance, Usage).

## 3. Backend Integrations & Services Used

- **Analytics Service:** The primary service responsible for aggregating, processing, and serving analytics data.
- **Database:** Stores raw event data (calls, messages, AI interactions) and pre-aggregated analytics summaries.
- **AI Service:** Provides sentiment analysis scores and other AI-specific metrics for reporting.
- **Billing Service:** Provides data on credit consumption and costs.
- **Number Management Service:** Provides context about the numbers being analyzed.

## 4. Necessary API Endpoints

- `GET /api/analytics/overview?startDate=<date>&endDate=<date>&phoneNumberId=<id|all>`: Fetches summary metrics for the selected period/number.
- `GET /api/analytics/calls?startDate=<date>&endDate=<date>&phoneNumberId=<id|all>&groupBy=day|week|month`: Fetches time-series data for call volume, duration, outcomes.
- `GET /api/analytics/messages?startDate=<date>&endDate=<date>&phoneNumberId=<id|all>&groupBy=day|week|month`: Fetches time-series data for message volume.
- `GET /api/analytics/voicemails?startDate=<date>&endDate=<date>&phoneNumberId=<id|all>&groupBy=day|week|month`: Fetches time-series data for voicemail metrics.
- `GET /api/analytics/ai?startDate=<date>&endDate=<date>&phoneNumberId=<id|all>&groupBy=day|week|month`: Fetches metrics on AI interactions (calls handled, SMS replies, sentiment distribution).
- `GET /api/analytics/usage?startDate=<date>&endDate=<date>&phoneNumberId=<id|all>&groupBy=day|week|month`: Fetches data on credit consumption and costs.
- `GET /api/analytics/export?format=csv&report=calls|messages|ai|usage&startDate=<date>&endDate=<date>&phoneNumberId=<id|all>`: Initiates a data export.

## 5. Expected Frontend Component Structure

```
/components
  /analytics
    AnalyticsLayout.tsx         # Main layout for the analytics section
    DateRangePicker.tsx         # Reusable component for selecting date ranges
    NumberFilterDropdown.tsx    # Component to filter by phone number
    ChartWrapper.tsx            # Wrapper for chart components (handles loading/error states)
      LineChart.tsx             # Reusable line chart component
      BarChart.tsx              # Reusable bar chart component
      PieChart.tsx              # Reusable pie chart component
    MetricDisplay.tsx           # Displays key single-value metrics
    DataTable.tsx               # Component for displaying tabular data
    ExportButton.tsx            # Button to trigger data export
    AnalyticsSkeleton.tsx       # Loading state placeholder for charts/tables
    AnalyticsCategoryTabs.tsx   # Tabs to switch between Calls, Messages, AI, etc.
```

## 6. Data Displayed

- **Charts:**
    - Call/Message Volume over time.
    - Average Call Duration over time.
    - Call Outcomes distribution (pie chart).
    - AI Sentiment distribution (pie chart).
    - Credit Usage over time.
    - AI Interactions (calls handled, SMS sent) over time.
- **Key Metrics:**
    - Total Calls / Messages / Voicemails in period.
    - Average Call Duration.
    - Missed Call Rate.
    - Voicemail Answer Rate.
    - Average Sentiment Score.
    - Total Credits Consumed / Estimated Cost.
- **Tables:** Detailed logs corresponding to the charts (optional, could link to Call Logs section).

## 7. State and UI Behavior

- **Loading State:** Display skeletons while charts and data are loading.
- **Filtering:** Applying date range or number filters triggers API calls and updates all relevant charts and metrics. Keep filter state consistent across different analytic views.
- **Interactivity:** Charts should be interactive (tooltips on hover).
- **No Data State:** Display clear messages when there is no data for the selected filters (e.g., "No call data available for this period").
- **Responsiveness:** Charts and layout must adapt to different screen sizes.
- **Export:** Provide feedback during export generation and notify upon completion/download readiness.

## 8. AI Integration

- **Sentiment Analysis:** Display trends and distributions of sentiment scores derived from call transcripts or message content.
- **Trend Detection:** (Future) AI could identify significant upward or downward trends in key metrics and highlight them.
- **Anomaly Detection:** (Future) AI could flag unusual patterns in call volume, costs, or error rates.
- **Performance Insights:** Report on AI-specific metrics like successful command execution rates, interaction duration, etc.

## 9. Error Handling Rules

- **API Errors:** If fetching data for a specific chart fails, display an error message within that chart's container. A global error message can be shown if the main overview data fails. Provide retry options.
- **Filtering Errors:** Handle invalid date ranges or filter combinations gracefully.
- **Export Errors:** Notify the user if the data export fails, providing a reason if possible.
- **Authentication Errors:** Redirect to login if the session is invalid.

## 10. Logging and Usage Tracking Expectations

- **Log:**
    - Successful loads of analytics data (specify filters used).
    - API errors during data fetching (include filters and error details).
    - Data export requests (format, report type, filters).
    - Errors during data export generation.
- **Track:**
    - Views of the analytics section and specific sub-categories (Calls, AI, etc.).
    - Use of filtering controls (date range changes, number selections).
    - Interactions with charts (hovers, clicks if applicable).
    - Data export actions initiated by users.
    - Time spent viewing different analytics reports.
