'use client';

import { useState } from 'react';
import PhoneNumbersTab from '../../components/settings/PhoneNumbersTab';

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('account');
  
  // Tabs for settings page
  const tabs = [
    { id: 'account', label: 'Account Settings' },
    { id: 'notifications', label: 'Notifications' },
    { id: 'billing', label: 'Billing & Subscription' },
    { id: 'numbers', label: 'Phone Numbers' },
    { id: 'api', label: 'API Access' },
  ];
  
  return (
    <div>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-white">Settings</h2>
        <p className="text-gray-400 mt-1">
          Manage your account preferences, notifications, and billing information.
        </p>
      </div>
      
      {/* Tabs */}
      <div className="border-b border-gray-800 mb-6">
        <div className="flex space-x-2 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              className={`
                px-4 py-2 font-medium text-sm whitespace-nowrap
                ${activeTab === tab.id
                  ? 'text-white border-b-2 border-purple-500'
                  : 'text-gray-400 hover:text-white hover:border-b-2 hover:border-gray-600'
                }
              `}
              onClick={() => setActiveTab(tab.id)}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>
      
      {/* Content based on active tab */}
      <div className="bg-gray-900/70 backdrop-blur-lg rounded-2xl border border-gray-800 shadow-xl p-6 md:p-8">
        {activeTab === 'account' && (
          <div>
            <h3 className="text-xl font-semibold text-white mb-4">Account Information</h3>
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Email Address
                </label>
                <input
                  type="email"
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder="Your email address"
                  disabled
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Full Name
                </label>
                <input
                  type="text"
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder="Your full name"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  Company (Optional)
                </label>
                <input
                  type="text"
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                  placeholder="Your company name"
                />
              </div>
              
              <div className="pt-4">
                <button className="px-5 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors">
                  Save Changes
                </button>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'notifications' && (
          <div>
            <h3 className="text-xl font-semibold text-white mb-4">Notification Preferences</h3>
            <p className="text-gray-400 mb-6">
              Configure how and when you receive notifications from CallSaver.
            </p>
            <div className="space-y-4">
              <div className="flex justify-between items-center py-2 border-b border-gray-800">
                <div>
                  <h4 className="text-white font-medium">Email Notifications</h4>
                  <p className="text-sm text-gray-400">Receive notifications via email</p>
                </div>
                <div className="flex items-center">
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" />
                    <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                  </label>
                </div>
              </div>
              
              <div className="flex justify-between items-center py-2 border-b border-gray-800">
                <div>
                  <h4 className="text-white font-medium">Missed Call Alerts</h4>
                  <p className="text-sm text-gray-400">Get notified about missed calls</p>
                </div>
                <div className="flex items-center">
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" defaultChecked />
                    <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                  </label>
                </div>
              </div>
              
              <div className="flex justify-between items-center py-2 border-b border-gray-800">
                <div>
                  <h4 className="text-white font-medium">New Voicemail Notifications</h4>
                  <p className="text-sm text-gray-400">Get notified when you receive a new voicemail</p>
                </div>
                <div className="flex items-center">
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" defaultChecked />
                    <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {activeTab === 'numbers' && (
          <PhoneNumbersTab />
        )}
        
        {activeTab === 'billing' && (
          <div className="h-64 flex items-center justify-center">
            <p className="text-gray-400">
              Billing & Subscription content will appear here.
            </p>
          </div>
        )}
        
        {activeTab === 'api' && (
          <div className="h-64 flex items-center justify-center">
            <p className="text-gray-400">
              API Access content will appear here.
            </p>
          </div>
        )}
      </div>
    </div>
  );
} 