import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import AnalyticsPage from '../../app/dashboard/analytics/page';
import axios from 'axios';

// Mock dependencies
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock framer-motion to prevent animation errors in tests
jest.mock('framer-motion', () => {
  const actual = jest.requireActual('framer-motion');
  return {
    ...actual,
    motion: {
      div: ({ children, ...props }) => <div {...props}>{children}</div>,
    },
    AnimatePresence: ({ children }) => <>{children}</>,
  };
});

// Mock recharts components
jest.mock('recharts', () => {
  const OriginalModule = jest.requireActual('recharts');
  return {
    ...OriginalModule,
    ResponsiveContainer: ({ children, width, height }) => (
      <div data-testid="responsive-container" style={{ width, height }}>
        {children}
      </div>
    ),
    BarChart: ({ children }) => <div data-testid="bar-chart">{children}</div>,
    LineChart: ({ children }) => <div data-testid="line-chart">{children}</div>,
    PieChart: ({ children }) => <div data-testid="pie-chart">{children}</div>,
    Bar: () => <div data-testid="bar" />,
    Line: () => <div data-testid="line" />,
    Pie: () => <div data-testid="pie" />,
    XAxis: () => <div data-testid="x-axis" />,
    YAxis: () => <div data-testid="y-axis" />,
    CartesianGrid: () => <div data-testid="cartesian-grid" />,
    Tooltip: () => <div data-testid="tooltip" />,
    Legend: () => <div data-testid="legend" />,
    Cell: () => <div data-testid="cell" />,
  };
});

// Mock react-circular-progressbar
jest.mock('react-circular-progressbar', () => ({
  CircularProgressbar: ({ value }) => <div data-testid="circular-progressbar">{value}</div>,
  buildStyles: () => ({}),
}));

describe('AnalyticsDashboard', () => {
  // Mock data to match interface
  const mockAnalyticsData = {
    totalCalls: 250,
    totalMessages: 500,
    totalDuration: 75000, // seconds
    callsByDirection: {
      inbound: 150,
      outbound: 100,
    },
    messagesByDirection: {
      inbound: 300,
      outbound: 200,
    },
    callTrend: Array.from({ length: 7 }, (_, i) => ({
      date: `Mar ${i + 1}`,
      inbound: 20 + i,
      outbound: 10 + i,
      total: 30 + i * 2,
    })),
    messageTrend: Array.from({ length: 7 }, (_, i) => ({
      date: `Mar ${i + 1}`,
      inbound: 40 + i,
      outbound: 25 + i,
      total: 65 + i * 2,
    })),
    callsByStatus: {
      completed: 180,
      noAnswer: 40,
      busy: 15,
      failed: 10,
      canceled: 5,
    },
    peakCallHours: Array.from({ length: 24 }, (_, hour) => ({
      hour,
      count: hour >= 8 && hour <= 18 ? 20 + hour : 5 + hour,
    })),
    topCallers: Array.from({ length: 5 }, (_, i) => ({
      phoneNumber: `+1${800 + i}${900 + i}${1000 + i}`,
      callCount: 20 - i * 2,
    })),
    missedCalls: 55,
    missedCallRate: 36.7,
    aiHandoffRate: 68.0,
    aiHandledCalls: 102,
    aiHandledMessages: 240,
    smsResponseRate: 92.5,
    smsResponseTime: 45.3,
    organizationId: 'org_123456',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    
    // Mock successful API response
    mockedAxios.get.mockResolvedValue({
      data: mockAnalyticsData,
    });
    
    // Mock fetch for the analytics API
    global.fetch = jest.fn().mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockAnalyticsData),
      })
    ) as jest.Mock;
  });
  
  afterEach(() => {
    jest.useRealTimers();
  });

  test('should render the analytics dashboard with correct title', async () => {
    render(<AnalyticsPage />);
    
    // Check for title and main elements
    expect(screen.getByText('Analytics Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Call Analytics')).toBeInTheDocument();
    expect(screen.getByText('Message Analytics')).toBeInTheDocument();
    expect(screen.getByText('AI Performance')).toBeInTheDocument();
  });

  test('should display loading state initially and then show data', async () => {
    // Simulate slow API response
    global.fetch = jest.fn().mockImplementation(() =>
      new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            ok: true,
            json: () => Promise.resolve(mockAnalyticsData),
          });
        }, 500);
      })
    ) as jest.Mock;

    render(<AnalyticsPage />);
    
    // Should initially show loading state
    expect(screen.getByText('Loading analytics data...')).toBeInTheDocument();
    
    // Fast forward timers
    act(() => {
      jest.advanceTimersByTime(500);
    });
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Total Calls')).toBeInTheDocument();
    });
    
    // Check that data is displayed
    expect(screen.getByText('250')).toBeInTheDocument(); // totalCalls
  });

  test('should allow switching between time ranges', async () => {
    render(<AnalyticsPage />);
    
    // Wait for initial data load
    await waitFor(() => {
      expect(screen.getByText('Total Calls')).toBeInTheDocument();
    });
    
    // Find the time range selector (this might need adjustment based on actual implementation)
    const timeRangeSelector = screen.getByText('Last 7 days');
    expect(timeRangeSelector).toBeInTheDocument();
    
    // Change time range
    fireEvent.click(timeRangeSelector);
    fireEvent.click(screen.getByText('Last 30 days'));
    
    // Check for refetch
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/analytics?timeRange=30d');
    });
  });

  test('should render all three tabs with appropriate content', async () => {
    render(<AnalyticsPage />);
    
    // Wait for initial data load
    await waitFor(() => {
      expect(screen.getByText('Total Calls')).toBeInTheDocument();
    });
    
    // Should default to call analytics tab
    expect(screen.getByText('Total Calls')).toBeInTheDocument();
    expect(screen.getByText('Total Duration')).toBeInTheDocument();
    expect(screen.getByText('Missed Call Rate')).toBeInTheDocument();
    
    // Switch to message analytics tab
    fireEvent.click(screen.getByText('Message Analytics'));
    
    // Check for message metrics
    expect(screen.getByText('Total Messages')).toBeInTheDocument();
    expect(screen.getByText('Inbound Messages')).toBeInTheDocument();
    expect(screen.getByText('Outbound Messages')).toBeInTheDocument();
    
    // Switch to AI performance tab
    fireEvent.click(screen.getByText('AI Performance'));
    
    // Check for AI metrics
    expect(screen.getByText('AI Handled Calls')).toBeInTheDocument();
    expect(screen.getByText('AI Handled Messages')).toBeInTheDocument();
  });

  test('should refresh data when refresh button is clicked', async () => {
    render(<AnalyticsPage />);
    
    // Wait for initial data load
    await waitFor(() => {
      expect(screen.getByText('Total Calls')).toBeInTheDocument();
    });
    
    // Clear fetch mock to track new calls
    (global.fetch as jest.Mock).mockClear();
    
    // Find and click refresh button (adjust selector based on implementation)
    const refreshButton = screen.getByLabelText('refresh') || screen.getByRole('button', { name: /refresh/i });
    fireEvent.click(refreshButton);
    
    // Check loading state
    expect(screen.getByText('...')).toBeInTheDocument();
    
    // Forward timer to complete refresh
    act(() => {
      jest.advanceTimersByTime(500);
    });
    
    // Data should be refreshed
    await waitFor(() => {
      expect(screen.getByText('250')).toBeInTheDocument(); // totalCalls
    });
  });

  test('should format duration correctly', async () => {
    render(<AnalyticsPage />);
    
    // Wait for initial data load
    await waitFor(() => {
      expect(screen.getByText('Total Calls')).toBeInTheDocument();
    });
    
    // Total duration should be formatted as hours, minutes, seconds
    // For 75000 seconds = 20h 50m 0s
    expect(screen.getByText('20h 50m 0s')).toBeInTheDocument();
  });
});
