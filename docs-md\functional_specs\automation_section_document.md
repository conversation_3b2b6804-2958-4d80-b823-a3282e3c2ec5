---
description:
globs:
alwaysApply: false
---
# Automation Section Functional Document (`automation_section_document.mdc`)

## 1. Purpose and Scope

**Purpose:** Allow users to configure, manage, and monitor AI-powered automations for their phone numbers. This includes setting up call handling rules, SMS auto-replies, voicemail transcription, AI assistant behavior, and specific command triggers.

**Scope:**
- List all phone numbers associated with the account.
- For each number, allow configuration of its dedicated AI assistant.
- Define rules for incoming calls (e.g., forward, send to voicemail, AI answer).
- Configure SMS handling (e.g., AI auto-reply, forward, ignore).
- Set up voicemail preferences (transcription, AI summary, notifications).
- Manage AI assistant settings (personality, knowledge base, specific commands).
- View automation logs and performance metrics per number.
- Enable/disable automations globally or per number.

## 2. User Interactions

- **View Numbers:** See a list of owned numbers with their current automation status.
- **Select Number:** Choose a number to configure its specific automations.
- **Configure Call Rules:** Use UI controls (dropdowns, toggles) to define how incoming calls are handled based on conditions (e.g., time of day, caller ID).
- **Configure SMS Rules:** Define auto-reply templates, keywords, or AI-driven responses.
- **Configure Voicemail:** Enable/disable transcription, AI summaries, and set notification preferences.
- **Manage AI Assistant:**
    - Access a dedicated interface (potentially a chat-like UI or form-based) to train the AI.
    - Define custom commands (e.g., "schedule appointment", "check order status").
    - Select AI personality/tone.
    - Upload documents or provide text for the knowledge base.
- **Enable/Disable:** Toggle automation features on or off for a number.
- **View Logs:** Access logs specific to automation triggers and actions for a number.

## 3. Backend Integrations & Services Used

- **Number Management Service:** Fetch the list of user-owned numbers.
- **AI Service:**
    - Configure AI assistant parameters (personality, knowledge).
    - Process incoming calls/SMS based on defined rules.
    - Handle voicemail transcription and summarization.
    - Execute AI commands.
- **Twilio/eSIM Provider Service:** Apply call/SMS routing rules based on automation settings.
- **Database:** Store automation configurations, rules, AI training data, and logs.
- **Notification Service:** Send alerts based on automation events (e.g., failed action, new voicemail summary).

## 4. Necessary API Endpoints

- `GET /api/numbers`: Fetches the list of numbers owned by the user.
- `GET /api/numbers/{phoneNumberId}/automations`: Fetches the current automation configuration for a specific number.
- `PUT /api/numbers/{phoneNumberId}/automations`: Updates the automation configuration for a specific number (covers call rules, SMS rules, voicemail settings).
- `GET /api/numbers/{phoneNumberId}/ai-assistant`: Fetches the AI assistant configuration (personality, knowledge sources, commands).
- `PUT /api/numbers/{phoneNumberId}/ai-assistant`: Updates the AI assistant configuration.
- `POST /api/numbers/{phoneNumberId}/ai-assistant/train`: Submits new training data or knowledge base updates.
- `GET /api/numbers/{phoneNumberId}/automation-logs?type=calls|sms|voicemail|ai&limit=50`: Fetches automation-specific logs.
- `POST /api/numbers/{phoneNumberId}/automations/toggle`: Enables/disables automation for the number.

## 5. Expected Frontend Component Structure

```
/components
  /automation
    AutomationLayout.tsx          # Main layout for the automation section
    NumberList.tsx                # Displays the list of numbers for selection
      NumberListItem.tsx          # Represents a single number in the list
    AutomationConfigPanel.tsx     # Container for configuring a selected number
      CallRulesConfig.tsx         # Component for setting call handling rules
      SmsRulesConfig.tsx          # Component for setting SMS handling rules
      VoicemailConfig.tsx         # Component for setting voicemail preferences
      AIAssistantManager.tsx      # Interface for managing the AI assistant
        AIPersonalitySelector.tsx
        AIKnowledgeUploader.tsx
        AICustomCommandsEditor.tsx
      AutomationToggle.tsx        # Enable/disable switch
    AutomationLogsViewer.tsx      # Displays automation logs
    AutomationSkeleton.tsx      # Loading state placeholder
```

## 6. Data Displayed

- List of phone numbers with status indicators (automation active/inactive).
- Selected number's configuration:
    - Call handling rules (e.g., "If business hours -> AI Answer, Else -> Voicemail").
    - SMS rules (e.g., "Auto-reply with: 'We'll get back to you'").
    - Voicemail settings (transcription enabled, summary enabled).
    - AI Assistant details (name, personality, list of custom commands, knowledge sources).
- Logs: Timestamp, event type (call answered by AI, SMS auto-replied), details, status (success/failure).

## 7. State and UI Behavior

- **Number Selection:** Selecting a number loads its specific configuration into the panel.
- **Loading States:** Skeletons shown while fetching number list or configuration.
- **Saving Changes:** Provide clear feedback (e.g., toast notification) on successful updates or errors. Disable forms during save operations.
- **Complex Configuration:** Use modals or dedicated sub-views for complex settings like AI command editing or rule creation.
- **Real-time Updates:** (Optional) Use WebSockets to reflect status changes (e.g., AI currently handling a call) if feasible.
- **Form Validation:** Implement client-side validation for configuration inputs.

## 8. AI Integration

- **Core Functionality:** This entire section revolves around configuring the AI's behavior for calls, SMS, and voicemails.
- **AI Training Interface:** Provide a user-friendly way to manage the AI's knowledge and commands. This might involve text inputs, file uploads, or even a conversational training interface.
- **AI Performance Feedback:** Display metrics on how effectively the AI is handling interactions (e.g., successful command executions, sentiment analysis of interactions).

## 9. Error Handling Rules

- **API Errors:** Display specific error messages when fetching/saving configurations fails (e.g., "Failed to save call rules: Invalid input"). Use toast notifications.
- **Configuration Conflicts:** Implement backend validation to prevent conflicting rules (e.g., forwarding a call and having the AI answer simultaneously). Provide clear error messages to the user.
- **AI Service Errors:** If the AI service is unavailable during configuration (e.g., fetching personalities), disable relevant UI sections and show an error message.
- **Training Failures:** Notify the user if AI training data submission fails.

## 10. Logging and Usage Tracking Expectations

- **Log:**
    - All configuration changes (what was changed, by whom, for which number).
    - Errors during configuration saving or fetching.
    - AI training submissions (success/failure).
    - Enable/disable actions.
    - Automation rule execution events (handled by the backend service, but logs should be viewable here).
- **Track:**
    - Views of the automation section.
    - Interactions with configuration elements (which settings are most frequently changed).
    - Use of the AI training interface.
    - Views of automation logs.
    - Frequency of enabling/disabling automations.
