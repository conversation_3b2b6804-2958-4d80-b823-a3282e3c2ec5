/**
 * <PERSON>ript to install ESLint plugins for frontend
 */
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Define the plugins to install
const plugins = [
  'eslint-plugin-jsx-a11y',
  'eslint-plugin-import'
];

console.log('Installing ESLint plugins for frontend...');

try {
  // Install the plugins
  const command = `npm install --save-dev ${plugins.join(' ')}`;
  execSync(command, { stdio: 'inherit' });
  
  console.log('Successfully installed ESLint plugins for frontend!');
} catch (error) {
  console.error('Error installing ESLint plugins:', error.message);
  process.exit(1);
}
