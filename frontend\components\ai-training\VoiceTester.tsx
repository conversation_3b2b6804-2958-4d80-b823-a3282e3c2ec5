'use client';

import { useState, useRef } from 'react';
import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { PlayIcon, PauseIcon, MicrophoneIcon, StopIcon } from '@heroicons/react/24/solid';
import LoadingSpinner from '../shared/LoadingSpinner';

export default function VoiceTester() {
  const [inputText, setInputText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  // Generate voice mutation
  const generateVoiceMutation = useMutation({
    mutationFn: async (text: string) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Return a sample audio URL (this is just a placeholder)
        return { audioUrl: 'https://www2.cs.uic.edu/~i101/SoundFiles/CantinaBand3.wav' };
      }
      
      // In production, call API
      const { data } = await axios.post('/api/ai/generate-voice', { text });
      return data;
    },
    onSuccess: (data) => {
      // Set the audio URL
      setAudioUrl(data.audioUrl);
      
      // Show success toast
      console.log('Voice generated successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to generate voice');
    },
  });

  // Start recording
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
        
        // Convert speech to text (in a real app, you would send the blob to your API)
        // For now, we'll just simulate it
        setTimeout(() => {
          setInputText('Hello, I would like to know more about your services.');
        }, 1000);
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error accessing microphone:', error);
    }
  };

  // Stop recording
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      
      // Stop all audio tracks
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
    }
  };

  // Play audio
  const playAudio = () => {
    if (audioRef.current && audioUrl) {
      audioRef.current.play();
      setIsPlaying(true);
    }
  };

  // Pause audio
  const pauseAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  // Handle audio ended
  const handleAudioEnded = () => {
    setIsPlaying(false);
  };

  // Handle generate voice
  const handleGenerateVoice = () => {
    if (!inputText.trim()) return;
    generateVoiceMutation.mutate(inputText);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Voice Testing
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Test how your AI assistant sounds by entering text or recording your voice.
        </p>
      </div>

      <div className="space-y-4">
        <div>
          <label htmlFor="inputText" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Input Text
          </label>
          <div className="flex space-x-2">
            <textarea
              id="inputText"
              rows={4}
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="Enter text to convert to speech..."
              className="block w-full border-gray-300 dark:border-gray-700 dark:bg-gray-800 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              disabled={generateVoiceMutation.isPending}
            />
            <div className="flex flex-col justify-center">
              <button
                type="button"
                onClick={isRecording ? stopRecording : startRecording}
                className={`p-3 rounded-full ${
                  isRecording
                    ? 'bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                {isRecording ? (
                  <StopIcon className="h-5 w-5" />
                ) : (
                  <MicrophoneIcon className="h-5 w-5" />
                )}
              </button>
            </div>
          </div>
          {isRecording && (
            <p className="mt-2 text-sm text-red-600 dark:text-red-400 animate-pulse">
              Recording... Click the stop button when finished.
            </p>
          )}
        </div>

        <div className="flex justify-end">
          <button
            type="button"
            onClick={handleGenerateVoice}
            disabled={!inputText.trim() || generateVoiceMutation.isPending}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {generateVoiceMutation.isPending ? (
              <>
                <LoadingSpinner size="small" color="white" />
                <span className="ml-2">Generating...</span>
              </>
            ) : (
              'Generate Voice'
            )}
          </button>
        </div>
      </div>

      {audioUrl && (
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Generated Audio
          </h3>
          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={isPlaying ? pauseAudio : playAudio}
              className="p-2 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
            >
              {isPlaying ? (
                <PauseIcon className="h-5 w-5" />
              ) : (
                <PlayIcon className="h-5 w-5" />
              )}
            </button>
            <div className="flex-1 h-2 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden">
              {audioRef.current && (
                <div
                  className="h-full bg-blue-500 dark:bg-blue-400"
                  style={{
                    width: `${
                      audioRef.current.duration
                        ? (audioRef.current.currentTime / audioRef.current.duration) * 100
                        : 0
                    }%`,
                  }}
                />
              )}
            </div>
            <span className="text-xs text-gray-500 dark:text-gray-400 min-w-[40px]">
              {audioRef.current
                ? `${Math.floor(audioRef.current.currentTime)}s`
                : '0s'}
            </span>
          </div>
          <audio
            ref={audioRef}
            src={audioUrl}
            onEnded={handleAudioEnded}
            onTimeUpdate={() => {
              // Force re-render to update progress bar
              setIsPlaying(audioRef.current?.paused === false);
            }}
            className="hidden"
          />
        </div>
      )}

      <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-md p-4 text-sm">
        <h3 className="font-medium text-yellow-800 dark:text-yellow-300 mb-1">
          About Voice Testing
        </h3>
        <p className="text-yellow-700 dark:text-yellow-200">
          This tool allows you to test how your AI assistant will sound when speaking to customers. You can enter text manually or record your voice to generate a sample of the AI's speech. The voice model can be selected in the Voice Models tab.
        </p>
      </div>
    </div>
  );
}
