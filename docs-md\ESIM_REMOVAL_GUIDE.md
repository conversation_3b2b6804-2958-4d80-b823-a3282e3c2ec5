---
title: eSIM Removal Guide
description: Guide to removing eSIM functionality from the CallSaver.app
date: 2025-04-28
status: Required
---

# eSIM Removal Guide

## Overview

This document outlines the process for removing all eSIM-related functionality from the CallSaver.app codebase. The eSIM feature has been deprecated and should be completely removed to streamline the application and focus on core call/SMS management capabilities.

## Components to Remove

### Backend Components

1. **Controllers**
   - `esimController.js`
   - Any eSIM-related methods in other controllers

2. **Routes**
   - `esimRoutes.js`
   - eSIM route references in `apiGatewayRoutes.js`

3. **Services**
   - Any eSIM-related services (e.g., integration with eSIM providers)
   - eSIM methods in telephony services

4. **Models**
   - Keep the `EsimPurchase` model in the database schema to preserve historical data
   - Remove any eSIM-specific fields from active models

5. **Configuration**
   - Remove eSIM provider credentials and configuration settings
   - Update environment variable templates to remove eSIM variables

### Frontend Components

1. **Pages**
   - Remove eSIM management pages
   - Remove eSIM components from dashboard

2. **API**
   - Remove eSIM-related API calls in frontend services

3. **UI Components**
   - Remove eSIM-specific UI components
   - Update navigation to remove eSIM-related links

### Documentation

1. **Archive eSIM Documentation**
   - Move all eSIM-related documentation to an archive folder
   - Update system documentation to remove eSIM references

## Implementation Steps

1. **Create a feature branch** for the removal process
2. **Identify and remove backend components**
   - Comment out eSIM routes in API gateway first
   - Remove eSIM controllers and services
   - Update any dependent services
3. **Remove frontend components**
   - Remove eSIM pages and components
   - Update navigation and dashboard
4. **Update database references**
   - Keep historical tables but remove new references
5. **Update documentation**
   - Remove eSIM from user guides and API documentation
6. **Test thoroughly** to ensure removal doesn't break core functionality
7. **Deploy and monitor** for any issues

## Associated Files

The following files should be archived or removed:

- `back/backend/controllers/esimController.js`
- `back/backend/routes/esimRoutes.js`
- `back/backend/esim/**/*` (entire directory)
- `front/mainpage/app/esim/**/*` (if exists)
- `ESIM_INTEGRATION_README.md`
- `AIRALO_CREDENTIALS_GUIDE.md`
- `AIRALO_INTEGRATION_GUIDE.md`
- `docs/functional_specs/esim_transition_plan.mdc`

## Testing After Removal

After removing eSIM functionality, thoroughly test:

1. User signup and login flow
2. Phone number purchase and management
3. Call and SMS functionality
4. Dashboard metrics and analytics
5. Navigation and UI for any broken links or references

## Conclusion

Removing the eSIM functionality will simplify the codebase and allow the team to focus on core features. This process should be done carefully to ensure no disruption to existing users and functionality.
