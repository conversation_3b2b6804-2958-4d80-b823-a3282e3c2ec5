"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import DashboardNavbar from '../components/DashboardNavbar';
import UserSettingsForm from '../components/settings/UserSettingsForm';
import { useSession } from '../providers/SessionProvider';

export default function SettingsPage() {
  const { user, loading } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('profile');
  
  // Redirect to login if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/signin');
    }
  }, [user, loading, router]);
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-950 flex items-center justify-center">
        <div className="loading-pulse"></div>
      </div>
    );
  }
  
  if (!user) {
    return null; // Will redirect to signin
  }
  
  return (
    <div className="min-h-screen bg-gray-950">
      {/* Dashboard Navbar with Auth user data */}
      <DashboardNavbar user={user} />
      
      {/* Main Content */}
      <main className="container mx-auto px-4 py-8 pt-24">
        <div className="max-w-5xl mx-auto">
          <h1 className="text-2xl font-bold text-white mb-6">Settings</h1>
          
          {/* Settings Tabs */}
          <div className="bg-gray-900/80 backdrop-blur-sm rounded-xl border border-gray-800/60 overflow-hidden">
            <div className="border-b border-gray-800">
              <nav className="flex space-x-4 px-4">
                <button
                  onClick={() => setActiveTab('profile')}
                  className={`py-4 px-2 border-b-2 font-medium text-sm ${
                    activeTab === 'profile'
                      ? 'border-purple-500 text-purple-400'
                      : 'border-transparent text-gray-400 hover:text-gray-300'
                  }`}
                >
                  Profile Settings
                </button>
                <button
                  onClick={() => setActiveTab('notifications')}
                  className={`py-4 px-2 border-b-2 font-medium text-sm ${
                    activeTab === 'notifications'
                      ? 'border-purple-500 text-purple-400'
                      : 'border-transparent text-gray-400 hover:text-gray-300'
                  }`}
                >
                  Notification Preferences
                </button>
                <button
                  onClick={() => setActiveTab('billing')}
                  className={`py-4 px-2 border-b-2 font-medium text-sm ${
                    activeTab === 'billing'
                      ? 'border-purple-500 text-purple-400'
                      : 'border-transparent text-gray-400 hover:text-gray-300'
                  }`}
                >
                  Billing & Subscription
                </button>
                <button
                  onClick={() => setActiveTab('security')}
                  className={`py-4 px-2 border-b-2 font-medium text-sm ${
                    activeTab === 'security'
                      ? 'border-purple-500 text-purple-400'
                      : 'border-transparent text-gray-400 hover:text-gray-300'
                  }`}
                >
                  Security
                </button>
              </nav>
            </div>
            
            <div className="p-6">
              {activeTab === 'profile' && (
                <UserSettingsForm user={user} />
              )}
              
              {activeTab === 'notifications' && (
                <div className="text-white">
                  <h2 className="text-xl font-semibold mb-4">Notification Preferences</h2>
                  <p className="text-gray-400 mb-6">Configure how and when you receive notifications from CallSaver.</p>
                  
                  <div className="space-y-6">
                    <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-white font-medium">Email Notifications</h3>
                          <p className="text-gray-400 text-sm mt-1">Receive important updates and summaries via email</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-purple-500/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                        </label>
                      </div>
                    </div>
                    
                    <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-white font-medium">SMS Notifications</h3>
                          <p className="text-gray-400 text-sm mt-1">Get instant alerts about missed calls and important events</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-purple-500/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                        </label>
                      </div>
                    </div>
                    
                    <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-white font-medium">Browser Notifications</h3>
                          <p className="text-gray-400 text-sm mt-1">Receive real-time notifications in your browser</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-purple-500/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                        </label>
                      </div>
                    </div>
                    
                    <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="text-white font-medium">Weekly Summary</h3>
                          <p className="text-gray-400 text-sm mt-1">Receive a weekly summary of your call activity</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" defaultChecked />
                          <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-purple-500/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                        </label>
                      </div>
                    </div>
                    
                    <div className="flex justify-end">
                      <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                        Save Preferences
                      </button>
                    </div>
                  </div>
                </div>
              )}
              
              {activeTab === 'billing' && (
                <div className="text-white">
                  <h2 className="text-xl font-semibold mb-4">Billing & Subscription</h2>
                  <p className="text-gray-400 mb-6">Manage your subscription plan and payment methods.</p>
                  
                  <div className="bg-gray-800/50 rounded-lg p-5 border border-purple-500/30 mb-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium text-white">Current Plan</h3>
                      <span className="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm font-medium">
                        Pro Plan
                      </span>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-gray-400 text-sm">Billing Cycle</p>
                        <p className="text-white">Monthly</p>
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm">Next Billing Date</p>
                        <p className="text-white">June 15, 2023</p>
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm">Amount</p>
                        <p className="text-white">$29.99/month</p>
                      </div>
                      <div>
                        <p className="text-gray-400 text-sm">Status</p>
                        <p className="text-green-400">Active</p>
                      </div>
                    </div>
                    <div className="flex space-x-3 mt-4">
                      <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                        Upgrade Plan
                      </button>
                      <button className="px-4 py-2 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white rounded-lg transition-colors">
                        Cancel Subscription
                      </button>
                    </div>
                  </div>
                  
                  <h3 className="text-lg font-medium text-white mb-4">Payment Methods</h3>
                  <div className="bg-gray-800/50 rounded-lg p-4 border border-gray-700/30 mb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="bg-blue-500/20 p-2 rounded-lg mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                          </svg>
                        </div>
                        <div>
                          <p className="text-white font-medium">Visa ending in 4242</p>
                          <p className="text-gray-400 text-sm">Expires 12/2024</p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button className="text-gray-400 hover:text-white">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                          </svg>
                        </button>
                        <button className="text-gray-400 hover:text-red-400">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                  
                  <button className="px-4 py-2 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white rounded-lg transition-colors flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                    Add Payment Method
                  </button>
                </div>
              )}
              
              {activeTab === 'security' && (
                <div className="text-white">
                  <h2 className="text-xl font-semibold mb-4">Security Settings</h2>
                  <p className="text-gray-400 mb-6">Manage your account security and authentication options.</p>
                  
                  <div className="space-y-6">
                    <div className="bg-gray-800/50 rounded-lg p-5 border border-gray-700/30">
                      <h3 className="text-lg font-medium text-white mb-4">Change Password</h3>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-gray-300 text-sm font-medium mb-2">Current Password</label>
                          <input 
                            type="password" 
                            className="w-full bg-gray-900 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500"
                            placeholder="Enter your current password"
                          />
                        </div>
                        <div>
                          <label className="block text-gray-300 text-sm font-medium mb-2">New Password</label>
                          <input 
                            type="password" 
                            className="w-full bg-gray-900 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500"
                            placeholder="Enter new password"
                          />
                        </div>
                        <div>
                          <label className="block text-gray-300 text-sm font-medium mb-2">Confirm New Password</label>
                          <input 
                            type="password" 
                            className="w-full bg-gray-900 border border-gray-700 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:border-purple-500"
                            placeholder="Confirm new password"
                          />
                        </div>
                        <div className="flex justify-end">
                          <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors">
                            Update Password
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-800/50 rounded-lg p-5 border border-gray-700/30">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h3 className="text-lg font-medium text-white">Two-Factor Authentication</h3>
                          <p className="text-gray-400 text-sm mt-1">Add an extra layer of security to your account</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input type="checkbox" className="sr-only peer" />
                          <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-purple-500/50 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                        </label>
                      </div>
                      <p className="text-gray-400 text-sm">
                        When enabled, you'll be required to enter a verification code from your mobile device when signing in.
                      </p>
                    </div>
                    
                    <div className="bg-gray-800/50 rounded-lg p-5 border border-gray-700/30">
                      <h3 className="text-lg font-medium text-white mb-4">Session Management</h3>
                      <p className="text-gray-400 text-sm mb-4">
                        You're currently signed in on these devices. You can sign out of any sessions you don't recognize.
                      </p>
                      
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-3 bg-gray-900/60 rounded-lg">
                          <div className="flex items-center">
                            <div className="bg-green-500/20 p-2 rounded-lg mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 110-12 6 6 0 010 12z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <div>
                              <p className="text-white font-medium">Current Session</p>
                              <p className="text-gray-400 text-xs">Windows • Chrome • New York, USA</p>
                            </div>
                          </div>
                          <span className="text-green-400 text-xs font-medium">Active Now</span>
                        </div>
                        
                        <div className="flex items-center justify-between p-3 bg-gray-900/60 rounded-lg">
                          <div className="flex items-center">
                            <div className="bg-gray-500/20 p-2 rounded-lg mr-3">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 110-12 6 6 0 010 12z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <div>
                              <p className="text-white font-medium">iPhone 12</p>
                              <p className="text-gray-400 text-xs">iOS • Safari • San Francisco, USA</p>
                            </div>
                          </div>
                          <button className="text-red-400 text-xs font-medium hover:text-red-300">
                            Sign Out
                          </button>
                        </div>
                      </div>
                      
                      <div className="mt-4">
                        <button className="px-4 py-2 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 rounded-lg transition-colors">
                          Sign Out of All Other Sessions
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
