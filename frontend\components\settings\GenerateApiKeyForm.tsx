'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useGenerateApiKey } from '../../../hooks/useSettings';

interface GenerateApiKeyFormProps {
  onSuccess: (fullKey: string) => void;
  onCancel: () => void;
}

export default function GenerateApiKeyForm({ onSuccess, onCancel }: GenerateApiKeyFormProps) {
  // Available permissions
  const availablePermissions = [
    { id: 'read:calls', label: 'Read Calls', description: 'View call logs and recordings' },
    { id: 'write:calls', label: 'Write Calls', description: 'Make calls and update call settings' },
    { id: 'read:messages', label: 'Read Messages', description: 'View SMS messages' },
    { id: 'write:messages', label: 'Write Messages', description: 'Send SMS messages' },
    { id: 'read:transcriptions', label: 'Read Transcriptions', description: 'View call transcriptions' },
    { id: 'read:analytics', label: 'Read Analytics', description: 'View usage analytics' }
  ];
  
  // Form validation
  const { 
    register, 
    handleSubmit, 
    formState: { errors } 
  } = useForm({
    defaultValues: {
      label: '',
      permissions: ['read:calls'] // Default permission
    }
  });
  
  // Generate API key mutation
  const generateApiKey = useGenerateApiKey();
  
  // Handle form submission
  const onSubmit = async (data: any) => {
    try {
      const result = await generateApiKey.mutateAsync({
        label: data.label,
        permissions: data.permissions
      });
      
      onSuccess(result.fullKey);
    } catch (err) {
      // Error is handled by the mutation
      console.error('Error generating API key:', err);
    }
  };
  
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {generateApiKey.isError && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md text-red-800 dark:text-red-400 text-sm">
          {generateApiKey.error instanceof Error 
            ? generateApiKey.error.message 
            : 'Failed to generate API key. Please try again.'}
        </div>
      )}
      
      {/* Key Label */}
      <div className="mb-4">
        <label htmlFor="label" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Key Label
        </label>
        <input
          id="label"
          type="text"
          {...register('label', { 
            required: 'Label is required',
            minLength: {
              value: 3,
              message: 'Label must be at least 3 characters'
            },
            maxLength: {
              value: 50,
              message: 'Label must be less than 50 characters'
            }
          })}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          placeholder="e.g., Production API Key"
        />
        {errors.label && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.label.message}</p>
        )}
      </div>
      
      {/* Permissions */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Permissions
        </label>
        <div className="space-y-2">
          {availablePermissions.map((permission) => (
            <div key={permission.id} className="flex items-start">
              <div className="flex items-center h-5">
                <input
                  id={permission.id}
                  type="checkbox"
                  value={permission.id}
                  {...register('permissions', { 
                    required: 'At least one permission is required' 
                  })}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor={permission.id} className="font-medium text-gray-700 dark:text-gray-300">
                  {permission.label}
                </label>
                <p className="text-gray-500 dark:text-gray-400">{permission.description}</p>
              </div>
            </div>
          ))}
        </div>
        {errors.permissions && (
          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.permissions.message}</p>
        )}
      </div>
      
      {/* Security Warning */}
      <div className="mb-6 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-300">
              Security Notice
            </h3>
            <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-200">
              <p>
                API keys provide full access to your account based on the permissions you select. 
                Keep your API keys secure and never share them publicly.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Form Actions */}
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={generateApiKey.isPending}
          className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {generateApiKey.isPending ? 'Generating...' : 'Generate API Key'}
        </button>
      </div>
    </form>
  );
}
