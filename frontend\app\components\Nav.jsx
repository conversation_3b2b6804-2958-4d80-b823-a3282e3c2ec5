"use client";

import { useState } from 'react';
import Link from 'next/link';
import { UserCircleIcon, Bars3Icon, XMarkIcon, CreditCardIcon } from '@heroicons/react/24/outline';
import { createClient } from '@supabase/supabase-js';
import { loadStripe } from '@stripe/stripe-js';
import { BillingAPI } from '../utils/api';

// Load Stripe.js
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);

// Define Credit Packages (Match backend)
const CREDIT_PACKAGES_UI = [
  { id: 'credits_10', name: '10 Credits', description: 'Good for starters', price: '$5', amount: 10 },
  { id: 'credits_50', name: '50 Credits', description: 'Most popular', price: '$20', amount: 50 },
  { id: 'credits_100', name: '100 Credits', description: 'Best value', price: '$35', amount: 100 },
];

export default function Nav({ userName = 'User', userEmail = '' }) {
  const [showMenu, setShowMenu] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const [showCreditModal, setShowCreditModal] = useState(false);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentError, setPaymentError] = useState('');
  
  // Handle logout click
  const handleLogout = async () => {
    const demoUser = localStorage.getItem('callsaver_demo_user');
    
    if (demoUser) {
      // For demo users, just remove the demo flag
      localStorage.removeItem('callsaver_demo_user');
      window.location.href = '/signin';
      return;
    }
    
    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
      
      if (!supabaseUrl || !supabaseKey) {
        console.error('Supabase credentials missing');
        window.location.href = '/signin';
        return;
      }
      
      const supabase = createClient(supabaseUrl, supabaseKey);
      await supabase.auth.signOut();
      window.location.href = '/signin';
    } catch (error) {
      console.error('Error signing out:', error);
      window.location.href = '/signin';
    }
  };
  
  // Handle buying credits
  const handleBuyCredits = async (packageId) => {
    setIsProcessingPayment(true);
    setPaymentError('');
    console.log(`Attempting to buy credit package: ${packageId}`);

    try {
      // 1. Create Checkout Session (call backend)
      const sessionData = await BillingAPI.createCheckoutSession({ packageId });
      const sessionId = sessionData.sessionId;
      console.log(`Created checkout session: ${sessionId}`);

      // 2. Redirect to Stripe Checkout
      const stripe = await stripePromise;
      const { error } = await stripe.redirectToCheckout({ sessionId });

      if (error) {
        console.error('Stripe redirect error:', error);
        setPaymentError(error.message || 'Failed to redirect to Stripe.');
        setIsProcessingPayment(false);
      }
      // If redirect is successful, the user leaves the page.
      // Need to handle success/cancel on return via URL params.

    } catch (err) {
      console.error('Error creating checkout session:', err);
      setPaymentError(err.message || 'Could not initiate credit purchase.');
      setIsProcessingPayment(false);
    }
  };

  return (
    <>
      <nav className="mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Link href="/dashboard" className="text-2xl font-bold text-white flex items-center">
              <span className="text-purple-500">Call</span>
              <span>Saver</span>
            </Link>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex ml-8 space-x-6">
              <Link href="/dashboard" className="text-white hover:text-purple-400 transition-colors">
                Dashboard
              </Link>
              <Link href="/dashboard/buy-number" className="text-gray-400 hover:text-purple-400 transition-colors">
                Buy Number
              </Link>
              <Link href="/dashboard/settings" className="text-gray-400 hover:text-purple-400 transition-colors">
                Settings
              </Link>
              <Link href="/dashboard/help" className="text-gray-400 hover:text-purple-400 transition-colors">
                Help
              </Link>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
             {/* Buy Credits Button */}
             <button 
                onClick={() => setShowCreditModal(true)}
                className="hidden md:flex items-center px-3 py-1.5 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-md transition-colors"
              >
                <CreditCardIcon className="h-4 w-4 mr-1.5" />
                Buy Credits
              </button>
          
              {/* User Menu */}
              <div className="relative">
                <button 
                  className="flex items-center space-x-2 bg-gray-800/70 hover:bg-gray-800 rounded-lg pl-2 pr-3 py-1.5 transition-colors"
                  onClick={() => setShowDropdown(!showDropdown)}
                >
                  <UserCircleIcon className="h-8 w-8 text-gray-400" />
                  <div className="text-left">
                    <p className="text-white text-sm font-medium leading-none">{userName}</p>
                    {userEmail && (
                      <p className="text-gray-400 text-xs leading-snug">{userEmail}</p>
                    )}
                  </div>
                </button>
                
                {showDropdown && (
                  <div className="absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg py-1 z-10">
                    <Link 
                      href="/dashboard/settings/profile" 
                      className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white"
                      onClick={() => setShowDropdown(false)}
                    >
                      Account Settings
                    </Link>
                    <Link 
                      href="/dashboard/settings/billing" 
                      className="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white"
                      onClick={() => setShowDropdown(false)}
                    >
                      Billing & Usage
                    </Link>
                    <button 
                      onClick={() => { setShowDropdown(false); setShowCreditModal(true); }}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white"
                    >
                      Buy Credits
                    </button>
                    <button 
                      className="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white"
                      onClick={handleLogout}
                    >
                      Sign Out
                    </button>
                  </div>
                )}
              </div>
           </div>
          
          {/* Mobile menu button */}
          <div className="md:hidden">
            <button 
              className="text-gray-400 hover:text-white"
              onClick={() => setShowMenu(!showMenu)}
            >
              {showMenu ? (
                <XMarkIcon className="h-6 w-6" />
              ) : (
                <Bars3Icon className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
        
        {/* Mobile menu */}
        {showMenu && (
          <div className="md:hidden mt-4 bg-gray-800/80 backdrop-blur-sm rounded-lg p-4">
            <div className="flex flex-col space-y-3">
              <Link 
                href="/dashboard" 
                className="text-white hover:text-purple-400 transition-colors py-2"
                onClick={() => setShowMenu(false)}
              >
                Dashboard
              </Link>
              <Link 
                href="/dashboard/buy-number" 
                className="text-gray-400 hover:text-purple-400 transition-colors py-2"
                onClick={() => setShowMenu(false)}
              >
                Buy Number
              </Link>
              <Link 
                href="/dashboard/settings" 
                className="text-gray-400 hover:text-purple-400 transition-colors py-2"
                onClick={() => setShowMenu(false)}
              >
                Settings
              </Link>
              <Link 
                href="/dashboard/help" 
                className="text-gray-400 hover:text-purple-400 transition-colors py-2"
                onClick={() => setShowMenu(false)}
              >
                Help
              </Link>
              <button 
                className="text-left text-red-400 hover:text-red-300 transition-colors py-2"
                onClick={handleLogout}
              >
                Sign Out
              </button>
            </div>
          </div>
        )}
      </nav>

      {/* Buy Credits Modal */}
      {showCreditModal && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900 rounded-xl border border-purple-500/30 shadow-xl w-full max-w-lg relative">
            {/* Close Button */}
            <button 
              onClick={() => setShowCreditModal(false)}
              className="absolute top-3 right-3 text-gray-400 hover:text-white transition-colors"
              disabled={isProcessingPayment}
            >
              <XMarkIcon className="h-6 w-6" />
            </button>

            <div className="p-6 md:p-8">
              <h2 className="text-2xl font-bold text-white mb-2 text-center">Buy Credits</h2>
              <p className="text-gray-400 text-center mb-6">Choose a package to add credits to your account.</p>

              {paymentError && (
                 <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-center">
                   <p className="text-red-200 text-sm">{paymentError}</p>
                 </div>
              )}

              <div className="space-y-4">
                {CREDIT_PACKAGES_UI.map((pkg) => (
                  <div key={pkg.id} className="bg-gray-800/60 rounded-lg border border-gray-700 p-4 flex justify-between items-center">
                    <div>
                      <h3 className="font-semibold text-white">{pkg.name}</h3>
                      <p className="text-sm text-gray-400">{pkg.description}</p>
                    </div>
                    <button 
                      onClick={() => handleBuyCredits(pkg.id)}
                      disabled={isProcessingPayment}
                      className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed min-w-[100px] text-center"
                    >
                      {isProcessingPayment ? 
                        <div className="h-5 w-5 animate-spin rounded-full border-b-2 border-white mx-auto"></div> 
                        : pkg.price
                      }
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
} 