'use client';

import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';
import { DocumentTextIcon, TrashIcon, PlusIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from '../shared/LoadingSpinner';
import EmptyState from '../shared/EmptyState';

interface KnowledgeSource {
  id: string;
  name: string;
  type: 'file' | 'url' | 'text';
  status: 'processing' | 'active' | 'error';
  lastUpdated: string;
  size?: number;
  url?: string;
}

export default function KnowledgeBaseManager() {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newSourceUrl, setNewSourceUrl] = useState('');
  const [newSourceName, setNewSourceName] = useState('');
  const [urlError, setUrlError] = useState<string | null>(null);
  const queryClient = useQueryClient();

  // Fetch knowledge sources
  const {
    data: knowledgeSources,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['ai', 'knowledge-base'],
    queryFn: async () => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock data
        return [
          {
            id: '1',
            name: 'Company Website',
            type: 'url',
            status: 'active',
            lastUpdated: '2023-06-01T10:30:00Z',
            url: 'https://example.com',
          },
          {
            id: '2',
            name: 'Product Documentation',
            type: 'file',
            status: 'active',
            lastUpdated: '2023-06-05T14:45:00Z',
            size: 1024000,
          },
          {
            id: '3',
            name: 'Customer FAQ',
            type: 'text',
            status: 'active',
            lastUpdated: '2023-06-10T09:15:00Z',
          },
        ] as KnowledgeSource[];
      }
      
      // In production, fetch from API
      const { data } = await axios.get<KnowledgeSource[]>('/api/ai/knowledge-base');
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Add knowledge source mutation
  const addSourceMutation = useMutation({
    mutationFn: async (data: { url: string; name: string }) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Validate URL
        try {
          new URL(data.url);
        } catch (e) {
          throw new Error('Invalid URL format');
        }
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data: response } = await axios.post('/api/ai/knowledge-base', data);
      return response;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['ai', 'knowledge-base'] });
      
      // Reset form
      setNewSourceUrl('');
      setNewSourceName('');
      setShowAddForm(false);
      
      // Show success toast
      console.log('Knowledge source added successfully');
    },
    onError: (error) => {
      // Show error message
      if (error instanceof Error) {
        setUrlError(error.message);
      } else {
        setUrlError('Failed to add knowledge source');
      }
    },
  });

  // Delete knowledge source mutation
  const deleteSourceMutation = useMutation({
    mutationFn: async (id: string) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.delete(`/api/ai/knowledge-base/${id}`);
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['ai', 'knowledge-base'] });
      
      // Show success toast
      console.log('Knowledge source deleted successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to delete knowledge source');
    },
  });

  // Refresh knowledge source mutation
  const refreshSourceMutation = useMutation({
    mutationFn: async (id: string) => {
      // In development, use mock data
      if (process.env.NODE_ENV === 'development') {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1200));
        
        // Return mock success
        return { success: true };
      }
      
      // In production, call API
      const { data } = await axios.post(`/api/ai/knowledge-base/${id}/refresh`);
      return data;
    },
    onSuccess: () => {
      // Invalidate queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['ai', 'knowledge-base'] });
      
      // Show success toast
      console.log('Knowledge source refreshed successfully');
    },
    onError: () => {
      // Show error toast
      console.error('Failed to refresh knowledge source');
    },
  });

  // Handle add source form submission
  const handleAddSource = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Reset error
    setUrlError(null);
    
    // Validate URL
    try {
      new URL(newSourceUrl);
    } catch (e) {
      setUrlError('Please enter a valid URL');
      return;
    }
    
    // Submit form
    addSourceMutation.mutate({
      url: newSourceUrl,
      name: newSourceName || new URL(newSourceUrl).hostname,
    });
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (error) {
      return dateString;
    }
  };

  // Format file size
  const formatFileSize = (bytes?: number) => {
    if (bytes === undefined) return '';
    
    if (bytes < 1024) {
      return `${bytes} B`;
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    } else {
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    }
  };

  // If loading, show loading spinner
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" color="blue" />
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-md">
        <h3 className="text-sm font-medium text-red-800 dark:text-red-300">
          Failed to load knowledge base
        </h3>
        <p className="mt-2 text-sm text-red-700 dark:text-red-200">
          We couldn't load your knowledge base. Please try again later.
        </p>
        <button
          type="button"
          onClick={() => refetch()}
          className="mt-3 inline-flex items-center px-3 py-1.5 border border-red-300 dark:border-red-700 shadow-sm text-xs font-medium rounded text-red-700 dark:text-red-300 bg-white dark:bg-gray-800 hover:bg-red-50 dark:hover:bg-red-900/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800"
        >
          <ArrowPathIcon className="h-4 w-4 mr-1" />
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">
            Knowledge Base
          </h2>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Manage external sources of knowledge for your AI assistant.
          </p>
        </div>
        <button
          type="button"
          onClick={() => setShowAddForm(!showAddForm)}
          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
        >
          <PlusIcon className="h-4 w-4 mr-1" />
          Add Source
        </button>
      </div>

      {/* Add Source Form */}
      {showAddForm && (
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-medium text-blue-800 dark:text-blue-300 mb-3">
            Add Knowledge Source
          </h3>
          <form onSubmit={handleAddSource} className="space-y-4">
            <div>
              <label htmlFor="sourceName" className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">
                Name (Optional)
              </label>
              <input
                type="text"
                id="sourceName"
                value={newSourceName}
                onChange={(e) => setNewSourceName(e.target.value)}
                placeholder="e.g., Company Website"
                className="block w-full border-blue-300 dark:border-blue-700 dark:bg-blue-900/30 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                disabled={addSourceMutation.isPending}
              />
            </div>
            <div>
              <label htmlFor="sourceUrl" className="block text-sm font-medium text-blue-700 dark:text-blue-400 mb-1">
                URL
              </label>
              <input
                type="text"
                id="sourceUrl"
                value={newSourceUrl}
                onChange={(e) => {
                  setNewSourceUrl(e.target.value);
                  setUrlError(null);
                }}
                placeholder="https://example.com"
                className={`block w-full border-blue-300 dark:border-blue-700 dark:bg-blue-900/30 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                  urlError ? 'border-red-300 dark:border-red-700 focus:ring-red-500 focus:border-red-500' : ''
                }`}
                disabled={addSourceMutation.isPending}
                required
              />
              {urlError && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                  {urlError}
                </p>
              )}
            </div>
            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={() => setShowAddForm(false)}
                className="inline-flex items-center px-3 py-2 border border-blue-300 dark:border-blue-700 shadow-sm text-sm leading-4 font-medium rounded-md text-blue-700 dark:text-blue-400 bg-white dark:bg-gray-800 hover:bg-blue-50 dark:hover:bg-blue-900/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800"
                disabled={addSourceMutation.isPending}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={addSourceMutation.isPending}
              >
                {addSourceMutation.isPending ? (
                  <>
                    <LoadingSpinner size="small" color="white" />
                    <span className="ml-2">Adding...</span>
                  </>
                ) : (
                  'Add Source'
                )}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Knowledge Sources List */}
      {knowledgeSources && knowledgeSources.length > 0 ? (
        <div className="bg-white dark:bg-gray-800 overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-900/50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Source
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Last Updated
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {knowledgeSources.map((source) => (
                  <tr key={source.id}>
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {source.name}
                      </div>
                      {source.url && (
                        <div className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
                          {source.url}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {source.type.charAt(0).toUpperCase() + source.type.slice(1)}
                      {source.size && ` (${formatFileSize(source.size)})`}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          source.status === 'active'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                            : source.status === 'processing'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                            : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                        }`}
                      >
                        {source.status.charAt(0).toUpperCase() + source.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(source.lastUpdated)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          type="button"
                          onClick={() => refreshSourceMutation.mutate(source.id)}
                          disabled={refreshSourceMutation.isPending || source.status === 'processing'}
                          className="text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Refresh"
                        >
                          <ArrowPathIcon className="h-5 w-5" />
                        </button>
                        <button
                          type="button"
                          onClick={() => deleteSourceMutation.mutate(source.id)}
                          disabled={deleteSourceMutation.isPending}
                          className="text-gray-400 hover:text-red-500 dark:hover:text-red-400 disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Delete"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <EmptyState
          title="No knowledge sources yet"
          description="Add external sources like websites or documents to enhance your AI assistant's knowledge."
          icon={<DocumentTextIcon className="h-12 w-12 text-gray-400" />}
          actionText="Add Source"
          onAction={() => setShowAddForm(true)}
        />
      )}

      <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-md p-4 text-sm">
        <h3 className="font-medium text-yellow-800 dark:text-yellow-300 mb-1">
          About Knowledge Base
        </h3>
        <p className="text-yellow-700 dark:text-yellow-200">
          The knowledge base allows your AI assistant to access information from external sources like websites, documents, and databases. This helps it provide more accurate and relevant responses to customer inquiries about your business, products, or services.
        </p>
      </div>
    </div>
  );
}
