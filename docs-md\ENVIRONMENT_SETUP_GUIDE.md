---
title: CallSaver.app Environment Setup Guide
description: Comprehensive guide for setting up development, staging, and production environments
date: 2025-04-28
status: Required
---

# CallSaver.app Environment Setup Guide

## Overview

This document provides detailed instructions for setting up development, staging, and production environments for the CallSaver.app application. It covers environment variables, database configuration, external service integration, and local development setup.

## Environment Types

### Development Environment

- **Purpose**: Local development, testing, and debugging
- **Database**: Local PostgreSQL and MongoDB instances
- **Services**: Local or development-tier external services
- **Features**: Hot reloading, debug logging, sample data

### Staging Environment

- **Purpose**: Testing deployments, QA, integration verification
- **Database**: Isolated staging databases with structure similar to production
- **Services**: Separate staging accounts for external services (Twilio, OpenAI, etc.)
- **Features**: Similar to production but with enhanced logging and monitoring

### Production Environment

- **Purpose**: Live application serving end users
- **Database**: Production databases with proper backups and scaling
- **Services**: Production accounts with appropriate limits and monitoring
- **Features**: Optimized performance, minimal logging, error tracking

## Environment Variables

### Backend Environment Variables

Create the following `.env` files in the `back/backend` directory:

#### Development (.env.development)

```env
# Server Configuration
PORT=3000
NODE_ENV=development
API_URL=http://localhost:3000/api
FRONTEND_URL=http://localhost:3001

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/callsaver_dev
DIRECT_URL=postgresql://postgres:postgres@localhost:5432/callsaver_dev
MONGODB_URI=mongodb://localhost:27017/callsaver_dev

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=development_jwt_secret_change_in_production
JWT_REFRESH_SECRET=development_refresh_secret_change_in_production
JWT_EXPIRY=24h
JWT_REFRESH_EXPIRY=7d

# Twilio (Development Account)
TWILIO_ACCOUNT_SID=AC_YOUR_DEV_ACCOUNT_SID
TWILIO_AUTH_TOKEN=YOUR_DEV_AUTH_TOKEN
TWILIO_PHONE_NUMBER=+***********
TWILIO_WEBHOOK_BASE_URL=http://localhost:3000/api/webhooks/twilio

# OpenAI
OPENAI_API_KEY=YOUR_OPENAI_API_KEY
OPENAI_ORGANIZATION=YOUR_OPENAI_ORG_ID

# Stripe
STRIPE_SECRET_KEY=sk_test_YOUR_TEST_KEY
STRIPE_WEBHOOK_SECRET=whsec_YOUR_TEST_WEBHOOK_SECRET
STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_TEST_KEY

# Pinecone (Vector Database)
PINECONE_API_KEY=YOUR_PINECONE_API_KEY
PINECONE_ENVIRONMENT=YOUR_PINECONE_ENVIRONMENT
PINECONE_INDEX=callsaver-dev

# Supabase (Auth)
SUPABASE_URL=YOUR_SUPABASE_URL
SUPABASE_KEY=YOUR_SUPABASE_KEY
SUPABASE_JWT_SECRET=YOUR_SUPABASE_JWT_SECRET

# Logging
LOG_LEVEL=debug

# Feature Flags
FEATURE_AI_ASSISTANT=true
FEATURE_ANALYTICS=true
FEATURE_ADVANCED_AUTOMATION=true
```

#### Staging (.env.staging)

```env
# Server Configuration
PORT=3000
NODE_ENV=staging
API_URL=https://staging-api.callsaver.app/api
FRONTEND_URL=https://staging.callsaver.app

# Database Configuration - Use Railway provisioned values
DATABASE_URL=${DATABASE_URL_FROM_RAILWAY}
DIRECT_URL=${DIRECT_URL_FROM_RAILWAY}
MONGODB_URI=${MONGODB_URI_FROM_ATLAS}

# Redis Configuration
REDIS_URL=${REDIS_URL_FROM_RAILWAY}

# Authentication - Use strong secrets for staging
JWT_SECRET=${GENERATE_STRONG_SECRET}
JWT_REFRESH_SECRET=${GENERATE_STRONG_SECRET}
JWT_EXPIRY=24h
JWT_REFRESH_EXPIRY=7d

# Twilio (Staging Account)
TWILIO_ACCOUNT_SID=AC_YOUR_STAGING_ACCOUNT_SID
TWILIO_AUTH_TOKEN=YOUR_STAGING_AUTH_TOKEN
TWILIO_PHONE_NUMBER=+***********
TWILIO_WEBHOOK_BASE_URL=https://staging-api.callsaver.app/api/webhooks/twilio

# OpenAI
OPENAI_API_KEY=YOUR_OPENAI_API_KEY
OPENAI_ORGANIZATION=YOUR_OPENAI_ORG_ID

# Stripe
STRIPE_SECRET_KEY=sk_test_YOUR_TEST_KEY
STRIPE_WEBHOOK_SECRET=whsec_YOUR_TEST_WEBHOOK_SECRET
STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_TEST_KEY

# Pinecone (Vector Database)
PINECONE_API_KEY=YOUR_PINECONE_API_KEY
PINECONE_ENVIRONMENT=YOUR_PINECONE_ENVIRONMENT
PINECONE_INDEX=callsaver-staging

# Supabase (Auth)
SUPABASE_URL=YOUR_SUPABASE_URL
SUPABASE_KEY=YOUR_SUPABASE_KEY
SUPABASE_JWT_SECRET=YOUR_SUPABASE_JWT_SECRET

# Logging
LOG_LEVEL=info

# Feature Flags
FEATURE_AI_ASSISTANT=true
FEATURE_ANALYTICS=true
FEATURE_ADVANCED_AUTOMATION=true
```

#### Production (.env.production)

```env
# Server Configuration
PORT=3000
NODE_ENV=production
API_URL=https://api.callsaver.app/api
FRONTEND_URL=https://callsaver.app

# Database Configuration - Use Railway provisioned values
DATABASE_URL=${DATABASE_URL_FROM_RAILWAY}
DIRECT_URL=${DIRECT_URL_FROM_RAILWAY}
MONGODB_URI=${MONGODB_URI_FROM_ATLAS}

# Redis Configuration
REDIS_URL=${REDIS_URL_FROM_RAILWAY}

# Authentication - Use strong secrets for production
JWT_SECRET=${GENERATE_STRONG_SECRET}
JWT_REFRESH_SECRET=${GENERATE_STRONG_SECRET}
JWT_EXPIRY=24h
JWT_REFRESH_EXPIRY=7d

# Twilio (Production Account)
TWILIO_ACCOUNT_SID=AC_YOUR_PRODUCTION_ACCOUNT_SID
TWILIO_AUTH_TOKEN=YOUR_PRODUCTION_AUTH_TOKEN
TWILIO_PHONE_NUMBER=+***********
TWILIO_WEBHOOK_BASE_URL=https://api.callsaver.app/api/webhooks/twilio

# OpenAI
OPENAI_API_KEY=YOUR_OPENAI_API_KEY
OPENAI_ORGANIZATION=YOUR_OPENAI_ORG_ID

# Stripe
STRIPE_SECRET_KEY=sk_live_YOUR_LIVE_KEY
STRIPE_WEBHOOK_SECRET=whsec_YOUR_LIVE_WEBHOOK_SECRET
STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_LIVE_KEY

# Pinecone (Vector Database)
PINECONE_API_KEY=YOUR_PINECONE_API_KEY
PINECONE_ENVIRONMENT=YOUR_PINECONE_ENVIRONMENT
PINECONE_INDEX=callsaver-production

# Supabase (Auth)
SUPABASE_URL=YOUR_SUPABASE_URL
SUPABASE_KEY=YOUR_SUPABASE_KEY
SUPABASE_JWT_SECRET=YOUR_SUPABASE_JWT_SECRET

# Logging
LOG_LEVEL=warn

# Feature Flags
FEATURE_AI_ASSISTANT=true
FEATURE_ANALYTICS=true
FEATURE_ADVANCED_AUTOMATION=true
```

### Frontend Environment Variables

Create the following `.env` files in the `front/mainpage` directory:

#### Development (.env.local)

```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000/api
NEXT_PUBLIC_WEBSOCKET_URL=ws://localhost:3000

# Authentication
NEXT_PUBLIC_SUPABASE_URL=YOUR_SUPABASE_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY
NEXT_AUTH_SECRET=YOUR_NEXTAUTH_SECRET

# Analytics
NEXT_PUBLIC_ANALYTICS_ID=

# Feature Flags
NEXT_PUBLIC_FEATURE_AI_ASSISTANT=true
NEXT_PUBLIC_FEATURE_ANALYTICS=true
NEXT_PUBLIC_FEATURE_ADVANCED_AUTOMATION=true

# External Services
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_TEST_KEY
```

#### Staging (.env.staging)

```env
# API Configuration
NEXT_PUBLIC_API_URL=https://staging-api.callsaver.app/api
NEXT_PUBLIC_WEBSOCKET_URL=wss://staging-api.callsaver.app

# Authentication
NEXT_PUBLIC_SUPABASE_URL=YOUR_SUPABASE_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY
NEXT_AUTH_SECRET=YOUR_NEXTAUTH_SECRET

# Analytics
NEXT_PUBLIC_ANALYTICS_ID=YOUR_ANALYTICS_ID

# Feature Flags
NEXT_PUBLIC_FEATURE_AI_ASSISTANT=true
NEXT_PUBLIC_FEATURE_ANALYTICS=true
NEXT_PUBLIC_FEATURE_ADVANCED_AUTOMATION=true

# External Services
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_TEST_KEY
```

#### Production (.env.production)

```env
# API Configuration
NEXT_PUBLIC_API_URL=https://api.callsaver.app/api
NEXT_PUBLIC_WEBSOCKET_URL=wss://api.callsaver.app

# Authentication
NEXT_PUBLIC_SUPABASE_URL=YOUR_SUPABASE_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY=YOUR_SUPABASE_ANON_KEY
NEXT_AUTH_SECRET=YOUR_NEXTAUTH_SECRET

# Analytics
NEXT_PUBLIC_ANALYTICS_ID=YOUR_ANALYTICS_ID

# Feature Flags
NEXT_PUBLIC_FEATURE_AI_ASSISTANT=true
NEXT_PUBLIC_FEATURE_ANALYTICS=true
NEXT_PUBLIC_FEATURE_ADVANCED_AUTOMATION=true

# External Services
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_YOUR_LIVE_KEY
```

## Local Development Setup

### Prerequisites

- Node.js 18+
- PostgreSQL 14+
- MongoDB 5+
- Redis 6+
- Git

### Backend Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/your-org/callsaver.app.git
   cd callsaver.app/back/backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your local configuration
   ```

4. Set up the database:
   ```bash
   # Create PostgreSQL database
   createdb callsaver_dev
   
   # Run migrations
   npx prisma migrate dev
   
   # Generate Prisma client
   npx prisma generate
   ```

5. Start the development server:
   ```bash
   npm run dev
   ```

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd ../../front/mainpage
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

5. The frontend will be available at http://localhost:3001

## External Service Setup

### Twilio Setup

1. Create a Twilio account at https://www.twilio.com
2. Purchase a phone number with voice and SMS capabilities
3. Configure webhook URLs for voice and messaging:
   - Voice: `https://your-api-url/api/webhooks/twilio/voice`
   - Messaging: `https://your-api-url/api/webhooks/twilio/message`
4. Copy the Account SID, Auth Token, and phone number to your environment variables

### OpenAI Setup

1. Create an OpenAI account at https://platform.openai.com
2. Create an API key
3. Add your API key to the environment variables

### Stripe Setup

1. Create a Stripe account at https://stripe.com
2. Get your API keys (test for development/staging, live for production)
3. Configure webhook endpoints:
   - Webhook URL: `https://your-api-url/api/webhooks/stripe`
   - Events to listen for:
     - `payment_intent.succeeded`
     - `payment_intent.payment_failed`
     - `customer.subscription.created`
     - `customer.subscription.updated`
     - `customer.subscription.deleted`
4. Get the webhook signing secret
5. Add all Stripe credentials to environment variables

### Supabase Setup

1. Create a Supabase project at https://supabase.com
2. Set up Google OAuth if needed (follow the steps in SUPABASE_GOOGLE_OAUTH_SETUP.md)
3. Configure the auth settings:
   - Site URL: Your frontend URL
   - Redirect URLs: Include development, staging, and production URLs
4. Get the API URL and anon key
5. Add Supabase credentials to environment variables

### Pinecone Setup

1. Create a Pinecone account at https://www.pinecone.io
2. Create an index with 1536 dimensions (for OpenAI embeddings)
3. Get your API key and environment
4. Add Pinecone credentials to environment variables

## Database Migration Handling

### Development Migrations

Create and test migrations in development using Prisma:

```bash
# Create a new migration
npx prisma migrate dev --name descriptive_name

# Reset development database (caution: deletes all data)
npx prisma migrate reset
```

### Production Migrations

For production deployments, use Prisma Deploy to apply migrations safely:

```bash
# Deploy migrations without data loss
npx prisma migrate deploy
```

Include this command in your CI/CD pipeline before deploying the backend application.

## Environment Variable Management in CI/CD

### GitHub Actions

Store sensitive environment variables as GitHub Secrets:

1. Go to your GitHub repository settings
2. Navigate to Secrets and Variables > Actions
3. Add the required secrets for each environment:
   - `RAILWAY_TOKEN`
   - `VERCEL_TOKEN`
   - `VERCEL_ORG_ID`
   - `VERCEL_PROJECT_ID`
   - `DATABASE_URL`
   - `MONGODB_URI`
   - `REDIS_URL`
   - `JWT_SECRET`
   - `TWILIO_ACCOUNT_SID`
   - `TWILIO_AUTH_TOKEN`
   - etc.

### Railway Configuration

1. Set up environment variables in the Railway project settings
2. Group variables by environment (development, staging, production)
3. Use variable references where appropriate
4. Ensure database connection strings are properly configured

### Vercel Configuration

1. Configure environment variables in the Vercel project settings
2. Set up separate environment variable sets for:
   - Development (Preview Deployments)
   - Staging
   - Production
3. Use Vercel's integration with GitHub to automate deployments

## Security Considerations

1. Never commit environment files with real credentials to version control
2. Rotate secrets regularly, especially for production
3. Use different credentials for each environment
4. Limit access to production credentials
5. Encrypt sensitive values at rest

## Troubleshooting

### Common Issues

1. **Database connection errors**:
   - Verify connection strings are correct
   - Check network access permissions
   - Ensure database server is running

2. **API connectivity issues**:
   - Verify the API URL is correct
   - Check CORS settings
   - Ensure the backend is running

3. **Authentication problems**:
   - Verify JWT secrets are configured
   - Check Supabase settings
   - Clear browser storage and try again

### Getting Help

If you encounter issues that you cannot resolve, contact:

- For development environment issues: Slack #dev-support channel
- For staging environment issues: Slack #ops-staging channel
- For production environment issues: Slack #ops-production channel

## Conclusion

This guide provides the necessary information to set up and configure the CallSaver.app environments. Follow these instructions carefully to ensure a smooth deployment process. Keep this document updated as the application evolves and new requirements emerge.
