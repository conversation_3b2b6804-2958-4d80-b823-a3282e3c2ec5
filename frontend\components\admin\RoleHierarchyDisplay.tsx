'use client';

import React from 'react';
import { Role } from '../../hooks/useRoleManagement';

interface RoleHierarchyDisplayProps {
  role: Role | null;
  allRoles: Role[];
}

export default function RoleHierarchyDisplay({
  role,
  allRoles,
}: RoleHierarchyDisplayProps) {
  if (!role) return null;

  // Get parent role
  const parentRole = role.parentId ? allRoles.find(r => r.id === role.parentId) : null;

  // Get child roles
  const childRoles = allRoles.filter(r => r.parentId === role.id);

  // Get inherited permissions
  const getInheritedPermissions = (): string[] => {
    if (!parentRole) return [];

    // Get parent's permissions
    const parentPermissions = [...parentRole.permissions];

    // Get parent's parent permissions recursively
    const grandParent = parentRole.parentId ? allRoles.find(r => r.id === parentRole.parentId) : null;
    if (grandParent) {
      // Create a recursive function to get all ancestor permissions
      const getAncestorPermissions = (roleId: string): string[] => {
        const role = allRoles.find(r => r.id === roleId);
        if (!role) return [];

        const permissions = [...role.permissions];

        if (role.parentId) {
          permissions.push(...getAncestorPermissions(role.parentId));
        }

        return permissions;
      };

      // Add all ancestor permissions
      if (parentRole.parentId) {
        parentPermissions.push(...getAncestorPermissions(parentRole.parentId));
      }
    }

    // Return unique permissions
    return [...new Set(parentPermissions)];
  };

  // Get all permissions (direct + inherited)
  const allPermissions = [...role.permissions, ...getInheritedPermissions()];

  return (
    <div className="space-y-4">
      <div className="border rounded-md p-4 bg-gray-50">
        <h4 className="font-medium mb-2">Role Hierarchy</h4>
        <div className="space-y-2">
          {parentRole && (
            <div>
              <div className="text-sm text-gray-500">Parent Role:</div>
              <div className="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 text-gray-400 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 10l7-7m0 0l7 7m-7-7v18"
                  />
                </svg>
                <span className="font-medium">{parentRole.name}</span>
              </div>
            </div>
          )}

          <div>
            <div className="text-sm text-gray-500">Current Role:</div>
            <div className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 text-blue-500 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                />
              </svg>
              <span className="font-medium">{role.name}</span>
            </div>
          </div>

          {childRoles.length > 0 && (
            <div>
              <div className="text-sm text-gray-500">Child Roles:</div>
              <div className="space-y-1">
                {childRoles.map(childRole => (
                  <div key={childRole.id} className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 text-gray-400 mr-1"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 14l-7 7m0 0l-7-7m7 7V3"
                      />
                    </svg>
                    <span>{childRole.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="border rounded-md p-4">
        <h4 className="font-medium mb-2">Permissions</h4>
        <div className="space-y-2">
          <div>
            <div className="text-sm text-gray-500">Direct Permissions: {role.permissions.length}</div>
            {role.permissions.length > 0 ? (
              <div className="mt-1 flex flex-wrap gap-1">
                {role.permissions.map(permission => (
                  <span
                    key={permission}
                    className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {permission}
                  </span>
                ))}
              </div>
            ) : (
              <div className="text-sm text-gray-400 italic">No direct permissions</div>
            )}
          </div>

          {parentRole && (
            <div>
              <div className="text-sm text-gray-500">Inherited Permissions: {getInheritedPermissions().length}</div>
              {getInheritedPermissions().length > 0 ? (
                <div className="mt-1 flex flex-wrap gap-1">
                  {getInheritedPermissions().map(permission => (
                    <span
                      key={permission}
                      className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                    >
                      {permission}
                    </span>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-gray-400 italic">No inherited permissions</div>
              )}
            </div>
          )}

          <div>
            <div className="text-sm text-gray-500">Total Permissions: {allPermissions.length}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
