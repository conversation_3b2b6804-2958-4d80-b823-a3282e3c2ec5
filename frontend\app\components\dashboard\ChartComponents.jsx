"use client";

import React, { useState, useEffect } from 'react';
import { 
  <PERSON>C<PERSON>, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  ResponsiveContainer,
  LineChart,
  Line,
  Tooltip,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Legend
} from 'recharts';

// Sample data for charts
const lineData = [
  { name: 'Mon', value: 420 },
  { name: 'Tu<PERSON>', value: 380 },
  { name: 'Wed', value: 450 },
  { name: 'Thu', value: 520 },
  { name: 'Fri', value: 580 },
  { name: 'Sat', value: 480 },
  { name: 'Sun', value: 400 }
];

const barData = [
  { name: '9AM', value: 120 },
  { name: '10AM', value: 180 },
  { name: '11AM', value: 250 },
  { name: '12PM', value: 320 },
  { name: '1PM', value: 290 },
  { name: '2PM', value: 230 },
  { name: '3PM', value: 210 },
  { name: '4PM', value: 180 }
];

const donutData = [
  { name: 'Automated', value: 42 },
  { name: 'Knowledge Base', value: 28 },
  { name: 'Self-Serve', value: 18 },
  { name: 'Agent Assist', value: 12 }
];

// Custom colors for the charts
const COLORS = {
  blue: ['#3b82f6', '#60a5fa', '#93c5fd', '#bfdbfe'],
  purple: ['#8b5cf6', '#a78bfa', '#c4b5fd', '#ddd6fe'],
  green: ['#10b981', '#34d399', '#6ee7b7', '#a7f3d0'],
  indigo: ['#6366f1', '#818cf8', '#a5b4fc', '#c7d2fe'],
  yellow: ['#facc15', '#fcd34d', '#fde68a', '#fef3c7']
};

// Custom tooltip component for a more futuristic look
const CustomTooltip = ({ active, payload, label, valuePrefix = '', valueSuffix = '' }) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-3 backdrop-blur-sm">
        <p className="text-gray-400 text-xs mb-1">{label}</p>
        <p className="text-white font-medium">
          {valuePrefix}{payload[0].value}{valueSuffix}
        </p>
      </div>
    );
  }
  return null;
};

// Hook to track window size
const useWindowSize = () => {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return windowSize;
};

// Line Chart Component with responsive design
export function LineChartComponent({ data = [], color = '#3b82f6', height = 300 }) {
  const { width } = useWindowSize();
  const isMobile = width < 768;
  
  // If no data is provided, use sample data
  const chartData = data.length > 0 ? data : [
    { name: 'Jan', value: 65 },
    { name: 'Feb', value: 59 },
    { name: 'Mar', value: 80 },
    { name: 'Apr', value: 81 },
    { name: 'May', value: 56 },
    { name: 'Jun', value: 55 },
    { name: 'Jul', value: 40 }
  ];

  // For mobile, we might want to reduce the data points
  const displayData = isMobile && chartData.length > 5 
    ? chartData.filter((_, i) => i % 2 === 0 || i === chartData.length - 1)
    : chartData;

  return (
    <div className="chart-container w-full h-full">
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={displayData} margin={{ top: 10, right: 10, left: isMobile ? -30 : -20, bottom: 0 }}>
          <defs>
            <linearGradient id="lineGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor={color} stopOpacity={0.2} />
              <stop offset="100%" stopColor={color} stopOpacity={0} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#374151" opacity={0.3} />
          <XAxis 
            dataKey="name" 
            axisLine={false} 
            tickLine={false} 
            tick={{ fill: '#9ca3af', fontSize: isMobile ? 8 : 10 }}
            tickMargin={5}
          />
          <YAxis 
            axisLine={false} 
            tickLine={false} 
            tick={{ fill: '#9ca3af', fontSize: isMobile ? 8 : 10 }}
            tickMargin={5}
            width={30}
          />
          <Tooltip content={<CustomTooltip />} />
          <Area 
            type="monotone" 
            dataKey="value" 
            stroke={color}
            fill="url(#lineGradient)" 
            strokeWidth={2}
            activeDot={{ r: 6, fill: color, stroke: '#1f2937', strokeWidth: 2 }}
            animationDuration={1500}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

// Bar Chart Component with responsive design
export function BarChartComponent({ data = [], colors = ['#3b82f6', '#8b5cf6'], height = 300 }) {
  const { width } = useWindowSize();
  const isMobile = width < 768;
  
  // If no data is provided, use sample data
  const chartData = data.length > 0 ? data : [
    { name: 'Mon', value: 20 },
    { name: 'Tue', value: 35 },
    { name: 'Wed', value: 28 },
    { name: 'Thu', value: 42 },
    { name: 'Fri', value: 30 },
    { name: 'Sat', value: 15 },
    { name: 'Sun', value: 22 }
  ];

  // For mobile, we might want to reduce the data points
  const displayData = isMobile && chartData.length > 5 
    ? chartData.filter((_, i) => i % 2 === 0 || i === chartData.length - 1)
    : chartData;

  return (
    <div className="chart-container w-full h-full">
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={displayData} margin={{ top: 10, right: 10, left: isMobile ? -30 : -20, bottom: 0 }}>
          <defs>
            <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor={colors[0]} stopOpacity={0.8} />
              <stop offset="100%" stopColor={colors[1]} stopOpacity={0.4} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#374151" opacity={0.3} />
          <XAxis 
            dataKey="name" 
            axisLine={false} 
            tickLine={false} 
            tick={{ fill: '#9ca3af', fontSize: isMobile ? 8 : 10 }}
            tickMargin={5}
          />
          <YAxis 
            axisLine={false} 
            tickLine={false} 
            tick={{ fill: '#9ca3af', fontSize: isMobile ? 8 : 10 }}
            tickMargin={5}
            width={30}
            tickFormatter={(value) => value}
          />
          <Tooltip content={<CustomTooltip />} cursor={{ fill: 'rgba(255, 255, 255, 0.05)' }} />
          <Bar 
            dataKey="value" 
            fill="url(#barGradient)" 
            radius={[4, 4, 0, 0]} 
            barSize={isMobile ? 12 : 20}
            animationDuration={1500}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}

// Donut Chart Component with responsive design
export function DonutChartComponent({ data = [], height = 300 }) {
  const [colorizedData, setColorizedData] = useState(data);

  // Generate random colors on client-side only
  useEffect(() => {
    if (data && data.length > 0) {
      const newColorizedData = data.map(entry => ({
        ...entry,
        color: entry.color || `#${Math.floor(Math.random()*16777215).toString(16)}`
      }));
      setColorizedData(newColorizedData);
    }
  }, [data]);

  return (
    <div className="chart-container w-full">
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={colorizedData}
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={80}
            fill="#8884d8"
            paddingAngle={5}
            dataKey="value"
            label
          >
            {colorizedData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip 
            content={({ payload }) => 
              payload.length ? (
                <div className="bg-gray-800 border border-purple-700 p-2 text-xs rounded shadow-lg">
                  <p className="font-medium text-white">{payload[0].name}</p>
                  <p className="text-purple-300">{`Calls: ${payload[0].value}`}</p>
                  <p className="text-purple-300">{`Percentage: ${((payload[0].value / colorizedData.reduce((sum, item) => sum + item.value, 0)) * 100).toFixed(1)}%`}</p>
                </div>
              ) : null
            }
          />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}

// Simplified pie chart for mobile
export function PieChartComponent({ data = [], height = 50, color = "purple" }) {
  const { width } = useWindowSize();
  const isMobile = width < 768;
  
  // Get color scheme based on color prop
  const getColors = (colorName) => {
    const colorMap = {
      blue: ['#60a5fa', '#3b82f6', '#2563eb', '#1d4ed8'],
      purple: ['#a78bfa', '#8b5cf6', '#7c3aed', '#6d28d9'],
      green: ['#4ade80', '#22c55e', '#16a34a', '#15803d'],
      indigo: ['#818cf8', '#6366f1', '#4f46e5', '#4338ca'],
      pink: ['#f472b6', '#ec4899', '#db2777', '#be185d'],
      red: ['#f87171', '#ef4444', '#dc2626', '#b91c1c'],
      yellow: ['#facc15', '#eab308', '#ca8a04', '#a16207']
    };
    
    return colorMap[colorName] || colorMap.purple;
  };
  
  // Mock data if none provided
  const chartData = data.length > 0 ? data : [
    { name: 'Cat A', value: 35 },
    { name: 'Cat B', value: 25 },
    { name: 'Cat C', value: 20 },
    { name: 'Cat D', value: 15 }
  ];
  
  const colors = getColors(color);
  
  // Simplify data if too many entries
  const displayData = chartData.length > 4 ? chartData.slice(0, 4) : chartData;
  
  return (
    <div className="chart-container w-full">
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Tooltip 
            content={({ payload }) => 
              payload.length ? (
                <div className="bg-gray-800 border border-gray-700 p-1 text-xs rounded shadow-lg">
                  <p className="text-white">{`${payload[0].name}: ${payload[0].value}`}</p>
                </div>
              ) : null
            }
          />
          <Pie
            data={displayData}
            cx="50%"
            cy="50%"
            innerRadius={0}
            outerRadius={25}
            paddingAngle={1}
            dataKey="value"
          >
            {displayData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={colors[index % colors.length]} 
              />
            ))}
          </Pie>
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}

// Area chart with better mobile support
export function AreaChartComponent({ data = [], colors = ['#3b82f6', '#8b5cf6'], height = 300 }) {
  const { width } = useWindowSize();
  const isMobile = width < 768;
  
  // If no data is provided, use sample data
  const chartData = data.length > 0 ? data : [
    { name: 'Jan', value: 40 },
    { name: 'Feb', value: 55 },
    { name: 'Mar', value: 45 },
    { name: 'Apr', value: 60 },
    { name: 'May', value: 70 },
    { name: 'Jun', value: 65 },
    { name: 'Jul', value: 80 }
  ];

  // For mobile, we might want to reduce the data points
  const displayData = isMobile && chartData.length > 5 
    ? chartData.filter((_, i) => i % 2 === 0 || i === chartData.length - 1)
    : chartData;

  return (
    <div className="chart-container w-full h-full">
      <ResponsiveContainer width="100%" height={height}>
        <AreaChart data={displayData} margin={{ top: 10, right: 10, left: isMobile ? -30 : -20, bottom: 0 }}>
          <defs>
            <linearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor={colors[0]} stopOpacity={0.5} />
              <stop offset="100%" stopColor={colors[1]} stopOpacity={0} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#374151" opacity={0.3} />
          <XAxis 
            dataKey="name" 
            axisLine={false} 
            tickLine={false} 
            tick={{ fill: '#9ca3af', fontSize: isMobile ? 8 : 10 }}
            tickMargin={5}
          />
          <YAxis 
            axisLine={false} 
            tickLine={false} 
            tick={{ fill: '#9ca3af', fontSize: isMobile ? 8 : 10 }}
            tickMargin={5}
            width={30}
          />
          <Tooltip content={<CustomTooltip valueSuffix="%" />} />
          <Area 
            type="monotone" 
            dataKey="value" 
            stroke={colors[0]}
            strokeWidth={2}
            fill="url(#areaGradient)" 
            activeDot={{ r: 6, fill: colors[0], stroke: '#1f2937', strokeWidth: 2 }}
            animationDuration={1500}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
}

// Multi-line chart with better mobile support
export function MultiLineChartComponent({ data = [], height = 120, showGrid = false, colors = ["purple", "blue"] }) {
  const { width } = useWindowSize();
  const isMobile = width < 768;
  
  // Get colors based on color props
  const getColor = (colorName) => {
    const colorMap = {
      blue: '#3b82f6',
      purple: '#8b5cf6',
      green: '#22c55e',
      indigo: '#6366f1',
      pink: '#ec4899',
      red: '#ef4444',
      yellow: '#eab308'
    };
    
    return colorMap[colorName] || colorMap.purple;
  };
  
  const primaryColor = getColor(colors[0]);
  const secondaryColor = getColor(colors[1]);
  
  // If no data is provided, use sample data
  const chartData = data.length > 0 ? data : [
    { day: 'Mon', active: 300, new: 100 },
    { day: 'Tue', active: 320, new: 120 },
    { day: 'Wed', active: 310, new: 90 },
    { day: 'Thu', active: 340, new: 140 },
    { day: 'Fri', active: 360, new: 160 },
    { day: 'Sat', active: 330, new: 110 },
    { day: 'Sun', active: 310, new: 90 }
  ];
  
  // Simplify data for mobile
  const displayData = isMobile && chartData.length > 5 
    ? chartData.filter((_, i) => i % 2 === 0 || i === chartData.length - 1)
    : chartData;
  
  return (
    <div className="chart-container w-full">
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={displayData} margin={{ top: 5, right: 5, left: isMobile ? -10 : 0, bottom: 5 }}>
          {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#374151" vertical={false} opacity={0.3} />}
          <XAxis 
            dataKey="day" 
            axisLine={false}
            tickLine={false}
            tick={{ fill: '#9ca3af', fontSize: isMobile ? 8 : 10 }}
            tickMargin={5}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fill: '#9ca3af', fontSize: isMobile ? 8 : 10 }}
            width={25}
            tickMargin={5}
          />
          <Tooltip 
            content={({ payload, label }) => 
              payload.length ? (
                <div className="bg-gray-800 border border-gray-700 p-1 text-xs rounded shadow-lg">
                  <p className="text-white">{label}</p>
                  {payload.map((entry, index) => (
                    <p key={index} style={{ color: entry.color }}>{`${entry.name}: ${entry.value}`}</p>
                  ))}
                </div>
              ) : null
            }
          />
          <Line 
            type="monotone" 
            dataKey="active" 
            stroke={primaryColor} 
            strokeWidth={1.5}
            dot={false}
            activeDot={{ r: 3 }}
            name="Active"
          />
          <Line 
            type="monotone" 
            dataKey="new" 
            stroke={secondaryColor} 
            strokeWidth={1.5}
            dot={false}
            activeDot={{ r: 3 }}
            name="New"
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

// Large donut chart with better responsiveness
export function LargeDonutChartComponent({ data = [], height = 200, labelLine = false }) {
  const { width } = useWindowSize();
  const isMobile = width < 768;
  
  // If no data is provided, use sample data
  const chartData = data.length > 0 ? data : [
    { name: 'Success', value: 45, color: '#10b981' },
    { name: 'Transferred', value: 25, color: '#6366f1' },
    { name: 'Follow-up', value: 20, color: '#8b5cf6' },
    { name: 'Unresolved', value: 10, color: '#ef4444' }
  ];
  
  // Adjust dimensions for mobile
  const innerRadius = isMobile ? 25 : 30;
  const outerRadius = isMobile ? 60 : 70;
  
  return (
    <div className="chart-container w-full">
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={labelLine}
            label={({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }) => {
              if (isMobile && chartData.length > 3) return null;
              
              const RADIAN = Math.PI / 180;
              const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
              const x = cx + radius * Math.cos(-midAngle * RADIAN);
              const y = cy + radius * Math.sin(-midAngle * RADIAN);
              
              return (
                <text 
                  x={x} 
                  y={y} 
                  fill="#ffffff" 
                  textAnchor={x > cx ? 'start' : 'end'} 
                  dominantBaseline="central"
                  className="text-xs font-medium"
                >
                  {`${(percent * 100).toFixed(0)}%`}
                </text>
              );
            }}
            outerRadius={outerRadius}
            innerRadius={innerRadius}
            dataKey="value"
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color || `#${Math.floor(Math.random()*16777215).toString(16)}`} />
            ))}
          </Pie>
          <Tooltip 
            content={({ payload }) => 
              payload.length ? (
                <div className="bg-gray-800 border border-purple-700 p-2 text-xs rounded shadow-lg">
                  <p className="font-medium text-white">{payload[0].name}</p>
                  <p className="text-purple-300">{`Calls: ${payload[0].value}`}</p>
                  <p className="text-purple-300">{`Percentage: ${((payload[0].value / chartData.reduce((sum, item) => sum + item.value, 0)) * 100).toFixed(1)}%`}</p>
                </div>
              ) : null
            }
          />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
} 