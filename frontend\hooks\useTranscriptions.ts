import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';

// Types
export interface Transcription {
  id: string;
  callSid: string;
  recordingUrl: string | null;
  transcriptionText: string;
  sentiment: {
    overall: string;
    overallScore: number;
    segments?: Array<{
      text: string;
      sentiment: string;
      score: number;
    }>;
    confidence: number;
  };
  keywords: Array<{
    text: string;
    relevance: number;
  }>;
  entities: Array<{
    text: string;
    type: string;
    relevance: number;
  }>;
  speakerSegments?: Array<{
    text: string;
    speaker: string;
    confidence: number;
  }>;
  callDetails: {
    from: string;
    to: string;
    duration: number;
    timestamp: string;
  };
}

export interface TranscriptionFilters {
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  sentiment?: 'positive' | 'negative' | 'neutral';
  minDuration?: number;
  maxDuration?: number;
  page?: number;
  limit?: number;
}

// Hook for fetching transcriptions list
export const useTranscriptions = (filters: TranscriptionFilters = {}) => {
  return useQuery({
    queryKey: ['transcriptions', filters],
    queryFn: async () => {
      const params = new URLSearchParams();
      
      // Add filters to query params
      if (filters.search) params.append('search', filters.search);
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom);
      if (filters.dateTo) params.append('dateTo', filters.dateTo);
      if (filters.sentiment) params.append('sentiment', filters.sentiment);
      if (filters.minDuration) params.append('minDuration', filters.minDuration.toString());
      if (filters.maxDuration) params.append('maxDuration', filters.maxDuration.toString());
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());
      
      const response = await axios.get(`/api/transcriptions?${params.toString()}`);
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for fetching a single transcription
export const useTranscriptionDetail = (id: string) => {
  return useQuery({
    queryKey: ['transcription', id],
    queryFn: async () => {
      const response = await axios.get(`/api/transcriptions/${id}`);
      return response.data as Transcription;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!id, // Only run if id is provided
  });
};

// Hook for searching transcriptions
export const useTranscriptionSearch = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ query, filters }: { query: string; filters?: TranscriptionFilters }) => {
      const response = await axios.post('/api/transcriptions/search', { query, filters });
      return response.data;
    },
    onSuccess: (data) => {
      // Optionally update cache with search results
      queryClient.setQueryData(['transcriptions', 'search'], data);
    },
  });
};

// Hook for deleting a transcription
export const useDeleteTranscription = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string) => {
      const response = await axios.delete(`/api/transcriptions/${id}`);
      return response.data;
    },
    onSuccess: () => {
      // Invalidate transcriptions queries to refetch data
      queryClient.invalidateQueries({ queryKey: ['transcriptions'] });
    },
  });
};

// Hook for sharing a transcription
export const useShareTranscription = () => {
  return useMutation({
    mutationFn: async ({ id, expiresIn }: { id: string; expiresIn?: number }) => {
      const response = await axios.post(`/api/transcriptions/${id}/share`, { expiresIn });
      return response.data;
    },
  });
};

// Hook for accessing a shared transcription
export const useSharedTranscription = (token: string) => {
  return useQuery({
    queryKey: ['shared-transcription', token],
    queryFn: async () => {
      const response = await axios.get(`/api/transcriptions/shared/${token}`);
      return response.data;
    },
    enabled: !!token, // Only run if token is provided
  });
};
