'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function RuleEditor({ ruleId, template }) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(ruleId ? true : false);
  const [isSaving, setIsSaving] = useState(false);
  
  // Form state
  const [ruleName, setRuleName] = useState('');
  const [triggerType, setTriggerType] = useState('call_keyword');
  const [triggerKeywords, setTriggerKeywords] = useState('');
  const [actionType, setActionType] = useState('calendar');
  const [actionDetails, setActionDetails] = useState('');
  
  // Available options for dropdowns
  const triggerTypes = [
    { id: 'call_keyword', name: 'Call contains keywords' },
    { id: 'missed_call', name: 'Call is missed' },
    { id: 'voicemail', name: 'Voicemail is received' },
    { id: 'new_lead', name: 'New lead is created' },
    { id: 'specific_number', name: 'Call from specific number' }
  ];
  
  const actionTypes = [
    { id: 'calendar', name: 'Create calendar event' },
    { id: 'sms', name: 'Send SMS message' },
    { id: 'email', name: 'Send email' },
    { id: 'crm', name: 'Update CRM record' },
    { id: 'notification', name: 'Send notification' }
  ];
  
  // Load rule data if editing
  useEffect(() => {
    if (ruleId) {
      // Simulate API call to fetch rule data
      const fetchRuleData = async () => {
        setIsLoading(true);
        
        // Simulate network request
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock data
        const mockRule = {
          id: ruleId,
          name: 'Schedule Appointment for New Leads',
          triggerType: 'call_keyword',
          triggerKeywords: 'appointment,schedule,meet',
          actionType: 'calendar',
          actionDetails: 'Create calendar event and send confirmation email'
        };
        
        // Set form data
        setRuleName(mockRule.name);
        setTriggerType(mockRule.triggerType);
        setTriggerKeywords(mockRule.triggerKeywords);
        setActionType(mockRule.actionType);
        setActionDetails(mockRule.actionDetails);
        
        setIsLoading(false);
      };
      
      fetchRuleData();
    } else if (template) {
      // Set template defaults based on template type
      if (template === 'appointment') {
        setRuleName('Appointment Scheduling');
        setTriggerType('call_keyword');
        setTriggerKeywords('appointment,schedule,meet');
        setActionType('calendar');
        setActionDetails('');
      } else if (template === 'sms') {
        setRuleName('SMS Follow-up');
        setTriggerType('missed_call');
        setTriggerKeywords('');
        setActionType('sms');
        setActionDetails('');
      } else if (template === 'email') {
        setRuleName('Email Notification');
        setTriggerType('voicemail');
        setTriggerKeywords('');
        setActionType('email');
        setActionDetails('');
      }
    }
  }, [ruleId, template]);
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);
    
    // Simulate saving the rule
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Redirect back to automation page with rules tab active
    router.push('/dashboard/automation?tab=rules');
  };
  
  // Handle cancel button
  const handleCancel = () => {
    router.push('/dashboard/automation?tab=rules');
  };
  
  if (isLoading) {
    return (
      <div className="bg-gray-900/70 backdrop-blur-lg rounded-2xl border border-gray-800 p-6 shadow-xl">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-700 rounded w-1/4"></div>
          <div className="h-4 bg-gray-700 rounded w-1/2"></div>
          <div className="space-y-2 pt-4">
            <div className="h-4 bg-gray-700 rounded"></div>
            <div className="h-10 bg-gray-700 rounded"></div>
          </div>
          <div className="space-y-2 pt-4">
            <div className="h-4 bg-gray-700 rounded"></div>
            <div className="h-32 bg-gray-700 rounded"></div>
          </div>
          <div className="space-y-2 pt-4">
            <div className="h-4 bg-gray-700 rounded"></div>
            <div className="h-32 bg-gray-700 rounded"></div>
          </div>
          <div className="flex justify-end space-x-3 pt-4">
            <div className="h-10 bg-gray-700 rounded w-24"></div>
            <div className="h-10 bg-gray-700 rounded w-24"></div>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">
            {ruleId ? 'Edit Automation Rule' : 'Create Automation Rule'}
          </h1>
          <p className="text-gray-400 mt-2">
            {ruleId 
              ? 'Modify your existing automation rule settings below.' 
              : 'Configure a new automation rule to handle calls and messages automatically.'}
          </p>
        </div>
        <button
          onClick={handleCancel}
          className="text-gray-400 hover:text-white"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-gray-900/70 backdrop-blur-lg rounded-2xl border border-gray-800 p-6 shadow-xl">
          <h2 className="text-xl font-bold text-white mb-6">Rule Details</h2>
          
          {/* Rule Name */}
          <div className="mb-6">
            <label htmlFor="ruleName" className="block text-sm font-medium text-gray-400 mb-2">
              Rule Name
            </label>
            <input
              type="text"
              id="ruleName"
              className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-purple-500 focus:border-purple-500"
              placeholder="Enter a name for this rule"
              value={ruleName}
              onChange={(e) => setRuleName(e.target.value)}
              required
            />
          </div>
          
          {/* Trigger Configuration */}
          <div className="border-t border-gray-700 pt-6 mb-6">
            <h3 className="text-lg font-medium text-white mb-4">Trigger</h3>
            <p className="text-sm text-gray-400 mb-4">Define when this automation should run.</p>
            
            <div className="mb-4">
              <label htmlFor="triggerType" className="block text-sm font-medium text-gray-400 mb-2">
                Trigger Type
              </label>
              <select
                id="triggerType"
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-purple-500 focus:border-purple-500"
                value={triggerType}
                onChange={(e) => setTriggerType(e.target.value)}
              >
                {triggerTypes.map(type => (
                  <option key={type.id} value={type.id}>{type.name}</option>
                ))}
              </select>
            </div>
            
            {triggerType === 'call_keyword' && (
              <div className="mb-4">
                <label htmlFor="triggerKeywords" className="block text-sm font-medium text-gray-400 mb-2">
                  Keywords (comma separated)
                </label>
                <textarea
                  id="triggerKeywords"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-purple-500 focus:border-purple-500"
                  placeholder="appointment, schedule, meeting"
                  rows="3"
                  value={triggerKeywords}
                  onChange={(e) => setTriggerKeywords(e.target.value)}
                ></textarea>
                <p className="text-xs text-gray-500 mt-1">
                  The automation will trigger when any of these keywords are detected in a call.
                </p>
              </div>
            )}
            
            {triggerType === 'specific_number' && (
              <div className="mb-4">
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-400 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="phoneNumber"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-purple-500 focus:border-purple-500"
                  placeholder="+****************"
                />
                <p className="text-xs text-gray-500 mt-1">
                  The automation will trigger for calls from this specific number.
                </p>
              </div>
            )}
          </div>
          
          {/* Action Configuration */}
          <div className="border-t border-gray-700 pt-6">
            <h3 className="text-lg font-medium text-white mb-4">Action</h3>
            <p className="text-sm text-gray-400 mb-4">Define what should happen when the trigger conditions are met.</p>
            
            <div className="mb-4">
              <label htmlFor="actionType" className="block text-sm font-medium text-gray-400 mb-2">
                Action Type
              </label>
              <select
                id="actionType"
                className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-purple-500 focus:border-purple-500"
                value={actionType}
                onChange={(e) => setActionType(e.target.value)}
              >
                {actionTypes.map(type => (
                  <option key={type.id} value={type.id}>{type.name}</option>
                ))}
              </select>
            </div>
            
            {actionType === 'calendar' && (
              <div className="space-y-4">
                <div>
                  <label htmlFor="calendarType" className="block text-sm font-medium text-gray-400 mb-2">
                    Calendar Service
                  </label>
                  <select
                    id="calendarType"
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="google">Google Calendar</option>
                    <option value="outlook">Outlook Calendar</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="eventDuration" className="block text-sm font-medium text-gray-400 mb-2">
                    Default Event Duration
                  </label>
                  <select
                    id="eventDuration"
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="30">30 minutes</option>
                    <option value="60">60 minutes</option>
                    <option value="90">90 minutes</option>
                  </select>
                </div>
                <div>
                  <label htmlFor="eventTitle" className="block text-sm font-medium text-gray-400 mb-2">
                    Event Title Template
                  </label>
                  <input
                    type="text"
                    id="eventTitle"
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-purple-500 focus:border-purple-500"
                    placeholder="Call with [caller_name]"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Use [caller_name], [caller_number], [date], etc. as variables.
                  </p>
                </div>
              </div>
            )}
            
            {actionType === 'sms' && (
              <div>
                <label htmlFor="smsTemplate" className="block text-sm font-medium text-gray-400 mb-2">
                  SMS Message Template
                </label>
                <textarea
                  id="smsTemplate"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Hi [caller_name], you recently called us. Would you like to schedule a callback at a convenient time?"
                  rows="4"
                  value={actionDetails}
                  onChange={(e) => setActionDetails(e.target.value)}
                ></textarea>
                <p className="text-xs text-gray-500 mt-1">
                  Use [caller_name], [caller_number], [date], etc. as variables.
                </p>
              </div>
            )}
            
            {actionType === 'email' && (
              <div className="space-y-4">
                <div>
                  <label htmlFor="emailSubject" className="block text-sm font-medium text-gray-400 mb-2">
                    Email Subject
                  </label>
                  <input
                    type="text"
                    id="emailSubject"
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-purple-500 focus:border-purple-500"
                    placeholder="Following up on your recent call"
                  />
                </div>
                <div>
                  <label htmlFor="emailTemplate" className="block text-sm font-medium text-gray-400 mb-2">
                    Email Template
                  </label>
                  <textarea
                    id="emailTemplate"
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-purple-500 focus:border-purple-500"
                    placeholder="Dear [caller_name],

Thank you for your call. We noticed you tried to reach us and we'd like to follow up.

Best regards,
The Team"
                    rows="6"
                    value={actionDetails}
                    onChange={(e) => setActionDetails(e.target.value)}
                  ></textarea>
                  <p className="text-xs text-gray-500 mt-1">
                    Use [caller_name], [caller_number], [date], etc. as variables.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Form Actions */}
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={handleCancel}
            className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center"
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </>
            ) : (
              'Save Rule'
            )}
          </button>
        </div>
      </form>
    </div>
  );
} 