"use client";

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';

const ScrollRevealWrapper = ({ children, delay = 0, duration = 0.6, yOffset = 30, once = true }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: once, margin: "-50px 0px -50px 0px" }); // Trigger slightly before fully in view

  const variants = {
    hidden: { opacity: 0, y: yOffset },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: duration,
        delay: delay,
        ease: "easeOut" // Use standard easeOut easing
      }
    }
  };

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={variants}
      style={{ willChange: 'transform, opacity' }} // Optimize animation performance
    >
      {children}
    </motion.div>
  );
};

export default ScrollRevealWrapper;
