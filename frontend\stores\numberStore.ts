import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

// Define a PhoneNumber type
export interface PhoneNumber {
  id: string;
  number: string;
  friendlyName?: string;
  type: 'twilio' | 'esim';
  status: 'active' | 'inactive' | 'pending';
  capabilities?: {
    voice: boolean;
    sms: boolean;
    mms: boolean;
  };
  createdAt: string;
  lastUsed?: string;
}

// Define the NumberState interface
interface NumberState {
  selectedNumber: PhoneNumber | null;
  cachedNumbers: PhoneNumber[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setSelectedNumber: (number: PhoneNumber | null) => void;
  setCachedNumbers: (numbers: PhoneNumber[]) => void;
  updateNumber: (updatedNumber: PhoneNumber) => void;
  addNumber: (number: PhoneNumber) => void;
  removeNumber: (numberId: string) => void;
  setLoading: (status: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

// Create the number store
export const useNumberStore = create<NumberState>()(
  persist(
    (set, get) => ({
      selectedNumber: null,
      cachedNumbers: [],
      isLoading: false,
      error: null,
      
      setSelectedNumber: (number) => {
        set({ selectedNumber: number });
      },
      
      setCachedNumbers: (numbers) => {
        set({ cachedNumbers: numbers });
      },
      
      updateNumber: (updatedNumber) => {
        const currentNumbers = [...get().cachedNumbers];
        const index = currentNumbers.findIndex(n => n.id === updatedNumber.id);
        
        if (index !== -1) {
          currentNumbers[index] = updatedNumber;
          set({ cachedNumbers: currentNumbers });
          
          // Also update selected number if it's the same one
          if (get().selectedNumber?.id === updatedNumber.id) {
            set({ selectedNumber: updatedNumber });
          }
        }
      },
      
      addNumber: (number) => {
        set((state) => ({ 
          cachedNumbers: [...state.cachedNumbers, number]
        }));
      },
      
      removeNumber: (numberId) => {
        set((state) => ({
          cachedNumbers: state.cachedNumbers.filter(n => n.id !== numberId),
          // If the selected number is being removed, clear it
          selectedNumber: state.selectedNumber?.id === numberId ? null : state.selectedNumber
        }));
      },
      
      setLoading: (status) => {
        set({ isLoading: status });
      },
      
      setError: (error) => {
        set({ error });
      },
      
      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'callsaver-numbers', // Name for the persisted state in storage
      storage: createJSONStorage(() => localStorage),
      // Only persist the selected number
      partialize: (state) => ({ 
        selectedNumber: state.selectedNumber
      }),
    }
  )
);

// Export selectors to prevent unnecessary re-renders
export const useSelectedNumber = () => useNumberStore((state) => state.selectedNumber);
export const useCachedNumbers = () => useNumberStore((state) => state.cachedNumbers);
export const useNumbersLoading = () => useNumberStore((state) => state.isLoading);
export const useNumbersError = () => useNumberStore((state) => state.error);
