'use client';

import React from 'react';
import { useFeatureFlag, FeatureFlagConfig } from '../../hooks/useFeatureFlag';

interface PermissionAwareFeatureProps extends Omit<FeatureFlagConfig, 'defaultEnabled'> {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  loading?: React.ReactNode;
  renderDisabled?: boolean;
  disabledClassName?: string;
}

/**
 * A component that conditionally renders features based on user permissions
 * 
 * @param name - Feature name
 * @param permission - Permission required to access this feature
 * @param anyPermission - Array of permissions, any of which will grant access
 * @param allPermissions - Array of permissions, all of which are required for access
 * @param resource - Resource to check access for
 * @param action - Action to check (default: 'use')
 * @param scope - Scope to check (default: 'any')
 * @param description - Description of the feature (for debugging)
 * @param children - Content to render if the feature is enabled
 * @param fallback - Optional content to render if the feature is disabled
 * @param loading - Optional content to render while permissions are being loaded
 * @param renderDisabled - Whether to render the children with a disabled style instead of the fallback
 * @param disabledClassName - CSS class to apply when renderDisabled is true and feature is disabled
 */
const PermissionAwareFeature: React.FC<PermissionAwareFeatureProps> = ({
  name,
  permission,
  anyPermission,
  allPermissions,
  resource,
  action = 'use',
  scope = 'any',
  description,
  children,
  fallback = null,
  loading = null,
  renderDisabled = false,
  disabledClassName = 'opacity-50 pointer-events-none',
}) => {
  const isEnabled = useFeatureFlag({
    name,
    permission,
    anyPermission,
    allPermissions,
    resource,
    action,
    scope,
    description,
    defaultEnabled: false,
  });

  // If the feature is enabled, render the children
  if (isEnabled) {
    return <>{children}</>;
  }

  // If renderDisabled is true, render the children with a disabled style
  if (renderDisabled) {
    return <div className={disabledClassName}>{children}</div>;
  }

  // Otherwise, render the fallback content
  return <>{fallback}</>;
};

export default PermissionAwareFeature;
