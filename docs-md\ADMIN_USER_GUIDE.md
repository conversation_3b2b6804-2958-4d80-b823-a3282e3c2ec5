---
title: CallSaver.app Administrator User Guide
description: Comprehensive guide for setting up and managing the CallSaver.app platform
date: 2025-04-29
status: Required
priority: High
---

# CallSaver.app Administrator User Guide

## Table of Contents
1. [Introduction](#introduction)
2. [Setting Up Your AI Assistant](#setting-up-your-ai-assistant)
3. [Purchasing a Phone Number](#purchasing-a-phone-number)
4. [Viewing Analytics](#viewing-analytics)
5. [Managing Calls and Messages](#managing-calls-and-messages)
6. [Troubleshooting](#troubleshooting)

## Introduction

CallSaver.app is a powerful multi-tenant platform that allows organizations to manage phone calls and SMS messages with AI-powered assistance. This guide provides step-by-step instructions for administrators on how to configure and use the platform effectively.

## Setting Up Your AI Assistant

The AI assistant is the core component that handles calls and messages on your behalf. Proper configuration ensures it represents your organization appropriately.

### Accessing AI Assistant Settings

1. Log in to your CallSaver.app dashboard
2. Navigate to **Phone Numbers** in the main menu
3. Select the phone number you want to configure
4. Click on the **AI Assistant** tab

### Selecting a Personality

The AI personality defines how your assistant communicates with callers and message senders:

1. In the AI Assistant section, locate the **Assistant Personality** card
2. Choose from the available personalities:
   - **Professional**: Formal and business-oriented communication style
   - **Friendly**: Warm and conversational tone
   - **Technical**: Detailed and precise responses
   - **Custom**: Create your own personality profile

### Uploading Knowledge Base Documents

The knowledge base provides information your AI assistant uses to answer questions:

1. In the **Knowledge Base** section, click **Add Document**
2. Upload files (PDF, DOCX, TXT) containing information about:
   - Company information
   - Product/service details
   - Pricing
   - Policies and procedures
   - FAQ
3. Each document will be processed and made available to your AI assistant
4. You can prioritize documents by dragging them to reorder the list

### Creating Custom Commands

Custom commands help your AI assistant handle specific requests:

1. In the **Custom Commands** section, click **Add Command**
2. Define a **Trigger Phrase** that will activate the command
3. Create a **Response Template** that will be used when the trigger is detected
4. (Optional) Add **Variables** for dynamic responses
5. Click **Save** to add the command

### Testing Your AI Assistant

Before deploying your configuration:

1. Switch to the **Test Call** tab to simulate phone conversations
2. Use the **Test SMS** tab to simulate text message exchanges
3. Try different scenarios to ensure your assistant responds appropriately
4. Make adjustments to the configuration as needed

## Purchasing a Phone Number

### Finding Available Numbers

1. Navigate to **Phone Numbers** > **Purchase Number** in the dashboard
2. Select your desired country from the dropdown menu
3. (Optional) Enter an area code to find numbers in a specific region
4. Click **Search** to view available numbers

### Selecting a Number

1. Browse the list of available numbers
2. View details such as location and capabilities
3. Click **Purchase** next to the number you want to acquire

### Configuring Your Number

After purchasing:

1. Navigate to the **Settings** tab for your new number
2. Configure the following options:
   - **Name**: A friendly name for the number
   - **AI Enabled**: Toggle whether the AI assistant should handle calls/messages
   - **Call Forwarding**: Set up forwarding to your personal or business number
   - **Recording**: Enable/disable call recording
   - **Transcription**: Enable/disable call transcription
3. Click **Save Changes** to apply your configuration

## Viewing Analytics

The analytics dashboard provides insights into call and message patterns:

### Accessing Analytics

1. Navigate to **Analytics** in the main menu
2. Use the time range selector to adjust the data period (7 days, 30 days, etc.)

### Understanding Call Analytics

The Call Analytics tab shows:
- Total calls received and made
- Total call duration
- Call direction breakdown (inbound vs. outbound)
- Missed call rate
- AI handoff rate (percentage of calls handled entirely by AI)
- Peak call hours visualization
- Call status breakdown (completed, missed, etc.)

### Understanding Message Analytics

The Message Analytics tab shows:
- Total messages received and sent
- Message direction breakdown (inbound vs. outbound)
- Message volume trends
- SMS response rate
- SMS response time

### Understanding AI Performance

The AI Performance tab shows:
- AI handled calls and messages count
- AI resolution rate
- Average handling time
- Top topics identified
- Sentiment analysis

### Exporting Data

1. Use the **Download** button to export analytics data
2. Choose your preferred format (CSV, Excel)
3. The exported file will contain detailed information for the selected time period

## Managing Calls and Messages

### Viewing Call History

1. Navigate to **Calls** in the main menu
2. Browse the list of all calls, sorted by date (newest first)
3. Use filters to narrow down by:
   - Direction (inbound/outbound)
   - Status (completed, missed, etc.)
   - Date range
   - AI handled status

### Accessing Call Details

1. Click on any call in the list to view details
2. Access the following information:
   - Call metadata (time, duration, phone numbers)
   - Call recording (if enabled)
   - Transcription (if enabled)
   - AI responses provided
   - Notes and tags

### Managing Messages

1. Navigate to **Messages** in the main menu
2. View conversations organized by contact
3. Click on a conversation to see the message history
4. Reply directly from the interface if needed

### Adding Notes and Tags

For both calls and messages:
1. Open the detailed view
2. Add notes in the **Notes** section
3. Create or select tags to categorize the interaction
4. Use these for searching and reporting later

## Troubleshooting

### Common Issues and Solutions

| Issue | Solution |
|-------|----------|
| AI not responding correctly | Check knowledge base documents for missing information |
| Calls not being forwarded | Verify forwarding number and settings |
| Poor transcription quality | Ensure call recording quality is set to high |
| Missing analytics data | Check date range selection and refresh the page |
| Number not receiving calls | Verify number status in Phone Numbers section |

### Getting Support

If you encounter issues not covered in this guide:

1. Check the **Help Center** accessible from the dashboard
2. Use the **Contact Support** button for direct assistance
3. Email <EMAIL> with details of your issue

---

This guide covers the essential aspects of administering CallSaver.app. For more detailed information on specific features, refer to the full documentation available in the Help Center.
