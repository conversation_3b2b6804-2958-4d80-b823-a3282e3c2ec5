'use client';

import { useEffect, useRef } from 'react';
import { format, parseISO } from 'date-fns';

interface ChartDataPoint {
  date: string;
  successCount: number;
  failureCount: number;
}

interface PerformanceChartProps {
  chartData: ChartDataPoint[];
}

export default function PerformanceChart({
  chartData,
}: PerformanceChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  useEffect(() => {
    if (!canvasRef.current || !chartData || chartData.length === 0) return;
    
    const ctx = canvasRef.current.getContext('2d');
    if (!ctx) return;
    
    // Clear canvas
    ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
    
    // Set dimensions
    const width = canvasRef.current.width;
    const height = canvasRef.current.height;
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;
    
    // Find max value for scaling
    const maxValue = Math.max(
      ...chartData.map(d => Math.max(d.successCount, d.failureCount)),
      5 // Minimum max value to avoid division by zero
    );
    
    // Draw axes
    ctx.beginPath();
    ctx.strokeStyle = '#CBD5E1'; // gray-300
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.stroke();
    
    // Draw grid lines
    const gridLines = 5;
    ctx.textAlign = 'right';
    ctx.textBaseline = 'middle';
    ctx.fillStyle = '#64748B'; // gray-500
    ctx.font = '10px sans-serif';
    
    for (let i = 0; i <= gridLines; i++) {
      const y = padding + (chartHeight / gridLines) * i;
      const value = maxValue - (maxValue / gridLines) * i;
      
      ctx.beginPath();
      ctx.strokeStyle = '#E2E8F0'; // gray-200
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
      
      ctx.fillText(Math.round(value).toString(), padding - 5, y);
    }
    
    // Draw x-axis labels
    ctx.textAlign = 'center';
    ctx.textBaseline = 'top';
    
    const barWidth = chartWidth / chartData.length;
    
    chartData.forEach((dataPoint, i) => {
      const x = padding + barWidth * i + barWidth / 2;
      const date = parseISO(dataPoint.date);
      const label = format(date, 'MMM d');
      
      ctx.fillText(label, x, height - padding + 5);
    });
    
    // Draw success bars
    chartData.forEach((dataPoint, i) => {
      const x = padding + barWidth * i + barWidth / 4;
      const barHeight = (dataPoint.successCount / maxValue) * chartHeight;
      const y = height - padding - barHeight;
      
      ctx.fillStyle = '#22C55E'; // green-500
      ctx.fillRect(x, y, barWidth / 3, barHeight);
    });
    
    // Draw failure bars
    chartData.forEach((dataPoint, i) => {
      const x = padding + barWidth * i + barWidth / 2 + barWidth / 4;
      const barHeight = (dataPoint.failureCount / maxValue) * chartHeight;
      const y = height - padding - barHeight;
      
      ctx.fillStyle = '#EF4444'; // red-500
      ctx.fillRect(x, y, barWidth / 3, barHeight);
    });
    
    // Draw legend
    const legendX = width - padding - 100;
    const legendY = padding + 10;
    
    // Success legend
    ctx.fillStyle = '#22C55E'; // green-500
    ctx.fillRect(legendX, legendY, 15, 15);
    ctx.fillStyle = '#64748B'; // gray-500
    ctx.textAlign = 'left';
    ctx.textBaseline = 'middle';
    ctx.fillText('Success', legendX + 20, legendY + 7.5);
    
    // Failure legend
    ctx.fillStyle = '#EF4444'; // red-500
    ctx.fillRect(legendX, legendY + 25, 15, 15);
    ctx.fillStyle = '#64748B'; // gray-500
    ctx.fillText('Failure', legendX + 20, legendY + 32.5);
    
  }, [chartData]);

  return (
    <div className="w-full h-64">
      <canvas
        ref={canvasRef}
        width={800}
        height={300}
        className="w-full h-full"
      />
    </div>
  );
}
