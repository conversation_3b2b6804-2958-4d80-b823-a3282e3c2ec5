"use client";

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { conversationData } from '../data/conversationData';
import IMessageConversationCarousel from './iMessage/ConversationCarousel';

export default function StaticConversationCarousel() {
  // Use the new iPhone-style conversation carousel
  // Pass the conversationData to the component
  return <IMessageConversationCarousel conversationData={conversationData} />;
} 