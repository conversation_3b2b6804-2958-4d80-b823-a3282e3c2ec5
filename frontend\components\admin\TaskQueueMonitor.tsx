import React, { useState, useEffect } from 'react';
import axios from 'axios';

interface QueueStats {
  name: string;
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  deadLetter: number;
  isPaused: boolean;
}

interface RedisHealth {
  isHealthy: boolean;
  status: string;
  metrics: Record<string, any>;
  responseTime: number;
  circuitBreaker: {
    state: string;
  };
}

interface TaskQueueHealth {
  isHealthy: boolean;
  status: string;
  redisHealth: RedisHealth;
  queues: Array<{
    name: string;
    stats: Record<string, number>;
  }>;
}

interface QueueMonitorData {
  queues: QueueStats[];
  redis: RedisHealth;
  health: TaskQueueHealth;
}

const TaskQueueMonitor: React.FC = () => {
  const [data, setData] = useState<QueueMonitorData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshInterval, setRefreshInterval] = useState<number>(10000); // 10 seconds
  const [autoRefresh, setAutoRefresh] = useState<boolean>(true);
  const [activeTab, setActiveTab] = useState<string>('queues');

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/monitor/queues');
      setData(response.data.data);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch queue data');
      console.error('Failed to fetch queue data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();

    // Set up auto-refresh
    let intervalId: NodeJS.Timeout | null = null;
    if (autoRefresh) {
      intervalId = setInterval(fetchData, refreshInterval);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [autoRefresh, refreshInterval]);

  const handlePauseQueue = async (queueName: string) => {
    try {
      await axios.post(`/api/monitor/queues/${queueName}/pause`);
      alert(`Queue ${queueName} paused successfully`);
      fetchData();
    } catch (err: any) {
      alert(`Failed to pause queue: ${err.message}`);
    }
  };

  const handleResumeQueue = async (queueName: string) => {
    try {
      await axios.post(`/api/monitor/queues/${queueName}/resume`);
      alert(`Queue ${queueName} resumed successfully`);
      fetchData();
    } catch (err: any) {
      alert(`Failed to resume queue: ${err.message}`);
    }
  };

  const getStatusBadge = (status: string) => {
    let color = 'gray';
    
    switch (status) {
      case 'healthy':
        color = 'green';
        break;
      case 'degraded':
        color = 'orange';
        break;
      case 'unhealthy':
      case 'error':
        color = 'red';
        break;
    }
    
    return (
      <span 
        style={{ 
          backgroundColor: color, 
          color: 'white', 
          padding: '2px 8px', 
          borderRadius: '9999px',
          fontSize: '0.75rem',
          fontWeight: 'bold'
        }}
      >
        {status}
      </span>
    );
  };

  const getCircuitBreakerBadge = (state: string) => {
    let color = 'gray';
    
    switch (state) {
      case 'CLOSED':
        color = 'green';
        break;
      case 'HALF_OPEN':
        color = 'orange';
        break;
      case 'OPEN':
        color = 'red';
        break;
    }
    
    return (
      <span 
        style={{ 
          backgroundColor: color, 
          color: 'white', 
          padding: '2px 8px', 
          borderRadius: '9999px',
          fontSize: '0.75rem',
          fontWeight: 'bold'
        }}
      >
        {state}
      </span>
    );
  };

  if (loading && !data) {
    return <div>Loading task queue data...</div>;
  }

  if (error) {
    return <div style={{ color: 'red' }}>Error: {error}</div>;
  }

  return (
    <div style={{ fontFamily: 'Arial, sans-serif' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
        <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold' }}>Task Queue Monitor</h2>
        <div>
          <button 
            onClick={() => setAutoRefresh(!autoRefresh)}
            style={{
              marginRight: '0.5rem',
              padding: '0.5rem 1rem',
              border: '1px solid #ccc',
              borderRadius: '0.25rem',
              background: 'white'
            }}
          >
            {autoRefresh ? 'Disable Auto-refresh' : 'Enable Auto-refresh'}
          </button>
          <button 
            onClick={fetchData}
            disabled={loading}
            style={{
              padding: '0.5rem 1rem',
              border: '1px solid #ccc',
              borderRadius: '0.25rem',
              background: 'white'
            }}
          >
            Refresh
          </button>
        </div>
      </div>

      {data && (
        <div>
          <div style={{ borderBottom: '1px solid #ccc', marginBottom: '1rem' }}>
            <button 
              onClick={() => setActiveTab('queues')}
              style={{
                padding: '0.5rem 1rem',
                marginRight: '0.5rem',
                background: activeTab === 'queues' ? '#f0f0f0' : 'transparent',
                border: 'none',
                borderBottom: activeTab === 'queues' ? '2px solid blue' : 'none'
              }}
            >
              Queues
            </button>
            <button 
              onClick={() => setActiveTab('redis')}
              style={{
                padding: '0.5rem 1rem',
                marginRight: '0.5rem',
                background: activeTab === 'redis' ? '#f0f0f0' : 'transparent',
                border: 'none',
                borderBottom: activeTab === 'redis' ? '2px solid blue' : 'none'
              }}
            >
              Redis
            </button>
            <button 
              onClick={() => setActiveTab('health')}
              style={{
                padding: '0.5rem 1rem',
                background: activeTab === 'health' ? '#f0f0f0' : 'transparent',
                border: 'none',
                borderBottom: activeTab === 'health' ? '2px solid blue' : 'none'
              }}
            >
              System Health
            </button>
          </div>

          {activeTab === 'queues' && (
            <div style={{ border: '1px solid #ccc', borderRadius: '0.5rem', padding: '1rem', marginBottom: '1rem' }}>
              <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>Queue Statistics</h3>
              <p style={{ color: '#666', marginBottom: '1rem' }}>Current status and statistics for all task queues</p>
              
              <div style={{ overflowX: 'auto' }}>
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr style={{ borderBottom: '1px solid #ccc' }}>
                      <th style={{ padding: '0.5rem', textAlign: 'left' }}>Queue Name</th>
                      <th style={{ padding: '0.5rem', textAlign: 'left' }}>Status</th>
                      <th style={{ padding: '0.5rem', textAlign: 'left' }}>Waiting</th>
                      <th style={{ padding: '0.5rem', textAlign: 'left' }}>Active</th>
                      <th style={{ padding: '0.5rem', textAlign: 'left' }}>Completed</th>
                      <th style={{ padding: '0.5rem', textAlign: 'left' }}>Failed</th>
                      <th style={{ padding: '0.5rem', textAlign: 'left' }}>Delayed</th>
                      <th style={{ padding: '0.5rem', textAlign: 'left' }}>Dead Letter</th>
                      <th style={{ padding: '0.5rem', textAlign: 'left' }}>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.queues.map((queue) => (
                      <tr key={queue.name} style={{ borderBottom: '1px solid #eee' }}>
                        <td style={{ padding: '0.5rem', fontWeight: 'bold' }}>
                          {queue.name}
                          {queue.isPaused && (
                            <span style={{ 
                              marginLeft: '0.5rem',
                              padding: '2px 6px',
                              fontSize: '0.75rem',
                              border: '1px solid #ccc',
                              borderRadius: '9999px'
                            }}>
                              Paused
                            </span>
                          )}
                        </td>
                        <td style={{ padding: '0.5rem' }}>
                          {queue.isPaused ? 
                            <span style={{ color: 'blue' }}>Paused</span> : 
                            (queue.failed > 0 || queue.deadLetter > 0) ? 
                              <span style={{ color: 'orange' }}>Warning</span> : 
                              <span style={{ color: 'green' }}>Healthy</span>
                          }
                        </td>
                        <td style={{ padding: '0.5rem' }}>{queue.waiting}</td>
                        <td style={{ padding: '0.5rem' }}>{queue.active}</td>
                        <td style={{ padding: '0.5rem' }}>{queue.completed}</td>
                        <td style={{ padding: '0.5rem', color: queue.failed > 0 ? 'red' : 'inherit', fontWeight: queue.failed > 0 ? 'bold' : 'normal' }}>
                          {queue.failed}
                        </td>
                        <td style={{ padding: '0.5rem' }}>{queue.delayed}</td>
                        <td style={{ padding: '0.5rem', color: queue.deadLetter > 0 ? 'red' : 'inherit', fontWeight: queue.deadLetter > 0 ? 'bold' : 'normal' }}>
                          {queue.deadLetter}
                        </td>
                        <td style={{ padding: '0.5rem' }}>
                          {queue.isPaused ? (
                            <button
                              onClick={() => handleResumeQueue(queue.name)}
                              style={{
                                padding: '0.25rem 0.5rem',
                                border: '1px solid #ccc',
                                borderRadius: '0.25rem',
                                background: 'white'
                              }}
                            >
                              Resume
                            </button>
                          ) : (
                            <button
                              onClick={() => handlePauseQueue(queue.name)}
                              style={{
                                padding: '0.25rem 0.5rem',
                                border: '1px solid #ccc',
                                borderRadius: '0.25rem',
                                background: 'white'
                              }}
                            >
                              Pause
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'redis' && (
            <div style={{ border: '1px solid #ccc', borderRadius: '0.5rem', padding: '1rem', marginBottom: '1rem' }}>
              <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>Redis Status</h3>
              <p style={{ color: '#666', marginBottom: '1rem' }}>Current status and metrics for Redis</p>
              
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                <div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                    <span style={{ fontWeight: 'bold' }}>Status:</span>
                    <div>
                      {getStatusBadge(data.redis.status)}
                    </div>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                    <span style={{ fontWeight: 'bold' }}>Response Time:</span>
                    <span>{data.redis.responseTime}ms</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                    <span style={{ fontWeight: 'bold' }}>Circuit Breaker:</span>
                    {getCircuitBreakerBadge(data.redis.circuitBreaker?.state || 'unknown')}
                  </div>
                </div>

                <div>
                  {data.redis.metrics && (
                    <>
                      {data.redis.metrics.used_memory && data.redis.metrics.maxmemory && (
                        <div style={{ marginBottom: '0.5rem' }}>
                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.25rem' }}>
                            <span style={{ fontWeight: 'bold' }}>Memory Usage:</span>
                            <span>
                              {data.redis.metrics.used_memory_human} / {data.redis.metrics.maxmemory_human || 'Unlimited'}
                            </span>
                          </div>
                          <div style={{ 
                            height: '0.5rem', 
                            backgroundColor: '#eee', 
                            borderRadius: '9999px',
                            overflow: 'hidden'
                          }}>
                            <div style={{ 
                              width: `${(data.redis.metrics.used_memory / data.redis.metrics.maxmemory) * 100}%`,
                              height: '100%',
                              backgroundColor: 'blue',
                              borderRadius: '9999px'
                            }} />
                          </div>
                        </div>
                      )}
                      {data.redis.metrics.connected_clients && (
                        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                          <span style={{ fontWeight: 'bold' }}>Connected Clients:</span>
                          <span>{data.redis.metrics.connected_clients}</span>
                        </div>
                      )}
                      {data.redis.metrics.uptime_in_seconds && (
                        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.5rem' }}>
                          <span style={{ fontWeight: 'bold' }}>Uptime:</span>
                          <span>
                            {Math.floor(data.redis.metrics.uptime_in_seconds / 86400)} days,{' '}
                            {Math.floor((data.redis.metrics.uptime_in_seconds % 86400) / 3600)} hours
                          </span>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'health' && (
            <div style={{ border: '1px solid #ccc', borderRadius: '0.5rem', padding: '1rem', marginBottom: '1rem' }}>
              <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>System Health</h3>
              <p style={{ color: '#666', marginBottom: '1rem' }}>Overall health status of the task queue system</p>
              
              <div style={{ marginBottom: '1rem' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                  <span style={{ fontSize: '1.125rem', fontWeight: 'bold' }}>Overall Status:</span>
                  <div>
                    {getStatusBadge(data.health.status)}
                  </div>
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                  <div style={{ border: '1px solid #ccc', borderRadius: '0.5rem', padding: '1rem' }}>
                    <h4 style={{ fontSize: '1rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>Redis Health</h4>
                    <div style={{ marginBottom: '0.5rem' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.25rem' }}>
                        <span>Status:</span>
                        <div>
                          {getStatusBadge(data.health.redisHealth.status)}
                        </div>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                        <span>Circuit Breaker:</span>
                        {getCircuitBreakerBadge(data.health.redisHealth.circuitBreaker?.state || 'unknown')}
                      </div>
                    </div>
                  </div>

                  <div style={{ border: '1px solid #ccc', borderRadius: '0.5rem', padding: '1rem' }}>
                    <h4 style={{ fontSize: '1rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>Queue Health</h4>
                    <div>
                      {data.health.queues.map((queue) => (
                        <div key={queue.name} style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '0.25rem' }}>
                          <span>{queue.name}:</span>
                          <div>
                            {queue.stats ? (
                              <span style={{ 
                                backgroundColor: 'green', 
                                color: 'white', 
                                padding: '2px 8px', 
                                borderRadius: '9999px',
                                fontSize: '0.75rem',
                                fontWeight: 'bold'
                              }}>
                                Available
                              </span>
                            ) : (
                              <span style={{ 
                                backgroundColor: 'red', 
                                color: 'white', 
                                padding: '2px 8px', 
                                borderRadius: '9999px',
                                fontSize: '0.75rem',
                                fontWeight: 'bold'
                              }}>
                                Unavailable
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TaskQueueMonitor;
