"use client";

import React from 'react';
import { 
  HomeIcon, 
  ChartBarIcon, 
  PhoneIcon, 
  UserGroupIcon, 
  Cog6ToothIcon, 
  ArrowLeftOnRectangleIcon,
  QuestionMarkCircleIcon,
  BellIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import Image from "next/image";

export function DashboardSidebar({ activeTab = 'home', onTabChange }) {
  // Nav items for the sidebar
  const navItems = [
    { name: 'Home', icon: <HomeIcon className="h-5 w-5" />, id: 'home' },
    { name: 'Analytics', icon: <ChartBarIcon className="h-5 w-5" />, id: 'analytics' },
    { name: 'Call Logs', icon: <PhoneIcon className="h-5 w-5" />, id: 'call-logs' },
    { name: 'Users', icon: <UserGroupIcon className="h-5 w-5" />, id: 'users' },
    { name: 'Setting<PERSON>', icon: <Cog6ToothIcon className="h-5 w-5" />, id: 'settings' },
    { name: 'Help', icon: <QuestionMarkCircleIcon className="h-5 w-5" />, id: 'help' },
  ];

  // Handle tab click
  const handleTabClick = (tabId) => {
    if (onTabChange) {
      onTabChange(tabId);
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-900 border-r border-gray-750">
      {/* Ambient lighting effects */}
      <div className="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 opacity-40"></div>
      <div className="absolute -top-40 -left-40 w-80 h-80 bg-indigo-600 rounded-full filter blur-[100px] opacity-5 pointer-events-none"></div>
      
      {/* Logo Section */}
      <div className="p-5 border-b border-gray-750">
        <div className="flex items-center">
          <div className="mr-3 rounded-md bg-gradient-to-br from-indigo-500 to-purple-600 p-2 border border-indigo-400/20 shadow-neon-indigo">
            <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 15.536L9.879 9.879M9.879 9.879L4.222 4.222M4.222 4.222l5.657 5.657M4.222 4.222l11.314 11.314" />
            </svg>
          </div>
          <div>
            <h1 className="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400">
              Callsaver
            </h1>
            <p className="text-xs text-gray-400">AI Call Management</p>
          </div>
        </div>
      </div>

      {/* User Profile Section */}
      <div className="p-4 border-b border-gray-750 bg-gray-850/50">
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-indigo-500 flex items-center justify-center text-white font-medium text-sm overflow-hidden shadow-neon-blue border border-blue-500/30">
            <Image src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" className="w-full h-full object-cover" width={40} height={40} />
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-white">Alex Johnson</p>
            <p className="text-xs text-gray-400">Admin</p>
          </div>
          <div className="ml-auto relative">
            <div className="relative">
              <BellIcon className="h-5 w-5 text-gray-400 hover:text-indigo-400 cursor-pointer transition-colors duration-200" />
              <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-transparent">
        <ul className="space-y-1">
          {navItems.map((item) => (
            <li key={item.id}>
              <button
                onClick={() => handleTabClick(item.id)}
                className={`
                  w-full group flex items-center py-2 px-3 rounded-lg transition-all duration-200 
                  ${activeTab === item.id 
                    ? 'bg-gradient-to-r from-indigo-900/50 to-purple-900/30 text-white border border-indigo-500/30 shadow-neon-indigo/20' 
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50 hover:border hover:border-gray-700/50'}
                `}
              >
                <span className={`${activeTab === item.id ? 'text-indigo-400' : 'text-gray-500 group-hover:text-indigo-400'} transition-colors duration-200 mr-3`}>
                  {item.icon}
                </span>
                <span className="text-sm font-medium">{item.name}</span>
                {activeTab === item.id && (
                  <span className="ml-auto w-1.5 h-1.5 rounded-full bg-indigo-400 animate-pulse"></span>
                )}
              </button>
            </li>
          ))}
        </ul>
        
        <div className="mt-6 pt-6 border-t border-gray-750/50">
          <div className="bg-gray-800/40 rounded-lg p-3 border border-indigo-700/20 border-glow">
            <div className="flex items-center mb-2">
              <div className="w-8 h-8 rounded-full bg-indigo-900/60 flex items-center justify-center">
                <svg className="h-4 w-4 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-white">Pro Tip</h3>
              </div>
            </div>
            <p className="text-xs text-gray-400">Use voice commands to navigate faster through the dashboard.</p>
          </div>
        </div>
      </nav>

      {/* Bottom Section */}
      <div className="p-4 border-t border-gray-750 bg-gray-850/30">
        <button className="w-full flex items-center justify-center py-2 px-4 rounded-lg bg-gray-800 hover:bg-gray-750 text-gray-300 transition-colors duration-200 text-sm border border-gray-700 hover:border-red-500/30">
          <ArrowLeftOnRectangleIcon className="h-4 w-4 mr-2 text-red-400" />
          <span>Sign Out</span>
        </button>
      </div>
    </div>
  );
} 