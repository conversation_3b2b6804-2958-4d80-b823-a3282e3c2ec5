"use client";

import React from 'react';
import { motion } from 'framer-motion';

export default function MessageBubble({ message, isLast = false }) {
  if (!message) return null;
  
  const isUser = message.sender === 'user';
  
  // More authentic iMessage styling
  const bubbleStyles = isUser 
    ? "bg-[#1982FC] text-white rounded-[16px] rounded-br-[4px] shadow-sm" 
    : "bg-[#E9E9EB] text-black rounded-[16px] rounded-bl-[4px] shadow-sm";
  
  const containerStyles = isUser 
    ? "flex mb-3 justify-end" 
    : "flex mb-3 justify-start";
  
  // Initial animation for new messages
  const initialAnimation = isLast 
    ? { opacity: 0, y: 20, scale: 0.9 } 
    : { opacity: 1, y: 0, scale: 1 };
  
  return (
    <motion.div 
      className={containerStyles}
      initial={initialAnimation}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.2 }}
    >
      <div className={`max-w-[85%] relative px-4 py-2.5 ${bubbleStyles}`}>
        <div className="relative z-10">
          <p className="text-sm break-words leading-tight">{message.content}</p>
        </div>
        <div className={`text-right text-[9px] mt-1 mr-0.5 ${isUser ? 'text-blue-100' : 'text-gray-500'}`}>
          {isUser ? 'Delivered' : 'Read'}
        </div>
      </div>
    </motion.div>
  );
}