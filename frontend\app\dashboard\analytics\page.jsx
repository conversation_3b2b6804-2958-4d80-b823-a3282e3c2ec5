'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Line, PieChart, Pie, 
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, 
  ResponsiveContainer, Cell
} from 'recharts';

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState('month');
  const [analyticsData, setAnalyticsData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch analytics data
  useEffect(() => {
    const fetchAnalyticsData = async () => {
      try {
        setIsLoading(true);
        // Replace with actual API endpoint when backend is ready
        const response = await fetch(`/api/analytics/data?timeRange=${timeRange}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch analytics data');
        }
        
        const data = await response.json();
        setAnalyticsData(data);
      } catch (err) {
        console.error('Error fetching analytics data:', err);
        setError('Failed to load analytics data. Please try again later.');
        // Use mock data for now
        setAnalyticsData(getMockAnalyticsData(timeRange));
      } finally {
        setIsLoading(false);
      }
    };

    fetchAnalyticsData();
  }, [timeRange]);

  // Handle time range change
  const handleTimeRangeChange = (range) => {
    setTimeRange(range);
  };

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  // Format time for display
  const formatTime = (minutes) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  // Calculate percentage change
  const calculateChange = (current, previous) => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  };

  // Get color based on sentiment score
  const getSentimentColor = (score) => {
    if (score >= 0.7) return '#10b981'; // Green
    if (score >= 0.4) return '#f59e0b'; // Yellow
    return '#ef4444'; // Red
  };

  // Render loading state
  if (isLoading && !analyticsData) {
    return (
      <div className="flex justify-center items-center h-[calc(100vh-200px)]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white">Analytics Dashboard</h1>
            <p className="text-gray-400 mt-1">
              Comprehensive insights into your call and message activity
            </p>
          </div>
          
          <div className="mt-4 md:mt-0">
            <div className="inline-flex rounded-md shadow-sm" role="group">
              <button
                type="button"
                onClick={() => handleTimeRangeChange('week')}
                className={`px-4 py-2 text-sm font-medium rounded-l-lg ${
                  timeRange === 'week'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                Week
              </button>
              <button
                type="button"
                onClick={() => handleTimeRangeChange('month')}
                className={`px-4 py-2 text-sm font-medium ${
                  timeRange === 'month'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                Month
              </button>
              <button
                type="button"
                onClick={() => handleTimeRangeChange('quarter')}
                className={`px-4 py-2 text-sm font-medium rounded-r-lg ${
                  timeRange === 'quarter'
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                Quarter
              </button>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3 text-red-200">
            {error}
          </div>
        )}

        {/* Navigation Tabs */}
        <div className="border-b border-gray-700">
          <nav className="flex space-x-8">
            <button
              onClick={() => handleTabChange('overview')}
              className={`py-4 px-1 font-medium text-sm border-b-2 ${
                activeTab === 'overview'
                  ? 'border-purple-500 text-purple-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => handleTabChange('calls')}
              className={`py-4 px-1 font-medium text-sm border-b-2 ${
                activeTab === 'calls'
                  ? 'border-purple-500 text-purple-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              Call Analytics
            </button>
            <button
              onClick={() => handleTabChange('messages')}
              className={`py-4 px-1 font-medium text-sm border-b-2 ${
                activeTab === 'messages'
                  ? 'border-purple-500 text-purple-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              Message Analytics
            </button>
            <button
              onClick={() => handleTabChange('users')}
              className={`py-4 px-1 font-medium text-sm border-b-2 ${
                activeTab === 'users'
                  ? 'border-purple-500 text-purple-400'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              User Analytics
            </button>
          </nav>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && analyticsData && (
          <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
              {/* Phone Numbers */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-gray-400 text-sm">Phone Numbers</h3>
                    <div className="mt-2 flex items-end">
                      <span className="text-3xl font-bold text-white">{analyticsData.overview.phoneNumbers.current}</span>
                      <span className="text-gray-400 ml-2 text-sm">Active</span>
                    </div>
                  </div>
                  <div className="p-2 bg-purple-900/30 rounded-lg">
                    <svg className="w-6 h-6 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                </div>
                <div className="mt-2">
                  {analyticsData.overview.phoneNumbers.change >= 0 ? (
                    <span className="text-green-400 text-sm flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                      </svg>
                      {analyticsData.overview.phoneNumbers.change}% from last {timeRange}
                    </span>
                  ) : (
                    <span className="text-red-400 text-sm flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                      </svg>
                      {Math.abs(analyticsData.overview.phoneNumbers.change)}% from last {timeRange}
                    </span>
                  )}
                </div>
              </div>
              
              {/* Total Calls */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-gray-400 text-sm">Total Calls</h3>
                    <div className="mt-2 flex items-end">
                      <span className="text-3xl font-bold text-white">{analyticsData.overview.calls.current}</span>
                      <span className="text-gray-400 ml-2 text-sm">This {timeRange}</span>
                    </div>
                  </div>
                  <div className="p-2 bg-blue-900/30 rounded-lg">
                    <svg className="w-6 h-6 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 8l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M5 3a2 2 0 00-2 2v1c0 8.284 6.716 15 15 15h1a2 2 0 002-2v-3.28a1 1 0 00-.684-.948l-4.493-1.498a1 1 0 00-1.21.502l-1.13 2.257a11.042 11.042 0 01-5.516-5.517l2.257-1.128a1 1 0 00.502-1.21L9.228 3.683A1 1 0 008.279 3H5z" />
                    </svg>
                  </div>
                </div>
                <div className="mt-2">
                  {analyticsData.overview.calls.change >= 0 ? (
                    <span className="text-green-400 text-sm flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                      </svg>
                      {analyticsData.overview.calls.change}% from last {timeRange}
                    </span>
                  ) : (
                    <span className="text-red-400 text-sm flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                      </svg>
                      {Math.abs(analyticsData.overview.calls.change)}% from last {timeRange}
                    </span>
                  )}
                </div>
              </div>
              
              {/* Talk Time */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-gray-400 text-sm">Talk Time</h3>
                    <div className="mt-2 flex items-end">
                      <span className="text-3xl font-bold text-white">{formatTime(analyticsData.overview.talkTime.current)}</span>
                    </div>
                  </div>
                  <div className="p-2 bg-green-900/30 rounded-lg">
                    <svg className="w-6 h-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="mt-2">
                  {analyticsData.overview.talkTime.change >= 0 ? (
                    <span className="text-green-400 text-sm flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                      </svg>
                      {analyticsData.overview.talkTime.change}% from last {timeRange}
                    </span>
                  ) : (
                    <span className="text-red-400 text-sm flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                      </svg>
                      {Math.abs(analyticsData.overview.talkTime.change)}% from last {timeRange}
                    </span>
                  )}
                </div>
              </div>
              
              {/* Messages */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-gray-400 text-sm">Messages</h3>
                    <div className="mt-2 flex items-end">
                      <span className="text-3xl font-bold text-white">{analyticsData.overview.messages.current}</span>
                      <span className="text-gray-400 ml-2 text-sm">Processed</span>
                    </div>
                  </div>
                  <div className="p-2 bg-yellow-900/30 rounded-lg">
                    <svg className="w-6 h-6 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                    </svg>
                  </div>
                </div>
                <div className="mt-2">
                  {analyticsData.overview.messages.change >= 0 ? (
                    <span className="text-green-400 text-sm flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                      </svg>
                      {analyticsData.overview.messages.change}% from last {timeRange}
                    </span>
                  ) : (
                    <span className="text-red-400 text-sm flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                      </svg>
                      {Math.abs(analyticsData.overview.messages.change)}% from last {timeRange}
                    </span>
                  )}
                </div>
              </div>
            </div>
            
            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Call Volume Chart */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-white font-semibold mb-6">Call Volume</h3>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={analyticsData.charts.callVolume}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis 
                        dataKey="date" 
                        stroke="#9CA3AF"
                        tickFormatter={(value) => formatDate(value)}
                      />
                      <YAxis stroke="#9CA3AF" />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1F2937', 
                          borderColor: '#4B5563',
                          color: '#F9FAFB'
                        }}
                        labelStyle={{ color: '#F9FAFB' }}
                      />
                      <Legend />
                      <Line 
                        type="monotone" 
                        dataKey="inbound" 
                        name="Inbound Calls"
                        stroke="#8B5CF6" 
                        activeDot={{ r: 8 }} 
                        strokeWidth={2}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="outbound" 
                        name="Outbound Calls"
                        stroke="#10B981" 
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
              
              {/* Call Distribution Chart */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-white font-semibold mb-6">Call Distribution</h3>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={analyticsData.charts.callDistribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {analyticsData.charts.callDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1F2937', 
                          borderColor: '#4B5563',
                          color: '#F9FAFB'
                        }}
                        formatter={(value, name) => [`${value} calls`, name]}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
            
            {/* Recent Activity */}
            <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-white font-semibold">Recent Activity</h3>
                <a href="/dashboard/calls" className="text-purple-400 hover:text-purple-300 text-sm">
                  View All
                </a>
              </div>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-gray-400 border-b border-gray-700">
                      <th className="pb-2 font-medium">Type</th>
                      <th className="pb-2 font-medium">Number</th>
                      <th className="pb-2 font-medium">Date & Time</th>
                      <th className="pb-2 font-medium">Duration</th>
                      <th className="pb-2 font-medium">Sentiment</th>
                      <th className="pb-2 font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {analyticsData.recentActivity.map((activity) => (
                      <tr key={activity.id} className="border-b border-gray-700 text-gray-300">
                        <td className="py-3">
                          {activity.type === 'call' ? (
                            <span className="flex items-center">
                              <svg className="w-4 h-4 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                              </svg>
                              Call
                            </span>
                          ) : (
                            <span className="flex items-center">
                              <svg className="w-4 h-4 mr-2 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                              </svg>
                              SMS
                            </span>
                          )}
                        </td>
                        <td className="py-3">{activity.phoneNumber}</td>
                        <td className="py-3">{new Date(activity.timestamp).toLocaleString()}</td>
                        <td className="py-3">{activity.duration ? formatTime(activity.duration) : 'N/A'}</td>
                        <td className="py-3">
                          {activity.sentiment && (
                            <div className="flex items-center">
                              <div 
                                className="w-3 h-3 rounded-full mr-2" 
                                style={{ backgroundColor: getSentimentColor(activity.sentiment) }}
                              ></div>
                              <span>{(activity.sentiment * 10).toFixed(1)}/10</span>
                            </div>
                          )}
                        </td>
                        <td className="py-3">
                          <a 
                            href={`/dashboard/${activity.type === 'call' ? 'calls' : 'sms'}/${activity.id}`}
                            className="text-purple-400 hover:text-purple-300 transition-colors"
                          >
                            View Details
                          </a>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Call Analytics Tab */}
        {activeTab === 'calls' && analyticsData && (
          <div className="space-y-6">
            {/* Call Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
              {/* Total Calls */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-gray-400 text-sm mb-2">Total Calls</h3>
                <div className="flex items-end">
                  <span className="text-3xl font-bold text-white">{analyticsData.calls.total}</span>
                  <span className="text-gray-400 ml-2 text-sm">This {timeRange}</span>
                </div>
              </div>
              
              {/* Average Duration */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-gray-400 text-sm mb-2">Avg. Duration</h3>
                <div className="flex items-end">
                  <span className="text-3xl font-bold text-white">{formatTime(analyticsData.calls.avgDuration)}</span>
                </div>
              </div>
              
              {/* Answered Rate */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-gray-400 text-sm mb-2">Answer Rate</h3>
                <div className="flex items-end">
                  <span className="text-3xl font-bold text-white">{analyticsData.calls.answerRate}%</span>
                </div>
              </div>
              
              {/* Conversion Rate */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-gray-400 text-sm mb-2">Conversion Rate</h3>
                <div className="flex items-end">
                  <span className="text-3xl font-bold text-white">{analyticsData.calls.conversionRate}%</span>
                </div>
              </div>
            </div>
            
            {/* Call Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Call Volume by Hour */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-white font-semibold mb-6">Call Volume by Hour</h3>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={analyticsData.charts.callsByHour}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis 
                        dataKey="hour" 
                        stroke="#9CA3AF"
                        tickFormatter={(value) => `${value}:00`}
                      />
                      <YAxis stroke="#9CA3AF" />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1F2937', 
                          borderColor: '#4B5563',
                          color: '#F9FAFB'
                        }}
                        formatter={(value, name) => [`${value} calls`, name]}
                      />
                      <Legend />
                      <Bar 
                        dataKey="calls" 
                        name="Calls"
                        fill="#8B5CF6" 
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
              
              {/* Call Outcomes */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-white font-semibold mb-6">Call Outcomes</h3>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={analyticsData.charts.callOutcomes}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {analyticsData.charts.callOutcomes.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1F2937', 
                          borderColor: '#4B5563',
                          color: '#F9FAFB'
                        }}
                        formatter={(value, name) => [`${value} calls`, name]}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
            
            {/* Call Sentiment Analysis */}
            <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
              <h3 className="text-white font-semibold mb-6">Call Sentiment Analysis</h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={analyticsData.charts.callSentiment}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                    <XAxis 
                      dataKey="date" 
                      stroke="#9CA3AF"
                      tickFormatter={(value) => formatDate(value)}
                    />
                    <YAxis 
                      stroke="#9CA3AF" 
                      domain={[0, 1]}
                      tickFormatter={(value) => (value * 10).toFixed(1)}
                    />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: '#1F2937', 
                        borderColor: '#4B5563',
                        color: '#F9FAFB'
                      }}
                      formatter={(value, name) => [`${(value * 10).toFixed(1)}/10`, name]}
                    />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="sentiment" 
                      name="Sentiment Score"
                      stroke="#10B981" 
                      activeDot={{ r: 8 }} 
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        )}

        {/* Message Analytics Tab */}
        {activeTab === 'messages' && analyticsData && (
          <div className="space-y-6">
            {/* Message Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
              {/* Total Messages */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-gray-400 text-sm mb-2">Total Messages</h3>
                <div className="flex items-end">
                  <span className="text-3xl font-bold text-white">{analyticsData.messages.total}</span>
                  <span className="text-gray-400 ml-2 text-sm">This {timeRange}</span>
                </div>
              </div>
              
              {/* Response Rate */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-gray-400 text-sm mb-2">Response Rate</h3>
                <div className="flex items-end">
                  <span className="text-3xl font-bold text-white">{analyticsData.messages.responseRate}%</span>
                </div>
              </div>
              
              {/* Avg. Response Time */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-gray-400 text-sm mb-2">Avg. Response Time</h3>
                <div className="flex items-end">
                  <span className="text-3xl font-bold text-white">{analyticsData.messages.avgResponseTime}s</span>
                </div>
              </div>
              
              {/* Conversion Rate */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-gray-400 text-sm mb-2">Conversion Rate</h3>
                <div className="flex items-end">
                  <span className="text-3xl font-bold text-white">{analyticsData.messages.conversionRate}%</span>
                </div>
              </div>
            </div>
            
            {/* Message Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Message Volume */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-white font-semibold mb-6">Message Volume</h3>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={analyticsData.charts.messageVolume}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis 
                        dataKey="date" 
                        stroke="#9CA3AF"
                        tickFormatter={(value) => formatDate(value)}
                      />
                      <YAxis stroke="#9CA3AF" />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1F2937', 
                          borderColor: '#4B5563',
                          color: '#F9FAFB'
                        }}
                      />
                      <Legend />
                      <Line 
                        type="monotone" 
                        dataKey="inbound" 
                        name="Inbound Messages"
                        stroke="#8B5CF6" 
                        activeDot={{ r: 8 }} 
                        strokeWidth={2}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="outbound" 
                        name="Outbound Messages"
                        stroke="#10B981" 
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
              
              {/* Message Categories */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-white font-semibold mb-6">Message Categories</h3>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={analyticsData.charts.messageCategories}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {analyticsData.charts.messageCategories.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1F2937', 
                          borderColor: '#4B5563',
                          color: '#F9FAFB'
                        }}
                        formatter={(value, name) => [`${value} messages`, name]}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
            
            {/* Popular Keywords */}
            <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
              <h3 className="text-white font-semibold mb-6">Popular Keywords</h3>
              <div className="flex flex-wrap gap-2">
                {analyticsData.messages.keywords.map((keyword, index) => (
                  <div 
                    key={index}
                    className="px-3 py-2 bg-gray-700/50 rounded-lg text-white"
                    style={{ fontSize: `${Math.max(0.8, Math.min(1.5, 0.8 + (keyword.count / 20)))}rem` }}
                  >
                    {keyword.text} <span className="text-gray-400">({keyword.count})</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* User Analytics Tab */}
        {activeTab === 'users' && analyticsData && (
          <div className="space-y-6">
            {/* User Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
              {/* Total Users */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-gray-400 text-sm mb-2">Total Users</h3>
                <div className="flex items-end">
                  <span className="text-3xl font-bold text-white">{analyticsData.users.total}</span>
                </div>
                <div className="mt-2">
                  {analyticsData.users.growth >= 0 ? (
                    <span className="text-green-400 text-sm flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                      </svg>
                      {analyticsData.users.growth}% from last {timeRange}
                    </span>
                  ) : (
                    <span className="text-red-400 text-sm flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                      </svg>
                      {Math.abs(analyticsData.users.growth)}% from last {timeRange}
                    </span>
                  )}
                </div>
              </div>
              
              {/* Active Users */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-gray-400 text-sm mb-2">Active Users</h3>
                <div className="flex items-end">
                  <span className="text-3xl font-bold text-white">{analyticsData.users.active}</span>
                  <span className="text-gray-400 ml-2 text-sm">This {timeRange}</span>
                </div>
                <div className="mt-2">
                  <div className="w-full bg-gray-700 rounded-full h-2.5">
                    <div 
                      className="bg-purple-600 h-2.5 rounded-full" 
                      style={{ width: `${(analyticsData.users.active / analyticsData.users.total) * 100}%` }}
                    ></div>
                  </div>
                  <div className="text-gray-400 text-xs mt-1">
                    {((analyticsData.users.active / analyticsData.users.total) * 100).toFixed(1)}% of total users
                  </div>
                </div>
              </div>
              
              {/* Avg. Session Duration */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-gray-400 text-sm mb-2">Avg. Session Duration</h3>
                <div className="flex items-end">
                  <span className="text-3xl font-bold text-white">{analyticsData.users.avgSessionDuration}m</span>
                </div>
              </div>
              
              {/* Retention Rate */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-gray-400 text-sm mb-2">Retention Rate</h3>
                <div className="flex items-end">
                  <span className="text-3xl font-bold text-white">{analyticsData.users.retentionRate}%</span>
                </div>
              </div>
            </div>
            
            {/* User Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* User Growth */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-white font-semibold mb-6">User Growth</h3>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={analyticsData.charts.userGrowth}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis 
                        dataKey="date" 
                        stroke="#9CA3AF"
                        tickFormatter={(value) => formatDate(value)}
                      />
                      <YAxis stroke="#9CA3AF" />
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1F2937', 
                          borderColor: '#4B5563',
                          color: '#F9FAFB'
                        }}
                      />
                      <Legend />
                      <Line 
                        type="monotone" 
                        dataKey="users" 
                        name="Total Users"
                        stroke="#8B5CF6" 
                        activeDot={{ r: 8 }} 
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
              
              {/* Subscription Distribution */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
                <h3 className="text-white font-semibold mb-6">Subscription Distribution</h3>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={analyticsData.charts.subscriptionDistribution}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                      >
                        {analyticsData.charts.subscriptionDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip 
                        contentStyle={{ 
                          backgroundColor: '#1F2937', 
                          borderColor: '#4B5563',
                          color: '#F9FAFB'
                        }}
                        formatter={(value, name) => [`${value} users`, name]}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
            
            {/* Top Users */}
            <div className="bg-gray-800/50 rounded-xl p-6 border border-purple-500/20">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-white font-semibold">Top Users</h3>
                <a href="/dashboard/users" className="text-purple-400 hover:text-purple-300 text-sm">
                  View All Users
                </a>
              </div>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="text-left text-gray-400 border-b border-gray-700">
                      <th className="pb-2 font-medium">User</th>
                      <th className="pb-2 font-medium">Plan</th>
                      <th className="pb-2 font-medium">Phone Numbers</th>
                      <th className="pb-2 font-medium">Calls</th>
                      <th className="pb-2 font-medium">Messages</th>
                      <th className="pb-2 font-medium">Last Active</th>
                    </tr>
                  </thead>
                  <tbody>
                    {analyticsData.users.topUsers.map((user) => (
                      <tr key={user.id} className="border-b border-gray-700 text-gray-300">
                        <td className="py-3">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-purple-900/50 flex items-center justify-center mr-3">
                              <span className="text-purple-300 font-medium">{user.name.charAt(0)}</span>
                            </div>
                            <div>
                              <div className="font-medium text-white">{user.name}</div>
                              <div className="text-sm text-gray-400">{user.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="py-3">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            user.plan === 'Business' ? 'bg-purple-900/30 text-purple-400' :
                            user.plan === 'Professional' ? 'bg-blue-900/30 text-blue-400' :
                            'bg-green-900/30 text-green-400'
                          }`}>
                            {user.plan}
                          </span>
                        </td>
                        <td className="py-3">{user.phoneNumbers}</td>
                        <td className="py-3">{user.calls}</td>
                        <td className="py-3">{user.messages}</td>
                        <td className="py-3">{new Date(user.lastActive).toLocaleDateString()}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Helper function to generate mock analytics data
function getMockAnalyticsData(timeRange) {
  // Generate dates for the selected time range
  const generateDates = (days) => {
    const dates = [];
    const today = new Date();
    
    for (let i = days; i >= 0; i--) {
      const date = new Date();
      date.setDate(today.getDate() - i);
      dates.push(date.toISOString().split('T')[0]);
    }
    
    return dates;
  };
  
  // Generate random data points
  const generateDataPoints = (dates, min, max, trend = 'up') => {
    return dates.map((date, index) => {
      let value;
      
      if (trend === 'up') {
        // Upward trend with some randomness
        value = min + (max - min) * (index / dates.length) + Math.random() * (max - min) * 0.2;
      } else if (trend === 'down') {
        // Downward trend with some randomness
        value = max - (max - min) * (index / dates.length) + Math.random() * (max - min) * 0.2;
      } else {
        // Random fluctuation
        value = min + Math.random() * (max - min);
      }
      
      return Math.floor(value);
    });
  };
  
  // Set date range based on selected time range
  let dates;
  switch (timeRange) {
    case 'week':
      dates = generateDates(7);
      break;
    case 'quarter':
      dates = generateDates(90);
      break;
    case 'month':
    default:
      dates = generateDates(30);
      break;
  }
  
  // Generate call volume data
  const inboundCalls = generateDataPoints(dates, 10, 50, 'up');
  const outboundCalls = generateDataPoints(dates, 5, 30, 'up');
  
  // Generate message volume data
  const inboundMessages = generateDataPoints(dates, 15, 70, 'up');
  const outboundMessages = generateDataPoints(dates, 20, 90, 'up');
  
  // Generate user growth data
  const userGrowth = [];
  let totalUsers = 120;
  
  dates.forEach((date, index) => {
    // Add 1-5 new users each day with some randomness
    const newUsers = Math.floor(Math.random() * 5) + 1;
    totalUsers += newUsers;
    
    userGrowth.push({
      date,
      users: totalUsers
    });
  });
  
  // Generate sentiment data
  const sentimentData = dates.map(date => ({
    date,
    sentiment: 0.5 + Math.random() * 0.4 // Random sentiment between 0.5 and 0.9
  }));
  
  // Generate call distribution data
  const callDistribution = [
    { name: 'Answered', value: 156, color: '#10B981' },
    { name: 'Voicemail', value: 87, color: '#F59E0B' },
    { name: 'Missed', value: 43, color: '#EF4444' },
    { name: 'Rejected', value: 12, color: '#6B7280' }
  ];
  
  // Generate call outcomes data
  const callOutcomes = [
    { name: 'Appointment', value: 78, color: '#10B981' },
    { name: 'Information', value: 124, color: '#8B5CF6' },
    { name: 'No Action', value: 96, color: '#F59E0B' }
  ];
  
  // Generate message categories data
  const messageCategories = [
    { name: 'Inquiry', value: 145, color: '#8B5CF6' },
    { name: 'Booking', value: 87, color: '#10B981' },
    { name: 'Follow-up', value: 63, color: '#F59E0B' },
    { name: 'Other', value: 32, color: '#6B7280' }
  ];
  
  // Generate subscription distribution data
  const subscriptionDistribution = [
    { name: 'Starter', value: 87, color: '#10B981' },
    { name: 'Professional', value: 124, color: '#8B5CF6' },
    { name: 'Business', value: 43, color: '#F59E0B' }
  ];
  
  // Generate calls by hour data
  const callsByHour = Array.from({ length: 24 }, (_, i) => ({
    hour: i,
    calls: Math.floor(Math.random() * 20) + (i >= 8 && i <= 18 ? 10 : 0) // More calls during business hours
  }));
  
  // Generate recent activity data
  const recentActivity = [
    {
      id: '1',
      type: 'call',
      phoneNumber: '+****************',
      timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
      duration: 4.5 * 60, // 4.5 minutes
      sentiment: 0.85
    },
    {
      id: '2',
      type: 'sms',
      phoneNumber: '+****************',
      timestamp: new Date(Date.now() - 1000 * 60 * 45).toISOString(), // 45 minutes ago
      sentiment: 0.72
    },
    {
      id: '3',
      type: 'call',
      phoneNumber: '+****************',
      timestamp: new Date(Date.now() - 1000 * 60 * 120).toISOString(), // 2 hours ago
      duration: 8.2 * 60, // 8.2 minutes
      sentiment: 0.65
    },
    {
      id: '4',
      type: 'sms',
      phoneNumber: '+****************',
      timestamp: new Date(Date.now() - 1000 * 60 * 180).toISOString(), // 3 hours ago
      sentiment: 0.91
    },
    {
      id: '5',
      type: 'call',
      phoneNumber: '+****************',
      timestamp: new Date(Date.now() - 1000 * 60 * 240).toISOString(), // 4 hours ago
      duration: 2.8 * 60, // 2.8 minutes
      sentiment: 0.38
    }
  ];
  
  // Generate top users data
  const topUsers = [
    {
      id: '1',
      name: 'John Smith',
      email: '<EMAIL>',
      plan: 'Business',
      phoneNumbers: 5,
      calls: 187,
      messages: 243,
      lastActive: new Date(Date.now() - 1000 * 60 * 30).toISOString() // 30 minutes ago
    },
    {
      id: '2',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      plan: 'Professional',
      phoneNumbers: 3,
      calls: 142,
      messages: 198,
      lastActive: new Date(Date.now() - 1000 * 60 * 60).toISOString() // 1 hour ago
    },
    {
      id: '3',
      name: 'Michael Brown',
      email: '<EMAIL>',
      plan: 'Professional',
      phoneNumbers: 2,
      calls: 98,
      messages: 156,
      lastActive: new Date(Date.now() - 1000 * 60 * 120).toISOString() // 2 hours ago
    },
    {
      id: '4',
      name: 'Emily Davis',
      email: '<EMAIL>',
      plan: 'Starter',
      phoneNumbers: 1,
      calls: 76,
      messages: 112,
      lastActive: new Date(Date.now() - 1000 * 60 * 180).toISOString() // 3 hours ago
    },
    {
      id: '5',
      name: 'David Wilson',
      email: '<EMAIL>',
      plan: 'Business',
      phoneNumbers: 4,
      calls: 165,
      messages: 201,
      lastActive: new Date(Date.now() - 1000 * 60 * 240).toISOString() // 4 hours ago
    }
  ];
  
  // Compile all data
  return {
    overview: {
      phoneNumbers: {
        current: 254,
        change: 12
      },
      calls: {
        current: inboundCalls.reduce((a, b) => a + b, 0) + outboundCalls.reduce((a, b) => a + b, 0),
        change: 8
      },
      talkTime: {
        current: 12450, // In minutes
        change: 5
      },
      messages: {
        current: inboundMessages.reduce((a, b) => a + b, 0) + outboundMessages.reduce((a, b) => a + b, 0),
        change: 15
      }
    },
    calls: {
      total: 298,
      avgDuration: 4.2 * 60, // 4.2 minutes
      answerRate: 78,
      conversionRate: 42
    },
    messages: {
      total: 327,
      responseRate: 92,
      avgResponseTime: 45, // seconds
      conversionRate: 38,
      keywords: [
        { text: 'appointment', count: 87 },
        { text: 'booking', count: 65 },
        { text: 'schedule', count: 54 },
        { text: 'availability', count: 48 },
        { text: 'pricing', count: 42 },
        { text: 'services', count: 39 },
        { text: 'hours', count: 36 },
        { text: 'location', count: 32 },
        { text: 'consultation', count: 28 },
        { text: 'reschedule', count: 25 },
        { text: 'cancel', count: 22 },
        { text: 'confirm', count: 20 },
        { text: 'question', count: 18 },
        { text: 'information', count: 16 },
        { text: 'payment', count: 14 }
      ]
    },
    users: {
      total: totalUsers,
      active: Math.floor(totalUsers * 0.75),
      growth: 18,
      avgSessionDuration: 8.5, // minutes
      retentionRate: 82,
      topUsers
    },
    charts: {
      callVolume: dates.map((date, i) => ({
        date,
        inbound: inboundCalls[i],
        outbound: outboundCalls[i]
      })),
      messageVolume: dates.map((date, i) => ({
        date,
        inbound: inboundMessages[i],
        outbound: outboundMessages[i]
      })),
      callDistribution,
      callOutcomes,
      messageCategories,
      callsByHour,
      callSentiment: sentimentData,
      userGrowth,
      subscriptionDistribution
    },
    recentActivity
  };
}
