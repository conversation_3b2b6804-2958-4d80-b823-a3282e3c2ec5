'use client';

import React, { memo, useCallback, useMemo } from 'react';
import { useRenderTimer } from '../../utils/performance/performanceMonitor';

/**
 * MemoizedComponent - A higher-order component for optimizing React rendering
 * 
 * This component applies several optimization techniques:
 * - Memoization of the component to prevent unnecessary re-renders
 * - Memoization of expensive calculations
 * - Memoization of callback functions
 * - Performance monitoring of render times
 * 
 * @param {React.ComponentType} Component - The component to optimize
 * @param {Object} options - Optimization options
 * @param {Function} options.areEqual - Custom comparison function for memoization
 * @param {boolean} options.monitorPerformance - Whether to monitor render performance
 * @param {string} options.name - Name for the component (used in performance monitoring)
 * @returns {React.ComponentType} - The optimized component
 */
const MemoizedComponent = (Component, options = {}) => {
  const {
    areEqual,
    monitorPerformance = true,
    name = Component.displayName || Component.name || 'UnnamedComponent',
  } = options;

  // Create the memoized component
  const OptimizedComponent = memo((props) => {
    // Set up performance monitoring
    const { startRender, endRender } = monitorPerformance
      ? useRenderTimer(name)
      : { startRender: () => {}, endRender: () => {} };
    
    // Start measuring render time
    const startTime = startRender();
    
    // Render the component
    const result = <Component {...props} />;
    
    // End measuring render time (in useEffect to ensure it's after the actual render)
    React.useEffect(() => {
      endRender(startTime);
    }, [startTime]);
    
    return result;
  }, areEqual);

  // Set display name for debugging
  OptimizedComponent.displayName = `Memoized(${name})`;

  return OptimizedComponent;
};

/**
 * useMemoizedValue - A hook for memoizing expensive calculations
 * 
 * @param {Function} factory - Function that returns the value to memoize
 * @param {Array} dependencies - Dependencies array for memoization
 * @param {Object} options - Options for memoization
 * @param {boolean} options.monitorPerformance - Whether to monitor calculation performance
 * @param {string} options.name - Name for the calculation (used in performance monitoring)
 * @returns {any} - The memoized value
 */
export const useMemoizedValue = (factory, dependencies, options = {}) => {
  const {
    monitorPerformance = true,
    name = 'UnnamedCalculation',
  } = options;

  // Wrap the factory function with performance monitoring
  const monitoredFactory = useMemo(() => {
    if (!monitorPerformance) return factory;

    return (...args) => {
      const startTime = performance.now();
      const result = factory(...args);
      const endTime = performance.now();
      const duration = endTime - startTime;

      if (duration > 5) { // Only log calculations that take more than 5ms
        console.log(`Calculation ${name} took ${duration.toFixed(2)}ms`);
      }

      return result;
    };
  }, [factory, monitorPerformance, name]);

  // Memoize the value
  return useMemo(() => monitoredFactory(), dependencies);
};

/**
 * useMemoizedCallback - A hook for memoizing callback functions
 * 
 * @param {Function} callback - The callback function to memoize
 * @param {Array} dependencies - Dependencies array for memoization
 * @param {Object} options - Options for memoization
 * @param {boolean} options.monitorPerformance - Whether to monitor callback performance
 * @param {string} options.name - Name for the callback (used in performance monitoring)
 * @returns {Function} - The memoized callback
 */
export const useMemoizedCallback = (callback, dependencies, options = {}) => {
  const {
    monitorPerformance = true,
    name = 'UnnamedCallback',
  } = options;

  // Create the memoized callback
  return useCallback((...args) => {
    if (!monitorPerformance) return callback(...args);

    const startTime = performance.now();
    const result = callback(...args);
    const endTime = performance.now();
    const duration = endTime - startTime;

    if (duration > 5) { // Only log callbacks that take more than 5ms
      console.log(`Callback ${name} took ${duration.toFixed(2)}ms`);
    }

    return result;
  }, dependencies);
};

export default MemoizedComponent;
