'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../components/ui/card';
import AdminPageLayout from '../../../../components/admin/AdminPageLayout';

export default function SecuritySettings() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [securitySettings, setSecuritySettings] = useState({
    mfaEnabled: true,
    passwordPolicy: {
      minLength: 12,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
      passwordExpiryDays: 90
    },
    sessionSettings: {
      sessionTimeout: 30, // minutes
      maxConcurrentSessions: 5
    },
    ipRestrictions: {
      enabled: false,
      allowedIPs: []
    },
    auditLogging: {
      enabled: true,
      retentionDays: 90
    }
  });

  useEffect(() => {
    const fetchSecuritySettings = async () => {
      try {
        setIsLoading(true);
        // In a real implementation, this would be an API call to get security settings
        // For now, we'll simulate a delay and return mock data
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock data is already set in the state
        // In a real app, you would fetch this from an API
      } catch (err) {
        console.error('Error fetching security settings:', err);
        setError('Failed to load security settings. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSecuritySettings();
  }, []);

  const handleSaveSettings = async () => {
    try {
      setIsLoading(true);
      // In a real implementation, this would be an API call to save security settings
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('Security settings saved successfully!');
    } catch (err) {
      console.error('Error saving security settings:', err);
      setError('Failed to save security settings. Please try again later.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AdminPageLayout
      title="Security Settings"
      description="Configure platform security settings and policies."
    >
      {error && (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mb-6">
          <p className="text-red-200">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 gap-6">
        {/* Authentication Settings */}
        <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white">Authentication Settings</CardTitle>
            <CardDescription className="text-gray-400">Configure authentication methods and policies</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-white">Multi-Factor Authentication</label>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={securitySettings.mfaEnabled}
                  onChange={(e) => setSecuritySettings({...securitySettings, mfaEnabled: e.target.checked})}
                  className="mr-2 h-4 w-4"
                  disabled={isLoading}
                />
                <span className="text-gray-300">Required for all users</span>
              </div>
            </div>

            <div>
              <h3 className="text-white font-medium mb-2">Password Policy</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-gray-300 block mb-1">Minimum Length</label>
                  <input
                    type="number"
                    value={securitySettings.passwordPolicy.minLength}
                    onChange={(e) => setSecuritySettings({
                      ...securitySettings,
                      passwordPolicy: {
                        ...securitySettings.passwordPolicy,
                        minLength: parseInt(e.target.value)
                      }
                    })}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                    disabled={isLoading}
                  />
                </div>
                <div>
                  <label className="text-gray-300 block mb-1">Password Expiry (days)</label>
                  <input
                    type="number"
                    value={securitySettings.passwordPolicy.passwordExpiryDays}
                    onChange={(e) => setSecuritySettings({
                      ...securitySettings,
                      passwordPolicy: {
                        ...securitySettings.passwordPolicy,
                        passwordExpiryDays: parseInt(e.target.value)
                      }
                    })}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                    disabled={isLoading}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mt-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={securitySettings.passwordPolicy.requireUppercase}
                    onChange={(e) => setSecuritySettings({
                      ...securitySettings,
                      passwordPolicy: {
                        ...securitySettings.passwordPolicy,
                        requireUppercase: e.target.checked
                      }
                    })}
                    className="mr-2 h-4 w-4"
                    disabled={isLoading}
                  />
                  <span className="text-gray-300">Require uppercase</span>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={securitySettings.passwordPolicy.requireLowercase}
                    onChange={(e) => setSecuritySettings({
                      ...securitySettings,
                      passwordPolicy: {
                        ...securitySettings.passwordPolicy,
                        requireLowercase: e.target.checked
                      }
                    })}
                    className="mr-2 h-4 w-4"
                    disabled={isLoading}
                  />
                  <span className="text-gray-300">Require lowercase</span>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={securitySettings.passwordPolicy.requireNumbers}
                    onChange={(e) => setSecuritySettings({
                      ...securitySettings,
                      passwordPolicy: {
                        ...securitySettings.passwordPolicy,
                        requireNumbers: e.target.checked
                      }
                    })}
                    className="mr-2 h-4 w-4"
                    disabled={isLoading}
                  />
                  <span className="text-gray-300">Require numbers</span>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={securitySettings.passwordPolicy.requireSpecialChars}
                    onChange={(e) => setSecuritySettings({
                      ...securitySettings,
                      passwordPolicy: {
                        ...securitySettings.passwordPolicy,
                        requireSpecialChars: e.target.checked
                      }
                    })}
                    className="mr-2 h-4 w-4"
                    disabled={isLoading}
                  />
                  <span className="text-gray-300">Require special characters</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Session Settings */}
        <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white">Session Settings</CardTitle>
            <CardDescription className="text-gray-400">Configure user session behavior</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-gray-300 block mb-1">Session Timeout (minutes)</label>
                <input
                  type="number"
                  value={securitySettings.sessionSettings.sessionTimeout}
                  onChange={(e) => setSecuritySettings({
                    ...securitySettings,
                    sessionSettings: {
                      ...securitySettings.sessionSettings,
                      sessionTimeout: parseInt(e.target.value)
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                  disabled={isLoading}
                />
              </div>
              <div>
                <label className="text-gray-300 block mb-1">Max Concurrent Sessions</label>
                <input
                  type="number"
                  value={securitySettings.sessionSettings.maxConcurrentSessions}
                  onChange={(e) => setSecuritySettings({
                    ...securitySettings,
                    sessionSettings: {
                      ...securitySettings.sessionSettings,
                      maxConcurrentSessions: parseInt(e.target.value)
                    }
                  })}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                  disabled={isLoading}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* IP Restrictions */}
        <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white">IP Restrictions</CardTitle>
            <CardDescription className="text-gray-400">Restrict access by IP address</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-white">Enable IP Restrictions</label>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={securitySettings.ipRestrictions.enabled}
                  onChange={(e) => setSecuritySettings({
                    ...securitySettings,
                    ipRestrictions: {
                      ...securitySettings.ipRestrictions,
                      enabled: e.target.checked
                    }
                  })}
                  className="mr-2 h-4 w-4"
                  disabled={isLoading}
                />
                <span className="text-gray-300">Restrict access to allowed IPs only</span>
              </div>
            </div>

            <div>
              <label className="text-gray-300 block mb-1">Allowed IP Addresses (one per line)</label>
              <textarea
                value={securitySettings.ipRestrictions.allowedIPs.join('\n')}
                onChange={(e) => setSecuritySettings({
                  ...securitySettings,
                  ipRestrictions: {
                    ...securitySettings.ipRestrictions,
                    allowedIPs: e.target.value.split('\n').filter(ip => ip.trim() !== '')
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white h-32"
                placeholder="Enter IP addresses, one per line"
                disabled={isLoading || !securitySettings.ipRestrictions.enabled}
              />
            </div>
          </CardContent>
        </Card>

        {/* Audit Logging */}
        <Card className="bg-gray-800/70 backdrop-blur-lg border border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-white">Audit Logging</CardTitle>
            <CardDescription className="text-gray-400">Configure audit logging settings</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="text-white">Enable Audit Logging</label>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={securitySettings.auditLogging.enabled}
                  onChange={(e) => setSecuritySettings({
                    ...securitySettings,
                    auditLogging: {
                      ...securitySettings.auditLogging,
                      enabled: e.target.checked
                    }
                  })}
                  className="mr-2 h-4 w-4"
                  disabled={isLoading}
                />
                <span className="text-gray-300">Log all user actions</span>
              </div>
            </div>

            <div>
              <label className="text-gray-300 block mb-1">Log Retention Period (days)</label>
              <input
                type="number"
                value={securitySettings.auditLogging.retentionDays}
                onChange={(e) => setSecuritySettings({
                  ...securitySettings,
                  auditLogging: {
                    ...securitySettings.auditLogging,
                    retentionDays: parseInt(e.target.value)
                  }
                })}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                disabled={isLoading || !securitySettings.auditLogging.enabled}
              />
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end">
          <button
            onClick={handleSaveSettings}
            disabled={isLoading}
            className="bg-purple-600 hover:bg-purple-700 text-white py-2 px-6 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Save Security Settings'}
          </button>
        </div>
      </div>
    </AdminPageLayout>
  );
}
