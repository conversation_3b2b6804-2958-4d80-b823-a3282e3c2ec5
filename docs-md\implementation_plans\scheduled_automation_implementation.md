# Scheduled Automation Implementation Plan

## Overview
This document outlines the implementation plan for the Scheduled Automation UI within the CallSaver.app dashboard. This feature allows users to create, configure, and monitor automated call and SMS sequences based on schedules and conditions.

## Component Hierarchy
```mermaid
graph TD
    A[ScheduledAutomationPage] --> B(AutomationList);
    A --> C(AutomationEditor{props: automation, isOpen, onClose, onSave});
    A --> D(AutomationAnalytics);

    B --> B1(AutomationListItem{props: automation, onEdit, onDelete, onToggle});

    C --> C1(BasicInfoForm{props: name, description, type: 'call'|'sms'});
    C --> C2(ScheduleConfigurator{props: schedule, onChange});
    C --> C3(ConditionBuilder{props: conditions, onChange});
    C --> C4(TemplateSelector{props: templateType, selectedTemplate, onChange});
    C --> C5(ActionPreview{props: automationConfig});

    D --> D1(ExecutionHistoryTable{props: historyData});
    D --> D2(PerformanceChart{props: chartData});
```

*   **ScheduledAutomationPage**: Main container for the scheduled automation feature.
    *   State: `automations`, `selectedAutomation`, `isEditorOpen`, `viewMode` ('list' | 'analytics'), `isLoading`
*   **AutomationList**: Displays the list of created automations.
    *   **AutomationListItem**: Represents a single automation in the list. Props: `automation` (name, status, schedule summary, type), `onEdit(id)`, `onDelete(id)`, `onToggle(id, isEnabled)`. Includes a toggle switch for enabling/disabling.
*   **AutomationEditor**: Modal or dedicated view for creating/editing an automation. Props: `automation` (optional, for editing), `isOpen`, `onClose`, `onSave(automationData)`.
    *   State: `currentAutomationData`, `formStep`
    *   **BasicInfoForm**: For setting name, description, and type (Call/SMS). Props: `name`, `description`, `type`.
    *   **ScheduleConfigurator**: UI for setting the schedule (e.g., specific date/time, recurring pattern like cron). Props: `schedule`, `onChange(newSchedule)`. Could use a library for cron generation.
    *   **ConditionBuilder**: Interface to define rules for when the automation should run (e.g., based on contact properties, integration events). Props: `conditions`, `onChange(newConditions)`. Needs a flexible rule engine UI.
    *   **TemplateSelector**: Dropdown/search to select pre-defined message or call script templates. Props: `templateType` ('sms' | 'call'), `selectedTemplate`, `onChange(templateId)`. Links to a separate Template Management section.
    *   **ActionPreview**: Shows a summary or preview of the configured call/SMS action. Props: `automationConfig`.
*   **AutomationAnalytics**: Section to display monitoring and analytics for automations.
    *   **ExecutionHistoryTable**: Table showing past and upcoming executions. Props: `historyData` (timestamp, status, target, result).
    *   **PerformanceChart**: Visual representation of automation performance (e.g., completion rates, errors over time). Props: `chartData`.

## Data Flow
1.  **Load Automations**: `ScheduledAutomationPage` fetches the list of automations (`/api/automations`) via TanStack Query.
2.  **Create/Edit Automation**: User clicks "Create New" or "Edit" on an `AutomationListItem`. `AutomationEditor` opens. User fills in details across steps/forms (`BasicInfoForm`, `ScheduleConfigurator`, `ConditionBuilder`, `TemplateSelector`). `onSave` sends data (`POST /api/automations` or `PUT /api/automations/{id}`) to the backend. List is refreshed.
3.  **Toggle Automation**: User clicks the toggle switch on `AutomationListItem`. `onToggle` calls backend (`PUT /api/automations/{id}/status`) to enable/disable. UI updates immediately.
4.  **Delete Automation**: User clicks "Delete". Confirmation shown. `onDelete` calls backend (`DELETE /api/automations/{id}`). List is refreshed.
5.  **View Analytics**: User switches `viewMode` to 'analytics'. `AutomationAnalytics` fetches execution history (`/api/automations/history`) and performance data (`/api/automations/stats`) via TanStack Query.

## API Integration
*   `GET /api/automations`: Fetch list of all scheduled automations. Returns: `[{ id, name, type, status: 'enabled'|'disabled', scheduleSummary, createdAt, lastRun?, nextRun? }]`.
*   `POST /api/automations`: Create a new automation. Body: `{ name, description, type, schedule: {...}, conditions: [...], templateId, isEnabled }`. Returns: `{ id, ... }`.
*   `GET /api/automations/{id}`: Fetch details for a specific automation. Returns: `{ id, name, description, type, schedule, conditions, templateId, isEnabled, ... }`.
*   `PUT /api/automations/{id}`: Update an existing automation. Body: `{ name?, description?, type?, schedule?, conditions?, templateId?, isEnabled? }`. Returns: `{ success: true }`.
*   `PUT /api/automations/{id}/status`: Enable/disable an automation. Body: `{ isEnabled: true|false }`. Returns: `{ success: true }`.
*   `DELETE /api/automations/{id}`: Delete an automation. Returns: `{ success: true }`.
*   `GET /api/automations/history`: Fetch execution history (paginated). Query params: `?page=1&limit=20&automationId=...&status=...`. Returns: `{ data: [{ id, automationId, timestamp, status: 'success'|'failed'|'pending', target, result, error? }], totalPages }`.
*   `GET /api/automations/stats`: Fetch performance statistics. Query params: `?timeRange=...&automationId=...`. Returns: `{ successRate, totalExecutions, errorCount, chartData: [...] }`.
*   `GET /api/templates`: Fetch list of available templates (used by `TemplateSelector`). Query params: `?type=sms|call`. Returns: `[{ id, name, preview }]`.

## State Management
*   **TanStack Query**:
    *   Fetching automation list (`/api/automations`).
    *   Fetching specific automation details (`/api/automations/{id}`).
    *   Fetching execution history (`/api/automations/history`).
    *   Fetching stats (`/api/automations/stats`).
    *   Fetching templates (`/api/templates`).
    *   Mutations for create, update, delete, toggle status.
*   **Zustand**:
    *   Managing editor visibility (`isEditorOpen`).
    *   Storing the currently selected automation for editing.
    *   Local form state within `AutomationEditor` across multiple steps.
    *   UI view mode (`viewMode`).

## User Interactions
*   **Viewing List**: User sees a list of automations with key details and status.
*   **Creating/Editing**: User interacts with a multi-step form/modal to define all aspects of the automation.
*   **Scheduling**: User uses a calendar/cron UI to set the schedule.
*   **Defining Conditions**: User uses a visual builder to add/remove/group conditions.
*   **Selecting Template**: User selects from a list of pre-made templates.
*   **Enabling/Disabling**: User clicks a toggle switch for quick activation/deactivation.
*   **Monitoring**: User views tables and charts showing past performance and upcoming runs.

## Error Handling
*   **API Errors**: Use TanStack Query error states and toasts for feedback on data fetching and mutations.
*   **Validation Errors**: Implement frontend validation (e.g., react-hook-form/Zod) for the `AutomationEditor` forms. Backend must also validate thoroughly.
*   **Scheduling Conflicts**: Backend should validate schedule logic. UI could potentially warn about overlapping or invalid schedules.
*   **Condition Logic Errors**: Provide clear feedback if condition logic is invalid or cannot be evaluated.
*   **Execution Errors**: Clearly display errors in the `ExecutionHistoryTable`. Backend needs robust error logging for failed automation runs.

## Implementation Notes
*   **Scheduling Engine**: Backend requires a robust job scheduler (e.g., node-cron, BullMQ with delayed jobs, or a dedicated workflow engine).
*   **Condition Engine**: Backend needs a system to evaluate conditions based on contact data, integration events, etc.
*   **Template Management**: Assumes a separate UI/system exists for creating and managing SMS/call templates. `TemplateSelector` interacts with this.
*   **Cron UI**: Use a library like `react-cron-generator` to provide a user-friendly way to create cron expressions.
*   **Condition Builder UI**: This can be complex. Consider libraries designed for building rule engines visually, or start with a simpler structure.
*   **Analytics**: Data aggregation for analytics might require specific backend processing or database queries. Consider performance implications.
*   **Timezones**: Handle timezones carefully in scheduling and reporting. Store schedules/timestamps in UTC on the backend.

## Related Components
*   **DashboardLayout**: Overall structure.
*   **NotificationSystem**: For toasts.
*   **AuthenticationContext**: User context.
*   **Template Management UI**: Separate section for creating/managing templates.
*   **Contact Management**: Conditions might rely on contact data.
*   **External Integrations**: Conditions might rely on integration events/data.

## Link to prompt_to_mdc_router.mdc
*   **Primary Purpose**: Defines the implementation plan for the Scheduled Automation UI.
*   **Frontend Components**: `ScheduledAutomationPage`, `AutomationList`, `AutomationEditor`, `AutomationAnalytics`, and their sub-components.
*   **API Endpoints**: `/api/automations`, `/api/automations/{id}`, `/api/automations/{id}/status`, `/api/automations/history`, `/api/automations/stats`, `/api/templates`.
*   **Dependencies**: Assumes backend API endpoints, scheduling engine, condition evaluation logic, and template management system are implemented. May depend on functional specs related to task queuing or automation workflows.
