'use client';

import { useState } from 'react';
import { useSecurity } from '../../../hooks/useSettings';
import PasswordChangeForm from './PasswordChangeForm';
import MfaSetupForm from './MfaSetupForm';
import SessionsManager from './SessionsManager';
import LoadingSpinner from '../shared/LoadingSpinner';
import ErrorMessage from '../shared/ErrorMessage';

export default function SecuritySettingsPanel() {
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  
  // Fetch security data
  const { 
    data: security, 
    isLoading, 
    isError, 
    error 
  } = useSecurity();
  
  // Handle success message
  const handleSuccess = (message: string) => {
    setSuccessMessage(message);
    
    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }
  
  if (isError) {
    return (
      <ErrorMessage 
        title="Failed to load security settings" 
        message="We couldn't load your security information. Please try again later."
        error={error instanceof Error ? error : undefined}
        onRetry={() => window.location.reload()}
      />
    );
  }
  
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Security Settings</h2>
      
      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg text-green-800 dark:text-green-400">
          {successMessage}
        </div>
      )}
      
      <div className="space-y-8">
        {/* Password Change */}
        <div className="pb-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Password</h3>
          
          {security?.passwordLastChanged && (
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
              Last changed: {new Date(security.passwordLastChanged).toLocaleDateString()}
            </p>
          )}
          
          <PasswordChangeForm 
            onSuccess={() => handleSuccess('Password changed successfully')}
          />
        </div>
        
        {/* Two-Factor Authentication */}
        <div className="pb-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Two-Factor Authentication</h3>
          
          <MfaSetupForm 
            isMfaEnabled={security?.isMfaEnabled || false}
            onSuccess={(enabled) => handleSuccess(
              enabled 
                ? 'Two-factor authentication enabled successfully' 
                : 'Two-factor authentication disabled successfully'
            )}
          />
        </div>
        
        {/* Active Sessions */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Active Sessions</h3>
          
          <SessionsManager 
            onSuccess={() => handleSuccess('Session terminated successfully')}
          />
        </div>
      </div>
    </div>
  );
}
