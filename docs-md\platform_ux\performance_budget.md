---
description: Defines performance budgets for key frontend metrics to ensure a fast user experience.
---
# Frontend Performance Budget (`performance_budget.mdc`)

## 1. Purpose and Scope

**Purpose:** To define specific, measurable performance budgets for the CallSaver frontend application. These budgets act as constraints during development and automated checks in CI/CD to prevent performance regressions and ensure a consistently fast user experience.

**Scope:**
- Key performance metrics to track (Core Web Vitals, load times, bundle sizes).
- Budget targets for these metrics under specific conditions (e.g., network speed, device type).
- Tools and methods for monitoring performance against the budget.
- Processes for enforcing the budget (CI checks, build analysis).
- Strategies for addressing budget violations.

## 2. Key Performance Metrics and Budgets

Budgets should be defined for representative user experiences (e.g., initial load, dashboard interaction) under specific conditions (e.g., "Slow 4G" network, mid-range mobile device). Targets should be reviewed and adjusted periodically.

### 2.1. Core Web Vitals (Target: "Good" Thresholds)

- **Largest Contentful Paint (LCP):** Measures loading performance.
    - **Budget:** <= 2.5 seconds (on Slow 4G)
- **First Input Delay (FID) / Interaction to Next Paint (INP):** Measures interactivity. (INP is replacing FID).
    - **Budget (FID):** <= 100 milliseconds
    - **Budget (INP):** <= 200 milliseconds
- **Cumulative Layout Shift (CLS):** Measures visual stability.
    - **Budget:** <= 0.1

### 2.2. Load Metrics

- **Time to Interactive (TTI):** Time until the page is fully interactive.
    - **Budget:** <= 5 seconds (on Slow 4G)
- **First Contentful Paint (FCP):** Time until the first piece of content is rendered.
    - **Budget:** <= 1.8 seconds (on Slow 4G)

### 2.3. Resource Budgets (Initial Load - Critical Path)

These budgets help control the amount of code shipped to the user.

- **Total JavaScript Size (gzipped):**
    - **Budget:** <= 250 KB (Consider separate budgets for critical vs. deferred JS)
- **Critical CSS Size (gzipped):**
    - **Budget:** <= 50 KB
- **Image Sizes:** Optimize images aggressively. Avoid large, unoptimized images above the fold. Set guidelines rather than strict KB budgets per image, but monitor total image weight per page.
    - **Guideline:** Total image weight (above fold) <= 300 KB
- **Total Request Count:**
    - **Budget:** <= 50 requests (for initial load)

*Note: These are example starting points and should be refined based on baseline measurements and application specifics.*

## 3. Monitoring and Measurement

- **Real User Monitoring (RUM):** Use frontend monitoring tools (e.g., Vercel Analytics, Datadog RUM, Sentry Performance) to collect Core Web Vitals and other metrics from actual user sessions. This provides the most accurate picture of real-world performance.
- **Synthetic Testing (Lab Data):**
    - **Tools:** Lighthouse (in Chrome DevTools, CLI, CI), WebPageTest, Playwright/Cypress scripts measuring performance APIs.
    - **Purpose:** Run automated tests in controlled environments (e.g., CI pipeline, periodic checks) to catch regressions before they reach users. Measure load metrics, resource sizes, and Lighthouse scores.
- **Build Analysis Tools:** Use tools like `webpack-bundle-analyzer` or Next.js build output analysis to monitor JavaScript bundle sizes and composition during development and in CI.

## 4. Budget Enforcement

- **CI/CD Integration:**
    - Run Lighthouse CLI or similar synthetic tests automatically in the CI pipeline for key pages/flows. Fail the build if budgets for critical metrics (e.g., LCP, TTI, JS bundle size) are exceeded.
    - Integrate bundle size checks (e.g., using `size-limit` or custom scripts) into the CI pipeline. Fail the build if JavaScript bundle budgets are violated.
- **Pull Request Checks:** Display performance budget results (Lighthouse scores, bundle size changes) directly on pull requests to inform developers before merging.
- **Performance Dashboards:** Maintain dashboards (using RUM data and synthetic test results) to track performance trends over time and identify regressions.

## 5. Addressing Violations

- **Prioritization:** Treat performance budget violations like functional bugs. Prioritize fixing regressions that significantly impact user experience or violate Core Web Vitals budgets.
- **Root Cause Analysis:** Use monitoring data, profilers (React DevTools Profiler, browser performance tools), and build analysis tools to identify the cause of the regression.
- **Optimization Techniques:** Apply relevant optimization techniques (code splitting, lazy loading, image optimization, reducing third-party scripts, caching, optimizing critical rendering path, etc.).
- **Budget Adjustments:** If a violation is deemed necessary and justified (e.g., essential new feature significantly increases bundle size), consciously adjust the budget and document the reason. Avoid arbitrary budget increases.

## 6. Related Documents

- `docs/dev_guides/testing_strategy.mdc` (Performance testing section)
- `docs/functional_specs/deployment_strategy.mdc` (if exists, regarding CI/CD)
- Frontend framework documentation (e.g., Next.js performance features)
