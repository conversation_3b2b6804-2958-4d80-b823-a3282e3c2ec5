'use client';

import { useState } from 'react';
import { Tab } from '@headlessui/react';
import TrainingDataManager from '../../../components/ai-training/TrainingDataManager';
import VoiceTester from '../../../components/ai-training/VoiceTester';
import VoiceModelSelector from '../../../components/ai-training/VoiceModelSelector';
import KnowledgeBaseManager from '../../../components/ai-training/KnowledgeBaseManager';
import CustomCommandConfigurator from '../../../components/ai-training/CustomCommandConfigurator';
import PromptTemplateEditor from '../../../components/ai-training/PromptTemplateEditor';

const tabs = [
  { name: 'Training Data', component: TrainingDataManager },
  { name: 'Voice Testing', component: VoiceTester },
  { name: 'Voice Models', component: VoiceModelSelector },
  { name: 'Knowledge Base', component: KnowledgeBaseManager },
  { name: 'Custom Commands', component: CustomCommandConfigurator },
  { name: 'Prompt Templates', component: PromptTemplateEditor },
];

export default function AITrainingPage() {
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          AI Training
        </h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          Train and configure your AI assistant to handle calls and messages more effectively.
        </p>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        <Tab.Group selectedIndex={selectedTabIndex} onChange={setSelectedTabIndex}>
          <Tab.List className="flex border-b border-gray-200 dark:border-gray-700">
            {tabs.map((tab, index) => (
              <Tab
                key={index}
                className={({ selected }) =>
                  `flex-1 py-3 px-4 text-sm font-medium text-center focus:outline-none ${
                    selected
                      ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                      : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`
                }
              >
                {tab.name}
              </Tab>
            ))}
          </Tab.List>
          <Tab.Panels className="p-4 sm:p-6">
            {tabs.map((tab, index) => (
              <Tab.Panel key={index}>
                <tab.component />
              </Tab.Panel>
            ))}
          </Tab.Panels>
        </Tab.Group>
      </div>
    </div>
  );
}
