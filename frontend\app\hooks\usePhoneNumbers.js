"use client";

import { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { useToast } from '@/app/hooks/useToast';

// API base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3005/api';

// Create context
const PhoneNumberContext = createContext(null);

export const PhoneNumberProvider = ({ children }) => {
  const [availableNumbers, setAvailableNumbers] = useState([]);
  const [userNumbers, setUserNumbers] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState('US');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedNumber, setSelectedNumber] = useState(null);
  const toast = useToast();

  // Countries supported for phone number purchase
  const supportedCountries = [
    { code: 'US', name: 'United States', flag: '🇺🇸' },
    { code: 'CA', name: 'Canada', flag: '🇨🇦' },
    { code: 'GB', name: 'United Kingdom', flag: '🇬🇧' },
    { code: 'AU', name: 'Australia', flag: '🇦🇺' },
    { code: 'DE', name: 'Germany', flag: '🇩🇪' },
    { code: 'FR', name: 'France', flag: '🇫🇷' },
    { code: 'ES', name: 'Spain', flag: '🇪🇸' },
    { code: 'IT', name: 'Italy', flag: '🇮🇹' },
    { code: 'JP', name: 'Japan', flag: '🇯🇵' },
  ];

  // Function to get JWT token - can be expanded based on auth system
  const getAuthToken = () => {
    // In a real app, you'd get this from localStorage, cookies, or state
    // For development, we'll use a mock token
    const devToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.hL2-nMJlsQQZKJN4_2pyS38sTn_9zVptQON4SgZ9xLI';
    return localStorage.getItem('authToken') || devToken;
  };

  // Get headers for API requests
  const getHeaders = () => {
    const token = getAuthToken();
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    };
  };

  // Fetch available phone numbers for a country
  const fetchAvailableNumbers = async (country = selectedCountry) => {
    setIsLoading(true);
    try {
      const response = await axios.get(`${API_BASE_URL}/numbers/available?country=${country}`, {
        headers: getHeaders()
      });
      
      if (response.data.status === 'success') {
        setAvailableNumbers(response.data.data.numbers);
      } else {
        throw new Error(response.data.message || 'Failed to fetch numbers');
      }
    } catch (error) {
      console.error('Error fetching available numbers:', error);
      toast.error('Failed to fetch available numbers: ' + (error.response?.data?.message || error.message));
      setAvailableNumbers([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch user's phone numbers
  const fetchUserNumbers = async () => {
    setIsLoading(true);
    try {
      const response = await axios.get(`${API_BASE_URL}/numbers/mine`, {
        headers: getHeaders()
      });
      
      if (response.data.status === 'success') {
        setUserNumbers(response.data.data.phoneNumbers);
      } else {
        throw new Error(response.data.message || 'Failed to fetch your numbers');
      }
    } catch (error) {
      console.error('Error fetching user numbers:', error);
      toast.error('Failed to fetch your numbers: ' + (error.response?.data?.message || error.message));
      setUserNumbers([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Purchase a specific phone number
  const purchaseNumber = async (phoneNumber) => {
    setIsLoading(true);
    try {
      const response = await axios.post(`${API_BASE_URL}/numbers/purchase`, {
        phoneNumber,
        countryCode: selectedCountry
      }, {
        headers: getHeaders()
      });
      
      if (response.data.status === 'success') {
        toast.success('Phone number purchased successfully!');
        await fetchUserNumbers(); // Refresh the user's numbers
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to purchase number');
      }
    } catch (error) {
      console.error('Error purchasing number:', error);
      toast.error('Failed to purchase number: ' + (error.response?.data?.message || error.message));
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Purchase a random phone number
  const purchaseRandomNumber = async () => {
    setIsLoading(true);
    try {
      const response = await axios.post(`${API_BASE_URL}/numbers/purchase-random`, {
        countryCode: selectedCountry
      }, {
        headers: getHeaders()
      });
      
      if (response.data.status === 'success') {
        toast.success('Random phone number purchased successfully!');
        await fetchUserNumbers(); // Refresh the user's numbers
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to purchase random number');
      }
    } catch (error) {
      console.error('Error purchasing random number:', error);
      toast.error('Failed to purchase random number: ' + (error.response?.data?.message || error.message));
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Change the selected country and fetch available numbers
  const changeCountry = async (countryCode) => {
    setSelectedCountry(countryCode);
    await fetchAvailableNumbers(countryCode);
  };

  // Select a specific number
  const selectNumber = (number) => {
    setSelectedNumber(number);
  };

  // Context value
  const value = {
    availableNumbers,
    userNumbers,
    selectedCountry,
    supportedCountries,
    isLoading,
    selectedNumber,
    fetchAvailableNumbers,
    fetchUserNumbers,
    purchaseNumber,
    purchaseRandomNumber,
    changeCountry,
    selectNumber
  };

  return (
    <PhoneNumberContext.Provider value={value}>
      {children}
    </PhoneNumberContext.Provider>
  );
};

// Custom hook to use the phone number context
export const usePhoneNumbers = () => {
  const context = useContext(PhoneNumberContext);
  if (!context) {
    throw new Error('usePhoneNumbers must be used within a PhoneNumberProvider');
  }
  return context;
};

export default usePhoneNumbers; 