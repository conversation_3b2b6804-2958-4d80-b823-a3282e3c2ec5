---
description:
globs:
alwaysApply: false
---
# Number Management UI Implementation Plan (`number_management_ui_implementation.mdc`)

## 1. Overview

This document outlines the frontend implementation plan for the CallSaver Number Management section. It details components, data fetching, state management, user interactions, and error handling based on `number_management_section_document.mdc` and `component_state_mapping.mdc`. This section allows users to search, purchase, manage, and release phone numbers and eSIMs.

## 2. Component Hierarchy

The Number Management UI will be built using React components within the Next.js app router structure (e.g., `app/numbers/page.tsx`). Components will reside in `front/mainpage/components/number-management/`.

```
/components
  /number-management
    NumberManagementLayout.tsx    # Main layout, potentially tabs for Owned/Search/eSIMs.
    NumberSearchForm.tsx          # Form for searching available numbers. Props: onSubmit, isSearching.
    AvailableNumbersList.tsx      # Displays search results. Props: numbers[], isLoading, error, onPurchaseClick.
      AvailableNumberItem.tsx     # Represents a single available number. Props: numberData, onPurchaseClick.
    OwnedNumbersList.tsx          # Fetches and displays the user's current numbers. Props: isLoading, error, numbers[], onConfigClick, onReleaseClick.
      OwnedNumberItem.tsx         # Represents a single owned number. Props: numberData, onConfigClick, onReleaseClick.
    NumberConfigModal.tsx         # Modal/Panel for editing number settings (e.g., friendly name). Props: numberData, isOpen, onClose, onSave. Uses useMutation.
    PurchaseConfirmationDialog.tsx # Modal dialog to confirm number purchase. Props: numberData, isOpen, onClose, onConfirm, isPurchasing.
    ReleaseConfirmationDialog.tsx  # Modal dialog to confirm number release. Props: numberData, isOpen, onClose, onConfirm, isReleasing.
    NumberManagementSkeleton.tsx  # Loading state placeholder for lists or search results.
    ESimManagementPanel.tsx       # (If applicable) Specific UI for eSIMs (Search, Purchase, Owned List). May reuse/adapt other components.
  /shared                       # Shared components
    LoadingSpinner.tsx
    ErrorMessage.tsx
    EmptyState.tsx
    Modal.tsx
    Dialog.tsx
    TextInput.tsx
    Select.tsx
    Button.tsx
    Table.tsx / List components
    ToastNotification.tsx
```

## 3. Data Sources & Backend Integration

React Query (`@tanstack/react-query`) will manage server state.

| Component / Feature         | API Endpoint                                                                 | Method | React Query Hook        | Query Key                                                    | Data Shape (Expected)                                                                                                                                                           | Notes                                                                                                                                                                                             |
| :-------------------------- | :--------------------------------------------------------------------------- | :----- | :---------------------- | :----------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **Search Available Numbers**| `GET /api/numbers/available?country=...&areaCode=...&capabilities=...&type=...` | GET    | `useQuery`              | `['numbers', 'available', searchParams]`                     | `[{ id: string, phoneNumber: string, location: string, capabilities: string[], type: string, cost: { setup: number, monthly: number } }, ...]`                               | Triggered manually or on param change in `NumberSearchForm`. `enabled: false` initially.                                                                                                          |
| **Purchase Number**         | `POST /api/numbers/purchase`                                                 | POST   | `useMutation`           | -                                                            | Input: `{ numberIdentifier: string }`. Output: Success/Error + purchased number details.                                                                                        | Triggered from `PurchaseConfirmationDialog`. Invalidates `['numbers', 'owned']` and potentially `['user', 'balance']` on success.                                                              |
| **List Owned Numbers**      | `GET /api/numbers/owned?page=...&limit=...`                                  | GET    | `useQuery` / `useInfiniteQuery` | `['numbers', 'owned', { pagination }]`                       | `[{ id: string, phoneNumber: string, friendlyName: string, status: string, type: string, capabilities: string[], acquiredDate: string, automationLink?: string }, ...]` (Paginated) | Fetched in `OwnedNumbersList`.                                                                                                                                                    |
| **Update Number Settings**  | `PUT /api/numbers/{numberId}`                                                | PUT    | `useMutation`           | -                                                            | Input: `{ friendlyName?: string, ... }`. Output: Success/Error + updated number details.                                                                                       | Triggered from `NumberConfigModal`. Invalidates `['numbers', 'owned']` on success.                                                                                                                |
| **Release Number**          | `DELETE /api/numbers/{numberId}`                                             | DELETE | `useMutation`           | -                                                            | Input: `{ numberId }`. Output: Success/Error.                                                                                                                                   | Triggered from `ReleaseConfirmationDialog`. Invalidates `['numbers', 'owned']` on success.                                                                                                        |
| **Search Available eSIMs**  | `GET /api/esims/available?country=...&region=...`                            | GET    | `useQuery`              | `['esims', 'available', searchParams]`                       | `[{ id: string, planName: string, data: string, validity: string, country: string, cost: number }, ...]`                                                                        | (eSIM Specific) Triggered in `ESimManagementPanel`.                                                                                                                               |
| **Purchase eSIM**           | `POST /api/esims/purchase`                                                   | POST   | `useMutation`           | -                                                            | Input: `{ planId: string }`. Output: Success/Error + eSIM details/installation info.                                                                                            | (eSIM Specific) Triggered in `ESimManagementPanel`. Invalidates `['esims', 'owned']`.                                                                                             |
| **List Owned eSIMs**        | `GET /api/esims/owned`                                                       | GET    | `useQuery`              | `['esims', 'owned']`                                         | `[{ id: string, planName: string, status: string, ... }, ...]`                                                                                                                  | (eSIM Specific) Triggered in `ESimManagementPanel`.                                                                                                                               |
| **Install eSIM**            | `POST /api/esims/{esimId}/install`                                           | POST   | `useMutation`           | -                                                            | Input: `{ esimId }`. Output: Success/Error + QR code data / instructions.                                                                                                       | (eSIM Specific) Triggered in `ESimManagementPanel`.                                                                                                                               |

**Backend Integration Notes:**
- Use `useMutation` for `POST`, `PUT`, `DELETE`.
- Invalidate relevant `useQuery` caches on successful mutations using `queryClient.invalidateQueries`.
- Handle authentication via HttpOnly cookies.
- Display loading states (`isPending`) during mutations.

## 4. UI State Management

- **Server State (React Query):**
    - Manages all data related to available numbers, owned numbers, eSIMs, and the results of purchase/release/update actions.
    - `useQuery` for fetching lists and search results.
    - `useMutation` for purchase, release, and update operations.
    - `isLoading`, `isError`, `error`, `data`, `isFetching`, `isPending` states drive UI feedback.
- **Global Client State (Zustand/Context):**
    - Potentially consume user's credit balance from a global store to check before enabling purchase buttons.
- **Local Component State (`useState`):**
    - `NumberSearchForm`: Manage search criteria inputs (country, area code, etc.).
    - `NumberManagementLayout`: Manage state for active tab (Owned vs. Search vs. eSIM).
    - State for controlling visibility of modals/dialogs (`NumberConfigModal`, `PurchaseConfirmationDialog`, `ReleaseConfirmationDialog`).
    - State within `NumberConfigModal` for the friendly name input.

## 5. UI/UX Behavior

- **Initial Load:** `NumberManagementLayout` likely defaults to showing the `OwnedNumbersList`. It fetches data using `useQuery`. Skeletons are shown during loading.
- **Searching:**
    - User enters criteria in `NumberSearchForm`.
    - Submitting the form triggers the `useQuery` for available numbers.
    - `AvailableNumbersList` displays results or a skeleton/loading state (`isLoading`).
    - Handles "No results found" using `EmptyState` component if `data` is an empty array.
- **Viewing Owned Numbers:** `OwnedNumbersList` displays fetched numbers, potentially with pagination controls if using `useInfiniteQuery`.
- **Purchasing:**
    - Clicking "Purchase" on an `AvailableNumberItem` opens `PurchaseConfirmationDialog`.
    - Check user balance (potentially fetched via React Query or from global state) before enabling confirm button.
    - On confirm, trigger `POST /api/numbers/purchase` mutation. Show loading state (`isPending`).
    - On success: Close dialog, show success toast, invalidate `['numbers', 'owned']` query to refresh the owned list.
    - On error: Show error toast/message in the dialog. Keep dialog open.
- **Editing Settings:**
    - Clicking "Configure" or an edit icon on `OwnedNumberItem` opens `NumberConfigModal` with the number's current data.
    - User modifies settings (e.g., friendly name).
    - Clicking "Save" triggers `PUT /api/numbers/{numberId}` mutation. Show loading state.
    - On success: Close modal, show success toast, invalidate `['numbers', 'owned']` query.
    - On error: Show error message in the modal.
- **Releasing:**
    - Clicking "Release" on `OwnedNumberItem` opens `ReleaseConfirmationDialog`.
    - On confirm, trigger `DELETE /api/numbers/{numberId}` mutation. Show loading state.
    - On success: Close dialog, show success toast, invalidate `['numbers', 'owned']` query.
    - On error: Show error toast/message in the dialog. Keep dialog open.
- **Responsiveness:** Use TailwindCSS for responsive tables/lists and forms. Search form might stack vertically on mobile.

## 6. Error Handling

- **Search Errors (`useQuery`):** If `GET /api/numbers/available` fails, display an error message within the `AvailableNumbersList` area using `ErrorMessage`.
- **Owned List Errors (`useQuery`):** If `GET /api/numbers/owned` fails, display an error message within the `OwnedNumbersList` area.
- **Purchase Errors (`useMutation`):**
    - Display specific errors from the API in the `PurchaseConfirmationDialog` or via toast: "Insufficient credits.", "Number is no longer available.", "Provider error: ...", "Purchase failed. Please try again."
- **Release Errors (`useMutation`):**
    - Display specific errors in `ReleaseConfirmationDialog` or via toast: "Failed to release number.", "Provider error: ..."
- **Update Errors (`useMutation`):**
    - Display errors within the `NumberConfigModal`.
- **Authentication Errors:** Global handler redirects to login.

## 7. AI Integration

- **Minimal:** Primarily links owned numbers to their automation configuration (handled in the Automation section).
- **Future:** Potential for AI-driven number suggestions during search based on user profile/needs.

## 8. Implementation Notes

- Follow TailwindCSS guidelines (`frontend_guidelines_document.mdc`).
- Adhere to architecture rules (`cursor_project_rules.mdc`).
- Use shared components for UI consistency.
- Ensure clear visual distinction between available and owned numbers.
- Implement accessibility for forms, lists, modals, and buttons.
- Add logging for search, purchase, release, update actions, and errors as per `number_management_section_document.mdc`.
- Handle eSIM specific flows within `ESimManagementPanel` if applicable, potentially reusing list/item/dialog components.
