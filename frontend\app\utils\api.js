// API utility functions for communicating with the backend
'use client';

import { getSupabaseClient } from './supabaseClient'; // Import the function

// Base URL for API calls - check both environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:3007';

/**
 * Makes an authenticated API request to the backend
 */
async function fetchWithAuth(endpoint, options = {}) {
  try {
    // Check for demo user first - they don't need Supabase auth
    const demoUser = typeof window !== 'undefined' ? localStorage.getItem('callsaver_demo_user') : null;
    const isDemoUser = !!demoUser;

    let token = null;

    if (!isDemoUser) {
      try {
        // Get the client instance - will always return something (real client or mock)
        const supabase = getSupabaseClient();

        // Check if supabase client is valid before trying to use it
        if (supabase && typeof supabase.auth === 'object' && typeof supabase.auth.getSession === 'function') {
          try {
            // Try to get the session from Supabase
            const { data, error } = await supabase.auth.getSession();

            if (error) {
              console.warn('Warning getting auth session:', error);
              // Continue without a token instead of throwing
            } else if (data?.session?.access_token) {
              token = data.session.access_token;
              console.log(`[fetchWithAuth] Got valid token for ${endpoint}`);
            } else {
              console.warn(`[fetchWithAuth] No session found for request to ${endpoint}`);
            }
          } catch (sessionError) {
            // Catch any unexpected errors from getSession
            console.error('Unexpected error getting auth session:', sessionError);
            // Continue without a token instead of throwing
          }
        } else {
          console.warn(`[fetchWithAuth] Invalid Supabase client for request to ${endpoint}`);
        }
      } catch (clientError) {
        console.error(`[fetchWithAuth] Error getting Supabase client: ${clientError.message}`);
        // Continue without a token instead of throwing
      }
    } else {
      console.log(`[fetchWithAuth] Using demo user for ${endpoint}`);
      // Demo users might need special handling here if required
    }

    // Set up headers with authentication
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Always try to use the token if available
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    } else {
      // If no token, the request might fail on protected routes,
      // or the backend might handle it (like public routes)
      console.warn(`[fetchWithAuth] No access token for request to ${endpoint}`);
      // We'll attempt the request anyway and let the server decide
    }

    // Make the request
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers,
    });

    // Handle common status codes
    if (response.status === 401) {
      console.warn(`[fetchWithAuth] Unauthorized (401) response from ${endpoint}`);

      // Only redirect if in browser context
      if (typeof window !== 'undefined') {
        // Check for demo user first - don't redirect them
        if (!isDemoUser) {
          console.log('[fetchWithAuth] Redirecting to signin due to 401');
          // Redirect to login page with a slight delay to allow logs to be seen
          setTimeout(() => {
            window.location.href = '/signin';
          }, 100);
        }
      }

      throw new Error('Authentication required or expired');
    }

    // Handle non-200 responses
    if (!response.ok) {
      let errorMessage = `HTTP error! Status: ${response.status}`;

      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch (parseError) {
        // Couldn't parse error response as JSON, use default message
      }

      throw new Error(errorMessage);
    }

    // Parse JSON response
    try {
      const data = await response.json();
      return data;
    } catch (parseError) {
      console.error(`[fetchWithAuth] Error parsing JSON from ${endpoint}:`, parseError);
      throw new Error(`Invalid response format: ${parseError.message}`);
    }
  } catch (error) {
    console.error(`[fetchWithAuth] API error in ${endpoint}:`, error);
    throw error;
  }
}

/**
 * Phone Number API functions
 */
export const NumbersAPI = {
  // Search for available phone numbers
  searchAvailableNumbers: async (params) => {
    const queryParams = new URLSearchParams();

    // Add search params
    if (params.countryCode) queryParams.append('countryCode', params.countryCode);
    if (params.areaCode) queryParams.append('areaCode', params.areaCode);
    if (params.voiceEnabled) queryParams.append('voiceEnabled', params.voiceEnabled);
    if (params.smsEnabled) queryParams.append('smsEnabled', params.smsEnabled);
    if (params.limit) queryParams.append('limit', params.limit);

    return fetchWithAuth(`/api/numbers/available?${queryParams.toString()}`);
  },

  // Purchase a phone number
  purchaseNumber: async (numberData) => {
    return fetchWithAuth('/api/numbers/purchase', {
      method: 'POST',
      body: JSON.stringify(numberData),
    });
  },

  // Get subaccount status
  getSubaccountStatus: async () => {
    return fetchWithAuth('/api/numbers/subaccount');
  },

  // Create a subaccount
  createSubaccount: async (data) => {
    return fetchWithAuth('/api/numbers/subaccount', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Get user's phone numbers
  getUserNumbers: async () => {
    try {
      // Add console log for debugging
      console.log('[NumbersAPI] Attempting to fetch phone numbers from main API');
      
      // Try to fetch from the main API
      const result = await fetchWithAuth('/api/numbers/mine');
      console.log('[NumbersAPI] Main API response:', result);
      
      // If successful and contains numbers, return as is
      if (result && result.success) {
        // Normalize response structure regardless of format
        const normalizedResult = {
          success: true,
          numbers: Array.isArray(result.numbers) ? result.numbers : 
                  Array.isArray(result.data) ? result.data :
                  Array.isArray(result.data?.phoneNumbers) ? result.data.phoneNumbers :
                  Array.isArray(result.phoneNumbers) ? result.phoneNumbers : []
        };
        
        console.log(`[NumbersAPI] Normalized ${normalizedResult.numbers.length} phone numbers`);
        return normalizedResult;
      }
      
      // Try the UserAPI endpoint as a backup
      console.log('[NumbersAPI] Main API returned no numbers, trying UserAPI endpoint');
      const userApiResult = await fetchWithAuth('/api/users/numbers');
      console.log('[NumbersAPI] UserAPI response:', userApiResult);
      
      if (userApiResult && userApiResult.success) {
        // Normalize response structure for UserAPI endpoint
        const normalizedResult = {
          success: true,
          numbers: Array.isArray(userApiResult.data) ? userApiResult.data : []
        };
        
        console.log(`[NumbersAPI] UserAPI returned ${normalizedResult.numbers.length} phone numbers`);
        return normalizedResult;
      }
      
      // If we got here, try to use the direct database query
      console.log('[NumbersAPI] Standard endpoints failed, trying direct database query');
      const directDbResult = await fetchWithAuth('/api/numbers/mine?direct=true');
      console.log('[NumbersAPI] Direct database query response:', directDbResult);
      
      if (directDbResult && directDbResult.success) {
        const normalizedResult = {
          success: true,
          numbers: Array.isArray(directDbResult.numbers) ? directDbResult.numbers : []
        };
        
        console.log(`[NumbersAPI] Direct database query returned ${normalizedResult.numbers.length} phone numbers`);
        return normalizedResult;
      }
      
      // If all attempts failed but we have a response, try to extract numbers
      if (result) {
        console.log('[NumbersAPI] Attempting to extract numbers from partial result');
        const fallbackResult = {
          success: true,
          numbers: Array.isArray(result.numbers) ? result.numbers : 
                  Array.isArray(result.data) ? result.data : []
        };
        
        if (fallbackResult.numbers.length > 0) {
          console.log(`[NumbersAPI] Extracted ${fallbackResult.numbers.length} phone numbers from fallback`);
          return fallbackResult;
        }
      }
      
      // Return empty response if all attempts failed
      console.log('[NumbersAPI] All attempts failed, returning empty result');
      return { success: true, numbers: [] };
    } catch (error) {
      console.error('[NumbersAPI] Error fetching user numbers:', error);
      // Return empty array in case of error
      return { success: false, message: error.message, numbers: [] };
    }
  }
};

// Helper to get auth token
async function getAuthToken() {
  try {
    const supabase = getSupabaseClient();
    if (!supabase || typeof supabase.auth.getSession !== 'function') {
      return null;
    }
    
    const { data } = await supabase.auth.getSession();
    return data?.session?.access_token || null;
  } catch (error) {
    console.error('[getAuthToken] Error getting authentication token:', error);
    return null;
  }
}

/**
 * Call Logs API functions
 */
export const CallLogsAPI = {
  // Get call logs with optional filtering
  getCallLogs: async (params = {}) => {
    const queryParams = new URLSearchParams();

    // Add optional filters
    if (params.startDate) queryParams.append('startDate', params.startDate);
    if (params.endDate) queryParams.append('endDate', params.endDate);
    if (params.status) queryParams.append('status', params.status);
    if (params.direction) queryParams.append('direction', params.direction);
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.offset) queryParams.append('offset', params.offset);

    return fetchWithAuth(`/api/call-logs?${queryParams.toString()}`);
  },

  // Get call stats (weekly/daily/hourly breakdown)
  getCallStats: async (timeframe = 'weekly') => {
    return fetchWithAuth(`/api/call-logs/stats?timeframe=${timeframe}`);
  },

  // Get recent calls (limited list for dashboard)
  getRecentCalls: async (limit = 5) => {
    return fetchWithAuth(`/api/call-logs/recent?limit=${limit}`);
  },

  // Get voicemails
  getVoicemails: async (limit = 5) => {
    return fetchWithAuth(`/api/call-logs/voicemails?limit=${limit}`);
  }
};

/**
 * User API functions
 */
export const UserAPI = {
  // Get current user profile
  getUserProfile: async () => {
    return fetchWithAuth('/api/users/me');
  },

  // Get user phone numbers
  getUserNumbers: async () => {
    return fetchWithAuth('/api/users/numbers');
  },

  // Update user profile
  updateProfile: async (userData) => {
    return fetchWithAuth('/api/users/profile', {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }
};

/**
 * Billing API functions
 */
export const BillingAPI = {
  // Get user credits/tokens
  getCredits: async () => {
    console.log('Fetching user credits via BillingAPI.getCredits...'); // Add log
    return fetchWithAuth('/api/credits');
  },

  // Get subscription info
  getSubscription: async () => {
    return fetchWithAuth('/api/billing/subscription');
  },

  // Create checkout session
  createCheckout: async (planData) => {
    return fetchWithAuth('/api/billing/checkout', {
      method: 'POST',
      body: JSON.stringify(planData),
    });
  }
};

/**
 * Message API functions (New)
 */
export const MessagesAPI = {
  // Get recent messages (limited list for dashboard)
  getRecentMessages: async (limit = 5) => {
    return fetchWithAuth(`/api/messages/recent?limit=${limit}`);
  },

  // Get all messages (implement pagination later)
  getAllMessages: async (params = {}) => {
    const queryParams = new URLSearchParams();
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.offset) queryParams.append('offset', params.offset);
    // Add other filters as needed
    return fetchWithAuth(`/api/messages?${queryParams.toString()}`);
  }
  // Add reply function later
};

// Export default if needed
export default {
  NumbersAPI,
  CallLogsAPI,
  UserAPI,
  BillingAPI,
  MessagesAPI // Add new API group
};
