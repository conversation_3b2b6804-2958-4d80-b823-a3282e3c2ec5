'use client';

import Link from 'next/link';
import { ComponentType } from 'react';
import { formatDistanceToNow } from 'date-fns';

interface ActivityItemProps {
  itemData: {
    id: string;
    type: 'call' | 'sms' | 'voicemail' | 'alert';
    timestamp: string;
    details: string;
    link?: string;
  };
  icon: ComponentType<{ className?: string }>;
}

export default function ActivityItem({ itemData, icon: Icon }: ActivityItemProps) {
  const { id, type, timestamp, details, link } = itemData;
  
  // Format the timestamp as a relative time (e.g., "5 minutes ago")
  const formattedTime = formatDistanceToNow(new Date(timestamp), { addSuffix: true });
  
  // Get background color based on activity type
  const getBgColorForType = (type: string) => {
    switch (type) {
      case 'call':
        return 'bg-blue-100 dark:bg-blue-900';
      case 'sms':
        return 'bg-green-100 dark:bg-green-900';
      case 'voicemail':
        return 'bg-purple-100 dark:bg-purple-900';
      case 'alert':
        return 'bg-red-100 dark:bg-red-900';
      default:
        return 'bg-gray-100 dark:bg-gray-700';
    }
  };

  // Get text color based on activity type
  const getTextColorForType = (type: string) => {
    switch (type) {
      case 'call':
        return 'text-blue-600 dark:text-blue-400';
      case 'sms':
        return 'text-green-600 dark:text-green-400';
      case 'voicemail':
        return 'text-purple-600 dark:text-purple-400';
      case 'alert':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const itemContent = (
    <div className="flex items-start p-4 rounded-lg border border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors duration-150">
      <div className={`p-2 rounded-full ${getBgColorForType(type)} mr-4 flex-shrink-0`}>
        <Icon className={`h-5 w-5 ${getTextColorForType(type)}`} />
      </div>
      <div className="flex-grow min-w-0">
        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">{details}</p>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{formattedTime}</p>
      </div>
    </div>
  );

  if (link) {
    return (
      <Link href={link} className="block">
        {itemContent}
      </Link>
    );
  }

  return itemContent;
}
