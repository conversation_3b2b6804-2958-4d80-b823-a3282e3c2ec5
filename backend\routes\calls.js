const express = require('express');
const router = express.Router();
const { getCalls, getCallStats, getRecentCalls, getVoicemails } = require('../controllers/callController');
const { protect, csrfProtection } = require('../middleware/authMiddleware'); // Adjusted path
const { requirePermission } = require('../middleware/permissionMiddleware'); // Adjusted path
const { standardLimiter } = require('../middleware/rateLimitMiddleware'); // Adjusted path

// Apply authentication and CSRF protection middleware to all routes in this router
router.use(protect);
// router.use(csrfProtection); // CSRF protection can be selectively applied or globally if appropriate

// Define call routes with permissions and rate limiting
// These permissions are examples and should be aligned with your actual permission system
router.get('/', standardLimiter, requirePermission('calls:read'), getCalls);
router.get('/stats', standardLimiter, requirePermission('calls:read:stats'), getCallStats);
router.get('/recent', standardLimiter, requirePermission('calls:read:recent'), getRecentCalls);
router.get('/voicemails', standardLimiter, requirePermission('calls:read:voicemails'), getVoicemails);

// Placeholder for other CRUD operations if needed in the future
// router.post('/', standardLimiter, requirePermission('calls:create'), (req, res) => { /* ... */ });
// router.get('/:id', standardLimiter, requirePermission('calls:read:one'), (req, res) => { /* ... */ });
// router.put('/:id', standardLimiter, requirePermission('calls:update'), (req, res) => { /* ... */ });
// router.delete('/:id', standardLimiter, requirePermission('calls:delete'), (req, res) => { /* ... */ });

module.exports = router;