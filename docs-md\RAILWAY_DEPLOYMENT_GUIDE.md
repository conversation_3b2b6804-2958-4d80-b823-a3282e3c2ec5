---
title: Railway Backend Deployment Guide
description: Step-by-step instructions for deploying the CallSaver.app backend to Railway
date: 2025-04-29
status: Required
priority: High
---

# Railway Backend Deployment Guide

This guide provides detailed instructions for deploying the CallSaver.app backend to Railway, including database setup, environment configuration, and deployment verification.

## Prerequisites

- Railway account with billing set up
- GitHub account connected to Railway
- Access to the CallSaver.app GitHub repository
- Twilio account with API credentials
- Supabase account with API credentials
- OpenAI API key for AI functionality

## Deployment Steps

### 1. Initial Project Setup

1. Log in to [Railway Dashboard](https://railway.app/dashboard)
2. Click **New Project** > **Deploy from GitHub repo**
3. Select the `callsaver.app` repository
4. Choose the `main` branch
5. Select the `/back/backend` directory as the root directory
6. Click **Deploy**

### 2. Database Configuration

1. In your project, navigate to **+ New** and select **PostgreSQL**
2. Once the database is created, go to **Variables** tab
3. Note the following variables that have been automatically created:
   - `PGDATABASE`
   - `PGHOST`
   - `PGPASSWORD`
   - `PGPORT`
   - `PGUSER`
   - `DATABASE_URL`

### 3. Redis Configuration

1. In your project, navigate to **+ New** and select **Redis**
2. Once Redis is created, go to **Variables** tab
3. Note the `REDIS_URL` variable

### 4. Environment Variables Setup

Add the following environment variables to your Railway project:

#### Core Configuration
```
NODE_ENV=production
PORT=3001
API_URL=https://api.callsaver.app  # Update with your actual domain
WEBSITE_URL=https://callsaver.app  # Update with your actual domain
LOG_LEVEL=info
```

#### Twilio Configuration
```
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_API_KEY=your_api_key
TWILIO_API_SECRET=your_api_secret
TWILIO_WEBHOOK_BASE_URL=https://api.callsaver.app  # Update with your actual domain
```

#### Supabase Configuration
```
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SUPABASE_JWT_SECRET=your_supabase_jwt_secret
```

#### OpenAI Configuration
```
OPENAI_API_KEY=your_openai_api_key
AI_MODEL=gpt-4-turbo
EMBEDDING_MODEL=text-embedding-3-small
```

#### Security Configuration
```
JWT_SECRET=your_secure_jwt_secret  # Generate a secure random string
JWT_EXPIRES_IN=7d
COOKIE_SECRET=your_secure_cookie_secret  # Generate a secure random string
CORS_ORIGIN=https://callsaver.app  # Update with your actual domain
```

#### Queue Configuration
```
QUEUE_CONCURRENCY=3
CALL_PROCESSING_CONCURRENCY=2
SMS_PROCESSING_CONCURRENCY=5
TRANSCRIPTION_CONCURRENCY=3
```

### 5. Database Migration

After setting up your database, you need to run the migration scripts:

1. Navigate to the **Settings** tab of your Railway project
2. Click on **Generate Build Command**
3. Set the build command to:
   ```
   npm install && npx prisma migrate deploy && npm run build
   ```
4. Set the start command to:
   ```
   npm start
   ```
5. Click **Save Changes**
6. Go to the **Deployments** tab and click **Deploy now** to trigger a new build

### 6. Configure Health Checks

1. Navigate to the **Settings** tab
2. Scroll down to the **Health Checks** section
3. Add a health check with the following settings:
   - Path: `/api/health`
   - Interval: `30s`
   - Timeout: `5s`
   - Grace Period: `45s`
4. Click **Save Changes**

### 7. Domain Setup

1. Navigate to the **Settings** tab
2. Scroll down to the **Custom Domain** section
3. Click **Generate Domain** or **Add Custom Domain**
4. If using a custom domain:
   - Enter your domain (e.g., `api.callsaver.app`)
   - Configure DNS settings as instructed by Railway
   - Wait for SSL certificate provisioning

### 8. Continuous Deployment

Railway automatically sets up continuous deployment. When new code is pushed to the main branch, it will be automatically deployed.

To customize this behavior:

1. Navigate to the **Settings** tab
2. Scroll to the **Deployments** section
3. Configure the automatic deployment settings as needed

### 9. Verification

After deployment is complete, verify the following:

1. Navigate to `https://your-railway-domain.railway.app/api/health` or your custom domain
2. You should receive a `200 OK` response with basic health information
3. Check the logs in the **Deployments** tab for any errors
4. Verify database connectivity by checking the logs

### 10. Monitoring

1. Navigate to the **Metrics** tab to view:
   - CPU usage
   - Memory usage
   - Disk usage
   - Network traffic
2. Set up alerts for critical thresholds:
   - Click **Create Alert** in the project settings
   - Configure CPU, memory, and error rate thresholds

## Troubleshooting

### Common Issues

1. **Database Migration Failures**
   - Check migration logs in the **Deployments** tab
   - Verify that your DATABASE_URL environment variable is correctly set
   - Try running migrations manually through Railway's CLI

2. **Environment Variable Issues**
   - Ensure all required environment variables are set
   - Check for typos or formatting issues
   - Verify that secrets don't contain special characters that need escaping

3. **Health Check Failures**
   - Verify that the API server is running correctly
   - Check logs for any startup errors
   - Ensure the health check endpoint is correctly implemented

4. **Memory or CPU Limits**
   - Consider upgrading your Railway plan if you're hitting resource limits
   - Check for memory leaks in the application code
   - Optimize database queries that might be consuming excessive resources

## Manual Deployment

If you need to deploy manually rather than using GitHub integration:

1. Install the Railway CLI:
   ```bash
   npm install -g @railway/cli
   ```

2. Log in to Railway:
   ```bash
   railway login
   ```

3. Link to your project:
   ```bash
   railway link
   ```

4. Deploy your application:
   ```bash
   railway up
   ```

## Rollback Procedure

If a deployment causes issues:

1. Navigate to the **Deployments** tab
2. Find the last working deployment
3. Click the three dots menu (⋮) next to it
4. Select **Rollback to this deployment**
5. Confirm the rollback

## Security Considerations

1. Ensure that all sensitive environment variables are properly secured
2. Regularly rotate API keys and secrets
3. Configure proper CORS settings to prevent unauthorized access
4. Use the JWT and cookie secrets with sufficient entropy
5. Consider enabling Railway's VPC isolation for enhanced security

## Post-Deployment Checklist

- [ ] Health endpoint returns 200 OK status
- [ ] Database migrations completed successfully
- [ ] Redis connection is established
- [ ] Twilio webhooks can be received and processed
- [ ] OpenAI integration is functioning
- [ ] API endpoints respond correctly to authenticated requests
- [ ] CPU and memory usage are within expected ranges
- [ ] Logs show no unexpected errors
- [ ] All background queues are processing jobs
