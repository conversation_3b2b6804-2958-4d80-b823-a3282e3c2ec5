"use client";

import React, { useState } from 'react';
import {
  UserCircleIcon,
  PhoneIcon,
  ChatBubbleLeftRightIcon,
  EnvelopeIcon,
  ShoppingCartIcon,
  GlobeAltIcon,
  CheckCircleIcon,
  XCircleIcon,
  UserIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';

export function CustomerJourney() {
  const [customers, setCustomers] = useState([
    {
      id: 'cust-001',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+****************',
      joinDate: 'Jan 15, 2023',
      avatar: null,
      journeyEvents: [
        {
          id: 'event-001',
          type: 'website-visit',
          label: 'Website Visit',
          detail: 'Landing Page',
          date: 'Mar 10, 2023',
          icon: GlobeAltIcon
        },
        {
          id: 'event-002',
          type: 'missed-call',
          label: 'Missed Call',
          detail: 'Product Inquiry',
          date: 'Mar 12, 2023',
          icon: PhoneIcon
        },
        {
          id: 'event-003',
          type: 'sms',
          label: 'SMS Conversation',
          detail: 'Handled by AI Assistant',
          date: 'Mar 12, 2023',
          icon: ChatBubbleLeftRightIcon
        },
        {
          id: 'event-004',
          type: 'call',
          label: 'Call',
          detail: 'Follow-up by Agent',
          date: 'Mar 14, 2023',
          icon: PhoneIcon
        },
        {
          id: 'event-005',
          type: 'purchase',
          label: 'Purchase',
          detail: 'Premium Plan - $89.99',
          date: 'Mar 15, 2023',
          icon: ShoppingCartIcon
        },
        {
          id: 'event-006',
          type: 'email',
          label: 'Email',
          detail: 'Onboarding Materials Sent',
          date: 'Mar 16, 2023',
          icon: EnvelopeIcon
        }
      ]
    },
    {
      id: 'cust-002',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      joinDate: 'Feb 3, 2023',
      avatar: null,
      journeyEvents: [
        {
          id: 'event-007',
          type: 'website-visit',
          label: 'Website Visit',
          detail: 'Pricing Page',
          date: 'Feb 28, 2023',
          icon: GlobeAltIcon
        },
        {
          id: 'event-008',
          type: 'missed-call',
          label: 'Missed Call',
          detail: 'Technical Support',
          date: 'Mar 2, 2023',
          icon: PhoneIcon
        },
        {
          id: 'event-009',
          type: 'sms',
          label: 'SMS Conversation',
          detail: 'Handled by AI Assistant',
          date: 'Mar 2, 2023',
          icon: ChatBubbleLeftRightIcon
        },
        {
          id: 'event-010',
          type: 'sms',
          label: 'SMS Conversation',
          detail: 'Technical Troubleshooting',
          date: 'Mar 3, 2023',
          icon: ChatBubbleLeftRightIcon
        },
        {
          id: 'event-011',
          type: 'email',
          label: 'Email',
          detail: 'Satisfaction Survey',
          date: 'Mar 5, 2023',
          icon: EnvelopeIcon
        }
      ]
    }
  ]);
  
  const [selectedCustomer, setSelectedCustomer] = useState(customers[0]);
  const [searchQuery, setSearchQuery] = useState('');
  
  // Filter customers based on search
  const filteredCustomers = customers.filter(customer => 
    customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.phone.includes(searchQuery)
  );
  
  // Get icon color based on event type
  const getEventColor = (type) => {
    switch (type) {
      case 'website-visit':
        return 'text-blue-400 bg-blue-900/30';
      case 'missed-call':
        return 'text-red-400 bg-red-900/30';
      case 'sms':
        return 'text-purple-400 bg-purple-900/30';
      case 'call':
        return 'text-green-400 bg-green-900/30';
      case 'purchase':
        return 'text-yellow-400 bg-yellow-900/30';
      case 'email':
        return 'text-indigo-400 bg-indigo-900/30';
      default:
        return 'text-gray-400 bg-gray-900/30';
    }
  };
  
  return (
    <div className="h-full flex flex-col overflow-hidden bg-gray-900">
      <div className="p-5 sm:p-6 border-b border-gray-800">
        <h3 className="text-xl font-bold text-white flex items-center">
          <UserCircleIcon className="h-5 w-5 mr-2 text-indigo-400" />
          Customer Journey
        </h3>
        <p className="text-gray-400 text-sm mt-1">
          Track and visualize customer interactions over time
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 h-full">
        {/* Customer list */}
        <div className="border-r border-gray-800 overflow-hidden flex flex-col">
          <div className="p-4 border-b border-gray-800">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
              <input
                type="text"
                placeholder="Search customers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-900 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-white text-sm"
              />
            </div>
          </div>
          
          <div className="overflow-y-auto flex-grow">
            {filteredCustomers.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                No customers found
              </div>
            ) : (
              <ul className="divide-y divide-gray-800">
                {filteredCustomers.map(customer => (
                  <li 
                    key={customer.id}
                    onClick={() => setSelectedCustomer(customer)}
                    className={`p-4 cursor-pointer transition-colors ${
                      selectedCustomer.id === customer.id 
                      ? 'bg-indigo-900/20' 
                      : 'hover:bg-gray-800/50'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="bg-gray-800 h-10 w-10 rounded-full flex items-center justify-center flex-shrink-0">
                        <UserIcon className="h-5 w-5 text-gray-400" />
                      </div>
                      <div>
                        <h4 className="font-medium text-white">{customer.name}</h4>
                        <p className="text-gray-400 text-sm">{customer.email}</p>
                        <div className="flex items-center mt-0.5 text-gray-500 text-xs">
                          <span>Customer since {customer.joinDate}</span>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
        
        {/* Journey timeline */}
        <div className="md:col-span-2 overflow-hidden flex flex-col">
          {selectedCustomer ? (
            <>
              <div className="p-4 border-b border-gray-800 bg-gray-900">
                <div className="flex items-center">
                  <div className="bg-indigo-900/50 h-12 w-12 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <UserIcon className="h-6 w-6 text-indigo-400" />
                  </div>
                  <div>
                    <h3 className="font-bold text-white text-lg">{selectedCustomer.name}</h3>
                    <div className="flex flex-wrap text-sm text-gray-400 gap-x-4">
                      <span>{selectedCustomer.email}</span>
                      <span>{selectedCustomer.phone}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex-grow overflow-y-auto p-5 bg-gray-900">
                <div className="relative">
                  {/* Timeline line */}
                  <div className="absolute top-0 bottom-0 left-6 w-0.5 bg-gray-800"></div>
                  
                  {/* Timeline events */}
                  <ul className="space-y-8">
                    {selectedCustomer.journeyEvents.map((event, index) => (
                      <li key={event.id} className="relative pl-14">
                        {/* Event icon */}
                        <div className={`absolute left-0 p-2 rounded-full ${getEventColor(event.type)}`}>
                          <event.icon className="h-5 w-5" />
                        </div>
                        
                        {/* Event date */}
                        <div className="text-sm text-indigo-400 font-medium mb-1">
                          {event.date}
                        </div>
                        
                        {/* Event content */}
                        <div className="bg-gray-800/40 p-4 rounded-lg border border-gray-700/40">
                          <h4 className="font-medium text-white">{event.label}</h4>
                          <p className="text-gray-400 text-sm mt-1">{event.detail}</p>
                          
                          {/* Conditional rendering of success/failure badges */}
                          {event.type === 'sms' && (
                            <div className="mt-3 flex items-center">
                              <span className="bg-green-900/30 border border-green-700/30 text-green-400 text-xs px-2 py-0.5 rounded-full flex items-center">
                                <CheckCircleIcon className="h-3 w-3 mr-1" />
                                Successfully Resolved
                              </span>
                            </div>
                          )}
                          
                          {event.type === 'missed-call' && (
                            <div className="mt-3 flex items-center">
                              <span className="bg-yellow-900/30 border border-yellow-700/30 text-yellow-400 text-xs px-2 py-0.5 rounded-full flex items-center">
                                <XCircleIcon className="h-3 w-3 mr-1" />
                                Missed Opportunity
                              </span>
                            </div>
                          )}
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </>
          ) : (
            <div className="flex items-center justify-center h-full bg-gray-900">
              <p className="text-gray-500">Select a customer to view their journey</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 