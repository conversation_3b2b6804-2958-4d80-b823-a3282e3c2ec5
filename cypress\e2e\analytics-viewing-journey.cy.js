/**
 * E2E Test for Analytics Viewing Journey
 * 
 * This test covers the process of viewing and interacting with the analytics dashboard,
 * ensuring users can access and filter important metrics.
 */

describe('Analytics Viewing Journey', () => {
  beforeEach(() => {
    // Log in before each test
    cy.login();
    
    // Mock API responses
    cy.mockAiApi();
  });
  
  it('should display the analytics dashboard with metrics', () => {
    // Navigate to analytics
    cy.visit('/dashboard/analytics');
    
    // Wait for analytics data to load
    cy.wait('@analytics');
    
    // Verify page title
    cy.get('h1').should('contain', 'Analytics Dashboard');
    
    // Verify time range selector exists
    cy.get('select').should('be.visible');
    
    // Verify tab navigation
    cy.get('button').contains('Call Analytics').should('be.visible');
    cy.get('button').contains('Message Analytics').should('be.visible');
    cy.get('button').contains('AI Performance').should('be.visible');
    
    // Check key metrics for call analytics (default tab)
    cy.get('div').contains('Total Calls').parent().should('contain', '250');
    cy.get('div').contains('Total Duration').parent().should('contain', '20h 50m 0s');
    cy.get('div').contains('Missed Call Rate').parent().should('contain', '36.7%');
    cy.get('div').contains('AI Handoff Rate').parent().should('contain', '68%');
    
    // Verify charts are displayed
    cy.get('[data-testid="bar-chart"]').should('exist');
    cy.get('[data-testid="line-chart"]').should('exist');
  });
  
  it('should allow switching between analytics tabs', () => {
    // Navigate to analytics
    cy.visit('/dashboard/analytics');
    
    // Wait for analytics data to load
    cy.wait('@analytics');
    
    // Switch to Message Analytics tab
    cy.get('button').contains('Message Analytics').click();
    
    // Check message metrics are displayed
    cy.get('div').contains('Total Messages').parent().should('contain', '500');
    cy.get('div').contains('Inbound Messages').parent().should('contain', '300');
    cy.get('div').contains('Outbound Messages').parent().should('contain', '200');
    cy.get('div').contains('SMS Response Rate').parent().should('contain', '92.5%');
    
    // Switch to AI Performance tab
    cy.get('button').contains('AI Performance').click();
    
    // Check AI metrics are displayed
    cy.get('div').contains('AI Handled Calls').parent().should('contain', '102');
    cy.get('div').contains('AI Handled Messages').parent().should('contain', '240');
  });
  
  it('should allow changing time ranges', () => {
    // Mock additional time range responses
    cy.intercept('GET', '/api/analytics?timeRange=30d', {
      statusCode: 200,
      body: {
        // Same structure but with 30 days of data
        totalCalls: 950,
        totalMessages: 1800,
        totalDuration: 285000,
        callsByDirection: {
          inbound: 570,
          outbound: 380
        },
        messagesByDirection: {
          inbound: 1080,
          outbound: 720
        },
        // Other fields would have more data points
        organizationId: "org_123456"
      }
    }).as('analytics30d');
    
    // Navigate to analytics
    cy.visit('/dashboard/analytics');
    
    // Wait for default analytics data to load
    cy.wait('@analytics');
    
    // Change time range to 30 days
    cy.get('select').select('Last 30 days');
    
    // Wait for new data to load
    cy.wait('@analytics30d');
    
    // Verify updated metrics for the longer time period
    cy.get('div').contains('Total Calls').parent().should('contain', '950');
    cy.get('div').contains('Total Messages').parent().should('contain', '1800');
  });
  
  it('should refresh data when refresh button is clicked', () => {
    // Navigate to analytics
    cy.visit('/dashboard/analytics');
    
    // Wait for initial analytics data to load
    cy.wait('@analytics');
    
    // Clear previous analytics interception
    cy.intercept('GET', '/api/analytics*').as('analyticsRefresh');
    
    // Click refresh button
    cy.get('button').find('svg').parent().click();
    
    // Wait for refreshed data
    cy.wait('@analyticsRefresh');
    
    // Verify loading states appear and then resolve
    cy.get('div').contains('...').should('not.exist');
  });
  
  it('should handle download functionality', () => {
    // Mock download endpoint
    cy.intercept('GET', '/api/analytics/export*', {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/octet-stream',
        'Content-Disposition': 'attachment; filename=analytics_export.csv'
      },
      body: 'Date,Inbound Calls,Outbound Calls,Total Calls\n2025-04-22,20,10,30\n2025-04-23,21,11,32'
    }).as('downloadAnalytics');
    
    // Navigate to analytics
    cy.visit('/dashboard/analytics');
    
    // Wait for analytics data to load
    cy.wait('@analytics');
    
    // Click download button
    cy.get('button').contains('Download').click();
    
    // Wait for download request
    cy.wait('@downloadAnalytics');
  });
  
  it('should display tenant-specific data', () => {
    // Mock organization switching
    cy.intercept('GET', '/api/organizations', {
      statusCode: 200,
      body: {
        organizations: [
          { id: 'org_123456', name: 'Company A' },
          { id: 'org_654321', name: 'Company B' }
        ],
        currentOrganization: 'org_123456'
      }
    }).as('getOrganizations');
    
    // Mock analytics for second organization
    cy.intercept('GET', '/api/analytics*', {
      statusCode: 200,
      body: {
        // Different data for different organization
        totalCalls: 175,
        totalMessages: 320,
        totalDuration: 50000,
        callsByDirection: {
          inbound: 105,
          outbound: 70
        },
        messagesByDirection: {
          inbound: 190,
          outbound: 130
        },
        // Other fields would be different
        organizationId: "org_654321"
      }
    }).as('analyticsOrg2');
    
    // Navigate to organization selector first
    cy.visit('/dashboard/organizations');
    
    // Wait for organizations to load
    cy.wait('@getOrganizations');
    
    // Switch to second organization
    cy.get('button').contains('Company B').click();
    
    // Navigate to analytics
    cy.visit('/dashboard/analytics');
    
    // Wait for organization-specific analytics
    cy.wait('@analyticsOrg2');
    
    // Verify tenant-specific metrics
    cy.get('div').contains('Total Calls').parent().should('contain', '175');
    cy.get('div').contains('Total Messages').parent().should('contain', '320');
  });
});
