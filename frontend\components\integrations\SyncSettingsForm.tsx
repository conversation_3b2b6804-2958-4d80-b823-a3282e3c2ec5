'use client';

import { useState } from 'react';
import { Integration, IntegrationSettings } from '../../hooks/useIntegrations';
import GoogleCalendarSettings from './calendar/GoogleCalendarSettings';
import OutlookCalendarSettings from './calendar/OutlookCalendarSettings';
import SlackSettings from './communication/SlackSettings';
import SalesforceSettings from './crm/SalesforceSettings';
import ZapierSettings from './automation/ZapierSettings';
import HubSpotSettings from './crm/HubSpotSettings';
import ZoomSettings from './communication/ZoomSettings';
import ShopifySettings from './other/ShopifySettings';
import DefaultSettings from './DefaultSettings';

interface SyncSettingsFormProps {
  integration: Integration;
  settings: IntegrationSettings;
  onChange: (settings: IntegrationSettings) => void;
}

export default function SyncSettingsForm({
  integration,
  settings,
  onChange,
}: SyncSettingsFormProps) {
  // Render the appropriate settings form based on the integration provider
  const renderSettingsForm = () => {
    switch (integration.provider.toLowerCase()) {
      case 'google calendar':
        return (
          <GoogleCalendarSettings
            settings={settings}
            onChange={onChange}
          />
        );
      case 'outlook':
        return (
          <OutlookCalendarSettings
            settings={settings}
            onChange={onChange}
          />
        );
      case 'slack':
        return (
          <SlackSettings
            settings={settings}
            onChange={onChange}
          />
        );
      case 'salesforce':
        return (
          <SalesforceSettings
            settings={settings}
            onChange={onChange}
          />
        );
      case 'zapier':
        return (
          <ZapierSettings
            settings={settings}
            onChange={onChange}
          />
        );
      case 'hubspot':
        return (
          <HubSpotSettings
            settings={settings}
            onChange={onChange}
          />
        );
      case 'zoom':
        return (
          <ZoomSettings
            settings={settings}
            onChange={onChange}
          />
        );
      case 'shopify':
        return (
          <ShopifySettings
            settings={settings}
            onChange={onChange}
          />
        );
      default:
        return (
          <DefaultSettings
            settings={settings}
            onChange={onChange}
          />
        );
    }
  };

  return (
    <div className="space-y-4">
      <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
        {integration.name} Settings
      </h4>
      {renderSettingsForm()}
    </div>
  );
}
