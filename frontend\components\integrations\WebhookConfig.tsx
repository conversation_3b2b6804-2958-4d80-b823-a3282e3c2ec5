'use client';

import { useState } from 'react';
import { ClipboardIcon, CheckIcon } from '@heroicons/react/24/outline';
import LoadingSpinner from '../shared/LoadingSpinner';

interface WebhookConfigProps {
  webhookUrl?: string;
  config: any;
  onChange: (config: any) => void;
  onSave: () => void;
  isLoading: boolean;
}

export default function WebhookConfig({
  webhookUrl,
  config,
  onChange,
  onSave,
  isLoading,
}: WebhookConfigProps) {
  const [copied, setCopied] = useState(false);

  // Handle copy webhook URL to clipboard
  const handleCopyWebhookUrl = () => {
    if (webhookUrl) {
      navigator.clipboard.writeText(webhookUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    onChange({
      ...config,
      [name]: value,
    });
  };

  return (
    <div className="space-y-4">
      {webhookUrl && (
        <div>
          <label
            htmlFor="webhookUrl"
            className="block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            Webhook URL
          </label>
          <div className="mt-1 flex rounded-md shadow-sm">
            <input
              type="text"
              name="webhookUrl"
              id="webhookUrl"
              className="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-l-md focus:ring-blue-500 focus:border-blue-500 sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              value={webhookUrl}
              readOnly
            />
            <button
              type="button"
              className="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-md bg-gray-50 dark:bg-gray-600 text-gray-500 dark:text-gray-300 sm:text-sm"
              onClick={handleCopyWebhookUrl}
            >
              {copied ? (
                <CheckIcon className="h-5 w-5 text-green-500" aria-hidden="true" />
              ) : (
                <ClipboardIcon className="h-5 w-5" aria-hidden="true" />
              )}
            </button>
          </div>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Use this URL in your webhook configuration.
          </p>
        </div>
      )}

      <div>
        <label
          htmlFor="webhookSecret"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Webhook Secret (Optional)
        </label>
        <div className="mt-1">
          <input
            type="text"
            name="webhookSecret"
            id="webhookSecret"
            className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
            placeholder="Enter webhook secret for verification"
            value={config.webhookSecret || ''}
            onChange={handleInputChange}
          />
        </div>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          If your webhook provider supports it, enter a secret to verify webhook requests.
        </p>
      </div>

      <div>
        <label
          htmlFor="eventTypes"
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          Event Types (Optional)
        </label>
        <div className="mt-1">
          <input
            type="text"
            name="eventTypes"
            id="eventTypes"
            className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md"
            placeholder="e.g., call.completed,message.received"
            value={config.eventTypes || ''}
            onChange={handleInputChange}
          />
        </div>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Comma-separated list of event types to subscribe to.
        </p>
      </div>

      <div className="pt-4">
        <button
          type="button"
          className="inline-flex justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          onClick={onSave}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <LoadingSpinner size="sm" />
              <span className="ml-2">Saving...</span>
            </>
          ) : (
            'Save Configuration'
          )}
        </button>
      </div>
    </div>
  );
}
