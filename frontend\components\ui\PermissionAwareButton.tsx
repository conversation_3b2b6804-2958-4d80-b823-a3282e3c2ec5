'use client';

import React from 'react';
import { usePermissions } from '../../hooks/usePermissions';
import PermissionTooltip from './PermissionTooltip';

interface PermissionAwareButtonProps {
  permission?: string;
  anyPermission?: string[];
  allPermissions?: string[];
  resource?: string;
  action?: string;
  scope?: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  title?: string;
  showTooltipWhenDisabled?: boolean;
  tooltipMessage?: string;
  tooltipPosition?: 'top' | 'bottom' | 'left' | 'right';
  renderWhenNoAccess?: boolean;
  [key: string]: any; // For any other props that might be passed
}

/**
 * A button component that is only rendered if the user has the required permissions
 */
const PermissionAwareButton: React.FC<PermissionAwareButtonProps> = ({
  permission,
  anyPermission,
  allPermissions,
  resource,
  action = 'create',
  scope = 'any',
  children,
  className = '',
  onClick,
  disabled = false,
  type = 'button',
  title,
  showTooltipWhenDisabled = false,
  tooltipMessage,
  tooltipPosition = 'top',
  renderWhenNoAccess = false,
  ...rest
}) => {
  const {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccess
  } = usePermissions();

  // Check if the user has the required permissions
  let hasAccess = false;

  if (permission) {
    hasAccess = hasPermission(permission);
  } else if (anyPermission) {
    hasAccess = hasAnyPermission(anyPermission);
  } else if (allPermissions) {
    hasAccess = hasAllPermissions(allPermissions);
  } else if (resource) {
    hasAccess = canAccess(resource, action, scope);
  } else {
    // If no permissions are specified, allow access
    hasAccess = true;
  }

  // If the user doesn't have access and we're not rendering when no access, don't render anything
  if (!hasAccess && !renderWhenNoAccess) {
    return null;
  }

  // Generate tooltip message if not provided
  const defaultTooltipMessage = !hasAccess
    ? 'You do not have permission to perform this action'
    : 'This action is currently disabled';

  const finalTooltipMessage = tooltipMessage || defaultTooltipMessage;
  const isButtonDisabled = disabled || !hasAccess;

  // Render the button with or without tooltip
  const buttonElement = (
    <button
      type={type}
      className={className}
      onClick={onClick}
      disabled={isButtonDisabled}
      title={isButtonDisabled && !showTooltipWhenDisabled ? finalTooltipMessage : title}
      {...rest}
    >
      {children}
    </button>
  );

  // If tooltip should be shown when disabled and the button is disabled, wrap in tooltip
  if (showTooltipWhenDisabled && isButtonDisabled) {
    return (
      <PermissionTooltip
        message={finalTooltipMessage}
        position={tooltipPosition}
      >
        {buttonElement}
      </PermissionTooltip>
    );
  }

  // Otherwise just return the button
  return buttonElement;
};

export default PermissionAwareButton;
