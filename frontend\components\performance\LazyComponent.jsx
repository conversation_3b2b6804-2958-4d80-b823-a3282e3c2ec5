'use client';

import React, { Suspense } from 'react';
import useLazyComponent from '../../hooks/useLazyComponent';

/**
 * LazyComponent - A wrapper component for lazy loading components
 * 
 * This component makes it easy to lazy load any component based on various conditions:
 * - When the component is in or near the viewport
 * - After a specified delay
 * - When a specific condition is met
 * - On demand (manually triggered)
 * 
 * @param {Function} importFunc - Dynamic import function that returns the component
 * @param {Object} props - Component props and lazy loading options
 * @param {boolean} props.viewport - Whether to load when in viewport (default: true)
 * @param {number} props.delay - Delay in ms before loading (default: 0)
 * @param {boolean} props.condition - Condition that must be true to load (default: true)
 * @param {React.ReactNode} props.fallback - Fallback UI while loading (default: null)
 * @param {string} props.rootMargin - Root margin for IntersectionObserver (default: '200px')
 * @param {number} props.threshold - Threshold for IntersectionObserver (default: 0.1)
 * @returns {React.ReactElement} - The lazy loaded component or fallback
 */
const LazyComponent = ({
  importFunc,
  viewport = true,
  delay = 0,
  condition = true,
  fallback = null,
  rootMargin = '200px',
  threshold = 0.1,
  ...props
}) => {
  const { Component, loading, error, ref } = useLazyComponent(importFunc, {
    viewport,
    delay,
    condition,
    rootMargin,
    threshold,
  });

  // Handle error state
  if (error) {
    console.error('Error loading component:', error);
    return (
      <div className="text-red-500 p-4 border border-red-300 rounded">
        Error loading component: {error.message}
      </div>
    );
  }

  // Show fallback while loading or if component is not loaded yet
  if (loading || !Component) {
    return <div ref={ref}>{fallback}</div>;
  }

  // Render the loaded component with the provided props
  return (
    <Suspense fallback={fallback}>
      <Component {...props} />
    </Suspense>
  );
};

export default LazyComponent;
