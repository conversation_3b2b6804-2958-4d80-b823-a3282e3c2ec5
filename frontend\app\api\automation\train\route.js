import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

export async function POST(request) {
  const cookieStore = cookies();
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        get: (name) => cookieStore.get(name)?.value,
        set: (name, value, options) => {
          console.warn("Attempting to set cookie in Route Handler - might not persist");
          cookieStore.set({ name, value, ...options });
        },
        remove: (name, options) => {
          console.warn("Attempting to remove cookie in Route Handler - might not persist");
          cookieStore.set({ name, value: '', ...options });
        },
      },
    }
  );

  try {
    // 1. Get User Session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError || !session) {
      console.error('Error getting session or no session found:', sessionError);
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 401 });
    }
    const userId = session.user.id;

    // 2. Parse request body
    const body = await request.json();
    const { phoneNumber } = body;

    if (!phoneNumber) {
      return NextResponse.json({ success: false, message: 'Phone number is required' }, { status: 400 });
    }

    console.log(`[API /train] Training request for phone number: ${phoneNumber} by user: ${userId}`);

    // 3. Check if there are documents to train on for this specific phone number
    const { data: documents, error: docError } = await supabase
      .from('training_documents')
      .select('id, file_name')
      .eq('user_id', userId)
      .eq('phone_number', phoneNumber);

    if (docError) {
      console.error('Error checking for documents:', docError);
      throw new Error(`Database query failed: ${docError.message}`);
    }

    if (!documents || documents.length === 0) {
      // Check if user has any documents at all
      const { data: anyDocuments } = await supabase
        .from('training_documents')
        .select('id')
        .eq('user_id', userId)
        .limit(1);

      if (!anyDocuments || anyDocuments.length === 0) {
        return NextResponse.json({
          success: false,
          message: 'No documents found for training. Please upload at least one document first.'
        }, { status: 400 });
      } else {
        return NextResponse.json({
          success: false,
          message: `No documents found for phone number ${phoneNumber}. Please upload documents specifically for this number.`
        }, { status: 400 });
      }
    }

    console.log(`[API /train] Found ${documents.length} documents for phone number ${phoneNumber}`);

    // 4. Call the backend API to trigger training
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_API_URL;
    if (!backendUrl) {
      throw new Error('Backend API URL not configured');
    }

    const apiUrl = `${backendUrl}/api/automation/train`;
    console.log(`[API /train] Calling backend API: ${apiUrl}`);

    // Get the access token from the session
    const accessToken = session.access_token;

    const backendResponse = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        phoneNumber,
        documentCount: documents.length,
        documentIds: documents.map(doc => doc.id)
      })
    });

    // 5. Handle backend response
    const backendData = await backendResponse.json();

    if (!backendResponse.ok) {
      // Pass through the backend error
      return NextResponse.json({
        success: false,
        message: backendData.message || 'Training failed'
      }, { status: backendResponse.status });
    }

    // 6. Update training status in database (optional)
    // This could be a record in a training_status table or an update to the phone_number table
    // For now, we'll just return success

    return NextResponse.json({
      success: true,
      message: backendData.message || 'Training process initiated successfully',
      documentCount: documents.length,
      phoneNumber: phoneNumber,
      jobId: backendData.jobId || 'unknown'
    });

  } catch (error) {
    console.error('[API /train] POST Error:', error);
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to initiate training'
    }, { status: 500 });
  }
}
